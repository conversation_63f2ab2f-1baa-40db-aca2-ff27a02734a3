import dotenv from 'dotenv'
// 加载.env.local 文件中的环境变量
const config = dotenv.config({ path: '.env.local' })
const VITE_APP_API_URL = config.parsed?.VITE_APP_API_URL
const VITE_APP_MENU_URL = config.parsed?.VITE_APP_MENU_URL
const VITE_APP_MENU_UERRID = config.parsed?.VITE_APP_MENU_UERRID
const VITE_APP_MENU_TOKEN = config.parsed?.VITE_APP_MENU_TOKEN
const VITE_APP_MENU_VIEWMENUDTL = config.parsed?.VITE_APP_MENU_VIEWMENUDTL
export const GENERATE_PATH = 'dist'
export const CATCH_PATH = '.cache'

// swagger 接口请求配置
export const API_CONFIG = {
  method: 'get',
  url: VITE_APP_API_URL, // swagger获取配置接口地址
}

// 权限中心菜单接口配置
export const MENU_CONFIG = {
  url: VITE_APP_MENU_URL, // 权限中心菜单接口地址
  method: 'get',
  params: {
    userId: VITE_APP_MENU_UERRID, // 用户id
    menuType: '0',
  },
  headers: {
    authorization: VITE_APP_MENU_TOKEN, // token
    opuser: VITE_APP_MENU_UERRID, // 用户id
    systemcode: 'dataPlatform',
    tenantid: 'system',
  },
}

// 权限中心菜单接口配置
export const VIEWMENUDTL_CONFIG = {
  url: VITE_APP_MENU_VIEWMENUDTL, // 权限中心菜单接口地址
  method: 'get',
  params: {
    tenantId: 'system',
    menuId: '',
  },
  headers: {
    authorization: VITE_APP_MENU_TOKEN, // token
    opuser: VITE_APP_MENU_UERRID, // 用户id
    systemcode: 'dataPlatform',
    tenantid: 'system',
  },
}

// 模块数据
export const PROJECT_MODULE = {
  url: 'http://localhost:8112/flow/projectmodule',
  method: 'get',
  params: {
    projectId: '670c7e040d195e4f885fac52',
    pageSize: 999,
    includeDeletedData: 0,
    includeShare: true,
  },
}
