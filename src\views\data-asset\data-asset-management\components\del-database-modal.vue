<template>
  <a href="javascript:;" style="color: red" @click="toggleModal">删除库</a>
  <a-modal
    :open="open"
    title="删除库提示"
    @ok="handleOk"
    @cancel="toggleModal"
    okText="删除"
    okType="danger"
    :confirm-loading="confirmLoading"
  >
    <div>
      <a-alert type="warning" show-icon>
        <template #description>
          要删除数据库<strong>{{ databaseName }}</strong
          >吗？此操作不可恢复！
        </template>
      </a-alert>
    </div>
  </a-modal>
</template>
<script setup lang="ts">
import { defineProps, ref, defineEmits } from 'vue'
import { deleteDatabase } from '@/api/assetmanager/dataassertmanage/dataassertmanage'

const props = defineProps<{
  databaseName: string
  databaseId: string
}>()

const emit = defineEmits(['onSuccess'])

const open = ref(false)
const confirmLoading = ref(false)

const toggleModal = () => {
  open.value = !open.value
}

const handleOk = async () => {
  try {
    confirmLoading.value = true
    await deleteDatabase({ databaseId: props.databaseId })
    open.value = false
    emit('onSuccess', props.databaseId) // 删除成功后触发外部回调
  } catch (error) {
    console.error('删除数据库失败:', error)
  } finally {
    confirmLoading.value = false
  }
}
</script>
