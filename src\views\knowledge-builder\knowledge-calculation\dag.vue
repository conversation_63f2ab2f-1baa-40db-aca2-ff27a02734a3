<script lang="ts" setup>
import { defineProps } from 'vue'
import { dagColumn } from './data-source'
import { CodeSandboxOutlined } from '@ant-design/icons-vue'
const props = withDefaults(defineProps<{ pageData?: any }>(), {
  pageData: () => [], // 默认值为数组
})
</script>

<template>
  <div class="centrality-page">
    <a-table :columns="dagColumn" :data-source="pageData">
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'info'">
          <div><CodeSandboxOutlined /> {{ record.nodeInfo.name }}</div>
          <div>年龄：{{ record.nodeInfo.age }}</div>
          <div>部门：{{ record.nodeInfo.department }}</div>
        </template>
      </template>
    </a-table>
  </div>
</template>

<style lang="less" scoped></style>
