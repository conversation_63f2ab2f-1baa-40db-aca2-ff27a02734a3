import type { DatabaseDesign } from './types'

/**
 * 父项目的iframe通信客户端工具类
 * 用于与DBER iframe进行数据通信
 */
class IframeClient {
  iframe: HTMLIFrameElement | null = null
  targetOrigin: string = '*'
  timeout: number = 10000
  pendingRequests: Map<string, any>
  eventListeners: Map<string, Function[]>
  isReady: boolean = false
  readyCallbacks: Function[] = []
  boundHandleMessage: any = null
  constructor(
    iframeElement: HTMLIFrameElement,
    options?: { targetOrigin?: string; timeout?: number },
  ) {
    this.iframe = iframeElement
    this.targetOrigin = options?.targetOrigin || '*'
    this.timeout = options?.timeout || 10000 // 10秒超时
    this.pendingRequests = new Map()
    this.eventListeners = new Map()
    this.isReady = false
    this.readyCallbacks = []

    // 绑定事件处理器，保存引用以便后续移除
    this.boundHandleMessage = this.handleMessage.bind(this)
    this.initializeListener()
  }

  /**
   * 初始化消息监听器
   */
  initializeListener() {
    window.addEventListener('message', this.boundHandleMessage, false)
  }

  /**
   * 处理接收到的消息
   * @param {MessageEvent} event
   */
  handleMessage(event: MessageEvent) {
    // 验证消息来源
    if (event.source !== this.iframe?.contentWindow) {
      return
    }

    const { type, data, id, source } = event.data

    // 验证消息来源标识
    if (source !== 'dber-iframe') {
      return
    }

    console.log('Received message from DBER:', type, data)

    // 处理响应消息
    if (type === 'response' || type === 'error') {
      const pendingRequest = this.pendingRequests.get(id)
      if (pendingRequest) {
        this.pendingRequests.delete(id)
        clearTimeout(pendingRequest.timeoutId)

        if (type === 'response') {
          pendingRequest.resolve(data)
        } else {
          pendingRequest.reject(new Error(data.error || 'Unknown error'))
        }
      }
      return
    }

    // 处理DBER准备就绪消息
    if (type === 'dber-ready') {
      this.isReady = true
      this.readyCallbacks.forEach((callback) => callback(data))
      this.readyCallbacks = []
      this.triggerEvent('ready', data)
      return
    }

    // 处理其他事件消息
    this.triggerEvent(type, data)
  }

  /**
   * 触发事件监听器
   * @param {string} type 事件类型
   * @param {any} data 事件数据
   */
  triggerEvent(type: string, data: any) {
    const listeners = this.eventListeners.get(type) || []
    listeners.forEach((listener) => {
      try {
        listener(data)
      } catch (error) {
        console.error('Error in event listener:', error)
      }
    })
  }

  /**
   * 生成唯一的消息ID
   * @returns {string}
   */
  generateMessageId() {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 向iframe发送消息
   * @param {string} type 消息类型
   * @param {any} data 消息数据
   * @param {boolean} expectResponse 是否期待响应
   * @returns {Promise<any>}
   */
  sendMessage(type: string, data: any, expectResponse = false) {
    return new Promise((resolve, reject) => {
      if (!this.iframe || !this.iframe.contentWindow) {
        reject(new Error('Iframe not available'))
        return
      }

      const id = expectResponse ? this.generateMessageId() : null
      const message = {
        type,
        data,
        id,
        source: 'parent',
      }

      // 如果期待响应，设置超时和回调
      if (expectResponse) {
        const timeoutId = setTimeout(() => {
          id && this.pendingRequests.delete(id)
          reject(new Error(`Message timeout: ${type}`))
        }, this.timeout)

        id &&
          this.pendingRequests.set(id, {
            resolve,
            reject,
            timeoutId,
          })
      } else {
        // 不期待响应，直接resolve
        resolve(void 0)
      }

      this.iframe.contentWindow.postMessage(message, this.targetOrigin)
      console.log('Sent message to DBER:', type, data)
    })
  }

  /**
   * 等待DBER准备就绪
   * @returns {Promise<any>}
   */
  waitForReady() {
    return new Promise((resolve) => {
      if (this.isReady) {
        resolve(true)
      } else {
        this.readyCallbacks.push(resolve)
      }
    })
  }

  /**
   * 设置图形数据
   * @param {Object} graphData 图形数据
   * @returns {Promise<any>}
   */
  async setData(graphData: DatabaseDesign) {
    await this.waitForReady()
    return this.sendMessage('set-data', graphData, true)
  }

  /**
   * 获取当前图形数据
   * @returns {Promise<Object>}
   */
  async getData() {
    await this.waitForReady()
    return this.sendMessage('get-data', null, true)
  }

  /**
   * 清空所有数据
   * @returns {Promise<any>}
   */
  async clearData() {
    await this.waitForReady()
    return this.sendMessage('clear-data', null, true)
  }

  /**
   * 导出数据
   * @param {string} format 导出格式 (json, mysql, postgres, etc.)
   * @returns {Promise<Object>}
   */
  async exportData(format = 'json') {
    await this.waitForReady()
    return this.sendMessage('export-data', { format }, true)
  }

  /**
   * 设置图形名称
   * @param {string} name 图形名称
   * @returns {Promise<any>}
   */
  async setName(name: string) {
    await this.waitForReady()
    return this.sendMessage('set-name', { name }, true)
  }

  /**
   * 添加表
   * @param {Object} table 表数据
   * @returns {Promise<any>}
   */
  async addTable(table: any) {
    await this.waitForReady()
    return this.sendMessage('add-table', { table }, true)
  }

  /**
   * 删除表
   * @param {string} tableId 表ID
   * @returns {Promise<any>}
   */
  async removeTable(tableId: string) {
    await this.waitForReady()
    return this.sendMessage('remove-table', { tableId }, true)
  }

  /**
   * 批量更新操作
   * @param {Array} operations 操作列表
   * @returns {Promise<any>}
   */
  async batchUpdate(operations: any[]) {
    await this.waitForReady()
    return this.sendMessage('batch-update', { operations }, true)
  }

  /**
   * 添加事件监听器
   * @param {string} type 事件类型
   * @param {Function} listener 监听器函数
   */
  on(type: string, listener: Function) {
    if (!this.eventListeners.has(type)) {
      this.eventListeners.set(type, [])
    }
    this.eventListeners.get(type)?.push(listener)
  }

  /**
   * 移除事件监听器
   * @param {string} type 事件类型
   * @param {Function} listener 监听器函数
   */
  off(type: string, listener: Function) {
    const listeners = this.eventListeners.get(type)
    if (listeners) {
      const index = listeners.indexOf(listener)
      if (index > -1) {
        listeners.splice(index, 1)
      }
    }
  }

  /**
   * 销毁客户端
   */
  destroy() {
    // 移除事件监听器，使用保存的引用
    if (this.boundHandleMessage) {
      window.removeEventListener('message', this.boundHandleMessage)
      this.boundHandleMessage = null
    }

    // 清理所有待处理的请求
    this.pendingRequests.forEach((request) => {
      clearTimeout(request.timeoutId)
      request.reject(new Error('Client destroyed'))
    })
    this.pendingRequests.clear()

    this.eventListeners.clear()
    this.readyCallbacks = []
    this.isReady = false
  }
}

export default IframeClient
