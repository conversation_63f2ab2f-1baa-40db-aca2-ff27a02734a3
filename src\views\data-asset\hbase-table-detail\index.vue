<template>
  <div class="table-detail">
    <cardBox :title="tableName">
      <a-tabs v-model:activeKey="activeKey" class="table-detail-tabs">
        <template #leftExtra>
          <span type="link" @click="goBack" class="go-back-btn">
            <ArrowLeftOutlined />
          </span>
        </template>
        <a-tab-pane key="1">
          <template #tab>
            <span>
              <UnorderedListOutlined />
              列
            </span>
          </template>
          <div class="content">
            <!-- 列的内容 -->
            <Table
              :columns="columns"
              :getData="transgetScanDataApi"
              :autoRequest="false"
              ref="tableRef"
              :searchFormState="searchFormData"
            >
            </Table>
          </div>
          <!-- <div class="data-preview">
          <a-row justify="start" align="top" type="flex" :wrap="true" class="data-preview-row">
            <a-col :span="24" class="data-preview-col">
            </a-col>
          </a-row>
        </div> -->
        </a-tab-pane>
      </a-tabs>
    </cardBox>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { UnorderedListOutlined } from '@ant-design/icons-vue'
import { ArrowLeftOutlined } from '@ant-design/icons-vue'
import { getScanDataApi } from '@/api/services/data-asset/hbase-management'
import { Table } from '@fs/fs-components'

const route = useRoute()
const router = useRouter()
const activeKey = ref('1')
const tableName = computed(() => (route.query.tableName as string) || '')
const tableComment = computed(() => (route.query.tableComment as string) || '')

const tableRef = ref<any>(null)
const tableDetailRef = ref<any[]>([])

const columns = computed(
  () =>
    Object.keys(tableDetailRef.value?.[0]).map((item: string) => {
      return {
        key: item,
        title: item,
        dataIndex: item,
      }
    }) ?? [],
)

const searchFormData = ref<any>({})

const transgetScanDataApi = (params: any) =>
  getScanDataApi(params).then((d: any) => {
    // 接口应该返回records 前端做处理
    // d.data.records = d.data.data
    tableDetailRef.value = d.data.records
    return d
  })

const fetchTableData = async () => {
  try {
    setTimeout(() => {
      tableRef.value?.getTableData()
    }, 20)
  } catch (error) {
    console.error('获取数据表列表失败:', error)
  }
}

const goBack = () => {
  setTimeout(() => {
    router.back()
  }, 300)
}

onMounted(() => {
  // 有数据的入参
  // const mockParams: IDbParams = {
  //     id: '10188',
  //     namespace: 'hbase',
  //     tableName: 'test_all_col_poc_new',
  // }
  searchFormData.value = {
    id: route.query.id as string,
    namespace: route.query.namespace as string,
    tableName: route.query.tableName as string,
    // ...mockParams
  }
  // console.log('searchFormData:',route.query)
  if (!searchFormData.value.id) return
  fetchTableData()
  // fetchTableDetail(params)
})
</script>

<style lang="less" scoped>
// .data-preview {
//   width: 100%;
//   height: 100%;
//   // overflow: auto;
// }

.table-detail {
  flex: auto;
  height: 100%;
  user-select: none;
  background: white;
  .table-detail-col:last-of-type {
    border-width: 0px;
  }
}
.content :deep(.filter-box) {
  display: none;
}
.go-back-btn {
  color: var(--primary-color);
  margin-left: 5px;
  margin-right: 10px;
  cursor: pointer;
}
</style>
