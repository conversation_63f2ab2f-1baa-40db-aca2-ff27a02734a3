import type { RouteRecordRaw } from 'vue-router'

/**
 * 权限系统路由
 */
const routes: RouteRecordRaw[] = [
  {
    path: '/home',
    name: 'home',
    // component: () => import('@/views/permission/home/<USER>'),
    component: () => import('@/views/sql-editing/index.vue'), // 临时测试
    meta: {
      icon: 'HomeOutlined',
    },
  },
  {
    path: '/getSsoAppList',
    name: 'getSsoAppList',
    component: () => import('@/views/permission/single-sign.vue'),
    meta: {
      icon: 'LoginOutlined',
    },
  },
  {
    path: '/TebantSelect',
    name: 'TebantSelect',
    component: () => import('@/views/permission/tebant-select.vue'),
    meta: {
      icon: 'FunnelPlotOutlined',
    },
  },
  {
    path: '/ParameterSelect',
    name: 'ParameterSelect',
    component: () => import('@/views/permission/parameter-select.vue'),
    meta: {
      icon: 'GoldOutlined',
    },
  },
  {
    path: '/SystemManagement',
    name: 'SystemManagement',
    component: () => import('@/views/permission/system-manage-ment.vue'),
    meta: {
      icon: 'HourglassOutlined',
    },
  },
  {
    path: '/detailSystem',
    name: 'detailSystem',
    component: () => import('@/views/permission/detail-system.vue'),
    meta: {
      icon: 'LockOutlined',
    },
  },
  {
    path: '/positionManageList',
    name: 'positionManageList',
    component: () => import('@/views/permission/position-manage-list.vue'),
    meta: {
      icon: 'PartitionOutlined',
    },
  },
  {
    path: '/organizationalManagement',
    name: 'organizationalManagement',
    component: () => import('@/views/permission/organizational-management.vue'),
    meta: {
      icon: 'ProjectOutlined',
    },
  },
  {
    path: '/RoleManagement',
    name: 'RoleManagement',
    component: () => import('@/views/permission/role-management.vue'),
    meta: {
      icon: 'SecurityScanOutlined',
    },
  },
  {
    path: '/positionManage',
    name: 'positionManage',
    component: () => import('@/views/permission/position-manage.vue'),
    meta: {
      icon: 'SolutionOutlined',
    },
  },
  {
    path: '/userGroupList',
    name: 'userGroupList',
    component: () => import('@/views/permission/user-group-list.vue'),
    meta: {
      icon: 'UserOutlined',
    },
  },
  {
    path: '/userManagementList',
    name: 'userManagementList',
    component: () => import('@/views/permission/user-management.vue'),
    meta: {
      icon: 'WalletOutlined',
    },
  },
  {
    path: '/BuSelect',
    name: 'BuSelect',
    component: () => import('@/views/permission/bu-management.vue'),
    meta: {
      icon: 'BlockOutlined',
    },
  },
  {
    path: '/organizationType',
    name: 'organizationType',
    component: () => import('@/views/permission/organization-type.vue'),
    meta: {
      icon: 'BlockOutlined',
    },
  },
]

export default routes
