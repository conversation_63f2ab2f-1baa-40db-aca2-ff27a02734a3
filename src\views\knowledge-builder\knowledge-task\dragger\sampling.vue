<template>
  <div class="content">
    <a-collapse v-model:activeKey="activeKey">
      <a-collapse-panel v-for="(item, index) in sampleList" :key="index" :header="item.id">
        <a-table :dataSource="item.child" :columns="sampleColumns" :pagination="false"></a-table>
      </a-collapse-panel>
    </a-collapse>
    <a-empty v-if="!sampleList.length" :image="simpleImage" />
  </div>
</template>

<script lang="ts" setup>
import { reqGetSampleData, reqGetSampleMap } from '@/api/services/knowledge'
import { Empty } from 'ant-design-vue'
import { sampleColumns } from './data-source'
const simpleImage = Empty.PRESENTED_IMAGE_SIMPLE

const prop = defineProps<{ currentEchartsData: any }>()
const route = useRoute()
const sampleMap = ref({})
const sampleList = ref<any>([])
const activeKey = ref(['1'])

const getSampleData = async () => {
  const mapData = await getSampleMap()
  const res = await reqGetSampleData({
    name: prop?.currentEchartsData?.name,
    limit: 10,
    projectId: 1,
  })
  // sampleList.value = res
  const list = res.map((item: any) => {
    const child = Object.keys(item.properties)
      .map((i) => ({
        label: mapData[`${item.label}.${i}`],
        name: item?.properties?.[i],
      }))
      .filter((ii: any) => ii.label)
    item.child = child
    return item
  })
  sampleList.value = list

  console.log(res, mapData, list)
}

const getSampleMap = async () => {
  const res = await reqGetSampleMap({
    projectId: route.query?.projectId,
  })
  sampleMap.value = res
  console.log('抽样数据', res)
  return res
}

onMounted(() => {
  getSampleData()
})
</script>
<style scoped lang="less"></style>
