export const tableExample = [
  {
    label: '获取接口数据',
    name: 'getData',
    type: 'requestApi',
    value: [
      {
        label: '查询接口名称',
        name: 'interface',
        value: 'getDataHandle',
        valueType: 'string',
      },
      {
        label: '查询接口引用路径',
        name: 'interfaceImport',
        value: "import { getDataHandle } from '@/api/services/test'",
        valueType: 'string',
      },
    ],
  },
  {
    label: '获取表头列表数据',
    name: 'getColumns',
    value: [
      {
        label: '表头列表数据',
        type: 'columnsApi',
        name: 'columnsData',
        valueType: 'array',
        value: [
          {
            name: 'name',
            description: '名字描述',
          },
          {
            name: 'age',
            description: '年龄描述',
          },
        ],
      },
    ],
  },
  {
    label: '获取新增表单字段',
    name: 'formColumns',
    value: [
      {
        label: '新增表单数据列表',
        type: 'columnsApi',
        name: 'addFormColumns',
        valueType: 'array',
        value: [
          {
            type: 'input',
            name: 'name',
            description: '名字',
            required: true,
          },
          {
            type: 'input',
            name: 'age',
            description: '名字',
            required: true,
          },
        ],
      },
    ],
  },
  {
    label: '获取搜索表单字段',
    name: 'searchColumns',
    value: [
      {
        label: '搜索表单数据列表',
        type: 'columnsApi',
        name: 'searchColumnsData',
        valueType: 'array',
        value: [
          {
            type: 'input',
            name: 'name',
            description: '名字',
          },
          {
            type: 'input',
            name: 'age',
            description: '年龄',
          },
        ],
      },
    ],
  },
  {
    name: 'editData',
    label: '编辑接口',
    type: 'requestApi',
    value: [
      {
        label: '编辑接口名称',
        value: 'editDataHanlde',
        name: 'interface',
        valueType: 'string',
      },
      {
        label: '编辑接口引用路径',
        name: 'interfaceImport',
        value: "import { editDataHanlde } from '@/api/services/test'",
        valueType: 'string',
      },
    ],
  },
  {
    name: 'delData',
    label: '删除接口',
    type: 'requestApi',
    value: [
      {
        label: '删除接口名称',
        name: 'interface',
        value: 'delDataHanlde',
        valueType: 'string',
      },
      {
        label: '删除接口引用路径',
        name: 'interfaceImport',
        value: "import { delDataHanlde } from '@/api/services/test'",
        valueType: 'string',
      },
    ],
  },
  {
    name: 'addData',
    label: '新增接口',
    type: 'requestApi',
    value: [
      {
        label: '新增接口名称',
        name: 'interface',
        value: 'addDataHandle',
        valueType: 'string',
      },
      {
        label: '新增接口引用路径',
        name: 'interfaceImport',
        value: "import { addDataHandle } from '@/api/services/test'",
        valueType: 'string',
      },
    ],
  },
]

export const formExample = [
  {
    label: '获取表单列表数据',
    name: 'getColumns',
    value: [
      {
        label: '数据列表',
        type: 'columnsApi',
        name: 'getDataHandle',
        valueType: 'array',
        value: [
          {
            name: 'name',
            description: '名字',
            type: 'input',
            required: true,
          },
          {
            name: 'age',
            description: '年龄',
            type: 'input',
            required: false,
          },
          {
            name: 'sex',
            description: '姓名',
            type: 'select',
            compProps: {
              placeholder: '请选择',
              options: [
                {
                  label: '男',
                  value: '0',
                },
                {
                  label: '女',
                  value: '1',
                },
              ],
            },
          },
        ],
      },
    ],
  },
  {
    name: 'layout',
    label: '表单布局',
    value: [
      {
        label: '表单布局数据',
        type: 'input',
        name: 'layoutConfig',
        valueType: 'object',
        value: '{  layout: "horizontal",  labelCol: { span: 4 },  wrapperCol: { span: 18 }}',
      },
    ],
  },
  {
    label: '新增接口',
    name: 'addData',
    type: 'requestApi',
    value: [
      {
        label: '新增接口名称',
        valueType: 'string',
        name: 'interface',
        value: 'addDataHandle',
      },
      {
        label: '新增接口引用路径',
        name: 'interfaceImport',
        valueType: 'string',
        value: "import { addDataHandle } from '@/api/services/test'",
      },
    ],
  },
]
