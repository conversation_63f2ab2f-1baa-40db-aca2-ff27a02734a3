<template>
  <main
    class="main"
    :class="{ 'main-bg': backdrop }"
    :style="{ height: preview ? (backdrop ? '100%' : '100vh') : '100%' }"
  >
    <div
      class="section-login"
      :class="{ 'section-login-bg': backdrop, preview: preview && backdrop }"
    >
      <Login
        :system-code="SYSTEM_CODE"
        :interfaceConfig="{
          getVerifyReqUrl: '/api/301UC/loginManager/getVerifyCode', // 获取图形验证码
          loginReqUrl: '/api/301UC/loginManager/loginCheck', // 登录请求地址
          getPhoneVerifyReqUrl: '/api/301UC/loginManager/sendSmsCode', // 获取手机验证码
          verifyReqUrl: '/api/301UC/loginManager/verifyCodeLogin', // 验证码登录地址
          forgetPasswordReqUrl: '/api/301UC/loginManager/forgetPassword', // 忘记密码登录地址
          getAuthReqUrl: '/api/docEditorFront/userCenter/getAuthUrl', // 获取微信、支付宝登陆页地址并跳转  code 1 微信  2 支付宝
          registerReqUrl: 'register', // 注册地址
        }"
        :set-public-key="PUBLICKEY"
        :showSignIn="false"
        :showWx="false"
        :showAlipay="false"
        :isAccountLogin="false"
      />
    </div>
  </main>
</template>

<script setup lang="ts">
import { message } from 'ant-design-vue'
import { Login } from '@fs/fs-components'
import { SYSTEM_CODE, PUBLICKEY, PROJECT_AUTHORITYCENTER } from '@/api/base'
import { getUserMenuFn } from '@/utils/user'

defineProps({
  backdrop: {
    type: Boolean,
    default: false,
  },
  preview: {
    type: Boolean,
    default: false,
  },
})
</script>

<style scoped lang="less">
.main {
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f0f4f9;
  background-image: url(../login/bg.png);
  background-repeat: no-repeat;
  background-position: center;
  background-size: 100% 100%;
}
.section-login {
  width: 368px;
  height: 436px;
  padding: 50px 36px;
  background-color: #fff;
  box-shadow: 5px 5px 10px rgba(0, 0, 0, 0.08);
}
.logo-img {
  width: 120px;
  height: 63px;
}

.main-bg {
  background-image: url(@/assets/bg.png);
}
.section-login-bg {
  width: 368px;
  height: 100vh !important;
  padding: 50px 36px;
  padding-top: 50vh;
  background-color: #fff;
  box-shadow: 5px 5px 10px rgba(0, 0, 0, 0.08);
}

.preview {
  height: 910px !important;
  padding: 0px 36px;
  .fs-login {
    position: relative;
    top: 200px;
  }
}
</style>
