import { createRouter, createWebHistory, type RouteLocationNormalized } from 'vue-router'
import { useUserStore } from '@/stores/user'
import LayoutView from '@/layout/layout-view.vue'
import permissionRoutes from '../core/permission'
// import toolRoutes from '../core/tool'
import pageRoutes from '../biz/page'
import generate from '../biz/generate'
import dataAsset from '../biz/data-asset'
import mateDate from '../biz/mate-data'
import monitorMaintenance from '../biz/monitor-maintenance'
import type { RouteRecordRaw } from 'vue-router'
import { storeToRefs } from 'pinia'
import MenuData from '@/layout/login/init-menu'
import defaultRoutes from '../core/base'
import { getUserInfoFn, getUserMenuFn } from '@/utils/user'
import { getExternalLoginUrl } from '@/utils/auth-util'
import { useAppStore } from '@/stores/app'

declare module 'vue-router' {
  interface RouteMeta {
    title?: string // 页签标题
    id?: string // 菜单id
    menuKeyPath?: string[] // 菜单id路径
    icon?: string // antd-icon 图标名
  }
}

const whiteRouter = ['/login']

const routes = [
  ...permissionRoutes,
  // ...toolRoutes,
  ...pageRoutes,
  ...generate,
  ...dataAsset,
  ...mateDate,
  ...monitorMaintenance,
]
export const routeMap = getRouteMap(routes)

/**
 * history 模式
 * 如果要使用 hashchange 模式，参考文档 @see https://router.vuejs.org/zh/guide/essentials/history-mode.html#Hash-%E6%A8%A1%E5%BC%8F
 *
 */
const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'layout',
      component: LayoutView,
      redirect: '/home',
      children: routes,
    },
    ...defaultRoutes,
  ],
})

let menuDataInitialized = false
let hasCheckedSharedToken = false
router.beforeEach(async (to: RouteLocationNormalized) => {
  // 如果配置不开启登录鉴权，则不进行路由拦截
  // if (import.meta.env.VITE_APP_NEED_AUTH === 'false') {
  //   return
  // }
  const userStore = useUserStore()
  // 首次访问时检查共享token
  if (!hasCheckedSharedToken) {
    userStore.syncTokenFromCookie()
    if (userStore.token) {
      await getUserInfoFn(userStore.token)
      await getUserMenuFn()
    }
    hasCheckedSharedToken = true
  }
  const whiteList: RouteLocationNormalized['name'][] = ['fc-form']

  const { userMenu } = storeToRefs(userStore)

  if (!to.meta.clonePermission && !menuDataInitialized) {
    if (userStore && userStore.isLoggedIn && Object.keys(userMenu.value).length != 0) {
      menuDataInitialized = true
      MenuData(userMenu.value, userMenu, userStore, () => {
        router.replace(to.fullPath)
      })
    }
  }
  // 默认选择
  if (to && to?.name === (Object.keys(userMenu.value).length > 0 && userMenu.value[0].menuUrl)) {
    userStore.currentMenuKeyPath = [to?.meta.id as string]
  }
  if (!userStore.isLoggedIn && to.name !== 'login' && !whiteList.includes(to.name)) {
    console.log('🚀 ~ router.beforeEach ~ window:', window)

    if (window.ENV && window.ENV.EXTERNAL_LOGIN_URL && window.ENV.TO_THIRD_LOGIN) {
      const loginUrl = getExternalLoginUrl(to.fullPath)
      // 跳转到外部登录系统
      // alert(`跳转到外部登录系统: ${loginUrl}`)
      window.location.href = loginUrl
    } else {
      return { name: 'login' }
    }
  }
  const appStore = useAppStore()
  // 过滤白名单路由和404
  const isWhite = whiteRouter.includes(to.path) || to.name === 'empty'
  const { isSecondary = false, title = '', menuId = '', keepAlive = true } = to.meta
  console.log('🚀 ~ menuId:', menuId)
  console.log('🚀 ~ to.meta:', to)
  if (!isWhite && !isSecondary && keepAlive) {
    // 把跳转路由加入头部缓存
    appStore.setMenuTabs({
      menuUrl: to.fullPath,
      path: to.path,
      // 实时任务监控-查看结果 'realTimeTableQuery' 需要用 to.query.pageName
      menuName:
        to.name === 'iframePage' || to.name === 'realTimeTableQuery' ? to.query.pageName : title,
      menuId: to.name === 'iframePage' ? to.query.menuId : menuId,
    })
  }
})

router.afterEach((to) => {
  document.title = ((to.name === 'iframePage' ? to.query.pageName : to.meta.title) as string) || ''
})

/**
 * 获取路由Map
 */
function getRouteMap(routes: RouteRecordRaw[]) {
  const obj: Record<string, RouteRecordRaw> = {}
  routes.forEach((item) => {
    if (item.name) {
      const key = item.name
      //@ts-ignore
      obj[key] = item
    }
  })
  return obj
}

export default router
