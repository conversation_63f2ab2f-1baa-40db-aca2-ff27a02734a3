<template>
  <a-modal
    :visible="visible"
    :title="'新增数据库'"
    @ok="handleOk"
    @cancel="handleCancel"
    :maskClosable="false"
    width="700px"
    :confirm-loading="loading"
  >
    <a-form
      ref="formRef"
      :model="formState"
      :rules="rules"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 16 }"
    >
      <a-form-item label="数据库名称" name="namespace">
        <a-input
          show-count
          :maxlength="30"
          v-model:value="formState.namespace"
          placeholder="请输入数据库名称"
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script lang="ts" setup>
import { ref, defineProps, defineEmits, watch } from 'vue'

import { addDbItem } from '@/api/services/data-asset/hbase-management'
import { message } from 'ant-design-vue'
import type { RuleObject } from 'ant-design-vue/es/form'

const props = defineProps<{
  visible: boolean
  parentData: Record<string, any>
}>()

const emit = defineEmits(['update:visible', 'success'])
const loading = ref(false)

const formRef = ref()
const formState = ref({
  namespace: '',
})

const validateName = async (_rule: RuleObject, value: string) => {
  if (value === '') {
    return Promise.reject('请输入数据库名称')
  }
  if (!/^[a-zA-Z0-9_\u4e00-\u9fa5]+$/u.test(value)) {
    return Promise.reject('数据库名称只能包含以下字符：下划线 (_)')
  }
  return Promise.resolve()
}

// /^[a-zA-Z0-9_.]{1,255}$/
const rules = {
  namespace: [
    // { required: true, min: 3, max: 255, message: '请输入字符串长度在3-255，有且只包含以下字符下划线 (_)、字符 (-)、点 (.)' },
    { required: true, max: 30, validator: validateName },
  ],
}

const handleOk = async () => {
  try {
    await formRef.value.validate()
    loading.value = true
    const obj: any = {
      namespace: formState.value.namespace,
      id: props?.parentData?.id,
    }
    const res = await addDbItem(obj)
    if (res.code === '000000') {
      message.success('创建成功')
      emit('success', { id: obj.namespace, name: obj.namespace, dbUrlId: obj.id })
      handleCancel()
    } else {
      message.error('创建失败:' + res.msg)
    }
    loading.value = false
  } catch (error) {
    console.error('创建失败:', error)
    loading.value = false
  }
}

const handleCancel = () => {
  formRef.value?.resetFields()
  emit('update:visible', false)
}

watch(
  () => props.visible,
  async (newVal) => {
    if (!newVal) {
      formRef.value?.resetFields()
    }
  },
)
</script>
