<script lang="ts" setup>
import { useRouter, useRoute } from 'vue-router'
import { onMounted, onUnmounted, ref, reactive, nextTick, watch } from 'vue'
import * as echarts from 'echarts/core'
import { TreeChart } from 'echarts/charts'
import { TooltipComponent } from 'echarts/components'
import { CanvasRenderer } from 'echarts/renderers'
import Dragger from './concept-dragger.vue'
import type { ECBasicOption } from 'echarts/types/dist/shared'
import { reqGetConceptTreeData } from '@/api/services/knowledge'

echarts.use([TooltipComponent, TreeChart, CanvasRenderer])

const router = useRouter()
const route = useRoute()
// Removed unused timer variable
const chartRef = ref<HTMLElement>()
const currentEchartsData = ref()
const treeData = ref<any>([])
const draggerRef = ref<{ showDrawer: () => void } | null>(null)
let chartInstance: echarts.ECharts | null = null
const option = reactive<ECBasicOption>({
  tooltip: {
    trigger: 'item',
    formatter: (params: any) => {
      if (!params?.data) return ''
      const node = params.data.conceptDTO
      return `
      ${node.nameZh}<br/>
      ${node.name || ''}
    `
    },
  },
  series: [
    {
      type: 'tree',
      width: '20%',
      data: treeData,
      orient: 'LR', // 垂直方向布局
      layout: 'orthogonal',
      expandAndCollapse: true, // 展开折叠
      roam: true, // 开启平移
      // 节点样式配置
      itemStyle: {
        color: '#1890ff', // 蓝色节点
        borderWidth: 0,
      },
      left: '20%',

      // 标签样式
      label: {
        color: 'white',
        padding: [5, 5],
        backgroundColor: '#818aa5',
        fontSize: 14,
        position: 'left',
        verticalAlign: 'middle',
        align: 'left',
        distance: 10,
        formatter: function (param: any) {
          console.log('param', param)

          return `${param?.data?.conceptDTO?.nameZh}(${param?.data?.conceptDTO?.name})`
        },
      },

      // 叶子节点特殊配置
      leaves: {
        label: {
          backgroundColor: 'transparent',
          color: '#7d7d7d',
          distance: 15,
          position: 'inside',
          verticalAlign: 'middle',
          align: 'left',
        },
      },

      // 连接线样式
      lineStyle: {
        color: '#1890ff',
        width: 2,
        curveness: 0.5, // 边的曲度
      },

      // 符号样式（圆点标记）
      symbol: 'circle',
      symbolSize: 8,
    },
  ],
})

const showDrawerHandler = (params: any) => {
  const obj = {
    ...params.data.conceptDTO,
    children: params.data.children,
  }
  currentEchartsData.value = obj
  draggerRef.value?.showDrawer?.()
}

const initRelationshipChart = () => {
  chartInstance = echarts.init(chartRef.value)
  chartInstance.setOption(option)
  chartInstance.on('click', showDrawerHandler)
}
// 窗口自适应
const resizeHandler = () => {
  console.log('窗口变化啦')
  setTimeout(function () {
    chartInstance?.resize()
  }, 200)
}

watch(
  () => treeData.value,
  (newVal) => {
    console.log('有变化啦~~~', newVal, chartRef)

    if (newVal?.children?.length) {
      ;(option.series as any)[0].data = [newVal] || []
      nextTick(() => {})
      initRelationshipChart()
    }
  },
  { immediate: true, deep: true },
)

const getTreeData = async () => {
  const res = await reqGetConceptTreeData({
    projectId: route.query?.projectId,
  })
  treeData.value = res
  console.log('获取到树数据了', res)
}

onMounted(() => {
  getTreeData()
  window.addEventListener('resize', resizeHandler)
})
onUnmounted(() => {
  window.removeEventListener('resize', resizeHandler)
  chartInstance?.dispose()
})
</script>

<template>
  <div class="conceptual">
    <a-page-header title="概念模型" @back="() => router.back()" />
    <div class="tree-wrap" ref="contentRef">
      <div class="tree-container" ref="chartRef"></div>
      <Dragger ref="draggerRef" :currentEchartsData="currentEchartsData" />
    </div>
  </div>
</template>

<style lang="less" scoped>
:deep(.ant-page-header) {
  padding: 0;
}
.conceptual {
  width: 100%;
  height: 100%;
}
.tree-wrap {
  width: 100%;
  height: calc(100% - 40px);
  background: #fff;
  position: relative;
  right: 0;
  overflow: hidden;
}
.tree-container {
  box-sizing: border-box;
  overflow: hidden;
  // width: 100%;
  height: 100%;
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
}
</style>
