import { getModuleName, getToLocaleLowerCase } from '../tools/index.js'

// 存储需要添加可选标记的头部名称数组
const whileArr = ['authorization', 'opuser', 'opusername', 'buid', 'tenantid']

/**
 * 生成模板API代码
 * @param {Array} list - API列表
 * @returns {string} - 生成的模板API代码
 */
export function generateTemplateApi(item, list, moduleName) {
  try {
    // 构建API代码内容
    let content = `import service from '@/api'
import { MOCK_DATA } from './${getToLocaleLowerCase(moduleName ? moduleName : getModuleName(item, list))}-mock' 
    `
    item.interfaceInfos.map((val) => {
      content += `
${getFuncAnnotation(val, val.params, val.response)} 
${generateTemplateApiFunc(val, val.params, val.response)}
`
    })
    return content
  } catch (err) {
    console.log('%c 🍲 err: ', 'font-size:12px;background-color: #42b983;color:#fff;', err)
  }

  // 使用prettier格式化代码
}

/**
 * 生成模板API函数的代码
 * @param {Object} config - API配置对象
 * @param {string} config.apiName - API名称
 * @param {string} config.url - API的URL
 * @param {string} config.method - API的请求方法
 * @param {Object} [config.headers={}] - API的请求头
 * @param {string} config.responseType - API的响应类型
 * @returns {string} - 生成的模板API函数的代码
 */
export function generateTemplateApiFunc(val, paramData, response) {
  return `
export async function ${val.interfaceName} (${getParam(paramData)}${getHeader(val?.headers)}): Promise<${getReturn(response)}> {
  return service({
    method: '${val.httpMethodName}',
    url: '${val.fullPath}',
    ${getBodyParmas(val.httpMethodName)}${val?.headers && val?.headers.length > 0 ? ',' : ''}
    ${getHeaderParams(val?.headers)}
  }, MOCK_DATA.${val.interfaceName})
}`
}

/**
 * 根据请求方法获取请求体参数的描述
 * @param {string} methods - 请求方法
 * @returns {string} - 请求体参数的描述
 */
function getBodyParmas(methods) {
  if (methods.toLocaleLowerCase() === 'get') {
    return 'params: data'
  }
  return 'data'
}

/**
 * 获取请求头参数
 * @param {Array} headers - 请求头参数数组
 * @returns {string} - 请求头参数字符串
 * @description
 * 这个函数用于获取请求头参数。如果传入的 headers 数组长度大于 0，则返回 'headers'，否则返回空字符串。
 */
function getHeaderParams(headers) {
  try {
    if (headers && headers.length > 0) {
      return 'headers'
    }
    return ''
  } catch (err) {
    console.log('生成函数请求头失败：' + err)
  }
}

/**
 * 根据参数数据生成返回值字符串类型。
 *
 * @param {Array} paramData - 包含参数信息的对象数组。
 * @param {boolean} state - 是否返回一个对象字符串。
 * @returns {string} 返回格式化的返回值字符串。
 *
 * @description
 * 此函数接收一个参数数据数组，生成一个表示返回值类型的字符串。
 * 如果参数数据为空，返回 'any'。
 * 否则，根据参数数据生成一个对象字符串，包含每个参数的名称和类型。
 * 如果 state 为 true，则返回一个对象字符串，否则返回一个类型字符串。
 *
 * @example
 * const params = [
 *   { parameterName: 'name', dataType: 'string' },
 *   { parameterName: 'age', dataType: 'number' }
 * ];
 * const result = getReturn(params, true);
 * console.log(result);
 * // 输出: "{ name:string,  age:number, }"
 */
export function getReturn(paramData, state) {
  if (!paramData || paramData.length === 0) {
    return 'any'
  }

  // 辅助函数，用于获取类型表示
  const getTypeRepresentation = (type, isArray) => {
    if (Array.isArray(type)) {
      const nestedType = getReturn(type, true)
      return wrapArrayType(nestedType, isArray)
    }
    return type
  }

  let str = ''

  paramData.map((item, index) => {
    const itemType = getTypeRepresentation(item.type, item.isArray)

    // 拼接逻辑保持不变
    str += `${index === 0 && !state ? '\r' : ''} ${item.name}: ${itemType}${
      state ? (index === paramData.length - 1 ? ' ' : ',') : '\r'
    }`
  })

  return state ? `${str}` : `{${str}}`
}

// 辅助函数，用于根据是否为数组包装类型
const wrapArrayType = (type, isArray) => {
  if (isArray && type === 'any') {
    return 'any'
  }
  return isArray ? `Array<{${type}}>` : `{${type}}`
}

/**
 * 生成参数注释字符串。
 * @param {Array} paramData - 包含参数信息的对象数组。
 * @returns {string} 返回格式化的参数注释字符串。
 */
export function getAnnotation(paramData) {
  let str = ''
  paramData.map((item, index) => {
    str += `${index !== 0 ? '  ' : ''}* @param {${item.type}} ${item.name} - ${item.description} \r`
  })
  return str
}

// 导出一个名为 getParam 的函数，用于根据参数数据和类型生成参数字符串
// @param {Array} paramData - 输入的参数数据数组，数组中的元素可能包含子元素，每个元素可能具有 name、type 等属性
// @param {String} type - 参数的类型，默认为 'data'
// @return {String} - 最终生成的参数字符串，包含类型和参数信息
export function getParam(paramData, type = 'data') {
  // 使用 reduce 方法将参数数据转换为字符串
  const str = paramData.reduce((acc, item, index) => {
    let typeStr
    // 如果参数有子元素，调用 getListParam 函数处理子元素
    if (item?.children?.length > 0) {
      typeStr = getListParam(item.children)
      // 如果参数类型为 'array'，将类型字符串设置为 'Array<any>'
    } else if (item.type === 'array') {
      typeStr = 'Array<any>'
    } else {
      typeStr = item.type
    }
    // 拼接参数名称、是否必需标记和类型字符串
    return acc + `${index === 0 ? '\r' : ''} ${item.name}${getRequired(item, type)}: ${typeStr} \r`
  }, '')
  // 最终返回包含类型和参数信息的字符串
  return `${type}${getRequiredState(paramData, type)}: ${str ? `{${str}}` : 'any'}`
}

// 定义一个名为 getListParam 的函数，用于将参数数据转换为特定格式的字符串
// @param {Array} paramData - 输入的参数数据数组，数组中的元素可能包含子元素，每个元素可能具有 name、type、required 等属性
// @return {String} - 最终生成的参数字符串，如果生成的字符串不为空，将使用 wrapArrayType 函数包装，否则返回 'any'
function getListParam(paramData) {
  // 使用 reduce 方法遍历 paramData 数组，将其元素组合成一个字符串
  const str = paramData.reduce((acc, item, index) => {
    let typeStr
    // 如果当前元素有子元素，递归调用 getListParam 函数处理子元素
    if (item?.children?.length > 0) {
      typeStr = getListParam(item.children)
    } else {
      // 否则，使用元素的类型作为类型字符串
      typeStr = item.type
    }
    // 根据元素是否必需添加相应的标记，并将元素名称、标记和类型字符串拼接起来
    return acc + `${index === 0 ? '\r' : ''} ${item.name}${item.required ? '' : '?'}: ${typeStr} \r`
  }, '')

  // 如果生成的字符串不为空，使用 wrapArrayType 函数包装，否则返回 'any'
  return str ? wrapArrayType(str, true) : 'any'
}

/**
 * 此函数用于确定参数是否为必需的，并根据不同情况添加相应的标记。
 *
 * @param {Object} item - 包含参数信息的对象，可能包含名称、是否必需等属性。
 * @param {String} type - 参数的类型，可能用于进一步的条件判断。
 * @returns {String} - 若满足特定条件返回 '?' 表示可选，若为必需则返回空字符串。
 *
 * 函数内部逻辑如下：
 * 1. 首先，定义了一个名为 whileArr 的数组，存储了一些特定的头部名称。
 * 2. 检查 type 是否为 'headers' 且 item 的名称（转换为小写）是否在 whileArr 中。
 *    - 若满足此条件，返回 '?'，表示该参数是可选的。
 * 3. 接着，根据 item.required 的值判断参数是否必需。
 *    - 若 item.required 为 false，返回 '?'，表示该参数是可选的。
 *    - 若 item.required 为 true，返回空字符串，表示该参数是必需的。
 */

function getRequired(item, type) {
  // 如果类型是 'headers' 并且 item 的名称（转换为小写）在 whileArr 中，则添加 '?' 表示该参数是可选的
  if (type === 'headers' && whileArr.includes(getToLocaleLowerCase(item.name))) {
    return '?'
  }
  // 如果 item 是必需的，返回空字符串
  if (item.required) return ''
  // 如果 item 不是必需的，返回 '?' 表示该参数是可选的
  if (!item.required) return '?'
}

/**
 * 此函数用于获取所需状态 是否必填 如果只包含头部白名单 参数非必填 反之必填。
 *
 * @param {Array} paramData - 包含参数数据的数组，可能包含请求头信息等。
 * @param {string} type - 表示参数的类型，用于判断是否为 'headers' 类型。
 * @returns {string} - 如果满足条件返回 '?'，否则返回空字符串。
 *
 * 函数内部逻辑如下：
 * 1. 定义了一个内部函数 checkArrayElements，用于检查 arr2 中的元素是否都包含在 arr1 中。
 * 2. 首先检查 type 是否等于 'headers' 且 paramData 的长度是否大于 0。
 * 3. 若满足条件，将 paramData 中的元素名称转换为小写并存储在 arr 数组中。
 * 4. 调用 checkArrayElements 函数检查 whileArr 中的元素是否都包含在 arr 中。
 * 5. 若都包含，返回 '?'，否则返回空字符串。
 * 6. 若不满足初始条件，直接返回空字符串。
 */
function getRequiredState(paramData, type) {
  // 定义内部函数 checkArrayElements，用于检查 arr2 中的元素是否都包含在 arr1 中
  function checkArrayElements(arr1, arr2) {
    return arr2.every((item) => arr1.includes(item))
  }
  if (type === 'headers' && paramData.length > 0) {
    // 将 paramData 中的元素名称转换为小写并存储在 arr 数组中
    const arr = paramData.map((item) => getToLocaleLowerCase(item.name))
    // 检查 whileArr 中的元素是否都包含在 arr 中
    if (checkArrayElements(whileArr, arr)) {
      return '?'
    } else {
      return ''
    }
  }
  return ''
}

/**
 * 根据请求头数据生成请求头参数的字符串
 * @param {Array} headerData - 请求头数据数组
 * @returns {string} - 请求头参数字符串
 * @description
 * 这个函数用于根据请求头数据生成请求头参数的字符串。如果传入的 headerData 数组长度大于 0，则返回一个包含请求头参数的字符串，否则返回空字符串。
 */
export function getHeader(headerData) {
  try {
    if (headerData && headerData.length > 0) {
      return `, ${getParam(headerData, 'headers')}`
    }
    return ''
  } catch (err) {
    console.log('获取请求头参数失败：' + err)
  }
}

/**
 * 获取函数注释
 * @param {object} val - 函数的值
 * @param {object} paramData - 参数数据
 * @returns {string} - 函数的JSDoc注释
 */
function getFuncAnnotation(val, paramData) {
  const headers = val?.headers ? val.headers : []
  const annotationData = [...paramData, ...headers]
  return `/**
  * ${val.description} - ${val.interfaceNameCn}
  ${getAnnotation(annotationData)}
  * @returns {${getReturn(val.return)}} - 返回一个解析为响应数据的Promise
*/`
}
