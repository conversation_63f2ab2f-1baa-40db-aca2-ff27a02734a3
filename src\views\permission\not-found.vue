<template>
  <div class="content">
    <div class="img">
      <img src="../../assets/404.png" alt="" />
    </div>
    <!-- <p>您正在寻找的页面不存在</p> -->
    <div class="btn" @click="goHome"><a-button> 返回首页</a-button></div>
  </div>
</template>

<script lang="ts" setup>
const router = useRouter()
import { useRouter } from 'vue-router'
function goHome() {
  router.push('/')
}
</script>

<style scoped lang="less">
/* Styles for 404 page */
.content {
  position: relative;
  width: 100%;

  .img {
    width: 900px;
    margin: 0 auto;

    img {
      width: 100%;
      height: 100%;
    }
  }

  p {
    width: 100%;
    text-align: center;
    font-size: 30px;
    position: absolute;
    bottom: 160px;
  }

  .btn {
    position: absolute;
    bottom: 50px;
    z-index: 1;
    right: 0;
    left: 0;
    margin: 0 auto;
    text-align: center;
  }
}
</style>
