<template>
  <div class="echarts-box">
    <div class="left-box">
      <a-tabs v-model:activeKey="activeKey" type="card" @change="handleTabChange">
        <a-tab-pane key="1" tab="代码编辑">
          <v-ace-editor
            v-model:value="codeOptions"
            lang="javascript"
            theme="chrome"
            :style="{ height: props.height }"
            :options="{ useWorker: true }"
            @input="debouncedHandleInput"
          />
        </a-tab-pane>
        <a-tab-pane key="2" tab="完整代码">
          <v-ace-editor
            v-model:value="integrityCode"
            lang="javascript"
            theme="chrome"
            :style="{ height: props.height }"
            :readonly="true"
          />
        </a-tab-pane>
      </a-tabs>
    </div>
    <div class="right-box">
      <Echarts
        :getOptions="getOptions"
        :items="itemsArr"
        title="统计图"
        ref="echartsRef"
        @render="renderHandle"
      ></Echarts>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick } from 'vue'
import { debounce } from '@/utils'
//@ts-ignore
import { Echarts } from '@fs/fs-components'
import { VAceEditor } from 'vue3-ace-editor'
import 'ace-builds/src-noconflict/mode-javascript' // Load the language definition file used below
import 'ace-builds/src-noconflict/theme-chrome' // Load the theme definition file used below

import ace from 'ace-builds'
import workerJavascript from 'ace-builds/src-noconflict/worker-javascript?url' // For vite
ace.config.setModuleUrl('ace/mode/javascript_worker', workerJavascript)

interface EchartsProps {
  width: string | number
  height: string | number
}

const props = withDefaults(defineProps<EchartsProps>(), {
  width: '60%',
  height: 'calc(100vh - 220px)',
})

const echartsRef = ref<any>(null)

const customer = `

const items = [
  {
    type: 'radio-group',
    name: 'type',
    defaultValue: 'all',
    comProps: {
      options: [
        { label: '全部', value: 'all' },
        { label: '今日', value: 'today' },
        { label: '本周', value: 'week' },
        { label: '本月', value: 'month' },
        { label: '本年', value: 'year' },
      ],
      optionType: 'button',
    },
  },
];

function getOptions() {
  function generateRandomArray(length, min, max) {
      const arr = []
      for (let i = 0; i < length; i++) {
        // 在 min 和 max 之间生成一个随机数，并将其推入数组
        const randomNum = Math.floor(Math.random() * (max - min + 1)) + min
        arr.push(randomNum)
      }
      return arr
    }
    const randomArray = generateRandomArray(7, 10, 200)
    return new Promise((resolve) => {
          setTimeout(() => {
              resolve({
          code: '000000',
          data: {
            xAxis: {
              type: 'category',
              data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
            },
            grid: {
              left: '0',
              right: '0',
              top: '10px',
              bottom: '0',
              containLabel: true,
            },
            yAxis: {
              type: 'value',
            },
            series: [
              {
                data: randomArray,
                type: 'bar',
              },
            ],
          },
        })
          }, 1000)
    })
}

({ items, getOptions })
`

const getOptions = ref(() => {})

const itemsArr = ref([
  {
    type: 'radio-group',
    name: 'type',
    defaultValue: 'all',
    comProps: {
      options: [
        { label: '全部', value: 'all' },
        { label: '今日', value: 'today' },
        { label: '本周', value: 'week' },
        { label: '本月', value: 'month' },
        { label: '本年', value: 'year' },
      ],
      optionType: 'button',
    },
  },
])

const activeKey = ref('1')
const codeOptions = ref(customer)
const integrityCode = ref('')

const renderHandle = (data: any) => {
  console.log(
    '%c 🍞 我是请求数据data: ',
    'font-size:12px;background-color: #3F7CFF;color:#fff;',
    data,
  )
}

const inputHandle = () => {
  try {
    const data = eval(codeOptions.value as string)
    if (data?.items) itemsArr.value = data?.items
    if (data?.getOptions) getOptions.value = data?.getOptions
    nextTick(() => {
      echartsRef.value.updateChart()
    })
  } catch (err: any) {
    console.error(err)
  }
}

const debouncedHandleInput = debounce(inputHandle, 300)

const handleTabChange = (val: string) => {
  if (val === '2') {
    // eslint-disable-next-line no-useless-escape
    integrityCode.value =
      `<template>
      <Echarts :getOptions="getOptions" :items="items" title="统计图"></Echarts>
</template>
  
` +
      '<' +
      `script setup>
import { ref, onMounted } from 'vue'
import { Echarts } from '@fs/fs-components'

const items = ${JSON.stringify(itemsArr.value, null, 2)}

${getOptions.value}

</` +
      `script>`
  }
}
</script>
<style scoped lang="less">
.echarts-box {
  display: flex;
  .left-box {
    width: 40%;
    margin-right: 20px;
  }
  .right-box {
    width: 60%;
  }
}
</style>
