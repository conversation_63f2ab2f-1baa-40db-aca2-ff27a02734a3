<template>
  <div class="custom-date-picker">
    <!-- 顶部 Tab -->
    <a-tabs v-model:activeKey="activeTab" class="tab-container">
      <a-tab-pane :key="ConditionEnum.between" tab="在...之间" />
      <a-tab-pane :key="ConditionEnum.before" tab="之前" />
      <a-tab-pane :key="ConditionEnum.current" tab="当前" />
      <a-tab-pane :key="ConditionEnum.after" tab="之后" />
    </a-tabs>

    <!-- 日期选择器 -->
    <div class="picker-container">
      <a-range-picker v-show="activeTab === ConditionEnum.between" v-model:value="dateRange" />
      <a-date-picker
        v-show="activeTab !== ConditionEnum.between"
        v-model:value="singleDate"
        style="width: 100%"
      />
    </div>
    <a-divider
      type="horizontal"
      orientation="left,right,center"
      :style="{ color: 'black', fontWeight: 'bold', margin: '36px 0 12px 0' }"
    >
    </a-divider>
    <!-- 确认按钮 -->
    <div style="text-align: right">
      <a-button type="primary" @click="handleConfirm">确定</a-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref, watch } from 'vue'
import { ConditionEnum } from './constant'
import type { Dayjs } from 'dayjs'
const props = defineProps<{
  value?: Dayjs[] | Dayjs | null
  onConfirm: (value?: Dayjs[] | Dayjs, condition?: ConditionEnum) => void
}>()

// 默认激活的 Tab
const activeTab = ref<ConditionEnum>(ConditionEnum.between)

// 日期范围
const dateRange = ref<[Dayjs, Dayjs]>([] as unknown as [Dayjs, Dayjs])

// 单个日期
const singleDate = ref<Dayjs>()

const handleConfirm = () => {
  // 设置 data.value
  if (activeTab.value === ConditionEnum.between) {
    props.onConfirm(dateRange.value, activeTab.value)
  } else {
    props.onConfirm(singleDate.value, activeTab.value)
  }
}

// 监听 Tab 切换，清空日期
watch(activeTab, () => {
  dateRange.value = [] as unknown as [Dayjs, Dayjs]
  singleDate.value = void 0
})

onMounted(() => {
  if (Array.isArray(props.value)) {
    dateRange.value = props.value as [Dayjs, Dayjs]
  } else if (props.value) {
    singleDate.value = props.value
  }
})
</script>

<style scoped>
.custom-date-picker {
  width: 100%;
  max-width: 400px;
}

.tab-container {
  margin-bottom: 16px;
}

.picker-container {
  width: 302px;
  display: flex;
  justify-content: center;
}
</style>
