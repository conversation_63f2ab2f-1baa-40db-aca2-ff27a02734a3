<template>
  <div class="page-full">
    <a-flex justify="space-between" style="padding-bottom: 10px">
      <div class="title mb-20px" text="20px #000000d9">实时表监控</div>
      <a-space :size="8">
        <a-button type="primary" @click="showMore = !showMore">
          <template #icon v-if="showMore"><UpOutlined /></template>
          <template #icon v-else><DownOutlined /></template>
          {{ showMore ? '收起' : '搜索' }}
        </a-button>
        <a-button type="primary" @click="batchCreateTask">批量校验</a-button>
        <a-dropdown trigger="click" v-model:open="dropdownVisible">
          <template #overlay>
            <a-menu>
              <a-menu-item v-for="(item, index) in columns" :key="index">
                <a-checkbox
                  :checked="item.show"
                  @change="(e:any)=>{columnsCheck(e.target.checked,index)}"
                  >{{ item.title }}</a-checkbox
                >
              </a-menu-item>
            </a-menu>
          </template>
          <a-button style="margin-left: 8px" type="primary">筛选列<DownOutlined /> </a-button>
        </a-dropdown>
      </a-space>
    </a-flex>
    <div class="page-main" ref="pageMain">
      <div ref="pageTop">
        <div v-show="showMore">
          <a-form class="query-container" ref="queryForm" style="margin-top: 0px">
            <a-row :gutter="32">
              <a-col :span="6">
                <!-- 作业流 -->

                <a-form-item label="作业流名称">
                  <a-input
                    class="mb-16"
                    v-model:value="param.name"
                    allowClear
                    placeholder="请输入作业流名称"
                    style="width: -webkit-fill-available"
                    @pressEnter="handleQuery"
                  />
                </a-form-item>
                <!-- 目标表类型 -->
                <a-form-item label="目标库类型">
                  <a-select
                    v-model:value="param.dbDesType"
                    showSearch
                    allowClear
                    class="mb-16"
                    style="width: 100%"
                    :filter-option="selectInput"
                    placeholder="请选择目标数据库类型"
                    @change="(val:any) => handleChangeDbSrcType(val, 'des')"
                  >
                    <a-select-option
                      v-for="(option, optKey) in dbTypeList"
                      :key="optKey"
                      :value="option.label"
                      :label="option.label"
                      >{{ option.label }}</a-select-option
                    >
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <!-- 源库类型 -->
                <a-form-item label="源库类型">
                  <a-select
                    class="mb-16"
                    style="width: 100%"
                    v-model:value="param.dbSrcType"
                    showSearch
                    :allowClear="true"
                    :filter-option="selectInput"
                    placeholder="请选择源库类型"
                    @change="(val:any) => handleChangeDbSrcType(val, 'src')"
                  >
                    <a-select-option
                      v-for="(option, optKey) in dbTypeList"
                      :key="optKey"
                      :value="option.label"
                      :label="option.label"
                      >{{ option.label }}</a-select-option
                    >
                  </a-select>
                </a-form-item>
                <!-- 目标库名称 -->
                <a-form-item label="目标库名称" :autoLink="false">
                  <a-input
                    class="mb-16"
                    style="flex: 1"
                    @blur="handleChangeDbNameResetVal('des')"
                    placeholder="请输入目标库名称"
                    v-model:value="param.dbDesName"
                    @pressEnter="handleQuery"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <!-- 源库 -->
                <a-form-item label="源库名称">
                  <a-input
                    class="mb-16"
                    placeholder="请输入源库名称"
                    style="width: -webkit-fill-available"
                    v-model:value="param.dbSrcName"
                    @pressEnter="handleQuery"
                  />
                </a-form-item>
                <!-- 目标表 -->
                <a-form-item label="目标表名称" :autoLink="false">
                  <a-input
                    class="mb-16"
                    style="flex: 1"
                    placeholder="请输入目标表名称"
                    v-model:value="param.dbDesTable"
                    @pressEnter="handleQuery"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <!-- 源表 -->
                <a-form-item label="源表名称" :autoLink="false">
                  <a-input
                    class="mb-16"
                    style="flex: 1"
                    placeholder="请输入源表名称"
                    v-model:value="param.dbSrcTable"
                    @pressEnter="handleQuery"
                  />
                </a-form-item>
                <!-- 状态 -->
                <a-form-item label="状态">
                  <a-select
                    class="mb-16"
                    v-model:value="param.state"
                    showSearch
                    :allowClear="true"
                    :filter-option="selectInput"
                    style="width: 100%"
                    @change="handleQuery"
                    placeholder="请选择状态"
                  >
                    <a-select-option
                      v-for="(option, optKey) in stateList"
                      :key="optKey"
                      :value="option.value"
                      :label="option.label"
                      >{{ option.label }}</a-select-option
                    >
                  </a-select>
                </a-form-item>
                <a-space :size="8" style="float: right">
                  <a-button @click="handleQuery" type="primary">查询</a-button>
                  <a-button @click="handleReset">重置</a-button>
                </a-space>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </div>
      <div class="list-content">
        <a-spin :spinning="spinning">
          <a-card class="table-card" :bordered="false">
            <a-table
              ref="table"
              :columns="columns.filter((col:any)=>col.show)"
              :data-source="ruleList"
              :scroll="{ x: 1300, y: 'calc(100vh - 450px)' }"
              :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
              row-key="id"
              :pagination="false"
            >
              <template #bodyCell="{ text, column, record }">
                <template v-if="column.dataIndex === 'isMain'">
                  <div v-if="record.isMain === 0">从表</div>
                  <div v-else-if="record.isMain === 1">主表</div>
                </template>
                <template v-if="column.dataIndex === 'isMonitor'">
                  <div v-if="record.isMonitor === 0">不监控</div>
                  <div v-else-if="record.isMonitor === 1">监控</div>
                  <div v-else>-</div>
                </template>
                <template v-if="column.dataIndex === 'cdcTime'">
                  <div v-if="record.cdcTime">{{ record.cdcTime }}</div>
                  <div v-else>-</div>
                </template>
                <template v-if="column.dataIndex === 'processTime'">
                  <div v-if="record.processTime">{{ record.processTime }}</div>
                  <div v-else>-</div>
                </template>
                <template v-if="column.dataIndex === 'duration'">
                  <div v-if="record.duration">{{ record.duration }}</div>
                  <div v-else>-</div>
                </template>
                <template v-if="column.dataIndex === 'latestTime'">
                  <div v-if="record.latestTime">{{ record.latestTime }}</div>
                  <div v-else>-</div>
                </template>
                <template v-if="column.dataIndex === 'srcCount'">
                  <div v-if="record.srcCount">{{ record.srcCount }}</div>
                  <div v-else>-</div>
                </template>
                <template v-if="column.dataIndex === 'desCount'">
                  <div v-if="record.desCount">{{ record.desCount }}</div>
                  <div v-else>-</div>
                </template>
                <template v-if="column.dataIndex === 'dbSrcDesc'">
                  <a
                    @click="logModal(splitStr(record, 'src'), '查看详情', true)"
                    :title="splitStr(record, 'src')"
                    >{{ splitStr(record, 'src') || '-' }}</a
                  >
                </template>
                <template v-if="column.dataIndex === 'dbDesDesc'">
                  <a
                    @click="logModal(splitStr(record, 'des'), '查看详情', true)"
                    :title="splitStr(record, 'des')"
                    >{{ splitStr(record, 'des') || '-' }}</a
                  >
                </template>
                <template v-if="column.dataIndex === 'log'">
                  <a @click="logModal(text, '查看详情', true)"> {{ text || '-' }}</a>
                </template>
                <template v-if="column.dataIndex === 'state'">
                  <div v-if="record.state === '0'">
                    <img
                      class="img-icon"
                      :src="statusImg(record.state)"
                      style="width: 25px"
                      alt=""
                    />执行中
                  </div>
                  <div v-else-if="record.state === '1'">
                    <img
                      class="img-icon"
                      :src="statusImg(record.state)"
                      style="width: 25px"
                      alt=""
                    />成功
                  </div>
                  <div v-else-if="record.state === '2'">
                    <img
                      class="img-icon"
                      :src="statusImg(record.state)"
                      style="width: 25px"
                      alt=""
                    />失败
                  </div>
                  <div v-else-if="record.state === '3'">
                    <img
                      class="img-icon"
                      :src="statusImg(record.state)"
                      style="width: 25px"
                      alt=""
                    />待执行
                  </div>
                  <div v-else>
                    <img
                      class="img-icon"
                      :src="statusImg('')"
                      style="width: 20px; margin-right: 5px"
                      alt=""
                    />未校验
                  </div>
                </template>
                <template
                  v-if="
                    column.dataIndex === 'name' ||
                    column.dataIndex === 'dbSrcTable' ||
                    column.dataIndex === 'dbDesDbTable'
                  "
                >
                  <a @click="logModal(text, '查看详情', true)"> {{ text || '-' }}</a>
                </template>
                <template v-if="column.dataIndex === 'action'">
                  <a-space>
                    <a-button size="small" type="link" @click="creatTask([record.id])"
                      >校验</a-button
                    >
                    <a-button size="small" type="link" @click="getResult(record)"
                      >查看结果</a-button
                    >
                  </a-space>
                </template>
              </template>
            </a-table>
            <!-- 分页卡片-->
            <div class="card-pagination">
              <a-pagination
                size="small"
                show-quick-jumper
                show-size-changer
                @change="handelPageChange"
                v-model="pagination.pageIndex"
                :total="pagination.total"
                @showSizeChange="handelShowSizeChange"
                :defaultPageSize="pagination.pageSize"
                :pageSizeOptions="pagination.pageSizeOptions"
                :showTotal="(total: number, range: number[])  => `显示 ${range[0]} 到 ${range[1]} 条 , 共 ${total} 条记录，已选中 ${selectedRowKeys.length} 条数据`"
              />
            </div>
          </a-card>
        </a-spin>
      </div>
    </div>
  </div>
</template>

<script lang="tsx" setup>
import {
  getRealTimeQueryList,
  ceateRealTimeTask,
} from '@/api/services/monitor-maintenance/data-quality'
import { getDataSourceList } from '@/api/services/monitor-maintenance/flow-definition'
import {
  getDatabaseByIdFun,
  getDbTypeList,
  selectDbnameByIdFun,
  selectSchemaByIdFun,
} from '@/api/services/monitor-maintenance/etl-list'
import { logModal } from '@/utils/log-modal'
import uncheck from '@/assets/uncheck.svg'
import running from '@/assets/running.svg'
import success from '@/assets/success.svg'
import waiting from '@/assets/waiting.svg'
import failure from '@/assets/failure.svg'
import { UpOutlined, DownOutlined } from '@ant-design/icons-vue'
import { useUserStore } from '@/stores/user'
import { ref, reactive, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { message } from 'ant-design-vue'

const route = useRoute()
const userStore = useUserStore()
const router = useRouter()
const showMore = ref(true) // 是否展开页面全部查询信息
const ownerId = ref<string>(userStore.userInfo.loginAccount)
const dbTypeList = getDbTypeList().filter((i) => ![18].includes(i.value)) // 数据库类型列表
let param = reactive<any>({
  name: '',
  dbSrcType: undefined,
  dbSrcId: undefined,
  dbSrcName: '',
  dbSrcTable: '',
  dbDesType: undefined,
  dbDesId: undefined,
  dbDesName: '',
  dbDesTable: '',
  // isMain: ''
})
const spinning = ref(false)
const dropdownVisible = ref(false)
const ruleList = ref<any[]>([])
let pagination = reactive<any>({
  total: 0,
  size: 'small',
  pageIndex: 1,
  pageSize: 24, // 每页中显示多少条数据
  showSizeChanger: true,
  pageSizeOptions: ['12', '24', '36', '48'], // 每页中显示的数据
  showQuickJumper: true, // 是否可以快速跳转至某页
})
const columns = ref<any[]>([
  {
    title: '作业流',
    dataIndex: 'name',
    width: 120,
    show: true,
    scopedSlots: { customRender: 'name' },
  },
  {
    title: '校验状态',
    dataIndex: 'state',
    width: 120,
    show: true,
    scopedSlots: { customRender: 'state' },
  },
  {
    title: '源COUNT',
    dataIndex: 'srcCount',
    width: 100,
    show: true,
    scopedSlots: { customRender: 'srcCount' },
  },
  {
    title: '目标COUNT',
    dataIndex: 'desCount',
    width: 100,
    show: true,
    scopedSlots: { customRender: 'desCount' },
  },
  {
    title: 'CDC时间',
    dataIndex: 'cdcTime',
    width: 180,
    show: true,
    scopedSlots: { customRender: 'cdcTime' },
  },
  {
    title: '处理时间',
    dataIndex: 'processTime',
    width: 180,
    show: true,
    scopedSlots: { customRender: 'processTime' },
  },
  {
    title: '上一次执行时间',
    dataIndex: 'latestTime',
    width: 180,
    show: true,
    scopedSlots: { customRender: 'latestTime' },
  },
  {
    title: '数据延迟时间',
    dataIndex: 'duration',
    width: 120,
    show: true,
    scopedSlots: { customRender: 'duration' },
  },
  {
    title: '校验结果描述',
    dataIndex: 'log',
    width: 180,
    show: true,
    scopedSlots: { customRender: 'log' },
  },
  {
    title: '源数据源',
    dataIndex: 'dbSrcDesc',
    width: 160,
    show: true,
    scopedSlots: { customRender: 'dbSrcDesc' },
  },
  {
    title: '源库',
    dataIndex: 'dbSrcDbName',
    width: 160,
    show: true,
    scopedSlots: { customRender: 'dbSrcDbName' },
  },
  {
    title: '源模式名',
    dataIndex: 'dbSrcSchema',
    width: 160,
    show: true,
    scopedSlots: { customRender: 'dbSrcSchema' },
  },
  {
    title: '源表',
    dataIndex: 'dbSrcTable',
    width: 160,
    show: true,
    scopedSlots: { customRender: 'dbSrcTable' },
  },
  {
    title: '目标数据源',
    dataIndex: 'dbDesDesc',
    width: 160,
    show: true,
    scopedSlots: { customRender: 'dbDesDesc' },
  },
  {
    title: '目标库名',
    dataIndex: 'dbDesDbName',
    width: 160,
    show: true,
    scopedSlots: { customRender: 'dbDesDbName' },
  },
  {
    title: '目标模式名',
    dataIndex: 'dbDesSchema',
    width: 160,
    show: true,
    scopedSlots: { customRender: 'dbDesSchema' },
  },
  {
    title: '目标表名',
    dataIndex: 'dbDesDbTable',
    width: 160,
    show: true,
    scopedSlots: { customRender: 'dbDesDbTable' },
  },
  {
    title: '是否为主表',
    dataIndex: 'isMain',
    width: 120,
    show: true,
    scopedSlots: { customRender: 'isMain' },
  },
  {
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
    show: true,
    width: 160,
    scopedSlots: { customRender: 'action' },
  },
])

const libraryList = ref<any[]>([])
//  源
const dbSrcList = ref<any[]>([])
const dbSrcNameList = ref<any[]>([])
const dbSrcSchemaList = ref<any[]>([])
const srcTableNameList = ref<any[]>([])
//  目标
const dbDesList = ref<any[]>([])
const dbDesNameList = ref<any[]>([])
const dbDesSchemaList = ref<any[]>([])
const desTableNameList = ref<any[]>([])
const selectedRowKeys = ref<any[]>([])
const queryFlowId = ref<any>()

const stateList = ref<any[]>([
  {
    label: '执行中',
    value: '0',
  },
  {
    label: '成功',
    value: '1',
  },
  {
    label: '失败',
    value: '2',
  },
  {
    label: '待执行',
    value: '3',
  },
  {
    label: '未校验',
    value: '-2',
  },
])

onMounted(() => {
  getDataSourceListFn()
  if (route.query.id) {
    queryFlowId.value = Number(route.query.id)
  }
  getList()
})

const splitStr = (record: any, type: any) => {
  if (type === 'src') {
    return `${record.dbSrcType}${'库-'}${record.dbSrcDesc}`
  } else {
    return `${record.dbDesType}${'库-'}${record.dbDesDesc}`
  }
}

// 筛选列
const columnsCheck = (checked: boolean, index: number) => {
  columns.value[index].show = checked
}

//  查询结果
const getResult = (record: any) => {
  logModal(JSON.parse(record.result), '查看结果', true, 600, null, { type: 'json' })
}

// 复选框选择变化
const onSelectChange = (selectedRowKeysT: Array<any>) => {
  selectedRowKeys.value = selectedRowKeysT
}

//  批量生成任务
const batchCreateTask = () => {
  if (selectedRowKeys.value.length) {
    creatTask(selectedRowKeys.value)
  } else {
    message.error('请勾选需要批量校验的任务')
  }
  // creatTask( selectedRowKeys.value)
}

// 筛选下拉框
const selectInput = (inputValue: string, option: any) => {
  return option.label.toLowerCase().indexOf(inputValue.toLowerCase()) >= 0
}

//  获取数据源列表
const getDataSourceListFn = () => {
  getDataSourceList({ ownerId: ownerId.value }).then((res) => {
    if (res.data.length) {
      let dataSort =
        res.data && res.data.sort((a: any, b: any) => a.dbDesc.charCodeAt() - b.dbDesc.charCodeAt())
      libraryList.value = dataSort
    }
  })
}

//  切换数据库类型
const handleChangeDbSrcType = (val: any, type: string) => {
  if (type === 'src') {
    dbSrcList.value = libraryList.value.filter((i: any) => i.dbType === val)
    param.dbSrcId = undefined
    param.dbSrcName = ''
    // param.dbSrcSchema = ''
    param.dbSrcTable = ''
    dbSrcNameList.value = []
    dbSrcSchemaList.value = []
    srcTableNameList.value = []
  } else {
    dbSrcList.value = libraryList.value.filter((i: any) => i.dbType === val)
    param.dbDesId = undefined
    param.dbDesName = ''
    param.dbDesSchema = ''
    param.dbDesTable = ''
    dbDesNameList.value = []
    dbDesSchemaList.value = []
    desTableNameList.value = []
  }
  handleQuery()
}

//  数据库名称切换
const handleChangeDbSrcId = (type: string) => {
  if (type === 'src') {
    param.dbSrcName = ''
    // param.dbSrcSchema = ''
    param.dbSrcTable = ''
    dbSrcNameList.value = []
    dbSrcSchemaList.value = []
    srcTableNameList.value = []
  } else {
    param.dbDesName = ''
    param.dbDesSchema = ''
    param.dbDesTable = ''
    dbDesNameList.value = []
    dbDesSchemaList.value = []
    desTableNameList.value = []
  }
}
const handleChangeDbNameResetVal = (type: string) => {
  if (type === 'src') {
    // param.dbSrcSchema = ''
    param.dbSrcTable = ''
    dbSrcSchemaList.value = []
    srcTableNameList.value = []
  } else {
    // param.dbDesSchema = ''
    param.dbDesTable = ''
    dbDesSchemaList.value = []
    desTableNameList.value = []
  }
}

//  数据库模式切换
const handleChangeDbSchemaResetVal = (type: string) => {
  if (type === 'src') {
    param.dbSrcTable = ''
    srcTableNameList.value = []
  } else {
    param.dbDesTable = ''
    desTableNameList.value = []
  }
}

//  获取数据库名称列表
const handleSelectDBName = (type: string) => {
  if (type === 'src') {
    if (param.dbSrcId) {
      const params = { id: param.dbSrcId }
      getDatabaseByIdFun(params).then((res) => {
        if (res.data.code !== 1) message.error(res.data.message)
        dbSrcNameList.value = res.data.obj && res.data.obj.sort()
      })
    }
  } else {
    if (param.dbDesId) {
      const params = { id: param.dbDesId }
      getDatabaseByIdFun(params).then((res) => {
        if (res.data.code !== 1) message.error(res.data.message)
        dbDesNameList.value = res.data.obj && res.data.obj.sort()
      })
    }
  }
}

//  获取数据库模式
const getdbSchemaList = (type: any) => {
  if (type === 'src') {
    const dbSrcId = param.dbSrcId
    const dbName = param.dbSrcName
    if (dbSrcId && dbName) {
      let params = { id: dbSrcId, dbName: dbName }
      selectSchemaByIdFun(params).then((res) => {
        if (res.data.code !== 1) message.error(res.data.message)
        const dataSort = res.data.obj && res.data.obj.sort()
        dbSrcSchemaList.value = dataSort
      })
    }
  } else {
    const dbId = param.dbDesId
    const dbName = param.dbDesName
    if (dbId && dbName) {
      let params = { id: dbId, dbName: dbName }
      selectSchemaByIdFun(params).then((res) => {
        if (res.data.code !== 1) message.error(res.data.message)
        const dataSort = res.data.obj && res.data.obj.sort()
        dbDesSchemaList.value = dataSort
      })
    }
  }
}

//  选择库名称加载表名列表
const getTableNameList = (type: any) => {
  let dbId = ''
  let dbName = ''
  let dbSchema = ''
  let dbType = -1
  if (type === 'src') {
    dbId = param.dbSrcId
    dbName = param.dbSrcName
    dbSchema = param.dbSrcSchema
    dbType = param.dbSrcType
  } else {
    dbId = param.dbDesId
    dbName = param.dbDesName
    dbSchema = param.dbDesSchema
    dbType = param.dbDesType
  }
  if (dbId && dbName) {
    let params = { id: dbId, dbName, dbSchema }
    if ([5, 6, 12, 16, 29].includes(dbType)) {
      if (!dbSchema) return message.warning('请选择数据库模式')
    }
    selectDbnameByIdFun(params).then((res) => {
      if (res.data.code !== 1) message.error(res.data.message)
      const dataSort = res.data.obj && res.data.obj.sort()
      if (type === 'src') {
        srcTableNameList.value = dataSort
      } else {
        desTableNameList.value = dataSort
      }
    })
  }
}

/* 获取列表数据 */
const getList = () => {
  let params = {
    // name: this.param.name || null,
    flowId: queryFlowId.value,
    ...param,
    pageIndex: pagination.pageIndex,
    pageSize: pagination.pageSize,
  }
  spinning.value = true
  getRealTimeQueryList(params).then((res) => {
    let data = res.data.records
    pagination.total = res.data.totalRecords
    ruleList.value = data
    spinning.value = false
  })
}

// 切换分页
const handelPageChange = (pageIndex: number, pageSize: number) => {
  pagination.pageIndex = pageIndex
  pagination.pageSize = pageSize
  getList()
}

// 切换分页大小
const handelShowSizeChange = (pageIndex: number, pageSize: number) => {
  pagination.pageIndex = pageIndex
  pagination.pageSize = pageSize
  getList()
}

// 重置
const handleReset = () => {
  param = reactive<any>({
    name: '',
    dbSrcType: undefined,
    dbSrcId: undefined,
    dbSrcName: '',
    dbSrcTable: '',
    dbDesType: undefined,
    dbDesId: undefined,
    dbDesName: '',
    dbDesTable: '',
    // isMain: '',
    pageIndex: 1,
    pageSize: param.pageSize,
  })
  pagination.pageIndex = 1
  pagination.pageSize = 24

  queryFlowId.value = null
  router.push({
    path: '/dataCheck/realTimeTableQuery',
  })
  getList()
}

// 查询
const handleQuery = () => {
  pagination.pageIndex = 1
  getList()
}

//  生成任务
const creatTask = (ids: any) => {
  ceateRealTimeTask({
    list: ids,
  }).then((res) => {
    if (res.data.code === 1) {
      message.success(res.msg)
    }
    getList()
  })
}

// 状态图标
const statusImg = (val: string | number) => {
  let result = uncheck
  switch (val) {
    case '0':
      result = running
      break
    case '1':
      result = success
      break
    case '2':
      result = failure
      break
    case '3':
      result = waiting
      break
  }
  return result
}
</script>

<style scoped lang="less">
:deep(.ant-table-wrapper) {
  margin: 0 !important;
}

:deep(.ant-table-row-level-1) {
  background-color: #f6ffed;
}

:deep(.ant-table-row-level-2) {
  background-color: #fff1f0;
}

:deep(.ant-table-row-level-3) {
  background-color: #fffbe6;
}

:deep(.ant-form-item) {
  margin-bottom: 0px;
}
.query-container :deep(.ant-row .ant-form-item .ant-form-item-label) {
  display: none;
}
.img-icon {
  position: relative;
  top: 5px;
}
.mb-16 {
  margin-bottom: 16px;
  flex: 1;
}
</style>
