<template>
  <a-modal
    width="900px"
    v-model:visible="open"
    :title="title"
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <Table
      class="history-table"
      :columns="columns"
      :getData="getIndexHistoryDataListByPages"
      ref="tableRef"
      :searchFormState="form"
    >
    </Table>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from 'vue'
import { Table } from '@fs/fs-components'
import { getIndexHistoryDataListByPages } from '@/api/indexmanage/indexmanage/indexmanage'

// 使用 defineModel 控制弹窗显示/隐藏
const open = defineModel<boolean>({ default: false, required: true })

// 定义 props 并设置默认值
const props = withDefaults(
  defineProps<{
    title?: string // 弹窗标题
    formId: string //编号
  }>(),
  {
    title: '历史记录',
  },
)

watch(
  () => open.value,
  async (newVal) => {
    if (newVal) {
      form.value.basicDataId = props.formId
      tableRef.value?.getTableData()
    }
  },
)

const getIndexFormTemplateHistoryDataListByPage = async (params: any) => {
  return []
}

const form: Record<string, any> = ref({
  basicDataId: '',
  formName: '',
  isDeleted: '',
})
const tableRef = ref<InstanceType<typeof Table> | null>(null)

const columns = [
  {
    title: '编号',
    dataIndex: 'basicDataId',
    key: 'basicDataId',
  },
  {
    title: '指标名称',
    dataIndex: 'indexName',
    key: 'indexName',
  },
  {
    title: '是否启用',
    dataIndex: 'isDeleted',
    key: 'isDeleted',
    customRender: (...arg: [any]): string => {
      const { record } = arg[0]
      const str = record.isDeleted ? '是' : '否'
      return str
    },
  },
  {
    title: '是否已发布',
    dataIndex: 'isRelease',
    key: 'isRelease',
    customRender: (...arg: [any]): string => {
      const { record } = arg[0]
      const str = record.isRelease ? '是' : '否'
      return str
    },
  },
  {
    title: '是否删除',
    dataIndex: 'isDeleted',
    key: 'isDeleted',
    customRender: (...arg: [any]): string => {
      const { record } = arg[0]
      const str = record.isDeleted ? '否' : '是'
      return str
    },
  },
  {
    title: '备注',
    dataIndex: 'remarks',
    key: 'remarks',
  },
  {
    title: '版本号',
    dataIndex: 'version',
    key: 'version',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
  },
]

// 定义 emits
const emit = defineEmits<{
  (event: 'ok'): void // 点击确定按钮
  (event: 'cancel'): void // 点击取消按钮
}>()

// 点击确定按钮
const handleOk = () => {
  emit('ok')
  closeModal()
}

// 点击取消按钮
const handleCancel = () => {
  emit('cancel')
  closeModal()
}

// 关闭弹窗
const closeModal = () => {
  open.value = false
}
</script>
<style lang="less" scoped>
.history-table :deep(.filter-box) {
  display: none;
}
</style>
