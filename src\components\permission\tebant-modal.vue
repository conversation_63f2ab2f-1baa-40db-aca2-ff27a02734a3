<template>
  <a-modal
    :open="open"
    @ok="modalHandleOk"
    :title="modalType === 'edit' ? '编辑' : '新增'"
    @cancel="cancel"
    width="800px"
    :confirm-loading="ConfirmLoading"
  >
    <a-form
      ref="formRef"
      :model="form"
      :rules="rules"
      :label-col="labelCol"
      :wrapper-col="wrapperCol"
    >
      <a-form-item label="租户名称" name="tenantName">
        <a-input v-model:value="form.tenantName" placeholder="请输入租户名称" />
      </a-form-item>
      <a-form-item label="租户代码" name="tenantCode">
        <a-input
          v-model:value="form.tenantCode"
          :disabled="disabled"
          placeholder="请输入租户代码"
        />
      </a-form-item>
      <a-form-item label="管理员账号" name="manageUserAccount">
        <a-input
          v-model:value="form.manageUserAccount"
          :disabled="disabled"
          :addon-after="addon + (modalType != 'add' ? '' : form.tenantCode)"
          placeholder="请输入管理员账号"
        />
        <span
          class="tip"
          style="display: inline-block; padding-top: 15px; width: 100%; color: rgba(0, 0, 0, 0.45)"
          >登录默认密码在基本配置 > 系统参数配置管理页面查看</span
        >
      </a-form-item>
      <a-form-item label="管理员角色" name="manageRoleId" v-if="modalType != 'add'">
        <a-input
          v-model:value="form.manageRoleId"
          :disabled="disabled"
          placeholder="请输入管理员角色"
        />
      </a-form-item>
      <a-form-item label="是否启用" name="isUse" v-if="modalType != 'add'">
        <a-radio-group v-model:value="form.isUse" placeholder="请选择是否启用">
          <a-radio :value="1">是</a-radio>
          <a-radio :value="0">否</a-radio>
        </a-radio-group>
      </a-form-item>
      <a-form-item label="备注">
        <a-textarea v-model:value="form.remark" placeholder="请输入租户备注信息" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>
<script lang="ts" setup>
import { defineEmits, defineProps, defineExpose, ref } from 'vue'
import type { Rule, FormInstance } from 'ant-design-vue/es/form'
import { addTenant, updateTenant } from '@/api/services/permission'
import { message } from 'ant-design-vue'
interface FormState {
  tenantCode: string
  tenantName: string
  manageUserAccount: string
  isUse: string
  manageRoleId: string
  updateTime: string
  remark: string
  tenantId?: string
}
defineProps({
  open: {
    type: Boolean,
    default: false,
  },
  disabled: Boolean,
})

const emits = defineEmits(['modalHandleOk', 'modalCancel', 'confirmLoadingHandle'])
const formRef = ref<FormInstance>()
const labelCol = { span: 5 }
const wrapperCol = { span: 13 }
const addon = ref<string>('')
const modalType = ref<string>('')
const ConfirmLoading = ref<boolean>(false)
const form = ref<FormState>({
  tenantCode: '',
  tenantName: '',
  manageUserAccount: '',
  manageRoleId: '',
  isUse: '',
  updateTime: '',
  remark: '',
})

const rules: Record<string, Rule[]> = {
  tenantCode: [{ required: true, message: '请输入租户代码', trigger: 'blur' }],
  tenantName: [{ required: true, message: '请输入租户名称', trigger: 'blur' }],
  manageUserAccount: [{ required: true, message: '请输入管理员账号', trigger: 'blur' }],
  manageRoleId: [{ required: true, message: '请选择管理员角色', trigger: 'blur' }],
}

function modalHandleOk() {
  formRef.value &&
    formRef.value
      .validate()
      .then(() => {
        const { tenantCode, tenantName, remark, manageUserAccount, isUse, tenantId } = form.value
        const data: any = {
          tenantCode,
          tenantName,
          remark,
        }
        ConfirmLoading.value = true
        if (modalType.value === 'edit') {
          data['tenantId'] = tenantId
          data['isUse'] = isUse
          updateTenant(data)
            .then(() => {
              emits('modalHandleOk', form.value)
              message.success('编辑成功!')
            })
            .finally(() => {
              ConfirmLoading.value = false
            })
        } else {
          data['manageUserAccount'] = `${manageUserAccount}@${tenantCode}`
          addTenant(data)
            .then(() => {
              emits('modalHandleOk', form.value)
              message.success('新增成功!')
            })
            .finally(() => {
              ConfirmLoading.value = false
            })
        }
      })
      .catch((error: any) => {
        console.log(error)
      })
}

function cancel() {
  emits('modalCancel')
}

function show(type: string, data?: Record<string, any>) {
  resetForm()
  modalType.value = type
  if (type === 'edit') {
    addon.value = ''
    editForm(data)
  } else if (type === 'add') {
    addon.value = '@'
  }
}

const resetForm = () => {
  formRef.value?.resetFields()
  form.value = {
    tenantCode: '',
    tenantName: '',
    manageUserAccount: '',
    manageRoleId: '',
    isUse: '',
    updateTime: '',
    remark: '',
  }
}

function editForm(data?: any) {
  if (data) {
    const copyData = JSON.parse(JSON.stringify(data))
    copyData['isUse'] = copyData?.isUse === '是' ? 1 : 0
    form.value = copyData
  }
}
defineExpose({ show })
</script>
