<template>
  <div class="server-monitor">
    <div class="tool-bar">
      <p>服务器监控</p>
      <a-space :size="8">
        <a-select v-model="serverIp" style="width: 200px" @change="fetchServerMonitor('change')">
          <a-select-option v-for="(item, key) in serverList" :key="key" :value="item.serviceIp">{{
            item.serviceIp
          }}</a-select-option>
        </a-select>
        <a-button type="primary" @click="fetchServerMonitor('reload')">刷新</a-button>
        <a-button type="primary" @click="fetchAllServerMonitor">查看全部</a-button>
      </a-space>
    </div>
    <div class="monitor-list-container">
      <a-row :gutter="24">
        <a-col :lg="24" :md="24" :sm="24" :xl="24" :xs="24" :xxl="12">
          <div class="monitor-item-box">
            <div class="mi-top-box">CPU</div>
            <div class="mi-content-box">
              <a-row class="mi-table-th">
                <a-col :span="12" class="mi-table-td">
                  <img alt="" src="@/assets/monitor/icon_01.png" />
                  <div class="mi-info-box">
                    <p>系统使用率</p>
                    <a-progress
                      :percent="formatPercent(monitorInfo.cpuInfo.cSys)"
                      strokeColor="#117dbb"
                    />
                  </div>
                </a-col>
                <a-col :span="12" class="mi-table-td">
                  <img alt="" src="@/assets/monitor/icon_02.png" />
                  <div class="mi-info-box">
                    <p>当前使用率</p>
                    <a-progress
                      :percent="formatPercent(monitorInfo.cpuInfo.idle)"
                      strokeColor="#117dbb"
                    />
                  </div>
                </a-col>
              </a-row>
              <a-row class="mi-table-th">
                <a-col :span="12" class="mi-table-td">
                  <img alt="" src="@/assets/monitor/icon_03.png" />
                  <div class="mi-info-box">
                    <p>用户使用率</p>
                    <a-progress
                      :percent="formatPercent(monitorInfo.cpuInfo.used)"
                      strokeColor="#117dbb"
                    />
                  </div>
                </a-col>
                <a-col :span="12" class="mi-table-td">
                  <img alt="" src="@/assets/monitor/icon_04.png" />
                  <div class="mi-info-box">
                    <p>当前等待率</p>
                    <a-progress
                      :percent="formatPercent(monitorInfo.cpuInfo.iowait)"
                      strokeColor="#117dbb"
                    />
                  </div>
                </a-col>
              </a-row>
              <a-row class="mi-table-th">
                <a-col :span="24" class="mi-table-td" style="border: none">
                  <img alt="" src="@/assets/monitor/icon_05.png" />
                  <div class="mi-info-box">
                    <p style="margin: 2px 0 0">核数</p>
                    <strong style="font-size: 28px">{{ monitorInfo.cpuInfo.cpuNum }}</strong>
                  </div>
                </a-col>
              </a-row>
            </div>
          </div>
        </a-col>
        <a-col :lg="24" :md="24" :sm="24" :xl="24" :xs="24" :xxl="12">
          <div class="monitor-item-box">
            <div class="mi-top-box">系统信息</div>
            <div class="mi-content-box">
              <a-row class="mi-table-th">
                <a-col :span="12" class="mi-table-td">
                  <img alt="" src="@/assets/monitor/icon_01.png" />
                  <div class="mi-info-box">
                    <p style="margin: 2px 0 0">操作系统名</p>
                    <strong style="font-size: 20px">{{ monitorInfo.sysInfo.osName }}</strong>
                  </div>
                </a-col>
                <a-col :span="12" class="mi-table-td">
                  <img alt="" src="@/assets/monitor/icon_07.png" />
                  <div class="mi-info-box">
                    <p style="margin: 2px 0 0">服务器Ip</p>
                    <strong style="font-size: 20px">{{ monitorInfo.sysInfo.computerIp }}</strong>
                  </div>
                </a-col>
              </a-row>
              <a-row class="mi-table-th">
                <a-col :span="12" class="mi-table-td">
                  <img alt="" src="@/assets/monitor/icon_08.png" />
                  <div class="mi-info-box">
                    <p style="margin: 2px 0 0">系统架构</p>
                    <strong style="font-size: 20px">{{ monitorInfo.sysInfo.osArch }}</strong>
                  </div>
                </a-col>
                <a-col :span="12" class="mi-table-td">
                  <img alt="" src="@/assets/monitor/icon_09.png" />
                  <div class="mi-info-box">
                    <p style="margin: 2px 0 0">服务器名称</p>
                    <strong style="font-size: 16px; word-wrap: break-word">{{
                      monitorInfo.sysInfo.computerName
                    }}</strong>
                  </div>
                </a-col>
              </a-row>
              <a-row class="mi-table-th">
                <a-col :span="24" class="mi-table-td" style="border: none">
                  <img alt="" src="@/assets/monitor/icon_10.png" />
                  <div class="mi-info-box">
                    <p style="margin: 2px 0 0">项目路径</p>
                    <strong style="font-size: 20px">{{ monitorInfo.sysInfo.userDir }}</strong>
                  </div>
                </a-col>
              </a-row>
            </div>
          </div>
        </a-col>
        <a-col :lg="24" :md="24" :sm="24" :xl="24" :xs="24" :xxl="12">
          <div class="monitor-item-box">
            <div class="mi-top-box">内存</div>
            <div class="mi-content-box">
              <a-row class="mi-table-th">
                <a-col :span="24" class="mi-table-td" style="border: none">
                  <img alt="" src="@/assets/monitor/icon_05.png" />
                  <div class="mi-info-box">
                    <p style="margin: 2px 0 0">总内存</p>
                    <strong style="font-size: 28px">{{ monitorInfo.memInfo.total }}</strong>
                  </div>
                </a-col>
              </a-row>
              <a-row class="mi-table-th">
                <a-col :span="12" class="mi-table-td">
                  <img alt="" src="@/assets/monitor/icon_11.png" />
                  <div class="mi-info-box" style="width: 70%">
                    <p>
                      内存：<strong>{{ monitorInfo.memInfo.used }}</strong
                      >已用<strong style="margin-left: 15px">{{ monitorInfo.memInfo.free }}</strong
                      >可用
                    </p>
                    <a-progress
                      :percent="formatPercent(monitorInfo.memInfo.usageRate)"
                      strokeColor="#1791FF"
                    />
                  </div>
                </a-col>
                <a-col :span="12" class="mi-table-td">
                  <img alt="" src="@/assets/monitor/icon_11.png" />
                  <div class="mi-info-box" style="width: 70%">
                    <p>
                      虚拟内存：<strong style="margin-right: 15px">{{
                        monitorInfo.memInfo.swapUsed
                      }}</strong
                      >已用<strong>{{ monitorInfo.memInfo.swapTotal }}</strong
                      >可用
                    </p>
                    <a-progress
                      :percent="formatPercent(monitorInfo.memInfo.swapRate)"
                      strokeColor="#1791FF"
                    />
                  </div>
                </a-col>
              </a-row>
              <a-row class="mi-table-th">
                <a-col :span="24" class="mi-table-td" style="border: none">
                  <img alt="" src="@/assets/monitor/icon_12.png" />
                  <div class="mi-info-box">
                    <p style="margin: 2px 0 0">使用率</p>
                    <a-progress
                      :percent="formatPercent(monitorInfo.memInfo.totalUsageRate)"
                      strokeColor="#1791FF"
                    />
                  </div>
                </a-col>
              </a-row>
            </div>
          </div>
        </a-col>
        <a-col :lg="24" :md="24" :sm="24" :xl="24" :xs="24" :xxl="12">
          <div class="monitor-item-box">
            <div class="mi-top-box">JVM总内存</div>
            <div class="mi-content-box">
              <a-row class="mi-table-th">
                <a-col :span="24" class="mi-table-td">
                  <img alt="" src="@/assets/monitor/icon_11.png" />
                  <div class="mi-info-box">
                    <p>
                      空间：<strong>{{ monitorInfo.jvmInfo.free }}</strong
                      >可用<strong style="margin-left: 10px">{{ monitorInfo.jvmInfo.used }}</strong
                      >已用 （共<strong>{{ monitorInfo.jvmInfo.total }}</strong
                      >）&nbsp;<span style="color: rgba(0, 0, 0, 0.45)"
                        >最大可申请{{ monitorInfo.jvmInfo.max }}</span
                      >
                    </p>
                    <a-progress
                      :percent="formatPercent(monitorInfo.cpuInfo.cSys)"
                      strokeColor="#117dbb"
                    />
                  </div>
                </a-col>
              </a-row>
              <a-row class="mi-table-th">
                <a-col :span="12" class="mi-table-td">
                  <img alt="" src="@/assets/monitor/icon_09.png" />
                  <div class="mi-info-box">
                    <p>JDK版本</p>
                    <strong style="font-size: 28px">{{ monitorInfo.jvmInfo.jdkVersion }}</strong>
                  </div>
                </a-col>
                <a-col :span="12" class="mi-table-td" style="border: none">
                  <img alt="" src="@/assets/monitor/icon_10.png" />
                  <div class="mi-info-box">
                    <p style="margin: 2px 0 0">JDK路径</p>
                    <strong style="font-size: 16px; word-wrap: break-word">{{
                      monitorInfo.jvmInfo.jdkHome
                    }}</strong>
                  </div>
                </a-col>
              </a-row>
              <a-row class="mi-table-th">
                <a-col :span="24" class="mi-table-td">
                  <img alt="" src="@/assets/monitor/icon_12.png" />
                  <div class="mi-info-box">
                    <p>空间使用率</p>
                    <a-progress
                      :percent="formatPercent(monitorInfo.jvmInfo.usageRate)"
                      strokeColor="#117dbb"
                    />
                  </div>
                </a-col>
              </a-row>
            </div>
          </div>
        </a-col>
      </a-row>
    </div>
    <div class="container" style="box-shadow: 0px 20px 27px rgba(0, 0, 0, 0.05)">
      <a-table
        :columns="columns"
        :data-source="monitorInfo.sysFileInfo"
        :pagination="false"
        :rowKey="(record: SysFileInfo, index: number) => index"
        :scroll="{ y: 500 }"
      >
        <template #bodyCell="{ column, record }: { column: any; record: SysFileInfo }">
          <template v-if="column.key === 'total'">
            <a-tag color="blue">{{ record.total }}</a-tag>
          </template>
          <template v-if="column.key === 'usage'">
            <a-progress :percent="formatPercent(record.usage)" size="small" />
          </template>
          <template v-if="column.key === 'used'">
            <a-tag color="green">{{ record.used }}</a-tag>
          </template>
          <template v-if="column.key === 'free'">
            <a-tag color="volcano">{{ record.free }}</a-tag>
          </template>
        </template>
      </a-table>
    </div>
    <!--  弹窗   -->
    <a-modal v-model="allServerVisible" :footer="null" title="服务器监控" width="80%">
      <a-row :gutter="24">
        <template :key="key" v-for="(item, key) in allServerMonitor">
          <a-col :span="12">
            <a-descriptions :column="4" :title="item.computerIp" bordered>
              <a-descriptions-item :span="2" label="CPU">
                <a-progress :percent="formatPercent(item.cpuInfo)" strokeColor="#117dbb" />
              </a-descriptions-item>
              <a-descriptions-item :span="2" label="内存">
                <a-progress :percent="formatPercent(item.memInfo)" strokeColor="#8b12ae" />
              </a-descriptions-item>
              <a-descriptions-item label="磁盘">
                <a-progress :percent="formatPercent(item.jvmInfo)" strokeColor="#2ca60c" />
              </a-descriptions-item>
            </a-descriptions>
          </a-col>
        </template>
      </a-row>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import {
  getAllServerMonitor,
  getServerMonitor,
} from '@/api/services/monitor-maintenance/server-monitor'
import { getWorkNodeList } from '@/api/services/monitor-maintenance/flow-definition'

import { onMounted, onBeforeUnmount, ref } from 'vue'
import { message } from 'ant-design-vue/es/components'

/**
 * 计算百分数，防止出现两位小数 * 100出现小数失真的问题（例如：0.58 * 100 = 57.9999999）
 * @param num 数值
 * @param fixed 小数位数
 * return 百分数
 */
function formatPercent(num: number = 0, fixed = 2) {
  return parseFloat((num * 100).toFixed(fixed))
}

type CpuInfo = {
  cSys: number
  idle: number
  used: number
  iowait: number
  cpuNum: number
}

type MemInfo = {
  total: string
  used: string
  free: string
  usageRate: number
  swapUsed: string
  swapTotal: string
  swapRate: number
  totalUsageRate: number
}

type SysInfo = {
  osName: string
  computerIp: string
  osArch: string
  computerName: string
  userDir: string
}

type JvmInfo = {
  free: string
  used: string
  total: string
  max: string
  jdkVersion: string
  jdkHome: string
  usageRate: number
}

type SysFileInfo = {
  total: string
  used: string
  free: string
  typeName: string
  usage: number
  sysTypeName: string
  dirName: string
}

interface MonitorInfo {
  cpuInfo: Partial<CpuInfo>
  memInfo: Partial<MemInfo>
  sysInfo: Partial<SysInfo>
  jvmInfo: Partial<JvmInfo>
  sysFileInfo: SysFileInfo[]
}

const allServerVisible = ref(false)
const monitorInfo = ref<MonitorInfo>({
  cpuInfo: {}, // CPU
  memInfo: {}, // 内存
  sysInfo: {}, // 系统
  jvmInfo: {}, // 磁盘
  sysFileInfo: [], // 系统文件
})
const allServerMonitor = ref<
  Array<{ computerIp: string; cpuInfo: number; memInfo: number; jvmInfo: number }>
>([])
const serverList = ref<Array<{ serviceIp: string }>>([])
const serverIp = ref('')
const columns = [
  {
    width: 180,
    align: 'center',
    title: '总大小',
    key: 'total',
    dataIndex: 'total',
    scopedSlots: { customRender: 'total' },
  },
  {
    width: 180,
    align: 'center',
    title: '已使用大小',
    key: 'used',
    dataIndex: 'used',
    scopedSlots: { customRender: 'used' },
  },
  {
    width: 180,
    align: 'center',
    title: '剩余大小',
    key: 'free',
    dataIndex: 'free',
    scopedSlots: { customRender: 'free' },
  },
  {
    title: '文件类型',
    key: 'typeName',
    dataIndex: 'typeName',
    align: 'center',
  },
  {
    title: '资源的使用率',
    key: 'usage',
    dataIndex: 'usage',
    scopedSlots: { customRender: 'usage' },
  },
  {
    title: '盘符类型',
    key: 'sysTypeName',
    dataIndex: 'sysTypeName',
    align: 'center',
  },
  {
    title: '盘符路径',
    key: 'dirName',
    dataIndex: 'dirName',
  },
]

// 查询服务器监控信息
async function fetchServerMonitor(eventType?: string) {
  if (serverIp.value) {
    const resp = await getServerMonitor({ ip: serverIp.value })

    if (resp.data) {
      monitorInfo.value = resp.data
      if (eventType === 'reload') message.info('刷新成功')
      if (eventType === 'change') message.info('切换成功')
    }
  }
}

// 查询所有服务器监控信息
async function fetchAllServerMonitor() {
  const resp = await getAllServerMonitor({})

  allServerMonitor.value = resp.data
  allServerVisible.value = true
}

// 获取服务器列表
async function getServerList() {
  const resp = await getWorkNodeList({})
  const data = resp.data || []
  if (data.length > 0) {
    serverList.value = data
    serverIp.value = data[0].serviceIp
  }
}

let polling: ReturnType<typeof setInterval> | number | undefined

onMounted(async () => {
  await getServerList()
  await fetchServerMonitor()
  polling = setInterval(fetchServerMonitor, 10000)
})

onBeforeUnmount(() => {
  clearInterval(polling)
})
</script>
<style lang="less" scoped>
.server-monitor {
  height: 100%;
  width: 100%;

  .tool-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    background: #ffffff;
    // box-shadow: 0px 20px 27px rgba(0, 0, 0, 0.05);
    // border-radius: 12px;
    padding: 12px 20px;

    .title {
      font-size: 18px;
      font-weight: 400;
      color: rgba(0, 0, 0, 0.85);
    }

    > p {
      font-size: 18px;
      color: rgba(0, 0, 0, 0.85);
      line-height: 32px;
    }
  }

  .container {
    padding: 24px;
    width: 100%;
    background-color: #fff;
    overflow-y: auto;
  }

  .monitor-list-container {
    padding: 0 24px;
    .monitor-item-box {
      width: 100%;
      height: 380px;
      background: #ffffff;
      // box-shadow: 0px 20px 27px rgba(0, 0, 0, 0.05);
      border-radius: 8px;

      .mi-top-box {
        height: 60px;
        line-height: 60px;
        font-size: 16px;
        position: relative;
        padding-left: 40px;
        font-weight: 600;
      }

      .mi-top-box::after {
        content: '';
        width: 2px;
        height: 14px;
        position: absolute;
        left: 26px;
        border-radius: 2px;
        background-color: #1890ff;
        top: 50%;
        margin-top: -7px;
      }

      .mi-content-box {
        background-color: rgba(45, 94, 236, 0.02);
        height: 300px;
        border: 1px solid #ededed;
        display: flex;
        flex-direction: column;

        .mi-table-th {
          border-top: 1px solid #ededed;
          height: 100%;
        }

        .mi-table-th:first-child {
          border: none;
        }

        .mi-table-td {
          height: 100%;
          border-right: 1px solid #ededed;
          display: flex;
          align-items: center;

          img {
            width: 68px;
            height: 68px;
            margin-left: 15px;
          }

          .mi-info-box {
            width: 60%;

            p {
              line-height: 22px;
              margin: -16px 0 8px;
            }
          }
        }

        .mi-table-td:nth-child(2) {
          border: none;
        }
      }
    }
  }
}
</style>
