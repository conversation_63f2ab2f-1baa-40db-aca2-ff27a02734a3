<template>
  <div class="content">
    <div>
      <v-ace-editor
        v-model:value="jsonInput"
        lang="javascript"
        theme="chrome"
        style="width: 400px; height: 500px"
        @input="debouncedHandleInput"
        :options="{ useWorker: true }"
      />
    </div>
    <div>
      <Form
        :items="items"
        ref="FormRefs"
        v-model="formData"
        :extral="{}"
        @submit="submitHandle"
        :layout="layout"
        v-if="resetState"
      >
      </Form>
      <div class="button-group">
        <a-button class="submit-button" @click="submit" type="primary">提交</a-button>
        <a-button @click="reset">重置</a-button>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, nextTick, onMounted } from 'vue'
import { Form } from '@fs/fs-components'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { debounce } from '@/utils'
import { VAceEditor } from 'vue3-ace-editor'
import 'ace-builds/src-noconflict/mode-javascript' // Load the language definition file used below
import 'ace-builds/src-noconflict/theme-chrome' // Load the theme definition file used below
import ace from 'ace-builds'
import workerJavascript from 'ace-builds/src-noconflict/worker-javascript?url' // For vite
ace.config.setModuleUrl('ace/mode/javascript_worker', workerJavascript)

const router = useRouter()
const formData = ref<any>({})

const items = ref<any[]>([])

const jsonInput = ref(`${JSON.stringify(items.value, null, 2)}`)
const resetState = ref(true)
const FormRefs = ref()
const layout = ref({})

function submit() {
  FormRefs.value.submit()
}

function blurHandle() {
  try {
    resetState.value = false

    let result = eval(jsonInput.value)
    console.log('%c 🍔 result: ', 'font-size:12px;background-color: #ED9EC7;color:#fff;', result)
    items.value = result?.items || []
    layout.value = result?.layout || {}
    nextTick(() => {
      resetState.value = true
    })
  } catch (error) {
    // this.jsonOutput = '无效的 JSON 格式'
    console.error('无效的 JSON 格式', error)
    resetState.value = true
  }
}

function submitHandle(data: any) {
  console.log('%c 🍩 data: ', 'font-size:12px;background-color: #ED9EC7;color:#fff;', data)
}

function reset() {
  formData.value = {}
  FormRefs.value.reset()
}

const debouncedHandleInput = debounce(blurHandle, 300)

onMounted(async () => {
  resetState.value = false
  let RegexPatterns = FormRefs.value.RegexPatterns
  jsonInput.value = `const form = {
    layout: {
      layout: 'horizontal',
      labelCol: { span: 6 },
      wrapperCol: { span: 18 }
    },
    items:  [
      {
        name: 'name',
        label: '姓名',
        required: true,
        span: 8,
        rules: [{ required: true, message: '请输入姓名', trigger: 'change' }],
      },
      {
        name: 'age',
        label: '年龄',
        required: true,
        span: 8,
        rules: [
          { required: true, message: '请输入年龄' },
          { pattern: /^[0-9a-zA-Z_]{1,}$/, message: '只允许包含数字、字母、下划线' },
        ],
      },
      {
        name: 'account',
        label: '账号',
        required: true,
        span: 8,
        rules: [{ required: true, message: '请输入账号', trigger: 'change' }],
      },
      {
        name: 'password',
        label: '密码',
        required: true,
        span: 8,
        rules: [
          {
            required: true,
            validator: () => {
              if (!formData.value?.password) {
                return Promise.reject('请输入密码')
              }
            },
            trigger: 'change',
          },
        ],
      },
      {
        name: 'newPassword',
        label: '确认密码',
        required: true,
        span: 8,
        rules: [
          {
            required: true,
            validator: () => {
              if (!formData.value?.newPassword) {
                return Promise.reject('请再次输入密码')
              }
              if (formData.value?.password !== formData.value?.newPassword) {
                return Promise.reject('两次输入密码不一致')
              }
              return Promise.resolve();
            },
            trigger: 'change',
          },
        ],
      },
      {
        name: 'love',
        label: '爱好',
        type: 'select',
        span: 8,
        compProps: {
          options: [
            {
              label: '男人',
              value: 1,
            },
            {
              label: '女人',
              value: 2,
            },
            {
              label: '其他',
              value: 3,
            },
          ],
        },
      },
      {
        name: 'timer',
        label: '生日',
        span: 8,
        type: 'date-picker',
        compProps: {},
      },
      {
        name: 'email',
        label: '邮箱',
        required: true,
        span: 8,
        rules: [
          { required: true, message: '请输入邮箱' }
        ],
      },
      {
        name: 'desc',
        label: '描述',
        type: 'textarea',
        compProps: {},
      },
    ]
  }
  
  form
  `
  nextTick(() => {
    resetState.value = true
  })
})
</script>

<style scoped lang="less">
/* Styles for 404 page */
.content {
  position: relative;
  width: 100%;
  padding-top: 30px;
  display: flex;
  .button-group {
    text-align: right;
    .submit-button {
      margin-right: 20px;
    }
  }
}
</style>
