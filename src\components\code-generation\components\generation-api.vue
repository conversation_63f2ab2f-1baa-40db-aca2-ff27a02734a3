<template>
  <a-form
    :model="newModelValue"
    name="basic"
    :label-col="{ span: 4 }"
    :wrapper-col="{ span: 16 }"
    autocomplete="off"
    ref="formRef"
  >
    <a-form-item
      label="项目名称"
      name="project"
      :rules="[{ required: true, message: '请选择项目' }]"
    >
      <a-select
        v-model:value="newModelValue.project"
        placeholder="请选择对应文件"
        style="width: 300px"
        @change="handleChange($event, false)"
      >
        <a-select-option v-for="(item, index) in project" :key="index" :value="item.project">{{
          item.project
        }}</a-select-option>
      </a-select>
    </a-form-item>
    <a-form-item
      label="模块"
      name="module"
      v-if="newModelValue.project"
      :rules="[{ required: true, message: '请选择模块' }]"
    >
      <a-select
        v-model:value="newModelValue.module"
        placeholder="请选择模块"
        style="width: 300px"
        @change="modulesChange($event, false)"
      >
        <a-select-option v-for="(item, index) in modulesList" :key="index" :value="item.moduleName"
          >{{ item.moduleNameCn }} {{ item.moduleName }}</a-select-option
        >
      </a-select>
    </a-form-item>
  </a-form>
  <requestComponent
    v-if="newModelValue.module"
    v-model="newModelValue.request"
    :options="interfaceInfos"
    @updateSource="updateSource"
    @updateSearch="updateSearch"
    @updateForm="updateForm"
    :type="type"
  ></requestComponent>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import type { Project, Moudle, item } from '../generation'
import requestComponent from './request-component.vue'

interface Api {
  project: Project[] | null
  modelValue: any
  type?: string
}

const props: Api = withDefaults(defineProps<Api>(), {
  project: () => [],
  modelValue: {},
})

const formRef = ref()

const interfaceInfos = ref<item[]>([])

const newModelValue = computed({
  get: () => {
    return props.modelValue
  },
  set: (value) => {
    emit('update:modelValue', value)
  },
})

const updateSource = (columns: any[]) => {
  newModelValue.value.dataSource = columns
}

const updateSearch = (data: any[]) => {
  newModelValue.value.search = JSON.parse(JSON.stringify(data))
}

const updateForm = (data: any[]) => {
  newModelValue.value.form = data
}

const emit = defineEmits(['update:modelValue', 'interfaceInfos'])

const onSubmit = async () => {
  return new Promise((resolve, reject) => {
    formRef.value.validate().then(() => {
      return resolve(true)
    })
  })
}

defineExpose({
  onSubmit,
  handleChange,
  modulesChange,
})

const modulesList = ref<Moudle[]>([])

function handleChange(params: string, state = false) {
  const item = props.project?.find((item) => item.project === params)
  if (!state) {
    newModelValue.value.module = undefined
    newModelValue.value.request.map((item: { api: undefined }) => (item.api = undefined))
  }

  if (item) {
    modulesList.value = item.modules
  }
}

function modulesChange(params: string, state = false) {
  const item = modulesList.value.find((item) => item.moduleName === params)
  if (!state) {
    newModelValue.value.request.map((item: { api: undefined }) => (item.api = undefined))
  }
  if (item) {
    interfaceInfos.value = item.interfaceInfos
    emit('interfaceInfos', item.interfaceInfos)
  }
}
</script>

<style scoped lang="less"></style>
