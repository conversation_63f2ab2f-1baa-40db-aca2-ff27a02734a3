<template>
  <a-modal
    v-model:open="addOpen"
    @ok="modalHandleOk"
    :title="modalType === 'edit' ? '编辑' : '新增' + '岗位'"
    @cancel="cancel"
    width="800px"
    :confirm-loading="confirmLoading"
  >
    <a-form
      ref="formRef"
      :model="form"
      :rules="rules"
      :label-col="labelCol"
      :wrapper-col="wrapperCol"
    >
      <a-form-item label="岗位编号" name="jobId" v-if="modalType === 'edit'">
        <a-input v-model:value="form.jobId" :disabled="true" placeholder="请输入岗位编号" />
      </a-form-item>
      <a-form-item label="岗位名称" name="jobName">
        <a-input v-model:value="form.jobName" placeholder="请输入岗位名称" />
      </a-form-item>
      <a-form-item label="是否启用" name="isUse">
        <a-radio-group v-model:value="form.isUse" placeholder="请选择是否启用">
          <a-radio :value="1">是</a-radio>
          <a-radio :value="0">否</a-radio>
        </a-radio-group>
      </a-form-item>
      <a-form-item label="岗位职责" name="responsibility">
        <a-textarea v-model:value="form.responsibility" placeholder="请输入岗位职责" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>
<script lang="ts" setup>
import { defineEmits, defineProps, defineExpose, ref, watch } from 'vue'
import { message, type CascaderProps } from 'ant-design-vue'
import { useUserStore } from '@/stores/user'
import type { Rule } from 'ant-design-vue/es/form'
import { getJobDetails } from '@/api/services/permission'
interface FormState {
  jobId: string
  jobName: string
  // deptId: string
  isUse: string
  responsibility: string
}
const emits = defineEmits(['modalHandleOk', 'modalCancel', 'confirmLoadingHandle'])
const formRef = ref<HTMLFormElement | null>(null)
const labelCol = { span: 5 }
const wrapperCol = { span: 13 }
const modalType = ref<string>('')
const confirmLoading = ref<boolean>(false)
const addOpen = ref<boolean>(false)
const form = ref<FormState>({
  jobId: '',
  // deptId: '',
  jobName: '',
  isUse: '',
  responsibility: '',
})

defineProps({
  disabled: Boolean,
})

const rules: Record<string, Rule[]> = {
  jobName: [{ required: true, message: '请输入岗位名称', trigger: 'blur' }],
  isUse: [{ required: true, message: '请选择是否启用', trigger: 'blur' }],
  // deptId: [{ required: true, message: '请选择组织', trigger: 'blur' }],
}
const userStore = useUserStore()
function modalHandleOk() {
  formRef.value &&
    formRef.value
      .validate()
      .then(() => {
        confirmLoading.value = true
        if (modalType.value === 'edit') {
          // updateJob({ ...form.value })
          //   .then(() => {
          emits('modalHandleOk', form.value)
          message.success('更新成功!')
          addOpen.value = false
          confirmLoading.value = false
          //   })
          //   .finally(() => {
          //     confirmLoading.value = false
          //   })
        } else {
          const { isUse, jobName, responsibility } = form.value
          const params = {
            buId: userStore.userInfo.buId,
            isUse,
            jobName,
            responsibility,
            // deptId: deptId.length && deptId[deptId.length - 1],
          }
          // addJob(params)
          //   .then(() => {
          emits('modalHandleOk', form.value)
          message.success('新增成功!')
          addOpen.value = false
          confirmLoading.value = false
          //   })
          //   .finally(() => {
          //     confirmLoading.value = false
          //   })
        }
      })
      .catch((error: any) => {
        console.log(error)
      })
}

function cancel() {
  emits('modalCancel')
  formRef.value?.resetFields()
}

async function show(type: string, data?: Record<string, any>) {
  formRef.value?.resetFields()
  modalType.value = type
  addOpen.value = true
  if (type === 'edit') {
    editForm(data)
  }
}

function editForm(data: any) {
  if (data) {
    const copyData = JSON.parse(JSON.stringify(data))
    getJobDetails({ jobId: copyData.jobId }).then((res) => {
      const formValue = res?.data
      const { jobId, jobName, isUse, responsibility } = formValue
      // const deptId = 'D000161'
      const data = {
        jobId,
        jobName,
        isUse,
        responsibility,
      }
      form.value = data
    })
  }
}
defineExpose({ show })
</script>
