<template>
  <div class="column">
    <div class="top-box">
      <span @click="addColumn">
        <PlusOutlined class="PlusOutlined" />
      </span>
      <template v-if="indexNum >= 0">
        <EditOutlined class="EditOutlined mg-left5" @click="editColumn"></EditOutlined>
        <a-popconfirm
          title="是否确认删除?"
          ok-text="是"
          cancel-text="否"
          @confirm="deleteHandle(indexNum)"
        >
          <DeleteOutlined class="DeleteOutlined" />
        </a-popconfirm>
      </template>
    </div>
    <a-flex gap="small" :vertical="columnData?.comProps?.vertical">
      <template v-for="(item, index) in columnData.children" :key="index">
        <Echarts
          v-if="item.compnentName === 'Echarts'"
          :columnData="columnData"
          :data="item"
          :index="index"
          :projectData="projectData"
          @update:import="updateImport"
          :selectData="selectData"
        ></Echarts>
        <Column
          v-else
          :data="item"
          :indexNum="index"
          :projectData="projectData"
          @update:import="updateImport"
          @delete:column="deleteColumn"
          :selectData="selectData"
        ></Column>
      </template>
    </a-flex>
  </div>

  <a-modal
    v-model:open="open"
    :title="disabled ? '编辑' : '新增'"
    @ok="okHandle"
    @cancel="canceHandle"
    width="600px"
  >
    <a-form
      :model="formData"
      name="basic"
      :label-col="{ span: 4 }"
      :wrapper-col="{ span: 16 }"
      autocomplete="off"
      ref="formRef"
    >
      <a-form-item
        label="新增类型"
        name="type"
        :rules="[{ required: true, message: '请选择类型' }]"
      >
        <a-radio-group v-model:value="formData.type" :disabled="disabled">
          <a-radio-button value="echarts">图表</a-radio-button>
          <a-radio-button value="column">列</a-radio-button>
        </a-radio-group>
      </a-form-item>
      <a-form-item
        v-if="formData.type === 'column'"
        label="布局方式"
        name="layout"
        :rules="[{ required: true, message: '请选择布局方式' }]"
      >
        <a-radio-group v-model:value="formData.layout">
          <a-radio-button value="horizontal">水平</a-radio-button>
          <a-radio-button value="vertical">垂直</a-radio-button>
        </a-radio-group>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { PlusOutlined, DeleteOutlined, EditOutlined } from '@ant-design/icons-vue'
import Echarts from './echarts.vue'
import Column from './column.vue'

interface ColumnProps {
  data: any
  projectData: any[]
  indexNum: number
  selectData: any[]
}

const porps: ColumnProps = withDefaults(defineProps<ColumnProps>(), {
  selectData: () => [],
})

const open = ref(false)
const formData = ref({ type: 'echarts', layout: 'horizontal' })
const columnData = computed(() => porps.data)
const disabled = ref(false)

const emit = defineEmits(['update:import', 'delete:column'])

const updateImport = (data: any) => {
  emit('update:import', data)
}

const addColumn = () => {
  if (porps.indexNum >= 0) {
    open.value = true
  } else {
    columnData.value.children.push({
      compnentName: 'a-flex',
      comProps: {
        vertical: false,
        gap: 0,
      },
      children: [],
    })
  }
}

const editColumn = () => {
  open.value = true
  disabled.value = true
  formData.value.type = 'column'
  const vertical = columnData.value.comProps.vertical ? 'vertical' : 'horizontal'
  formData.value.layout = vertical
}

const canceHandle = () => {
  disabled.value = false
  open.value = false
}

const okHandle = () => {
  if (disabled.value) {
    columnData.value.comProps.vertical = formData.value.layout === 'vertical' ? true : false
    disabled.value = false
    open.value = false
    return
  }
  addHandle()
}

const deleteHandle = (index: number) => {
  emit('delete:column', index)
}

const deleteColumn = (index: number) => {
  columnData.value.children.splice(index, 1)
}

const addHandle = () => {
  const { type, layout } = formData.value
  if (type === 'echarts') {
    columnData.value.children.push({
      compnentName: 'Echarts',
      comProps: {
        getOptions: undefined,
        title: '图表',
        height: 300,
        options: {
          grid: {
            left: '0',
            right: '0',
            top: '20px',
            bottom: '0',
            containLabel: true,
          },
        },
      },
    })
  } else {
    columnData.value.children.push({
      compnentName: 'a-flex',
      comProps: {
        vertical: layout === 'vertical' ? true : false,
        gap: 0,
      },
      children: [],
    })
  }
  open.value = false
  console.log(
    '%c 🍨  columnData.value: ',
    'font-size:12px;background-color: #42b983;color:#fff;',
    columnData.value,
  )
}
</script>
<style scoped lang="less">
.column {
  width: calc(100% - 20px);
  padding: 10px;
  border: 1px solid #ccc;
  position: relative;
  .top-box {
    text-align: right;
    margin-bottom: 15px;
    .PlusOutlined,
    .DeleteOutlined,
    .EditOutlined {
      cursor: pointer;
    }
    .DeleteOutlined {
      margin-left: 5px;
    }
    .mg-right20 {
      margin-right: 20px;
    }
    .mg-left5 {
      margin-left: 5px;
    }
  }
  .content {
    display: flex;
  }
  .vertical {
    flex-direction: column;
  }
}
</style>
