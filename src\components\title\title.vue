<template>
  <div class="title">
    <div class="left">
      <div class="title-text">{{ title }}</div>
      <div class="desc">{{ desc }}</div>
    </div>
    <div class="right">
      <slot name="right"></slot>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { defineProps } from 'vue'

defineProps<{
  title?: string
  desc?: string
}>()
</script>
<style lang="less" scoped>
.title {
  display: flex;
  align-items: flex-start;
  justify-content: space-between; // 两端对齐
  width: 100%;
  position: relative;
  margin-bottom: 24px;

  .title-text {
    font-size: 20px;
    font-weight: bold;
    color: #141414;
    word-break: break-all;
  }
  .desc {
    font-size: 14px;
    color: #999;
    margin-top: 4px;
  }
}
</style>
