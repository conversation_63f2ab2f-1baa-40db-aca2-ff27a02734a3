import path from 'path'
import { getJsonPath } from '../../base-business/virtual-file-system.js'
const { cacheDirPath } = getJsonPath()
import fsExtra from 'fs-extra'
const { ensureFile } = fsExtra
import { promises as fs } from 'fs'

// 缓存目录json文件路径
export const virtualFileSystemJsonPath = path.join(cacheDirPath, 'virtual-file-system.json')

// 定义一个公共的 Car 类
export class VirtualFileSystem {
  // 构造函数，用于初始化实例
  constructor() {
    ensureFile(virtualFileSystemJsonPath)
  }

  /**
   * 异步读取虚拟文件系统中的数据
   * @param {...string} args - 用于定位数据的键
   * @returns {Promise<any>} - 返回找到的数据
   * @throws {Error} - 如果键不存在，则抛出错误
   */
  async readByKey(...args) {
    try {
      // 读取文件内容
      const content = (await fs.readFile(virtualFileSystemJsonPath, 'utf8')) || `{}`
      const data = JSON.parse(content)
      // 如果没有传入 args 参数，直接返回 data
      if (args.length === 0) {
        return data
      }
      // 使用 reduce 进行动态层级访问
      return args.reduce((acc, key) => {
        if (acc && acc[key] !== undefined) {
          return acc[key]
        } else {
          console.log(`Key "${key}" not found in the data`)
          return {}
        }
      }, data)
    } catch (err) {
      console.error('Error reading the data:', err.message)
      throw err // 重新抛出错误以便外部处理
    }
  }

  /**
   * 异步设置虚拟文件系统中的数据
   * @param {any} value - 要设置的值
   * @param {...string} args - 用于定位数据的键
   * @returns {Promise<void>} - 无返回值，表示操作完成
   * @throws {Error} - 如果键不存在，则抛出错误
   */
  async setByKey(value, ...args) {
    try {
      // 读取文件内容，如果文件为空则用空对象初始化
      const content = (await fs.readFile(virtualFileSystemJsonPath, 'utf8')) || '{}'
      const data = JSON.parse(content)

      // 获取最后一个键作为目标键
      const lastKey = args.pop()

      // 使用 reduce 找到目标对象，并进行动态创建缺失层级
      const target = args.reduce((acc, key) => {
        // 如果当前层级的对象不存在，创建一个新的空对象
        if (!acc[key]) {
          acc[key] = {} // 也可以用 [] 创建数组，视需求而定
        }
        return acc[key]
      }, data)

      // 设置目标键的值
      target[lastKey] = JSON.stringify(value)

      // 将更新后的数据写回文件
      await fs.writeFile(virtualFileSystemJsonPath, JSON.stringify(data, null, 2), 'utf8')
    } catch (err) {
      console.error('Error setting the data:', err.message)
      throw err // 重新抛出错误以便外部处理
    }
  }
}
