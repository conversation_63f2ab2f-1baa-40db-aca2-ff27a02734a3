<template>
  <div>
    <div class="page-full">
      <a-flex justify="space-between" style="padding-bottom: 10px">
        <div class="title mb-20px" text="20px #000000d9">实时表管理</div>
        <a-space :size="8">
          <a-button type="primary" @click="showMore = !showMore">
            <template #icon v-if="showMore"><UpOutlined /></template>
            <template #icon v-else><DownOutlined /></template>
            {{ showMore ? '收起' : '搜索' }}
          </a-button>
        </a-space>
      </a-flex>

      <div ref="pageMain" class="page-main">
        <div v-show="showMore">
          <a-form
            ref="queryForm"
            :labelCol="{ span: 8 }"
            class="query-container"
            style="margin-top: 0px"
          >
            <a-row :gutter="32">
              <a-col :lg="8" :md="12" :sm="24" :xl="6" :xs="24">
                <a-form-item label="源库">
                  <a-select
                    allowClear
                    v-model:value="params.dbSrcId"
                    placeholder="请选择源库"
                    optionFilterProp="label"
                    show-search
                    @change="handleQuery"
                  >
                    <a-select-option
                      v-for="(option, optKey) in libraryList"
                      :key="optKey"
                      :title="option.dbDesc"
                      :label="option.dbDesc"
                      :value="option.id"
                      >{{ option.dbDesc }}</a-select-option
                    >
                  </a-select>
                </a-form-item>
                <a-form-item label="目标库">
                  <a-select
                    allowClear
                    v-model:value="params.dbDesId"
                    optionFilterProp="label"
                    placeholder="请选择目标库"
                    show-search
                    @change="handleQuery"
                  >
                    <a-select-option
                      v-for="(option, optKey) in libraryList"
                      :key="optKey"
                      :title="option.dbDesc"
                      :label="option.dbDesc"
                      :value="option.id"
                      >{{ option.dbDesc }}</a-select-option
                    >
                  </a-select>
                </a-form-item>
                <a-form-item label="作业流名称">
                  <a-input
                    v-model:value="params.name"
                    allowClear
                    placeholder="请输入作业流名称"
                    @change="handleInputChangeCallback"
                    @pressEnter="handleQuery"
                  />
                </a-form-item>
              </a-col>
              <a-col :lg="8" :md="12" :sm="24" :xl="6" :xs="24">
                <a-form-item label="源库名称">
                  <a-input
                    v-model:value="params.dbSrcDbName"
                    allowClear
                    placeholder="请输入源库名称"
                    @change="handleInputChangeCallback"
                    @pressEnter="handleQuery"
                  />
                </a-form-item>
                <a-form-item label="目标库名称">
                  <a-input
                    v-model:value="params.dbDesDbName"
                    allowClear
                    placeholder="请输入目标库名称"
                    @change="handleInputChangeCallback"
                    @pressEnter="handleQuery"
                  />
                </a-form-item>
              </a-col>
              <a-col :lg="8" :md="12" :sm="24" :xl="6" :xs="24">
                <a-form-item label="源模式">
                  <a-input
                    v-model:value="params.dbSrcSchema"
                    allowClear
                    placeholder="请输入源模式"
                    @change="handleInputChangeCallback"
                    @pressEnter="handleQuery"
                  />
                </a-form-item>
                <a-form-item label="目标模式">
                  <a-input
                    v-model:value="params.dbDesSchema"
                    allowClear
                    placeholder="请输入目标模式"
                    @change="handleInputChangeCallback"
                    @pressEnter="handleQuery"
                  />
                </a-form-item>
              </a-col>
              <a-col :lg="8" :md="12" :sm="24" :xl="6" :xs="24">
                <a-form-item label="源表名称">
                  <a-input
                    v-model:value="params.dbSrcTable"
                    allowClear
                    placeholder="请输入源表名称"
                  />
                </a-form-item>
                <a-form-item label="目标表名称">
                  <a-input
                    v-model:value="params.dbDesDbTable"
                    allowClear
                    placeholder="请输入目标表名称"
                  />
                </a-form-item>
                <a-space :size="8" style="float: right">
                  <a-button type="primary" @click="handleQuery">查询</a-button>
                  <a-button @click="handleReset">重置</a-button>
                </a-space>
              </a-col>
            </a-row>
          </a-form>
        </div>
        <div class="list-content">
          <div class="table-card" :bordered="false">
            <a-table
              ref="table"
              :columns="columns"
              :loading="dataSourceLoading"
              :pagination="pagination"
              :rowKey="(record: any) => record.id"
              :scroll="{ x: 1300, y: 'calc(100vh - 450px)' }"
              :data-source="fullCompensationList"
              @change="handChangeTable"
              :row-selection="{
                selectedRowKeys: selectedRowKeys,
                onChange: onSelectChange,
                selections: selections,
                fixed: true,
              }"
            >
              <template #bodyCell="{ text, column, record }">
                <template v-if="column.dataIndex === 'dbDesIndexFields'">
                  <a :title="text" @click="handleEditField(record)"> {{ text || '-' }}</a>
                </template>
              </template>
            </a-table>
          </div>
        </div>
      </div>
    </div>
    <MultipleTableField ref="multipleTableField" @on-success="multipleTableFieldCallBack" />
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue'
import {
  getRealTimeTableList,
  getFullCompenstionJobFlowList,
  jobFlowConfigDTO,
} from '@/api/services/monitor-maintenance/flow-config'
import {
  DataSourceDTO,
  getDataSourceList,
  createIndexfun,
} from '@/api/services/monitor-maintenance/flow-definition'

import MultipleTableField from './components/index-field-table.vue'
import { message } from 'ant-design-vue'
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { UpOutlined, DownOutlined } from '@ant-design/icons-vue'

const userStore = useUserStore()

const route = useRoute()

const multipleTableField = ref()
const showMore = ref(true) // 是否展开页面全部查询信息

const ownerId = ref<string>(userStore.userInfo.loginAccount)
const jobFlowList = ref<jobFlowConfigDTO[]>([]) // 主流程列表
const libraryList = ref<DataSourceDTO[]>([])
const selectedRowKeys = ref<string[]>([])
// const splitFieldList = ref<any[]>([])
const dataSourceLoading = ref(false)
let pagination = reactive<any>({
  size: 'small',
  pageIndex: 1,
  pageSize: 20,
  defaultPageSize: 20,
  hideOnSinglePage: false,
  showQuickJumper: true,
  showSizeChanger: true,
  pageSizeOptions: ['10', '20', '40', '60'],
  showTotal: (total: number, range: any) =>
    `显示 ${range[0]} 到 ${range[1]} 条 , 共 ${total} 条记录`,
})
const params = reactive<any>({
  userName: '', // 用户名
  id: '', // 作业流ID
  dbSrcId: undefined, // 数据源ID
  dbSrcDbName: '', // 源库
  dbSrcSchema: '', // 源模式名
  dbSrcTable: '', // 源表
  dbDesId: undefined, // 目标数据源ID
  dbDesDbName: '', // 目标库名
  dbDesSchema: '', // 目标模式名
  dbDesDbTable: '', // 目标表名
  name: '', // 作业流名称
  compId: '', //  组件ID
})
const fullCompensationList = ref<any[]>([])
const columns = ref<any[]>([
  {
    title: '索引',
    ellipsis: true,
    dataIndex: 'dbDesIndexFields',
    width: 180,
    show: false,
    align: 'center',
    scopedSlots: { customRender: 'dbDesIndexFields' },
  },
  {
    title: '数据源',
    ellipsis: true,
    dataIndex: 'srcDbDesc',
    width: 180,
    show: false,
    align: 'center',
    scopedSlots: { customRender: 'srcDbDesc' },
  },
  {
    title: '源库',
    dataIndex: 'dbSrcDbName',
    width: 140,
    show: true,
    align: 'center',
    scopedSlots: { customRender: 'dbSrcDbName' },
  },
  {
    title: '源模式名',
    dataIndex: 'dbSrcSchema',
    width: 140,
    show: true,
    align: 'center',
    scopedSlots: { customRender: 'dbSrcSchema' },
  },
  {
    width: 220,
    show: true,
    title: '源表',
    align: 'center',
    dataIndex: 'dbSrcTable',
    scopedSlots: { customRender: 'dbSrcTable' },
  },
  {
    show: true,
    width: 180,
    ellipsis: true,
    align: 'center',
    title: '目标数据源',
    dataIndex: 'desDbDesc',
    scopedSlots: { customRender: 'desDbDesc' },
  },
  {
    title: '目标库名',
    dataIndex: 'dbDesDbName',
    width: 180,
    show: true,
    align: 'center',
    scopedSlots: { customRender: 'dbDesDbName' },
  },
  {
    title: '目标模式名',
    dataIndex: 'dbDesSchema',
    width: 140,
    show: true,
    align: 'center',
    scopedSlots: { customRender: 'dbDesSchema' },
  },
  {
    title: '目标表名',
    dataIndex: 'dbDesDbTable',
    width: 220,
    show: true,
    align: 'center',
    scopedSlots: { customRender: 'dbDesDbTable' },
  },
  {
    title: '作业流名称',
    dataIndex: 'name',
    width: 140,
    show: false,
    align: 'center',
    scopedSlots: { customRender: 'name' },
  },

  {
    title: 'ID',
    dataIndex: 'id',
    width: 160,
    show: true,
    align: 'center',
    scopedSlots: { customRender: 'id' },
  },
])

// 表格复选框下拉操作
const selections = computed(() => {
  return [
    {
      key: 'all-data',
      text: '全选所有',
      onSelect: () => {
        selectedRowKeys.value = fullCompensationList.value.map((i: any) => i.id)
      },
    },
    {
      key: 'clear-all',
      text: '清除所有',
      onSelect: () => {
        selectedRowKeys.value = []
      },
    },
  ]
})

onMounted(() => {
  if (route.query.id) {
    params.compId = route.query.id
  }
  getList()
  getJobFlowConfigList()
  getDataSourceListFn()
})

const handleInputChangeCallback = (e: any) => {
  const { value } = e.target
  if (!value && e.type === 'click') {
    handleQuery()
  }
}

const handChangeTable = (e: any) => {
  console.log('%c [ e ]-319', 'font-size:13px; background:pink; color:#bf2c9f;', e)

  pagination = reactive<any>(e)
  pagination.pageIndex = e.current
  getList()
}
const multipleTableFieldCallBack = () => {
  getList()
}

// 获取列表数据
const getList = () => {
  params.userName = ownerId.value
  const newkeys = Object.keys(params).filter((item) => params[item] !== '')
  const newP: any = {}
  newkeys.forEach((item) => {
    newP[item] = params[item]
  })
  dataSourceLoading.value = true
  const parameter = {
    pageIndex: pagination.pageIndex,
    pageSize: pagination.pageSize,
  }
  getRealTimeTableList(Object.assign(parameter, newP))
    .then((res) => {
      selectedRowKeys.value = []
      fullCompensationList.value = res.data.records || []
      pagination.total = res.data.totalRecords
    })
    .finally(() => {
      dataSourceLoading.value = false
    })
}

// 查询
const handleQuery = () => {
  getList()
}

// 复选框选择变化
const onSelectChange = (selectedRowKeyst: Array<string>) => {
  // 提取参数
  selectedRowKeys.value = selectedRowKeyst
}

// 获取作业流列表
const getJobFlowConfigList = () => {
  let params = { userName: ownerId }
  getFullCompenstionJobFlowList(params).then((res) => {
    jobFlowList.value = res.data
  })
}

// 获取数据源列表(源库、目标库,下拉数据)
const getDataSourceListFn = () => {
  if (!libraryList.value.length) {
    getDataSourceList({ ownerId: ownerId.value }).then((res) => {
      libraryList.value = res.data || []
    })
  }
}
const handleEditField = (record: any) => {
  createIndexfun({
    json: JSON.stringify(record),
    indexType: '[]',
  }).then((res) => {
    if (res.data.code === 1) {
      let dbDesColumn: any = JSON.parse(record.desIndexColumn)
      if (dbDesColumn.length) {
        let optionData = dbDesColumn.map((item: string) => {
          return {
            name: item,
            type: item,
          }
        })
        multipleTableField.value.showModal(
          record,
          JSON.parse(record['dbDesIndexFields']),
          optionData,
        )
      } else {
        multipleTableField.value.showModal(record)
      }
    } else {
      message.error(res.data.obj)
    }
  })
}

const handleReset = () => {
  params.value = {
    userName: ownerId.value,
    id: '', // 作业流ID
    dbSrcId: undefined, // 数据源ID
    dbSrcDbName: '', // 源库
    dbSrcSchema: '', // 源模式名
    dbSrcTable: '', // 源表
    dbDesId: undefined, // 目标数据源ID
    dbDesDbName: '', // 目标库名
    dbDesSchema: '', // 目标模式名
    compId: '', // 目标模式名
    dbDesDbTable: '', // 目标表名
  }
}
</script>

<style scoped lang="less">
.query-container :deep(.ant-row .ant-form-item .ant-form-item-label) {
  display: none;
}
</style>
