import type { FlowNodeData, ListItem } from './type'
import type { WorkflowItem, WorkflowNode } from '@/api/approval-workflow'

/**
 * 将 WorkflowNode 转换为 FlowNodeData
 * @param node - WorkflowNode 对象
 * @returns 转换后的 FlowNodeData 对象
 */
export function workflowNode2FlowNodeData(node: WorkflowNode): Partial<FlowNodeData> {
  return {
    id: node.id || '',
    enabled: true,
    label: node.nodeName || '',
    value: node.approverId,
  }
}
/**
 * 将 FlowNodeData 转换为 WorkflowNode
 * @param node - FlowNodeData 对象
 * @returns 转换后的 WorkflowNode 对象
 */
export function flowNodeData2WorkflowNode(
  node: FlowNodeData,
  index: number,
): Partial<WorkflowNode> {
  return {
    nodeName: node.label,
    nodeOrder: index,
    approverId: node.value,
    approverType: 1,
    nodeType: 2,
  }
}
/**
 * 将 WorkflowItem 转换为 ListItem
 * @param item - WorkflowItem 对象
 * @returns 转换后的 ListItem 对象
 */
export function workflowItem2ListItem(item: WorkflowItem): Partial<ListItem> {
  return {
    id: item.id,
    name: item.name,
    description: item.description,
    category: item.workflowType,
    pushResultUrl: item.pushResultUrl,
    updatedTime: item.updatedTime,
    updatedUserName: item.updateUserName,
    flowNodes: (item.nodes || []).map((node) => workflowNode2FlowNodeData(node) as FlowNodeData),
  }
}
/**
 * 将 ListItem 转换为 WorkflowItem
 * @param item - ListItem 对象
 * @returns 转换后的 WorkflowItem 对象
 */
export function listItem2WorkflowItem(item: ListItem): Partial<WorkflowItem> {
  return {
    name: item.name,
    description: item.description,
    workflowType: item.category,
    pushResultUrl: item.pushResultUrl,
    nodes: (item.flowNodes || []).map(
      (node, index) => flowNodeData2WorkflowNode(node, index) as WorkflowNode,
    ),
  }
}
