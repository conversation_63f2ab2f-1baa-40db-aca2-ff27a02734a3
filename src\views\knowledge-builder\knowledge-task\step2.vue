<template>
  <div class="step-content">
    <a-form
      layout="horizontal"
      label-align="right"
      class="form-content w-50%"
      :label-col="{ span: 5 }"
      :wrapper-col="{ span: 19 }"
      ref="formRef"
      :model="formState"
      :rule="rules"
    >
      <a-form-item name="splitLength" label="分段最大长度" required>
        <a-input-number
          style="width: 100%"
          placeholder="请输入分段最大长度"
          id="inputNumber"
          v-model:value="formState.splitLength"
        />
      </a-form-item>

      <a-form-item name="semanticSplit" label="分段处理">
        <a-checkbox
          v-model:checked="formState.semanticSplit"
          :disabled="false"
          :indeterminate="false"
        >
          根据文档语义切分段落，段落长度可能小于分段最大长度
        </a-checkbox>
      </a-form-item>
    </a-form>
  </div>
</template>

<script setup lang="ts">
import type { TaskItem } from '../type'

const rules = {
  llmType: [{ required: true, message: '分段最大长度不能为空', trigger: 'change' }],
}
const formRef = ref()

const formState = defineModel<TaskItem>('data', { required: true })

const stepSubmit = async () => {
  const res = await formRef.value.validate().catch((error) => {
    console.log('error', error)
  })
  if (res) return true
}

defineExpose({
  stepSubmit,
})
</script>
<style scoped>
.step-content {
  flex: 3 1 0%;
  display: flex;
  flex-direction: column;
  align-items: center;
}
:deep(.ant-form-item-control-input-content) {
  text-align: left;
}
:deep(.ant-form-item-explain-error) {
  text-align: left;
}
</style>
