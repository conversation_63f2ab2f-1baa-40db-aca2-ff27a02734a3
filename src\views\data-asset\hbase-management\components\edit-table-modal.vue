<template>
  <a-modal
    :open="visible"
    :title="`${isEdit ? '编辑' : '新增'}表字段`"
    @ok="handleOk"
    @cancel="handleCancel"
    :maskClosable="false"
    width="700px"
    :confirmLoading="loading"
  >
    <a-form
      ref="formRef"
      :model="formState"
      :rules="rules"
      :label-col="{ style: { width: '50px' } }"
    >
      <div class="modal-header">
        <a-form-item style="width: 360px" v-if="isEdit" label="表名" labelAlign="left">
          {{ formState.tableName }}
        </a-form-item>
        <a-form-item style="width: 360px" v-else label="表名" name="tableName" labelAlign="left">
          <a-input
            v-model:value="formState.tableName"
            placeholder="请输入表名"
            show-count
            :maxlength="30"
          />
        </a-form-item>
        <a-button @click="handleAdd" style="margin-bottom: 8px">新增</a-button>
      </div>
      <div class="error-msg" v-if="showError">{{ `必须至少指定一个列族` }}</div>
      <a-table :data-source="dataSource" :columns="columns" :pagination="false">
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'columnName'">
            <a-input v-model:value="editableData[record.key]" />
          </template>
        </template>
        <template #operation="{ record }">
          <!-- <a @click.prevent="onDelete(record.key)">删除</a> -->
          <!-- 必须至少指定一个列族 -->
          <a-button :disabled="dataSource.length < 2" @click="onDelete(record.key)" type="link"
            >删除</a-button
          >
        </template>
      </a-table>
    </a-form>
  </a-modal>
</template>

<script lang="ts" setup>
import {
  ref,
  defineProps,
  defineEmits,
  watch,
  reactive,
  computed,
  type UnwrapRef,
  type Ref,
} from 'vue'
import { message } from 'ant-design-vue'
import {
  addHbaseTable,
  getHbaseTableColumnList,
  updateHbaseTableColumn,
} from '@/api/services/data-asset/hbase-management'
import type { RuleObject } from 'ant-design-vue/es/form'

// interface Column {
//   columnName: string
//   columnComment: string
//   dataType: string
//   columnType: string
// }

const props = defineProps<{
  visible: boolean
  isEdit: boolean
  tableData: Record<string, any>
  databaseData?: Record<string, any>
}>()

const emit = defineEmits(['update:visible', 'success', 'refresh'])

const formRef = ref()
const formState = ref({
  tableName: '',
})
const showError = ref(false)
const loading = ref(false)

const validateName = async (_rule: RuleObject, value: string) => {
  if (value === '') {
    return Promise.reject('请输入表名')
  }
  if (!/^[a-zA-Z0-9_\u4e00-\u9fa5]+$/u.test(value)) {
    return Promise.reject('表名称只能包含以下字符：下划线 (_)')
  }
  return Promise.resolve()
}

const rules = {
  tableName: [{ required: true, max: 30, validator: validateName }],
}
// 表格相关字段
const editableData: UnwrapRef<any[]> = reactive([])
const columns = [
  {
    title: '列名',
    dataIndex: 'columnName',
  },
  {
    title: '操作',
    dataIndex: 'operation',
    slots: { customRender: 'operation' },
  },
]

const dataSource: Ref<any[]> = ref([
  {
    columnName: '',
    key: '0',
  },
])

watch(
  () => props.visible,
  async (newVal) => {
    if (props?.tableData?.tableName && newVal) {
      formState.value.tableName = props?.tableData?.tableName
      if (!props.isEdit) {
        return
      }
      try {
        const params = {
          id: props.databaseData?.dbUrlId,
          namespace: props.databaseData?.name,
          tableName: formState.value.tableName,
        }
        const res: any = await getHbaseTableColumnList(params)
        if ((res.data?.length ?? 0) > 0) {
          dataSource.value = res.data.map((item: string, index: number) => {
            return {
              key: `${index}`,
              columnName: item,
            }
          })
        }
        for (const key in dataSource.value) {
          editableData[key] = dataSource.value.filter((item) => key === item.key)[0]?.columnName
        }
      } catch (error) {
        console.error('获取表字段失败:', error)
      }
    }
  },
  { immediate: true },
)

const handleOk = async () => {
  showError.value = false
  loading.value = true
  try {
    await formRef.value.validate()
    const params = {
      id: props.databaseData?.dbUrlId,
      namespace: props.databaseData?.name,
      tableName: formState.value.tableName,
      colFamily: editableData.filter((item) => item),
    }
    if (params.colFamily.length === 0) {
      showError.value = true
      loading.value = false
      return
    }
    if (!props.isEdit) {
      await addHbaseTable(params)
      message.success('创建成功')
    } else {
      await updateHbaseTableColumn(params)
    }

    emit('success')
    handleCancel()
  } catch (error) {
    console.error('失败:', error)
  } finally {
    loading.value = false
  }
}

const handleCancel = () => {
  showError.value = false
  formRef.value?.resetFields()
  emit('update:visible')
}

const count = computed(() => dataSource.value.length + 1)
const handleAdd = () => {
  const newData = {
    key: `${count.value}`,
    columnName: '',
  }
  dataSource.value.push(newData)
}

const onDelete = (key: any) => {
  dataSource.value = dataSource.value.filter((item) => item.key !== key)
  delete editableData[key]
}
</script>

<style lang="less" scoped>
:deep(.ant-checkbox-group) {
  width: 100%;
}
:deep(.ant-form-item) {
  margin-bottom: 22px;
}
.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.error-msg {
  color: #ff4d4f;
  font-size: 12px;
}
</style>
