import service from '@/api'
import { SODATAFLINK } from '@/api/base'

type CodeTemplateDTO = {
  id?: string
  name: string
  desc: string
  content: string
  ownerId: string
  createTime?: string
  updateTime?: string
}

type ArrayResponse<T> = {
  code: string
  data: { code: number; message: string; obj: Array<T> }
  msg: string
}

type DataResponse<T> = {
  code: string
  data: any
  msg: string
}

// 获取代码模板列表
export function getCodeTemplatList(data: {
  name?: string
  desc?: string
  pageIndex: number
  pageSize: number
}): Promise<DataResponse<{ records: CodeTemplateDTO[]; totalRecords: number }>> {
  return service({
    method: 'get',
    url: `${SODATAFLINK}/flink/resourceManager/templatecoderealtimePageList`,
    params: data,
  })
}

// 新增代码模板
export function addCodeTemplat(data: CodeTemplateDTO): Promise<DataResponse<string>> {
  return service({
    method: 'POST',
    url: `${SODATAFLINK}/flink/resourceManager/templatecoderealtimeInsert`,
    data,
  })
}

// 编辑代码模板
export function editCodeTemplat(params: CodeTemplateDTO): Promise<DataResponse<string>> {
  return service({
    method: 'put',
    url: `${SODATAFLINK}/flink/resourceManager/templatecoderealtimeUpdate/${params.id}`,
    data: params,
  })
}

// 删除代码模板
export function deleteCodeTemplat(params: {
  id: string
  ownerId: string
}): Promise<DataResponse<string>> {
  return service({
    url: `${SODATAFLINK}/flink/resourceManager/templatecoderealtimeDelete/${params.id}`,
    method: 'delete',
    data: params,
  })
}
