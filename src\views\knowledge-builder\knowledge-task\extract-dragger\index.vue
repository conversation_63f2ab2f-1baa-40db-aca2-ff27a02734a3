<template>
  <a-drawer
    v-model:open="open"
    class="custom-class"
    root-class-name="root-class-name"
    :bodyStyle="{ padding: 0 }"
    :headerStyle="{ borderBottom: 'none' }"
    :root-style="{ color: 'blue' }"
    :width="650"
    placement="right"
    :getContainer="false"
    :mask-closable="true"
    :maskStyle="{ background: 'transparent' }"
    @after-open-change="afterOpenChange"
  >
    <template #title>
      <header class="title">{{ currentEchartsData?.name }}</header>
    </template>
    <a-collapse v-model:activeKey="activeKey">
      <a-collapse-panel key="1" header="基本信息">
        <div class="belong" :style="{ marginBottom: '8px' }">所属类型：{{ computedName }}</div>
        <a-radio-group v-model:value="mode">
          <a-radio-button value="static">静态属性</a-radio-button>
          <a-tooltip title="当前实体无动态属性">
            <a-radio-button value="dynamic" disabled>动态属性</a-radio-button>
          </a-tooltip>
        </a-radio-group>
      </a-collapse-panel>
      <a-collapse-panel key="2" header="基础属性">
        <a-table :dataSource="computedTableData" :columns="columns" :pagination="false"> </a-table>
      </a-collapse-panel>
    </a-collapse>
  </a-drawer>
</template>

<script lang="ts" setup>
import { ref, defineProps } from 'vue'
import { data, columns, nameMap } from './data-source'
const props = defineProps<{
  currentEchartsData: any
  sampleMap: any
}>()
const open = ref<boolean>(false)
const activeKey = ref(['1', '2'])
const mode = ref<'static' | 'dynamic'>('static')

const computedName = computed(() => {
  return props.sampleMap[props.currentEchartsData?.labels] + `(${props.currentEchartsData?.labels})`
})
const computedTableData = computed(() => {
  const list = (
    Object.keys(props.currentEchartsData?.properties || {}) as Array<keyof typeof nameMap>
  ).map((item, index) => {
    return {
      key: index,
      chinese: nameMap[item] || item,
      english: item,
      type: props.currentEchartsData?.properties[item],
    }
  })
  return list
})

const afterOpenChange = (bool: boolean) => {
  console.log('open', bool)
}

const showDrawer = () => {
  open.value = true
}

defineExpose({
  showDrawer,
})
</script>
<style scoped lang="less">
.root-class-name {
  /deep/ .ant-drawer-header {
    border-bottom: none;
  }
  .radio-wrap {
    padding: 0 24px 16px;
  }
}
.title {
}
</style>
