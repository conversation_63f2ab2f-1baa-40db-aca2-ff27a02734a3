<template>
  <a-form
    :model="formPort"
    name="basic"
    :label-col="{ span: 3 }"
    :wrapper-col="{ span: 20 }"
    autocomplete="off"
    ref="formPortRef"
  >
    <a-form-item label="入参">
      <searchColumns v-model="formPort.params" type="params"></searchColumns>
    </a-form-item>
    <a-form-item :label="'出参' + getTypeName(formPort.returnType)">
      <searchColumns v-model="formPort.response" type="response"></searchColumns>
    </a-form-item>
  </a-form>
  <div style="text-align: right">
    <a-button type="primary" @click="submit">确定</a-button>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { getTypeName } from '@/utils/gpt-tool'
import searchColumns from '../code-generation/components/search-columns.vue'
import { addGptMoudles, addInterface } from '@/api/services/manage'
import { getNewResponse } from '@/utils/gpt-tool'

interface Argument {
  data: any
  moduleName: string
}

const props: Argument = withDefaults(defineProps<Argument>(), {})

const emit = defineEmits(['update:form', 'success:argument'])
const addModule = ref(false)

const formPort = computed({
  get: () => {
    return props.data
  },
  set: (value) => {
    emit('update:form', value)
  },
})

async function submit() {
  try {
    await addGptMoudles({
      moduleName: props.moduleName,
      moduleNameCn: props.moduleName,
    })
    addModule.value = true
    const response = JSON.parse(JSON.stringify(formPort.value.response))
    getNewResponse(response)
    const obj: any = {
      moduleName: props.moduleName,
      ...formPort.value,
      response,
    }
    await addInterface(obj)
    emit('success:argument')
  } catch (error) {
    console.log('%c 🍺 error: ', 'font-size:12px;background-color: #2EAFB0;color:#fff;', error)
  }
}
</script>
<style scoped lang="less"></style>
