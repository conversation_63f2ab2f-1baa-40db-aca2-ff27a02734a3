<template>
  <div class="gpt-manage">
    <div class="add-box">
      <div>
        <a-form :label-col="{ span: 6 }" :wrapper-col="{ span: 17 }">
          <a-form-item label="模块名称">
            <div style="display: flex">
              <a-select
                ref="select"
                v-model:value="moduleValue"
                style="width: 300px"
                @change="handleChange"
                placeholder="清先新增模块"
              >
                <a-select-option
                  :value="item.moduleName"
                  v-for="item in moduleList"
                  :key="item.moduleName"
                >
                  {{ item.moduleNameCn }} {{ item.moduleName }}
                </a-select-option>
              </a-select>
              <a-button type="primary" @click="generationGptHandle" class="mg-left10"
                >生成代码</a-button
              >
            </div>
          </a-form-item>
        </a-form>
      </div>
      <div>
        <span class="mg-right10"
          >入参 <a-switch v-model:checked="paramsChecked" @change="dataChange($event, 'params')"
        /></span>
        <span class="mg-right10"
          >出参
          <a-switch v-model:checked="responseChecked" @change="dataChange($event, 'response')"
        /></span>
        <a-button type="primary" @click="addMouleHandle" class="mg-right10">新增模块</a-button>
        <a-button type="primary" @click="openInterfaceHandle">新增接口</a-button>
      </div>
    </div>
    <a-table :dataSource="dataSource" :columns="columns" :pagination="{ pageSize: 100 }">
      <template #bodyCell="{ column, record }">
        <div v-if="column.key === 'action'" style="display: flex">
          <a-button type="link" @click="editHandle(record)">编辑</a-button>
          <a-popconfirm
            title="是否确认删除"
            ok-text="是"
            cancel-text="否"
            @confirm="delHandle(record)"
          >
            <a-button type="link">删除</a-button>
          </a-popconfirm>
          <a-button type="link" @click="generateMockData(record)">生成mock数据</a-button>
        </div>
        <template v-else-if="column.key === 'params'">
          <div style="width: 500px">
            <a-tag v-if="!record?.params" color="red">any</a-tag>
            <template v-else>
              <DownCircleOutlined
                v-if="!record?.paramsShow"
                @click="record.paramsShow = true"
              ></DownCircleOutlined>
              <UpCircleOutlined v-if="record.paramsShow" @click="record.paramsShow = false" />
              <paramsTable :dataSource="record.params" v-if="record.paramsShow"></paramsTable>
            </template>
          </div>
        </template>
        <template v-else-if="column.key === 'response'">
          <div style="width: 430px">
            <a-tag v-if="!record?.response" color="red">any</a-tag>
            <template v-else>
              <DownCircleOutlined
                v-if="!record?.responseShow"
                @click="record.responseShow = true"
              ></DownCircleOutlined>
              <UpCircleOutlined v-if="record?.responseShow" @click="record.responseShow = false" />
              <responseTable
                :dataSource="record.response"
                v-if="record.responseShow"
              ></responseTable>
            </template>
          </div>
        </template>
        <template v-else>
          {{ record[column.key] }}
        </template>
      </template>
    </a-table>
  </div>

  <a-modal
    v-model:open="open"
    title="新增模块"
    width="700px"
    okText="保存"
    :confirmLoading="spinning"
  >
    <a-spin tip="Loading..." :spinning="spinning">
      <a-form
        :model="formState"
        name="basic"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 16 }"
        autocomplete="off"
        ref="formRef"
      >
        <a-form-item
          label="模块名称"
          name="moduleNameCn"
          :rules="[{ required: true, message: '请输入模块名称' }]"
        >
          <a-input v-model:value="formState.moduleNameCn" placeholder="请输入模块名称" />
        </a-form-item>
        <a-form-item
          label="模块路径"
          name="moduleName"
          :rules="[
            { required: true, message: '请输入模块路径' },
            { pattern: /^[a-zA-Z0-9_]+$/, message: '模块路径只能包含字母、数字和下划线' },
          ]"
        >
          <a-input v-model:value="formState.moduleName" placeholder="请输入模块路径" />
        </a-form-item>
      </a-form>
    </a-spin>
    <template #footer>
      <a-button key="back" @click="open = false">取消</a-button>
      <a-button key="submit" type="primary" @click="addMoudles">保存</a-button>
    </template>
  </a-modal>

  <a-modal
    v-model:open="openPort"
    :title="oldInterfaceName ? '编辑接口' : '新增接口'"
    width="1000px"
    okText="生成接口"
    :confirmLoading="spinning"
    @cancel="cancelHandle"
    :maskClosable="false"
  >
    <a-spin tip="Loading..." :spinning="spinning">
      <a-form
        :model="formPort"
        name="basic"
        :label-col="{ span: 3 }"
        :wrapper-col="{ span: 20 }"
        autocomplete="off"
        ref="formPortRef"
      >
        <a-form-item
          label="接口需求"
          name="message"
          :rules="[{ required: true, message: '请输入接口需求' }]"
        >
          <a-textarea v-model:value="formPort.message" placeholder="请输入接口需求" />
          <div class="generate">
            <a-button key="submit" type="primary" @click="generateHandle" :loading="generateLoading"
              >生成入参出参</a-button
            >
          </div>
        </a-form-item>
        <a-form-item label="入参">
          <searchColumns v-model="formPort.params" type="params"></searchColumns>
        </a-form-item>
        <a-form-item :label="'出参' + getTypeName(formPort.returnType)">
          <searchColumns v-model="formPort.response" type="response"></searchColumns>
        </a-form-item>
      </a-form>
    </a-spin>
    <template #footer>
      <a-button key="back" @click="openPort = false">取消</a-button>
      <a-button key="submit" type="primary" @click="addInterfaceHandle" :loading="spinning"
        >保存</a-button
      >
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import {
  getGptMoudles,
  addGptMoudles,
  addInterface,
  generationGpt,
  delGenerationGpt,
  completions,
  getApiTemplate,
  saveApiMock,
} from '@/api/services/manage'
import { message, notification } from 'ant-design-vue/es/components'
import searchColumns from '@/components/code-generation/components/search-columns.vue'
import { DownCircleOutlined, UpCircleOutlined } from '@ant-design/icons-vue'
import paramsTable from './component/params-table.vue'
import responseTable from './component/response-table.vue'
import { getMessage, getCompletionsResponse, getTypeName, getNewResponse } from '@/utils/gpt-tool'

const open = ref(false)
const openPort = ref(false)
const spinning = ref(false)
const generateLoading = ref(false)
const paramsChecked = ref(false)
const responseChecked = ref(false)
const moduleList = ref<any[]>([])
const formRef = ref<any>()
const formPortRef = ref<any>()
const formPort = ref<any>({
  message: '',
  params: [],
  response: [],
})
const oldInterfaceName = ref('')
const moduleValue = ref(undefined)
const resultData = ref<any>('')
const columns = [
  {
    title: '接口路径（模块/接口名）',
    dataIndex: 'title',
    key: 'title',
  },
  {
    title: '入参',
    dataIndex: 'params',
    key: 'params',
    with: 500,
  },
  {
    title: '出参',
    dataIndex: 'response',
    key: 'response',
    width: 400,
  },
  {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
  },
]
const formState = ref({
  moduleName: '',
  moduleNameCn: '',
})
const dataSource = ref<any[]>([])

function openInterfaceHandle() {
  openPort.value = true
  cancelHandle()
}

async function generateMockData(record: any) {
  try {
    const { data } = await getApiTemplate({
      moduleName: moduleValue.value,
      interfaceName: record.interfaceName,
    })
    const val = {
      model: 'Qwen2-coder-32B-feisuan',
      stream: true,
      messages: [{ role: 'user', content: getMessageMock(data) }],
    }
    notification.open({
      message: '提示',
      description: '生成mock数据中，请稍后',
      duration: 2,
    })
    const response = await getCompletions(val)

    await saveApiMock({
      moduleName: moduleValue.value,
      interfaceName: record.interfaceName,
      content: response,
    })

    message.success('生成mock数据成功')
  } catch (error: any) {
    message.error(error?.data?.data || '生成mock数据失败')
  }
}

function addMouleHandle() {
  open.value = true
  oldInterfaceName.value = ''
  formState.value = {
    moduleName: '',
    moduleNameCn: '',
  }
}

function cancelHandle() {
  formPort.value.message = ''
  formPort.value.params = []
  formPort.value.response = []
  oldInterfaceName.value = ''
}

function dataChange(event: any, type: string) {
  dataSource.value.map((item: any) => {
    if (type === 'params') {
      item.paramsShow = event
    } else if (type === 'response') {
      item.responseShow = event
    }
  })
}

function handleChange() {
  getMoudles()
}

async function addMoudles() {
  try {
    formRef.value.validate().then(async () => {
      spinning.value = true
      const obj: any = {
        ...formState.value,
      }
      await addGptMoudles({
        ...obj,
      })
      message.success('新增成功')
      getMoudles()
      spinning.value = false
      open.value = false
    })
  } catch (error) {
    spinning.value = false
    console.error('新增错误：' + error)
  }
}

async function getMoudles() {
  const { data } = await getGptMoudles()
  moduleList.value = []
  if (data) {
    dataSource.value = []
    Object.keys(data).forEach((key) => {
      const obj = JSON.parse(data[key])
      moduleList.value.push({
        ...obj,
      })
    })
    if (moduleList.value.length > 0) {
      if (!moduleValue.value) {
        moduleValue.value = moduleList.value[0].moduleName
      }
      const newobj = moduleList.value.find((item: any) => item.moduleName === moduleValue.value)
      const obj = !moduleValue.value ? moduleList.value[0] : newobj
      obj.interfaceInfos &&
        obj.interfaceInfos.forEach((item: any) => {
          dataSource.value.push({
            ...item,
            title: item?.fullPath + `（${obj?.moduleNameCn}/${item?.interfaceNameCn}）`,
          })
        })
    }
  }
}

function editHandle(data: any) {
  const newData: any = JSON.parse(JSON.stringify(data))
  setResponseType(newData.response)
  formPort.value = newData
  openPort.value = true
  formPort.value.message = newData.interfaceNameCn
  oldInterfaceName.value = newData.interfaceName
}

function setResponseType(list: any) {
  list.forEach((element: any) => {
    if (element.isArray !== undefined) {
      element.type = element.isArray ? 'array' : 'object'
      if (element.isArray) setResponseType(element.children)
    }
  })
}

async function delHandle(data: any) {
  try {
    await delGenerationGpt({
      moduleName: moduleValue.value,
      interfaceName: data.interfaceName,
    })
    message.success('删除成功')
    getMoudles()
  } catch (error) {
    console.log('删除失败：' + error)
  }
}

function generateHandle() {
  try {
    formPortRef.value.validate().then(async () => {
      generateLoading.value = true
      resultData.value = ''
      const paramsData = formPort.value?.params || []
      const responseData = formPort.value?.response || []
      const params = {
        model: 'Qwen2-coder-32B-feisuan',
        stream: true,
        messages: [
          { role: 'user', content: getMessage(formPort.value.message, paramsData, responseData) },
        ],
      }
      const response = await getCompletions(params)
      generateLoading.value = false
      resultData.value = response
      console.log(
        '%c 🦀 resultData.value: ',
        'font-size:12px;background-color: #4b4b4b;color:#fff;',
        resultData.value,
      )
      formPort.value = { ...formPort.value, ...resultData.value }
    })
  } catch (error) {
    spinning.value = false
    message.error('生成失败，请稍后重试' + error)
    console.error('新增错误：' + error)
  }
}

async function getCompletions(val: any) {
  const response = await completions(val)
  const data = getCompletionsResponse(response)
  return data
}

async function addInterfaceHandle() {
  try {
    spinning.value = true
    const {
      interfaceName = 'test',
      httpMethodName = 'GET',
      params = [],
      response = [],
      fullPath = '',
      interfaceDesc = '',
      message: newMessage = '',
      returnType = '',
    } = formPort.value

    getNewResponse(response)
    const obj: any = {
      moduleName: moduleValue.value,
      interfaceName,
      httpMethodName,
      params,
      response,
      fullPath,
      interfaceDesc: interfaceDesc ? interfaceDesc : formPort.value.message,
      message: newMessage,
      returnType,
    }
    if (oldInterfaceName.value) {
      obj.oldInterfaceName = oldInterfaceName.value
    }
    await addInterface(obj)
    message.success('操作成功')
    getMoudles()
    cancelHandle()
    openPort.value = false
    spinning.value = false
  } catch (error) {
    console.log('新增接口错误：' + error)
    spinning.value = false
  }
}

async function generationGptHandle() {
  try {
    await generationGpt({ moduleName: moduleValue.value })
    message.success('生成代码成功')
  } catch (error) {
    console.log('生成接口错误：' + error)
  }
}

function getMessageMock(msg: string) {
  const str = `
  Typescript编程环境，基于如下代码，生成接口返回 mock数据，要求只返回 JSON 代码不需要解释文字 
    ${msg}
  `
  return str
}

onMounted(() => {
  getMoudles()
})
</script>
<style scoped lang="less">
.gpt-manage {
  .add-box {
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
  }
  .mg-right10 {
    margin-right: 10px;
  }
}
.generate {
  margin-top: 10px;
  text-align: right;
}
:deep(.ant-table-cell) {
  vertical-align: top !important;
}
.mg-left10 {
  margin-left: 10px;
}
</style>
