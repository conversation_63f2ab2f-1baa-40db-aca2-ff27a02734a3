<template>
  <div class="database-table">
    <a-table
      :columns="columns"
      :data-source="tableList"
      :pagination="{ pageSize: 10 }"
      :loading="loadingRef"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'operate'">
          <a-space class="action-container">
            <a @click.prevent="toDetail(record)">详情</a>
          </a-space>
        </template>
      </template>
    </a-table>
  </div>
  <a-modal v-model:open="modalOpenRef" title="字段信息" width="800px" destroyOnClose>
    <ColumnInfo :table-data="tableDataRef" />
  </a-modal>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import ColumnInfo from './column-info.vue'
import type { TableColumnsType } from 'ant-design-vue'
import type { queryTabledescFun } from '@/api/matedata/metadatamanagement/metadatamanagement'
import { useRouter, useRoute } from 'vue-router'

const router = useRouter()
const route = useRoute()

const props = defineProps<{
  databaseData: Record<string, any>
  tableList: TableData['obj']
}>()

const tableList = computed(() => props.tableList)

const modalOpenRef = ref(false)
const databaseData = computed(() => props.databaseData)

type TableData = Awaited<ReturnType<typeof queryTabledescFun>>['data']

const loadingRef = ref<boolean>(false)

const columns = ref<TableColumnsType>([
  {
    dataIndex: 'TABLE_NAME',
    title: '表名',
    key: 'TABLE_NAME',
  },
  {
    dataIndex: 'DATA_LENGTH',
    title: '数据长度',
    key: 'DATA_LENGTH',
  },
  {
    dataIndex: 'ENGINE',
    title: '表类型',
    key: 'ENGINE',
  },
  {
    dataIndex: 'ROW_FORMAT',
    title: '行格式',
    key: 'ROW_FORMAT',
  },
  {
    dataIndex: 'TABLE_COMMENT',
    title: '注释',
    key: 'TABLE_COMMENT',
  },
  {
    dataIndex: 'operate',
    title: '操作',
    key: 'operate',
    width: 150,
  },
])

const tableDataRef = ref<any>(null)

const toDetail = (record: any) => {
  const dbSchema = databaseData.value.title
  const token = route.query.token as string
  router.push({
    name: 'meta-data-table',
    query: {
      dbName: databaseData.value?.parent?.key || databaseData.value.key,
      id: databaseData.value.datasourceId,
      schema: record.TABLE_SCHEMA,
      dbSchema,
      tabName: record.TABLE_NAME,
      tableComment: record.TABLE_COMMENT,
      parentKey: databaseData.value?.parent ? databaseData.value.key : undefined,
      token,
    },
  })
}
</script>

<style lang="less" scoped>
.database-table {
  height: 100%;
  .header-div {
    display: flex;
    align-items: center;
    height: 48px;
    padding: 0px 12px 0 20px;
    border-bottom: 1px solid #0000001a;
  }
  .content-div {
    padding: 12px 12px 0 12px;
    height: calc(100% - 48px);
  }
}
:deep(.fs-table .search-btn) {
  display: none;
}
:deep(.ant-btn:hover) {
  .clear-icon {
    opacity: 1 !important;
  }
}
</style>
