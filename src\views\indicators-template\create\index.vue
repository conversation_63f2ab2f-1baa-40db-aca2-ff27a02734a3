<template>
  <Container>
    <fc-designer ref="designer" height="100vh" :config="config" @save="handeSave">
      <template #handle>
        <a-button
          type="primary"
          size="small"
          class="mr-10"
          @click="() => router.push('/indicators-template')"
          >返回上一级</a-button
        >
      </template>
    </fc-designer>
    <a-modal v-model:visible="modalVisible" title="保存模板" @ok="handleOk">
      <a-form
        ref="formRef"
        :model="modalForm"
        :rules="rules"
        :label-col="labelCol"
        :wrapper-col="wrapperCol"
      >
        <a-form-item label="模板名称" name="formName">
          <a-input v-model:value="modalForm.formName" placeholder="请输入模板名称" />
        </a-form-item>
        <a-form-item label="业务类型" name="businessType">
          <a-select
            v-model:value="modalForm.businessType"
            :options="businessTypeList"
            placeholder="请选择业务类型"
          ></a-select>
          <a-button type="link" size="small" @click="openBusinessTypeModal">
            管理业务类型
          </a-button>
        </a-form-item>
        <a-form-item label="备注" name="remarks">
          <a-input v-model:value="modalForm.remarks" placeholder="请输入备注"></a-input>
        </a-form-item>
      </a-form>
    </a-modal>
    <templateTypeModal
      ref="templateTypeModalRef"
      @handleOk="getBusinessTypeList"
    ></templateTypeModal>
  </Container>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import {
  addIndexTemplate,
  saveIndexTemplate,
  getSingleIndexFormData,
} from '@/api/indexmanage/indexform/indexform'
import router from '@/router/core'
import { useRoute } from 'vue-router'
import { message } from 'ant-design-vue'
import FcDesigner from '@form-create/designer'
import { Container } from '@fs/fs-components'
import templateTypeModal from '../component/template-type-modal.vue'
import { getIndexbusinessList } from '@/api/indexmanage/bussinesmanage/bussinesmanage'

const fcRule = ref([])
const fcOption = ref({})
const formData = ref({})
const fApi = ref(null)

const route = useRoute()
const designer: any = ref(null)
const rules = ref({
  formName: [
    {
      required: true,
      message: '请输入模板名称',
      trigger: 'blur',
    },
  ],
})
const formRef: any = ref(null)
const config = {
  showJsonPreview: false,
  showLanguage: false,
  showSaveBtn: true,
  hiddenMenu: ['subform', 'aide', 'layout'],
  hiddenItem: [
    'rate',
    'slider',
    'colorPicker',
    'tree',
    'elTransfer',
    'fcEditor',
    'stepForm',
    'fcValue',
    'tableForm',
    'nestedTableForm',
    'infiniteTableForm',
    'elAlert',
    'inputNumber',
    'upload',
    'password',
    'tree',
    'elTreeSelect',
    'textarea',
    'cascader',
    'switch',
  ],
  formOptions: {
    submitBtn: false,
  },
}
const status = ref('add')
const modalVisible = ref(false)
const modalForm = ref({
  formName: '',
  remarks: '',
  businessType: '',
})
const formJson: any = ref('')
const editData: any = ref('')
const labelCol = { span: 5 }
const wrapperCol = { span: 13 }
const templateTypeModalRef = ref<typeof templateTypeModal>()
const businessTypeList = ref([])

const handeSave = (json: any) => {
  getBusinessTypeList()
  // const handeSave = ({ ruleJson, optionsJson }: { ruleJson: any; optionsJson: any }) => {
  modalVisible.value = true
  formJson.value = json
}

const handleOk = () => {
  formRef.value?.validate().then(async () => {
    console.log(formJson.value, 'formJson.value')
    const { formName, remarks, businessType } = modalForm.value
    let handle: any = null
    let params: any = {
      formCode: '1',
      formName,
      compData: JSON.stringify(formJson.value.options),
      fieldsData: JSON.stringify(
        JSON.parse(formJson.value.rule).map((item: any) => {
          return {
            ...item,
            id: item.field,
          }
        }),
      ),
      remarks,
      businessType,
    }
    if (status.value === 'add') {
      handle = addIndexTemplate
    } else {
      handle = saveIndexTemplate
      params.formId = route.query.formId
      params.revision = editData.value.revision
    }
    const res = await handle({
      ...params,
    })
    if (res.code === '000000') {
      modalVisible.value = false
      router.push('/indicators-template')
      message.success('保存成功')
    } else {
      message.error(res.msg)
    }
  })
}

const getDetails = () => {
  getSingleIndexFormData({
    formId: route.query.formId,
  }).then(async (res: any) => {
    if (res.code === '000000') {
      designer.value.setOptions(FcDesigner.formCreate.parseJson(res.data.compData))
      designer.value.setRule(FcDesigner.formCreate.parseJson(res.data.fieldsData))
      modalForm.value.formName = res.data.formName
      modalForm.value.remarks = res.data.remarks
      editData.value = res.data
      // fcRule.value = FcDesigner.formCreate.parseJson(res.data.fieldsData)
      // console.log('🚀 ~ getDetails ~ fcRule.value:', fcRule.value)
      // fcOption.value = FcDesigner.formCreate.parseJson(res.data.compData)
      // console.log('🚀 ~ getDetails ~ fcOption.value:', fcOption.value)
      // formData.value = res.data
    }
  })
}

onMounted(() => {
  try {
    // // 示例：从服务器端获取保存的JSON规则
    // const { data } = await axios.get('/api/getForm');
    // const { ruleJson, optionsJson } = data;
    // // 回显设计的表单
    // designer.value.setOptions(optionsJson);
    // designer.value.setRule(ruleJson);
    if (route.query.formId) {
      status.value = 'edit'
      getDetails()
    }
    console.log(route)
  } catch (error) {
    console.error('加载表单数据失败', error)
  }
})

function openBusinessTypeModal() {
  templateTypeModalRef.value?.showModal(modalForm.value.businessType)
}

function getBusinessTypeList() {
  getIndexbusinessList({}).then((res: any) => {
    businessTypeList.value = res.data.map((item: any) => {
      return {
        label: item.businessName,
        value: item.businessId,
      }
    })
  })
}
</script>
