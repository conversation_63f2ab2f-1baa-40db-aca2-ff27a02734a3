import { VirtualFileSystem } from './virtual-file-system.js'
import { generateTemplateApiFunc } from '../../page-template/generate-api.js'
import { writeFolderToFile, writeMockTestTs } from '../../base-business/virtual-file-system.js'
const virtualCalssData = new VirtualFileSystem()

// 获取gpt模块数据
export async function handleGetGptMoudles() {
  const virtual = await virtualCalssData.readByKey('gpt-api')
  return { code: '000000', data: virtual }
}

// 新增gpt模块数据
export async function handleAddGptMoudles(req) {
  const { moduleName, moduleNameCn } = req.body
  const virtual = await virtualCalssData.readByKey('gpt-api')
  if (!virtual['gpt-api']) {
    virtual['gpt-api'] = {}
  }
  if (virtual['gpt-api'][moduleName]) {
    return { code: '000001', msg: '模块已存在' }
  }
  virtualCalssData.setByKey(
    {
      moduleName,
      moduleNameCn,
    },
    'gpt-api',
    moduleName,
  )
  return { code: '000000', msg: '新增成功' }
}

// 导出一个异步函数 handleAddInterface，用于添加接口信息
export async function handleAddInterface(req) {
  // 从请求体中解构出所需的信息：模块名、接口名、HTTP 方法名、参数、响应、完整路径和接口描述
  const {
    moduleName,
    interfaceName,
    httpMethodName,
    params,
    response,
    fullPath,
    interfaceDesc,
    oldInterfaceName = '',
    returnType = '',
  } = req.body
  const virtual = await virtualCalssData.readByKey('gpt-api', moduleName)
  // 检查虚拟数据中是否存在指定的模块，如果不存在则返回错误信息
  if (!virtual) {
    return { code: '000001', msg: moduleName + '模块不存在' }
  }
  // 将模块对应的 JSON 字符串解析为对象
  const obj = JSON.parse(virtual)
  // 如果对象中没有 interfaceInfos 属性，则将其初始化为一个空数组
  if (!obj.interfaceInfos) obj.interfaceInfos = []
  const interfaceData = {
    interfaceName,
    httpMethodName,
    params,
    response,
    fullPath,
    interfaceNameCn: interfaceDesc,
    returnType,
  }
  if (oldInterfaceName) {
    replaceObjectByName(obj.interfaceInfos, oldInterfaceName, interfaceData)
  } else {
    const index = obj.interfaceInfos.findIndex((item) => item.interfaceName === interfaceName)
    // 有相同的interfaceName 则自动添加序号
    if (index !== -1) {
      interfaceData.interfaceName = interfaceName + obj.interfaceInfos.length
    }
    // 将新的接口信息添加到 interfaceInfos 数组中
    obj.interfaceInfos.push(interfaceData)
  }

  // 将更新后的对象重新转换为 JSON 字符串并存储到虚拟数据中
  virtualCalssData.setByKey(obj, 'gpt-api', moduleName)
  // 返回成功信息
  return { code: '000000', msg: '新增成功' }
}

export async function handleDelGenerationGpt(req) {
  // 从请求体中解构出所需的信息：模块名、接口名、HTTP 方法名、参数、响应、完整路径和接口描述
  const { moduleName, interfaceName } = req.body
  const virtual = await virtualCalssData.readByKey('gpt-api', moduleName)
  // 检查虚拟数据中是否存在指定的模块，如果不存在则返回错误信息
  if (!virtual) {
    return { code: '000001', msg: moduleName + '模块不存在' }
  }
  const obj = JSON.parse(virtual)
  const index = obj.interfaceInfos.findIndex((item) => item.interfaceName === interfaceName)
  delete obj.interfaceInfos.splice(index, 1)
  if (obj['mock-data']) {
    delete obj['mock-data'][interfaceName]
  }
  await virtualCalssData.setByKey(obj, 'gpt-api', moduleName)
  // 将更新后的虚拟文件系统写入 JSON 文件
  return { code: '000000', msg: '删除成功' }
}

// 定义一个函数 replaceObjectByName，用于在数组中根据名称替换对象
// @param {Array} array - 输入的数组，其中元素可能包含 interfaceName 属性
// @param {String} oldInterfaceName - 要被替换的对象的名称
// @param {Object} newObject - 用于替换的新对象
function replaceObjectByName(array, oldInterfaceName, newObject) {
  // 使用 findIndex 方法查找数组中 interfaceName 属性等于 oldInterfaceName 的元素的索引
  const index = array.findIndex((item) => item.interfaceName === oldInterfaceName)
  // 如果找到了该元素（索引不为 -1），将该元素替换为新对象
  if (index !== -1) {
    array[index] = newObject // 替换原对象
  }
}

// 导出一个异步函数 handleGenerationGpt，用于处理 GPT 相关的生成操作
// @param {Object} req - 请求对象，包含请求体等信息
export async function handleGenerationGpt(req) {
  // 从请求体中解构出模块名
  const { moduleName } = req.body
  // 初始化一个对象，包含 'gpt-api' 属性，初始值为空对象
  let obj = {
    'gpt-api': {},
  }
  const virtual = await virtualCalssData.readByKey('gpt-api', moduleName)
  // 如果模块名为 'all'，将虚拟数据中 'gpt-api' 下的相应模块数据赋给 obj

  // 否则，将虚拟数据中 'gpt-api' 下的相应模块数据赋给 obj 的 'gpt-api' 属性下的相应模块
  obj['gpt-api'][moduleName] = virtual

  // 将过滤出来的数据写入文件夹
  writeFolderToFile(obj, 'api', {}, 'gpt')
  // 返回操作成功的信息
  return { code: '000000', msg: '生成成功' }
}

export async function handleGetApiTemplate(req) {
  const { moduleName, interfaceName } = req.body
  const virtual = await virtualCalssData.readByKey('gpt-api', moduleName)
  const obj = JSON.parse(virtual)
  const index = obj.interfaceInfos.findIndex((item) => item.interfaceName === interfaceName)
  const item = obj.interfaceInfos[index]
  const str = generateTemplateApiFunc(item, item.params, item.response)
  return { code: '000000', data: str }
}

export async function handleSaveApiMock(req) {
  try {
    const { moduleName, interfaceName, content } = req.body
    const gptVirtual = await virtualCalssData.readByKey()
    const obj = JSON.parse(gptVirtual['gpt-api'][moduleName])
    if (!obj['mock-data']) obj['mock-data'] = {}
    obj['mock-data'][interfaceName] = content
    gptVirtual['gpt-api'][moduleName] = JSON.stringify(obj)
    // 生成mock数据
    const data = await writeMockTestTs(gptVirtual, 'gpt-api', moduleName)
    if (!data.state) {
      return { code: '000001', data: data?.message }
    }
    // 将更新后的虚拟文件系统写入 JSON 文件
    // writeVirtualJson(virtualData)
    await virtualCalssData.setByKey(gptVirtual, 'gpt-api', moduleName)
    return { code: '000000', data: '生成mock数据成功' }
  } catch (error) {
    console.log('%c 🍈 error: ', 'font-size:12px;background-color: #EA7E5C;color:#fff;', error)
  }
}
