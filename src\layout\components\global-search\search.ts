import axiosNoBaseUrl from '@/api'
import { useUserStore } from '@/stores/user'

/**
 * 基础响应类型
 * code 为 000000 表示成功
 */
export interface ServiceResponse<TData> {
  code: string
  data: TData
  error?: { [key: string]: any }
  msg?: string
}

/**
 * 基础响应列表类型
 */
export interface ServiceResponseList<TData> {
  code: string
  data: {
    totalRecords: number
    pageIndex: number
    records: TData[]
    totalPages: number
    pageSize: number
  }
  error?: { [key: string]: any }
  msg?: string
}

export function parseTableName(item: AssetDataItem): string {
  const { databaseName, schemaName, tableName } = item
  const pathName = `${databaseName} > ${tableName}`
  if (schemaName !== databaseName) {
    return `${schemaName} > ${pathName}`
  }
  return pathName
}

/**
 * 节点类型枚举
 */
export const nodeTypeEnum = {
  DIRECTORY: 'directory',
  DATABASE: 'database',
  TABLE: 'table',
  COLUMN: 'column',
} as const
/**
 * 节点类型
 */
export type NodeType = (typeof nodeTypeEnum)[keyof typeof nodeTypeEnum]
/**
 * 搜索类型枚举
 */
export enum SearchType {
  /** 全部 */
  ALL = 'all',
  /** 元数据库 */
  DATABASE = 'database',
  /** 元数据表 */
  TABLE = 'table',
  /** 数据资产 */
  ASSET = 'asset',
  /** 数据资产表 */
  ASSET_TABLE = 'assetTable',
  /** 资产数据 */
  ASSETDATA = 'assetData',
  /** 指标 */
  METRIC = 'metric',
  /** API 列表 */
  API_LIST = 'apiList',
}

/**
 * 搜索类型标签映射
 */
export const SearchTypeLabels: Record<string, string> = {
  [SearchType.ASSETDATA]: '资产数据',
  [SearchType.DATABASE]: '元数据库',
  [SearchType.TABLE]: '元数据表',
  [SearchType.ASSET]: '资产库',
  [SearchType.ASSET_TABLE]: '资产表',
  [SearchType.METRIC]: '指标',
  [SearchType.API_LIST]: 'API 列表',
}

/**
 * 搜索类型选项
 */
export interface SearchTypeOption {
  value: SearchType
  label: string
}

/**
 * 指标信息
 */
export type MetricInfo = {
  autoId: string
  groupId: string
  parentId: string
  formDataId: string
  dataSourceId: number
  indexName: string
  calFormula: string
  remarks: string
  status: number
  revision: number
  isDeleted: number
  isRelease: number
  currentVersion: number
  tenantId: string
  createUser: string
  createUsername: string
  createTime: string
  updateUser: string
  updateUsername: string
  updateTime: string
}

/**
 * 指标列表响应
 */
export type MetricListResponse = ServiceResponseList<MetricInfo>
/**
 * 指标列表查询
 * @param data 请求参数
 * @param headers 请求头
 * @returns 指标列表
 */
export async function queryIndexDataList(data: {
  indexName?: string
  pageIndex?: number
  pageSize?: number
}): Promise<MetricListResponse> {
  const userStore = useUserStore()
  const { userInfo } = userStore
  const name = userInfo.userName
  const userId = userInfo.userId
  const params = {
    indexName: data.indexName,
    pageIndex: data.pageIndex || 1,
    pageSize: data.pageSize || 10,
  }
  return axiosNoBaseUrl({
    url: '/indexManage/indexManage/getIndexDataListByPages',
    method: 'get',
    params,
    headers: {
      opUser: userId,
      opUserName: name,
    },
  })
}

/**
 * 元数据库信息
 */
export type DatabaseInfo = {
  dbType: string
  ip: string
  port: string
  tableCount: number
  tableSchema: string
}

/**
 * 查询元数据库列表
 * @param params 请求参数
 * @returns 数据库列表
 */
export async function queryDatabaseList(params: { databaseName: string }) {
  const userStore = useUserStore()
  const { userInfo } = userStore
  const name = userInfo.userName
  const userId = userInfo.userId
  return axiosNoBaseUrl({
    url: `/assetManager/metadataManage/getDbListBySourceId`,
    method: 'get',
    params,
    headers: {
      opUser: userId,
      opUserName: name,
    },
  })
}
/** 资产数据的表数据 */
export type AssetTableData = {
  key: string
  name: string
  comment: string
  children?: AssetTableData[]
  nodeType: NodeType
  checked?: boolean
  [key: string]: any
}

/**
 * 树形数据
 */
export class TreeData {
  key: string
  title: string
  isLeaf?: boolean
  disabled?: boolean
  selectable?: boolean
  checkable?: boolean
  disableCheckbox?: boolean;
  [key: string]: any
  constructor(data: any) {
    this.key = data.key
    this.title = data.title
    this.isLeaf = data.isLeaf
    this.disabled = data.disabled
    this.selectable = data.selectable
    this.checkable = data.checkable
    this.disableCheckbox = data.disableCheckbox
  }
}
/**
 * 资产树形数据 222
 */
export class AssetTreeData extends TreeData {
  nodeType?: NodeType
  /** 目录或连接 id */
  directoryId?: string
  /** 目录或连接名称 */
  directoryName?: string
  /** 数据库 id */
  databaseId?: string
  /** 数据库名称 */
  databaseName?: string
  /** 模式 id */
  schemaId?: string
  /** 模式名称 */
  schemaName?: string
  /** 表 id */
  tableId?: string
  /** 表名称 */
  tableName?: string
  /** 数据库类型 */
  databaseType?: string
  /** 资产数量 */
  assetCount?: number
  /** 表数量 */
  tableCount?: number
  parentId?: number
  /** 表数据 */
  tableData?: AssetTableData[]
  children?: AssetTreeData[]

  constructor(data: any) {
    super(data)
    this.nodeType = data.nodeType
    this.directoryId = data.directoryId
    this.directoryName = data.directoryName
    this.databaseId = data.databaseId
    this.databaseName = data.databaseName
    this.schemaId = data.schemaId
    this.schemaName = data.schemaName
    this.tableId = data.tableId
    this.tableName = data.tableName
    this.databaseType = data.databaseType
    this.assetCount = data.assetCount
    this.tableCount = data.tableCount
    this.parentId = data.parentId
    this.tableData = data.tableData
    this.children = data.children
  }
}

/**
 * 数据资产目录信息
 */
export type DataAssetsDirectoryInfo = {
  /** 资产数量 */
  assertCount: number
  /** 描述 */
  description?: string
  /** 目录ID */
  id: number
  /** 目录名称 */
  name: string
  /** 父目录ID */
  parentId?: number
  /** 子目录 */
  children?: DataAssetsDirectoryInfo[]
}
/**
 * 获取数据资产目录列表
 * @returns 数据资产目录列表
 */
export async function getListDataAssetsDirectory() {
  const userStore = useUserStore()
  const { userInfo } = userStore
  const name = userInfo.userName
  const userId = userInfo.userId
  const res = await axiosNoBaseUrl({
    url: `/assetManager/catalogAdmin/getListDataAssetsDirectory`,
    method: 'get',
    headers: {
      opUser: userId,
      opUserName: name,
    },
  })
  if (res.code === '000000') {
    const recur = (data: DataAssetsDirectoryInfo[]): AssetTreeData[] => {
      return data.map(
        (item) =>
          new AssetTreeData({
            key: String(item.id),
            title: item.name,
            nodeType: nodeTypeEnum.DIRECTORY,
            directoryId: String(item.id),
            directoryName: item.name,
            parentId: item.parentId,
            assetCount: item.assertCount,
            checkable: false,
            children: item.children ? recur(item.children) : [],
          }),
      )
    }
    return recur(res.data)
  }
  return []
}
/**
 * 数据资产信息
 */
export type DataAssetInfo = {
  /** 数据库类型，如 mysql、oracle、postgresql 等，全小写 */
  databaseType: string
  /** 数据库名称 */
  databaseName: string
  /** 引擎 */
  engine?: string
  /** 端口 */
  port: string
  /** 表数量 */
  tableCount: number
  /** IP */
  ip: string
  /** 描述 */
  description?: string
  /** 目录ID */
  directoryId: number
  /** 数据库ID */
  databaseId: string
}
/**
 * 获取数据资产列表
 * @param params 请求参数
 * @param params.directoryId 目录ID
 * @returns 数据资产列表
 */
export async function getDataAssetList(params: { directoryId: number }) {
  const userStore = useUserStore()
  const { userInfo } = userStore
  const name = userInfo.userName
  const userId = userInfo.userId
  return axiosNoBaseUrl({
    url: `/assetManager/dataAssertManage/getDataAssetList`,
    method: 'get',
    params,
    headers: {
      opUser: userId,
      opUserName: name,
    },
  })
}
/**
 * 一次性获取数据资产目录和数据资产列表，然后合并成树形数据
 * @returns 树形数据
 */
export async function getTreeData() {
  // 获取一级目录
  const directoryRes = await getListDataAssetsDirectory()
  if (!Array.isArray(directoryRes)) return []

  // 只处理一级目录（无递归）
  const result: AssetTreeData[] = []

  // 并发获取所有一级目录下的资产列表
  const assetPromises = directoryRes.map(async (dir) => {
    try {
      const assetRes = await getDataAssetList({
        directoryId: Number(dir.directoryId),
      })
      if (assetRes && assetRes.code === '000000' && Array.isArray(assetRes.data)) {
        const assets = assetRes.data.map(
          (asset: any) =>
            new AssetTreeData({
              key: asset.databaseId,
              title: asset.databaseName,
              nodeType: nodeTypeEnum.DATABASE,
              databaseId: asset.databaseId,
              databaseName: asset.databaseName,
              directoryId: String(asset.directoryId),
              databaseType: asset.databaseType,
              tableCount: asset.tableCount,
              parentId: dir.id,
              disableCheckbox: true,
              isLeaf: true,
            }),
        )
        dir.children = [...(dir.children || []), ...assets]
      }
      return dir
    } catch (e) {
      // ignore error, return dir without assets
      return dir
    }
  })

  // 等待所有资产请求完成
  const results = await Promise.all(assetPromises)
  result.push(...results)

  return result
}

export type ColumnInfo = {
  columnLength: number
  columnDefault: string
  columnId: string
  dataType: string
  columnComment: string
  description: string
  updateUser: string
  isPrimaryKey: string
  updateTime: string
  ordinalPosition: string
  columnType: string
  createTime: string
  isNullable: string
  tableId: string
  createUser: string
  columnName: string
}
export type AssetTableDetailInfo = {
  tableId: string
  tableName: string
  tableComment: string
  columnsList: ColumnInfo[]
}
/**
 * 查询数据资产表详情
 * @param params 请求参数
 * @returns 数据资产表详情
 */
export async function getDataAssetTableDetail(params: { tableId: string }) {
  const userStore = useUserStore()
  const { userInfo } = userStore
  const name = userInfo.userName
  const userId = userInfo.userId
  return axiosNoBaseUrl({
    url: `/assetManager/dataAssertManage/getDataAssetTableDetail`,
    method: 'get',
    params,
    headers: {
      opUser: userId,
      opUserName: name,
    },
  })
}

export type TableInfo = {
  customComment: string
  engine: string
  inuse: string
  ip: string
  port: string
  tableComment: string
  tableName: string
  datasourceId: number
  databaseName: string
}

/**
 * 查询元数据表列表
 * @param params 请求参数
 * @returns 数据表列表
 */
export async function queryTableList(params: {
  tableName: string
  pageIndex?: number
  pageSize?: number
}) {
  params.pageIndex = params.pageIndex || 1
  params.pageSize = params.pageSize || 10
  const userStore = useUserStore()
  const { userInfo } = userStore
  const name = userInfo.userName
  const userId = userInfo.userId
  return axiosNoBaseUrl({
    url: `/assetManager/metadataManage/getTableListByDb`,
    method: 'get',
    params,
    headers: {
      opUser: userId,
      opUserName: name,
    },
  })
}

/**
 * 数据资产信息
 */
export type AssetInfo = {
  databaseId: string
  databaseName: string
  databaseType: string
  description: string | null
  directoryId: number
  engine: string
  tableCount: number
}

/**
 * 资产数据 ES 搜索
 */
export type AssetDataItem = {
  databaseName: string // 数据库名称
  databaseId: string // 数据库ID
  tableId: string // 表ID
  tableName: string // 表名称
  schemaName: string // 模式名称
  matchColumns: {
    name: string // 匹配的列名
    value: string // 匹配的列值
  }[]
}

/**
 * 查询数据资产库列表
 * @param params 请求参数
 * @returns 数据资产库列表
 */
export function queryAssetList(params: { databaseName: string }) {
  const userStore = useUserStore()
  const { userInfo } = userStore
  const name = userInfo.userName
  const userId = userInfo.userId
  return axiosNoBaseUrl({
    url: `/assetManager/dataAssertManage/getDataAssetList`,
    method: 'get',
    params,
    headers: {
      opUser: userId,
      opUserName: encodeURIComponent(name),
    },
  })
}
/**
 * 数据资产表信息
 */
export type AssetTableInfo = {
  createTime: string
  createUser: string
  databaseId: string
  databaseName?: string
  datasourceId: number
  description?: string
  directoryId: string
  labelId?: string
  labelName?: string
  status: number
  tableCollation?: string
  tableComment?: string
  tableId: string
  tableName: string
  tableType: string
  updateTime?: string
  updateUser?: string
  columnList?: ColumnInfo[]
  tableSchema: string // 表模式
}
/**
 * 查询数据资产 表 列表
 * @param params 请求参数
 * @returns 数据资产表列表
 */
export async function queryAssetTableInfoList(
  params: {
    tableName?: string
    databaseId?: string
    pageIndex?: number
    pageSize?: number
  },
  loadDetail?: boolean,
) {
  params.pageIndex = params.pageIndex || 1
  params.pageSize = params.pageSize || 10
  const userStore = useUserStore()
  const { userInfo } = userStore
  const userName = userInfo.userName
  const userId = userInfo.userId
  const res = await axiosNoBaseUrl({
    url: `/assetManager/dataAssertManage/getDataAssetTableList`,
    method: 'get',
    params,
    headers: {
      opUser: userId,
      opUserName: encodeURIComponent(userName),
    },
  })
  if (!loadDetail) {
    // 如果不加载详情，直接返回
    return res
  } else {
    // 如果加载详情，遍历records，根据tableId查询详情
    const detailRes = await Promise.all(
      res.data.records.map((item: { tableId: any }) =>
        getDataAssetTableDetail({
          tableId: item.tableId,
        }),
      ),
    )
    if (detailRes && detailRes.every((item) => item.code === '000000')) {
      res.data.records.forEach((item: { columnList: any }, index: number) => {
        const detail = detailRes[index].data
        if (detail) item.columnList = detail.columnsList
      })
    }
    return res
  }
}

export type ApiInfo = {
  apiCode: string
  apiName: string
  apiStatus: number
  approvalStatus: number
  gmtCrete: string
  gmtModified: string
  groupName: string
  isUsed: number
  requestMethod: string
  requestMethodList: number[]
  requestMethodNameList: string
  requestPath: string
  versionNo: string
}

/**
 * 查询API列表
 * @param params 请求参数
 * @returns API列表
 */
export function queryApiList(params: { apiName: string; pageIndex?: number; pageSize?: number }) {
  params.pageIndex = params.pageIndex || 1
  params.pageSize = params.pageSize || 10
  const userStore = useUserStore()
  const { userInfo } = userStore
  const userName = userInfo.userName
  const userId = userInfo.userId
  return axiosNoBaseUrl({
    url: `/uipAdmin/apiManage`,
    method: 'get',
    params,
    headers: {
      opUser: userId,
      opUserName: encodeURIComponent(userName),
    },
  })
}

/**
 * 获取搜索类型选项列表
 */
export function getSearchTypeOptions(): SearchTypeOption[] {
  return Object.values(SearchType)
    .filter((type) => type !== SearchType.ALL)
    .map((type: SearchType) => ({
      value: type,
      label: SearchTypeLabels[type],
    }))
}

/**
 * 搜索结果项接口
 */
export interface SearchResultItem {
  id: string
  name: string
  nameList?: { name: string; value: string }[] // 仅用于资产数据
  type: SearchType
  description?: string
  owner?: string
  matchCount?: number
  dbType?: string
  [key: string]: any
}

/**
 * 搜索请求参数
 */
export interface SearchParams {
  keyword: string
  searchType: SearchType
  page?: number
  pageSize?: number
  esParams?: Partial<getEsDataAssetListParams>
}

/**
 * 搜索响应结果
 */
export interface SearchResponse {
  total: number
  list: SearchResultItem[]
}

/**
 * 分类搜索结果类型
 */
export interface CategorySearchResult {
  total: number
  list: SearchResultItem[]
  loading: boolean
  error?: boolean
}

/**
 * 全部搜索响应结果 - 包含每个类型的详细信息
 */
export interface AllSearchResponse {
  totalResults: number // 所有类型的总结果数
  categoryResults: Map<SearchType, CategorySearchResult>
  allResults: SearchResultItem[] // 所有结果的合并列表
}

/**
 * 通过搜索类型获取落地页URL
 */
export function getSearchResultUrl(searchType: SearchType, options?: Record<string, any>): string {
  let path = ''
  let url = ''
  switch (searchType) {
    case SearchType.DATABASE:
      // 元数据库落地页
      url = `/metadata-query`
      if (options && options.dbName) {
        url += `?dbName=${options.dbName}`
      }
      path = url
      return path
    case SearchType.TABLE:
      // 元数据表落地页
      if (options && options.datasourceId && options.databaseName && options.tableName) {
        url += `?datasourceId=${options.datasourceId}&databaseName=${options.databaseName}&tableName=${options.tableName}`
      }
      path = `/metadata-detail${url}`
      return path
    case SearchType.ASSET:
      // 数据资产落地页
      url = `/data-asset-management`
      if (options && options.dbName) {
        url += `?dbName=${options.dbName}`
      }
      path += url
      return path
    case SearchType.METRIC:
      // 指标落地页
      url = `/indicator-list/edit`
      // url = `http://172.16.101.102:5163/indicator-list/edit`
      if (options && options.id) {
        url += `?id=${options.id}&search=${options.name}`
      }
      path += url
      return path
    case SearchType.ASSET_TABLE:
      // 数据资产表落地页
      url = `/table-detail`
      if (options && options.tableId) {
        url += `?tableId=${options.tableId}`
        if (options.tableName) {
          url += `&tableName=${options.tableName}`
        }
        if (options.tableComment) {
          url += `&tableComment=${options.tableComment}`
        }
      }
      path += url
      return path
    case SearchType.API_LIST:
      // API 列表落地页
      path = `/apiconfig/api/apiList/apiList`
      if (options && options.apiName) {
        path += `?apiName=${options.apiName}`
      }
      return path
    case SearchType.ASSETDATA:
      // API 资产数据

      url = `/table-detail`
      if (options && options.id) {
        url += `?tableId=${options.id}&tableName=${options.name}`
      }
      path = url
      return path
    default:
      return ''
  }
}

function transformSearchResult(
  res: ServiceResponse<any>,
  searchType: SearchType,
): SearchResultItem[] {
  if (!(res && res.code === '000000')) return []
  switch (searchType) {
    case SearchType.DATABASE:
      return res.data.map((item: DatabaseInfo) => ({
        id: item.tableSchema,
        name: item.tableSchema,
        type: SearchType.DATABASE,
        description: '无描述',
        owner: null,
        matchCount: 1,
        dbType: item.dbType,
      }))
    case SearchType.TABLE:
      return res.data.records.map((item: TableInfo) => ({
        id: `${item.datasourceId}-${item.databaseName}-${item.tableName}`,
        name: item.tableName,
        type: SearchType.TABLE,
        description: item.tableComment,
        owner: null,
        matchCount: 1,
        datasourceId: item.datasourceId,
        databaseName: item.databaseName,
      }))
    case SearchType.ASSET:
      return res.data.map((item: AssetInfo) => ({
        id: item.databaseId,
        name: item.databaseName,
        type: SearchType.ASSET,
        description: item.description || '无描述',
        owner: null,
        matchCount: 1,
      }))
    case SearchType.ASSETDATA:
      return res.data.records.map((item: AssetDataItem) => ({
        id: item.tableId,
        name: parseTableName(item),
        nameList: item.matchColumns,
        type: SearchType.ASSETDATA,
        description: null,
        owner: null,
        matchCount: item.matchColumns.length,
      }))
    case SearchType.ASSET_TABLE:
      return res.data.records.map((item: AssetTableInfo) => ({
        id: item.tableId,
        name: item.tableName,
        type: SearchType.ASSET_TABLE,
        description: item.tableComment || '无描述',
        owner: null,
        matchCount: 1,
        tableId: item.tableId,
        tableName: item.tableName,
        tableComment: item.tableComment,
      }))
    case SearchType.METRIC:
      return res.data.records.map((item: MetricInfo) => ({
        id: item.autoId,
        name: item.indexName,
        type: SearchType.METRIC,
        description: item.remarks,
        owner: item.createUsername,
        matchCount: 1,
      }))
    case SearchType.API_LIST:
      return res.data.records.map((item: ApiInfo) => ({
        id: item.apiCode,
        name: item.apiName,
        type: SearchType.API_LIST,
        description: '无描述',
        owner: null,
        matchCount: 1,
      }))
    default:
      return []
  }
}

/**
 * 统一的搜索API函数
 */
export async function performSearch(
  params: SearchParams,
  options?: {
    useMock?: boolean
    onCategoryResult?: (type: SearchType, result: CategorySearchResult) => void
  },
): Promise<SearchResponse | AllSearchResponse> {
  let total = 0
  let results: SearchResultItem[] = []

  try {
    if (options && options.useMock) {
      // 模拟网络延迟
      await new Promise((resolve) => setTimeout(resolve, 200 + Math.random() * 300))
      // 生成模拟搜索结果
      results = await mockSearchAPI(params.keyword, params.searchType)
      total = results.length
      return { total, list: results }
    } else {
      switch (params.searchType) {
        case SearchType.DATABASE: {
          const res1 = await queryDatabaseList({
            databaseName: params.keyword,
          })
          if (res1 && res1.code === '000000') {
            results = transformSearchResult(res1, SearchType.DATABASE)
            total = res1.data.length
            return { total, list: results }
          } else {
            return { total, list: results }
          }
        }
        case SearchType.TABLE: {
          const res2 = await queryTableList({
            tableName: params.keyword,
            pageIndex: params.page || 1,
            pageSize: params.pageSize || 20,
          })
          if (res2 && res2.code === '000000') {
            results = transformSearchResult(res2, SearchType.TABLE)
            total = res2.data.totalRecords
            return { total, list: results }
          } else {
            return { total, list: results }
          }
        }
        case SearchType.ASSET: {
          const res3 = await queryAssetList({
            databaseName: params.keyword,
          })
          if (res3 && res3.code === '000000') {
            results = transformSearchResult(res3, SearchType.ASSET)
            total = res3.data.length
            return { total, list: results }
          } else {
            return { total, list: results }
          }
        }

        case SearchType.ASSET_TABLE: {
          const res4 = await queryAssetTableInfoList({
            tableName: params.keyword,
            pageIndex: params.page || 1,
            pageSize: params.pageSize || 20,
          })
          if (res4 && res4.code === '000000') {
            results = transformSearchResult(res4, SearchType.ASSET_TABLE)
            total = res4.data.totalRecords
            return { total, list: results }
          } else {
            return { total, list: results }
          }
        }
        case SearchType.METRIC:
          try {
            const res5 = await queryIndexDataList({
              indexName: params.keyword,
              pageIndex: params.page || 1,
              pageSize: params.pageSize || 20,
            })
            if (res5 && res5.code === '000000' && res5.data && res5.data.records) {
              results = transformSearchResult(res5, SearchType.METRIC)
              total = res5.data.totalRecords
              return { total, list: results }
            } else {
              return { total, list: results }
            }
          } catch (apiError) {
            console.error('指标搜索API调用失败:', apiError)
            // API调用失败时，使用空结果数组
            return { total, list: results }
          }
        case SearchType.API_LIST: {
          const res6 = await queryApiList({
            apiName: params.keyword,
            pageIndex: params.page || 1,
            pageSize: params.pageSize || 20,
          })
          if (res6 && res6.code === '000000') {
            results = transformSearchResult(res6, SearchType.API_LIST)
            total = res6.data.totalRecords
            return { total, list: results }
          } else {
            return { total, list: results }
          }
        }
        case SearchType.ASSETDATA: {
          // TODO: 查询资产数据
          const params4CurrentRequest = {
            pageIndex: params.page || 1,
            pageSize: params.pageSize || 20,
            queryWords: params.keyword || '',
          }
          if (params.esParams) {
            Object.assign(params4CurrentRequest, params.esParams)
          }
          const res7 = await getEsDataAssetList(params4CurrentRequest)

          if (res7 && res7.code === '000000') {
            results = transformSearchResult(res7, SearchType.ASSETDATA)
            total = Array.isArray(res7.data) ? res7.data.length : 0
            return { total, list: results }
          } else {
            return { total, list: results }
          }
        }

        default: {
          // 搜索全部类型
          const p1 = queryDatabaseList({ databaseName: params.keyword })
          const p2 = queryTableList({
            tableName: params.keyword,
            pageIndex: 1,
            pageSize: params.pageSize, // 每个类型获取更多数据用于展示
          })
          const p3 = queryAssetList({ databaseName: params.keyword })
          const p4 = queryAssetTableInfoList({
            tableName: params.keyword,
            pageIndex: 1,
            pageSize: params.pageSize,
          })
          const p5 = queryIndexDataList({
            indexName: params.keyword,
            pageIndex: 1,
            pageSize: params.pageSize,
          })
          // const p6 = queryApiList({
          //   apiName: params.keyword,
          //   pageIndex: 1,
          //   pageSize: params.pageSize
          // })
          const p6 = () =>
            new Promise((resolve, reject) => {
              setTimeout(() => {
                // eslint-disable-next-line prefer-promise-reject-errors
                reject({
                  code: '000001',
                  data: { totalRecords: 0, records: [] },
                })
              }, 1000)
            })

          const p7 = async () => {
            const params4CurrentRequest = {
              pageIndex: params.page || 1,
              pageSize: params.pageSize || 20,
              queryWords: params.keyword || '',
            }
            if (params.esParams) {
              Object.assign(params4CurrentRequest, params.esParams)
            }
            return getEsDataAssetList(params4CurrentRequest)
          }

          // 手动实现 Promise.allSettled 的功能
          const settlePromise = (promise: Promise<any>) => {
            return promise
              .then((value) => ({ status: 'fulfilled', value }))
              .catch((reason) => ({ status: 'rejected', reason }))
          }

          // 并发请求，谁快谁先处理
          const promises = [
            settlePromise(p1),
            settlePromise(p2),
            settlePromise(p3),
            settlePromise(p4),
            settlePromise(p5),
            settlePromise(p6()),
            settlePromise(p7()),
          ]

          const categoryResults = new Map<SearchType, CategorySearchResult>()
          let totalResults = 0
          const allResults: SearchResultItem[] = []

          // 定义类型与处理函数映射
          const typeMap = [
            SearchType.DATABASE,
            SearchType.TABLE,
            SearchType.ASSET,
            SearchType.ASSET_TABLE,
            SearchType.METRIC,
            SearchType.API_LIST,
            SearchType.ASSETDATA,
          ]

          promises.forEach((promise, idx) => {
            promise.then((res) => {
              if (
                res.status === 'fulfilled' &&
                'value' in res &&
                res.value &&
                res.value.code === '000000'
              ) {
                let resultList: SearchResultItem[] = []
                let total = 0
                switch (typeMap[idx]) {
                  case SearchType.DATABASE:
                    resultList = transformSearchResult(res.value, SearchType.DATABASE)
                    total = res.value.data.length
                    if (options && options.onCategoryResult) {
                      options.onCategoryResult(SearchType.DATABASE, {
                        list: resultList,
                        total,
                        loading: false,
                      })
                    }
                    break
                  case SearchType.TABLE:
                    resultList = transformSearchResult(res.value, SearchType.TABLE)
                    total = res.value.data.totalRecords
                    if (options && options.onCategoryResult) {
                      options.onCategoryResult(SearchType.TABLE, {
                        list: resultList,
                        total,
                        loading: false,
                      })
                    }
                    break
                  case SearchType.ASSET:
                    resultList = transformSearchResult(res.value, SearchType.ASSET)
                    total = res.value.data.length
                    if (options && options.onCategoryResult) {
                      options.onCategoryResult(SearchType.ASSET, {
                        list: resultList,
                        total,
                        loading: false,
                      })
                    }
                    break
                  case SearchType.ASSET_TABLE:
                    resultList = transformSearchResult(res.value, SearchType.ASSET_TABLE)
                    total = res.value.data.totalRecords
                    if (options && options.onCategoryResult) {
                      options.onCategoryResult(SearchType.ASSET_TABLE, {
                        list: resultList,
                        total,
                        loading: false,
                      })
                    }
                    break
                  case SearchType.METRIC:
                    resultList = transformSearchResult(res.value, SearchType.METRIC)
                    total = res.value.data.totalRecords
                    if (options && options.onCategoryResult) {
                      options.onCategoryResult(SearchType.METRIC, {
                        list: resultList,
                        total,
                        loading: false,
                      })
                    }
                    break
                  case SearchType.API_LIST:
                    resultList = transformSearchResult(res.value, SearchType.API_LIST)
                    total = res.value.data.totalRecords
                    if (options && options.onCategoryResult) {
                      options.onCategoryResult(SearchType.API_LIST, {
                        list: resultList,
                        total,
                        loading: false,
                      })
                    }
                    break
                  case SearchType.ASSETDATA:
                    resultList = transformSearchResult(res.value, SearchType.ASSETDATA)
                    total = Array.isArray(res.value.data)
                      ? res.value.data.length
                      : res.value.data.totalRecords || 0
                    if (options && options.onCategoryResult) {
                      options.onCategoryResult(SearchType.ASSETDATA, {
                        list: resultList,
                        total,
                        loading: false,
                      })
                    }
                    break
                }
                categoryResults.set(typeMap[idx], {
                  total,
                  list: resultList,
                  loading: false,
                })
                totalResults += total
                allResults.push(...resultList)
              } else {
                if (options && options.onCategoryResult) {
                  options.onCategoryResult(typeMap[idx], {
                    list: [],
                    total,
                    loading: false,
                    error: true,
                  })
                }
                categoryResults.set(typeMap[idx], {
                  total: 0,
                  list: [],
                  loading: false,
                  error: true,
                })
              }
            })
          })

          // 等待所有 promise 完成
          await Promise.all(promises)

          return {
            totalResults,
            categoryResults,
            allResults,
          } as AllSearchResponse
        }
      }
    }
  } catch (error) {
    console.error('搜索请求失败:', error)
    throw error
  }
}

/**
 * 搜索预览API - 专门用于搜索框预览，返回较少结果
 */
export async function performPreviewSearch(
  keyword: string,
  searchType: SearchType,
): Promise<SearchResultItem[]> {
  const res = await performSearch({
    keyword,
    searchType,
  })

  // 处理返回值可能是 AllSearchResponse 的情况
  if ('categoryResults' in res) {
    // 如果是全部搜索结果，返回指定类型的结果
    const categoryResult = res.categoryResults.get(searchType)
    return categoryResult ? categoryResult.list : []
  }
  // 如果是单个类型搜索结果
  return res.list
}

export function handleResultClick(item: SearchResultItem, router: any): void {
  switch (item.type) {
    case SearchType.DATABASE: {
      // 跳转到元数据库查询
      const url1 = getSearchResultUrl(SearchType.DATABASE, {
        dbName: item.name,
      })
      router.push(url1)
      break
    }

    case SearchType.TABLE: {
      // 跳转到元数据表详情页
      const url2 = getSearchResultUrl(SearchType.TABLE, {
        datasourceId: item.datasourceId,
        databaseName: item.databaseName,
        tableName: item.name,
      })
      router.push(url2)
      break
    }

    case SearchType.ASSET: {
      // 跳转到数据资产管理
      const url3 = getSearchResultUrl(SearchType.ASSET, {
        dbName: item.name,
      })
      router.push(url3)
      break
    }

    case SearchType.METRIC: {
      // 跳转到指标详情页
      const url = getSearchResultUrl(SearchType.METRIC, {
        id: item.id,
        name: item.name,
      })
      router.push(url)
      break
    }

    case SearchType.ASSET_TABLE: {
      // 跳转到数据资产表详情页
      const url4 = getSearchResultUrl(SearchType.ASSET_TABLE, {
        tableId: item.tableId,
        tableName: item.tableName,
        tableComment: item.tableComment,
      })
      router.push(url4)
      break
    }

    case SearchType.API_LIST: {
      // 跳转到API列表
      const url5 = getSearchResultUrl(SearchType.API_LIST, {
        apiName: item.name,
      })
      router.push(url5)
      break
    }

    case SearchType.ASSETDATA: {
      // 跳转到API列表
      const url6 = getSearchResultUrl(SearchType.ASSETDATA, {
        id: item.id,
        name:
          item.description && item.description.split(' > ')[2]
            ? item.description.split(' > ')[2]
            : item.name,
      })
      router.push(url6)
      break
    }
  }
  // eventBus.emit(EventNames.CLEAR_SEARCH_KEYWORD)
}

/**
 * 模拟搜索API - 用于预览和结果页面
 */
export async function mockSearchAPI(
  keyword: string,
  searchType: SearchType,
): Promise<SearchResultItem[]> {
  const results: SearchResultItem[] = []
  const lowerKeyword = keyword.toLowerCase()

  // 生成更多模拟数据的辅助函数
  const generateMockData = (count: number, type: SearchType, baseName: string) => {
    return Array.from({ length: count }, (_, index) => ({
      id: `${type}_${index + 1}_${Date.now()}`,
      name: `${baseName}_${lowerKeyword}_${index + 1}`,
      type,
      description: `这是一个关于${keyword}的${SearchTypeLabels[type]}，包含相关信息和数据`,
      owner: ['张三', '李四', '王五', '赵六', '钱七'][index % 5],
      matchCount: Math.floor(Math.random() * 5) + 1,
    }))
  }

  // 根据搜索类型生成对应的结果
  if (searchType === SearchType.DATABASE) {
    results.push(...generateMockData(8, SearchType.DATABASE, '数据库'))
  }

  if (searchType === SearchType.TABLE) {
    results.push(...generateMockData(15, SearchType.TABLE, '数据表'))
  }

  if (searchType === SearchType.ASSET) {
    results.push(...generateMockData(12, SearchType.ASSET, '数据资产'))
  }

  if (searchType === SearchType.METRIC) {
    results.push(...generateMockData(6, SearchType.METRIC, '指标'))
  }

  // 如果是预览模式（搜索框预览），只返回前几条结果
  return results.sort(() => Math.random() - 0.5)
}

interface getEsDataAssetListParams {
  databases?: string[]
  tables?: string[]
  schemas?: string[]
  queryWords: string
  pageIndex: number
  pageSize: number
}

/**
 * 获取ES数据资产数据
 * @param params 请求参数
 * @returns 数据库列表
 */
export async function getEsDataAssetList(params: getEsDataAssetListParams) {
  const userStore = useUserStore()
  const { userInfo } = userStore
  const name = userInfo.userName
  const userId = userInfo.userId
  return axiosNoBaseUrl({
    url: `/api/sodataFlink/esDataSyncTask/getEsDataAssetList`,
    method: 'post',
    data: params,
    headers: {
      opUser: userId,
      opUserName: name,
    },
  })
}
