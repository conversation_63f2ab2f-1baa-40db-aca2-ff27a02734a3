<template>
  <div class="directory-tree">
    <div class="title">
      <span>数据资产目录</span>
      <span>
        <a-tooltip placement="top">
          <template #title>
            <span>刷新</span>
          </template>
          <RedoOutlined style="font-size: 19px; margin-right: 10px" @click="refresh" />
        </a-tooltip>
        <a-tooltip placement="top">
          <template #title>
            <span>添加数据资产目录</span>
          </template>
          <FolderAddOutlined style="font-size: 20px" @click="showAddModal" />
        </a-tooltip>
      </span>
    </div>
    <a-input
      v-model:value="searchName"
      placeholder="请输入内容"
      :bordered="true"
      @change="handleSearch"
      style="margin-bottom: 10px"
    />
    <a-tree
      :tree-data="treeData"
      v-model:expandedKeys="expandedKeys"
      v-model:selectedKeys="selectedKeys"
      :fieldNames="{
        children: 'children',
        title: 'name',
        key: 'id',
      }"
      class="custom-tree"
      showIcon
      :load-data="onLoadData"
      @select="handleSelect"
      @expand="handleExpand"
      v-if="reloadTree"
    >
      <template #icon="{ dataRef }">
        <FolderOutlined style="font-size: 18px" v-if="!dataRef.isDataBase" />
      </template>
      <template #title="{ name, dataRef }">
        <div
          class="tree-node-content"
          @mouseenter="handleMouseEnter(dataRef)"
          @mouseleave="handleMouseLeave(dataRef)"
        >
          <type-icon
            v-if="dataRef.isDataBase"
            style="margin-right: 10px"
            :type="dataRef?.databaseType"
          />

          <span class="node-name">
            {{ name }}{{ dataRef.tableCount ? '（' + dataRef.tableCount + '）' : '' }}
            <!-- {{ console.log(dataRef) }} -->
          </span>
          <div class="node-actions" v-show="dataRef.isHovered">
            <a-dropdown>
              <PlusOutlined class="action-icon" @click.stop />
              <template #overlay>
                <a-menu>
                  <a-menu-item key="1" v-if="!dataRef?.isDataBase" @click="handleAdd(dataRef)"
                    >新增资产目录</a-menu-item
                  >
                  <a-menu-item key="2" v-if="!dataRef?.isDataBase" @click="handleAddAsset(dataRef)"
                    >新增资产</a-menu-item
                  >
                  <a-menu-item key="3" v-if="dataRef?.isDataBase" @click="handleAddTable(dataRef)"
                    >新增表</a-menu-item
                  >
                  <a-menu-item key="4" v-if="dataRef?.isDataBase">
                    <DelDatabaseModal
                      :databaseName="dataRef.name"
                      :databaseId="dataRef.databaseId"
                      @onSuccess="handleDeleteDatabaseSuccess"
                    />
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
            <a-dropdown v-if="!dataRef?.isDataBase">
              <MoreOutlined class="action-icon" @click.stop />
              <template #overlay>
                <a-menu>
                  <a-menu-item key="1" @click="handleRename(dataRef)">重命名</a-menu-item>
                  <a-menu-item key="2" @click="handleDelete(dataRef)">删除</a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </div>
        </div>
      </template>
    </a-tree>

    <!-- 新增/编辑弹窗 -->
    <a-modal
      v-model:visible="modalVisible"
      :title="modalTitle"
      @ok="handleModalOk"
      @cancel="handleModalCancel"
    >
      <a-form
        ref="formRef"
        :model="modalForm"
        :rules="rules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 16 }"
      >
        <a-form-item label="目录名称" name="name">
          <a-input v-model:value="modalForm.name" placeholder="请输入目录名称" />
        </a-form-item>
        <a-form-item label="目录描述" name="description">
          <a-textarea v-model:value="modalForm.description" placeholder="请输入目录描述" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 新增资产弹窗 -->
    <add-asset-modal
      v-model:visible="assetModalVisible"
      :parent-data="selectedNode"
      @success="handleAssetSuccess"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import {
  PlusOutlined,
  MoreOutlined,
  FolderAddOutlined,
  FolderOutlined,
  RedoOutlined,
} from '@ant-design/icons-vue'
import { message, Modal } from 'ant-design-vue'
import {
  getListDataAssetsDirectory,
  addDataAssetsDirectory,
  updateDataAssetsDirectory,
  delDataAssetsDirectory,
} from '@/api/assetmanager/catalogadmin/catalogadmin'
import { getDataAssetList } from '@/api/assetmanager/dataassertmanage/dataassertmanage'
import AddAssetModal from './add-asset-modal.vue'
import { storeToRefs } from 'pinia'
import { useUserStore } from '@/stores/page/data-asset'
import TypeIcon from '@/views/data-asset/metadata-query/components/type-icon.vue'
import DelDatabaseModal from './del-database-modal.vue'
import { removeNodeById } from './helper'

const emit = defineEmits(['add-asset'])

const userStore = useUserStore()
const { pageData } = storeToRefs(userStore)
const { setPageData } = userStore

const expandedKeys = ref<string[]>([])
const selectedKeys = ref<string[]>([])
const treeData = ref<any>([])
const searchName = ref('')
const reloadTree = ref(true)
const route = useRoute()
const dbName = computed(() => (route.query.dbName as string) || '')

onMounted(() => {
  if (pageData.value.expandedKeys?.length) {
    expandedKeys.value = pageData.value.expandedKeys
  }
  if (pageData.value.selectedKeys?.length) {
    selectedKeys.value = pageData.value.selectedKeys
    emit('add-asset', { ...pageData.value.dataRef })
  }
  if (pageData.value.originalTreeData?.length) {
    originalTreeData.value = pageData.value.originalTreeData
  }
  if (pageData.value.treeData?.length) {
    treeData.value = JSON.parse(JSON.stringify(pageData.value.originalTreeData))
    if (dbName.value) {
      searchName.value = dbName.value
      handleSearch()
    }
  } else {
    fetchData()
  }
})

// 弹窗相关
const modalVisible = ref(false)
const modalTitle = ref('新增目录')
const isAddBaseData = ref(false)
const formRef = ref()
const modalForm = ref<Record<string, any>>({
  parentId: undefined,
  name: '',
  description: '',
})
const selectDataRef = ref<any>({})

const rules = {
  name: [{ required: true, message: '请输入目录名称' }],
  description: [{ required: false, message: '请输入目录描述' }],
}

// 处理树形数据，递归设置 isLeaf
const processTreeData = (data: any[]) => {
  return data.map((item) => {
    const processedItem = { ...item }
    if (processedItem.assertCount <= 0 && !processedItem?.children?.length) {
      processedItem.isLeaf = true
    }
    if (processedItem.children && processedItem.children.length > 0) {
      processedItem.children = processTreeData(processedItem.children)
    }
    return processedItem
  })
}

// 本地过滤树形数据
const filterTreeData = (data: any[], keyword: string): any[] => {
  if (!keyword) return data

  // 递归搜索匹配节点
  const filterNodes = (nodes: any[]): any[] => {
    return nodes.filter((node) => {
      // 检查当前节点是否匹配
      const isMatch = node.name.toLowerCase().includes(keyword.toLowerCase())

      // 递归检查子节点
      let filteredChildren: any[] = []
      if (node.children && node.children.length > 0) {
        filteredChildren = filterNodes(node.children)
      }

      // 如果子节点有匹配，保留这些子节点
      if (filteredChildren.length > 0) {
        node.children = filteredChildren
        return true
      }

      // 如果当前节点匹配，则保留
      return isMatch
    })
  }

  return filterNodes(JSON.parse(JSON.stringify(data)))
}

// 存储原始树形数据
const originalTreeData = ref<any>([])

const handleSelect = (selectedKeysArr: string[], info: any) => {
  setPageData('selectedKeys', selectedKeysArr)
  setPageData('expandedKeys', expandedKeys.value)
  setPageData('treeData', treeData.value)

  const dataRef = {
    ...info.node.dataRef,
    name: info.node.name,
  }
  selectDataRef.value = dataRef
  setPageData('dataRef', dataRef)
  if (dataRef?.isDataBase) {
    emit('add-asset', dataRef)
  }
}

const handleExpand = (expandedKeysArr: string[]) => {
  expandedKeys.value = expandedKeysArr
  setPageData('expandedKeys', expandedKeysArr)
  setPageData('treeData', treeData.value)
}

const onLoadData: any = (treeNode: any) => {
  return new Promise<void>((resolve) => {
    if (treeNode.dataRef.assertCount <= 0) {
      resolve()
      return
    }
    getDataAssetList({ directoryId: treeNode.dataRef.id }, {})
      .then(({ data }) => {
        const list = data.map((item) => ({
          ...item,
          id: item.databaseId,
          name: item.databaseName,
          isLeaf: true,
          isDataBase: true,
        }))
        if (!treeNode?.dataRef?.children) treeNode.dataRef.children = []
        const fileList = treeNode.dataRef.children.filter((item: any) => !item.isDataBase)
        treeNode.dataRef.children = [...fileList, ...list]
        treeData.value = [...treeData.value]
        setPageData('treeData', treeData.value)

        updateOriginalTreeNode(originalTreeData.value, treeNode.dataRef.id, treeNode.dataRef)

        resolve()
      })
      .catch((error) => {
        console.error('加载数据失败:', error)
        resolve()
      })
  })
}

// 更新 originalTreeData 中对应节点的数据
const updateOriginalTreeNode = (nodes: any[], nodeId: string, dataRef: any): boolean => {
  for (let i = 0; i < nodes.length; i++) {
    if (nodes[i].id === nodeId) {
      nodes[i] = JSON.parse(JSON.stringify(dataRef))
      return true
    }
    if (nodes[i].children && nodes[i].children.length > 0) {
      if (updateOriginalTreeNode(nodes[i].children, nodeId, dataRef)) return true
    }
  }

  return false
}

const fetchData = async () => {
  try {
    const params = {
      name: '', // 不再使用searchName作为过滤条件
    }
    const res = await getListDataAssetsDirectory(params, {})
    if (res?.data) {
      const processedData = processTreeData(res.data)
      treeData.value = processedData
      if (pageData.value?.originalTreeData.length <= 0) {
        originalTreeData.value = JSON.parse(JSON.stringify(treeData.value)) // 保存原始数据的副本
        setPageData('originalTreeData', originalTreeData.value)
      }
      setPageData('treeData', treeData.value)
      reloadTree.value = true
      if (dbName.value) {
        searchName.value = dbName.value
        handleSearch()
      } else {
        // 如果树形数据不为空，则展开第一个节点，并加载子节点
        findDefaultExpandedKeys()
      }
    }
  } catch (error) {
    console.error('获取数据失败:', error)
  }
}

async function findDefaultExpandedKeys() {
  if (treeData.value.length > 0) {
    const firstNode = treeData.value[0]
    expandedKeys.value = [firstNode.id]
    await onLoadData({ dataRef: firstNode })
    const childrenData = firstNode.children
    if (childrenData && childrenData.length > 0) {
      const firstChildNode = childrenData.find((item: any) => item.isDataBase)
      selectedKeys.value = [firstChildNode.databaseId]
      handleSelect([firstChildNode.databaseId], {
        selected: true,
        selectedNodes: [firstChildNode],
        node: { dataRef: firstChildNode },
      })
    }
  }
}

const showAddModal = () => {
  modalTitle.value = '新增目录'
  modalForm.value = {
    parentId: undefined,
    name: '',
    description: '',
  }
  modalVisible.value = true
}

const handleModalOk = async () => {
  try {
    await formRef.value.validate()
    const params: any = {
      ...modalForm.value,
    }
    const handle = modalForm.value.id ? updateDataAssetsDirectory : addDataAssetsDirectory
    const res = await handle(params, {})
    message.success('操作成功')
    if (modalForm.value.id) {
      // 重命名情况：更新当前节点名称
      const updateNodeName = (nodes: any[]) => {
        for (let node of nodes) {
          if (node.id === modalForm.value.id) {
            node.name = modalForm.value.name
            // 更新源数据
            updateOriginalTreeNode(originalTreeData.value, node.id, node)
            return true
          }
          if (node.children && node.children.length > 0) {
            if (updateNodeName(node.children)) return true
          }
        }
        return false
      }
      updateNodeName(treeData.value)
    } else {
      // 新增目录情况：添加到父节点下
      const addToParent = (nodes: any[]) => {
        for (let node of nodes) {
          if (node.id === modalForm.value.parentId) {
            // 确保children数组存在
            if (!node.children) {
              node.children = []
            }
            // 创建新节点
            const newNode = {
              ...res.data,
              name: modalForm.value.name,
              id: res?.data?.directoryId || '',
              description: modalForm.value.description,
              assertCount: 0,
              isLeaf: true,
              children: [],
            }
            // 添加新节点
            node.children.unshift(newNode)
            // 更新父节点的isLeaf状态
            node.isLeaf = false
            return true
          }
          if (node.children && node.children.length > 0) {
            if (addToParent(node.children)) return true
          }
        }
        return false
      }
      if (modalForm.value?.parentId) {
        addToParent(treeData.value)
      } else {
        const obj = {
          ...res.data,
          id: res?.data?.directoryId || '',
          assertCount: 0,
          isLeaf: true,
        }
        treeData.value.push(obj)
        // 更新源数据
        originalTreeData.value.push(obj)
      }
    }

    // 强制更新树形数据
    treeData.value = JSON.parse(JSON.stringify(treeData.value))

    modalForm.value = {}
    modalVisible.value = false
  } catch (error) {
    console.error('操作失败:', error)
  }
}

const handleModalCancel = () => {
  modalVisible.value = false
  formRef.value?.resetFields()
}

const handleAdd = (data: Record<string, any>) => {
  modalTitle.value = '新增目录'
  modalForm.value = {
    parentId: data.id,
  }
  modalVisible.value = true
}

const handleRename = (data: Record<string, any>) => {
  modalTitle.value = '重命名目录'
  modalForm.value = {
    ...data,
  }
  modalVisible.value = true
}

const handleDelete = (data: Record<string, any>) => {
  Modal.confirm({
    title: '确认删除',
    content: '是否确认删除该目录？',
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      try {
        await delDataAssetsDirectory({ id: data.id }, {})
        message.success('删除成功')

        // 从树形数据中删除节点
        const removeNode = (nodes: any[]): boolean => {
          for (let i = 0; i < nodes.length; i++) {
            if (nodes[i].id === data.id) {
              nodes.splice(i, 1)
              return true
            }
            if (nodes[i].children && nodes[i].children.length > 0) {
              if (removeNode(nodes[i].children)) return true
            }
          }
          return false
        }

        removeNode(treeData.value)
        // 强制更新树形数据
        treeData.value = JSON.parse(JSON.stringify(treeData.value))
      } catch (error) {
        console.error('删除失败:', error)
      }
    },
  })
}

// 删除库组件回调函数
function handleDeleteDatabaseSuccess() {
  const databaseId = arguments[0]

  // 删除内存中的节点
  treeData.value = removeNodeById(databaseId, treeData.value)

  // 查找默认可展开的节点
  findDefaultExpandedKeys()
}

const handleSearch = () => {
  if (!searchName.value) {
    // 如果搜索框为空，恢复原始数据
    treeData.value = JSON.parse(JSON.stringify(originalTreeData.value))
  } else {
    // 使用本地过滤
    treeData.value = filterTreeData(originalTreeData.value, searchName.value)
  }
  // 强制重新渲染树
  reloadTree.value = false
  setTimeout(() => {
    reloadTree.value = true
  }, 0)
}

const handleMouseEnter = (dataRef: any) => {
  dataRef.isHovered = true
}

const handleMouseLeave = (dataRef: any) => {
  dataRef.isHovered = false
}

// 资产弹窗相关
const assetModalVisible = ref(false)
const selectedNode = ref<Record<string, any>>({})

const handleAddAsset = (data: Record<string, any>) => {
  selectedNode.value = data
  selectedNode.value.directoryId = data?.id || ''
  assetModalVisible.value = true
  isAddBaseData.value = true
}

const handleAssetSuccess = (data: any) => {
  // 重新加载当前节点的子节点数据
  const node = selectedNode.value

  if (node) {
    // 确保children数组存在
    if (!node.children) {
      node.children = []
    }
    emit('add-asset', { ...data })
    selectedKeys.value = [data.databaseId]
    // 如果已存在，则不添加
    if (!isAddBaseData.value) return

    // 创建新节点
    const newNode = {
      ...data,
      isLeaf: true,
      isDataBase: true,
      name: data.databaseName,
      id: data.databaseId,
    }
    // 添加新节点到父节点
    node.children.unshift(newNode)
    // 更新父节点的isLeaf状态
    node.isLeaf = false
    // 强制更新树形数据
    treeData.value = JSON.parse(JSON.stringify(treeData.value))
    updateOriginalTreeNode(originalTreeData.value, node.id, node)
  }
  isAddBaseData.value = false
}

const handleAddTable = (data: Record<string, any>) => {
  selectedNode.value = data
  assetModalVisible.value = true
  isAddBaseData.value = false
}

const updateNodeData = (node: any) => {
  const findNodeInTree = (nodes: any[]): any => {
    for (let i = 0; i < nodes.length; i++) {
      if (nodes[i].children && nodes[i].children.length > 0) {
        const foundInChildren = findNodeInTree(nodes[i].children)
        if (foundInChildren) return foundInChildren
      }
      if (nodes[i].databaseId === node.databaseId) {
        return nodes[i]
      }
    }
    return null
  }

  const data = findNodeInTree(treeData.value)
  if (data) {
    data.tableCount = node.tableCount
    // 更新源数据
    updateOriginalTreeNode(originalTreeData.value, node.databaseId, data)
    if (selectDataRef.value?.databaseId === node.databaseId) {
      selectDataRef.value.tableCount = node.tableCount
    }
    // 强制更新树形数据
    treeData.value = [...treeData.value]
  }
}

const refresh = () => {
  useUserStore().clearPageData()
  fetchData()
}

defineExpose({
  fetchData,
  updateNodeData,
})
</script>

<style lang="less" scoped>
.directory-tree {
  height: 100%;

  .custom-tree {
    margin-top: 10px;
    :deep(.ant-tree-node-content-wrapper) {
      width: 100%;
      &:hover {
        background-color: #f5f5f5;
      }
    }
    :deep(.ant-tree-node-selected) {
      background-color: #e6f7ff;
    }
    :deep(.ant-tree-title) {
      width: 100%;
    }
    .tree-node-content {
      width: 100%;
      padding-right: 10px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .node-name {
        flex: 1;
        white-space: nowrap;
      }

      .node-actions {
        display: flex;
        align-items: center;
        gap: 8px;

        .action-icon {
          font-size: 16px;
          color: #666;
          cursor: pointer;

          &:hover {
            color: #1890ff;
          }
        }
      }
    }
  }

  :deep(.ant-tree) {
    .ant-tree-treenode {
      width: 100%;
      height: 40px;
      display: flex;
      align-items: center;
      .ant-tree-switcher {
        line-height: 40px;
      }
      .ant-tree-iconEle {
        width: 18px;
        height: 18px;
        line-height: 18px;
        margin-right: 5px;
      }
      .ant-tree-title {
        width: 100%;
      }
      .ant-tree-node-content-wrapper {
        width: 100%;
        height: 40px;
        display: flex;
        align-items: center;
      }
    }
  }

  .title {
    margin-right: auto;
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 24px;
    padding-bottom: 16px;
    display: flex;
    justify-content: space-between;
    padding-right: 12px;
  }
}
</style>
