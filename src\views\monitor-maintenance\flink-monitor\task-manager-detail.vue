<template>
  <div class="task-detail">
    <a-descriptions
      layout="vertical"
      bordered
      size="small"
      v-for="(item, key) in clusterlInfo"
      :key="key"
    >
      <a-descriptions-item :span="3" v-for="(value, key) of item" :key="key">
        <template #label
          ><span class="strong">{{ key }}</span></template
        >
        <template v-if="typeof value === 'object'">
          <a-descriptions layout="vertical" bordered size="small">
            <a-descriptions-item
              :label="index"
              :span="2"
              v-for="(childValue, index) of value"
              :key="index"
            >
              {{ childValue }}
            </a-descriptions-item>
          </a-descriptions>
        </template>
        <template v-else>{{ value }}</template>
      </a-descriptions-item>
    </a-descriptions>
  </div>
</template>
<script lang="ts" setup>
import { ref } from 'vue'
import { getTaskManagerDetail } from '@/api/services/monitor-maintenance/flink-monitor'

const clusterlInfo = ref<any>({})

// 获取列表数据
const getList = () => {
  getTaskManagerDetail({}).then((res) => {
    clusterlInfo.value = res.data.obj.taskmanagers
  })
}

onMounted(() => {
  getList()
})
</script>

<style lang="less" scoped>
.task-detail {
  height: calc(100vh - 150px);
  width: 100%;
  overflow-y: auto;
  padding: 8px 24px;
  :deep(.ant-descriptions .ant-descriptions-item-label) {
    font-size: 13px;
    font-weight: 600;
    color: #8c8c8c;
  }
  :deep(.ant-descriptions .ant-descriptions-item-content) {
    font-size: 13px;
    font-weight: 600;
    color: #141414;
  }
}
.strong {
  font-weight: bold;
}
</style>
