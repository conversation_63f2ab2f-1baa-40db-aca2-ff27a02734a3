<template>
  <div class="position-manage">
    <cardBox title="岗位管理" subTitle="岗位信息及新增编辑删除">
      <template #headerRight>
        <a-button type="primary" @click="addTable" v-action:addJob>新增</a-button>
      </template>
      <Table :columns="columns" :getData="getJobList" ref="tableRef" :searchFormState="form">
        <template #search>
          <a-form-item label="" name="岗位名称" :rules="[{ message: '请输入岗位名称' }]">
            <a-input v-model:value="form.jobName" placeholder="请输入岗位名称" />
          </a-form-item>
          <!-- <a-form-item label="组织" name="deptId">
          <a-cascader
            :field-names="{ label: 'deptName', value: 'deptId', children: 'deptList' }"
            :options="viewDeptListOptions"
            expand-trigger="hover"
            change-on-select
            placeholder="请选择组织"
            @change="deptChange"
          ></a-cascader>
        </a-form-item> -->
          <a-form-item label="">
            <a-select v-model:value="form.isUse" placeholder="请选择是否启用" allow-clear>
              <a-select-option value="1">是</a-select-option>
              <a-select-option value="0">否</a-select-option>
            </a-select>
          </a-form-item>
        </template>
      </Table>
    </cardBox>
    <position-manage-modal
      ref="positionModal"
      @modalHandleOk="modalHandleOk"
    ></position-manage-modal>
  </div>
</template>
<script setup lang="ts">
import { Table } from '@fs/fs-components'
import { useUserStore } from '@/stores/user'
import Modal from 'ant-design-vue/es/modal/Modal'
import { PositionManageDTO } from '@/utils/column'
import { getDeptListFn } from '@/components/permission/common'
import { ref, onBeforeMount, h, createVNode, watch, withDirectives, resolveDirective } from 'vue'
import { ExclamationCircleOutlined } from '@ant-design/icons-vue'
import { deleteJob, getJobList } from '@/api/services/permission'
import type { Column } from '@fs/fs-components/src/components/table/type'
import { Button, message, type CascaderProps } from 'ant-design-vue/es/components'
import cardBox from '@/components/card-box/card-box.vue'

defineOptions({
  name: 'positionManage',
})

let columns = ref<Column[]>([])
const tableRef = ref<InstanceType<typeof Table> | null>(null)
const modalOpen = ref<boolean>(false)
const disabled = ref<boolean>(false)
const positionModal = ref<any | null>(null)
const viewDeptListOptions = ref<CascaderProps['options']>([]) // 组织
const form: Record<string, any> = ref({
  jobName: '',
  isUse: null,
  deptId: '',
})
const userStore = useUserStore()
watch(
  () => userStore.userInfo.buId,
  (Val) => {
    if (Val) {
      form.value.buId = Val
      tableRef.value?.getTableData()
    }
  },
)

function modalHandleOk() {
  tableRef.value?.getTableData()
}

onBeforeMount(() => {
  initColumns()
  getDeptListFn().then((res) => {
    viewDeptListOptions.value = res
  })
})

function addTable() {
  modalOpen.value = true
  disabled.value = false
  positionModal.value?.show('add')
}

/***
 * 选择组织
 * **/
function deptChange(e: string | any[]) {
  form.value.deptId = e && e[e.length - 1]
}

async function initColumns() {
  columns.value = PositionManageDTO.map((item) => {
    return {
      ...item,
      dataIndex: item.key,
    }
  })
  columns.value.push({
    key: 'action',
    title: '操作',
    customRender(data: Record<string, any>) {
      return [
        withDirectives(
          h(
            Button,
            {
              type: 'link',
              onClick() {
                positionModal.value?.show('edit', data.record)
              },
            },
            {
              default: () => '编辑',
            },
          ),
          [[resolveDirective('action'), '', 'updateJob']],
        ),
        withDirectives(
          h(
            Button,
            {
              type: 'link',
              onClick() {
                Modal.confirm({
                  title: '提示',
                  icon: createVNode(ExclamationCircleOutlined),
                  content: createVNode('div', { style: 'color:red;' }, '确认执行删除操作？'),
                  async onOk() {
                    try {
                      return await new Promise((resolve, reject) => {
                        deleteJob({ jobId: data?.record?.jobId })
                          .then((res: any) => {
                            if (res.code === '000000') {
                              message.success('删除成功')
                              resolve(true)
                              tableRef.value?.resetForm()
                            }
                          })
                          .catch(() => {
                            reject()
                          })
                      })
                    } catch {
                      return console.log('Oops errors!')
                    }
                  },
                  class: 'test',
                })
              },
            },
            {
              default: () => '删除',
            },
          ),
          [[resolveDirective('action'), '', 'deleteJob']],
        ),
      ]
    },
  })
}
</script>
<style lang="less" scoped>
.position-manage {
  height: 100%;
  background: white;
}
</style>
