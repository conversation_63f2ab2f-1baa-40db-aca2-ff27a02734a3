import service from '@/api/index'
import { useUserStore } from '@/stores/user'
import axios from 'axios'

const userStore = useUserStore()
const token = userStore.token
const { userId, buId, tenantId, userName } = userStore.userInfo

// SM001-获取字典数据
export function reqGetModelTypeData(params: { dictTypeId: string } = { dictTypeId: 'modelType' }) {
  return service({
    method: 'get',
    url: '/knowledge/systemManege/getDataDictList',
    heaeders: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// MS001-获取模型服务列表
export function reqGetModelList(params: any = {}) {
  return service({
    method: 'get',
    url: '/knowledge/modelService/getModelServiceList',
    heaeders: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// MS002-新增模型服务
export function reqAddModel(data: any = {}) {
  return service({
    method: 'post',
    url: '/knowledge/modelService/saveModelServiceInfo',
    data,
    headers: {
      opUser: userId,
    },
  })
}

// MS003-修改模型服务
export function reqUpdateModel(data: any = {}) {
  return service({
    method: 'post',
    url: '/knowledge/modelService/updateModelServiceInfo',
    data,
    headers: {
      opUser: userId,
    },
  })
}

// MS004-删除模型服务
export function reqDeleteModel(data: any = {}) {
  return service({
    method: 'post',
    url: '/knowledge/modelService/deleteModelServiceInfo',
    data,
  })
}

// MS005-校验模型服务配置
export function reqCheckModel(params: any = {}) {
  return service({
    method: 'get',
    url: '/knowledge/modelService/verifyModelServiceUrl',
    params,
  })
}
