export function getOptionsBar() {
  function generateRandomArray(length: number, min: number, max: number): number[] {
    const arr = []
    for (let i = 0; i < length; i++) {
      // 在 min 和 max 之间生成一个随机数，并将其推入数组
      const randomNum = Math.floor(Math.random() * (max - min + 1)) + min
      arr.push(randomNum)
    }
    return arr
  }
  const randomArray = generateRandomArray(7, 10, 200)
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        code: '000000',
        data: {
          xAxis: {
            type: 'category',
            data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
          },
          grid: {
            left: '0',
            right: '0',
            top: '10px',
            bottom: '0',
            containLabel: true,
          },
          yAxis: {
            type: 'value',
          },
          series: [
            {
              data: randomArray,
              type: 'bar',
            },
          ],
        },
      })
    }, 1000)
  })
}

export function getOptionsLine() {
  function generateRandomArray(length: number, min: number, max: number): number[] {
    const arr = []
    for (let i = 0; i < length; i++) {
      // 在 min 和 max 之间生成一个随机数，并将其推入数组
      const randomNum = Math.floor(Math.random() * (max - min + 1)) + min
      arr.push(randomNum)
    }
    return arr
  }
  const randomArray = generateRandomArray(7, 10, 200)
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        code: '000000',
        data: {
          xAxis: {
            type: 'category',
            data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
          },
          grid: {
            left: '0',
            right: '0',
            top: '10px',
            bottom: '0',
            containLabel: true,
          },
          yAxis: {
            type: 'value',
          },
          series: [
            {
              data: randomArray,
              type: 'line',
            },
          ],
        },
      })
    }, 1000)
  })
}

export function getOptionsLineBg() {
  function generateRandomArray(length: number, min: number, max: number): number[] {
    const arr = []
    for (let i = 0; i < length; i++) {
      // 在 min 和 max 之间生成一个随机数，并将其推入数组
      const randomNum = Math.floor(Math.random() * (max - min + 1)) + min
      arr.push(randomNum)
    }
    return arr
  }
  const randomArray = generateRandomArray(7, 10, 200)
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        code: '000000',
        data: {
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
          },
          grid: {
            left: '0',
            right: '0',
            top: '10px',
            bottom: '0',
            containLabel: true,
          },
          yAxis: {
            type: 'value',
          },
          series: [
            {
              data: randomArray,
              type: 'line',
              areaStyle: {},
            },
          ],
        },
      })
    }, 1000)
  })
}

export function getOptionsPie() {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        code: '000000',
        data: {
          tooltip: {
            trigger: 'item',
          },
          legend: {
            top: '5%',
            left: 'center',
          },
          series: [
            {
              name: 'Access From',
              type: 'pie',
              radius: ['40%', '70%'],
              avoidLabelOverlap: false,
              itemStyle: {
                borderRadius: 10,
                borderColor: '#fff',
                borderWidth: 2,
              },
              label: {
                show: false,
                position: 'center',
              },
              emphasis: {
                label: {
                  show: true,
                  fontSize: 40,
                  fontWeight: 'bold',
                },
              },
              labelLine: {
                show: false,
              },
              data: [
                { value: 1048, name: 'Search Engine' },
                { value: 735, name: 'Direct' },
                { value: 580, name: 'Email' },
              ],
            },
          ],
        },
      })
    }, 1000)
  })
}

export function getOptionsRadar() {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        code: '000000',
        data: {
          legend: {
            data: ['Allocated Budget', 'Actual Spending'],
          },
          radar: {
            // shape: 'circle',
            radius: '65%', // 调整雷达图的半径以适应页面
            indicator: [
              { name: 'Sales', max: 6500 },
              { name: 'Administration', max: 16000 },
              { name: 'Information Technology', max: 30000 },
              { name: 'Customer Support', max: 38000 },
              { name: 'Development', max: 52000 },
              { name: 'Marketing', max: 25000 },
            ],
          },
          series: [
            {
              name: 'Budget vs spending',
              type: 'radar',

              data: [
                {
                  value: [5000, 14000, 28000, 26000, 42000, 21000],
                  name: 'Actual Spending',
                },
              ],
            },
          ],
        },
      })
    }, 1000)
  })
}

export function getOptionsGauge() {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        code: '000000',
        data: {
          series: [
            {
              type: 'gauge',
              startAngle: 180,
              endAngle: 0,
              center: ['50%', '75%'],
              radius: '90%',
              min: 0,
              max: 1,
              splitNumber: 8,
              axisLine: {
                lineStyle: {
                  width: 6,
                  color: [
                    [0.25, '#FF6E76'],
                    [0.5, '#FDDD60'],
                    [0.75, '#58D9F9'],
                    [1, '#7CFFB2'],
                  ],
                },
              },
              pointer: {
                icon: 'path://M12.8,0.7l12,40.1H0.7L12.8,0.7z',
                length: '12%',
                width: 20,
                offsetCenter: [0, '-60%'],
                itemStyle: {
                  color: 'auto',
                },
              },
              axisTick: {
                length: 12,
                lineStyle: {
                  color: 'auto',
                  width: 2,
                },
              },
              splitLine: {
                length: 20,
                lineStyle: {
                  color: 'auto',
                  width: 5,
                },
              },
              axisLabel: {
                color: '#464646',
                fontSize: 20,
                distance: -60,
                rotate: 'tangential',
                formatter: function (value: number) {
                  if (value === 0.875) {
                    return 'Grade A'
                  } else if (value === 0.625) {
                    return 'Grade B'
                  } else if (value === 0.375) {
                    return 'Grade C'
                  } else if (value === 0.125) {
                    return 'Grade D'
                  }
                  return ''
                },
              },
              title: {
                offsetCenter: [0, '-10%'],
                fontSize: 20,
              },
              detail: {
                fontSize: 30,
                offsetCenter: [0, '-35%'],
                valueAnimation: true,
                formatter: function (value: number) {
                  return Math.round(value * 100) + ''
                },
                color: 'inherit',
              },
              data: [
                {
                  value: 0.7,
                  name: 'Grade Rating',
                },
              ],
            },
          ],
        },
      })
    }, 1000)
  })
}
