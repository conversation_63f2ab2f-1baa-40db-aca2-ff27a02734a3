<template>
  <a-modal
    :open="open"
    @ok="modalHandleOk"
    :title="modalType === 'edit' ? '编辑' : '新增' + '角色'"
    @cancel="cancel"
    width="800px"
    :confirm-loading="confirmLoading"
  >
    <a-form
      ref="formRef"
      :model="form"
      :rules="rules"
      :label-col="labelCol"
      :wrapper-col="wrapperCol"
    >
      <a-form-item label="角色名称" name="roleName">
        <a-input v-model:value="form.roleName" placeholder="请选择角色名称" />
      </a-form-item>
      <a-form-item label="角色类别" name="roleType">
        <a-select
          v-model:value="form.roleType"
          show-search
          placeholder="请选择系统类别"
          :options="form.dictTypeOptions"
          :field-names="{ label: 'dataName', value: 'dataId' }"
          :filter-option="filterOption"
          @change="handleChange"
        ></a-select>
      </a-form-item>
      <a-form-item label="是否启用" name="isUse">
        <a-radio-group v-model:value="form.isUse" placeholder="请选择是否启用">
          <a-radio :value="1">是</a-radio>
          <a-radio :value="0">否</a-radio>
        </a-radio-group>
      </a-form-item>
      <a-form-item label="备注">
        <a-textarea v-model:value="form.remark" placeholder="请输入角色备注信息" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>
<script lang="ts" setup>
import { message } from 'ant-design-vue/es/components'
import { defineEmits, defineProps, defineExpose, ref } from 'vue'
import type { Rule, FormInstance } from 'ant-design-vue/es/form'
import { updateRole, addRole } from '@/api/services/permission'
interface FormState {
  roleName: string
  roleType: string | undefined | number
  isUse: any
  remark: string
  dictTypeOptions?: Data[]
}
interface Data {
  dataId: string
  dataName: string
}
defineProps({
  open: {
    type: Boolean,
    default: false,
  },
  disabled: Boolean,
})

const emits = defineEmits(['modalHandleOk', 'modalCancel'])
const formRef = ref<FormInstance>()
const labelCol = { span: 5 }
const wrapperCol = { span: 13 }
const modalType = ref<string>('')
const confirmLoading = ref<boolean>(false)
const form = ref<FormState>({
  roleName: '',
  roleType: undefined,
  isUse: undefined,
  remark: '',
  dictTypeOptions: [],
})

const rules: Record<string, Rule[]> = {
  isUse: [{ required: true, message: '请选择是否启用', trigger: 'blur' }],
  roleName: [{ required: true, message: '请输入角色名称', trigger: 'blur' }],
  roleType: [{ required: true, message: '请选择角色类别', trigger: 'blur' }],
}

const handleChange = (value: string) => {
  form.value.roleType = value
}

const filterOption = (input: string, option: any) => {
  return option.value.toLowerCase().indexOf(input.toLowerCase()) >= 0
}

function modalHandleOk() {
  formRef.value &&
    formRef.value
      .validate()
      .then(() => {
        const { dictTypeOptions, ...formData } = form.value
        const { isUse, roleType } = formData
        confirmLoading.value = true
        if (modalType.value === 'edit') {
          formData['isUse'] = isUse == 1 ? true : false
          updateRole(formData)
            .then(() => {
              emits('modalHandleOk', form.value)
              message.success('编辑成功!')
            })
            .finally(() => {
              confirmLoading.value = false
            })
        } else {
          const paramsData = formData
          paramsData.isUse = isUse == 1 ? true : false
          paramsData.roleType = Number(roleType)
          addRole(paramsData)
            .then(() => {
              emits('modalHandleOk')
              message.success('新增成功!')
            })
            .finally(() => {
              confirmLoading.value = false
            })
        }
      })
      .catch((error: any) => {
        console.log(error)
      })
}

function cancel() {
  emits('modalCancel')
}

async function show(type: string, data?: Record<string, any>) {
  await resetForm()
  modalType.value = type
  if (type === 'edit') {
    editForm(data)
  } else if (type === 'add') {
    form.value.dictTypeOptions = data?.dictTypeOptions
  }
}

const resetForm = () => {
  formRef.value?.resetFields()
}

function editForm(data?: any) {
  if (data) {
    const copyData = JSON.parse(JSON.stringify(data))
    const { roleName, roleType, isUser, remark, dictTypeOptions, roleId } = copyData
    const editForm = {
      roleName,
      roleType,
      isUse: 0,
      remark,
      dictTypeOptions,
      roleId,
    }
    editForm['isUse'] = isUser == '否' ? 0 : 1
    editForm['roleType'] = String(roleType)
    form.value = editForm
  }
}
defineExpose({ show })
</script>
