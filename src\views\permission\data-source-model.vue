<template>
  <a-modal
    class="dataSourceModel"
    v-model:open="open"
    :title="modalType === '7' ? '分配数据源' : '分配配置项目'"
    @cancel="cancel"
    width="1200px"
    :confirm-loading="confirmLoading"
    @ok="ok"
  >
    <Table
      :columns="columns"
      :getData="getData"
      :tableConfig="{
        rowSelection: {
          selectedRowKeys: selectedKeys,
          onSelect: onSelectChange,
          onSelectAll,
        },
        rowKey: rowKeyMap[modalType],
      }"
      ref="tableRef"
      class="tableOperate"
      :searchFormState="formState"
      v-if="tableSelectedKeys"
      @finish="finish"
    >
      <template #bodyCell="{ record, column }">
        <template v-if="column.key === 'dbType'">
          <div class="db-item">
            <img :src="getDbIcon(record.dbType)" alt="" />
            <div class="text">{{ DbTypeName[record.dbType] }}</div>
          </div>
        </template>
      </template>
      <template #operate></template>
      <template #search>
        <a-form-item name="用户名">
          <a-input
            v-model:value="formState[modalType === '7' ? 'dbDesc' : 'proName']"
            placeholder="请输入名称"
            allowClear
          />
        </a-form-item>
      </template>
    </Table>
  </a-modal>
</template>
<script lang="ts" setup>
import { Table } from '@fs/fs-components'
import { message, type TableProps } from 'ant-design-vue/es/components'
import { defineEmits, defineProps, defineExpose, ref, watch, computed } from 'vue'
import type { FormInstance } from 'ant-design-vue/es/form'
import { getRoleUserList, getUserGroupUserList } from '@/api/services/permission'
import type { Column } from '@fs/fs-components/src/components/table/type'
import { allocationUserDTO, UserGroupDTO } from '@/utils/column'
import {
  reqGetDataSourceList,
  reqGetHasAuthIds,
  reqGetModelList,
  reqGetRoleProjectList,
  reqSaveRoleAuth,
} from '@/api/services/data-auth'
import { dataSourceColumn, projectColumn, DbTypeName } from './data-source'
import { getDbIcon } from '@/utils/utils'
const props = defineProps({
  request: {
    type: String,
    default: () => '',
  },
})
const modalType = ref<string>('')
const formState = ref<any>({
  roleId: null,
  pageIndex: 1,
  pageSize: 10,
  [modalType.value === '7' ? 'dbDesc' : 'proName']: null,
})
const formRef = ref<FormInstance>()
const tableRef = ref<InstanceType<typeof Table> | null>(null)
const confirmLoading = ref<boolean>(false)
let columns = ref<Column[]>([])
const selectedKeys = ref<string[]>([])
const open = ref<boolean>(false)
const tableSelectedKeys = ref<boolean>(false)
const userId = ref<string>('')
const currentPageIds = ref<string[]>([])
const rowKeyMap: any = {
  '7': 'id',
  '8': 'projectId',
}
// watch(
//   () => props.requiredData,
//   (value) => {
//     modalType.value = props.request
//     if (modalType.value === 'user') {
//       formState.value.userGroupId = value?.userGroupId
//       formState.value.userGroupId && tableRef.value?.getTableData()
//       userId.value = value?.userGroupId
//     } else {
//       formState.value.roleId = value?.roleId
//       userId.value = value?.roleId
//       value?.roleId && tableRef.value?.getTableData()
//     }
//   },
//   { immediate: true, deep: true },
// )

const getData = computed(() => {
  console.log(
    '%c [ modalType ]-73',
    'font-size:13px; background:#55bccf; color:#99ffff;',
    modalType.value,
  )
  if (modalType.value === '7') {
    return reqGetDataSourceList
  } else if (modalType.value === '8') {
    return reqGetRoleProjectList
  } else if (modalType.value === '9') {
    return reqGetModelList
  } else if (modalType.value === '10') {
    return reqGetDataSourceList
  } else {
    return getRoleUserList
  }
})

const finish = (data: any) => {
  currentPageIds.value = data.map((item: any) => item[rowKeyMap[modalType.value]])
  // const selectedIds = data
  //   .filter((item: any) => !!item.isCheck)
  //   .map((ii: any) => ii[rowKeyMap[modalType.value]])
  // selectedKeys.value = Array.from(new Set([...selectedKeys.value, ...selectedIds]))
}

const getHasAuthList = async () => {
  const data = {
    roleId: formState.value.roleId,
    type: modalType.value === '7' ? 2 : 1,
  }
  const res = await reqGetHasAuthIds(data)
  selectedKeys.value = res.data.map((item: string) => String(item))
  console.log('%c [ res ]-120', 'font-size:13px; background:#530b14; color:#974f58;', res)
}

const onSelectChange = (record: any) => {
  const target = record[rowKeyMap[modalType.value]]
  if (selectedKeys.value.includes(target)) {
    selectedKeys.value = selectedKeys.value
      .filter((item: string) => item !== target)
      .map((item) => String(item))
  } else {
    selectedKeys.value = [...selectedKeys.value, target]
  }
  // selectedKeys.value = selectedRowKeys
}

const onSelectAll = (selected: boolean, selectedRows: any[]) => {
  const isSelect = selectedRows.filter(Boolean).length
  console.log(
    '%c [ selectedRows ]-144',
    'font-size:13px; background:#57f532; color:#9bff76;',
    selectedRows,
  )
  const ids = selectedRows.filter(Boolean).map((item: any) => item[rowKeyMap[modalType.value]])
  const filterList = selectedKeys.value
    .filter((item) => {
      if (currentPageIds.value.includes(item)) {
        return false
      } else {
        return true
      }
    })
    .map((item) => String(item))
  selectedKeys.value = isSelect
    ? [...new Set([...selectedKeys.value, ...ids])].map((item) => String(item))
    : filterList
}

const emits = defineEmits([])

// onBeforeMount(() => {
//   initColumns()
// })

async function initColumns(key: string) {
  console.log('%c [  ]-103', 'font-size:13px; background:#29b7fe; color:#6dfbff;', modalType.value)
  if (key === '7') {
    columns.value = dataSourceColumn
  } else if (key === '8') {
    columns.value = projectColumn
  } else if (key === '9') {
    columns.value = allocationUserDTO
  } else if (key === '10') {
    columns.value = allocationUserDTO
  } else {
    columns.value = []
  }
}

function cancel() {
  open.value = false
  tableSelectedKeys.value = false
}
async function ok() {
  if (modalType.value === '7' || modalType.value === '8') {
    const data = {
      roleId: formState.value.roleId,
      resourceType: modalType.value === '7' ? 2 : 1,
      resourceIds: selectedKeys.value.map((item: any) => String(item)),
    }
    await reqSaveRoleAuth(data)
    message.success('保存成功')
  } else if (modalType.value === '9') {
    return reqGetDataSourceList
  } else if (modalType.value === '10') {
    return reqGetDataSourceList
  } else {
    return getRoleUserList
  }

  // const res = await reqSaveRoleAuth
  open.value = false
  tableSelectedKeys.value = false
}

async function show(key: any, record: any) {
  console.log('%c [  ]-119', 'font-size:13px; background:#963ba7; color:#da7feb;', key, record)
  formState.value.roleId = record?.roleId
  formState.value[key === '7' ? 'dbDesc' : 'proName'] = null
  formState.value.pageIndex = 1
  formState.value.pageSize = 10
  modalType.value = key
  initColumns(key)
  tableSelectedKeys.value = true
  await resetForm()
  open.value = true
  // selectedKeys.value = []
  await getHasAuthList()
}

const resetForm = () => {
  formRef.value?.resetFields()
}

defineExpose({ show })
</script>
<style lang="less" scoped>
.nav {
  width: 100%;
  display: flex;
  flex: 1;
  justify-content: space-between;
  align-items: center;

  b {
    display: inline-block;
    font-size: 16px;
    font-weight: 600;
  }

  .ant-btn {
    margin-right: 20px;
  }
}

.db-item {
  display: flex;
  align-items: center;
  gap: 4px;
  img {
    width: 24px;
    height: 24px;
  }
}

// .tableOperate {
//   margin-top: 16px;
//   /deep/.ant-space,
//   /deep/.ant-space-item {
//     width: 100%;
//   }

//   /deep/.ant-space .ant-space-item:nth-child(2) {
//     display: none;
//   }

//   /deep/ .filter-box {
//     display: flex;
//     flex-wrap: unset;
//     justify-content: flex-end;
//   }
// }
// .dataSourceModel {
//   :deep('.filter-box') {
//     display: none;
//   }
// }
</style>
<style lang="less">
.dataSourceModel {
  padding-top: 20px;
  .filter-box {
    width: 100%;
    // display: none !important;
  }
}
</style>
