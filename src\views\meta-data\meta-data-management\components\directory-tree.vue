<template>
  <div class="directory-tree">
    <div class="header-div">
      <div class="title">
        <span>数据库目录</span>
      </div>
      <a-select
        v-model:value="datasourceId"
        placeholder="选择数据源连接"
        style="width: 100%"
        show-search
        :options="dataSourceList"
        :filter-option="filterOption"
        @change="handleChange"
      />
      <a-input-search
        v-model:value="searchValue"
        style="margin-bottom: 8px"
        placeholder="输入关键字搜索数据库"
      />
    </div>
    <div class="content-div">
      <a-tree
        :tree-data="filterTreeData"
        v-model:selectedKeys="selectedKeys"
        v-model:expandedKeys="expandedKeys"
        :fieldNames="{
          title: 'title',
          key: 'key',
          children: 'children',
        }"
        class="custom-tree"
        showIcon
        @select="handleSelect"
      >
        <template #title="{ title, dataRef }">
          <div class="tree-node-content">
            <type-icon
              v-if="dataRef?.dbTypMap"
              style="margin-right: 10px"
              :type="dataRef?.dbTypMap"
            />
            <span class="node-name">{{ title }}</span>
          </div>
        </template>
      </a-tree>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import {
  dbNameFun,
  PageListScameInfo,
  queryGetDbLevelList,
} from '@/api/matedata/metadatamanagement/metadatamanagement'
import TypeIcon from '@/views/data-asset/metadata-query/components/type-icon.vue'
import { useUserStore } from '@/stores/user'

const emit = defineEmits(['add-asset'])

const route = useRoute()
const router = useRouter()

const treeRef = ref()
const dataSourceList = ref<any[]>([])
const datasourceId = ref()

const selectedKeys = ref<string[]>([])
const expandedKeys = ref<string[]>([])
const treeData = ref<any>([])

const searchValue = ref<string>('')

const filterTreeData = computed(() => {
  if (!searchValue.value) {
    return treeData.value
  }
  return treeData.value.filter((item) => item.title.indexOf(searchValue.value) >= 0)
})

const filterOption = (input: string, option: any) => {
  return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
}

watch(searchValue, (newValue) => {
  console.log(treeRef.value)
})

onMounted(() => {
  const { id, datasourceId: initId } = route.query
  if (id === undefined || id === '' || id === null) {
    if (initId === undefined || initId === '' || initId === null) {
      return
    }
    fetchData(Number(initId))
    loadDataSourceList().then(() => {
      datasourceId.value = Number(initId)
    })
    return
  }
  fetchData(Number(id))
  loadDataSourceList().then(() => {
    datasourceId.value = Number(id)
  })
})

// 获取数据源列表

const loadDataSourceList = () => {
  const userStore = useUserStore()
  return PageListScameInfo({ ownerId: userStore.userInfo.loginAccount })
    .then(({ data }) => {
      const blackList = data.filter((item) =>
        [1, 5, 4, 6, 12, 13, 16, 19, 29].includes(item.dbType),
      )
      dataSourceList.value = blackList.map((item) => ({
        label: item.dbDesc,
        value: item.id,
      }))
    })
    .catch((error) => {
      console.error('获取数据源列表失败:', error)
    })
}
const handleChange = (id: number) => {
  datasourceId.value = id
  const { id: routerId, token } = route.query
  router.replace({
    path: route.path,
    query: { id: routerId, token },
  })
  expandedKeys.value = []
  selectedKeys.value = []
  // treeData.value = []
  nextTick(() => {
    fetchData(id, false)
  })
}

// 处理树形数据，递归设置 isLeaf
const processTreeData = (data: any[]) => {
  return data.map((item) => {
    const processedItem = { ...item }
    processedItem.isLeaf = true
    return processedItem
  })
}

const handleSelect = async (selectedKeysArr: string[], info: any) => {
  console.log('%c [ info ]-137', 'font-size:13px; background:#d6f131; color:#ffff75;', info)
  const data = {
    ...info.node,
    datasourceId: datasourceId.value,
    dataSourceName: dataSourceList.value.find((item) => item.value === datasourceId.value)?.label,
  }
  const dbSchema = data.dbTypMap
  const hasThreeLevelList = ['sqlserver', 'oracle', 'postgresql', 'greemplum', 'gbase', 'kingbase']
  const flag = hasThreeLevelList.includes(dbSchema)
  if (flag) {
    const json = JSON.stringify({
      id: data.datasourceId,
      dbName: data.key,
    })
    const res = await queryGetDbLevelList({ json })
    const list = treeData.value.map((item: any) => {
      if (item.key === data.key) {
        item.children = res.data.obj
        item.isLeaf = false
      } else {
        item.isLeaf = true
      }
      return item
    })
    treeData.value = list
    return
  }
  emit('add-asset', data)
}

const fetchData = async (id: number, needforceRequest: boolean = true) => {
  const { parentKey, dbName } = route.query
  try {
    const response = await dbNameFun({ id })
    console.log('Response:', response)
    if (response?.data) {
      const list = processTreeData(response.data.obj)
      console.log(
        '%c [ list ]-178',
        'font-size:13px; background:#c1d484; color:#ffffc8;',
        parentKey && dbName && needforceRequest,
        list,
      )
      if (parentKey && dbName && needforceRequest) {
        const dbTypMap = list.find((item: any) => item.key === dbName)?.dbTypMap
        const hasThreeLevelList = [
          'sqlserver',
          'oracle',
          'postgresql',
          'greemplum',
          'gbase',
          'kingbase',
        ]
        const flag = hasThreeLevelList.includes(dbTypMap)
        if (flag) {
          const json = JSON.stringify({
            id,
            dbName,
          })
          const res = await queryGetDbLevelList({ json })
          const tempArr = list.map((item: any) => {
            if (item.key === dbName) {
              item.children = res.data.obj
              item.isLeaf = false
            } else {
              item.isLeaf = true
            }
            return item
          })
          const curNode = (res.data.obj as unknown as Array<any>).find((i) => i.key === parentKey)
          console.log('%c [  ]-189', 'font-size:13px; background:#009cd5; color:#44e0ff;', curNode)
          treeData.value = tempArr
          expandedKeys.value = [dbName as string]
          selectedKeys.value = [parentKey as string]
          // 造一个data
          const data = {
            datasourceId: id,
            parent: {
              key: dbName,
            },
            ...curNode,
          }
          emit('add-asset', data)
        }
      } else {
        treeData.value = list
        if (dbName && needforceRequest) {
          selectedKeys.value = [dbName as string]
          const curNode = list.find((i) => i.key === dbName)
          // 造一个data
          const data = {
            datasourceId: id,
            parent: undefined,
            ...curNode,
          }
          emit('add-asset', data)
          return
        }
        emit('add-asset')
      }
      // selectedKeys.value = [dbName as string]
    }
  } catch (error) {
    console.error('Error fetching data:', error)
  }
}

defineExpose({
  fetchData,
})
</script>

<style lang="less" scoped>
.directory-tree {
  height: 100%;
  display: flex;
  flex-direction: column;

  .header-div {
    display: flex;
    flex-direction: column;
    gap: 12px 0;
  }

  .content-div {
    flex: auto;
    overflow-y: auto;
  }

  .custom-tree {
    margin-top: 10px;
    :deep(.ant-tree-node-content-wrapper) {
      width: 100%;
      &:hover {
        background-color: #f5f5f5;
      }
    }
    :deep(.ant-tree-node-selected) {
      background-color: #e6f7ff;
    }
    :deep(.ant-tree-title) {
      width: 100%;
    }
    .tree-node-content {
      width: 100%;
      padding-right: 10px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .node-name {
        flex: 1;
        white-space: nowrap;
      }

      .node-actions {
        display: flex;
        align-items: center;
        gap: 8px;

        .action-icon {
          font-size: 16px;
          color: #666;
          cursor: pointer;

          &:hover {
            color: #1890ff;
          }
        }
      }
    }
  }

  :deep(.ant-tree) {
    .ant-tree-switcher {
      // display: none;
    }
    .ant-tree-treenode {
      width: 100%;
      height: 40px;
      display: flex;
      align-items: center;
      .ant-tree-switcher {
        line-height: 40px;
      }
      .ant-tree-title {
        width: 100%;
      }
      .ant-tree-node-content-wrapper {
        width: 100%;
        height: 40px;
        display: flex;
        align-items: center;
      }
    }
  }

  .title {
    margin-right: auto;
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 24px;
    display: flex;
    justify-content: space-between;
  }
}
</style>
