<template>
  <div class="filed-table">
    <div class="content-div">
      <cardBox :title="`文件目录：${name}`">
        <template #headerRight>
          <a-upload style="width: 100%" :showUploadList="false" :customRequest="customUpload">
            <a-button type="primary">
              <upload-outlined></upload-outlined>
              上传文件
            </a-button>
          </a-upload>
        </template>
        <div class="cardbox-content">
          <a-tabs v-model:activeKey="activeKey" class="table-detail-tabs">
            <a-tab-pane key="1">
              <template #tab>
                <span> 文件列表 </span>
              </template>
              <a-input
                v-model:value="filterValue"
                :placeholder="'搜索文件列表...'"
                style="margin-bottom: 8px; min-width: 300px; width: 300px"
              >
                <template #prefix>
                  <SearchOutlined />
                </template>
              </a-input>
              <a-table :columns="columns" :data-source="filterData" :pagination="false">
                <template #bodyCell="{ column, record }">
                  <template v-if="column.key === 'operate'">
                    <a-space>
                      <a-popconfirm
                        title="是否确定下载当前文件?"
                        ok-text="是"
                        cancel-text="否"
                        @confirm="downloadItem(record)"
                      >
                        <a>下载</a>
                      </a-popconfirm>
                    </a-space>
                  </template>
                </template>
              </a-table>
            </a-tab-pane>
          </a-tabs>
        </div>
      </cardBox>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import cardBox from '@/components/card-box/card-box.vue'
import {
  downloadFileApi,
  uploadFileApi,
} from '@/api/services/data-asset/distributed-file-management'
import { SearchOutlined } from '@ant-design/icons-vue'

const activeKey = ref('1')
// 表格数据
const props = defineProps<{
  filedTableData?: Record<string, any>
  params?: any
}>()

const emit = defineEmits(['refreshFiledTable'])

// 选中的目录名称
const name = computed(() => props.params?.treeNode?.name ?? '')
const columns = ref<any[]>([
  {
    dataIndex: 'path',
    title: '文件名称',
    key: 'path',
    width: 160,
  },
  {
    dataIndex: 'modification_time',
    title: '修改时间',
    key: 'modification_time',
    width: 160,
  },
  {
    dataIndex: 'group',
    title: '所属组',
    key: 'group',
    width: 100,
  },
  {
    dataIndex: 'access_time',
    title: '最后访问时间',
    key: 'access_time',
    width: 160,
  },
  {
    dataIndex: 'owner',
    title: '所有者',
    key: 'owner',
    width: 100,
  },
  {
    dataIndex: 'operate',
    title: '操作',
    key: 'operate',
    width: 100,
  },
])
const filterValue = ref<string>('')
const filterData = computed(() => {
  return filterValue.value
    ? props.filedTableData?.filter((column: any) => column.path.includes(filterValue.value))
    : props.filedTableData
})
// 自定义上传
const customUpload = async (options: any) => {
  const { file, onSuccess, onError } = options
  const formData = new FormData()
  formData.append('file', file)
  formData.append('hdfsPath', props.params.fullPath)
  formData.append('id', props.params.id)
  try {
    const res = await uploadFileApi(formData)
    onSuccess(res.data) // 通知上传成功
    console.log('通知上传成功:', props.params)
    emit('refreshFiledTable')
  } catch (err) {
    onError(err) // 通知上传失败
  }
}

function getFileExtension(filePath: string) {
  // 处理空路径
  if (!filePath) return ''

  // 处理以点结尾的路径（如 "file."）
  if (filePath.endsWith('.')) return ''

  // 处理隐藏文件（如 ".gitignore"）
  const fileName = filePath.split('/').pop() ?? ''
  const dotIndex = fileName?.lastIndexOf('.') ?? 0

  // 没有后缀的情况
  if (dotIndex <= 0) return '' // dotIndex <= 0 排除隐藏文件情况

  return fileName.substring(dotIndex + 1)
}

// 根据文件后缀映射从内容类型
const contentTypenMap = {
  '.xls': 'application/vnd.ms-excel',
  '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  '.doc': 'application/msword',
  '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  '.pdf': 'application/pdf',
  '.jpg': 'image/jpeg',
  '.png': 'image/png',
  '.txt': 'text/plain',
  '.json': 'application/json',
  '.csv': 'text/csv',
}

// 下载
const downloadItem = async (record: any) => {
  if (!record.fullPath && !props.params.id) return
  const params = {
    id: props.params.id,
    hdfsPath: record.fullPath,
  }
  try {
    const res = await downloadFileApi(params)
    // 获取文件后缀（默认为.txt）
    const fileExtension = getFileExtension(record.fullPath)
      ? `.${getFileExtension(record.fullPath)}`
      : '.txt'
    // 获取文件后缀（默认为.txt）
    const contentType =
      Object.entries(contentTypenMap).find(([type]) => fileExtension.includes(type))?.[1] || ''
    const fullName = record.fullPath.split('/').pop()

    // 创建 Blob
    const blob = new Blob([res], {
      type: contentType,
    })

    // 创建下载链接
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.style.display = 'none'
    a.href = url
    a.download = `${fullName}`

    // // 添加到 DOM 并触发点击
    document.body.appendChild(a)
    a.click()

    // 清理
    setTimeout(() => {
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)
    }, 100)
  } catch (error) {
    console.error('下载失败:', error)
    // 显示错误提示
  }
}

// onMounted(() => {
//   console.log('params:',props.params)
// })
</script>

<style lang="less" scoped>
.filed-table {
  height: 100%;
  .content-div {
    padding: 12px 12px 0 12px;
    height: calc(100% - 48px);
  }
  .content-div .card-box-comp .box-header-g {
    margin-bottom: 0px;
  }
  .cardbox-content {
    position: relative;
    width: 100%;
    height: 100%;
  }
  .upload-btn {
    position: absolute;
    right: 0;
    top: 6px;
  }
  :deep(.card-box-comp .box-header-g) {
    margin-bottom: 0;
  }
}
</style>
