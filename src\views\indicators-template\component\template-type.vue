<template>
  <div class="template-type-list">
    <!-- :table-config="{
        rowKey: 'businessId',
        rowSelection: {
          type: 'radio',
          onChange: handleSelect,
        }
      }" -->
    <Table
      :columns="columns"
      :getData="handleSearch"

      :pagination="pagination"
      :searchFormState="form"
      ref="tableRef"
    >
      <template #operate>
        <a-button type="primary" shape="default" @click="handleAdd"> 添加 </a-button>
      </template>
      <template #search>
        <a-form-item label="业务名称" name="businessName">
          <a-input v-model:value="form.businessName" placeholder="请输入业务名称" />
        </a-form-item>
      </template>
      <template #bodyCell="data">
        <template v-if="data.column.dataIndex === 'action'">

          <a-button size="small" type="link" @click="handleEdit(data.record)">编辑</a-button>
          <a-popconfirm
            title="是否确认删除"
            @confirm="handleDelete(data.record)"
          >
            <a-button size="small" type="link" danger>删除</a-button>
          </a-popconfirm>
        </template>
      </template>
    </Table>
  </div>
  <a-modal v-model:open="modalVisible" title="新增业务类型" @ok="handleAddModalOk">
    <a-form :model="addForm" ref="formRef" :layout="'vertical'">
      <a-form-item label="业务名称" name="businessName" required>
        <a-input v-model:value="addForm.businessName" placeholder="请输入业务名称" />
      </a-form-item>
      <a-form-item label="备注" name="remark">  
        <a-textarea v-model:value="addForm.remark" placeholder="请输入备注" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Table } from '@fs/fs-components'
import { getBusinessPageList, indexbusinessDelete, indexbusinessInsert, indexbusinessUpdate } from '@/api/indexmanage/bussinesmanage/bussinesmanage'
import { message } from 'ant-design-vue'

const emit = defineEmits(['select'])

const tableRef = ref<any>()
const form = ref({
  businessName: ''
})
const addForm = ref({
  businessId: '',
  businessName: '',
  remark: ''
})
const modalVisible = ref(false)
const columns = [
  {
    title: '类型名称',
    dataIndex: 'businessName',
  },
  {
    title: '备注',
    dataIndex: 'remark',
  },
  {
    title: '创建人',
    dataIndex: 'createUser',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
  },
  {
    title: '操作',
    dataIndex: 'action',
    fix: 'right',

  },
]
const pagination = ref({
  pageSize: 10,
  pageIndex: 1,
})

const handleAdd = () => {
  modalVisible.value = true
  addForm.value = {
    businessId: '',
    businessName: '',
    remark: ''
  }
}

const handleEdit = (item: string) => {
  Object.assign(addForm.value, item)
  modalVisible.value = true
}

const handleDelete = (item: any) => {
  indexbusinessDelete({
    businessId: item.businessId
  }).then(() => {
    message.success('删除成功')
    tableRef.value?.getTableData()
  })
}

const handleAddModalOk = () => {
  let req = null
  if (addForm.value.businessId) {
    req = indexbusinessUpdate({
      ...addForm.value,
      businessId: +addForm.value.businessId
    })
  } else {
    req = indexbusinessInsert(addForm.value)
  }
  req.then(() => {
    message.success('新增成功')
    modalVisible.value = false
    tableRef.value?.getTableData()
  })
}

const handleSelect = (id: number, item: any) => {
  emit('select', item)
}

function handleSearch() {
  return getBusinessPageList({
    pageIndex: pagination.value.pageIndex,
    pageSize: pagination.value.pageSize,
    ...form.value
  })
}

</script>

<style lang="less" scoped>
.template-type-input {
  display: flex;
  gap: 20px;
}
.template-type-list {
  padding: 20px 0;
  min-height: 200px;
}
.ant-list .ant-list-item {
  cursor: pointer;
  // padding: 10px 0;
  &:hover {
    background-color: #f5f5f5;
  }
}
.active {
  background-color: #dcdcdc;
}
</style>
