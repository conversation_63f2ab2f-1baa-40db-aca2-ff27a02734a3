<template>
  <div class="page-full">
    <a-flex justify="space-between" style="padding-bottom: 10px">
      <div class="title mb-20px" text="20px #000000d9">实时任务监控</div>
      <a-space :size="8">
        <a-button type="primary" @click="showMore = !showMore">
          <template #icon v-if="showMore"><UpOutlined /></template>
          <template #icon v-else><DownOutlined /></template>
          {{ showMore ? '收起' : '搜索' }}
        </a-button>
        <a-button type="primary" @click="batchCancelTask()">批量取消</a-button>
        <a-button type="primary" @click="batchCheck()">批量校验</a-button>
        <a-button type="primary" @click="handleAdd()">添加校验</a-button>
      </a-space>
    </a-flex>

    <div class="page-main" ref="pageMain">
      <div ref="pageTop">
        <div v-show="showMore" class="search-item">
          <a-form
            class="query-container"
            ref="queryForm"
            style="margin-top: 0px"
            :label-col="{ span: 6 }"
            :wrapper-col="{ span: 18 }"
          >
            <a-row :gutter="32">
              <a-col :span="6">
                <!-- 源表类型 -->
                <a-form-item label="作业流">
                  <a-input
                    v-model:value="param.name"
                    placeholder="请输入作业流"
                    @pressEnter="handleQuery"
                  ></a-input>
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item label="状态">
                  <a-select
                    v-model:value="param.taskStatus"
                    showSearch
                    :allowClear="true"
                    @change="handleQuery"
                    :filter-option="selectInput"
                    placeholder="请选择状态"
                  >
                    <a-select-option
                      v-for="(option, optKey) in taskStatusList"
                      :key="optKey"
                      :value="option.value"
                      :label="option.label"
                      >{{ option.label }}</a-select-option
                    >
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="6"> </a-col>
              <a-col :span="6">
                <a-space :size="8" style="float: right">
                  <a-button @click="handleQuery" type="primary">查询</a-button>
                  <a-button @click="handleReset">重置</a-button>
                </a-space>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </div>
      <div class="list-content">
        <div class="table-card">
          <a-table
            ref="table"
            :columns="flowColumns"
            :data-source="ruleList"
            :scroll="{ y: 'calc(100vh - 250px)' }"
            :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
            row-key="id"
            :pagination="false"
          >
            <template #bodyCell="{ text, column, record }">
              <template v-if="column.dataIndex === 'name'">
                <a :title="text" @click="openFlow(record)"> {{ text || '-' }}</a>
              </template>
              <template v-if="column.dataIndex === 'taskStatus'">
                <div v-if="record.taskStatus === '1'">
                  <img
                    class="img-icon"
                    :src="statusImg(record.taskStatus)"
                    style="width: 25px"
                    alt=""
                  />执行中
                </div>
                <div v-else-if="record.taskStatus === '2'">
                  <img
                    class="img-icon"
                    :src="statusImg(record.taskStatus)"
                    style="width: 25px"
                    alt=""
                  />成功
                </div>
                <div v-else-if="record.taskStatus === '3'">
                  <img
                    class="img-icon"
                    :src="statusImg(record.taskStatus)"
                    style="width: 25px"
                    alt=""
                  />待执行
                </div>
                <div v-else-if="record.taskStatus === '-1'">
                  <img
                    class="img-icon"
                    :src="statusImg(record.taskStatus)"
                    style="width: 25px"
                    alt=""
                  />失败
                </div>
                <div v-else>
                  <img
                    class="img-icon"
                    :src="statusImg(record.taskStatus)"
                    style="width: 20px; margin-right: 5px"
                    alt=""
                  />未运行
                </div>
              </template>
              <template v-if="column.dataIndex === 'isMonitor'">
                <div v-if="record.isMonitor === 0">不监控</div>
                <div v-else-if="record.isMonitor === 1">监控</div>
                <div v-else>-</div>
              </template>
              <template v-if="column.dataIndex === 'latestTime'">
                <div v-if="record.latestTime">{{ record.latestTime }}</div>
                <div v-else>-</div>
              </template>
              <template v-if="column.dataIndex === 'duration'">
                <div v-if="record.duration">{{ record.duration }}</div>
                <div v-else>-</div>
              </template>
              <template v-if="column.dataIndex === 'action'">
                <a-space>
                  <a-button size="small" type="link" @click="getResult(record)">查看结果</a-button>
                  <a-button size="small" type="link" @click="onEditCron(record)">定时器</a-button>
                  <a-button size="small" type="link" @click="onCheck([record.id])">校验</a-button>
                  <a-button size="small" type="link" @click="onCancel([record.id])">取消</a-button>
                </a-space>
              </template>
            </template>
          </a-table>
          <!-- 分页卡片-->
          <div class="card-pagination">
            <a-pagination
              size="small"
              show-quick-jumper
              show-size-changer
              @change="handelPageChange"
              v-model="pagination.pageIndex"
              :total="pagination.total"
              @showSizeChange="handelShowSizeChange"
              :defaultPageSize="pagination.pageSize"
              :pageSizeOptions="pagination.pageSizeOptions"
              :showTotal="
                (total: number, range: number[]) =>
                  `显示 ${range[0]} 到 ${range[1]} 条 , 共 ${total} 条记录，已选中 ${selectedRowKeys.length} 条数据`
              "
            />
          </div>
        </div>
      </div>
      <addRealTimeTaskModel
        ref="addRealTimeTask"
        :title="modalTitle"
        :type="ModelType"
        @callback="getList"
      >
      </addRealTimeTaskModel>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue'
import {
  queryRealTimeFlow,
  getAllRealTimeFlow,
  cancelBatch,
  mulGenCheckTaskByFlowIdFun,
} from '@/api/services/monitor-maintenance/data-quality'
import addRealTimeTaskModel from './components/add-realtime-task-model.vue'
import { message } from 'ant-design-vue'
import { useRouter } from 'vue-router'
import confirm from 'ant-design-vue/es/modal/confirm'
import unrun from '@/assets/unrun.svg'
import running from '@/assets/running.svg'
import success from '@/assets/success.svg'
import waiting from '@/assets/waiting.svg'
import failure from '@/assets/failure.svg'
import { UpOutlined, DownOutlined } from '@ant-design/icons-vue'
import { useUserStore } from '@/stores/user'

const userStore = useUserStore()

const router = useRouter()
const addRealTimeTask = ref()
const showMore = ref(true) // 是否展开页面全部查询信息

const ownerId = ref<string>(userStore.userInfo.loginAccount)
const param = reactive({
  // 查询参数
  name: '',
  taskStatus: undefined,
})
// const dropdownVisible = ref<boolean>(false)
const ruleList = ref<any[]>([])
const flowColumns = ref<any[]>([
  {
    title: 'ID',
    dataIndex: 'id',
    width: 80,
    show: true,
    scopedSlots: { customRender: 'id' },
  },
  {
    title: '作业流',
    dataIndex: 'name',
    show: true,
    // width: 120,
    scopedSlots: { customRender: 'name' },
  },
  {
    title: '作业流状态',
    dataIndex: 'taskStatus',
    show: true,
    // width: 120,
    scopedSlots: { customRender: 'taskStatus' },
  },
  {
    title: '是否监控',
    dataIndex: 'isMonitor',
    show: true,
    // width: 120,
    scopedSlots: { customRender: 'isMonitor' },
  },
  {
    title: '上一次执行时间',
    dataIndex: 'latestTime',
    show: true,
    // width: 120,
    scopedSlots: { customRender: 'latestTime' },
  },
  {
    title: '下次校验时间',
    dataIndex: 'nextTime',
    show: true,
    // width: 120,
    scopedSlots: { customRender: 'nextTime' },
  },
  {
    title: '数据延迟时间',
    dataIndex: 'duration',
    show: true,
    // width: 120,
    scopedSlots: { customRender: 'duration' },
  },
  {
    title: '定时器',
    dataIndex: 'cronExpression',
    show: true,
    // width: 100,
    scopedSlots: { customRender: 'cronExpression' },
  },
  {
    title: '所有者',
    dataIndex: 'ownerId',
    show: true,
    // width: 100,
    scopedSlots: { customRender: 'ownerId' },
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: 280,
    scopedSlots: { customRender: 'action' },
  },
])
const ModelType = ref<string>('task')
const editCron = ref<any>(null)
const libraryList = ref<any[]>([])
const selectedRowKeys = ref<any[]>([])

const showAddTask = ref<boolean>(false)
const queryFlowId = ref<any>(null)
const modalTitle = ref<any>(null)
const flowList = ref<any[]>([])
const taskStatusList = ref<any[]>([
  {
    label: '执行中',
    value: '1',
  },
  {
    label: '待执行',
    value: '3',
  },
  {
    label: '成功',
    value: '2',
  },
  {
    label: '失败',
    value: '-1',
  },
])
const isMonitorList = ref<any[]>([
  {
    label: '否',
    value: 0,
  },
  {
    label: '是',
    value: 1,
  },
])
const pagination = reactive({
  total: 0,
  size: 'small',
  pageIndex: 1,
  pageSize: 24, // 每页中显示多少条数据
  showSizeChanger: true,
  pageSizeOptions: ['12', '24', '36', '48'], // 每页中显示的数据
  showQuickJumper: true, // 是否可以快速跳转至某页
})

onMounted(() => {
  getList()
  getFlowList()
})

//  跳转作业流
const openFlow = (record: any) => {
  router.push({
    path: '/jobFlow/jobFlowDefinition',
    query: { id: record.id, type: 'config', name: record.name },
  })
}

const getFlowList = () => {
  getAllRealTimeFlow({ ownerId: ownerId.value }).then((res: any) => {
    if (res.data.length) {
      flowList.value = res.data
    }
  })
}

// 复选框选择变化
const onSelectChange = (selectedRowKeyst: Array<any>) => {
  selectedRowKeys.value = selectedRowKeyst
}

const handleChangeDbId = (item: any) => {
  item.dbName = undefined
  item.dbSchema = undefined
  item.dbTable = undefined
  item.dbFilter = undefined
  item.dbNameList = []
  item.dbSchemaList = []
  item.tableNameList = []
  item.dbColumnFieldList = []
}

// 筛选下拉框
const selectInput = (inputValue: string, option: any) => {
  return option.label.toLowerCase().indexOf(inputValue.toLowerCase()) >= 0
}

//  新增校验
const handleAdd = () => {
  addRealTimeTask.value.showModel()
  ModelType.value = 'task'
  editCron.value = null
  modalTitle.value = '添加校验'
}

//  编辑校验
const onEdit = (record: any) => {
  addRealTimeTask.value.showModel(record)
  modalTitle.value = '编辑校验'
}

//  批量取消
const batchCancelTask = () => {
  if (selectedRowKeys.value.length) {
    onCancel(selectedRowKeys.value)
  } else {
    message.error('请勾选需要批量取消的校验任务')
  }
}
const onEditCron = (record: any) => {
  ModelType.value = 'cron'
  editCron.value = record
  addRealTimeTask.value.showModel(record)
}

//  取消校验
const onCancel = (ids: any) => {
  confirm({
    title: '提示',
    content: '确定要取消监控吗 ?',
    onOk: () => {
      return cancelBatch({
        ids: ids,
      }).then((res: any) => {
        if (res.data.code === 1) {
          getList()
          message.success(res.msg)
        } else {
          message.error(res.data.message)
        }
      })
    },
    okText: '确认',
    cancelText: '取消',
  })
}
const batchCheck = () => {
  if (selectedRowKeys.value.length) {
    onCheck(selectedRowKeys.value)
  } else {
    message.error('请勾选需要批量校验的任务')
  }
}

//  校验
const onCheck = (ids: any) => {
  mulGenCheckTaskByFlowIdFun({
    list: ids,
  }).then((res: any) => {
    if (res.data.code === 1) {
      message.success(res.data.message)
      getList()
    } else {
      message.error(res.data.message)
    }
  })
}

/* 获取列表数据 */
const getList = () => {
  let params: Record<string, any> = {
    ownerId: ownerId.value,
    pageIndex: pagination.pageIndex,
    pageSize: pagination.pageSize,
  }
  if (param.name !== '') {
    params.name = param.name
  }
  if (param.taskStatus) {
    params.taskStatus = param.taskStatus
  }
  queryRealTimeFlow(params).then((res) => {
    let data = res.data.records
    pagination.total = res.data.totalRecords
    ruleList.value = data
  })
}
const handelPageChange = (pageIndex: number, pageSize: number) => {
  // 切换分页
  pagination.pageIndex = pageIndex
  pagination.pageSize = pageSize
  getList()
}

// 切换分页大小
const handelShowSizeChange = (pageIndex: number, pageSize: number) => {
  pagination.pageIndex = pageIndex
  pagination.pageSize = pageSize
  getList()
}

// 查询
const handleQuery = () => {
  pagination.pageIndex = 1
  getList()
}
const handleReset = () => {
  param.name = ''
  param.taskStatus = undefined

  pagination.pageIndex = 1
  pagination.pageSize = 24
}
//  查询结果
const getResult = (record: any) => {
  router.push({
    path: '/dataCheck/realTimeTableQuery',
    query: {
      id: record.id,
      pageName: `实时表监控-${record.id}`,
    },
  })
}

// 状态图标
const statusImg = (val: string | number) => {
  let result = unrun
  switch (val) {
    case '1':
      result = running
      break
    case '2':
      result = success
      break
    case '3':
      result = waiting
      break
    case '-1':
      result = failure
      break
  }
  return result
}
</script>

<style scoped lang="less">
:deep(.ant-table-wrapper) {
  margin: 0 !important;
}
:deep(.ant-table-row-level-1) {
  background-color: #f6ffed;
}
:deep(.ant-table-row-level-2) {
  background-color: #fff1f0;
}
:deep(.ant-table-row-level-3) {
  background-color: #fffbe6;
}
:deep(.ant-form-item) {
  margin-bottom: 0px;
}
.search-item {
  margin-bottom: 16px;
}

.query-container :deep(.ant-row .ant-form-item .ant-form-item-label) {
  display: none;
}
.img-icon {
  position: relative;
  top: 5px;
}
</style>
