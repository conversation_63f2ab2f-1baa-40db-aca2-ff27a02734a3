<template>
  <a-modal
    :open="exportModal"
    :title="title"
    :confirm-loading="confirmLoading"
    width="800px"
    @ok="exportModalOk"
    @cancel="modalCancel"
    ref="modalRef"
    :destroyOnClose="true"
  >
    <a-form>
      <a-form-item label="选择系统">
        <a-select
          v-model:value="systemSelect"
          placeholder="请选择系统"
          @select="systemSelectHandle"
        >
          <a-select-option v-for="item in modalSystem" :value="item.systemId" :key="item">
            {{ item.systemName }}
          </a-select-option>
        </a-select>
      </a-form-item>
    </a-form>
    <a-tree
      checkable
      :tree-data="exportTreeData"
      :field-names="fieldNames"
      v-if="exportTreeData.length > 0"
      v-model:checkedKeys="checkedKeys"
    >
      <template #title="{ ...record }">
        <span @click="openForm(record)">{{ record.menuName }} </span>
      </template>
    </a-tree>
    <div v-else>
      <a-empty description="暂无数据" />
    </div>
    <a-modal v-model:open="formVisiable" title="设置表单权限" @ok="handleOk" width="800px">
      <template v-if="menuDtl?.grids && menuDtl?.grids.length">
        <a-tabs v-model:activeKey="activeTabKey" @change="handleTabChange">
          <a-tab-pane v-for="grid in menuDtl.grids" :key="grid.gridId" :tab="grid.gridName">
            <div class="transfer-container">
              <a-transfer
                :list-style="{
                  width: '250px',
                  height: '300px',
                }"
                :dataSource="grid.gridColumns"
                :select-all-labels="['展示字段', '隐藏字段']"
                :titles="['', '']"
                :targetKeys="getSelectedColumns(grid.gridId)"
                :render="(item: any) => item.columnName + '(' + item.columnCaption + ')'"
                :rowKey="(record: any) => record.columnId"
                @change="(targetKeys: any) => handleColumnTransferChange(targetKeys, grid.gridId)"
              />
            </div>
          </a-tab-pane>
        </a-tabs>
      </template>
      <div style="text-align: center; margin: 30px" v-else>暂无表单权限</div>
    </a-modal>
  </a-modal>
</template>
<script lang="ts" setup>
import { message, type TreeProps } from 'ant-design-vue'
import { ref, watch } from 'vue'
import { viewMenuDtl, newRoleHiddenGrid, getRoleHiddenGridList } from '@/api/services/permission'
const modalRef = ref(null)
const systemSelect = ref<string[]>([])
const checkedKeys = ref<string[]>([])
interface System {
  systemId: string
  systemName: string
}

interface TreeNodeProps {
  exportModal: boolean
  title: string
  confirmLoading: boolean
  modalSystem: System[]
  exportTreeData: any[]
  checkedDataRoleId: string
  checkedData: {
    [x: string]: any
  }
  fNames: {
    [x: string]: any
  }
}

const formVisiable = ref(false)
const formItemList = ref<any[]>([])
const gridIndex = ref(0)
const props = withDefaults(defineProps<TreeNodeProps>(), {
  confirmLoading: false,
  exportModal: false,
  title: '',
  MenusModalClear: false,
  checkedDataRoleId: '',
})

const fieldNames = ref<TreeProps['fieldNames']>({
  children: 'menuList',
  title: 'menuName',
  key: 'menuId',
})

const roleId = ref('')

watch(
  () => props.fNames,
  (value: any) => {
    if (value) {
      fieldNames.value = {
        children: value?.children,
        title: value?.title,
        key: value?.key,
      }
    }
  },
  { immediate: true },
)

watch(
  () => props.exportTreeData,
  (value: any) => {
    if (value && value.length > 0) {
      checkedKeys.value = []
      addCheckedKeys(value, checkedKeys.value)
    }
  },
  { immediate: true },
)

const emits = defineEmits(['systemSelectEmitHandle', 'exportModalHandleOk', 'exportModalCancel'])
const menuDtl = ref<Record<string, any>>({})

interface GridColumn {
  columnId: string
  columnName: string
  checked: boolean
}

// Watch for changes in gridColumns to update parent checkbox
watch(
  () => menuDtl.value.grids?.[gridIndex.value]?.gridColumns,
  (columns: GridColumn[] | undefined) => {
    if (columns && columns.length > 0) {
      const allChecked = columns.every((col: GridColumn) => col.checked)
      menuDtl.value.grids[gridIndex.value].checked = allChecked
    }
  },
  { deep: true },
)

function handleOk() {
  const grids: any[] = []

  if (!menuDtl.value?.grids || menuDtl.value?.grids?.length === 0) {
    formVisiable.value = false
    return
  }
  Object.keys(selectedColumns.value).forEach((item) => {
    const list = selectedColumns.value[item]
    const gridColumns: any[] = []
    list.forEach((item: GridColumn) => {
      gridColumns.push({ columnId: item })
    })
    grids.push({
      gridId: item,
      gridColumns,
    })
  })
  const { roleId } = props.checkedData
  const obj = {
    roleId,
    grids,
  }
  newRoleHiddenGrid(obj).then((res: any) => {
    if (res.code === '000000') {
      message.success('保存成功')
      formVisiable.value = false
    }
  })
}

function openForm(record: any) {
  const params = {
    tenantId: 'system',
    menuId: record.menuId,
  }
  viewMenuDtl(params).then((res: any) => {
    if (res.code === '000000') {
      menuDtl.value = res.data
      formVisiable.value = true
      if (res.data?.grids && res.data?.grids.length > 0) {
        formItemList.value = res.data?.grids[0].gridColumns
        res.data?.grids.forEach((item: any) => {
          const { roleId } = props.checkedData
          getRoleHiddenGridList({
            gridId: item.gridId,
            roleId: roleId,
          }).then((res: any) => {
            res.data.forEach((items: any) => {
              selectedColumns.value[items.gridId].push(items.columnId)
            })
          })
        })
      }
      console.log('🚀 ~ res.data.forEach ~ selectedColumns.value:', selectedColumns.value)
    }
  })
}

function exportModalOk() {
  let uniqueArray: any[] = []
  checkedKeys.value.map((item: any) => {
    uniqueArray.push(findParentMenuIds(props?.exportTreeData, item))
  })
  const uniqueData = flattenAndRemoveDuplicatesByMenuId(uniqueArray)
  if (checkedKeys.value.length > 0) {
    emits('exportModalHandleOk', uniqueData)
    systemSelect.value = []
  } else {
    message.info('请选择菜单!')
  }
}

function findParentMenuIds(tree: any[], targetMenuId: string, path = []): any {
  for (let node of tree) {
    // 检查当前节点的 menuId 是否与目标 menuId 匹配
    if (node.menuId === targetMenuId) {
      // 返回包含目标节点的完整路径
      const current = { menuId: node.menuId, isHalfChecked: 0 }
      const MenuFormat = path.map((item: any) => {
        return {
          menuId: item,
          isHalfChecked: 1,
        }
      })
      return [MenuFormat, current]
    }
    // 如果当前节点有子节点，递归查找
    if (node.datas && node.datas.length > 0) {
      //@ts-ignore
      const result = findParentMenuIds(node.datas, targetMenuId, [...path, node.menuId])
      if (result.length > 0) {
        return result // 如果在子树中找到，返回结果
      }
    }
  }
  // 如果没有找到目标节点，返回空数组
  return []
}

function flattenAndRemoveDuplicatesByMenuId(arr: any[]) {
  // 创建一个Set来存储已经遇到的menuId
  const menuIdSet = new Set()
  // 创建一个数组来存储去重后的对象
  const result: any[] = []

  // 递归函数，用于遍历嵌套数组和对象
  function traverse(items: any[]) {
    for (const item of items) {
      if (Array.isArray(item)) {
        // 如果当前项是数组，则递归遍历
        traverse(item)
      } else if (typeof item === 'object' && item !== null) {
        // 如果当前项是对象，并且具有menuId属性
        if (item.menuId && !menuIdSet.has(item.menuId)) {
          // 如果menuId还未出现过，则添加到结果集和Set中
          menuIdSet.add(item.menuId)
          result.push(item)
        }
      }
    }
  }
  // 调用递归函数开始遍历
  traverse(arr)
  // 返回去重后的一维数组
  return result
}

function addCheckedKeys(data: any[], checkedKeys: any[]) {
  data.forEach((item) => {
    if (item.isHalfChecked === 0) {
      checkedKeys.push(item.menuId)
    }
    if (item.datas && item.datas.length > 0) {
      addCheckedKeys(item.datas, checkedKeys)
    }
  })
}

function modalCancel() {
  emits('exportModalCancel')
  setTimeout(() => {
    systemSelect.value = []
  }, 300)
}

function systemSelectHandle(key: string) {
  emits('systemSelectEmitHandle', key)
}

const activeTabKey = ref('')
const selectedColumns = ref<Record<string, string[]>>({})

// 处理标签页切换
function handleTabChange(key: string) {
  activeTabKey.value = key
  gridIndex.value = menuDtl.value.grids.findIndex((grid: any) => grid.gridId === key)
}

// 获取指定表单的已选字段
function getSelectedColumns(gridId: string): string[] {
  return selectedColumns.value[gridId] || []
}

// 处理字段转移变化
function handleColumnTransferChange(targetKeys: string[], gridId: string) {
  selectedColumns.value[gridId] = targetKeys

  // 更新选中状态
  const grid = menuDtl.value.grids.find((g: any) => g.gridId === gridId)
  if (grid?.gridColumns) {
    grid.gridColumns.forEach((col: any) => {
      col.checked = targetKeys.includes(col.columnId)
    })

    // 更新表单选中状态
    grid.checked = grid.gridColumns.every((col: any) => col.checked)
  }
}

// 初始化数据时设置选中状态
watch(
  () => menuDtl.value.grids,
  (grids) => {
    if (grids && grids.length > 0) {
      // 设置默认选中的标签页
      activeTabKey.value = grids[0].gridId
      gridIndex.value = 0

      // 初始化每个表单的已选字段
      grids.forEach((grid: any) => {
        selectedColumns.value[grid.gridId] = grid.gridColumns
          .filter((col: any) => col.checked)
          .map((col: any) => col.columnId)
      })
    }
  },
  { immediate: true, deep: true },
)
</script>
<style lang="less" scoped>
.content-div {
  display: flex;
  margin-top: 10px;
  justify-content: space-around;
  &-item {
    width: 25%;
    display: flex;
    justify-content: center;
  }
  &-items {
    width: 25%;
    display: flex;
    .items-div {
      cursor: pointer;
      margin-bottom: 5px;
    }
  }
}

.transfer-container {
  margin-top: 16px;
  :deep(.ant-transfer) {
    display: flex;
    justify-content: center;
  }
}
</style>
