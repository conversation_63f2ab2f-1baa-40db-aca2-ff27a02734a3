import service from '@/api'
import { SODATAFLINK } from '@/api/base'
import type { Parameter } from '../data-asset/hbase-management'

/**
 * 获取服务器监控信息
 * @param params
 */
export function getServerMonitor(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/new2dataplatform/serviceMonitor/getSertviceInfo`,
    method: 'post',
    data: params,
    extraParams: {
      showLoading: false,
    },
  })
}

/**
 * 获取所有服务器监控信息-简略
 * @param params
 */
export function getAllServerMonitor(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/new2dataplatform/serviceMonitor/getAllSertviceInfo`,
    method: 'post',
    data: params,
  })
}
