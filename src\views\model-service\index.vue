<script lang="ts" setup>
import { reactive, ref, onMounted } from 'vue'
import { DownOutlined, UpOutlined } from '@ant-design/icons-vue'
import { message, type FormInstance } from 'ant-design-vue'
import { useRouter } from 'vue-router'

import { ArrowLeftOutlined } from '@ant-design/icons-vue'
import { columns } from './data-source'
import AddModal from './add-modal.vue'
import cardBox from '@/components/card-box/card-box.vue'
import {
  reqAddModel,
  reqCheckModel,
  reqDeleteModel,
  reqGetModelList,
  reqGetModelTypeData,
  reqUpdateModel,
} from './api'

const router = useRouter()

const formRef = ref<FormInstance>()
const formState = reactive({
  modelName: '', // 模型名称
  host: '', // 模型服务地址
  port: '', // 端口
  modelType: undefined, // 模型类别
  modelDesc: '', // 模型描述
})
const pageIndex = ref(1)
const pageSize = ref(10)
const total = ref(0)
const modelTypeList = ref([])
const loading = ref(false)

const addModalRef = ref<InstanceType<typeof AddModal> | null>(null)

const onFinish = (values: any) => {
  console.log('Received values of form: ', values)
  pageIndex.value = 1
  getModelList(values)
}

const handleAddModel = () => {
  addModalRef.value?.showModal(true)
}
const dataSource = ref([])

const addModelSbumit = async (data: any, modifyFlag: boolean) => {
  // 新增或修改前校验地址和端口
  if (modifyFlag) {
    await reqUpdateModel(data)
  } else {
    await reqAddModel(data)
  }
  message.success(`${modifyFlag ? '修改' : '添加'}成功`)
  getModelList()
  addModalRef.value?.showModal(false)
}

const handleReset = () => {
  formRef.value?.resetFields()
  pageIndex.value = 1
  getModelList()
}

const handleModify = (record: any) => {
  addModalRef.value?.showModal(true, toRaw(record))
}

const handleVerify = async (record: any) => {
  const params = {
    host: record.host,
    port: record.port,
  }
  const res = await reqCheckModel(params)
  if (res.result === '1') {
    message.success(res.data)
  } else {
    message.error(res.data)
  }
}

onMounted(() => {
  getModelTypeList()
  getModelList()
})

// 获取模型列表
const getModelList = async (values?: any) => {
  console.log('%c [ values ]-91', 'font-size:13px; background:#619b73; color:#a5dfb7;', values)
  const params = {
    modelName: values?.modelName || formState.modelName,
    host: values?.host || formState.host,
    port: values?.port || formState.port,
    modelType: values?.type || formState.modelType,
    modelDesc: values?.modelDesc || formState.modelDesc,
    pageIndex: values?.pageIndex || pageIndex.value,
    pageSize: values?.pageSize || pageSize.value,
  }
  loading.value = true
  const res = await reqGetModelList(params).finally(() => {
    loading.value = false
  })
  dataSource.value = res.data.records
  total.value = res.data.totalRecords
}

// 获取模型类型下拉数据
const getModelTypeList = async () => {
  const res = await reqGetModelTypeData()
  modelTypeList.value = res.data
  console.log('%c [ res ]-48', 'font-size:13px; background:#d19a03; color:#ffde47;', res)
}

const onTableChange = (data: any) => {
  console.log('%c [ page ]-81', 'font-size:13px; background:#3e0b46; color:#824f8a;', data)
  pageIndex.value = data.current
  pageSize.value = data.pageSize
  getModelList()
}

// 删除
const onDelete = async ({ id }: any) => {
  const res = await reqDeleteModel({ id })
  message.success('删除成功')
  getModelList()
  console.log('%c [ res ]-48', 'font-size:13px; background:#d19a03; color:#ffde47;', res)
}
</script>

<template>
  <card-box style="margin-bottom: 16px" showForm>
    <template #title>
      <div>
        <ArrowLeftOutlined
          @click="router.back()"
          style="margin-right: 8px; cursor: pointer"
        />模型服务
      </div>
    </template>
    <template #subTitle>用于展示知识图谱下的模型服务</template>
    <template #headerRight>
      <a-button type="primary" @click="handleAddModel">新建模型</a-button>
    </template>
    <template #form>
      <a-form
        ref="formRef"
        name="model_search"
        :model="formState"
        @finish="onFinish"
        :label-col="{ span: 5 }"
      >
        <a-row :gutter="24">
          <a-col :span="8">
            <a-form-item name="modelName" label="模型名称">
              <a-input
                v-model:value="formState.modelName"
                placeholder="例: xx模型"
                allowClear
              ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item name="host" label="服务地址">
              <a-input
                v-model:value="formState.host"
                placeholder="例: *************"
                allowClear
              ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item name="port" label="端口">
              <a-input v-model:value="formState.port" placeholder="例:8080" allowClear></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item name="modelType" label="模型类别">
              <a-select
                :options="modelTypeList"
                :fieldNames="{ label: 'dataName', value: 'dataId' }"
                v-model:value="formState.modelType"
                placeholder="请选择模型类别"
                allowClear
                show-search
              ></a-select>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item name="modelDesc" label="模型描述">
              <a-input
                v-model:value="formState.modelDesc"
                placeholder="请输入模型描述"
                allowClear
              ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item style="text-align: right">
              <a-button type="primary" html-type="submit">搜索</a-button>
              <a-button style="margin-left: 8px" @click="handleReset">重置</a-button>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </template>
    <a-table
      :pagination="{
        size: 'small',
        total,
        'show-size-changer': true,
        'show-quick-jumper': true,
        'show-total': () => `共 ${total} 个模型`,
      }"
      @change="onTableChange"
      :columns="columns"
      :dataSource="dataSource"
      :loading="loading"
      rowKey="id"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'operation'">
          <a-button style="padding: 4px 15px 4px 0" type="link" @click="handleVerify(record)"
            >校验</a-button
          >
          <a-button style="padding: 0" type="link" @click="handleModify(record)">修改</a-button>
          <a-popconfirm v-if="dataSource.length" title="确定要删除?" @confirm="onDelete(record)">
            <a-button type="link" danger>删除</a-button>
          </a-popconfirm>
        </template>
      </template>
    </a-table>
    <AddModal ref="addModalRef" :modelTypeList="modelTypeList" @handleSubmit="addModelSbumit" />
  </card-box>
</template>

<style lang="less" scoped></style>
