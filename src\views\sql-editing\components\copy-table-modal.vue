<script setup lang="ts">
defineOptions({
  name: 'CopyTableModal',
})
const formState = ref({
  desName: '',
})
let resolveFn: any = null
const visible = ref(false)
function open(originalName: string) {
  formState.value.desName = originalName + '_copy'
  visible.value = true
  return new Promise((resolve) => {
    resolveFn = resolve
  })
}
function handleOk() {
  if (!formState.value.desName.trim()) return
  if (!resolveFn) return
  resolveFn(formState.value.desName)
  visible.value = false
}
function handleCancel() {
  visible.value = false
}

defineExpose({
  open,
})
</script>

<template>
  <a-modal v-model:open="visible" title="复制表" @ok="handleOk" @cancel="handleCancel">
    <a-form :model="formState">
      <a-form-item
        label="目标表名"
        name="desName"
        :rules="[{ required: true, message: '请输入目标表名' }]"
      >
        <a-input v-model:value="formState.desName" placeholder="请输入目标表名" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<style scoped></style>
