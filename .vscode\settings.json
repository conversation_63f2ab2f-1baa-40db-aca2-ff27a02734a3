{
  "eslint.validate": ["javascript", "typescript", "vue"],
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit"
  },
  "eslint.format.enable": true,

  "[vue]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode" // Vue.volar
  },
  "[html]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[typescript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode", //esbenp.prettier-vscode, dbaeumer.vscode-eslint
  }
}
