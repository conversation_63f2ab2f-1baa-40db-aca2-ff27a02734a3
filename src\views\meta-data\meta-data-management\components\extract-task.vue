<template>
  <div class="extract-task">
    <a-steps class="steps" :current="current" :items="steps"></a-steps>
    <a-form
      ref="form1Ref"
      :model="formState"
      :label-col="{
        style: { width: '150px' },
      }"
      v-show="current === 0"
    >
      <a-form-item label="数据源">
        <a-input v-model:value="formState.datasourceId" type="hidden" />
        <a-input v-model:value="datasourceName" :disabled="true" />
      </a-form-item>
      <a-form-item label="数据库名称">
        <a-input :value="formState.databseName" :disabled="true" />
      </a-form-item>
      <a-form-item label="数据模式名称" v-if="props.schemaName">
        <a-input :value="schemaName" :disabled="true" />
      </a-form-item>
      <a-form-item
        label="任务名称"
        name="taskName"
        :rules="[{ required: true, message: '请输入任务名称!' }]"
      >
        <a-input v-model:value="formState.taskName" placeholder="请输入任务名称" />
      </a-form-item>
      <a-form-item label="任务描述">
        <a-textarea v-model:value="formState.description" placeholder="请输入任务描述" />
      </a-form-item>
      <a-form-item
        label="提取表配置"
        name="tableConfigs"
        :rules="[{ required: true, message: '请选择提取表!' }]"
      >
        <a-input v-model:value="formState.tableConfigs" type="hidden" />
        <a-table
          :row-key="(record: any) => record.TABLE_NAME"
          :row-selection="rowSelection"
          :columns="columns"
          :data-source="tableList"
        />
      </a-form-item>

      <a-form-item :wrapper-col="{ span: 24 }" style="text-align: right">
        <a-button type="primary" @click="onNextStep">下一步</a-button>
      </a-form-item>
    </a-form>

    <a-form
      :model="formState"
      :label-col="{
        style: { width: '150px' },
      }"
      v-show="current === 1"
    >
      <a-form-item label="触发方式">
        <a-radio-group v-model:value="formState.triggerType">
          <a-radio :value="1">自动</a-radio>
          <a-radio :value="2">手动</a-radio>
          <a-radio :value="3">实时</a-radio>
        </a-radio-group>
      </a-form-item>
      <a-form-item label="推送方式" v-if="formState.triggerType === 1">
        <a-radio-group v-model:value="formState.pushType">
          <a-radio :value="1">每日</a-radio>
          <a-radio :value="2">每周</a-radio>
          <a-radio :value="3">每月</a-radio>
        </a-radio-group>
      </a-form-item>
      <a-form-item label="推送日期" v-if="formState.triggerType === 1 && formState.pushType === 2">
        <a-radio-group v-model:value="formState.pushDate">
          <a-radio :value="'1'">周一</a-radio>
          <a-radio :value="'2'">周二</a-radio>
          <a-radio :value="'3'">周三</a-radio>
          <a-radio :value="'4'">周四</a-radio>
          <a-radio :value="'5'">周五</a-radio>
          <a-radio :value="'6'">周六</a-radio>
          <a-radio :value="'7'">周日</a-radio>
        </a-radio-group>
      </a-form-item>
      <a-form-item
        label="推送日期"
        v-else-if="formState.triggerType === 1 && formState.pushType === 3"
      >
        <a-select v-model:value="formState.pushDate" placeholder="请选择日期" style="width: 120px">
          <a-select-option v-for="day in monthDays" :key="day" :value="String(day)"
            >{{ day }}号</a-select-option
          >
        </a-select>
      </a-form-item>
      <a-form-item label="推送时间" v-if="formState.triggerType === 1">
        <a-time-picker v-model:value="formState.pushTime" value-format="HH:mm:ss" />
      </a-form-item>

      <a-form-item :wrapper-col="{ span: 24 }" style="text-align: right">
        <a-button @click="onPrevStep">上一步</a-button>
        <a-button type="primary" style="margin-left: 10px" @click="onSubmit">{{
          props.taskInfo !== undefined ? '修改任务' : '创建任务'
        }}</a-button>
        <a-button style="margin-left: 10px" @click="onCancel">取消</a-button>
      </a-form-item>
    </a-form>
  </div>
</template>

<script lang="ts" setup>
import { defineProps, defineEmits, type UnwrapRef, reactive, watch } from 'vue'
import { saveTask, updateTask } from '@/api/assetmanager/metadatamanage/metadatamanage'
import type { queryTabledescFun } from '@/api/matedata/metadatamanagement/metadatamanagement'
import type { Key } from 'ant-design-vue/es/table/interface'
import { Table } from 'ant-design-vue'
import type { DataType } from '@/views/data-asset/table-detail/components/graph'

type TableData = Awaited<ReturnType<typeof queryTabledescFun>>['data']
const props = defineProps<{
  datasourceId: number
  datasourceName: string
  databaseName: string
  taskInfo?: FormParam
  tableList: TableData['obj']
  parentKey?: string
  schemaName?: string
}>()

const dbSchema = inject('dbSchema')

const form1Ref = ref<any>()
const tableList = computed(() => props.tableList)
const datasourceName = computed(() => props.datasourceName)

const columns = ref<any[]>([
  {
    dataIndex: 'TABLE_NAME',
    title: '表名',
    key: 'TABLE_NAME',
  },
  {
    dataIndex: 'TABLE_COMMENT',
    title: '注释',
    key: 'TABLE_COMMENT',
  },
])

const current = ref<number>(0)
const steps = [
  {
    title: '任务配置',
    key: '1',
  },
  {
    title: '执行配置',
    key: '2',
  },
]

const emit = defineEmits(['finished', 'cancel'])

// const fmtPushDate = (pushType: number | undefined, suffix: string) => {
//   if (pushType === undefined || pushType === null) {
//     return ''
//   }
//   if (pushType === 2) {
//     return `周${suffix}`
//   }
//   if (pushType === 3) {
//     return `每月${suffix}号`
//   }
// }

type FormParam = Parameters<typeof saveTask>[0]

const formState: UnwrapRef<FormParam> = reactive({
  datasourceId: props.datasourceId,
  databseName: props.parentKey || props.databaseName,
  taskName: props.taskInfo?.taskName ?? '',
  tableConfigs: props.taskInfo?.tableConfigs ?? '',
  triggerType: props.taskInfo?.triggerType ?? 1,
  pushType: props.taskInfo?.pushType ?? 1,
  pushDate: props.taskInfo?.pushDate,
  pushTime: props.taskInfo?.pushTime ?? '09:00:00',
  description: props.taskInfo?.description ?? '',
})

// 生成1~19号数据
const monthDays = Array.from({ length: 29 }, (_, i) => i + 1)

watch(
  () => formState.pushType,
  (val) => {
    console.log('formState.pushDate', formState.pushDate)
    if ((val === 2 || val === 3) && formState.pushDate === undefined) {
      formState.pushDate = '1'
    }
  },
)

const state = reactive<{
  selectedRowKeys: Key[]
}>({
  selectedRowKeys: props.taskInfo?.tableConfigs?.split(',') || [], // Check here to configure the default column
})

const selectedRowKeys = ref<string[]>(props.taskInfo?.tableConfigs?.split(',') || []) // Check here to configure the default column

const onSelectChange = (changableRowKeys: string[]) => {
  console.log('selectedRowKeys changed: ', changableRowKeys)
  selectedRowKeys.value = changableRowKeys
  formState.tableConfigs = selectedRowKeys.value.join(',')
}

const rowSelection = computed(() => {
  return {
    selectedRowKeys: unref(selectedRowKeys),
    onChange: onSelectChange,
    hideDefaultSelections: true,
    selections: [Table.SELECTION_ALL, Table.SELECTION_INVERT, Table.SELECTION_NONE],
  }
})

const onNextStep = () => {
  form1Ref.value.validate().then(() => {
    onNext()
  })
}

const onNext = () => {
  current.value += 1
}
const onPrevStep = () => {
  current.value -= 1
}

const onSubmit = async () => {
  const cloned = { ...formState }
  if ((dbSchema as any)?.value) {
    cloned.dbSchema = (dbSchema as any).value
  }
  if ((cloned.pushType === 2 || cloned.pushType === 3) && !cloned.pushDate) {
    cloned.pushDate = '1'
  }

  if (cloned.triggerType !== 1) {
    delete cloned.pushType
    delete cloned.pushDate
    delete cloned.pushTime
  }

  if (cloned?.pushType === 1) {
    delete cloned.pushDate
  }
  if (props.taskInfo !== undefined) {
    await updateTask(
      {
        taskId: props.taskInfo?.taskId,
        ...cloned,
      },
      {},
    )
  } else {
    await saveTask({ ...cloned }, {})
  }

  emit('finished')
}

const onCancel = () => {
  emit('cancel')
}
</script>

<style lang="less" scoped>
.extract-task {
  width: 100%;
}
.steps {
  width: 60%;
  margin: 0 auto 30px;
}
</style>
