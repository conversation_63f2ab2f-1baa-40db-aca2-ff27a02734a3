<template>
  <div class="form-columns">
    <div class="title">
      <span class="label">标题：</span>
      <a-input
        type="text"
        style="width: 300px"
        v-model:value="newModelValue.title"
        placeholder="请输入标题"
      />
    </div>
    <div class="title">
      <span class="label">宽度：</span>
      <a-auto-complete
        v-model:value="newModelValue.width"
        :options="widthList"
        style="width: 300px"
        placeholder="请选择宽度"
      />
    </div>
    <div class="title">
      <span class="label"> 弹窗模式：</span>
      <a-switch v-model:checked="newModelValue.modal" />
    </div>
    <div class="title">
      <span class="label"> 排列方式：</span>
      <a-radio-group v-model:value="newModelValue.arrange" @change="changeHandle">
        <a-radio-button :value="item.value" v-for="item in arrangeList" :key="item.value">{{
          item.label
        }}</a-radio-button>
      </a-radio-group>
    </div>
    <searchColumns v-model="newModelValue.form" type="form-column"></searchColumns>
  </div>

  <a-modal v-model:open="open" title="选项列表" @ok="open = false">
    <div>
      <a-button @click="addValue">新增</a-button>
    </div>
    <a-table :dataSource="selectValue.options" :columns="optionsColumns">
      <template #bodyCell="{ column, record, index }">
        <template v-if="column.key === 'operate'">
          <a-button type="link" @click="delOptions(index)">删除</a-button>
        </template>
        <a-input
          v-else
          type="text"
          v-model:value="record[column.key]"
          :placeholder="'请输入' + column.title"
        />
      </template>
    </a-table>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import searchColumns from './search-columns.vue'

interface Columns {
  modelValue: any
}

const open = ref(false)
const selectValue: Record<string, any> = ref({})

const emit = defineEmits(['update:modelValue'])

const props: Columns = withDefaults(defineProps<Columns>(), {
  modelValue: () => [],
})

const widthList = [
  {
    label: '600',
    value: 600,
  },
  {
    label: '700',
    value: 700,
  },
  {
    label: '800',
    value: 800,
  },
  {
    label: '900',
    value: 900,
  },
]

const arrangeList = [
  {
    label: '一行一列',
    value: 24,
  },
  {
    label: '一行二列',
    value: 12,
  },
  {
    label: '一行三列',
    value: 8,
  },
  {
    label: '一行四列',
    value: 6,
  },
]

const optionsColumns = ref([
  {
    title: '名称',
    dataIndex: 'label',
    key: 'label',
  },
  {
    title: '值',
    dataIndex: 'value',
    key: 'value',
  },
  {
    title: '操作',
    dataIndex: 'operate',
    key: 'operate',
  },
])

const changeHandle = (ev: any) => {
  const span = ev.target.value
  newModelValue.value.form.forEach((item: { span: number }) => {
    item.span = span
  })
}

const newModelValue = computed({
  get: () => props.modelValue,
  set: (value) => {
    emit('update:modelValue', value)
  },
})

const addValue = () => {
  if (!selectValue.value?.options) selectValue.value.options = []
  selectValue.value.options.push({ label: '', value: '' })
}

const delOptions = (index: number) => {
  selectValue.value.options.splice(index, 1)
}
</script>

<style scoped lang="less">
.form-columns {
  .title {
    margin-bottom: 20px;
    display: flex;
    .label {
      display: inline-block;
      width: 100px;
    }
  }
}
.table-columns-add {
  text-align: right;
  margin-bottom: 16px;
}
.input-group {
  display: flex;
  align-items: center;
  .icon {
    margin-right: 5px;
    cursor: pointer;
  }
}
</style>
