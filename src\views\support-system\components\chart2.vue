<script lang="ts" setup>
import * as echarts from 'echarts'

const chartRef = ref(null)
const option = {
  title: {
    text: '各医院产出不足率分析对比',
    left: 'center',
    bottom: 0,
    textStyle: {
      fontSize: 14,
      fontWeight: 'normal',
    },
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'cross',
      crossStyle: { color: '#999' },
    },
  },
  legend: {
    data: ['出院人次', '平均住院日', '医疗服务收入占比'],
    bottom: '20px',
    textStyle: {
      color: '#666',
    },
  },
  xAxis: {
    type: 'category',
    data: ['医院1', '医院2', '医院3', '医院4', '医院5', '医院6'],
    axisPointer: {
      type: 'shadow',
    },
    axisLine: {
      lineStyle: {
        color: '#ccc',
      },
    },
    axisLabel: {
      color: '#666',
    },
  },
  yAxis: {
    type: 'value',
    name: '有效得分',
    min: 0.6,
    max: 1.0,
    axisLine: {
      lineStyle: {
        color: '#ccc',
      },
    },
    axisLabel: {
      formatter: '{value}',
      color: '#666',
    },
    splitLine: {
      lineStyle: {
        type: 'dashed',
        color: '#ddd',
      },
    },
  },
  series: [
    {
      name: '出院人次',
      type: 'line',
      data: [0.72, 0.74, 0.75, 0.77, 0.76, 0.74],
      lineStyle: {
        color: '#FF6B6B',
      },
      symbol: 'circle',
      symbolSize: 6,
      itemStyle: {
        color: '#FF6B6B',
      },
    },
    {
      name: '平均住院日',
      type: 'line',
      data: [0.77, 0.81, 0.82, 0.85, 0.84, 0.8],
      lineStyle: {
        color: '#4ECDC4',
      },
      symbol: 'circle',
      symbolSize: 6,
      itemStyle: {
        color: '#4ECDC4',
      },
    },
    {
      name: '医疗服务收入占比',
      type: 'line',
      data: [0.63, 0.74, 0.81, 0.89, 0.86, 0.74],
      lineStyle: {
        color: '#45B7D1',
      },
      symbol: 'circle',
      symbolSize: 6,
      itemStyle: {
        color: '#45B7D1',
      },
    },
  ],
}

onMounted(() => {
  const chart = echarts.init(chartRef.value, null, { renderer: 'canvas', height: 320 })
  chart.setOption(option)
})
</script>

<template>
  <div class="download-chart" ref="chartRef"></div>
</template>

<style lang="less" scoped></style>
