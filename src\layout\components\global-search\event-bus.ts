/**
 * 事件调度中心 (Event Bus)
 * 提供全局事件发布订阅功能，用于组件间通信
 *
 * @example
 * // 订阅事件
 * eventBus.on('user-login', (user) => {
 *   console.log('用户登录:', user)
 * })
 *
 * // 发布事件
 * eventBus.emit('user-login', { id: 1, name: '<PERSON>' })
 *
 * // 取消订阅
 * eventBus.off('user-login', handler)
 */

interface EventHandler {
  (...args: any[]): void
}

interface EventMap {
  [event: string]: EventHandler[]
}

class EventBus {
  private events: EventMap = {}

  /**
   * 订阅事件
   * @param event 事件名称
   * @param handler 事件处理函数
   * @returns 返回取消订阅函数
   */
  on(event: string, handler: EventHandler): () => void {
    if (!this.events[event]) {
      this.events[event] = []
    }

    this.events[event].push(handler)

    // 返回取消订阅函数
    return () => this.off(event, handler)
  }

  /**
   * 一次性订阅事件（触发一次后自动取消订阅）
   * @param event 事件名称
   * @param handler 事件处理函数
   * @returns 返回取消订阅函数
   */
  once(event: string, handler: EventHandler): () => void {
    const onceHandler = (...args: any[]) => {
      handler(...args)
      this.off(event, onceHandler)
    }

    return this.on(event, onceHandler)
  }

  /**
   * 发布事件
   * @param event 事件名称
   * @param args 传递给事件处理函数的参数
   */
  emit(event: string, ...args: any[]): void {
    const handlers = this.events[event]

    if (!handlers || handlers.length === 0) {
      console.debug(`[EventBus] 没有找到事件 "${event}" 的处理函数`)
      return
    }

    // 异步执行处理函数，避免阻塞
    handlers.forEach((handler) => {
      try {
        handler(...args)
      } catch (error) {
        console.error(`[EventBus] 事件 "${event}" 的处理函数执行出错:`, error)
      }
    })
  }

  /**
   * 取消订阅事件
   * @param event 事件名称
   * @param handler 要取消的事件处理函数（可选，不传则取消该事件的所有订阅）
   */
  off(event: string, handler?: EventHandler): void {
    const handlers = this.events[event]

    if (!handlers) {
      return
    }

    if (!handler) {
      // 如果没有指定处理函数，清除该事件的所有订阅
      delete this.events[event]
      return
    }

    // 移除指定的处理函数
    const index = handlers.indexOf(handler)
    if (index > -1) {
      handlers.splice(index, 1)
    }

    // 如果该事件没有处理函数了，删除该事件
    if (handlers.length === 0) {
      delete this.events[event]
    }
  }

  /**
   * 清除所有事件订阅
   */
  clear(): void {
    this.events = {}
  }

  /**
   * 获取所有已注册的事件名称
   * @returns 事件名称数组
   */
  getEventNames(): string[] {
    return Object.keys(this.events)
  }

  /**
   * 获取指定事件的订阅数量
   * @param event 事件名称
   * @returns 订阅数量
   */
  getListenerCount(event: string): number {
    const handlers = this.events[event]
    return handlers ? handlers.length : 0
  }

  /**
   * 检查是否有事件订阅
   * @param event 事件名称（可选，不传则检查是否有任何事件订阅）
   * @returns 是否有订阅
   */
  hasListeners(event?: string): boolean {
    if (event) {
      return this.getListenerCount(event) > 0
    }
    return Object.keys(this.events).length > 0
  }
}

// 创建全局事件总线实例
const eventBus = new EventBus()

// 在开发环境下添加调试功能
if (import.meta.env.MODE === 'development') {
  // 暴露到全局对象，方便调试
  ;(window as any).__eventBus__ = eventBus

  // 添加事件监听统计
  const originalEmit = eventBus.emit
  eventBus.emit = function (event: string, ...args: any[]) {
    console.debug(`[EventBus] 发布事件: ${event}`, args.length > 0 ? args : '')
    return originalEmit.call(this, event, ...args)
  }
}

export default eventBus

// 导出类型定义
export { EventHandler }

// 导出常用事件名称常量
export const EventNames = {
  /** 设置 sql 编辑器内容 */
  SET_SQL_CONTENT: 'set-sql-content',
  /** 追加 sql 编辑器内容 */
  PUSH_SQL_CONTENT: 'push-sql-content',
  /** 刷新变量列表 */
  REFRESH_VARIABLE_LIST: 'refresh-variable-list',
  /** 全局搜索 */
  GLOBAL_SEARCH: 'global-search',
  /** 清空搜索框 */
  CLEAR_SEARCH_KEYWORD: 'clear-search-keyword',
} as const
