<template>
  <Card class="w-300px">
    <template #title>
      <div class="" flex="~ justify-between">
        <span>{{ data.name }}</span>
        <Space>
          <Button type="text" @click="handleEdit">
            <template #icon>
              <EditOutlined />
            </template>
          </Button>
          <Popconfirm title="确定删除吗？" @confirm="handleDelete">
            <Button type="text">
              <template #icon>
                <DeleteOutlined />
              </template>
            </Button>
          </Popconfirm>
        </Space>
      </div>
    </template>
    <div flex="~ col justify-center gap-2">
      <div v-for="item in content" :key="item.value" flex="~ items-center">
        <span>{{ item.label }}：</span>
        <span>{{ item.value }}</span>
      </div>
    </div>
    <template #actions>
      <Button type="text" @click="handleFunction"> 函数 </Button>
      <Button type="text" @click="handleRule"> 规则 </Button>
    </template>
  </Card>
</template>

<script setup lang="ts">
import { Card, Button, Popconfirm, message, Space } from 'ant-design-vue'
import { EditOutlined, DeleteOutlined } from '@ant-design/icons-vue'
import type { ProjectItem } from '../data'
import { deleteProject } from '../service'
import { useRouter } from 'vue-router'

const router = useRouter()

const props = defineProps<{
  data: ProjectItem
}>()
const { data } = toRefs(props)

const emit = defineEmits<{
  (e: 'edit', project: ProjectItem): void
  (e: 'delete', project: ProjectItem): void
}>()

const content = computed(() => {
  return [
    { label: '名称', value: data.value.name },
    { label: '包名', value: data.value.packageName },
    { label: '标签', value: data.value.tag },
    { label: '描述', value: data.value.description },
  ]
})

const handleEdit = () => {
  emit('edit', props.data)
}

const handleDelete = async () => {
  try {
    await deleteProject(props.data.autoId!)
    emit('delete', props.data)
    message.success('删除成功')
  } catch (error) {
    console.error(error)
    message.error('删除失败')
  }
}

const handleFunction = () => {
  router.push({
    path: '/rule-engine/function-manage',
    query: { projectId: props.data.autoId },
  })
}

const handleRule = () => {
  router.push({
    path: '/rule-engine/rule-manage',
    query: { projectId: props.data.autoId },
  })
}
</script>

<style scoped></style>
