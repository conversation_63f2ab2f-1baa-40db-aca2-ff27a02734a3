<template>
  <a-modal
    v-model:open="modalVisible"
    :title="title"
    width="80%"
    :maskClosable="false"
    destroyOnClose
  >
    <a-form layout="vertical" :model="formItem" :rules="formRules" ref="ruleForm">
      <a-row :gutter="60">
        <a-col :span="12">
          <a-form-item label="模板名称" name="templateName">
            <a-input v-model:value="formItem.templateName" placeholder="请输入模板名称" />
          </a-form-item>
          <a-form-item label="模板类型" name="templateType">
            <a-select v-model:value="formItem.templateType" placeholder="请选择模板类型">
              <a-select-option value="shell">Shell</a-select-option>
              <a-select-option value="python">Python</a-select-option>
              <a-select-option value="java">Java</a-select-option>
              <a-select-option value="scala">Scala</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="模板描述" name="templateDesc">
            <a-textarea
              v-model:value="formItem.templateDesc"
              placeholder="请输入模板描述"
              :rows="4"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="模板内容" name="templateContent">
            <div class="edit-container">
              <base-editor
                ref="baseEditor"
                v-model:value="formItem.templateContent"
                :options="{ language: formItem.templateType }"
                style="width: 100%; height: 100%"
                @input="
                  (val) => {
                    formItem.templateContent = val
                  }
                "
              />
            </div>
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
    <template #footer>
      <a-upload
        action=""
        name="file"
        accept=".py,.sh"
        :showUploadList="false"
        @change="handleFileChange"
      >
        <a-button icon="file" style="margin-right: 8px">导入文件</a-button>
      </a-upload>
      <a-button key="back" @click="modalVisible = false">取消</a-button>
      <a-button key="submit" type="primary" :loading="submitLoading" @click="formSubmit"
        >确定</a-button
      >
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue'
import { message } from 'ant-design-vue'
import type { Rule } from 'ant-design-vue/es/form'
import { addTemplat, editTemplat, type TemplateDTO } from '@/api/setting/script-template'
import BaseEditor from '@/components/code-editor/base-editor.vue'

// 响应式数据
const modalVisible = ref(false)
const modalType = ref('add')
const submitLoading = ref(false)
const ruleForm = ref()
const baseEditor = ref()

const formItem = reactive({
  templateName: '',
  templateDesc: '',
  templateType: 'shell',
  templateContent: '',
})

const formRules: Record<string, Rule[]> = {
  templateName: [{ required: true, message: '请输入模板名称', trigger: 'blur' }],
  templateContent: [{ required: true, message: '请输入模板内容', trigger: 'blur' }],
  templateType: [{ required: true, message: '请选择模板类型', trigger: 'blur' }],
}

// 计算属性
const title = computed(() => {
  return modalType.value === 'add' ? '新增模板' : modalType.value === 'edit' ? '编辑模板' : ''
})

// 监听器
watch(modalVisible, (val: boolean) => {
  if (!val) {
    // 重置表单
    Object.assign(formItem, {
      templateName: '',
      templateDesc: '',
      templateType: 'shell',
      templateContent: '',
    })
  }
})

// 方法
const showModal = (type: string, record?: TemplateDTO) => {
  modalType.value = type
  if (type === 'edit' && record) {
    Object.assign(formItem, JSON.parse(JSON.stringify(record)))
  }
  modalVisible.value = true
}

const formSubmit = async () => {
  try {
    await ruleForm.value?.validate()
    submitLoading.value = true

    const params = JSON.parse(JSON.stringify(formItem))
    params.ownerId = 'system' // 这里需要从store获取

    if (params.id && modalType.value === 'edit') {
      await editTemplat(params)
      message.success('编辑成功')
    } else {
      await addTemplat(params)
      message.success('新增成功')
    }

    closeModal()
  } catch (error) {
    console.error('提交失败:', error)
  } finally {
    submitLoading.value = false
  }
}

const closeModal = () => {
  modalVisible.value = false
  emit('success')
}

const handleFileChange = (info: any) => {
  const file = info.file
  console.log('🚀 ~ handleFileChange ~ baseEditor.value:', baseEditor.value)

  if (file) {
    let reader = new FileReader()
    reader.readAsText(file.originFileObj, 'UTF-8')
    reader.onload = (evt: any) => {
      ;(baseEditor.value as any).setData(evt.target.result)
    }
  }
}

// 暴露方法给父组件
defineExpose({
  showModal,
})

// 定义事件
const emit = defineEmits<{
  success: []
}>()
</script>

<style scoped>
.edit-container {
  height: 400px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
}
</style>
