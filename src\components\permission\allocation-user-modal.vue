<template>
  <a-modal :open="open" title="添加用户" @cancel="cancelHandleUser" width="1200px">
    <a-tabs v-model:activeKey="activeKey">
      <a-tab-pane key="1" tab="按部门添加用户">
        <div class="details">
          <div class="userTree">
            <tree-node
              :treeData="treeData"
              @treeSelect="treeSelect"
              :isMenus="false"
              :fNames="fNames"
              :checkable="false"
            ></tree-node>
          </div>
          <div class="userTable">
            <Table
              :columns="columns"
              :getData="getUserList"
              v-if="resetTable"
              :tableConfig="{ rowSelection: Selection, rowKey: 'userId' }"
              ref="tableRef"
              :autoRequest="false"
              class="tableOperate"
              :searchFormState="formState"
            >
              <template #operate>
                <a-button type="primary" @click="submit" :loading="confirmLoading">提交</a-button>
              </template>
              <template #search>
                <a-form-item label="用户名" name="用户名" :rules="[{ message: '请输入用户名' }]">
                  <a-input v-model:value="formState.userName" placeholder="请输入用户名" />
                </a-form-item>
                <!-- <a-form-item label="查找范围" name="查找范围" :rules="[{ message: '请输入查找范围' }]">
                  <a-radio-group v-model:value="formState.deptId">
                    <a-radio :value="1">查看本级组织</a-radio>
                    <a-radio :value="0">查看本级及下级组织</a-radio>
                  </a-radio-group>
                </a-form-item> -->
              </template>
            </Table>
          </div>
        </div>
      </a-tab-pane>
    </a-tabs>
    <template #footer> </template>
  </a-modal>
</template>
<script lang="ts" setup>
import { Table } from '@fs/fs-components'
import { useUserStore } from '@/stores/user'
import { message } from 'ant-design-vue/es/components'
import { allocationUserModalDTO } from '@/utils/column'
import type { FormInstance } from 'ant-design-vue/es/form'
import { defineEmits, defineProps, defineExpose, ref, onBeforeMount, watch } from 'vue'
import { getDeptList, getUserList, addRoleUser, addUserGroupUser } from '@/api/services/permission'
import type { Column } from '@fs/fs-components/src/components/table/type'
import { type TableProps } from 'ant-design-vue'
const props = defineProps({
  open: {
    type: Boolean,
    default: false,
  },
  id: String,
})
const formState = ref<any>({
  deptId: null,
  userName: null,
})

const fNames = {
  children: 'deptList',
  title: 'deptName',
  key: 'deptId',
}

const emits = defineEmits(['modalHandleOk', 'modalCancel', 'modalCancelHandleUser', 'submitHandle'])
const formRef = ref<FormInstance>()
const tableRef = ref<InstanceType<typeof Table> | null>(null)
const confirmLoading = ref<boolean>(false)
const activeKey = ref('1')
const treeData = ref<any>([])
let columns = ref<Column[]>([])
const selectedKeysVal = ref<string>('')
const selectedKeys = ref<(string | number)[]>([])
const userId = ref<string>('')
const resetTable = ref<boolean>(false)
const Selection: TableProps['rowSelection'] = {
  onChange: (rowKeys: (string | number)[]) => {
    selectedKeys.value = rowKeys
  },
}
const userStore = useUserStore()
const type = ref<string>('')

watch(
  () => props.id,
  (value) => {
    if (value) {
      userId.value = value
    }
  },
  { immediate: true },
)

onBeforeMount(() => {
  initColumns()
})

function getTreeLists(flag = true) {
  const params = {
    buId: userStore.userInfo.buId as string,
  }
  getDeptList(params).then((result: any) => {
    if (result.code === '000000') {
      treeData.value = result.data
      flag && resetForm()
      tableRef.value?.getTableData()
    }
  })
}

async function treeSelect(selectedKeys: any) {
  selectedKeysVal.value = selectedKeys
  resetForm()
  if (selectedKeys) {
    formState.value.deptId = selectedKeys
    await tableRef.value?.getTableData()
  }
}

function cancelHandleUser() {
  emits('modalCancelHandleUser')
  resetTable.value = false
}

async function show(data: string) {
  resetTable.value = true
  type.value = data
  await resetForm()
  getTreeLists()
}

const resetForm = () => {
  formRef.value?.resetFields()
}

function submit() {
  if (selectedKeys.value.length > 0) {
    confirmLoading.value = true
    const userList = selectedKeys.value.map((item) => {
      return {
        userId: item,
      }
    })
    if (type.value === 'user') {
      const data = {
        userGroupId: userId.value,
        userList,
      }
      addUserGroupUser(data)
        .then(() => {
          message.success('添加分配用户成功')
          emits('submitHandle')
        })
        .finally(() => {
          confirmLoading.value = false
          resetTable.value = false
          selectedKeys.value = []
        })
    } else {
      const data = {
        roleId: userId.value,
        userList,
      }
      addRoleUser(data)
        .then(() => {
          message.success('添加分配用户成功')
          emits('submitHandle')
          resetTable.value = false
        })
        .finally(() => {
          confirmLoading.value = false
          resetTable.value = false
          selectedKeys.value = []
        })
    }
  } else {
    message.info('请选择分配用户')
  }
}

async function initColumns() {
  columns.value = allocationUserModalDTO.map((item) => {
    return {
      ...item,
      dataIndex: item.key,
    }
  })
}

defineExpose({ show })
</script>
<style lang="less" scoped>
/deep/.tree-dropdown {
  display: none;
}
.details {
  display: flex;
}
</style>
