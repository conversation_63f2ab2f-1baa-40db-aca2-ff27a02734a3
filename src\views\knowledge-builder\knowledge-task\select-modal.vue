<template>
  <a-modal
    :visible="visible"
    title="选择数据"
    @ok="handleOk"
    @cancel="handleCancel"
    :maskClosable="false"
    :confirmLoading="loading"
    width="1000px"
    :destroyOnClose="true"
  >
    <a-row justify="start" align="top" type="flex">
      <a-col :span="8" style="height: 550px; overflow-y: auto; overflow-x: hidden">
        <Tree
          :tree-data="treeData"
          :fieldNames="{
            children: 'children',
            title: 'name',
            key: 'id',
          }"
          class="custom-tree"
          showIcon
          @select="handleSelect"
          @expand="handleExpand"
          :defaultExpandAll="true"
          :expandedKeys="expandedKeys"
          :selectedKeys="selectedKeys"
          :load-data="onLoadData"
        >
          <template #icon="{ dataRef }">
            <img
              :src="dataSource"
              alt=""
              v-if="!dataRef.isDataBase"
              style="width: 18px; height: 18px"
            />
          </template>
          <template #title="{ name, dataRef }">
            <div class="tree-node-content">
              <type-icon
                v-if="dataRef.isDataBase && !dataRef.isTable"
                style="margin-right: 10px"
                :type="dataRef?.dbTypMap"
              />
              <TableOutlined
                v-if="dataRef.isTable && !dataRef.isSchema"
                style="margin-right: 5px"
              />
              <img
                :src="model"
                v-if="dataRef.isSchema"
                style="width: 14px; height: 14px; margin-right: 5px"
              />

              <span class="node-name" :title="name"
                >{{ name }}
                <template v-if="dataRef?.tableCount">（{{ dataRef?.tableCount }}）</template>
              </span>
            </div>
          </template>
        </Tree>
      </a-col>
      <a-col :span="16">
        <a-table
          :loading="loadingTableData"
          :columns="columns"
          :data-source="tableSource"
          :row-selection="{
            selectedRowKeys: selectedRowKeys,
            onChange: onSelectChange,
            checkStrictly: false,
          }"
          :pagination="false"
          row-key="key"
          :scroll="{ y: 500 }"
        >
          <template #expandIcon="{ expanded, onExpand, record }">
            <span @click="(e) => onExpand(record, e)" v-if="record.tableType">
              <DownOutlined v-if="expanded" style="margin-right: 5px; color: #6d6d6d" />
              <RightOutlined v-else style="margin-right: 5px; color: #6d6d6d" />
            </span>
          </template>
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'name'">
              <div class="table-name">
                <TableOutlined v-if="record.tableType" style="margin-right: 5px" />
                {{ record.name }}
              </div>
            </template>
          </template>
        </a-table>
      </a-col>
    </a-row>
  </a-modal>
</template>

<script lang="ts" setup>
import { ref, defineProps, defineEmits, watch, computed } from 'vue'
import { message, type TreeProps } from 'ant-design-vue'
import TypeIcon from '@/views/data-asset/metadata-query/components/type-icon.vue'
import dataSource from '@/assets/data-base/data-source.png'
import { TableOutlined, DownOutlined, RightOutlined } from '@ant-design/icons-vue'
import { Tree } from 'ant-design-vue'
import {
  PageListScameInfo,
  getDbNameFun,
  dbnameTableParmaTranFun,
  queryTableColumnInfoFun,
} from '@/api/services/knowledge'
import { useUserStore } from '@/stores/user'
import model from '@/assets/data-base/model.svg'

interface Column {
  columnType: string
  isNullable: string
  dataType: string
  columnComment: string
  ordinalPosition: string
  columnKey: string
  tableName: string
  columnName: string
  name?: string
  value?: string
  key?: string
  parentValue?: string
}

const props = defineProps<{
  visible: boolean
  parentData: Record<string, any>
}>()

const emit = defineEmits(['update:visible', 'success'])

const formRef = ref()

const treeData = ref<any[]>([])
const tableSource = ref<any>([])
const selectedData = ref<any>({})
const selectedKeys = ref<string[]>([])

const isAddTable = computed(() => props.parentData?.isDataBase)

const loading = ref(false)
const expandedKeys = ref<string[]>([])
const loadingTableData = ref(false)

const handleOk = async () => {
  try {
    if (selectedRow.value.length === 0) {
      message.warning('请先勾选字段')
      return
    }
    if (selectedRow.value.length > 1) {
      message.warning('最多只能勾选一个字段')
      return
    }
    loading.value = true
    const selectColumnsData: any = selectedRow.value[0]

    const data = treeData.value?.filter((item: any) => {
      return item.children?.find((ele: any) => ele.key === selectedData.value.dbName)
    })
    let connectionInfoData: any = {}
    if (data && data.length > 0) {
      connectionInfoData = data[0]
      delete connectionInfoData.children
    }
    const obj: any = {
      dbName: selectedData.value.dbName,
      tabName: selectedData.value.name,
      selectColumns: selectColumnsData.title,
      connectionInfo: connectionInfoData,
    }
    if (selectedData.value.dbSchema) {
      obj.dbSchema = selectedData.value.dbSchema
    }
    emit('success', obj)
    handleCancel()
  } catch (error) {
    message.error('创建失败')
  } finally {
    loading.value = false
  }
}

const onLoadData: any = (treeNode: any) => {
  return new Promise<void>((resolve) => {
    const obj: any = {
      id: treeNode.dataRef.parentId ? treeNode.dataRef.parentId : treeNode.dataRef.id,
    }
    if (treeNode.dataRef.dbTypMap) {
      obj.dbName = treeNode.dataRef.key
    }
    const isSchema = treeNode.dataRef.isSchema
    if (isSchema) {
      obj.dbSchema = treeNode.dataRef.title
      obj.dbName = treeNode.dataRef.dbName
    }
    let handle = obj.dbName || isSchema ? dbnameTableParmaTranFun : getDbNameFun
    handle({
      json: JSON.stringify(obj),
    }).then(({ data }) => {
      console.log('🚀 ~ data:', data)
      if (!data.obj) {
        resolve()
        return
      }
      const list = data.obj.map((item: any) => {
        const scopedSlots = item?.scopedSlots || {}
        const newObj = {
          ...item,
          id: item.key,
          parentId: obj.dbName ? treeNode.dataRef.parentId : treeNode.dataRef.id,
          key: item.title,
          name: item.title,
          isLeaf: scopedSlots?.icon === 'schema' ? false : Boolean(obj.dbName),
          isDataBase: true,
          isTable: obj.dbName ? true : false,
          isSchema: scopedSlots?.icon === 'schema' ? true : false,
        }
        if (obj.dbName) {
          newObj.dbName = treeNode.dataRef.key
        }
        if (isSchema) {
          newObj.dbName = treeNode.dataRef.dbName
          newObj.dbSchema = treeNode.dataRef.key
        }
        return newObj
      })
      if (!treeNode?.dataRef?.children) treeNode.dataRef.children = []
      const fileList = treeNode.dataRef.children.filter((item: any) => !item.isDataBase)
      treeNode.dataRef.children = [...fileList, ...list]
      treeData.value = [...treeData.value]
      resolve()
    })
  })
}

const handleExpand: TreeProps['onExpand'] = (keys) => {
  expandedKeys.value = keys as string[]
}

const handleCancel = () => {
  treeData.value = []
  tableSource.value = []
  selectedRow.value = []
  selectedRowKeys.value = []
  selectedData.value = {}
  expandedKeys.value = []
  emit('update:visible', false)
}

watch(
  () => props.visible,
  async (newVal) => {
    if (!newVal) {
      formRef.value?.resetFields()
    } else {
      await getSourceAndDBListHandle()
    }
  },
)

const getSourceAndDBListHandle = async () => {
  const userStore = useUserStore()
  const res = await PageListScameInfo({ ownerId: userStore.userInfo.loginAccount })
  const list = processTreeData(res.data)
  treeData.value = list
}

// 处理树形数据，递归设置 isLeaf
const processTreeData = (data: any[]) => {
  return data.map((item) => {
    const processedItem = { ...item, name: `${item.dbDesc}`, isLeaf: false }
    return processedItem
  })
}

const handleSelect = async (keys: any, info: any) => {
  if (!info.node.isTable) {
    return
  } else {
    selectedKeys.value = keys
  }
  selectedData.value = info.node
  try {
    loadingTableData.value = true
    const obj = {
      id: info.node.dataRef.parentId,
      tableName: info.node.dataRef.name,
      dbSchema: info.node.dataRef.dbSchema,
      dbName: info.node.dataRef.dbName,
    }
    const res = await queryTableColumnInfoFun({
      json: JSON.stringify(obj),
    })
    tableSource.value = res.data.obj
  } catch (error) {
    message.error('获取表信息失败')
  } finally {
    loadingTableData.value = false
  }
}

const columns = [
  {
    title: '名称',
    key: 'title',
    dataIndex: 'title',
  },
  {
    title: '类型',
    key: 'desc',
    dataIndex: 'desc',
  },
  {
    title: '注释',
    key: 'comment',
    dataIndex: 'comment',
  },
]

const selectedRowKeys = ref<string[]>([])
const selectedRow = ref<string[]>([])

const onSelectChange = (keys: string[], selectedRows: any[]) => {
  selectedRowKeys.value = keys
  selectedRow.value = selectedRows
}
</script>

<style lang="less" scoped>
:deep(.ant-tree) {
  .ant-tree-treenode {
    width: 100%;
    height: 40px;
    display: flex;
    align-items: center;
    .ant-tree-switcher {
      line-height: 40px;
    }
    .ant-tree-iconEle {
      width: 18px;
      height: 18px;
      line-height: 18px;
      margin-right: 5px;
    }
    .ant-tree-title {
      width: 100%;
    }
    .ant-tree-node-content-wrapper {
      width: 100%;
      height: 40px;
      display: flex;
      align-items: center;
    }
  }
}
.custom-tree {
  margin-top: 10px;
  :deep(.ant-tree-node-content-wrapper) {
    width: 100%;
    &:hover {
      background-color: #f5f5f5;
    }
  }
  :deep(.ant-tree-node-selected) {
    background-color: #e6f7ff;
  }
  :deep(.ant-tree-title) {
    width: 100%;
  }
  .tree-node-content {
    width: 100%;
    padding-right: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .node-name {
      flex: 1;
      white-space: nowrap;
    }

    .node-actions {
      display: flex;
      align-items: center;
      gap: 8px;

      .action-icon {
        font-size: 16px;
        color: #666;
        cursor: pointer;

        &:hover {
          color: #1890ff;
        }
      }
    }
  }
}
.table-name {
  display: flex;
  flex-wrap: nowrap;
}
:deep(.ant-table-cell-with-append) {
  display: flex;
}
</style>
