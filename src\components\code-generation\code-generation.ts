import axios from 'axios'

// 从虚拟文件系统获取api数据
export function getData(data?: any) {
  return axios.post('/server/getApiData', data)
}

// 格式化代码
export function formatting(content: string) {
  return axios.post('/server/formatting', { content })
}

// 保存草稿箱
export function savedraft(data: any) {
  return axios.post('/server/savedraft', data)
}

// 获取草稿箱
export function getdraft(data: any) {
  return axios.post('/server/getdraft', data)
}

// 生成代码
export function generationCode(data: any) {
  return axios.post('/server/generationCode', data)
}

// 生成路由
export function generationRouter(data: any) {
  return axios.post('/server/generationRouter', data)
}

// 获取需要生成路由列表
export function getGenerationRouter(data: any) {
  return axios.post('/server/getGenerationRouter', data)
}

// 生成布局代码
export function saveLayoutLogin(data: any) {
  return axios.post('/server/saveLayoutLogin', data)
}

// 获取自定义模板初始数据
export function getCustomTemplate(data: any) {
  return axios.post('/server/custom/getTemplate', data)
}

// 生成自定义模板字符串据
export function generationCoustomTemplate(data: any) {
  return axios.post('/server/custom/generationCoustomTemplate', data)
}
