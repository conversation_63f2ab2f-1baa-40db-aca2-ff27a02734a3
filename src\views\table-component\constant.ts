import { ColumnType } from '@/api/services/indicator/type'

export enum DateFilterType {
  TODAY = 'today',
  YESTERDAY = 'yesterday',
  LAST_WEEK = 'lastWeek',
  LAST_7_DAYS = 'last7Days',
  LAST_30_DAYS = 'last30Days',
  LAST_MONTH = 'lastMonth',
  LAST_3_MONTHS = 'last3Months',
  LAST_12_MONTHS = 'last12Months',
  CUSTOM_DATE = 'customDate',
  EXCLUDE = 'exclude',
}

export const dateFilterOptions = [
  { label: '今天', value: DateFilterType.TODAY },
  { label: '昨天', value: DateFilterType.YESTERDAY },
  { label: '上周', value: DateFilterType.LAST_WEEK },
  { label: '最近7天', value: DateFilterType.LAST_7_DAYS },
  { label: '最近30天', value: DateFilterType.LAST_30_DAYS },
  { label: '上个月', value: DateFilterType.LAST_MONTH },
  { label: '最近3个月', value: DateFilterType.LAST_3_MONTHS },
  { label: '过去12个月', value: DateFilterType.LAST_12_MONTHS },
  { label: '具体日期', value: DateFilterType.CUSTOM_DATE },
  { label: '不包括', value: DateFilterType.EXCLUDE },
]

export const columnTypeTextMap = {
  [ColumnType.TEXT]: 'text',
  [ColumnType.EMAIL]: '<EMAIL>',
  [ColumnType.NUMBER]: '123',
  [ColumnType.DATE]: '2052-01-01',
  [ColumnType.BOOLEAN]: 'true',
}
