<template>
  <a-table
    :dataSource="list"
    :columns="columns"
    :pagination="false"
    key="name"
    size="small"
    :scroll="{ x: 400, y: 300 }"
  >
    <template #bodyCell="{ column, record }">
      <template v-if="column.key === 'description'">
        {{ record[column.key] ? record[column.key] : '暂无' }}
      </template>
      <template v-if="column.key === 'isArray'">
        <span :style="{ color: record[column.key] ? 'red' : '' }">
          {{ record['children'] ? '是' : '否' }}</span
        >
      </template>
      <template v-if="column.key === 'required'">
        <span :style="{ color: record[column.key] ? 'red' : '' }">{{
          record[column.key] ? '是' : '否'
        }}</span>
      </template>
      <template v-if="column.key === 'type'">
        {{ record['children'] ? 'object' : record[column.key] }}
      </template>
    </template>
  </a-table>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

const { dataSource } = defineProps({
  dataSource: {
    type: Array,
    default: () => [],
  },
})

const list = computed({
  get: () => {
    if (dataSource.length > 0) {
      dataSource.map((item: any) => {
        if (item.isArray) {
          item.children = JSON.parse(JSON.stringify(item.type))
        }
      })
    }
    return dataSource
  },
  set: (value) => {
    list.value = value
  },
})

const columns = [
  {
    title: '字段名称',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '数据类型',
    dataIndex: 'description',
    key: 'description',
  },
  {
    title: '是否集合',
    dataIndex: 'isArray',
    key: 'isArray',
  },
  {
    title: '参数说明',
    dataIndex: 'type',
    key: 'type',
  },
]
</script>
<style scoped lang="less"></style>
