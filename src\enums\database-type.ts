import { BaseEnum } from '@/api/common'

/** 数据库类型枚举 */
export class DatabaseType extends BaseEnum<number> {
  static readonly mysql = new DatabaseType(1, 'mysql')
  static readonly kudu = new DatabaseType(2, 'kudu')
  static readonly tidb = new DatabaseType(3, 'tidb')
  static readonly hive = new DatabaseType(4, 'hive')
  static readonly oracle = new DatabaseType(5, 'oracle')
  static readonly sqlserver = new DatabaseType(6, 'sqlserver')
  static readonly cache = new DatabaseType(7, 'cache')
  static readonly mongodb = new DatabaseType(8, 'mongodb')
  static readonly file = new DatabaseType(9, 'file')
  static readonly http = new DatabaseType(10, 'http')
  static readonly impala = new DatabaseType(11, 'impala')
  static readonly greenplum = new DatabaseType(12, 'greenplum')
  static readonly dm = new DatabaseType(13, 'dm')
  static readonly druid = new DatabaseType(14, 'druid')
  static readonly kafka = new DatabaseType(15, 'kafka')
  static readonly postgresql = new DatabaseType(16, 'postgresql')
  static readonly socket = new DatabaseType(17, 'socket')
  static readonly flink = new DatabaseType(18, 'flink')
  static readonly gbase = new DatabaseType(19, 'gbase')
  static readonly hdfs = new DatabaseType(21, 'hdfs')
  static readonly oceanbase = new DatabaseType(22, 'oceanbase')
  static readonly influxdb = new DatabaseType(23, 'influxdb')
  static readonly spark = new DatabaseType(24, 'spark')
  static readonly doris = new DatabaseType(25, 'doris')
  static readonly tdengine = new DatabaseType(26, 'tdengine')
  static readonly elasticsearch = new DatabaseType(27, 'elasticsearch')
  static readonly clickhouse = new DatabaseType(28, 'clickhouse')
  static readonly kingbase = new DatabaseType(29, 'kingbase')
  static readonly hbase = new DatabaseType(30, 'hbase')

  public iconPath: string

  constructor(key: number, label: string) {
    super(key, label)
    this.iconPath = `/data-base/db-${label}.svg`
  }

  /**
   * 通过 label 查找枚举选项
   * @param label 数据库类型 label
   * @returns DatabaseType | undefined
   */
  static findByLabel(label: string): DatabaseType | undefined {
    const all = Object.values(DatabaseType) as DatabaseType[]
    return all.find((item) => item instanceof DatabaseType && item.label === label)
  }

  /**
   * 通过 key 或 label 查找枚举选项
   */
  static getBy(keyOrLabel: number | string): DatabaseType | undefined {
    if (typeof keyOrLabel === 'number') {
      return super.get(keyOrLabel) as DatabaseType
    } else {
      return this.findByLabel(keyOrLabel)
    }
  }
}
