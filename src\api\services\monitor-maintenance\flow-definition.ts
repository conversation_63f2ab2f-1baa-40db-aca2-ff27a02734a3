import service from '@/api'
import type { Parameter } from '../data-asset/hbase-management'
import { SODATAFLINK } from '@/api/base'

/* ----------------- 流程定义请求接口 ------------------- */

// 组件字段DTO
export class ComponentsFieldDTO {
  /* 字段名:label */
  ecwdLabelField?: string
  /* 参数名 */
  ecwdParamsField?: string
  /* 是否禁用 */
  ecwdDisabled?: number
  /* 是否隐藏 */
  ecwdValueInvisable?: number
  /* 是否渲染 */
  ecwdIsRender?: number
  /* 隐藏必填* */
  ecwdFlagInvisable?: number
  /* 参数等级,1显示与2不显示  */
  ecwdParamsRequire?: number
  /* 默认值 */
  ecwdParamsValueType?: string | number
  /* 输入框类型 */
  ecwdInputType?: string
  /* 选框取值 */
  ecwdInputValues?: Array<KeyValueDTO>
  /* 是否必填 */
  ecwdParamsLevel?: number
  /* 字段标识 0为源库  1为目标库  2为配置项 */
  ecwdType?: number
  /* 字段标识 1-9数据库类型 */
  ecwdDbCode?: string | number
  /* 组件 */
  ecwdTypeDesc?: string
}

// 工作节点DTO
export class WorkNodeDTO {
  id!: string
  serviceIp!: string
}

// 字段渲染表单下拉框数据DTO
export class KeyValueDTO {
  key!: string
  value!: string
  type?: string
}

// 数据源DTO
export class DataSourceDTO {
  id!: string
  dbType?: number
  dbUrl?: string
  dbDesc?: string
  dbName?: string
}

// 数据库连接信息DTO
export class ConnectInfoDTO {
  /* 连接串 */
  url?: ''
  /* 用户名 */
  userName?: ''
  /* 密码 */
  passWord?: ''
  /* 高级配置 */
  other?: ''
  /* 数据库类型 */
  type?: ''
}

// 通知人DTO
export class NotifierPeopleDTO {
  /* id */
  id?: ''
  /* 地址 */
  userAddress?: ''
  /* 通知人 */
  user?: ''
}

// 作业流DTO
export class JobFlowDTO {
  name?: string
  id?: string
}

/**
 * @desc 获取组件字段
 */
export function getFlowComponentsField(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/new2dataplatform/nodedef/getetlColumnWebDic`,
    method: 'post',
    data: params,
  })
}
/**
 * @desc 获取influxdb field id list
 */
export function getInfluxdbFieldByIdFun(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/SchemaInfo/getInfluxdbFieldByIdFun`,
    method: 'post',
    data: params,
    extraParams: {
      showLoading: false,
    },
  })
}

// 获取工作节点列表
export function getWorkNodeList(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/new2dataplatform/nodedef/getWorkId`,
    method: 'get',
    params,
  })
}

// 获取执行机器列表
export function getExecMachineList(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/new2dataplatform/resourceManager/scheserviceinfoList`,
    method: 'get',
    params,
  })
}

// 获取作业流列表
export function getJobFlowList(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/new2dataplatform/flowjsonversion/scheflowdefList`,
    method: 'get',
    params,
  })
}

// 获取通知人列表
export function getNotifierPeopleList(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/new2dataplatform/GetUserCenter/notepersonList`,
    method: 'get',
    params,
  })
}

// 获取数据源列表
export function getDataSourceList(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/new2dataplatform/InfoSchema/schemaInfoList`,
    method: 'get',
    params,
  })
}

// 获取连接信息
export function getConnectInfo(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/new2dataplatform/nodedef/etlschemainfoList`,
    method: 'get',
    params,
  })
}

// 获取组件运行结果
export function getComponentRunResult(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/new2dataplatform/scheComponent/schetaskinstanceList`,
    method: 'get',
    params,
  })
}

/**
 * 生产表达式
 * @param params
 */
export function generateExpression(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/flink/resourceManager/createCrontab`,
    method: 'post',
    data: params,
  })
}

//  获取超级表
export function getStableList(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/database_operate/tdengineSTableFun`,
    method: 'post',
    data: params,
  })
}

//  获取批量同步组件校验数据
export function getDbBatchCheckTaskInfo(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/dataQuailty/generateCheckTask`,
    method: 'post',
    data: params,
  })
}
/**
 * 新建索引
 * @param params
 */
export function createIndexfun(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/flink/index/createIndexfun`,
    method: 'post',
    data: params,
    extraParams: {
      showLoading: true,
    },
  })
}
