import { getViewsRequest } from '../tools/file-api.js'
import { generateTemplateRouter } from '../page-template/generate-router.js'
import {
  writeFolderToDisk,
  writeFolderToFile,
  readSystemFile,
} from '../base-business/virtual-file-system.js'
import { generateTemplate } from '../page-template/generate-template.js'
import process from 'node:process'

const args = process.argv.slice(2)
const argumentValue = args[0]

export async function writeFilesRouter(pageData, virtualFileSystem) {
  try {
    virtualFileSystem.router = {
      generate: await generateTemplateRouter(pageData),
    }
    console.log('router文件写入完成')
  } catch (err) {
    console.error('Error writing files:', err)
  }
}

// 在指定文件夹下并行写入文件views文件
async function writeFilesViews(pageData, virtualFileSystem) {
  try {
    // 创建一个包含所有文件写入操作的 Promise 数组
    for (const item of pageData) {
      const pageContent = item?.pageContent ?? (await generateTemplate(item))
      virtualFileSystem.views[item.menuUrl] = pageContent
    }
    console.log('页面写入完成')
  } catch (err) {
    console.error('Error writing files:', err)
  }
}

export async function generateRouterViews(pageData, pageList) {
  const virtual = await readSystemFile()
  const virtualFileSystem = {
    ...virtual,
    router: {},
    views: {},
  }

  // 执行写入操作
  Promise.all([
    writeFilesViews(pageData, virtualFileSystem),
    writeFilesRouter(pageList, virtualFileSystem),
  ]).then(async () => {
    await writeFolderToDisk(virtualFileSystem)
    await writeFolderToFile(null, 'router', virtualFileSystem)
    console.log('写入虚拟系统完成')
  })
}

if (argumentValue === 'generate-router') {
  const { list } = await getViewsRequest()
  generateRouterViews(list)
}
