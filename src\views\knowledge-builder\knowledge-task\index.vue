<template>
  <div class="content">
    <Flex wrap="nowrap" justify="space-between" class="mb-5">
      <div text="20px #000000d9">知识任务</div>
      <Space size="small" direction="horizontal" align="start">
        <Input
          placeholder="请输入任务名称"
          v-model:value="searchValue"
          @keyup.enter="handleSearch"
          @change="handleInputChange"
        />
        <Button type="primary" @click="createTask">创建任务</Button>
        <Button
          type="primary"
          @click="
            getTaskList({
              jobName: searchValue,
            })
          "
          >刷新</Button
        >
      </Space>
    </Flex>

    <div class="task-manage-table">
      <a-table
        width="100%"
        :columns="columns"
        :data-source="tableData"
        :loading="tableLoading"
        :locale="{ emptyText: '暂无数据' }"
        :pagination="pagination"
      >
        <!-- 自定义文档列 -->
        <template #document="{ text }">
          <a-tooltip>
            <template #title>
              <span>{{ text }}</span>
            </template>
            <span v-if="text" class="link-text">{{ text }}</span>
          </a-tooltip>
          <span v-if="!text">-</span>
        </template>

        <!-- 状态标签列 -->
        <template #status="{ text }">
          <!-- <a-tag>{{ text }}</a-tag> -->
          <a-tag :color="statusColorMap[text as keyof typeof statusColorMap]">
            <ExclamationOutlined v-if="text === 5" />{{
              statusLabelMap[text as keyof typeof statusColorMap]
            }}</a-tag
          >
        </template>
        <!-- 数据类型列 -->
        <template #dataType="{ text }">
          {{ dataTypeMap[text as keyof typeof dataTypeMap] }}
        </template>

        <!-- 提取方式列 -->
        <template #importType="{ text }">
          <a-tag :color="importTypeColorMap[text as keyof typeof importTypeColorMap]">
            {{ importTypeMap[String(text)] ?? text }}
          </a-tag>
        </template>

        <!-- 操作按钮列 -->
        <template #action="{ record }">
          <a-space :size="8">
            <!-- <a-button
              v-if="record.status === 'FINISH'"
              type="link"
              size="small"
              @click="onPreviewConfig(record)"
              >查看配置</a-button
            >
            <a-button
              v-if="record.status === 'FINISH'"
              type="link"
              size="small"
              @click="onPreviewRes(record)"
              >抽取结果</a-button
            >
            <a-button type="link" size="small" @click="onPreviewLog(record)">日志</a-button> -->

            <a-popconfirm
              title="您确定要删除吗？"
              @confirm="onDelete(record)"
              @cancel="onCancel"
              ok-text="确定"
              cancel-text="取消"
            >
              <a-button type="link" size="small" danger>删除</a-button>
            </a-popconfirm>
            <a-button v-if="record.status === 4" type="link" size="small" @click="onPreview(record)"
              >预览</a-button
            >
          </a-space>
        </template>
      </a-table>
    </div>

    <!-- 右侧抽屉 -->
    <a-drawer
      :title="curTask?.jobName"
      :width="720"
      placement="right"
      :closable="true"
      :mask-closable="true"
      :open="drawerOpen"
      @close="onDrawerClose"
    >
      <TaskConfig :data="curTask" />
    </a-drawer>

    <!-- 创建任务弹窗 -->
    <a-modal
      v-model:open="showCreateTaskModal"
      title="创建任务"
      :width="800"
      :footer="null"
      :destroy-on-close="true"
    >
      <CreateTask :onFinish="onCreateTaskModal" :close="closeCreateTaskModal" />
    </a-modal>
    <!-- 日志抽屉 -->
    <TaskLog ref="taskLogRef" :getTaskList="getTaskList" />
    <!-- 抽取结果 -->
    <TaskExtract v-if="extractOpen" v-model:open="extractOpen" :data="curTask" />
    <!-- 预览表格 -->
    <PreviewTable
      v-model:open="previewOpen"
      :curTask="curTask"
      @update:curTask="
        getTaskList({
          jobName: searchValue,
        })
      "
    />
  </div>
</template>

<script lang="ts" setup>
import { Flex, Space, Button, Input, message } from 'ant-design-vue'
import type { TableColumnType } from 'ant-design-vue'
import { DotType, type TaskItem, type TaskParam } from '../type'
import { getKnowledgeTaskList } from '@/api/mock'
import TaskConfig from './task-config.vue'
import CreateTask from './create-task.vue'
import TaskLog from './task-log.vue'
import TaskExtract from './task-extract.vue'
import PreviewTable from './preview-table.vue'
import { useStorage } from '@vueuse/core'
import { DotModelType } from '../type'
import {
  reqGetKnowledgeTaskList,
  reqBuildKnowledge,
  reqDeleteBuildTask,
  addUnKnowledgeTask,
} from '@/api/services/knowledge'
import { onMounted, ref } from 'vue'
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { ExclamationOutlined } from '@ant-design/icons-vue'
const showCreateTaskModal = ref(false)
const drawerOpen = ref(false)
const curTask = ref<TaskItem>()
const taskLogRef = ref()
// 表格列配置
const columns = ref<TableColumnType<TaskItem>[]>([
  {
    title: '任务名称',
    dataIndex: 'jobName',
    width: 300,
  },
  {
    title: '任务状态',
    dataIndex: 'status',
    slots: { customRender: 'status' },
    width: 230,
  },
  {
    title: '提取方式',
    dataIndex: 'importType',
    slots: { customRender: 'importType' },
  },
  {
    title: '数据类型',
    dataIndex: 'dataType',
    slots: { customRender: 'dataType' },
  },
  {
    title: '更新时间',
    dataIndex: 'gmtModified',
  },
  {
    title: '操作',
    slots: { customRender: 'action' },
    width: 200,
    fixed: 'right',
  },
])
const route = useRoute()

// 状态颜色映射
const statusColorMap = {
  // /** 未开始 */
  // PENDING: 'gray',
  // /** 进行中 */
  // RUNNING: 'blue',
  [0]: '',
  [1]: 'blue',
  [2]: 'blue',
  [3]: 'red',
  [4]: 'green',
  [5]: 'orange',
  [6]: 'red',
}

const importTypeColorMap = {
  [0]: '#2db7f5',
  [1]: '#87d068',
}

const statusLabelMap = {
  [0]: '未执行',
  [1]: '执行中',
  [2]: '执行成功',
  [3]: '执行失败',
  [4]: '提取完成',
  [5]: '存在异步提取任务，请稍后刷新是否提取完成',
  [6]: '提取失败',
}

const dataTypeMap = {
  '0': '节点数据',
  '1': '关系数据',
}

const importTypeMap: Record<string, string> = {
  '0': '数据库',
  '1': '文件上传',
}

const onPreviewLog = async (data: TaskItem) => {
  curTask.value = data
  taskLogRef.value.showLogDrawer(data)
}

// 分页配置
const pagination = ref({
  current: 1,
  pageSize: 10,
  showSizeChanger: true,
  total: 0,
  size: 'small',
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条`,
  onChange: (page: number, pageSize: number) => {
    pagination.value.current = page
    pagination.value.pageSize = pageSize
    getTaskList()
  },
  onShowSizeChange: (current: number, size: number) => {
    pagination.value.current = current
    pagination.value.pageSize = size
    getTaskList()
  },
})
const tableData = ref([])
const searchValue = ref('')
const tableLoading = ref(true)
const extractOpen = ref(false)
const createTask = () => {
  showCreateTaskModal.value = true
}
const onPreviewConfig = (data: TaskItem) => {
  curTask.value = data
  drawerOpen.value = true
}
// 抽取结果
const onPreviewRes = (data: TaskItem) => {
  curTask.value = data
  extractOpen.value = true
}
const onDrawerClose = () => {
  drawerOpen.value = false
}
const onDelete = async (task: TaskItem) => {
  await reqDeleteBuildTask({ id: task.id })
  // tableData.value = tableData.value.filter((item: any) => item.id !== task.id)
  getTaskList()
  message.success('删除成功')
}
const onCancel = () => {
  console.log('取消')
}
const closeCreateTaskModal = () => {
  showCreateTaskModal.value = false
}

const onCreateTaskModal = async (task: TaskItem) => {
  if (task.modelType === DotModelType.DATABASE) {
    console.log('🚀 ~ onCreateTaskModal ~ task:', task)
    const userStore = useUserStore()
    const obj = JSON.parse(
      JSON.stringify({
        ...task.connectionInfo,
        jobName: task.jobName,
        projectId: task.projectId,
        ownerId: userStore.userInfo.loginAccount,
        modelId: task.modelId,
      }),
    )
    console.log('🚀 ~ onCreateTaskModal ~ obj:', obj)
    await addUnKnowledgeTask(obj)
    closeCreateTaskModal()
    message.success('创建成功')
    // 关闭弹窗,刷新列表
    await getTaskList()
    return
  }
  const formData = new FormData()
  formData.append('projectId', String(route.query?.projectId || 0))
  formData.append('jobName', task.jobName ?? '')
  if (task.file instanceof Blob) {
    formData.append('file', task.file)
  } else if (typeof task.file === 'string') {
    formData.append('file', task.file)
  }
  formData.append('dataType', String(task.dataType ?? 0))
  // 开始创建任务
  await reqBuildKnowledge(formData)
  closeCreateTaskModal()
  message.success('创建成功')
  // 关闭弹窗,刷新列表
  await getTaskList()
}

const handleInputChange = (e: Event) => {
  const input = e.target as HTMLInputElement
  if (input.value === '') {
    pagination.value.current = 1
    pagination.value.pageSize = 10
    getTaskList()
  }
}

const handleSearch = (e: any) => {
  const value = e.target.value
  pagination.value.current = 1
  pagination.value.pageSize = 10
  getTaskList({
    jobName: value,
  })
}

const getTaskList = async (config: TaskParam = {}) => {
  tableLoading.value = true
  const res = await reqGetKnowledgeTaskList({
    pageIndex: pagination.value.current,
    pageSize: pagination.value.pageSize,
    projectId: route.query?.projectId,
    ...config,
  }).finally(() => {
    tableLoading.value = false
  })

  console.log('%c [  ]-275', 'font-size:13px; background:#5da893; color:#a1ecd7;', res)
  tableData.value = res.records
  pagination.value.total = res.totalRecords
  tableLoading.value = false
}
onMounted(async () => {
  getTaskList()
})

const previewOpen = ref(false)
const previewData = ref<Record<string, any>[]>([])

// Mock preview data - replace this with your actual data
const mockPreviewData = [
  { name: '测试1', age: 25, city: '北京' },
  { name: '测试2', age: 30, city: '上海' },
  { name: '测试3', age: 35, city: '广州' },
]

// 模拟获取数据的方法 - 替换为实际的API调用
const fetchPreviewData = async () => {
  // 模拟API延迟
  await new Promise((resolve) => setTimeout(resolve, 500))
  return mockPreviewData
}

const onPreview = (record: TaskItem) => {
  previewOpen.value = true
  curTask.value = record
}
</script>
<style scoped lang="less">
.mg-rt20 {
  margin-right: 20px;
}
.link-text {
  display: inline-block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
