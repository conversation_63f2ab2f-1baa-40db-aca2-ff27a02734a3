<template>
  <a-menu style="border: none">
    <a-menu-item v-for="item in moreOptions" :key="item.value" @click="onChange(item)">
      {{ item.label }}
    </a-menu-item>
  </a-menu>
</template>

<script setup lang="ts">
import { moreOptions, MorePeriodEnum } from './constant'
defineProps<{
  value?: MorePeriodEnum
  onChange: (item: { label: string; value: string }) => void
}>()
</script>

<style scoped></style>
