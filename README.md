# vue3-seed-framework

This template should help get you started developing with Vue 3 in Vite.

## 推荐的 IDE 设置

[VSCode](https://code.visualstudio.com/) + [Volar](https://marketplace.visualstudio.com/items?itemName=Vue.volar) (and disable Vetur) + [TypeScript Vue Plugin (Volar)](https://marketplace.visualstudio.com/items?itemName=Vue.vscode-typescript-vue-plugin).

## 在 TS 中为 `.vue` 导入提供类型支持

TypeScript 默认情况下无法处理 `.vue` 导入的类型信息，因此我们用 `vue-tsc` 替换 `tsc` CLI 进行类型检查。在编辑器中，我们需要 [TypeScript Vue 插件 (Volar)](https://marketplace.visualstudio.com/items?itemName=Vue.vscode-typescript-vue-plugin) 让 TypeScript 语言服务了解 `.vue` 类型。

如果独立的 TypeScript 插件对您来说速度不够快，Volar 还实现了一个更高效的 [接管模式](https://github.com/johnsoncodehk/volar/discussions/471#discussioncomment-1361669)。您可以通过以下步骤启用它：

1. 禁用内置的 TypeScript 扩展
    1) 从 VSCode 的命令面板中运行 `Extensions: Show Built-in Extensions`
    2) 找到 `TypeScript and JavaScript Language Features`，右键单击并选择 `Disable (Workspace)`
2. 通过运行 `Developer: Reload Window` 命令从命令面板重新加载 VSCode 窗口。

## 自定义 vite 配置文件

See [Vite Configuration Reference](https://vitejs.dev/config/).

## Project Setup

```sh
npm install
```

### Compile and Hot-Reload for Development

```sh
npm run dev
```

### Type-Check, Compile and Minify for Production

```sh
npm run build
```

### Run Unit Tests with [Vitest](https://vitest.dev/)

```sh
npm run test:unit
```

### Run End-to-End Tests with [Playwright](https://playwright.dev)

```sh
# Install browsers for the first run
npx playwright install

# When testing on CI, must build the project first
npm run build

# Runs the end-to-end tests
npm run test:e2e
# Runs the tests only on Chromium
npm run test:e2e -- --project=chromium
# Runs the tests of a specific file
npm run test:e2e -- tests/example.spec.ts
# Runs the tests in debug mode
npm run test:e2e -- --debug
```

### Lint with [ESLint](https://eslint.org/)

```sh
npm run lint
```

## 代码规范

- `src/api` 所有接口的调用
- `src/assets` 存放图片素材
- `src/common` 无业务耦合的服务，例如文件处理、进程管理、会话基础api等；
- `src/componets` 公共组件（头部、side、底部等）跨页面组件、全局组件、对话框类等；
- `src/constants` 跨页面引用的全局常量，枚举等；
- `src/router` 路由管理
- `src/store` 内存基本的跨页面状态管理
- `src/views`
    - `home` 首页
      - `home.ts` 首页
      - `helper.ts` 辅助函数
      - `componets/xxx` 页面级别的组件
    - `user` 用户中心
    - `apps` 应用管理

## 代码规范约定

- 文件名统一使用**小写+连接符**，例如：`file-service.ts`、`home-page.vue`；
- 函数、变量命令使用驼峰；
- `constants` 常量使用大写，枚举 key 可使用驼峰或大写

## 如何在新项目中使用这个模板？

以项目名`my-project` 为例：

1. 申请 `my-project` 项目的 git 仓库，例如：`git@10.8.241.87:root/my-project.git`

```shell
# clone 这个仓库
git clone git@10.8.241.87:root/vue3-seed-framework.git my-project

# 进入项目目录
cd my-project

# 修改 git 仓库地址
git remote set-url origin git@10.8.241.87:root/my-project.git

# 推送代码
git push  --set-upstream origin master
```

# 分支管理说明

- feature_master：主分支，生成环境分支
- feature_dev：开发分支，测试环境分支
- feature_dev0.01：目前版本开发分支
