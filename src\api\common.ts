import { Enum, type EnumKey } from '@airpower/enum'

export interface ApiResponse<T, IsList = false> {
  code: string
  msg: string
  data: IsList extends true ? PageResponse<T> : T
}

// 分页参数
export interface PageParams {
  pageIndex: number
  pageSize: number
}

// 分页响应
export class PageResponse<T> {
  records: T[]
  totalRecords: number
  pageIndex: number
  pageSize: number

  constructor(backend: any) {
    this.records = backend.records
    this.totalRecords = backend.totalRecords
    this.pageIndex = backend.pageIndex
    this.pageSize = backend.pageSize
  }
}

export class BaseEnum<T extends EnumKey = number> extends Enum<T> {
  public value: T
  constructor(key: T, label: string) {
    super(key, label)
    this.value = key
  }
}
