<template>
  <Flex vertical :gap="12">
    <Select
      v-model:value="modelValue"
      :options="dynamicParamOptions"
      mode="tags"
      style="width: 100%"
      placeholder="请输入"
    >
      <template #suffixIcon>
        <info-circle-outlined />
      </template>
    </Select>
    <!-- 只有当动态参数列表不为空且动态参数开关打开时，才显示动态参数开关 -->
    <Space v-if="!!dynamicParam?.length && needDynamicParam">
      <span>动态参数</span>
      <Switch v-model:checked="openDynamic" />
    </Space>
  </Flex>
</template>

<script setup lang="ts">
import { InfoCircleOutlined } from '@ant-design/icons-vue'
import { Flex, Select, Space, Switch } from 'ant-design-vue'
import {
  DynamicParamKey,
  NeedDynamicParamKey,
  type DynamicParam,
} from '@/views/indicator-config/provide-keys'
/** 动态参数列表 */
const needDynamicParam = inject<Ref<boolean>>(NeedDynamicParamKey) || ref(false)
const openDynamic = ref(needDynamicParam.value)
const dynamicParam = inject<Ref<DynamicParam>>(DynamicParamKey)
const dynamicParamOptions = computed(() => {
  if (!dynamicParam?.value || !needDynamicParam.value || !openDynamic.value) return []
  return dynamicParam.value.map((item) => {
    // const label = item.required ? `#{${item.name}}` : `[#{${item.name}}]`
    const label = `#{${item.name}}`
    return {
      label,
      value: label,
    }
  })
})
const modelValue = defineModel({
  default: () => [],
})
</script>

<style lang="less" scoped>
:deep(.ant-switch.ant-switch-checked) {
  background-color: #47dd6f;
}
:deep(.ant-switch.ant-switch-checked:hover) {
  background-color: #47dd6f;
}
</style>
