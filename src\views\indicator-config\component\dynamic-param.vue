<script setup lang="ts">
import { Button, Flex, Form, Space } from 'ant-design-vue'
defineOptions({
  name: 'DynamicParam',
})
const paramList = defineModel<ParamItem[]>('value', { required: true })
const categoryOptions = [
  { label: '字符串', value: ParamCategory.STRING },
  { label: '数字', value: ParamCategory.NUMBER },
  { label: '数组', value: ParamCategory.ARRAY },
]
const onFinish = (values: ParamItem[]) => {
  paramList.value = values
}
const onDelete = (index: number) => {
  paramList.value.splice(index, 1)
}
const onAdd = () => {
  paramList.value.push({
    name: '',
    category: ParamCategory.STRING,
    default: '',
    required: false,
  })
}

watch(
  paramList,
  (newVal) => {
    console.log('参数列表', newVal)
  },
  { deep: true },
)
</script>

<script lang="ts">
export enum ParamCategory {
  STRING = 1,
  NUMBER = 2,
  ARRAY = 3,
}
export type ParamItem = {
  name: string
  category: ParamCategory
  default: string
  required: boolean
}
</script>

<template>
  <div class="dynamic-param">
    <Form :model="paramList" @finish="onFinish">
      <Space direction="vertical" :size="24">
        <Flex
          v-for="(param, index) in paramList"
          :key="index"
          align="center"
          :gap="24"
          justify="start"
        >
          <Form.Item name="['paramList', index, 'name']">
            <template #label>
              <span text="#606266">参数名</span>
            </template>
            <a-input v-model:value="param.name" style="width: 140px" />
          </Form.Item>
          <Form.Item name="['paramList', index, 'category']">
            <template #label>
              <span text="#606266">参数类型</span>
            </template>
            <a-select
              v-model:value="param.category"
              :options="categoryOptions"
              style="width: 140px"
            />
          </Form.Item>
          <Form.Item name="['paramList', index, 'default']">
            <template #label>
              <span text="#606266">默认值</span>
            </template>
            <a-input v-model:value="param.default" style="width: 140px" />
          </Form.Item>
          <Form.Item name="['paramList', index, 'required']">
            <template #label>
              <span text="#606266">必填</span>
            </template>
            <a-switch v-model:checked="param.required" />
          </Form.Item>
          <Button @click="onDelete(index)">删除</Button>
        </Flex>
        <Button class="min-w-846px wfull" @click="onAdd">添加</Button>
      </Space>
    </Form>
  </div>
</template>

<style scoped lang="less">
.dynamic-param {
  width: 100%;
  min-width: 846px;
}
:deep(.ant-form-item) {
  margin-bottom: 0;
}
</style>
