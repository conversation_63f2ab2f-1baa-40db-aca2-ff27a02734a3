<script lang="ts" setup>
import { reqGetChartData, reqGetReasonerData, reqGetSampleMap } from '@/api/services/knowledge'
import type { TaskItem } from '../type'
import { ShowPage } from '../type'
import Extract from './extract.vue'
import Schema from './schema.vue'
import { getRandomColor, series } from '../knowledge-model/assets-data'

const extraOpen = defineModel('open', { required: true })
const props = defineProps<{ data?: TaskItem }>()
const showPage = ref<ShowPage>(ShowPage.EXTRACT)
const pageMap = {
  [ShowPage.EXTRACT]: Extract,
  [ShowPage.SCHEMA]: Schema,
}

const schemaData = reactive({
  demoDataListData: [],
  initLinkListData: [],
})

const extractData = reactive({
  demoDataListData: [],
  initLinkListData: [],
})

const sampleMap = ref({})

const afterOpenChange = (bool: boolean) => {
  console.log('open', bool)
}

const handleChangePage = () => {
  showPage.value = showPage.value === ShowPage.EXTRACT ? ShowPage.SCHEMA : ShowPage.EXTRACT
}

const getSchemaChartList = async () => {
  const res = await reqGetChartData({
    projectId: props.data?.projectId,
  })
  schemaData.demoDataListData = res.entityTypeDTOList.map((item: any) => {
    return {
      ...item,
      label: { show: true, position: 'bottom' },
      itemStyle: { color: getRandomColor() },
    }
  })
  schemaData.initLinkListData = res.relationTypeDTOList.map((item: any) => {
    return {
      ...item,
      source: String(item.startEntity.id), // 一定要确保source、target字段是字符串类型
      target: String(item.endEntity.id),
      level: item.originBelongToProject,
      lineStyle: { curveness: 0.3, color: '#2596FF', width: 2 },
    }
  })
  console.log('schema图表数据', schemaData)
}

const getExtractChartList = async () => {
  console.log(props.data)

  const res = await reqGetReasonerData({
    id: props?.data?.id,
    jobId: props?.data?.taskId,
  })
  extractData.demoDataListData = res.resultNodes.map((item: any) => {
    return {
      ...item,
      label: { show: true, position: 'bottom' },
      labels: item.label,
      itemStyle: { color: getRandomColor() },
    }
  })
  extractData.initLinkListData = res.resultEdges.map((item: any) => {
    return {
      ...item,
      source: item.from, // 一定要确保source、target字段是字符串类型
      target: item.to,
      // level: item.originBelongToProject,
      lineStyle: { curveness: 0.3, color: '#2596FF', width: 2 },
    }
  })
  console.log('extract图表数据', res)
}

const getSampleMap = async () => {
  const res = await reqGetSampleMap({
    projectId: props.data?.projectId,
  })
  sampleMap.value = res
  console.log('抽样数据', res)
}

onBeforeMount(() => {
  getSchemaChartList()
  getExtractChartList()
  getSampleMap()
})
</script>

<template>
  <a-drawer
    width="100%"
    v-model:open="extraOpen"
    root-class-name="root-extract"
    :root-style="{ color: 'blue' }"
    :body-style="{ background: '#ebf2f9' }"
    placement="right"
    :get-container="false"
    :mask="false"
    :push="false"
    @after-open-change="afterOpenChange"
  >
    <template #title>
      <div style="display: flex; align-items: center; gap: 8px">
        <span>{{ props?.data?.jobName }}</span>
        <a-tag color="processing">文档已导入</a-tag>
      </div>
    </template>
    <div class="title">
      <div>{{ showPage === ShowPage.EXTRACT ? '抽取结果[抽样]' : 'Schema预览' }}</div>
      <a-button @click="handleChangePage">{{
        showPage === ShowPage.EXTRACT ? '查看Schema' : '返回抽取结果'
      }}</a-button>
    </div>
    <div class="container-page">
      <keep-alive>
        <component
          :is="pageMap[showPage]"
          :schemaData="schemaData"
          :extractData="extractData"
          :sampleMap="sampleMap"
        ></component>
      </keep-alive>
    </div>
  </a-drawer>
</template>

<style scoped lang="less">
.title {
  color: #000;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  & > div {
    font-size: 20px;
    font-weight: 500;
  }
}
.container-page {
  width: 100%;
  height: calc(100% - 64px);
  background: #fff;
}
</style>
