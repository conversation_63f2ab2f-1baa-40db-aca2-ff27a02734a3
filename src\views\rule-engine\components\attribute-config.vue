<template>
  <main>
    <Space direction="vertical" size="middle" class="wfull">
      <div class="flex gap-2 wfull" v-for="(item, index) in data" :key="index">
        <Input class="w-30%" :value="item.key" @change="(e) => handleChange(e, index, 'key')" />
        <Input
          class="w-full"
          :value="item.value"
          @change="(e) => handleChange(e, index, 'value')"
        />
      </div>
    </Space>
    <Button class="wfull text-#9d9d9d mt-4" b="1px dashed #d9d9d9" @click="addAttribute">
      添加参数
    </Button>
  </main>
</template>

<script setup lang="ts">
import { Button, Input, message, Space } from 'ant-design-vue'
const attributes = defineModel<Record<string, any>>('value', { required: true })

const props = defineProps({
  paramsInfo: {
    type: Object,
    required: false,
  },
})

const data = ref<{ key: string; value: string }[]>([])

watch(
  () => props.paramsInfo,
  (newValue) => {
    if (newValue) {
      data.value = newValue.map((item: any) => ({ key: item.key }))
    }
  },
  { immediate: true, deep: true },
)

const addAttribute = () => {
  data.value.push({ key: '', value: '' })
}
const handleChange = (e: Event, index: number, type: 'key' | 'value') => {
  const value = (e.target as HTMLInputElement).value
  if (type === 'key') {
    // 检查key是否重复
    const keys = data.value.map((item) => item.key)
    if (keys.includes(value)) {
      message.warning('key不可以重复')

      return
    }
  }
  data.value[index][type] = value
  const newAttributes = data.value.reduce(
    (acc: Record<string, any>, curr) => {
      acc[curr.key] = curr.value
      return acc
    },
    {} as Record<string, any>,
  )
  console.log(newAttributes)
  attributes.value = newAttributes
}
</script>

<style scoped></style>
