import service from '@/api'

interface GridCol {
  gridCode: string
  gridName: string
  gridId: string
  width: number
  gridColumns: Partial<GridColumnsDto>[]
}

export class SysGridColumnDTO {
  /** 列标题 */
  columnCaption!: string

  /** 编号 */
  columnId!: string

  /** 列数据类型 */
  columnType?: string

  /** 列格式化参数 */
  columnTypeParam?: string

  /** 字段名 */
  fieldName!: string

  /** 宽度 */
  width?: string

  /** 所属表单编号 */
  gridId!: string

  /** 排序号 */
  priority?: number
}

export class SysGridDTO {
  /** 创建人 */
  createUser?: string

  /** 创建人登录名 */
  createUserName?: string

  /** 创建时间 */
  gmtCreate?: string

  /** 更新时间 */
  gmtModified?: string

  /** 表单码 */
  gridCode?: string

  /** 表单列 */
  gridColumns?: SysGridColumnDTO[]

  /** 表单编号 */
  gridId!: string

  /** 表单名称 */
  gridName!: string

  /** 菜单号 */
  menuId?: string

  /** 更新人 */
  modifiedUser?: string

  /** 更新人登录名 */
  modifiedUserName?: string

  /** 排序号 */
  priority?: number
}

export function getMenuGrids(menuId: string, headers: any = {}) {
  return service({
    baseURL: '/api/301UC',
    url: `/formManage/getGridList`,
    method: 'get',
    params: {
      menuId,
    },
    headers,
  })
}

export interface GridColumnsDto {
  show: boolean
  columnInfo: {
    /* 字段在卡片展示的类型 */
    type: string
    /* 字段名 */
    columnTitle?: string
    /* 字段对应的图标 */
    icon?: string
    /* 字段文字的色值 */
    color?: string
    /* 字段值对应文字的背景色 */
    bgColor?: string
    /* 排序 */
    sort?: number
    /* 字段值过滤器 */
    filter?: { [x: string]: any }
    /* 字段值对应的标签类型过滤器 */
    filterBadge?: { [x: string]: any }
  }
}

// export function useMenuGrids() {
//   const gridCol = ref<GridCol[]>([])
//   const columns = ref<Array<GridColumnsDto>>([])
//   const actionWidth = ref<number>(135)
//   const scrollY = ref<number>(0)
//   const pageY = ref<number>(0)
//   const showMore = ref<boolean>(false)
//   const pagination = ref<any>({
//     total: 0,
//     size: 'small',
//     pageIndex: 1,
//     pageSize: 24, // 每页中显示多少条数据
//     showSizeChanger: true,
//     pageSizeOptions: ['12', '24', '36', '48'], // 每页中显示的数据
//     showQuickJumper: true, // 是否可以快速跳转至某页
//   })
//   const editingKey = ref<string>('')

//   const route = useRoute()
//   const router = useRouter()

//   onBeforeMount(() => {
//     const menuId = route.meta._id
//     const { userId, tenantId } = this.$store.state.user.info
//     console.log(4444, this.$store.state.user.info)
//     const header = {
//       opUser: userId,
//       tenantId,
//     }
//     if (!menuId) {
//       throw new Error('该路由没有menuId')
//     }
//     getMenuGrids(menuId, this.axios, header).then((res: Ajax.Response<SysGridDTO[]>) => {
//       const interMenu = JSON.parse(localStorage.getItem('interMenu') as any)
//       // 国际化暂时屏蔽
//       // this.recursionReplace(res.data, interMenu)
//       // @ts-ignore
//       this.gridCol = res.data.map((i) => {
//         let width = 0 + this.actionWidth
//         const gridColumns = (i.gridColumns || [])
//           .map<Partial<Column>>((ii) => {
//             let colWidth = Number(ii.width) || 180
//             width += colWidth
//             let obj: Partial<GridColumnsDto> = {
//               title: ii.columnCaption,
//               dataIndex: ii.columnName,
//               show: true,
//               ellipsis: true,
//               width: colWidth,
//               align: 'center',
//               scopedSlots: { customRender: ii.columnName },
//             }
//             if (ii.columnType === '7') {
//               // eslint-disable-next-line no-new-func
//               obj.columnInfo =
//                 ii.columnTypeParam && ii.columnTypeParam[0] === '{'
//                   ? new Function('return ' + ii.columnTypeParam)()
//                   : {}
//             }
//             return obj
//           })
//           .concat({
//             // title: '操作',
//             title: (window as any)._Vue.$t('pages_index_core_mixins_MenuGrids_3963'),
//             // @ts-ignore
//             show: true,
//             width: this.actionWidth,
//             align: 'center',
//             fixed: 'right',
//             dataIndex: 'action',
//             scopedSlots: { customRender: 'action' },
//           })
//         return {
//           gridId: i.gridId,
//           gridName: i.gridName,
//           gridCode: i.gridCode,
//           width,
//           gridColumns,
//         }
//       })
//       this._gridChange()
//     })
//   })

//   return {
//     gridCol,
//     columns,
//     actionWidth,
//     scrollY,
//     pageY,
//     showMore,
//     pagination,
//     editingKey,
//   }
// }

// export default class MenuGrids extends Vue {
//   public gridCol: GridCol[] = []
//   columns: Array<GridColumnsDto> = [] // 用于页面卡片数据展示，加入排序功能
//   actionWidth: number = 135
//   scrollY: number = 0
//   pageY: number = 0
//   showMore = false // 是否展开页面全部查询信息
//   pagination = {
//     total: 0,
//     size: 'small',
//     pageIndex: 1,
//     pageSize: 24, // 每页中显示多少条数据
//     showSizeChanger: true,
//     pageSizeOptions: ['12', '24', '36', '48'], // 每页中显示的数据
//     showQuickJumper: true, // 是否可以快速跳转至某页
//   }
//   editingKey: string = '' // 当前编辑行
//   changedShowMore() {
//     this.reloadHeight()
//   }

//   mounted() {
//     // 设置表格主体宽高
//     this.$nextTick(() => {
//       /* 页面高度 - 页面除表格行的高度 */
//       this.pageY = document.body && document.body.offsetHeight
//       this.scrollY =
//         this.pageY - (this.$refs.pageTop ? (this.$refs.pageTop as any).offsetHeight : -10) - 325
//     })
//     // 表格的宽度随着窗口大小变化
//     window.onresize = () => {
//       if (this.$refs.table) {
//         this.scrollY = 0
//         setTimeout(() => {
//           this.pageY = document.body.offsetHeight
//           this.scrollY =
//             this.pageY - (this.$refs.pageTop ? (this.$refs.pageTop as any).offsetHeight : -10) - 325
//         }, 200)
//       }
//     }
//   }

//   created() {
//     const menuId = this.$route.meta._id
//     const { userId, tenantId } = this.$store.state.user.info
//     console.log(4444, this.$store.state.user.info)
//     const header = {
//       opUser: userId,
//       tenantId,
//     }
//     if (!menuId) {
//       throw new Error('该路由没有menuId')
//     }
//     getMenuGrids(menuId, this.axios, header).then((res: Ajax.Response<SysGridDTO[]>) => {
//       const interMenu = JSON.parse(localStorage.getItem('interMenu') as any)
//       // 国际化暂时屏蔽
//       // this.recursionReplace(res.data, interMenu)
//       // @ts-ignore
//       this.gridCol = res.data.map((i) => {
//         let width = 0 + this.actionWidth
//         const gridColumns = (i.gridColumns || [])
//           .map<Partial<Column>>((ii) => {
//             let colWidth = Number(ii.width) || 180
//             width += colWidth
//             let obj: Partial<GridColumnsDto> = {
//               title: ii.columnCaption,
//               dataIndex: ii.columnName,
//               show: true,
//               ellipsis: true,
//               width: colWidth,
//               align: 'center',
//               scopedSlots: { customRender: ii.columnName },
//             }
//             if (ii.columnType === '7') {
//               // eslint-disable-next-line no-new-func
//               obj.columnInfo =
//                 ii.columnTypeParam && ii.columnTypeParam[0] === '{'
//                   ? new Function('return ' + ii.columnTypeParam)()
//                   : {}
//             }
//             return obj
//           })
//           .concat({
//             // title: '操作',
//             title: (window as any)._Vue.$t('pages_index_core_mixins_MenuGrids_3963'),
//             // @ts-ignore
//             show: true,
//             width: this.actionWidth,
//             align: 'center',
//             fixed: 'right',
//             dataIndex: 'action',
//             scopedSlots: { customRender: 'action' },
//           })
//         return {
//           gridId: i.gridId,
//           gridName: i.gridName,
//           gridCode: i.gridCode,
//           width,
//           gridColumns,
//         }
//       })
//       this._gridChange()
//     })
//   }

//   //  递归替换菜单
//   recursionReplace(manu: any, interMenus: any) {
//     const lang = window.localStorage.getItem('lang') || 'zh-CN'
//     const find = (findVal: any) => {
//       return interMenus.filter((val: any) => {
//         if (val.ZH === findVal || val.EN === findVal) {
//           return val
//         }
//       })
//     }
//     const deep = (data: any) => {
//       data.forEach((item: any) => {
//         let info = find(item.columnCaption)
//         if (lang === 'zh-CN') {
//           item.columnCaption = info.length && info[0].ZH
//         } else {
//           item.columnCaption = info.length && info[0].EN
//         }
//         // if (item.children && item.children.length) deep(item.children)
//       })
//     }
//     deep(manu[0].gridColumns)
//     // return manu
//   }

//   $getGridName(index: number) {
//     return (this.gridCol[index] && this.gridCol[index].gridName) || ''
//   }

//   /**
//    * table列表展示数据用到的字段列表
//    * @param index 表单下标
//    * @param config 更多配置
//    * config.orderIndex: 是否自增序号
//    * config.hideactionable: 是否隐藏操作栏
//    * config.fixedAction: 固定操作栏
//    */
//   $getColumns(index: number, config: { [x: string]: any } = {}) {
//     if (config.gridCode) {
//       // 如果有配置 gridCode，优先使用 gridCode 查找表格列字段
//       const gridCode = config.gridCode
//       const grid = this.gridCol.find((i) => i.gridCode === gridCode)
//       if (grid) {
//         return grid.gridColumns
//       }
//     }
//     let data = JSON.parse(
//       JSON.stringify((this.gridCol[index] && this.gridCol[index].gridColumns) || []),
//     )
//     console.log('终于获取到了', data, this.gridCol, index)
//     // 是否隐藏操作栏
//     if (config.hideactionable) {
//       if (data.length > 1) data = data.slice(0, -1)
//     }
//     return data
//   }

//   /**
//    * 卡片展示数据用到的字段列表
//    * @param index 表单下标
//    */
//   $getCardColumns(index: number) {
//     const data = (this.gridCol[index] && this.gridCol[index].gridColumns) || []
//     return data.sort(this.compare('columnInfo'))
//   }

//   $getGridWidth(index: number) {
//     return (this.gridCol[index] && this.gridCol[index].width) || false
//   }

//   _gridChange() {
//     this.columns = this.$getCardColumns(0) as Array<GridColumnsDto>
//   }

//   /**
//    * 排序
//    * @param prop 排序根据的属性名称
//    */
//   compare(prop: string) {
//     return function (obj1: { [x: string]: any }, obj2: { [x: string]: any }) {
//       obj1[prop] = obj1[prop] || {}
//       obj2[prop] = obj2[prop] || {}
//       obj1[prop].sort = obj1[prop].sort ? Number(obj1[prop].sort) : 100
//       obj2[prop].sort = obj2[prop].sort ? Number(obj2[prop].sort) : 100
//       let val1 = obj1[prop].sort
//       let val2 = obj2[prop].sort
//       if (val1 < val2) {
//         return -1
//       } else if (val1 > val2) {
//         return 1
//       } else {
//         return 0
//       }
//     }
//   }

//   reloadHeight() {
//     if (this.$refs.table) {
//       this.scrollY = 0
//       setTimeout(() => {
//         this.pageY = document.body.offsetHeight
//         this.scrollY =
//           this.pageY - (this.$refs.pageTop ? (this.$refs.pageTop as any).offsetHeight : -10) - 335
//       }, 200)
//     }
//   }

//   // 删除最后一页最后一条数据页码问题
//   hackDeleteLastItem() {
//     let lastPage = Math.ceil(this.pagination.total / this.pagination.pageSize)
//     if (
//       this.pagination.pageIndex === lastPage &&
//       this.pagination.total % this.pagination.pageSize === 1
//     ) {
//       this.pagination.pageIndex--
//     }
//   }
// }
