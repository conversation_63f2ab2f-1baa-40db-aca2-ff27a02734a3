import { defineStore } from 'pinia'
import { useLocalStorage } from '@vueuse/core'

export const useHbaseStore = defineStore('hbase', () => {
  const treeDbData = useLocalStorage<any>('treeDbData', {
    expandedKeys: [], // 展开的节点
    selectedKeys: [], // 选中的节点
    treeDbData: [], // 树形数据
    dataRef: {}, // 当前选中的节点
  })
  /**
   * 保存页面数据
   */
  function setTreeDbData(key: string, data: any) {
    treeDbData.value[key] = data
  }

  function clearTreeDbData() {
    treeDbData.value = {
      expandedKeys: [],
      selectedKeys: [],
      treeDbData: [],
      dataRef: {},
    }
  }

  return {
    treeDbData,
    setTreeDbData,
    clearTreeDbData,
  }
})
