{"api": {"indexManage": {"system-config-detail": "{\"url\":\"http://**************:8112/?redirect=/login#/swaggerHome?uid=67ebb2b55e7e66acb0a9aab3&formShare=0\",\"time\":\"2025-06-09 09:17:41\"}", "indexForm": "{\"moduleName\":\"indexForm\",\"moduleId\":\"67ad978a6706a64d684a8aa6\",\"moduleNameCn\":\"指标配置模板管理\",\"interfaceInfos\":[{\"interfaceName\":\"addIndexTemplate\",\"interfaceNameCn\":\"新增指标模板\",\"description\":\"\",\"fullPath\":\"/indexManage/indexForm/addIndexTemplate\",\"httpMethodName\":\"POST\",\"params\":[{\"name\":\"formCode\",\"type\":\"string\",\"description\":\"指标配置模板编号\",\"required\":true},{\"name\":\"formName\",\"type\":\"string\",\"description\":\"指标配置模板名称\",\"required\":true},{\"name\":\"remarks\",\"type\":\"string\",\"description\":\" 备注\",\"required\":true},{\"name\":\"fieldsData\",\"type\":\"string\",\"description\":\"字段数据\",\"required\":false},{\"name\":\"compData\",\"type\":\"string\",\"description\":\"组件数据\",\"required\":false},{\"name\":\"businessType\",\"type\":\"number\",\"description\":\"业务类型1=301\",\"required\":true}],\"headers\":[{\"name\":\"opUser\",\"type\":\"string\",\"description\":\"\",\"required\":true},{\"name\":\"tenantId\",\"type\":\"string\",\"description\":\"\",\"required\":true},{\"name\":\"opUserName\",\"type\":\"string\",\"description\":\"\",\"required\":true}],\"type\":\"add\",\"check\":true},{\"interfaceName\":\"getIndexFormDataListByPage\",\"interfaceNameCn\":\"分页获取指标配置模板列表\",\"description\":\"新增接口\",\"fullPath\":\"/indexManage/indexForm/getIndexFormDataListByPage\",\"httpMethodName\":\"GET\",\"params\":[{\"name\":\"formName\",\"type\":\"string\",\"description\":\"指标配置模板名称\",\"required\":false},{\"name\":\"isDeleted\",\"type\":\"number\",\"description\":\"是否启用:1是0否\",\"required\":false},{\"name\":\"status\",\"type\":\"number\",\"description\":\"状态:0编辑中1已提交\",\"required\":false},{\"name\":\"startTime\",\"type\":\"string\",\"description\":\"开始时间\",\"required\":false},{\"name\":\"endTime\",\"type\":\"string\",\"description\":\"结束时间\",\"required\":false},{\"name\":\"isRelease\",\"type\":\"number\",\"description\":\"是否已发布:1是0否\",\"required\":false},{\"name\":\"businessType\",\"type\":\"number\",\"description\":\"业务类型\",\"required\":false},{\"name\":\"pageIndex\",\"type\":\"number\",\"description\":\"页码\",\"required\":true},{\"name\":\"pageSize\",\"type\":\"number\",\"description\":\"页大小\",\"required\":true}],\"headers\":[{\"name\":\"opUser\",\"type\":\"string\",\"description\":\"操作人\",\"required\":true},{\"name\":\"opUserName\",\"type\":\"string\",\"description\":\"操作人\",\"required\":true},{\"name\":\"tenantId\",\"type\":\"string\",\"description\":\"租户编号\",\"required\":false}],\"response\":[{\"name\":\"code\",\"isArray\":false,\"description\":\"状态码（000000：成功；其他：失败）\",\"type\":\"string\"},{\"name\":\"msg\",\"isArray\":false,\"description\":\"提示信息\",\"type\":\"string\"},{\"name\":\"data\",\"isArray\":false,\"description\":\"响应的业务数据内容\",\"type\":[{\"name\":\"records\",\"isArray\":true,\"description\":\"业务数据记录内容\",\"type\":[{\"name\":\"formId\",\"isArray\":false,\"description\":\"主键\",\"type\":\"string\"},{\"name\":\"formCode\",\"isArray\":false,\"description\":\"指标配置模板编号\",\"type\":\"string\"},{\"name\":\"formName\",\"isArray\":false,\"description\":\"指标配置模板名称\",\"type\":\"string\"},{\"name\":\"isDeleted\",\"isArray\":false,\"description\":\"是否启用:1是0否\",\"type\":\"number\"},{\"name\":\"status\",\"isArray\":false,\"type\":\"number\"},{\"name\":\"revision\",\"isArray\":false,\"description\":\"乐观锁\",\"type\":\"number\"},{\"name\":\"remarks\",\"isArray\":false,\"description\":\"备注\",\"type\":\"string\"},{\"name\":\"tenantId\",\"isArray\":false,\"description\":\"租户号\",\"type\":\"string\"},{\"name\":\"createUser\",\"isArray\":false,\"description\":\"创建人\",\"type\":\"string\"},{\"name\":\"createUsername\",\"isArray\":false,\"description\":\"创建人名称\",\"type\":\"string\"},{\"name\":\"createTime\",\"isArray\":false,\"description\":\"创建时间\",\"type\":\"string\"},{\"name\":\"updateUser\",\"isArray\":false,\"description\":\"更新人\",\"type\":\"string\"},{\"name\":\"updateUsername\",\"isArray\":false,\"description\":\"更新人名称\",\"type\":\"string\"},{\"name\":\"updateTime\",\"isArray\":false,\"description\":\"更新时间\",\"type\":\"string\"},{\"name\":\"isRelease\",\"isArray\":false,\"description\":\"是否已发布(1是0否)\",\"type\":\"number\"},{\"name\":\"currentVersion\",\"isArray\":false,\"description\":\"当前版本号\",\"type\":\"number\"}]}]}],\"type\":\"add\",\"check\":true},{\"interfaceName\":\"delIndexTemplate\",\"interfaceNameCn\":\"删除指标模板\",\"description\":\"新增接口\",\"fullPath\":\"/indexManage/indexForm/delIndexTemplate\",\"httpMethodName\":\"POST\",\"params\":[{\"name\":\"formId\",\"type\":\"string\",\"description\":\"主键id\",\"required\":false}],\"headers\":[{\"name\":\"opUser\",\"type\":\"string\",\"description\":\"操作人\",\"required\":true},{\"name\":\"opUserName\",\"type\":\"string\",\"description\":\"操作人\",\"required\":true},{\"name\":\"tenantId\",\"type\":\"string\",\"description\":\"租户编号\",\"required\":false}],\"response\":[{\"name\":\"code\",\"isArray\":false,\"description\":\"状态码（000000：成功；其他：失败）\",\"type\":\"string\"},{\"name\":\"msg\",\"isArray\":false,\"description\":\"提示信息\",\"type\":\"string\"},{\"name\":\"data\",\"isArray\":true,\"description\":\"响应的业务数据内容\",\"type\":[{\"name\":\"userId\",\"isArray\":false,\"description\":\"用户编号\",\"type\":\"string\"},{\"name\":\"userName\",\"isArray\":false,\"description\":\"用户名称\",\"type\":\"string\"}]}],\"type\":\"add\",\"check\":true},{\"interfaceName\":\"saveIndexTemplate\",\"interfaceNameCn\":\"保存指标模板\",\"description\":\"新增接口\",\"fullPath\":\"/indexManage/indexForm/saveIndexTemplate\",\"httpMethodName\":\"POST\",\"params\":[{\"name\":\"formId\",\"type\":\"string\",\"description\":\"主键id\",\"required\":true},{\"name\":\"formCode\",\"type\":\"string\",\"description\":\"指标配置模板编号\",\"required\":false},{\"name\":\"formName\",\"type\":\"string\",\"description\":\"指标配置模板名称\",\"required\":false},{\"name\":\"remarks\",\"type\":\"string\",\"description\":\"备注\",\"required\":false},{\"name\":\"fieldsData\",\"type\":\"string\",\"description\":\"字段数据\",\"required\":false},{\"name\":\"compData\",\"type\":\"string\",\"description\":\"组件数据\",\"required\":false},{\"name\":\"revision\",\"type\":\"number\",\"description\":\"乐观锁\",\"required\":false}],\"headers\":[{\"name\":\"opUser\",\"type\":\"string\",\"description\":\"操作人\",\"required\":true},{\"name\":\"opUserName\",\"type\":\"string\",\"description\":\"操作人\",\"required\":true},{\"name\":\"tenantId\",\"type\":\"string\",\"description\":\"租户编号\",\"required\":false}],\"response\":[{\"name\":\"code\",\"isArray\":false,\"description\":\"状态码（000000：成功；其他：失败）\",\"type\":\"string\"},{\"name\":\"msg\",\"isArray\":false,\"description\":\"提示信息\",\"type\":\"string\"},{\"name\":\"data\",\"isArray\":true,\"description\":\"响应的业务数据内容\",\"type\":[{\"name\":\"userId\",\"isArray\":false,\"description\":\"用户编号\",\"type\":\"string\"},{\"name\":\"userName\",\"isArray\":false,\"description\":\"用户名称\",\"type\":\"string\"}]}],\"type\":\"add\",\"check\":true},{\"interfaceName\":\"getValidIndexFormDataList\",\"interfaceNameCn\":\"获取有效指标配置模板列表\",\"description\":\"新增接口\",\"fullPath\":\"/indexManage/indexForm/getValidIndexFormDataList\",\"httpMethodName\":\"GET\",\"params\":[{\"name\":\"formName\",\"type\":\"string\",\"description\":\"指标配置模板名称\",\"required\":false}],\"headers\":[{\"name\":\"opUser\",\"type\":\"string\",\"description\":\"操作人\",\"required\":true},{\"name\":\"opUserName\",\"type\":\"string\",\"description\":\"操作人\",\"required\":true},{\"name\":\"tenantId\",\"type\":\"string\",\"description\":\"租户编号\",\"required\":false}],\"response\":[{\"name\":\"code\",\"isArray\":false,\"description\":\"状态码（000000：成功；其他：失败）\",\"type\":\"string\"},{\"name\":\"msg\",\"isArray\":false,\"description\":\"提示信息\",\"type\":\"string\"},{\"name\":\"data\",\"isArray\":true,\"description\":\"响应的业务数据内容\",\"type\":[{\"name\":\"formId\",\"isArray\":false,\"description\":\"主键\",\"type\":\"string\"},{\"name\":\"formCode\",\"isArray\":false,\"description\":\"指标配置模板编号\",\"type\":\"string\"},{\"name\":\"formName\",\"isArray\":false,\"description\":\"指标配置模板名称\",\"type\":\"string\"},{\"name\":\"historyId\",\"isArray\":false,\"description\":\"指标配置模板版本编号\",\"type\":\"string\"},{\"name\":\"fieldsData\",\"isArray\":false,\"description\":\"字段数据\",\"type\":\"string\"},{\"name\":\"compData\",\"isArray\":false,\"description\":\"组件数据\",\"type\":\"string\"},{\"name\":\"isDeleted\",\"isArray\":false,\"description\":\"是否启用:1是0否\",\"type\":\"number\"},{\"name\":\"status\",\"isArray\":false,\"type\":\"number\"},{\"name\":\"revision\",\"isArray\":false,\"description\":\"乐观锁\",\"type\":\"number\"},{\"name\":\"remarks\",\"isArray\":false,\"description\":\"备注\",\"type\":\"string\"},{\"name\":\"tenantId\",\"isArray\":false,\"description\":\"租户号\",\"type\":\"string\"},{\"name\":\"createUser\",\"isArray\":false,\"description\":\"创建人\",\"type\":\"string\"},{\"name\":\"createUsername\",\"isArray\":false,\"description\":\"创建人名称\",\"type\":\"string\"},{\"name\":\"createTime\",\"isArray\":false,\"description\":\"创建时间\",\"type\":\"string\"},{\"name\":\"updateUser\",\"isArray\":false,\"description\":\"更新人\",\"type\":\"string\"},{\"name\":\"updateUsername\",\"isArray\":false,\"description\":\"更新人名称\",\"type\":\"string\"},{\"name\":\"updateTime\",\"isArray\":false,\"description\":\"更新时间\",\"type\":\"string\"},{\"name\":\"isRelease\",\"isArray\":false,\"description\":\"是否已发布(1是0否)\",\"type\":\"number\"},{\"name\":\"currentVersion\",\"isArray\":false,\"description\":\"当前版本号\",\"type\":\"number\"}]}],\"type\":\"add\",\"check\":true},{\"interfaceName\":\"getSingleIndexFormData\",\"interfaceNameCn\":\"获取单个指标配置模板数据\",\"description\":\"新增接口\",\"fullPath\":\"/indexManage/indexForm/getSingleIndexFormData\",\"httpMethodName\":\"GET\",\"params\":[{\"name\":\"formId\",\"type\":\"string\",\"description\":\"指标配置模板主键\",\"required\":true}],\"headers\":[{\"name\":\"opUser\",\"type\":\"string\",\"description\":\"操作人\",\"required\":true},{\"name\":\"opUserName\",\"type\":\"string\",\"description\":\"操作人名称\",\"required\":true},{\"name\":\"tenantId\",\"type\":\"string\",\"description\":\"租户编号\",\"required\":false}],\"response\":[{\"name\":\"code\",\"isArray\":false,\"description\":\"状态码（000000：成功；其他：失败）\",\"type\":\"string\"},{\"name\":\"msg\",\"isArray\":false,\"description\":\"提示信息\",\"type\":\"string\"},{\"name\":\"data\",\"isArray\":false,\"description\":\"响应的业务数据内容\",\"type\":[{\"name\":\"formId\",\"isArray\":false,\"description\":\"主键\",\"type\":\"string\"},{\"name\":\"formCode\",\"isArray\":false,\"description\":\"指标配置模板编号\",\"type\":\"string\"},{\"name\":\"formName\",\"isArray\":false,\"description\":\"指标配置模板名称\",\"type\":\"string\"},{\"name\":\"fieldsData\",\"isArray\":false,\"description\":\"字段数据\",\"type\":\"string\"},{\"name\":\"compData\",\"isArray\":false,\"description\":\"组件数据\",\"type\":\"string\"},{\"name\":\"isDeleted\",\"isArray\":false,\"description\":\"是否启用:1是0否\",\"type\":\"number\"},{\"name\":\"status\",\"isArray\":false,\"type\":\"number\"},{\"name\":\"revision\",\"isArray\":false,\"description\":\"乐观锁\",\"type\":\"number\"},{\"name\":\"remarks\",\"isArray\":false,\"description\":\"备注\",\"type\":\"string\"},{\"name\":\"tenantId\",\"isArray\":false,\"description\":\"租户号\",\"type\":\"string\"},{\"name\":\"createUser\",\"isArray\":false,\"description\":\"创建人\",\"type\":\"string\"},{\"name\":\"createUsername\",\"isArray\":false,\"description\":\"创建人名称\",\"type\":\"string\"},{\"name\":\"createTime\",\"isArray\":false,\"description\":\"创建时间\",\"type\":\"string\"},{\"name\":\"updateUser\",\"isArray\":false,\"description\":\"更新人\",\"type\":\"string\"},{\"name\":\"updateUsername\",\"isArray\":false,\"description\":\"更新人名称\",\"type\":\"string\"},{\"name\":\"updateTime\",\"isArray\":false,\"description\":\"更新时间\",\"type\":\"string\"},{\"name\":\"isRelease\",\"isArray\":false,\"description\":\"是否已发布(1是0否)\",\"type\":\"number\"},{\"name\":\"currentVersion\",\"isArray\":false,\"description\":\"当前版本号\",\"type\":\"number\"},{\"name\":\"fieldList\",\"isArray\":true,\"description\":\"模板字段数组\",\"type\":[{\"name\":\"fieldId\",\"isArray\":false,\"description\":\"字段英文名\",\"type\":\"string\"},{\"name\":\"fieldName\",\"isArray\":false,\"description\":\"字段中文名\",\"type\":\"string\"},{\"name\":\"fieldType\",\"isArray\":false,\"description\":\"字段类型\",\"type\":\"string\"}]}]}],\"type\":\"add\",\"check\":true},{\"interfaceName\":\"deployIndexFormData\",\"interfaceNameCn\":\"指标配置模板发布\",\"description\":\"新增接口\",\"fullPath\":\"/indexManage/indexForm/deployIndexFormData\",\"httpMethodName\":\"POST\",\"params\":[{\"name\":\"formId\",\"type\":\"string\",\"description\":\"指标配置模板主键\",\"required\":true}],\"headers\":[{\"name\":\"opUser\",\"type\":\"string\",\"description\":\"操作人\",\"required\":true},{\"name\":\"opUserName\",\"type\":\"string\",\"description\":\"操作人名称\",\"required\":true},{\"name\":\"tenantId\",\"type\":\"string\",\"description\":\"租户编号\",\"required\":false}],\"response\":[{\"name\":\"code\",\"isArray\":false,\"description\":\"状态码（000000：成功；其他：失败）\",\"type\":\"string\"},{\"name\":\"msg\",\"isArray\":false,\"description\":\"提示信息\",\"type\":\"string\"},{\"name\":\"data\",\"isArray\":true,\"description\":\"响应的业务数据内容\"}],\"type\":\"add\",\"check\":true},{\"interfaceName\":\"getHistoryListPage\",\"interfaceNameCn\":\"获取指标配置模板历史版本数据列表\",\"description\":\"新增接口\",\"fullPath\":\"/indexManage/indexForm/getHistoryListPage\",\"httpMethodName\":\"GET\",\"params\":[{\"name\":\"formId\",\"type\":\"string\",\"description\":\"指标配置模板id\",\"required\":true},{\"name\":\"startTime\",\"type\":\"string\",\"description\":\"开始时间\",\"required\":false},{\"name\":\"endTime\",\"type\":\"string\",\"description\":\"结束时间\",\"required\":false},{\"name\":\"pageIndex\",\"type\":\"number\",\"description\":\"页码\",\"required\":true},{\"name\":\"pageSize\",\"type\":\"number\",\"description\":\"页大小\",\"required\":true}],\"headers\":[{\"name\":\"opUser\",\"type\":\"string\",\"description\":\"操作人\",\"required\":true},{\"name\":\"opUserName\",\"type\":\"string\",\"description\":\"操作人名称\",\"required\":true},{\"name\":\"tenantId\",\"type\":\"string\",\"description\":\"租户编号\",\"required\":false}],\"response\":[{\"name\":\"code\",\"isArray\":false,\"description\":\"状态码（000000：成功；其他：失败）\",\"type\":\"string\"},{\"name\":\"msg\",\"isArray\":false,\"description\":\"提示信息\",\"type\":\"string\"},{\"name\":\"data\",\"isArray\":false,\"description\":\"响应的业务数据内容\",\"type\":[{\"name\":\"records\",\"isArray\":true,\"description\":\"业务数据记录内容\",\"type\":[{\"name\":\"historyId\",\"isArray\":false,\"description\":\"主键\",\"type\":\"string\"},{\"name\":\"formId\",\"isArray\":false,\"description\":\"配置模板表id\",\"type\":\"string\"},{\"name\":\"formCode\",\"isArray\":false,\"description\":\"指标配置模板编号\",\"type\":\"string\"},{\"name\":\"formName\",\"isArray\":false,\"description\":\"指标配置模板名称\",\"type\":\"string\"},{\"name\":\"isDeleted\",\"isArray\":false,\"description\":\"是否启用:1是0否\",\"type\":\"number\"},{\"name\":\"revision\",\"isArray\":false,\"description\":\"乐观锁\",\"type\":\"number\"},{\"name\":\"remarks\",\"isArray\":false,\"description\":\"备注\",\"type\":\"string\"},{\"name\":\"version\",\"isArray\":false,\"description\":\"版本号\",\"type\":\"number\"},{\"name\":\"tenantId\",\"isArray\":false,\"description\":\"租户号\",\"type\":\"string\"},{\"name\":\"createUser\",\"isArray\":false,\"description\":\"创建人\",\"type\":\"string\"},{\"name\":\"createUsername\",\"isArray\":false,\"description\":\"创建人名称\",\"type\":\"string\"},{\"name\":\"createTime\",\"isArray\":false,\"description\":\"创建时间\",\"type\":\"string\"},{\"name\":\"updateUser\",\"isArray\":false,\"description\":\"更新人\",\"type\":\"string\"},{\"name\":\"updateUsername\",\"isArray\":false,\"description\":\"更新人名称\",\"type\":\"string\"},{\"name\":\"updateTime\",\"isArray\":false,\"description\":\"更新时间\",\"type\":\"string\"}]}]}],\"type\":\"add\",\"check\":true},{\"interfaceName\":\"getIndexFormByType\",\"interfaceNameCn\":\"根据类型获取单个指标配置模板数据\",\"description\":\"新增接口\",\"fullPath\":\"/indexManage/indexForm/getIndexFormByType\",\"httpMethodName\":\"GET\",\"params\":[{\"name\":\"businessType\",\"type\":\"number\",\"description\":\"业务类型1:301\",\"required\":false}],\"headers\":[{\"name\":\"opUser\",\"type\":\"string\",\"description\":\"操作人\",\"required\":true},{\"name\":\"opUserName\",\"type\":\"string\",\"description\":\"操作人名称\",\"required\":true},{\"name\":\"tenantId\",\"type\":\"string\",\"description\":\"租户编号\",\"required\":false}],\"response\":[{\"name\":\"code\",\"isArray\":false,\"description\":\"状态码（000000：成功；其他：失败）\",\"type\":\"string\"},{\"name\":\"msg\",\"isArray\":false,\"description\":\"提示信息\",\"type\":\"string\"},{\"name\":\"data\",\"isArray\":false,\"description\":\"响应的业务数据内容\",\"type\":[{\"name\":\"formId\",\"isArray\":false,\"description\":\"主键\",\"type\":\"string\"},{\"name\":\"formCode\",\"isArray\":false,\"description\":\"指标配置模板编号\",\"type\":\"string\"},{\"name\":\"formName\",\"isArray\":false,\"description\":\"指标配置模板名称\",\"type\":\"string\"},{\"name\":\"fieldsData\",\"isArray\":false,\"description\":\"字段数据\",\"type\":\"string\"},{\"name\":\"compData\",\"isArray\":false,\"description\":\"组件数据\",\"type\":\"string\"},{\"name\":\"isDeleted\",\"isArray\":false,\"description\":\"是否启用:1是0否\",\"type\":\"number\"},{\"name\":\"status\",\"isArray\":false,\"type\":\"number\"},{\"name\":\"revision\",\"isArray\":false,\"description\":\"乐观锁\",\"type\":\"number\"},{\"name\":\"remarks\",\"isArray\":false,\"description\":\"备注\",\"type\":\"string\"},{\"name\":\"tenantId\",\"isArray\":false,\"description\":\"租户号\",\"type\":\"string\"},{\"name\":\"createUser\",\"isArray\":false,\"description\":\"创建人\",\"type\":\"string\"},{\"name\":\"createUsername\",\"isArray\":false,\"description\":\"创建人名称\",\"type\":\"string\"},{\"name\":\"createTime\",\"isArray\":false,\"description\":\"创建时间\",\"type\":\"string\"},{\"name\":\"updateUser\",\"isArray\":false,\"description\":\"更新人\",\"type\":\"string\"},{\"name\":\"updateUsername\",\"isArray\":false,\"description\":\"更新人名称\",\"type\":\"string\"},{\"name\":\"updateTime\",\"isArray\":false,\"description\":\"更新时间\",\"type\":\"string\"},{\"name\":\"isRelease\",\"isArray\":false,\"description\":\"是否已发布(1是0否)\",\"type\":\"number\"},{\"name\":\"currentVersion\",\"isArray\":false,\"description\":\"当前版本号\",\"type\":\"number\"},{\"name\":\"fieldList\",\"isArray\":true,\"description\":\"模板字段数组\",\"type\":[{\"name\":\"fieldId\",\"isArray\":false,\"description\":\"字段英文名\",\"type\":\"string\"},{\"name\":\"fieldName\",\"isArray\":false,\"description\":\"字段中文名\",\"type\":\"string\"},{\"name\":\"fieldType\",\"isArray\":false,\"description\":\"字段类型\",\"type\":\"string\"}]}]}],\"type\":\"add\",\"check\":true}],\"check\":true}", "indexManage": "{\"moduleName\":\"indexManage\",\"moduleId\":\"67aeb1c46706a62be8d6bbf9\",\"moduleNameCn\":\"指标管理\",\"interfaceInfos\":[{\"interfaceName\":\"addNewIndexGroup\",\"interfaceNameCn\":\"新增指标分组\",\"description\":\"新增接口\",\"fullPath\":\"/indexManage/indexManage/addNewIndexGroup\",\"httpMethodName\":\"POST\",\"params\":[{\"name\":\"parentId\",\"type\":\"string\",\"description\":\"父节点ID\",\"required\":false},{\"name\":\"groupName\",\"type\":\"string\",\"description\":\"分组名称\",\"required\":true},{\"name\":\"remarks\",\"type\":\"string\",\"description\":\"备注\",\"required\":false}],\"headers\":[{\"name\":\"opUser\",\"type\":\"string\",\"description\":\"操作人\",\"required\":true},{\"name\":\"opUserName\",\"type\":\"string\",\"description\":\"操作人名称\",\"required\":true},{\"name\":\"tenantId\",\"type\":\"string\",\"description\":\"租户编号\",\"required\":false}],\"response\":[{\"name\":\"code\",\"isArray\":false,\"description\":\"状态码（000000：成功；其他：失败）\",\"type\":\"string\"},{\"name\":\"msg\",\"isArray\":false,\"description\":\"提示信息\",\"type\":\"string\"},{\"name\":\"data\",\"isArray\":true,\"description\":\"响应的业务数据内容\",\"type\":[{\"name\":\"userId\",\"isArray\":false,\"description\":\"用户编号\",\"type\":\"string\"},{\"name\":\"userName\",\"isArray\":false,\"description\":\"用户名称\",\"type\":\"string\"}]}],\"type\":\"add\",\"check\":true},{\"interfaceName\":\"getIndexGroupListByPage\",\"interfaceNameCn\":\"分页查询指标分组\",\"description\":\"新增接口\",\"fullPath\":\"/indexManage/indexManage/getIndexGroupListByPage\",\"httpMethodName\":\"GET\",\"params\":[{\"name\":\"groupName\",\"type\":\"string\",\"description\":\"分组名称\",\"required\":false},{\"name\":\"status\",\"type\":\"number\",\"description\":\"状态:1有效0无效\",\"required\":false},{\"name\":\"pageIndex\",\"type\":\"number\",\"description\":\"页码\",\"required\":true},{\"name\":\"pageSize\",\"type\":\"number\",\"description\":\"页大小\",\"required\":true}],\"headers\":[{\"name\":\"opUser\",\"type\":\"string\",\"description\":\"操作人\",\"required\":true},{\"name\":\"opUserName\",\"type\":\"string\",\"description\":\"操作人名称\",\"required\":true},{\"name\":\"tenantId\",\"type\":\"string\",\"description\":\"租户编号\",\"required\":false}],\"response\":[{\"name\":\"code\",\"isArray\":false,\"description\":\"状态码（000000：成功；其他：失败）\",\"type\":\"string\"},{\"name\":\"msg\",\"isArray\":false,\"description\":\"提示信息\",\"type\":\"string\"},{\"name\":\"data\",\"isArray\":false,\"description\":\"响应的业务数据内容\",\"type\":[{\"name\":\"records\",\"isArray\":true,\"description\":\"业务数据记录内容\",\"type\":[{\"name\":\"groupId\",\"isArray\":false,\"description\":\"分组主键\",\"type\":\"string\"},{\"name\":\"groupName\",\"isArray\":false,\"description\":\"组名称\",\"type\":\"string\"},{\"name\":\"status\",\"isArray\":false,\"description\":\"状态:1有效0无效\",\"type\":\"number\"},{\"name\":\"remarks\",\"isArray\":false,\"description\":\"备注\",\"type\":\"string\"},{\"name\":\"tenantId\",\"isArray\":false,\"description\":\"租户号\",\"type\":\"string\"},{\"name\":\"createUser\",\"isArray\":false,\"description\":\"创建人id\",\"type\":\"string\"},{\"name\":\"createUsername\",\"isArray\":false,\"description\":\"创建人名称\",\"type\":\"string\"},{\"name\":\"createTime\",\"isArray\":false,\"description\":\"创建时间\",\"type\":\"string\"},{\"name\":\"updateUser\",\"isArray\":false,\"description\":\"更新人\",\"type\":\"string\"},{\"name\":\"updateUsername\",\"isArray\":false,\"description\":\"更新人名称\",\"type\":\"string\"},{\"name\":\"updateTime\",\"isArray\":false,\"description\":\"更新时间\",\"type\":\"string\"}]}]}],\"type\":\"add\",\"check\":true},{\"interfaceName\":\"modifyIndexGroupData\",\"interfaceNameCn\":\"修改指标分组\",\"description\":\"新增接口\",\"fullPath\":\"/indexManage/indexManage/modifyIndexGroupData\",\"httpMethodName\":\"POST\",\"params\":[{\"name\":\"parentId\",\"type\":\"string\",\"description\":\"父节点ID\",\"required\":false},{\"name\":\"groupId\",\"type\":\"string\",\"description\":\"指标分组主键\",\"required\":true},{\"name\":\"groupName\",\"type\":\"string\",\"description\":\"分组名称\",\"required\":false},{\"name\":\"status\",\"type\":\"number\",\"description\":\"状态:1有效0无效\",\"required\":false},{\"name\":\"remarks\",\"type\":\"string\",\"description\":\"备注\",\"required\":false}],\"headers\":[{\"name\":\"opUser\",\"type\":\"string\",\"description\":\"操作人\",\"required\":true},{\"name\":\"opUserName\",\"type\":\"string\",\"description\":\"操作人名称\",\"required\":true},{\"name\":\"tenantId\",\"type\":\"string\",\"description\":\"租户编号\",\"required\":false}],\"response\":[{\"name\":\"code\",\"isArray\":false,\"description\":\"状态码（000000：成功；其他：失败）\",\"type\":\"string\"},{\"name\":\"msg\",\"isArray\":false,\"description\":\"提示信息\",\"type\":\"string\"},{\"name\":\"data\",\"isArray\":true,\"description\":\"响应的业务数据内容\"}],\"type\":\"add\",\"check\":true},{\"interfaceName\":\"getSingleIndexGroupDataDtl\",\"interfaceNameCn\":\"获取单个指标分组数据\",\"description\":\"新增接口\",\"fullPath\":\"/indexManage/indexManage/getSingleIndexGroupDataDtl\",\"httpMethodName\":\"GET\",\"params\":[{\"name\":\"groupId\",\"type\":\"string\",\"description\":\"指标分组主键\",\"required\":true}],\"headers\":[{\"name\":\"opUser\",\"type\":\"string\",\"description\":\"操作人\",\"required\":true},{\"name\":\"opUserName\",\"type\":\"string\",\"description\":\"操作人名称\",\"required\":true},{\"name\":\"tenantId\",\"type\":\"string\",\"description\":\"租户编号\",\"required\":false}],\"response\":[{\"name\":\"code\",\"isArray\":false,\"description\":\"状态码（000000：成功；其他：失败）\",\"type\":\"string\"},{\"name\":\"msg\",\"isArray\":false,\"description\":\"提示信息\",\"type\":\"string\"},{\"name\":\"data\",\"isArray\":false,\"description\":\"响应的业务数据内容\",\"type\":[{\"name\":\"groupId\",\"isArray\":false,\"description\":\"分组主键\",\"type\":\"string\"},{\"name\":\"groupName\",\"isArray\":false,\"description\":\"组名称\",\"type\":\"string\"},{\"name\":\"status\",\"isArray\":false,\"description\":\"状态:1有效0无效\",\"type\":\"number\"},{\"name\":\"remarks\",\"isArray\":false,\"description\":\"备注\",\"type\":\"string\"},{\"name\":\"tenantId\",\"isArray\":false,\"description\":\"租户号\",\"type\":\"string\"},{\"name\":\"createUser\",\"isArray\":false,\"description\":\"创建人id\",\"type\":\"string\"},{\"name\":\"createUsername\",\"isArray\":false,\"description\":\"创建人名称\",\"type\":\"string\"},{\"name\":\"createTime\",\"isArray\":false,\"description\":\"创建时间\",\"type\":\"string\"},{\"name\":\"updateUser\",\"isArray\":false,\"description\":\"更新人\",\"type\":\"string\"},{\"name\":\"updateUsername\",\"isArray\":false,\"description\":\"更新人名称\",\"type\":\"string\"},{\"name\":\"updateTime\",\"isArray\":false,\"description\":\"更新时间\",\"type\":\"string\"}]}],\"type\":\"add\",\"check\":true},{\"interfaceName\":\"addNewIndexData\",\"interfaceNameCn\":\"新增指标数据\",\"description\":\"新增接口\",\"fullPath\":\"/indexManage/indexManage/addNewIndexData\",\"httpMethodName\":\"POST\",\"params\":[{\"name\":\"formHistoryId\",\"type\":\"string\",\"description\":\"指标配置模板版本编号\",\"required\":true},{\"name\":\"indexName\",\"type\":\"string\",\"description\":\"指标名称\",\"required\":true},{\"name\":\"groupId\",\"type\":\"string\",\"description\":\"分组编号\",\"required\":false},{\"name\":\"parentId\",\"type\":\"string\",\"description\":\"指标父id\",\"required\":false},{\"name\":\"calFormula\",\"type\":\"string\",\"description\":\"计算公式/规则逻辑\",\"required\":false},{\"name\":\"dataSourceId\",\"type\":\"number\",\"description\":\"数据源编号\",\"required\":false},{\"name\":\"formData\",\"type\":\"string\",\"description\":\"指标配置数据\",\"required\":false},{\"name\":\"basicRuleList\",\"type\":\"Object\",\"description\":\"指标计算规则列表\",\"required\":false,\"children\":[{\"name\":\"ruleType\",\"type\":\"number\",\"description\":\"规则类型:1SQL脚本2shell脚本3python脚本\",\"required\":true},{\"name\":\"ruleText\",\"type\":\"string\",\"description\":\"参数配置\",\"required\":true},{\"name\":\"ruleScript\",\"type\":\"string\",\"description\":\"计算规则脚本\",\"required\":false}],\"isList\":true},{\"name\":\"remarks\",\"type\":\"string\",\"description\":\"描述\",\"required\":false}],\"headers\":[{\"name\":\"opUser\",\"type\":\"string\",\"description\":\"操作人\",\"required\":true},{\"name\":\"opUserName\",\"type\":\"string\",\"description\":\"操作人名称\",\"required\":true},{\"name\":\"tenantId\",\"type\":\"string\",\"description\":\"租户编号\",\"required\":true}],\"response\":[{\"name\":\"code\",\"isArray\":false,\"description\":\"状态码（000000：成功；其他：失败）\",\"type\":\"string\"},{\"name\":\"msg\",\"isArray\":false,\"description\":\"提示信息\",\"type\":\"string\"},{\"name\":\"data\",\"isArray\":true,\"description\":\"响应的业务数据内容\"}],\"type\":\"add\",\"check\":true},{\"interfaceName\":\"getIndexDataDtl\",\"interfaceNameCn\":\"获取指标数据详情\",\"description\":\"新增接口\",\"fullPath\":\"/indexManage/indexManage/getIndexDataDtl\",\"httpMethodName\":\"GET\",\"params\":[{\"name\":\"autoId\",\"type\":\"string\",\"description\":\"指标基础数据主键\",\"required\":true}],\"headers\":[{\"name\":\"opUser\",\"type\":\"string\",\"description\":\"操作人\",\"required\":true},{\"name\":\"opUserName\",\"type\":\"string\",\"description\":\"操作人名称\",\"required\":true},{\"name\":\"tenantId\",\"type\":\"string\",\"description\":\"租户编号\",\"required\":false}],\"response\":[{\"name\":\"code\",\"isArray\":false,\"description\":\"状态码（000000：成功；其他：失败）\",\"type\":\"string\"},{\"name\":\"msg\",\"isArray\":false,\"description\":\"提示信息\",\"type\":\"string\"},{\"name\":\"data\",\"isArray\":false,\"description\":\"响应的业务数据内容\",\"type\":[{\"name\":\"autoId\",\"isArray\":false,\"description\":\"主键\",\"type\":\"string\"},{\"name\":\"groupId\",\"isArray\":false,\"description\":\"指标分组id\",\"type\":\"string\"},{\"name\":\"parentId\",\"isArray\":false,\"description\":\"父指标id\",\"type\":\"string\"},{\"name\":\"formDataId\",\"isArray\":false,\"description\":\"指标配置数据id\",\"type\":\"string\"},{\"name\":\"indexName\",\"isArray\":false,\"description\":\"指标名称\",\"type\":\"string\"},{\"name\":\"calFormula\",\"isArray\":false,\"description\":\"计算公式/规则逻辑\",\"type\":\"string\"},{\"name\":\"formData\",\"isArray\":false,\"description\":\"指标配置数据\",\"type\":\"string\"},{\"name\":\"formHistoryId\",\"isArray\":false,\"description\":\"指标配置模板版本id\",\"type\":\"string\"},{\"name\":\"dataSourceId\",\"isArray\":false,\"description\":\"数据源编号\",\"type\":\"string\"},{\"name\":\"remarks\",\"isArray\":false,\"description\":\"描述\",\"type\":\"string\"},{\"name\":\"status\",\"isArray\":false,\"type\":\"number\"},{\"name\":\"revision\",\"isArray\":false,\"description\":\"乐观锁\",\"type\":\"number\"},{\"name\":\"isDeleted\",\"isArray\":false,\"description\":\"是否启用:1是0否\",\"type\":\"number\"},{\"name\":\"isRelease\",\"isArray\":false,\"description\":\"是否已发布(1是0否)\",\"type\":\"number\"},{\"name\":\"currentVersion\",\"isArray\":false,\"description\":\"当前版本号\",\"type\":\"number\"},{\"name\":\"tenantId\",\"isArray\":false,\"description\":\"租户号\",\"type\":\"string\"},{\"name\":\"basicRuleList\",\"isArray\":true,\"description\":\"指标计算规则数据表\",\"type\":[{\"name\":\"basicRuleId\",\"isArray\":false,\"description\":\"指标计算规则主键\",\"type\":\"string\"},{\"name\":\"basicDataId\",\"isArray\":false,\"description\":\"指标主键\",\"type\":\"string\"},{\"name\":\"ruleType\",\"isArray\":false,\"description\":\"规则类型:1SQL脚本2shell脚本3python脚本\",\"type\":\"number\"},{\"name\":\"ruleText\",\"isArray\":false,\"description\":\"参数配置\",\"type\":\"string\"},{\"name\":\"ruleScript\",\"isArray\":false,\"description\":\"计算规则脚本\",\"type\":\"string\"},{\"name\":\"createTime\",\"isArray\":false,\"description\":\"创建时间\",\"type\":\"string\"}]},{\"name\":\"createUser\",\"isArray\":false,\"description\":\"创建人id\",\"type\":\"string\"},{\"name\":\"createUsername\",\"isArray\":false,\"description\":\"创建人名称\",\"type\":\"string\"},{\"name\":\"createTime\",\"isArray\":false,\"description\":\"创建时间\",\"type\":\"string\"},{\"name\":\"updateUser\",\"isArray\":false,\"description\":\"更新人id\",\"type\":\"string\"},{\"name\":\"updateUsername\",\"isArray\":false,\"description\":\"更新人名称\",\"type\":\"string\"},{\"name\":\"updateTime\",\"isArray\":false,\"description\":\"更新时间\",\"type\":\"string\"}]}],\"type\":\"add\",\"check\":true},{\"interfaceName\":\"modifyIndexData\",\"interfaceNameCn\":\"编辑指标数据\",\"description\":\"新增接口\",\"fullPath\":\"/indexManage/indexManage/modifyIndexData\",\"httpMethodName\":\"POST\",\"params\":[{\"name\":\"autoId\",\"type\":\"string\",\"description\":\"指标基础数据主键\",\"required\":true},{\"name\":\"indexName\",\"type\":\"string\",\"description\":\"指标名称\",\"required\":false},{\"name\":\"groupId\",\"type\":\"string\",\"description\":\"分组编号\",\"required\":false},{\"name\":\"parentId\",\"type\":\"string\",\"description\":\"指标父id\",\"required\":false},{\"name\":\"calFormula\",\"type\":\"string\",\"description\":\"计算公式/规则逻辑\",\"required\":false},{\"name\":\"formData\",\"type\":\"string\",\"description\":\"指标配置数据\",\"required\":false},{\"name\":\"revision\",\"type\":\"number\",\"description\":\"乐观锁\",\"required\":true},{\"name\":\"basicRuleList\",\"type\":\"Object\",\"description\":\"指标计算规则列表\",\"required\":false,\"children\":[{\"name\":\"ruleType\",\"type\":\"number\",\"description\":\"规则类型:1SQL脚本2shell脚本3python脚本\",\"required\":false},{\"name\":\"ruleText\",\"type\":\"string\",\"description\":\"参数配置\",\"required\":true},{\"name\":\"ruleScript\",\"type\":\"string\",\"description\":\"计算规则脚本\",\"required\":false}],\"isList\":true},{\"name\":\"remarks\",\"type\":\"string\",\"description\":\"描述\",\"required\":false},{\"name\":\"isDeleted\",\"type\":\"number\",\"description\":\"是否启用:1是0否\",\"required\":false}],\"headers\":[{\"name\":\"opUser\",\"type\":\"string\",\"description\":\"操作人\",\"required\":true},{\"name\":\"opUserName\",\"type\":\"string\",\"description\":\"操作人名称\",\"required\":true},{\"name\":\"tenantId\",\"type\":\"string\",\"description\":\"租户编号\",\"required\":true}],\"response\":[{\"name\":\"code\",\"isArray\":false,\"description\":\"状态码（000000：成功；其他：失败）\",\"type\":\"string\"},{\"name\":\"msg\",\"isArray\":false,\"description\":\"提示信息\",\"type\":\"string\"},{\"name\":\"data\",\"isArray\":true,\"description\":\"响应的业务数据内容\"}],\"type\":\"add\",\"check\":true},{\"interfaceName\":\"deployIndexData\",\"interfaceNameCn\":\"发布指标数据\",\"description\":\"新增接口\",\"fullPath\":\"/indexManage/indexManage/deployIndexData\",\"httpMethodName\":\"POST\",\"params\":[{\"name\":\"autoId\",\"type\":\"string\",\"description\":\"指标基础数据主键\",\"required\":true}],\"headers\":[{\"name\":\"opUser\",\"type\":\"string\",\"description\":\"操作人\",\"required\":true},{\"name\":\"opUserName\",\"type\":\"string\",\"description\":\"操作人名称\",\"required\":true},{\"name\":\"tenantId\",\"type\":\"string\",\"description\":\"租户编号\",\"required\":true}],\"response\":[{\"name\":\"code\",\"isArray\":false,\"description\":\"状态码（000000：成功；其他：失败）\",\"type\":\"string\"},{\"name\":\"msg\",\"isArray\":false,\"description\":\"提示信息\",\"type\":\"string\"},{\"name\":\"data\",\"isArray\":true,\"description\":\"响应的业务数据内容\"}],\"type\":\"add\",\"check\":true},{\"interfaceName\":\"getIndexHistoryDataListByPages\",\"interfaceNameCn\":\"获取指标数据历史版本数据列表\",\"description\":\"新增接口\",\"fullPath\":\"/indexManage/indexManage/getIndexHistoryDataListByPages\",\"httpMethodName\":\"GET\",\"params\":[{\"name\":\"basicDataId\",\"type\":\"string\",\"description\":\"指标基础数据编号\",\"required\":true},{\"name\":\"startTime\",\"type\":\"string\",\"description\":\"开始时间\",\"required\":false},{\"name\":\"endTime\",\"type\":\"string\",\"description\":\"结束时间\",\"required\":false},{\"name\":\"pageIndex\",\"type\":\"number\",\"description\":\"页码\",\"required\":true},{\"name\":\"pageSize\",\"type\":\"number\",\"description\":\"页大小\",\"required\":true}],\"headers\":[{\"name\":\"opUser\",\"type\":\"string\",\"description\":\"操作人\",\"required\":true},{\"name\":\"opUserName\",\"type\":\"string\",\"description\":\"操作人名称\",\"required\":true},{\"name\":\"tenantId\",\"type\":\"string\",\"description\":\"租户编号\",\"required\":false}],\"response\":[{\"name\":\"code\",\"isArray\":false,\"description\":\"状态码（000000：成功；其他：失败）\",\"type\":\"string\"},{\"name\":\"msg\",\"isArray\":false,\"description\":\"提示信息\",\"type\":\"string\"},{\"name\":\"data\",\"isArray\":false,\"description\":\"响应的业务数据内容\",\"type\":[{\"name\":\"records\",\"isArray\":true,\"description\":\"业务数据记录内容\",\"type\":[{\"name\":\"historyId\",\"isArray\":false,\"description\":\"历史版本数据主键\",\"type\":\"string\"},{\"name\":\"basicDataId\",\"isArray\":false,\"description\":\"指标基础数据id\",\"type\":\"string\"},{\"name\":\"groupId\",\"isArray\":false,\"description\":\"分组id\",\"type\":\"string\"},{\"name\":\"parentId\",\"isArray\":false,\"description\":\"父指标id\",\"type\":\"string\"},{\"name\":\"formDataId\",\"isArray\":false,\"description\":\"指标配置数据id\",\"type\":\"string\"},{\"name\":\"indexName\",\"isArray\":false,\"description\":\"指标名称\",\"type\":\"string\"},{\"name\":\"formHistoryId\",\"isArray\":false,\"description\":\"指标配置模板版本id\",\"type\":\"string\"},{\"name\":\"formData\",\"isArray\":false,\"description\":\"指标配置数据内容\",\"type\":\"string\"},{\"name\":\"calFormula\",\"isArray\":false,\"description\":\"计算公式/规则逻辑\",\"type\":\"string\"},{\"name\":\"dataSourceId\",\"isArray\":false,\"description\":\"数据源编号\",\"type\":\"string\"},{\"name\":\"remarks\",\"isArray\":false,\"description\":\"描述\",\"type\":\"string\"},{\"name\":\"version\",\"isArray\":false,\"description\":\"版本号\",\"type\":\"number\"},{\"name\":\"revision\",\"isArray\":false,\"description\":\"乐观锁\",\"type\":\"number\"},{\"name\":\"isDeleted\",\"isArray\":false,\"description\":\"是否启用:1是0否\",\"type\":\"number\"},{\"name\":\"tenantId\",\"isArray\":false,\"description\":\"租户号\",\"type\":\"string\"},{\"name\":\"createUser\",\"isArray\":false,\"description\":\"创建人id\",\"type\":\"string\"},{\"name\":\"createUsername\",\"isArray\":false,\"description\":\"创建人名称\",\"type\":\"string\"},{\"name\":\"createTime\",\"isArray\":false,\"description\":\"创建时间\",\"type\":\"string\"},{\"name\":\"updateUser\",\"isArray\":false,\"description\":\"更新人id\",\"type\":\"string\"},{\"name\":\"updateUsername\",\"isArray\":false,\"description\":\"更新名称\",\"type\":\"string\"},{\"name\":\"updateTime\",\"isArray\":false,\"description\":\"更新时间\",\"type\":\"string\"}]}]}],\"type\":\"add\",\"check\":true},{\"interfaceName\":\"getIndexDataListByPages\",\"interfaceNameCn\":\"分页查询指标数据列表\",\"description\":\"新增接口\",\"fullPath\":\"/indexManage/indexManage/getIndexDataListByPages\",\"httpMethodName\":\"GET\",\"params\":[{\"name\":\"indexName\",\"type\":\"string\",\"description\":\"指标名称\",\"required\":false},{\"name\":\"groupId\",\"type\":\"string\",\"description\":\"分组编号\",\"required\":false},{\"name\":\"parentId\",\"type\":\"string\",\"description\":\"父指标id\",\"required\":false},{\"name\":\"formDataId\",\"type\":\"string\",\"description\":\"指标配置数据id\",\"required\":false},{\"name\":\"businessType\",\"type\":\"number\",\"description\":\"业务类型\",\"required\":false},{\"name\":\"formData\",\"type\":\"string\",\"description\":\"指标配置数据query\",\"required\":false},{\"name\":\"isDeleted\",\"type\":\"number\",\"description\":\"是否启用:1是0否\",\"required\":false},{\"name\":\"status\",\"type\":\"number\",\"description\":\"状态:0=编辑中1=已提交\",\"required\":false},{\"name\":\"startTime\",\"type\":\"string\",\"description\":\"开始时间\",\"required\":false},{\"name\":\"endTime\",\"type\":\"string\",\"description\":\"结束时间\",\"required\":false},{\"name\":\"isRelease\",\"type\":\"number\",\"description\":\"是否已发布:1是0否\",\"required\":false},{\"name\":\"pageIndex\",\"type\":\"number\",\"description\":\"页码\",\"required\":true},{\"name\":\"pageSize\",\"type\":\"number\",\"description\":\"页大小\",\"required\":true}],\"headers\":[{\"name\":\"opUser\",\"type\":\"string\",\"description\":\"操作人\",\"required\":true},{\"name\":\"opUserName\",\"type\":\"string\",\"description\":\"操作人名称\",\"required\":true},{\"name\":\"tenantId\",\"type\":\"string\",\"description\":\"租户编号\",\"required\":false}],\"response\":[{\"name\":\"code\",\"isArray\":false,\"description\":\"状态码（000000：成功；其他：失败）\",\"type\":\"string\"},{\"name\":\"msg\",\"isArray\":false,\"description\":\"提示信息\",\"type\":\"string\"},{\"name\":\"data\",\"isArray\":false,\"description\":\"响应的业务数据内容\",\"type\":[{\"name\":\"records\",\"isArray\":true,\"description\":\"业务记录数据\",\"type\":[{\"name\":\"autoId\",\"isArray\":false,\"description\":\"指标主键\",\"type\":\"string\"},{\"name\":\"groupId\",\"isArray\":false,\"description\":\"分组编号\",\"type\":\"string\"},{\"name\":\"parentId\",\"isArray\":false,\"description\":\"父指标id\",\"type\":\"string\"},{\"name\":\"formDataId\",\"isArray\":false,\"description\":\"指标配置数据id\",\"type\":\"string\"},{\"name\":\"dataSourceId\",\"isArray\":false,\"description\":\"数据源编号\",\"type\":\"number\"},{\"name\":\"indexName\",\"isArray\":false,\"description\":\"指标名称\",\"type\":\"string\"},{\"name\":\"calFormula\",\"isArray\":false,\"description\":\"计算公式/规则逻辑\",\"type\":\"string\"},{\"name\":\"remarks\",\"isArray\":false,\"description\":\"描述\",\"type\":\"string\"},{\"name\":\"status\",\"isArray\":false,\"description\":\"是否已发布:1是0否\",\"type\":\"number\"},{\"name\":\"revision\",\"isArray\":false,\"description\":\"乐观锁\",\"type\":\"number\"},{\"name\":\"isDeleted\",\"isArray\":false,\"description\":\"是否启用:1是0否\",\"type\":\"number\"},{\"name\":\"isRelease\",\"isArray\":false,\"description\":\"是否已发布:1是0否\",\"type\":\"number\"},{\"name\":\"currentVersion\",\"isArray\":false,\"description\":\"当前版本号\",\"type\":\"number\"},{\"name\":\"tenantId\",\"isArray\":false,\"description\":\"租户号\",\"type\":\"string\"},{\"name\":\"createUser\",\"isArray\":false,\"description\":\"创建人id\",\"type\":\"string\"},{\"name\":\"createUsername\",\"isArray\":false,\"description\":\"创建人名称\",\"type\":\"string\"},{\"name\":\"createTime\",\"isArray\":false,\"description\":\"创建时间\",\"type\":\"string\"},{\"name\":\"updateUser\",\"isArray\":false,\"description\":\"更新人id\",\"type\":\"string\"},{\"name\":\"updateUsername\",\"isArray\":false,\"description\":\"更新名称\",\"type\":\"string\"},{\"name\":\"updateTime\",\"isArray\":false,\"description\":\"更新时间\",\"type\":\"string\"}]}]}],\"type\":\"add\",\"check\":true},{\"interfaceName\":\"getGroupTree\",\"interfaceNameCn\":\"查询分组树形结构\",\"description\":\"\",\"fullPath\":\"/indexManage/indexManage/getGroupTree\",\"httpMethodName\":\"GET\",\"params\":[{\"name\":\"groupName\",\"type\":\"string\",\"description\":\"\",\"required\":false}],\"headers\":[{\"name\":\"opUser\",\"type\":\"string\",\"description\":\"\",\"required\":true},{\"name\":\"opUserName\",\"type\":\"string\",\"description\":\"\",\"required\":true}],\"type\":\"add\",\"check\":true},{\"interfaceName\":\"reviewByIndexId\",\"interfaceNameCn\":\"根据指标ID数据预览\",\"description\":\"根据指标ID数据预览\",\"fullPath\":\"/indexManage/indexManage/reviewByIndexId\",\"httpMethodName\":\"GET\",\"params\":[{\"name\":\"indexId\",\"type\":\"string\",\"description\":\"指标ID\",\"required\":true}],\"headers\":[],\"response\":[{\"name\":\"code\",\"isArray\":false,\"description\":\"状态码（000000：成功；其他：失败）\",\"type\":\"string\"},{\"name\":\"msg\",\"isArray\":false,\"description\":\"提示信息\",\"type\":\"string\"},{\"name\":\"data\",\"isArray\":false,\"description\":\"响应的业务数据内容\",\"type\":[{\"name\":\"cost\",\"isArray\":false,\"description\":\"耗时\",\"type\":\"number\"},{\"name\":\"dataList\",\"isArray\":true,\"description\":\"预览的数据列表\"}]}],\"type\":\"add\",\"check\":true},{\"interfaceName\":\"getIndexNameList\",\"interfaceNameCn\":\"查询指标下拉列表\",\"description\":\"\",\"fullPath\":\"/indexManage/indexManage/getIndexNameList\",\"httpMethodName\":\"GET\",\"params\":[{\"name\":\"indexName\",\"type\":\"string\",\"description\":\"\",\"required\":false},{\"name\":\"pageIndex\",\"type\":\"number\",\"description\":\"\",\"required\":true},{\"name\":\"pageSize\",\"type\":\"number\",\"description\":\"\",\"required\":true}],\"headers\":[],\"type\":\"add\",\"check\":true},{\"interfaceName\":\"getRealeaseIndexList\",\"interfaceNameCn\":\"分页查询已发布指标数据列表\",\"description\":\"新增接口\",\"fullPath\":\"/indexManage/indexManage/getRealeaseIndexList\",\"httpMethodName\":\"GET\",\"params\":[{\"name\":\"indexName\",\"type\":\"string\",\"description\":\"指标名称\",\"required\":false},{\"name\":\"pageIndex\",\"type\":\"number\",\"description\":\"页码\",\"required\":true},{\"name\":\"pageSize\",\"type\":\"number\",\"description\":\"页大小\",\"required\":true}],\"headers\":[],\"response\":[{\"name\":\"code\",\"isArray\":false,\"description\":\"状态码（000000：成功；其他：失败）\",\"type\":\"string\"},{\"name\":\"msg\",\"isArray\":false,\"description\":\"提示信息\",\"type\":\"string\"},{\"name\":\"data\",\"isArray\":false,\"description\":\"响应的业务数据内容\",\"type\":[{\"name\":\"records\",\"isArray\":true,\"description\":\"业务记录数据\",\"type\":[{\"name\":\"autoId\",\"isArray\":false,\"description\":\"指标主键\",\"type\":\"string\"},{\"name\":\"groupId\",\"isArray\":false,\"description\":\"分组编号\",\"type\":\"string\"},{\"name\":\"parentId\",\"isArray\":false,\"description\":\"父指标id\",\"type\":\"string\"},{\"name\":\"formDataId\",\"isArray\":false,\"description\":\"指标配置数据id\",\"type\":\"string\"},{\"name\":\"dataSourceId\",\"isArray\":false,\"description\":\"数据源编号\",\"type\":\"number\"},{\"name\":\"indexName\",\"isArray\":false,\"description\":\"指标名称\",\"type\":\"string\"},{\"name\":\"remarks\",\"isArray\":false,\"description\":\"描述\",\"type\":\"string\"},{\"name\":\"status\",\"isArray\":false,\"description\":\"是否已发布:1是0否\",\"type\":\"number\"},{\"name\":\"currentVersion\",\"isArray\":false,\"description\":\"当前版本号\",\"type\":\"number\"}]}]}],\"type\":\"add\",\"check\":true}],\"check\":true}", "dataSourceOptions": "{\"moduleName\":\"dataSourceOptions\",\"moduleId\":\"67b54c0cb6ffca3bc0fb1509\",\"moduleNameCn\":\"数据源选择\",\"interfaceInfos\":[{\"interfaceName\":\"getSqlSourceDataList\",\"interfaceNameCn\":\"获取数据源列表\",\"description\":\"新增接口\",\"fullPath\":\"/indexManage/dataSourceOptions/getSqlSourceDataList\",\"httpMethodName\":\"GET\",\"params\":[{\"name\":\"dbType\",\"type\":\"number\",\"description\":\"数据库类型(1:mysql;2:kudu;3:tidb;4:hive;5:oracle;6:sqlserver;7:cache;8:mongodb;9:file;10:Http WebApi)\",\"required\":false},{\"name\":\"ownerId\",\"type\":\"string\",\"description\":\"用户所属编号\",\"required\":false}],\"headers\":[{\"name\":\"opUser\",\"type\":\"string\",\"description\":\"操作人\",\"required\":true},{\"name\":\"opUserName\",\"type\":\"string\",\"description\":\"操作人名称\",\"required\":true},{\"name\":\"tenantId\",\"type\":\"string\",\"description\":\"租户编号\",\"required\":false}],\"response\":[{\"name\":\"code\",\"isArray\":false,\"description\":\"状态码（000000：成功；其他：失败）\",\"type\":\"string\"},{\"name\":\"msg\",\"isArray\":false,\"description\":\"提示信息\",\"type\":\"string\"},{\"name\":\"data\",\"isArray\":true,\"description\":\"响应的业务数据内容\",\"type\":[{\"name\":\"id\",\"isArray\":false,\"description\":\"数据源主键\",\"type\":\"number\"},{\"name\":\"dbType\",\"isArray\":false,\"description\":\"数据库类型(1:mysql;2:kudu;3:tidb;4:hive;5:oracle;6:sqlserver;7:cache;8:mongodb;9:file;10:Http WebApi)\",\"type\":\"number\"},{\"name\":\"dbUrl\",\"isArray\":false,\"description\":\"数据库连接路径\",\"type\":\"string\"},{\"name\":\"dbOther\",\"isArray\":false,\"description\":\"连接数据库所需的额外信息\",\"type\":\"string\"},{\"name\":\"dbDesc\",\"isArray\":false,\"description\":\"数据库的描述信息\",\"type\":\"string\"}]}],\"type\":\"add\",\"check\":true},{\"interfaceName\":\"getSqlDataBaseInfoList\",\"interfaceNameCn\":\"获取数据源的库信息列表\",\"description\":\"新增接口\",\"fullPath\":\"/indexManage/dataSourceOptions/getSqlDataBaseInfoList\",\"httpMethodName\":\"POST\",\"params\":[{\"name\":\"json\",\"type\":\"string\",\"description\":\"json参数(示例:\\\"{\\\\\\\"id\\\\\\\":1}\\\")\",\"required\":true}],\"headers\":[{\"name\":\"opUser\",\"type\":\"string\",\"description\":\"操作人\",\"required\":true},{\"name\":\"opUserName\",\"type\":\"string\",\"description\":\"操作人名称\",\"required\":true},{\"name\":\"tenantId\",\"type\":\"string\",\"description\":\"租户编号\",\"required\":false}],\"response\":[{\"name\":\"code\",\"isArray\":false,\"description\":\"状态码（000000：成功；其他：失败）\",\"type\":\"string\"},{\"name\":\"msg\",\"isArray\":false,\"description\":\"提示信息\",\"type\":\"string\"},{\"name\":\"data\",\"isArray\":true,\"description\":\"响应的业务数据内容\",\"type\":[{\"name\":\"dbTypMap\",\"isArray\":false,\"description\":\"数据库类型名称\",\"type\":\"string\"},{\"name\":\"scopedSlots\",\"isArray\":false,\"type\":[{\"name\":\"icon\",\"isArray\":false,\"description\":\"图标\",\"type\":\"string\"}]},{\"name\":\"title\",\"isArray\":false,\"description\":\"库名称\",\"type\":\"string\"},{\"name\":\"key\",\"isArray\":false,\"description\":\"库名称对应的key值\",\"type\":\"string\"}]}],\"type\":\"add\",\"check\":true},{\"interfaceName\":\"getSqlDateBaseTableInfoList\",\"interfaceNameCn\":\"获取数据源的表信息列表\",\"description\":\"新增接口\",\"fullPath\":\"/indexManage/dataSourceOptions/getSqlDateBaseTableInfoList\",\"httpMethodName\":\"POST\",\"params\":[{\"name\":\"dataSourceId\",\"type\":\"number\",\"description\":\"数据源ID\",\"required\":true},{\"name\":\"dataBaseName\",\"type\":\"string\",\"description\":\"数据库名称\",\"required\":true}],\"headers\":[{\"name\":\"opUser\",\"type\":\"string\",\"description\":\"操作人\",\"required\":true},{\"name\":\"opUserName\",\"type\":\"string\",\"description\":\"操作人名称\",\"required\":true},{\"name\":\"tenantId\",\"type\":\"string\",\"description\":\"租户编号\",\"required\":false}],\"response\":[{\"name\":\"code\",\"isArray\":false,\"description\":\"状态码（000000：成功；其他：失败）\",\"type\":\"string\"},{\"name\":\"msg\",\"isArray\":false,\"description\":\"提示信息\",\"type\":\"string\"},{\"name\":\"data\",\"isArray\":true,\"description\":\"响应的业务数据内容\",\"type\":[{\"name\":\"scopedSlots\",\"isArray\":false,\"type\":[{\"name\":\"icon\",\"isArray\":false,\"description\":\"图标\",\"type\":\"string\"}]},{\"name\":\"title\",\"isArray\":false,\"description\":\"表名称\",\"type\":\"string\"},{\"name\":\"key\",\"isArray\":false,\"description\":\"表名称对应的key值\",\"type\":\"string\"}]}],\"type\":\"add\",\"check\":true},{\"interfaceName\":\"getSqlDateBaseColumnParamsInfoList\",\"interfaceNameCn\":\"获取数据源的列字段信息列表\",\"description\":\"新增接口\",\"fullPath\":\"/indexManage/dataSourceOptions/getSqlDateBaseColumnParamsInfoList\",\"httpMethodName\":\"POST\",\"params\":[{\"name\":\"dataSourceId\",\"type\":\"number\",\"description\":\"数据源ID\",\"required\":true},{\"name\":\"dataBaseName\",\"type\":\"string\",\"description\":\"数据库名称\",\"required\":true},{\"name\":\"tableName\",\"type\":\"string\",\"description\":\"表名称\",\"required\":true}],\"headers\":[{\"name\":\"opUser\",\"type\":\"string\",\"description\":\"操作人\",\"required\":true},{\"name\":\"opUserName\",\"type\":\"string\",\"description\":\"操作人名称\",\"required\":true},{\"name\":\"tenantId\",\"type\":\"string\",\"description\":\"租户编号\",\"required\":false}],\"response\":[{\"name\":\"code\",\"isArray\":false,\"description\":\"状态码（000000：成功；其他：失败）\",\"type\":\"string\"},{\"name\":\"msg\",\"isArray\":false,\"description\":\"提示信息\",\"type\":\"string\"},{\"name\":\"data\",\"isArray\":true,\"description\":\"响应的业务数据内容\",\"type\":[{\"name\":\"scopedSlots\",\"isArray\":false,\"type\":[{\"name\":\"icon\",\"isArray\":false,\"description\":\"图标\",\"type\":\"string\"}]},{\"name\":\"title\",\"isArray\":false,\"description\":\"库名称\",\"type\":\"string\"},{\"name\":\"key\",\"isArray\":false,\"description\":\"库名称对应的key值\",\"type\":\"string\"},{\"name\":\"desc\",\"isArray\":false,\"description\":\"字段描述\",\"type\":\"string\"},{\"name\":\"disabled\",\"isArray\":false,\"description\":\"是否禁用\",\"type\":\"boolean\"},{\"name\":\"columnType\",\"isArray\":false,\"description\":\"字段类型[TEXT\",\"type\":\"string\"},{\"name\":\"columnName\",\"isArray\":false,\"description\":\"字段类型名称\",\"type\":\"string\"}]}],\"type\":\"add\",\"check\":true},{\"interfaceName\":\"executeSqlAndReturnResultData\",\"interfaceNameCn\":\"执行SQL并返回执行结果数据\",\"description\":\"新增接口\",\"fullPath\":\"/indexManage/dataSourceOptions/executeSqlAndReturnResultData\",\"httpMethodName\":\"POST\",\"params\":[{\"name\":\"id\",\"type\":\"number\",\"description\":\"数据源主键\",\"required\":true},{\"name\":\"dbName\",\"type\":\"string\",\"description\":\"库名称\",\"required\":true},{\"name\":\"querysql\",\"type\":\"string\",\"description\":\"执行SQL\",\"required\":true},{\"name\":\"ownerId\",\"type\":\"string\",\"description\":\"所属用户编号\",\"required\":true},{\"name\":\"tabName\",\"type\":\"string\",\"required\":false},{\"name\":\"pageNumber\",\"type\":\"number\",\"description\":\"页码\",\"required\":true},{\"name\":\"pageSize\",\"type\":\"number\",\"description\":\"页大小\",\"required\":true}],\"headers\":[{\"name\":\"opUser\",\"type\":\"string\",\"description\":\"操作人\",\"required\":true},{\"name\":\"opUserName\",\"type\":\"string\",\"description\":\"操作人名称\",\"required\":true},{\"name\":\"tenantId\",\"type\":\"string\",\"description\":\"租户编号\",\"required\":false}],\"response\":[{\"name\":\"code\",\"isArray\":false,\"description\":\"状态码（000000：成功；其他：失败）\",\"type\":\"string\"},{\"name\":\"msg\",\"isArray\":false,\"description\":\"提示信息\",\"type\":\"string\"},{\"name\":\"data\",\"isArray\":true,\"description\":\"响应的业务数据内容\"}],\"type\":\"add\",\"check\":true},{\"interfaceName\":\"getGenerateSql\",\"interfaceNameCn\":\"生成SQL\",\"description\":\"新增接口\",\"fullPath\":\"/indexManage/dataSourceOptions/getGenerateSql\",\"httpMethodName\":\"POST\",\"params\":[{\"name\":\"dataSourceId\",\"type\":\"number\",\"description\":\"数据源id\",\"required\":true},{\"name\":\"queryJson\",\"type\":\"string\",\"description\":\"查询参数json\",\"required\":true}],\"headers\":[{\"name\":\"opUser\",\"type\":\"string\",\"description\":\"操作人\",\"required\":true},{\"name\":\"opUserName\",\"type\":\"string\",\"description\":\"操作人名称\",\"required\":true},{\"name\":\"tenantId\",\"type\":\"string\",\"description\":\"租户编号\",\"required\":false}],\"response\":[{\"name\":\"code\",\"isArray\":false,\"description\":\"状态码（000000：成功；其他：失败）\",\"type\":\"string\"},{\"name\":\"msg\",\"isArray\":false,\"description\":\"提示信息\",\"type\":\"string\"},{\"name\":\"data\",\"isArray\":false,\"description\":\"响应的业务数据内容\",\"type\":[{\"name\":\"generateSql\",\"isArray\":false,\"description\":\"生成的SQL\",\"type\":\"string\"}]}],\"type\":\"add\",\"check\":true},{\"interfaceName\":\"getDataReview\",\"interfaceNameCn\":\"数据预览\",\"description\":\"新增接口\",\"fullPath\":\"/indexManage/dataSourceOptions/getDataReview\",\"httpMethodName\":\"POST\",\"params\":[{\"name\":\"dataSourceId\",\"type\":\"number\",\"description\":\"数据源id\",\"required\":true},{\"name\":\"queryJson\",\"type\":\"string\",\"description\":\"查询参数json\",\"required\":true}],\"headers\":[{\"name\":\"opUser\",\"type\":\"string\",\"description\":\"操作人\",\"required\":true},{\"name\":\"opUserName\",\"type\":\"string\",\"description\":\"操作人名称\",\"required\":true},{\"name\":\"tenantId\",\"type\":\"string\",\"description\":\"租户编号\",\"required\":false}],\"response\":[{\"name\":\"code\",\"isArray\":false,\"description\":\"状态码（000000：成功；其他：失败）\",\"type\":\"string\"},{\"name\":\"msg\",\"isArray\":false,\"description\":\"提示信息\",\"type\":\"string\"},{\"name\":\"data\",\"isArray\":false,\"description\":\"响应的业务数据内容\",\"type\":[{\"name\":\"cost\",\"isArray\":false,\"description\":\"耗时\",\"type\":\"number\"},{\"name\":\"dataList\",\"isArray\":true,\"description\":\"预览的数据列表\"}]}],\"type\":\"add\",\"check\":true},{\"interfaceName\":\"getDBTypeList\",\"interfaceNameCn\":\"查询数据库类型集合\",\"description\":\"\",\"fullPath\":\"/indexManage/dataSourceOptions/getDBTypeList\",\"httpMethodName\":\"GET\",\"params\":[],\"headers\":[],\"type\":\"add\",\"check\":true},{\"interfaceName\":\"getDataAssertDBList\",\"interfaceNameCn\":\"获取资产目录库信息列表\",\"description\":\"新增接口\",\"fullPath\":\"/indexManage/dataSourceOptions/getDataAssertDBList\",\"httpMethodName\":\"GET\",\"params\":[{\"name\":\"dbType\",\"type\":\"string\",\"description\":\"数据库类型\",\"required\":true}],\"headers\":[{\"name\":\"opUser\",\"type\":\"string\",\"description\":\"操作人\",\"required\":true},{\"name\":\"opUserName\",\"type\":\"string\",\"description\":\"操作人名称\",\"required\":true},{\"name\":\"tenantId\",\"type\":\"string\",\"description\":\"租户编号\",\"required\":false}],\"response\":[{\"name\":\"code\",\"isArray\":false,\"description\":\"状态码（000000：成功；其他：失败）\",\"type\":\"string\"},{\"name\":\"msg\",\"isArray\":false,\"description\":\"提示信息\",\"type\":\"string\"},{\"name\":\"data\",\"isArray\":true,\"description\":\"响应的业务数据内容\",\"type\":[{\"name\":\"dbTypMap\",\"isArray\":false,\"description\":\"数据库类型名称\",\"type\":\"string\"},{\"name\":\"scopedSlots\",\"isArray\":false,\"type\":[{\"name\":\"icon\",\"isArray\":false,\"description\":\"图标\",\"type\":\"string\"}]},{\"name\":\"title\",\"isArray\":false,\"description\":\"库名称\",\"type\":\"string\"},{\"name\":\"key\",\"isArray\":false,\"description\":\"库名称对应的key值\",\"type\":\"string\"}]}],\"type\":\"add\",\"check\":true},{\"interfaceName\":\"getDataAssetTableList\",\"interfaceNameCn\":\"获取资产目录表信息列表\",\"description\":\"新增接口\",\"fullPath\":\"/indexManage/dataSourceOptions/getDataAssetTableList\",\"httpMethodName\":\"GET\",\"params\":[{\"name\":\"databaseId\",\"type\":\"string\",\"description\":\"数据源ID\",\"required\":true}],\"headers\":[{\"name\":\"opUser\",\"type\":\"string\",\"description\":\"操作人\",\"required\":true},{\"name\":\"opUserName\",\"type\":\"string\",\"description\":\"操作人名称\",\"required\":true},{\"name\":\"tenantId\",\"type\":\"string\",\"description\":\"租户编号\",\"required\":false}],\"response\":[{\"name\":\"code\",\"isArray\":false,\"description\":\"状态码（000000：成功；其他：失败）\",\"type\":\"string\"},{\"name\":\"msg\",\"isArray\":false,\"description\":\"提示信息\",\"type\":\"string\"},{\"name\":\"data\",\"isArray\":true,\"description\":\"响应的业务数据内容\",\"type\":[{\"name\":\"title\",\"isArray\":false,\"description\":\"表名称\",\"type\":\"string\"},{\"name\":\"key\",\"isArray\":false,\"description\":\"表名称对应的key值\",\"type\":\"string\"},{\"name\":\"tableId\",\"isArray\":false,\"description\":\"表名称\",\"type\":\"string\"},{\"name\":\"databaseId\",\"isArray\":false,\"description\":\"数据库ID\",\"type\":\"string\"},{\"name\":\"comment\",\"isArray\":false,\"description\":\"表注释\",\"type\":\"string\"}]}],\"type\":\"add\",\"check\":true},{\"interfaceName\":\"getDataAssetColumnList\",\"interfaceNameCn\":\"获取资产目录表字段信息列表\",\"description\":\"新增接口\",\"fullPath\":\"/indexManage/dataSourceOptions/getDataAssetColumnList\",\"httpMethodName\":\"GET\",\"params\":[{\"name\":\"tableId\",\"type\":\"string\",\"description\":\"表名称\",\"required\":true}],\"headers\":[{\"name\":\"opUser\",\"type\":\"string\",\"description\":\"操作人\",\"required\":true},{\"name\":\"opUserName\",\"type\":\"string\",\"description\":\"操作人名称\",\"required\":true},{\"name\":\"tenantId\",\"type\":\"string\",\"description\":\"租户编号\",\"required\":false}],\"response\":[{\"name\":\"code\",\"isArray\":false,\"description\":\"状态码（000000：成功；其他：失败）\",\"type\":\"string\"},{\"name\":\"msg\",\"isArray\":false,\"description\":\"提示信息\",\"type\":\"string\"},{\"name\":\"data\",\"isArray\":true,\"description\":\"响应的业务数据内容\",\"type\":[{\"name\":\"scopedSlots\",\"isArray\":false,\"type\":[{\"name\":\"icon\",\"isArray\":false,\"description\":\"图标\",\"type\":\"string\"}]},{\"name\":\"title\",\"isArray\":false,\"description\":\"库名称\",\"type\":\"string\"},{\"name\":\"key\",\"isArray\":false,\"description\":\"库名称对应的key值\",\"type\":\"string\"},{\"name\":\"desc\",\"isArray\":false,\"description\":\"字段描述\",\"type\":\"string\"},{\"name\":\"disabled\",\"isArray\":false,\"description\":\"是否禁用\",\"type\":\"boolean\"},{\"name\":\"columnType\",\"isArray\":false,\"description\":\"字段类型[TEXT\",\"type\":\"string\"},{\"name\":\"columnName\",\"isArray\":false,\"description\":\"字段类型名称\",\"type\":\"string\"}]}],\"type\":\"add\",\"check\":true}],\"check\":true}", "bussinesManage": "{\"moduleName\":\"bussinesManage\",\"moduleId\":\"67db90215e7e66724019301a\",\"moduleNameCn\":\"业务类型管理\",\"interfaceInfos\":[{\"interfaceName\":\"indexbusinessDelete\",\"interfaceNameCn\":\"删除业务类型\",\"description\":\"根据businessId删除指标业务类型表数据\",\"fullPath\":\"/indexManage/bussinesManage/indexbusinessDelete\",\"httpMethodName\":\"POST\",\"params\":[{\"name\":\"businessId\",\"type\":\"number\",\"description\":\"业务类型\",\"required\":true}],\"headers\":[],\"type\":\"add\",\"check\":true},{\"interfaceName\":\"indexbusinessInsert\",\"interfaceNameCn\":\"新增业务类型\",\"description\":\"新增指标业务类型表数据\",\"fullPath\":\"/indexManage/bussinesManage/indexbusinessInsert\",\"httpMethodName\":\"POST\",\"params\":[{\"name\":\"businessName\",\"type\":\"string\",\"description\":\"业务类型名称\",\"required\":true},{\"name\":\"remark\",\"type\":\"string\",\"description\":\"备注\",\"required\":false}],\"headers\":[{\"name\":\"opUser\",\"type\":\"string\",\"description\":\"\",\"required\":true},{\"name\":\"opUserName\",\"type\":\"string\",\"description\":\"\",\"required\":true}],\"type\":\"add\",\"check\":true},{\"interfaceName\":\"indexbusinessUpdate\",\"interfaceNameCn\":\"修改业务类型\",\"description\":\"根据businessId修改指标业务类型表数据\",\"fullPath\":\"/indexManage/bussinesManage/indexbusinessUpdate\",\"httpMethodName\":\"POST\",\"params\":[{\"name\":\"businessName\",\"type\":\"string\",\"description\":\"业务类型名称\",\"required\":false},{\"name\":\"remark\",\"type\":\"string\",\"description\":\"备注\",\"required\":false},{\"name\":\"businessId\",\"type\":\"number\",\"description\":\"\",\"required\":true}],\"headers\":[{\"name\":\"opUser\",\"type\":\"string\",\"description\":\"\",\"required\":true},{\"name\":\"opUserName\",\"type\":\"string\",\"description\":\"\",\"required\":true}],\"type\":\"add\",\"check\":true},{\"interfaceName\":\"getIndexbusinessList\",\"interfaceNameCn\":\"查询业务类型列表\",\"description\":\"列表查询指标业务类型表数据\",\"fullPath\":\"/indexManage/bussinesManage/getIndexbusinessList\",\"httpMethodName\":\"GET\",\"params\":[{\"name\":\"businessName\",\"type\":\"string\",\"description\":\"业务类型名称\",\"required\":false},{\"name\":\"remark\",\"type\":\"string\",\"description\":\"备注\",\"required\":false}],\"headers\":[{\"name\":\"opUser\",\"type\":\"string\",\"description\":\"\",\"required\":true},{\"name\":\"opUserName\",\"type\":\"string\",\"description\":\"\",\"required\":true}],\"response\":[{\"name\":\"code\",\"isArray\":false,\"description\":\"\",\"type\":\"string\"},{\"name\":\"msg\",\"isArray\":false,\"description\":\"\",\"type\":\"string\"},{\"name\":\"data\",\"isArray\":false,\"description\":\"\",\"type\":[{\"name\":\"businessId\",\"isArray\":false,\"description\":\"业务类型ID\",\"type\":\"number\"},{\"name\":\"businessName\",\"isArray\":false,\"description\":\"业务类型名称\",\"type\":\"string\"},{\"name\":\"remark\",\"isArray\":false,\"description\":\"备注\",\"type\":\"string\"},{\"name\":\"createUser\",\"isArray\":false,\"description\":\"创建人\",\"type\":\"string\"},{\"name\":\"createTime\",\"isArray\":false,\"description\":\"创建时间\",\"type\":\"string\"}]}],\"type\":\"add\",\"check\":true},{\"interfaceName\":\"getBusinessPageList\",\"interfaceNameCn\":\"分页查询业务类型列表\",\"description\":\"分页查询指标业务类型表数据\",\"fullPath\":\"/indexManage/bussinesManage/getBusinessPageList\",\"httpMethodName\":\"GET\",\"params\":[{\"name\":\"businessName\",\"type\":\"string\",\"description\":\"业务类型名称\",\"required\":false},{\"name\":\"remark\",\"type\":\"string\",\"description\":\"备注\",\"required\":false},{\"name\":\"pageIndex\",\"type\":\"number\",\"description\":\"查询当前页数\",\"required\":true},{\"name\":\"pageSize\",\"type\":\"number\",\"description\":\"每页显示的记录条数\",\"required\":true}],\"headers\":[{\"name\":\"opUser\",\"type\":\"string\",\"description\":\"\",\"required\":true},{\"name\":\"opUserName\",\"type\":\"string\",\"description\":\"\",\"required\":true}],\"response\":[{\"name\":\"msg\",\"isArray\":false,\"description\":\"\",\"type\":\"string\"},{\"name\":\"code\",\"isArray\":false,\"description\":\"\",\"type\":\"string\"},{\"name\":\"data\",\"isArray\":false,\"description\":\"\",\"type\":[{\"name\":\"totalRecords\",\"isArray\":false,\"description\":\"总记录数\",\"type\":\"number\"},{\"name\":\"pageIndex\",\"isArray\":false,\"description\":\"页码\",\"type\":\"number\"},{\"name\":\"pageSize\",\"isArray\":false,\"description\":\"每页条数\",\"type\":\"number\"},{\"name\":\"totalPages\",\"isArray\":false,\"description\":\"总页数\",\"type\":\"number\"},{\"name\":\"records\",\"isArray\":true,\"description\":\"\",\"type\":[{\"name\":\"businessId\",\"isArray\":false,\"description\":\"业务类型Id\",\"type\":\"number\"},{\"name\":\"businessName\",\"isArray\":false,\"description\":\"业务类型名\",\"type\":\"string\"},{\"name\":\"remark\",\"isArray\":false,\"description\":\"备注\",\"type\":\"string\"},{\"name\":\"createUser\",\"isArray\":false,\"description\":\"创建人\",\"type\":\"string\"},{\"name\":\"createTime\",\"isArray\":false,\"description\":\"创建时间\",\"type\":\"string\"}]}]}],\"type\":\"add\",\"check\":true}],\"check\":true}"}}, "router": {"generate": "import type { RouteRecordRaw } from \"vue-router\";\n/**\n * 生成路由\n */\nconst routes: RouteRecordRaw[] = [\n  {\n    path: \"/indicators-template\",\n    name: \"indicators-template\",\n    component: () => import(\"@/views/indicators-template/index.vue\"),\n    meta: {},\n  },\n  {\n    path: \"/indicator-list\",\n    name: \"indicator-list\",\n    component: () => import(\"@/views/indicator-list/index.vue\"),\n    meta: {},\n  },\n  {\n    path: \"/indicator-config\",\n    name: \"indicator-config\",\n    component: () => import(\"@/views/indicator-config/index.vue\"),\n    meta: {},\n  },\n  {\n    path: \"/indicators-template/create\",\n    name: \"indicators-template/create\",\n    component: () => import(\"@/views/indicators-template/create/index.vue\"),\n    meta: {\n      breadcrumb: false,\n    },\n  },\n  {\n    path: \"/packet-management\",\n    name: \"packet-management\",\n    component: () => import(\"@/views/packet-management/index.vue\"),\n    meta: {},\n  },\n  {\n    path: \"/indicator-list/create\",\n    name: \"indicator-list/create\",\n    component: () => import(\"@/views/indicator-list/create/index.vue\"),\n    meta: {},\n  },\n  {\n    path: \"/rule-engine\",\n    name: \"rule-engine\",\n    component: () => import(\"@/views/rule-engine/index.vue\"),\n    meta: {},\n  },\n  {\n    path: \"//rule-engine/function\",\n    name: \"/rule-engine/function\",\n    component: () => import(\"@/views//rule-engine/function/index.vue\"),\n    meta: {\n      breadcrumb: false,\n    },\n  },\n  {\n    path: \"/rule-engine/function\",\n    name: \"rule-engine/function\",\n    component: () => import(\"@/views/rule-engine/function/index.vue\"),\n    meta: {\n      breadcrumb: false,\n    },\n  },\n  {\n    path: \"/rule-engine/rule-manage\",\n    name: \"rule-engine/rule-manage\",\n    component: () => import(\"@/views/rule-engine/rule-manage/index.vue\"),\n    meta: {\n      breadcrumb: false,\n    },\n  },\n  {\n    path: \"/rule-engine/function-manage\",\n    name: \"rule-engine/function-manage\",\n    component: () => import(\"@/views/rule-engine/function-manage/index.vue\"),\n    meta: {\n      breadcrumb: false,\n    },\n  },\n];\nexport default routes;\n"}, "views": {"rule-engine/rule-manage": "<script setup></script>\n<template>\n  <Container>\n    <template #headContent>headContent</template>\n    <template #default>规则管理</template>\n    <template #footer>footer</template>\n  </Container>\n</template>\n\n<script lang=\"ts\" setup>\nimport { Container } from \"@fs/fs-components\";\n</script>\n", "rule-engine/function-manage": "<script setup></script>\n<template>\n  <Container>\n    <template #headContent>headContent</template>\n    <template #default>函数管理</template>\n    <template #footer>footer</template>\n  </Container>\n</template>\n\n<script lang=\"ts\" setup>\nimport { Container } from \"@fs/fs-components\";\n</script>\n"}, "gpt-api": {"ckTvkajpIh": "{\"moduleName\":\"ckTvkajpIh\",\"moduleNameCn\":\"ckTvkajpIh\",\"interfaceInfos\":[{\"interfaceName\":\"createModal\",\"httpMethodName\":\"POST\",\"params\":[{\"name\":\"title\",\"description\":\"弹窗标题\",\"type\":\"string\",\"default\":\"\",\"required\":true,\"key\":2540583620810211},{\"name\":\"content\",\"description\":\"弹窗内容\",\"type\":\"string\",\"default\":\"\",\"required\":true,\"key\":7002874410763075},{\"name\":\"buttons\",\"description\":\"按钮配置\",\"type\":\"array\",\"default\":[],\"required\":false,\"children\":[{\"name\":\"text\",\"type\":\"string\",\"description\":\"按钮文本\",\"default\":\"\",\"required\":true,\"key\":2857108845352649},{\"name\":\"action\",\"type\":\"string\",\"description\":\"按钮点击后的操作\",\"default\":\"\",\"required\":false,\"key\":8434876472824031}],\"key\":3413385455572078}],\"response\":[{\"name\":\"code\",\"type\":\"string\",\"description\":\"状态码（000000：成功；其他：失败）\",\"key\":309999692679825},{\"name\":\"msg\",\"type\":\"string\",\"description\":\"提示信息\",\"key\":4079217752998032},{\"name\":\"data\",\"type\":[{\"name\":\"id\",\"type\":\"string\",\"description\":\"弹窗组件ID\",\"key\":1666133113323039},{\"name\":\"title\",\"type\":\"string\",\"description\":\"弹窗标题\",\"key\":4182929200286834},{\"name\":\"content\",\"type\":\"string\",\"description\":\"弹窗内容\",\"key\":5628079805528144},{\"name\":\"buttons\",\"type\":[{\"name\":\"text\",\"type\":\"string\",\"description\":\"按钮文本\"},{\"name\":\"action\",\"type\":\"string\",\"description\":\"按钮点击后的操作\"}],\"description\":\"按钮配置\",\"children\":[{\"name\":\"text\",\"type\":\"string\",\"description\":\"按钮文本\"},{\"name\":\"action\",\"type\":\"string\",\"description\":\"按钮点击后的操作\"}],\"key\":8052556931680251,\"isArray\":true}],\"description\":\"生成的弹窗组件信息\",\"children\":[{\"name\":\"id\",\"type\":\"string\",\"description\":\"弹窗组件ID\",\"key\":1666133113323039},{\"name\":\"title\",\"type\":\"string\",\"description\":\"弹窗标题\",\"key\":4182929200286834},{\"name\":\"content\",\"type\":\"string\",\"description\":\"弹窗内容\",\"key\":5628079805528144},{\"name\":\"buttons\",\"type\":[{\"name\":\"text\",\"type\":\"string\",\"description\":\"按钮文本\"},{\"name\":\"action\",\"type\":\"string\",\"description\":\"按钮点击后的操作\"}],\"description\":\"按钮配置\",\"children\":[{\"name\":\"text\",\"type\":\"string\",\"description\":\"按钮文本\"},{\"name\":\"action\",\"type\":\"string\",\"description\":\"按钮点击后的操作\"}],\"key\":8052556931680251,\"isArray\":true}],\"key\":2175159641012179,\"isArray\":false}],\"fullPath\":\"/component/modal/create\",\"interfaceNameCn\":\"生成一个弹窗组件\",\"returnType\":\"\"}]}"}}