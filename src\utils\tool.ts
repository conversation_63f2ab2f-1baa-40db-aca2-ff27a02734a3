import Mock from 'mockjs'

// 定义一个自定义字符集，包含字母、数字和下划线
const customCharset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789_'

/**
 * 从参数数据对象中提取参数名和类型，并格式化为字符串
 * @param {Record<string, any>} paramData - 参数数据对象，包含参数名和类型
 * @returns {string} - 格式化后的参数字符串，每个参数名后跟冒号和类型，参数之间用逗号分隔
 */
export function getParam(paramData: Record<string, any>) {
  let str = ''
  paramData.map((item: any, index: number) => {
    str += ` ${item.name}:${item.type}${index === paramData.length - 1 ? '' : ','}`
  })
  return str
}

/**
 * 从参数数据对象中提取参数名和类型，并格式化为字符串
 * @param {Record<string, any>} paramData - 参数数据对象，包含参数名和类型
 * @param {boolean} state - 是否返回字符串形式的参数数据，默认为 false
 * @returns {string} - 格式化后的参数字符串，每个参数名后跟冒号和类型，参数之间用逗号分隔
 */
export function getRetrun(paramData: any, state: boolean = false) {
  if (!paramData || paramData.length === 0) {
    return 'any'
  }
  let str = ''
  paramData.map((item: any) => {
    // item.isArray 为true 表示是数组对象类型
    str += ` ${item.name}:${Array.isArray(item.type) ? '{' + getRetrun(item.type, true) + `}${item?.isArray ? '[]' : ''}` : item.type}, `
  })
  if (state) return `${str}`
  return `{${str}}`
}

/**
 * 根据指定类型生成随机数据
 * @param {string} type - 要生成的数据类型，可以是 'string' 或 'number'
 * @returns {string | number} - 生成的随机数据，类型与指定的 type 参数一致
 * @description
 * 这个函数用于根据指定的类型生成随机数据。如果 type 是 'string'，则生成一个随机字符串；如果 type 是 'number'，则生成一个大于 0 的随机整数。
 *
 * @example
 * const randomString = generateDataFromList('string');
 * console.log(randomString); // 可能输出: "abcdefghijklmnopqrstuvwxyz"
 *
 * const randomNumber = generateDataFromList('number');
 * console.log(randomNumber); // 可能输出: 1234567890
 */
export function generateDataFromList(type: string) {
  let value
  if (type === 'string') {
    // 生成随机字符串
    value = Mock.Random.string(customCharset, 10)
  } else if (type === 'number') {
    // 生成大于 0 的整数
    value = Mock.Random.integer(1, Number.MAX_SAFE_INTEGER)
  }
  return value
}

/**
 * 生成常量值
 * @param {any} item - 输入的项，包含名称和类型等信息
 * @returns {any} - 根据项的名称生成相应的常量值，如果名称是 'pageIndex' 则返回 1，如果是 'pageSize' 则返回 20，其他情况调用 generateDataFromList 函数根据类型生成数据
 */
export function generateConstant(item: any) {
  if (item.name === 'pageIndex') {
    // 如果项的名称是 'pageIndex'，返回 1
    return 1
  } else if (item.name === 'pageSize') {
    // 如果项的名称是 'pageSize'，返回 20
    return 20
  } else {
    // 对于其他情况，调用 generateDataFromList 函数根据项的类型生成数据
    return generateDataFromList(item.type)
  }
}

/**
 * 将数据存储到本地存储中
 * @param {string} path - 存储数据的路径
 * @param {Record<string, any>} content - 要存储的数据对象
 * @description
 * 这个函数用于将数据对象存储到本地存储中。它首先从本地存储中获取名为 'mock-data' 的数据，如果没有找到，则返回一个空对象。然后，它检查数据对象中是否已经存在路径对应的数组，如果不存在，则创建一个空数组。接着，它将内容对象添加到数据对象中路径对应的数组中。最后，它将更新后的数据对象存储回本地存储中。
 *
 * @example
 * const path = 'test';
 * const content = { name: 'test', value: 'test' };
 * setLocalItem(path, content);
 * console.log(localStorage.getItem('mock-data')); // 可能输出: {"test":[{"name":"test","value":"test"}]}
 */
export function setLocalItem(path: string, content: Record<string, any>) {
  const data = JSON.parse(localStorage.getItem('mock-data') || '{}')
  if (!data[path]) data[path] = []
  data[path].unshift(content)
  localStorage.setItem('mock-data', JSON.stringify(data))
}

/**
 * 从本地存储中获取指定路径的数据
 * @param {string} path - 要获取数据的路径
 * @returns {Record<string, any>} - 获取到的数据对象，如果路径不存在则返回一个空对象
 * @description
 * 这个函数用于从本地存储中获取指定路径的数据。它首先从本地存储中获取名为 'mock-data' 的数据，如果没有找到，则返回一个空对象。然后，它返回数据对象中对应路径的数据，如果路径不存在，则返回一个空对象。
 *
 * @example
 * const path = 'test';
 * const data = getLocalItem(path);
 * console.log(data); // 可能输出: {"name":"test","value":"test"}
 */
export function getLocalItem(path: string) {
  const data = JSON.parse(localStorage.getItem('mock-data') || '{}')
  return data[path] || {}
}

/**
 * 解析 URL 并提取协议、IP 地址、端口和 UID
 * @param {string} url - 要解析的 URL
 * @returns {Object} - 包含协议、IP 地址、端口和 UID 的对象
 */
export function parseUrl(url: string) {
  // 使用 URL 类解析传入的 URL 字符串
  const urlObj = new URL(url)

  // 获取协议、IP 地址和端口，并格式化为字符串
  const ipWithPort = `${urlObj.protocol}//${urlObj.hostname}:${urlObj.port}`

  // 获取 URL 的哈希部分
  const hash = urlObj.hash

  // 如果哈希部分存在，则解析它以获取 UID 参数
  if (hash) {
    // 将哈希部分分割成键值对字符串
    const hashParams = new URLSearchParams(hash.split('?')[1])

    // 从键值对字符串中获取 UID 参数的值
    const uid = hashParams.get('uid')

    // 返回包含协议、IP 地址、端口和 UID 的对象
    return { ipWithPort, uid }
  }

  // 如果哈希部分不存在，则只返回协议、IP 地址和端口
  return { ipWithPort }
}

/**
 * 构建带有查询参数的 URL
 * @param {string} url - 基础 URL
 * @param {Object[]} params - 参数对象数组，每个对象包含 name 和 value 属性
 * @returns {string} - 构建完成的带有查询参数的 URL
 */
export const getUrl = (url: string, params: any[]) => {
  // 创建一个 URL 对象
  const urlObj = new URL(url)
  // 遍历参数数组
  params.forEach((param) => {
    // 将每个参数的名称和值添加到 URL 的 searchParams 中
    urlObj.searchParams.append(param.name, param.value.toString())
  })
  // 返回构建好的 URL 字符串
  return urlObj.toString()
}

/**
 * 转换时间为指定格式的字符串
 */

export const formatDate = (date: Date, format: string) => {
  const padZero = (num: number) => num.toString().padStart(2, '0')

  const dateObj = new Date(date)
  const hours = padZero(dateObj.getUTCHours())
  const minutes = padZero(dateObj.getUTCMinutes())
  const seconds = padZero(dateObj.getUTCSeconds())
  const milliseconds = padZero(dateObj.getUTCMilliseconds())

  if (format === 'HH:MM') {
    return `${hours}:${minutes}`
  } else if (format === 'HH:MM:SS') {
    return `${hours}:${minutes}:${seconds}`
  } else if (format === 'HH:MM:SS:MS') {
    return `${hours}:${minutes}:${seconds}:${milliseconds}`
  } else {
    throw new Error('Invalid format')
  }
}

/**
 * 转换时间为12小时制或者24小时制
 */
export const convertTimeTo12Or24Hour = (time: any, format: '12' | '24') => {
  const dateObj = new Date(time)
  const hours = dateObj.getHours()
  const minutes = dateObj.getMinutes()
  const seconds = dateObj.getSeconds()
  const ampm = hours >= 12 ? 'PM' : 'AM'
  const twelveHour = `${hours % 12 || 12}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')} ${ampm}`
  if (format === '12') {
    return twelveHour
  } else if (format === '24') {
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
  } else {
    throw new Error('Invalid format')
  }
}

/**
 * 判断是否为时间类型数据
 */
export const isTimeType = (type: string) => {
  return ['time', 'date', 'datetime'].includes(type)
}

/**
 * 创建uuid
 * @param n
 */
export const createUuid = (n = 36) => {
  const str = 'abcdefghijklmnopqrstuvwxyz0123456789'
  let result = ''
  for (let i = 0; i < n; i++) {
    result += str[parseInt(Math.random() * str.length + '')]
  }
  return result
}
