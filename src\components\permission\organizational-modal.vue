<template>
  <a-modal
    v-model:open="addOpen"
    @ok="modalHandleOk"
    :title="modalType === 'edit' ? '编辑' : '新增' + '组织'"
    @cancel="cancel"
    width="800px"
    :confirm-loading="confirmLoading"
  >
    <a-form
      ref="formRef"
      :model="form"
      :rules="rules"
      :label-col="labelCol"
      :wrapper-col="wrapperCol"
    >
      <a-form-item label="组织名称" name="deptName">
        <a-input v-model:value="form.deptName" placeholder="请输入组织名称" />
      </a-form-item>
      <a-form-item label="组织编号" name="deptId">
        <a-input v-model:value="form.deptId" :disabled="true" placeholder="请输入组织编号" />
      </a-form-item>
      <a-form-item label="上级编号" name="alias">
        <a-input v-model:value="form.alias" :disabled="true" placeholder="请输入上级编号" />
      </a-form-item>
      <a-form-item label="负责人" name="dutyusername">
        <a-input-search
          v-model:value="form.dutyusername"
          placeholder="请选择组织负责人"
          enter-button
          @search="onSearch"
        />
      </a-form-item>
      <a-form-item label="组织类型" name="deptType">
        <a-select v-model:value="form.deptType" placeholder="请选择组织类型">
          <a-select-option :value="item.dataId" v-for="item in deptTypeList" :key="item.dataId">{{
            item.dataName
          }}</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="是否启用" name="isUse">
        <a-radio-group v-model:value="form.isUse" placeholder="请选择是否启用">
          <a-radio :value="1">是</a-radio>
          <a-radio :value="0">否</a-radio>
        </a-radio-group>
      </a-form-item>
      <a-form-item label="序号" name="sortId">
        <a-input type="number" v-model:value="form.sortId" placeholder="请选择序号" />
      </a-form-item>
      <a-form-item label="备注" name="remark">
        <a-textarea v-model:value="form.remark" placeholder="请输入备注" />
      </a-form-item>
    </a-form>
  </a-modal>
  <organizational-user-modal ref="userOpenModal" @selectUserHandle="selectUserHandle" />
</template>
<script lang="ts" setup>
import { message } from 'ant-design-vue'
import type { Rule } from 'ant-design-vue/es/form'
import { defineEmits, defineProps, defineExpose, ref } from 'vue'
import { updateDept, addDept, viewDeptDtl } from '@/api/services/permission'
interface FormState {
  deptName: string
  parentId: string
  sortId: number | string
  deptType: string | undefined
  isUse: string
  remark: string
  dutyUserId: string
  alias: string
  deptId: string
  dutyusername: string
}
const emits = defineEmits(['modalHandleOk', 'modalCancel', 'confirmLoadingHandle'])
const formRef = ref<HTMLFormElement | null>(null)
const userOpenModal = ref<HTMLFormElement | null>(null)
const labelCol = { span: 5 }
const wrapperCol = { span: 13 }
const modalType = ref<string>('')
const confirmLoading = ref<boolean>(false)
const addOpen = ref<boolean>(false)
const deptTypeList = ref([
  {
    dataId: '',
    dataName: '',
  },
])
const form = ref<FormState>({
  deptName: '',
  parentId: '_top',
  alias: '',
  isUse: '',
  remark: '',
  deptType: undefined,
  sortId: '',
  dutyUserId: '',
  dutyusername: '',
  deptId: '',
})
defineProps({
  disabled: Boolean,
})
const rules: Record<string, Rule[]> = {
  deptName: [{ required: true, message: '请输入组织名称', trigger: 'blur' }],
  deptType: [{ required: true, message: '请选择组织类型', trigger: 'blur' }],
}

function modalHandleOk() {
  formRef.value &&
    formRef.value
      .validate()
      .then(() => {
        const { sortId } = form.value
        confirmLoading.value = true
        if (modalType.value === 'edit') {
          updateDept({ ...form.value })
            .then(() => {
              emits('modalHandleOk', form.value)
              message.success('更新成功!')
              addOpen.value = false
            })
            .finally(() => {
              confirmLoading.value = false
            })
        } else {
          if (modalType.value === 'add') {
            form.value.parentId = '_top'
          }
          form.value.sortId = Number(sortId)
          addDept({ ...form.value })
            .then(() => {
              emits('modalHandleOk', form.value)
              message.success('新增成功!')
              addOpen.value = false
            })
            .finally(() => {
              confirmLoading.value = false
            })
        }
      })
      .catch((error: any) => {
        console.log(error)
      })
}

function cancel() {
  emits('modalCancel')
  formRef.value?.resetFields()
}

function onSearch() {
  userOpenModal.value?.show()
}

function selectUserHandle(data: Record<string, any>) {
  form.value.dutyUserId = data?.userId
  form.value.dutyusername = data?.userName
}

function show(type: string, data?: Record<string, any>) {
  formRef.value?.resetFields()
  modalType.value = type
  // 获取组织类型,  设置上级编号
  deptTypeList.value = data?.deptTypeList
  form.value.parentId = data?.parentId
  addOpen.value = true
  if (type === 'edit') {
    editForm(data?.record, data?.type)
  } else if (type === 'add') {
    form.value.alias = ''
  } else if (type === 'addNext') {
    form.value.alias = data?.parentId
  }
}

function editForm(data: any, type: string) {
  if (data) {
    const copyData = JSON.parse(JSON.stringify(data))
    viewDeptDtl({ deptId: copyData.deptId }).then((res) => {
      const formValue: any = res?.data
      const {
        deptName,
        parentId,
        alias,
        isUse,
        remark,
        deptType,
        sortId,
        dutyUserId,
        dutyusername,
      } = formValue
      const data = {
        deptName,
        parentId,
        alias,
        remark,
        deptType: deptType,
        sortId,
        dutyUserId,
        dutyusername,
        isUse,
        deptId: copyData?.deptId,
      }
      form.value = data
      form.value.alias = type === 'addNextMenus' ? '' : res?.data?.parentId
    })
  }
}

defineExpose({ show })
</script>
