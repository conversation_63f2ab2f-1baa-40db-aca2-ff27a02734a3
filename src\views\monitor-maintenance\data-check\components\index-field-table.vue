<template>
  <a-drawer
    :width="720"
    destroyOnClose
    title="字段选择"
    class="drawer-con no_padding"
    :visible="visible"
    :maskClosable="false"
    @close="visible = false"
  >
    <div class="spin-box">
      <a-spin :spinning="loading">
        <a-button
          class="tableField-add-btn"
          type="dashed"
          block
          @click="addRow"
          style="margin-bottom: 16px"
          >新增字段</a-button
        >
        <a-table
          :columns="columns"
          size="small"
          ref="fieldTableRef"
          :data-source="dataList"
          :pagination="false"
          style="height: calc(100vh - 190px)"
          :scroll="{ y: 700 }"
          bordered
        >
          <template #bodyCell="{ column, record, index }">
            <template v-if="column.dataIndex === 'seqNo'">
              <span>{{ index + 1 }}</span>
            </template>

            <template v-if="column.dataIndex === 'indexName'">
              <a-input
                v-model:value="record.indexName"
                placeholder="请输入索引名"
                :disabled="!(record.type && record.type === 'add')"
              ></a-input>
            </template>
            <template v-if="column.dataIndex === 'index'">
              <a-select
                mode="multiple"
                style="width: 100%"
                v-model:value="record.index"
                placeholder="请选择字段名"
                option-label-prop="label"
                class="table-field-name-select"
                :disabled="!(record.type && record.type === 'add')"
                dropdownClassName="renderForm-tableField"
              >
                <a-select-option
                  v-for="(option, optKey) in optionData"
                  :class="`table-field-options-${optKey}`"
                  :label="option.name"
                  :title="option.name"
                  :key="optKey"
                  :value="option.name"
                  >{{ option.name }}</a-select-option
                >
              </a-select>
            </template>
            <template v-if="column.dataIndex === 'operation'">
              <a-popconfirm title="是否要删除此行？" @confirm="deleteRow(index, record)">
                <a-button type="danger" size="small">删除</a-button>
              </a-popconfirm>
            </template>
          </template>
        </a-table>
        <div class="footer-con">
          <a-button @click="visible = false">取消</a-button>
          <a-button class="multiple-ok-btn btn" type="primary" @click="handleSubmit">确定</a-button>
        </div>
      </a-spin>
    </div>
  </a-drawer>
</template>

<script setup lang="ts">
import { createIndexfun } from '@/api/services/monitor-maintenance/flow-definition'
import { logModal } from '@/utils/log-modal'
import { message } from 'ant-design-vue'
import { ref, watch, nextTick } from 'vue'

const emit = defineEmits(['on-success'])
const visible = ref(false)
const dataSource = ref<any[]>([])

const optionData = ref<any[]>([])
const columns = ref<any[]>([
  {
    title: '序号',
    align: 'center',
    dataIndex: 'seqNo',
    key: 'seqNo',
    width: 60,
    scopedSlots: { customRender: 'seqNo' },
  },
  {
    dataIndex: 'indexName',
    key: 'indexName',
    title: '索引名',
    align: 'center',
    scopedSlots: { customRender: 'indexName' },
  },
  {
    dataIndex: 'index',
    key: 'index',
    title: '字段名',
    align: 'center',
    scopedSlots: { customRender: 'index' },
  },
  {
    title: '操作',
    width: 80,
    key: 'operation',
    align: 'center',
    dataIndex: 'operation',
    scopedSlots: { customRender: 'operation' },
  },
])

const delList = ref<any[]>([])
const sourceRecord = ref<any>(null)
const loading = ref(false)

const dataList = computed(() => {
  return dataSource.value.filter((v: any) => v.type !== 'delete')
})

watch(
  () => visible.value,
  () => {
    if (!visible.value) dataSource.value = []
  },
)
const showModal = (record: any, dataSourceT?: any, optionDataT?: []) => {
  visible.value = true
  nextTick(() => {
    if (dataSourceT) {
      dataSource.value = JSON.parse(JSON.stringify(dataSourceT))
      console.log(dataSource.value)
      dataSource.value.forEach((item: any) => {
        item.type = 'edit'
      })
    }
    if (optionDataT) optionData.value = JSON.parse(JSON.stringify(optionDataT))
    if (record) sourceRecord.value = JSON.parse(JSON.stringify(record))
    delList.value = []
  })
}

const addRow = () => {
  dataSource.value.push({ index: [], indexName: '', type: 'add' })
}

const deleteRow = (index: number, record: any) => {
  if (record.type === 'add') {
    dataSource.value.splice(index, 1)
  } else {
    record.type = 'delete'
    dataSource.value[index].type = 'delete'
    delList.value.push(record)
  }
}

const handleSubmit = () => {
  let validateState = false
  const add = dataSource.value.filter((v: any) => v.type && v.type === 'add')
  if (delList.value.length || add.length) {
    validateState = dataSource.value.every((item: any) => {
      if (!item.index.length) {
        message.error('存在字段名为空，请检查')
        return false
      }
      return true
    })
    if (validateState) {
      let params = {
        json: JSON.stringify(sourceRecord.value),
        indexType: JSON.stringify({
          add,
          del: delList.value,
        }),
      }
      loading.value = true
      createIndexfun({ ...params }).then((res: any) => {
        loading.value = false
        if (res.data.code === 1) {
          message.success(res.data.message)
          visible.value = false
          delList.value = []
          emit('on-success')
        } else {
          logModal(res.data.obj, '创建目标表SQL', true, 60)
        }
      })
    }
  } else {
    visible.value = false
    emit('on-success', [])
  }
}
defineExpose({
  showModal,
})
</script>
<style lang="less" scope>
.no_padding .ant-drawer-body {
  padding-bottom: 0px !important;
  padding: 0;
  flex: auto;
  height: 0;
  overflow: hidden;
}
.spin-box {
  padding: 24px;
  overflow: hidden;
  .tableField-add-btn {
    border-color: #a59999;
  }
  .ant-table .ant-table-row:last-child .ant-table-cell {
    border-bottom: 1px solid #f0f0f0 !important;
  }
}
.footer-con {
  margin-top: 10px;
  text-align: right;
  .btn {
    margin-left: 10px;
  }
}
.ant-btn-danger {
  color: #fff;
  background-color: #ff4d4f;
  border-color: #ff4d4f;
}
.ant-btn-danger:focus,
.ant-btn-danger:hover {
  color: #fff;
  background-color: #ff7875;
  border-color: #ff7875;
}
</style>
