import { getUserMenu } from '@/api/services/permission'
import { storeToRefs } from 'pinia'
import { useUserStore } from '@/stores/user'
// import { templatePagesData } from '@/utils/tools-page'
import { mockPagesData } from '@/utils/mock-page'
import MenuData from '@/layout/login/init-menu'
import router from '@/router/core'
import { getUserInfo } from '@/api/services/permission'
import { getExternalLoginUrl } from '@/utils/auth-util'

/**
 * 获取用户信息
 */
export async function getUserInfoFn(token: string) {
  try {
    const userStore = useUserStore()
    const { userInfo } = storeToRefs(userStore)
    const { data } = await getUserInfo({ userToken: token })
    userInfo.value = data
  } catch (error) {
    console.log(error)
  }
}

/**
 * 获取用户菜单
 */
export async function getUserMenuFn(Refresh = true, userData?: any) {
  try {
    const userStore = useUserStore()
    const { userInfo, userMenu } = storeToRefs(userStore)
    const envMode = import.meta.env.MODE
    const { data } = await getUserMenu({
      userId: userInfo.value.userId || userData?.userId,
      menuType: 0,
    })
    let list: any[] = []
    if (envMode === 'development') {
      // list = [...data, ...templatePagesData, ...mockPagesData]
      list = [...data, ...mockPagesData]
    } else {
      list = [...data]
    }
    data &&
      MenuData(list, userMenu, userStore, () => {
        if (Refresh && import.meta.env.VITE_APP_NEED_AUTH !== 'false') router.replace('/')
      })
  } catch (error) {
    console.log(error)
  }
}

/**
 * 退出登录
 */
export async function logoutFn() {
  const userStore = useUserStore()
  userStore.clearLoginData()
  // 嵌入数据平台
  if (window.ENV.TO_THIRD_LOGIN) {
    const loginUrl = getExternalLoginUrl()
    window.location.href = loginUrl
  } else {
    router.push('/login')
  }
}
