import { expect, test } from 'vitest'
import {
  getSqlSourceDataList,
  getSqlDataBaseInfoList,
  getSqlDateBaseTableInfoList,
  getSqlDateBaseColumnParamsInfoList,
  executeSqlAndReturnResultData,
  getGenerateSql,
  getDataReview,
  getDBTypeList,
  getDataAssertDBList,
  getDataAssetTableList,
  getDataAssetColumnList,
} from './datasourceoptions'

test('getSqlSourceDataList', async () => {
  const { data } = await getSqlSourceDataList({
    dbType: 6721119314418906,
    ownerId: 'V[bt',
  })

  expect(data).toHaveProperty('code')
  expect(data).toHaveProperty('msg')
  expect(data.data[0]).toHaveProperty('id'),
    expect(data.data[0]).toHaveProperty('dbType'),
    expect(data.data[0]).toHaveProperty('dbUrl'),
    expect(data.data[0]).toHaveProperty('dbOther'),
    expect(data.data[0]).toHaveProperty('dbDesc')
})

test('getSqlDataBaseInfoList', async () => {
  const { data } = await getSqlDataBaseInfoList({ json: 'BME[z' })

  expect(data).toHaveProperty('code')
  expect(data).toHaveProperty('msg')
  expect(data.data[0]).toHaveProperty('dbTypMap'),
    expect(data.data[0]).toHaveProperty('scopedSlots'),
    expect(data.data[0]).toHaveProperty('title'),
    expect(data.data[0]).toHaveProperty('key')
})

test('getSqlDateBaseTableInfoList', async () => {
  const { data } = await getSqlDateBaseTableInfoList({
    dataSourceId: 4855300741833148,
    dataBaseName: '51ur6r',
  })

  expect(data).toHaveProperty('code')
  expect(data).toHaveProperty('msg')
  expect(data.data[0]).toHaveProperty('scopedSlots'),
    expect(data.data[0]).toHaveProperty('title'),
    expect(data.data[0]).toHaveProperty('key')
})

test('getSqlDateBaseColumnParamsInfoList', async () => {
  const { data } = await getSqlDateBaseColumnParamsInfoList({
    dataSourceId: 209703877886827,
    dataBaseName: 'iZ31UZL',
    tableName: '@6HB)',
  })

  expect(data).toHaveProperty('code')
  expect(data).toHaveProperty('msg')
  expect(data.data[0]).toHaveProperty('scopedSlots'),
    expect(data.data[0]).toHaveProperty('title'),
    expect(data.data[0]).toHaveProperty('key'),
    expect(data.data[0]).toHaveProperty('desc'),
    expect(data.data[0]).toHaveProperty('disabled'),
    expect(data.data[0]).toHaveProperty('columnType'),
    expect(data.data[0]).toHaveProperty('columnName')
})

test('executeSqlAndReturnResultData', async () => {
  const { data } = await executeSqlAndReturnResultData({
    id: 3610523200927048,
    dbName: '&SlwCf',
    querysql: '7KHG0B',
    ownerId: 'EKra',
    tabName: 'AjpFp6',
    pageNumber: 7673544427645119,
    pageSize: 5480076045088234,
  })

  expect(data).toHaveProperty('code')
  expect(data).toHaveProperty('msg')
  expect(data).toHaveProperty('data')
})

test('getGenerateSql', async () => {
  const { data } = await getGenerateSql({
    dataSourceId: 5536079551672842,
    queryJson: 'JZp',
  })

  expect(data).toHaveProperty('code')
  expect(data).toHaveProperty('msg')
  expect(data.data).toHaveProperty('generateSql')
})

test('getDataReview', async () => {
  const { data } = await getDataReview({
    dataSourceId: 1664010648009827,
    queryJson: '@Iu2rL',
  })

  expect(data).toHaveProperty('code')
  expect(data).toHaveProperty('msg')
  expect(data.data).toHaveProperty('cost'), expect(data.data).toHaveProperty('dataList')
})

test('getDBTypeList', async () => {
  const { data } = await getDBTypeList({})

  expect(data).toBeTruthy()
})

test('getDataAssertDBList', async () => {
  const { data } = await getDataAssertDBList({ dbType: ']%t0z' })

  expect(data).toHaveProperty('code')
  expect(data).toHaveProperty('msg')
  expect(data.data[0]).toHaveProperty('dbTypMap'),
    expect(data.data[0]).toHaveProperty('scopedSlots'),
    expect(data.data[0]).toHaveProperty('title'),
    expect(data.data[0]).toHaveProperty('key')
})

test('getDataAssetTableList', async () => {
  const { data } = await getDataAssetTableList({ databaseId: 'K]y' })

  expect(data).toHaveProperty('code')
  expect(data).toHaveProperty('msg')
  expect(data.data[0]).toHaveProperty('title'),
    expect(data.data[0]).toHaveProperty('key'),
    expect(data.data[0]).toHaveProperty('tableId'),
    expect(data.data[0]).toHaveProperty('databaseId'),
    expect(data.data[0]).toHaveProperty('comment')
})

test('getDataAssetColumnList', async () => {
  const { data } = await getDataAssetColumnList({ tableId: 'yxKn' })

  expect(data).toHaveProperty('code')
  expect(data).toHaveProperty('msg')
  expect(data.data[0]).toHaveProperty('scopedSlots'),
    expect(data.data[0]).toHaveProperty('title'),
    expect(data.data[0]).toHaveProperty('key'),
    expect(data.data[0]).toHaveProperty('desc'),
    expect(data.data[0]).toHaveProperty('disabled'),
    expect(data.data[0]).toHaveProperty('columnType'),
    expect(data.data[0]).toHaveProperty('columnName')
})
