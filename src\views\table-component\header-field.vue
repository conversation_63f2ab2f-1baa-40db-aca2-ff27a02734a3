<script setup lang="ts">
import { ref, onMounted, inject } from 'vue'
import {
  DownOutlined,
  FieldTimeOutlined,
  DashOutlined,
  <PERSON>UpOutlined,
  <PERSON>DownOutlined,
  EyeInvisibleOutlined,
  SettingOutlined,
  FilterOutlined,
  <PERSON><PERSON><PERSON><PERSON>utlined,
  <PERSON><PERSON><PERSON><PERSON>utlined,
  <PERSON>Outlined,
  CloseOutlined,
} from '@ant-design/icons-vue'
import { type IColumn, ColumnType } from '@/api/services/indicator/type'
// import { isTimeType } from '@/utils/tool'
import setField from './set-field.vue'
import { sleep } from './utile'
import { message } from 'ant-design-vue'
import type { FieldType } from '../indicator-list/details.vue'
import { dateFormats, numberStyleList, decimalStyleList } from './utile'
import DateFilter from './filters/date-filter.vue'
import MultiFilter from './filters/multi-filter.vue'
import SingleFilter from './filters/single-filter.vue'
import TextFilter from './filters/text-filter.vue'
import type { ConditionEnum } from '@/components/filter/constant'
import MergeColumn from './merge-column.vue'
import type { MergedColumnItem } from './type'

const props = defineProps<{
  data: IColumn
  columns: IColumn[]
  onMergeColumn: (columns: MergedColumnItem[], delimiter?: string[]) => void
}>()

const fieldSort: any = inject('fieldSort')
const hideFieldFn: any = inject('hideField')
// const setFieldTypeFn: any = inject('setFieldType')
const setFieldFn: any = inject('setField')
const showClickContent = ref(false)
const showHover = ref(false)
const showContentType = ref('default')
const extractList = ref([
  {
    label: '一天的时间',
    value: '0.1',
  },
  {
    label: '月份的日期',
    value: '1,2',
  },
  {
    label: '星期',
    value: 'Monday',
  },
  {
    label: '年月日',
    value: 'Jan, Feb',
  },
  {
    label: '年的季度',
    value: 'Q1, Q2',
  },
  {
    label: '年',
    value: '2023,2024',
  },
])
const defaultItem: any = ref()
const combinedList: any = ref([
  {
    split: '',
    field: 'ID',
  },
])
const example = ref('true 12345')
const filterComponentMap = {
  [ColumnType.DATE]: {
    comp: DateFilter,
    key: ColumnType.DATE,
    props: {
      onConfirm: (value: number | string | number[], condition?: ConditionEnum) => {
        console.log('🚀 ~ DateFilter onConfirm ~ value:', value)
        showClickContent.value = false
      },
    },
  },
  [ColumnType.NUMBER]: {
    comp: MultiFilter,
    key: ColumnType.NUMBER,
    props: {
      onConfirm: (condition: string, value: string[]) => {
        console.log('🚀 ~ DateFilter onConfirm ~ value:', value)
        showClickContent.value = false
      },
    },
  },

  [ColumnType.BOOLEAN]: {
    comp: SingleFilter,
    key: ColumnType.BOOLEAN,
    props: {
      onConfirm: (value: boolean) => {
        console.log('🚀 ~ SingleFilter onConfirm ~ value:', value)
        showClickContent.value = false
      },
    },
  },
  [ColumnType.EMAIL]: {
    comp: TextFilter,
    key: ColumnType.EMAIL,
    props: {
      onConfirm: (condition: ConditionEnum, value: string[]) =>
        textFilterOnConfirm(condition, value),
    },
  },
  [ColumnType.TEXT]: {
    comp: TextFilter,
    key: ColumnType.TEXT,
    props: {
      onConfirm: (condition: ConditionEnum, value: string[]) =>
        textFilterOnConfirm(condition, value),
    },
  },
}
const filterComp = computed(() => filterComponentMap[props.data.type])
const dateMoreList = ref([
  { label: '今天', value: 'today' },
  { label: '昨天', value: 'yesterday' },
  { label: '上周', value: 'yestweek' },
  { label: '最近7天', value: 'last7Day' },
  { label: '最近30天', value: 'last30Days' },
  { label: '最近3个月', value: 'last3Months' },
  { label: '过去12个月', value: 'last12Months' },
  { label: '具体日期', value: 'specificDate' },
  { label: '相对日期', value: 'relativeDate' },
  { label: '不包括', value: 'exclude' },
])
const showDatePick = ref(false)
const showSplit = ref(false)
const mergeColumnProps = reactive({
  curColumn: { field: props.data.field, kind: props.data.type },
  columns: props.columns.map((item) => ({ field: item.field, kind: item.type })),
  onConfirm: (columns: { field: string; kind: ColumnType }[], delimiter?: string[]) => {
    console.log('mergeColumnProps onConfirm ~ columns:', columns, delimiter)
    props.onMergeColumn(columns, delimiter)
    showClickContent.value = false
  },
})
const textFilterOnConfirm = (condition: ConditionEnum, value: string[]) => {
  console.log('🚀 ~ textFilterOnConfirm ~ condition:', condition, value)
  showClickContent.value = false
}
const hideField = () => {
  hideFieldFn(props.data.field)
}

const changeSort = (type: any) => {
  fieldSort(props.data.field, type)
}

const changeCT = (type: any) => {
  showContentType.value = type
}

const handleSplit = () => {
  showSplit.value = !showSplit.value
}

const handleClickField = () => {
  showHover.value = false
  showClickContent.value = true
  showContentType.value = 'default'
}

const handleTimeOk = () => {}

const handleClickAddCombined = () => {
  combinedList.value.push({
    split: '',
    field: '',
  })
}

// 用于input、非双向绑定
const handleField = (field: FieldType, event: Event) => {
  console.log('🚀 ~ handleField ~ field:', field, event)
  const target = event.target as HTMLInputElement
  defaultItem.value[field] = target.value
  setFieldFn(field, props.data.dataIndex, defaultItem.value[field])
}
// 用于双向绑定
const handleSetType = (field: FieldType) => {
  setFieldFn(field, props.data.dataIndex, defaultItem.value[field])
}

const selectVal = (item: any) => {
  console.log('🚀 ~ selectVal ~ item:', item)
}

// 判断setting功能弹框是哪种类型
const judgeSettingType = () => {
  if (props.data.type === ColumnType.DATE) {
    showContentType.value = 'setting-date'
  } else if (props.data.type === ColumnType.NUMBER) {
    showContentType.value = 'setting-number'
  } else {
    showContentType.value = 'setting-text'
  }
}

const changeItemData = (item: any) => {
  // console.log('🚀 ~ changeItemData ~ item:', item)
  showClickContent.value = false
}

onMounted(() => {
  defaultItem.value = props.data
  // console.log('🚀 ~ onMounted ~ defaultItem.value:', defaultItem.value)
  // 设置默认值
  // if (!defaultItem.value.selectedDateFormate) {
  //   // 日期格式
  //   defaultItem.value.selectedDateFormate = 'MMMM D, YYYY'
  //   setFieldFn('selectedDateFormate', props.data.dataIndex, 'MMMM D, YYYY')
  // }
  // if (!defaultItem.value.displayTime) {
  //   // 显示时间
  //   defaultItem.value.displayTime = 'HH:mm'
  //   setFieldFn('displayTime', props.data.dataIndex, 'HH:mm')
  // }
  // if (!defaultItem.value.timeFormate) {
  //   // 时间格式
  //   defaultItem.value.timeFormate = 'A'
  //   setFieldFn('timeFormate', props.data.dataIndex, 'A')
  // }
  // if (!defaultItem.value.numberStyle) {
  //   // 样式
  //   defaultItem.value.numberStyle = 0
  //   setFieldFn('numberStyle', props.data.dataIndex, 0)
  // }
  // if (!defaultItem.value.decimalStyle) {
  //   // 分隔样式
  //   defaultItem.value.decimalStyle = '0,0'
  //   setFieldFn('decimalStyle', props.data.dataIndex, decimalStyleList[0]?.value)
  // }
})
</script>

<template>
  <div class="">
    <a-popover
      trigger="click"
      v-model:open="showClickContent"
      :content="data.description"
      placement="bottom"
    >
      <!-- <div @click="handleClickField" @focus="handleFocus">{{ propsItem.field }}</div> -->
      <span @click="handleClickField" @mouseleave="showHover = true" class="field-btn ellipsis">
        {{ data.title }}
        <DownOutlined />
      </span>

      <!-- 鼠标点击弹窗 -->
      <template #content>
        <div>
          <div v-if="showContentType === 'default'">
            <div class="menu-header">
              <div class="header-ul">
                <!-- <div class="header-li" @click="changeSort('asc')">
                  <ArrowUpOutlined />
                </div>
                <div class="header-li" @click="changeSort('desc')">
                  <ArrowDownOutlined />
                </div> -->
                <div class="header-li" @click="hideField()">
                  <EyeInvisibleOutlined />
                </div>
                <div class="header-li" @click="judgeSettingType()">
                  <SettingOutlined />
                </div>
              </div>
            </div>
            <div class="menu-list">
              <div class="menu-item" @click="changeSort('asc')">
                <div class="menu-item-icon"><ArrowUpOutlined /></div>
                <div class="menu-item-text">升序</div>
              </div>
              <div class="menu-item" @click="changeSort('desc')">
                <div class="menu-item-icon"><ArrowUpOutlined /></div>
                <div class="menu-item-text">降序</div>
              </div>
              <!-- <div class="menu-item" @click="changeCT('filter')">
                <div class="menu-item-icon"><FilterOutlined /></div>
                <div class="menu-item-text">过滤该列</div>
              </div> -->
              <!-- <div class="menu-item">
                <div class="menu-item-icon"><BarChartOutlined /></div>
                <div class="menu-item-text">分布情况</div>
              </div>
              <div class="menu-item">
                <div class="menu-item-icon"><DotChartOutlined /></div>
                <div class="menu-item-text">唯一值</div>
              </div>
              <div class="menu-item" @click="changeCT('extract')">
                <div class="menu-item-icon"><ForkOutlined /></div>
                <div class="menu-item-text">Extract day,month,year</div>
              </div> -->
              <div class="menu-item" @click="changeCT('combined')">
                <div class="menu-item-icon"><ForkOutlined /></div>
                <div class="menu-item-text">合并栏目</div>
              </div>
            </div>
            <!-- <div class="summary-list">
              <p class="menu-item-title">汇总</p>
              <div class="btn-list mt-10">
                <div class="btn-item" style="padding: 2px 16px">
                  <span style="font-size: 12px; font-weight: 600">唯一值</span>
                </div>
                <div class="btn-item" style="padding: 2px 16px">
                  <span style="font-size: 12px; font-weight: 600">总和</span>
                </div>
                <div class="btn-item" style="padding: 2px 16px">
                  <span style="font-size: 12px; font-weight: 600">平均</span>
                </div>
              </div>
            </div> -->
          </div>
          <div style="width: 220px" v-else-if="showContentType === 'setting'">
            <setField :propsItem="data" />
          </div>
          <div style="width: 220px" v-else-if="showContentType === 'filter'">
            <component :is="filterComp.comp" v-bind="filterComp.props" :key="filterComp.key" />
          </div>
          <div style="width: 420px" v-else-if="showContentType === 'combined'">
            <!-- <p class="menu-item-title">将"{{ defaultItem.field }}"与其他列合并</p>
            <div v-if="combinedList.length">
              <a-row
                :gutter="10"
                justify="start"
                type="flex"
                :wrap="true"
                v-for="(it, key) in combinedList"
                :key="key"
              >
                <a-col :span="4" v-if="showSplit">
                  <div class="menu-item-title">分离器</div>
                  <a-input placeholder="空间" v-model:value="it.split"></a-input>
                </a-col>
                <a-col :span="16">
                  <div class="menu-item-title">第二栏</div>
                  <a-select style="width: 100%" v-model:value="it.field">
                    <a-select-opt-group label="Accounts+Order">
                      <a-select-option value="id"> ID </a-select-option>
                      <a-select-option value="Email"> Email </a-select-option>
                    </a-select-opt-group>
                    <a-select-opt-group label="Order->Product">
                      <a-select-option value="id"> ID </a-select-option>
                      <a-select-option value="Title"> Title </a-select-option>
                    </a-select-opt-group>
                  </a-select>
                </a-col>
                <a-col :span="4" v-if="showSplit">
                  <CloseOutlined style="margin-top: 35px; margin-left: 20px" />
                </a-col>
              </a-row>
            </div>
            <a-flex justify="space-between" style="margin-top: 10px">
              <a-button
                type="link"
                shape="default"
                :loading="false"
                :disabled="false"
                style="margin-left: -18px"
                @click="handleSplit"
              >
                用（空间）分割
              </a-button>
              <a-button
                type="link"
                shape="default"
                :loading="false"
                :disabled="false"
                @click="handleClickAddCombined"
              >
                + 添加栏目
              </a-button>
            </a-flex>
            <p class="menu-item-title">示例</p>
            <a-input disabled v-model:value="example"></a-input> -->
            <MergeColumn v-bind="mergeColumnProps" />
          </div>
          <div style="width: 220px" v-else-if="showContentType === 'extract'">
            <p class="menu-item-title">选择要提取的部分</p>
            <div class="menu-list">
              <div
                class="menu-item"
                style="justify-content: space-between"
                v-for="(item, key) in extractList"
                :key="key"
                @click="selectVal(item)"
              >
                <div class="menu-item-label">{{ item.label }}</div>
                <div class="menu-item-value">{{ item.value }}</div>
              </div>
            </div>
          </div>
          <div style="width: 220px" v-else-if="showContentType.includes('setting-')">
            <!-- <div style="width: 220px" v-else-if="showContentType === 'setting-text'"> -->
            <a-form layout="vertical">
              <a-form-item label="字段标题">
                <a-input :defaultValue="defaultItem.field" @blur="handleField('field', $event)" />
              </a-form-item>
              <a-form-item label="显示为" v-if="showContentType !== 'setting-number'">
                <!-- <a-radio-group v-model:value="propsItem.displayType"> -->
                <a-radio-group v-model:value="defaultItem.type" @change="handleSetType('type')">
                  <a-radio style="display: flex" value="TEXT">文本</a-radio>
                  <a-radio style="display: flex" value="LINK">链接</a-radio>
                </a-radio-group>
              </a-form-item>
              <!-- <template v-if="defaultItem.type === 'LINK'"> -->
              <a-form-item
                v-if="showContentType !== 'setting-number' && defaultItem.type === 'LINK'"
                label="链接文本"
              >
                <a-input
                  :defaultValue="defaultItem.linkText"
                  @blur="handleField('linkText', $event)"
                />
              </a-form-item>
              <a-form-item
                v-if="showContentType !== 'setting-number' && defaultItem.type === 'LINK'"
                label="链接网址"
              >
                <a-input
                  :defaultValue="defaultItem.linkUrl"
                  @blur="handleField('linkUrl', $event)"
                />
              </a-form-item>
              <a-form-item label="日期样式" v-if="showContentType === 'setting-date'">
                <!-- <a-input v-model:value="propsItem.linkUrl" /> -->
                <a-select
                  :defaultValue="dateFormats[0]?.value"
                  v-model:value="defaultItem.selectedDateFormate"
                  @change="handleSetType('selectedDateFormate')"
                  placeholder="请选择日期格式"
                >
                  <a-select-option
                    v-for="format in dateFormats"
                    :key="format.value"
                    :value="format.value"
                  >
                    {{ format.label }}
                  </a-select-option>
                </a-select>
              </a-form-item>
              <!-- <a-form-item label="日期和月份的缩略语" v-if="showContentType === 'setting-date'">
                <a-switch />
              </a-form-item> -->
              <a-form-item label="显示时间" v-if="showContentType === 'setting-date'">
                <!-- <a-radio-group v-model:value="propsItem.displayType"> -->
                <a-radio-group
                  v-model:value="defaultItem.displayTime"
                  @change="handleSetType('displayTime')"
                >
                  <a-radio style="display: flex" :value="undefined">关闭</a-radio>
                  <a-radio style="display: flex" value="HH:mm">HH:MM</a-radio>
                  <a-radio style="display: flex" value="HH:mm:ss">HH:MM:SS</a-radio>
                  <a-radio style="display: flex" value="HH:mm:ss.SSS">HH:MM:SS:MS</a-radio>
                </a-radio-group>
              </a-form-item>
              <a-form-item
                label="时间格式"
                v-if="showContentType === 'setting-date' && defaultItem.displayTime"
                @change="handleSetType('timeFormate')"
              >
                <a-radio-group defaultValue="h:mm A" v-model:value="defaultItem.timeFormate">
                  <a-radio style="display: flex" value="A">5:24 下午 (12小时制)</a-radio>
                  <a-radio style="display: flex" value="">17:24 (24小时制)</a-radio>
                </a-radio-group>
              </a-form-item>
              <a-form-item label="样式" v-if="showContentType === 'setting-number'">
                <a-select
                  :defaultValue="numberStyleList[0]?.value"
                  v-model:value="defaultItem.numberStyle"
                  @change="handleSetType('numberStyle')"
                >
                  <a-select-option
                    v-for="format in numberStyleList"
                    :key="format.value"
                    :value="format.value"
                  >
                    {{ format.label }}
                  </a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="分隔样式" v-if="showContentType === 'setting-number'">
                <a-select
                  v-model:value="defaultItem.decimalStyle"
                  @change="handleSetType('decimalStyle')"
                >
                  <a-select-option
                    v-for="format in decimalStyleList"
                    :key="format.value"
                    :value="format.value"
                  >
                    {{ format.label }}
                  </a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="小数位数" v-if="showContentType === 'setting-number'">
                <a-input
                  type="number"
                  :min="0"
                  v-model:value="defaultItem.decimalNum"
                  @blur="handleField('decimalNum', $event)"
                />
              </a-form-item>
              <a-form-item label="乘以一个数字" v-if="showContentType === 'setting-number'">
                <a-input
                  type="number"
                  :min="0"
                  v-model:value="defaultItem.multiple"
                  @blur="handleField('multiple', $event)"
                />
              </a-form-item>
              <a-form-item label="添加一个前缀" v-if="showContentType === 'setting-number'">
                <a-input
                  v-model:value="defaultItem.startStr"
                  @blur="handleField('startStr', $event)"
                />
              </a-form-item>
              <a-form-item label="添加一个后缀" v-if="showContentType === 'setting-number'">
                <a-input v-model:value="defaultItem.endStr" @blur="handleField('endStr', $event)" />
              </a-form-item>
            </a-form>
          </div>
          <!--  v-if="showContentType === 'setting-number'" -->
        </div>
      </template>
    </a-popover>
  </div>
</template>
<style lang="less" scoped>
.field-btn {
  border: 1px solid rgba(80, 158, 227, 0.2);
  padding: 0.25em 0.65em;
  border-radius: 6px;
  min-width: 35px;
  text-align: center;
  color: rgb(80, 158, 227);
  font-size: 12.5px;
  line-height: 24px;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
}

.field-btn:hover {
  border: 1px solid rgba(80, 158, 227, 0.5);
  color: rgb(80, 158, 227);
}

.btn-list {
  display: flex;
  justify-content: space-between;
  width: 240px;
  padding: 0 5px;
  .btn-item {
    border: 1px solid rgba(80, 158, 227, 0.5);
    color: rgb(80, 158, 227);
    padding: 0px 16px;
    border-radius: 12px;
    cursor: pointer;
    transition: 0.3s all;
  }
  .btn-item:hover {
    background-color: #509ee3;
    color: #fff;
  }
}

.menu-header {
  display: flex;
  justify-content: space-between;
  .header-ul {
    display: flex;
    justify-content: space-between;
    gap: 8px;
    .header-li {
      cursor: pointer;
      color: #509ee3;
      font-weight: 600;
      padding: 0 14px;
      border-radius: 16px;
      border: 1px solid #509ee3;
      &:hover {
        color: #fff;
        background-color: #509ee3;
      }
    }
  }
}

.menu-list {
  // padding: 10px 5px;
  margin-top: 10px;
  .menu-item {
    display: flex;
    line-height: 32px;
    cursor: pointer;
    border-radius: 8px;
    color: #4c5773;
    font-weight: 600;
    padding: 0 10px;
    .menu-item-icon {
      color: #509ee3;
      margin-right: 15px;
    }
    .menu-item-label {
      color: #4c5773;
      font-weight: 600;
    }
    .menu-item-value {
      color: #b0b5c1;
    }
  }
  .menu-item:hover {
    background-color: #509ee3;
    color: #fff;
    .menu-item-icon,
    .menu-item-label,
    .menu-item-value {
      color: #fff;
    }
  }
}
</style>
