import service from '@/api'
import type { Parameter } from '../data-asset/hbase-management'
import { SODATAFLINK } from '@/api/base'

/**
 * 页面内容加载
 * @param params
 */
export function getFlinkConfiList(params: Parameter) {
  return service({
    method: 'post',
    url: `${SODATAFLINK}/flink/FlinkTable/trunFlinkUrl`,
    data: params,
  })
}

/**
 * 获取Job详情
 * @param params
 */
export function getJobDetail(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/flink/flinkTaskSubmit/getJobIDListMapFun`,
    method: 'post',
    data: params,
  })
}

/**
 * 获取集群详情
 * @param params
 */
export function getClusterDetail(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/flink/flinkTaskSubmit/overviewFun`,
    method: 'post',
    data: params,
  })
}

/**
 * 获取JobManager监控指标
 * @param params
 */
export function getJobManagerMetrics(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/flink/flinkTaskSubmit/getJobMangerMetricsFun`,
    method: 'post',
    data: params,
  })
}

/**
 * 获取JobManager配置
 * @param params
 */
export function getJobManagerConfig(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/flink/flinkTaskSubmit/getJobMangerConfigFun`,
    method: 'post',
    data: params,
  })
}

/**
 * 获取taskManager详情
 * @param params
 */
export function getTaskManagerDetail(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/flink/flinkTaskSubmit/taskmanagersFun`,
    method: 'post',
    data: params,
  })
}

/**
 * 停止job
 * @param params
 */
export function stopJobById(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/flink/flinkTaskSubmit/stopWithSavepointFun`,
    method: 'post',
    data: params,
  })
}

/**
 * 获取job异常信息
 * @param params
 */
export function getJobErrorInfoById(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/flink/flinkTaskSubmit/getExceptionsFun`,
    method: 'post',
    data: params,
  })
}

/**
 * 获取job执行计划
 * @param params
 */
export function getExecutionPlanById(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/flink/flinkTaskSubmit/getPlanFun`,
    method: 'post',
    data: params,
  })
}

/**
 * 获取job执行计划
 * @param params
 */
export function getCheckPointHistoryById(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/flink/flinkTaskSubmit/getCheckpointsFun`,
    method: 'post',
    data: params,
  })
}

/**
 * 检查checkpoint取消实时任务
 * @param params
 */
export function cancelrealTimeTask(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/SyncCompensstion/checkCheckPointPathFun`,
    method: 'post',
    data: params,
  })
}

/**
 * 服务启停
 * @param params
 */
export function setProcessMonitor(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/new2dataplatform/serviceMonitor/health`,
    method: 'post',
    data: params,
  })
}
