import type { ColumnProps } from 'ant-design-vue/es/table'
import type { RuleItem } from '../data'
import { Tag } from 'ant-design-vue'
import { h } from 'vue'

export const columns: ColumnProps<RuleItem>[] = [
  {
    title: '名称',
    dataIndex: 'name',
    key: 'name',
    ellipsis: true,
  },
  {
    title: '版本',
    dataIndex: 'version',
    key: 'version',
    width: 120,
  },
  {
    title: '规则表达式',
    dataIndex: 'expression',
    key: 'expression',
    ellipsis: true,
  },
  {
    title: '标签',
    dataIndex: 'tag',
    key: 'tag',
    width: 150,
    ellipsis: true,
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 100,
    customRender: ({ text }) => {
      const statusMap = {
        active: { text: '启用', color: 'success' },
        inactive: { text: '禁用', color: 'error' },
      }
      const status = statusMap[text as keyof typeof statusMap]
      return h(Tag, { color: status?.color }, () => status?.text || text)
    },
  },
  {
    title: '操作',
    key: 'action',
    width: 220,
    fixed: 'right',
  },
]
