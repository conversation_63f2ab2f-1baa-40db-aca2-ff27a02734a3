<template>
  <div class="add-box">
    <a-form
      layout="inline"
      :model="formValue"
      name="basic"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 14 }"
      autocomplete="off"
      ref="formRef"
    >
      <a-form-item
        label="项目名称"
        name="project"
        :rules="[{ required: true, message: '请选择项目' }]"
      >
        <a-select
          v-model:value="formValue.project"
          placeholder="请选择对应文件"
          style="width: 300px"
          @change="handleChange"
        >
          <a-select-option v-for="(item, index) in options" :key="index" :value="item.project">{{
            item.project
          }}</a-select-option>
        </a-select></a-form-item
      >

      <a-form-item
        label="模块"
        name="module"
        v-if="formValue.project"
        :rules="[{ required: true, message: '请选择模块' }]"
      >
        <a-select
          v-model:value="formValue.module"
          placeholder="请选择模块"
          style="width: 300px"
          @change="modulesChange"
        >
          <a-select-option
            v-for="(item, index) in modulesList"
            :key="index"
            :value="item.moduleName"
            >{{ item.moduleNameCn }} {{ item.moduleName }}</a-select-option
          >
        </a-select>
      </a-form-item>
    </a-form>
    <div>
      <span class="mg-right20"
        >入参 <a-switch v-model:checked="paramsChecked" @change="dataChange($event, 'params')"
      /></span>
      <span class="mg-right20"
        >出参 <a-switch v-model:checked="responseChecked" @change="dataChange($event, 'response')"
      /></span>
      <a-button type="primary" style="margin-right: 10px" @click="openHeader = true"
        >公共参数</a-button
      >
      <a-button type="primary" @click="generateMadol">生成代码</a-button>
    </div>
  </div>
  <a-table :dataSource="dataSource" :columns="columns" :pagination="{ pageSize: 100 }">
    <template #bodyCell="{ column, record }">
      <template v-if="column.key === 'action'">
        <a-button type="link" @click="detail(record)">详情</a-button>
        <a-button type="link" @click="mockHandle(record)">mock调用</a-button>
      </template>
      <template v-else-if="column.key === 'params'">
        <div style="width: 500px">
          <a-tag v-if="!record?.params" color="red">any</a-tag>
          <template v-else>
            <DownCircleOutlined
              v-if="!record?.paramsShow"
              @click="record.paramsShow = true"
            ></DownCircleOutlined>
            <UpCircleOutlined v-if="record.paramsShow" @click="record.paramsShow = false" />
            <paramsTable :dataSource="record.params" v-if="record.paramsShow"></paramsTable>
          </template>
        </div>
      </template>
      <template v-else-if="column.key === 'response'">
        <a-tag v-if="!record?.response" color="red">any</a-tag>
        <template v-else>
          <DownCircleOutlined
            v-if="!record?.responseShow"
            @click="record.responseShow = true"
          ></DownCircleOutlined>
          <UpCircleOutlined v-if="record?.responseShow" @click="record.responseShow = false" />
          <responseTable :dataSource="record.response" v-if="record.responseShow"></responseTable>
        </template>
      </template>
      <template v-else>
        {{ record && record[column.key] }}
      </template>
    </template>
  </a-table>

  <a-modal v-model:open="open2" title="生成代码" width="900px" okText="部署" @ok="generateHandle">
    <div style="margin-bottom: 10px">接口列表</div>
    <div class="box">
      <div class="left-box">
        <a-tree
          class="draggable-tree"
          block-node
          :tree-data="leftTree"
          defaultExpandAll
          @select="handleClick"
        />
      </div>
      <div class="right-box">
        <v-ace-editor
          v-model:value="jsonInput"
          lang="javascript"
          theme="chrome"
          style="width: 100%; height: 100%; min-height: 300px"
          :options="{ useWorker: true }"
          :readonly="true"
        />
      </div>
    </div>
  </a-modal>

  <a-modal
    v-model:open="open3"
    title="接口详情"
    width="800px"
    :forceRender="true"
    :destroyOnClose="true"
  >
    <div class="detail-box">
      <div>接口名称：{{ selectData.interfaceName }}</div>
      <div>接口路径：{{ selectData.fullPath }}</div>
      <div>请求方法：{{ selectData.httpMethodName }}</div>
      <div>
        传入参数：
        <paramsTable :dataSource="selectData['params']"></paramsTable>
      </div>
      <div>
        返回参数：
        <responseTable :dataSource="selectData['response']"></responseTable>
      </div>
    </div>
    <template #footer>
      <a-button key="submit" type="primary" @click="open3 = false">确定</a-button>
    </template>
  </a-modal>

  <mockModal v-model="mockOpen" :data="requestData" :commonHead="commonHead"></mockModal>

  <a-modal v-model:open="openHeader" title="公共头部" width="900px" @ok="savecommonHead">
    <a-tabs v-model:activeKey="activeKey">
      <a-tab-pane key="Params" :tab="`Params （${commonHead.Params.length}）`">
        <paramsList
          :options="options"
          :list="commonHead.Params"
          type="Params"
          :isCommonHead="true"
        ></paramsList>
      </a-tab-pane>
      <a-tab-pane key="Cookie" :tab="`Cookie (${commonHead.Cookie.length})`">
        <paramsList
          :options="options"
          :list="commonHead.Cookie"
          type="Cookie"
          :isCommonHead="true"
        ></paramsList>
      </a-tab-pane>
      <a-tab-pane key="Headers" :tab="`Headers (${commonHead.Headers.length})`">
        <paramsList
          :options="options"
          :list="commonHead.Headers"
          type="Headers"
          :isCommonHead="true"
        ></paramsList>
      </a-tab-pane>
    </a-tabs>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { getApiData, generateDeploy } from '@/api/services/manage'
import { Cascader } from 'ant-design-vue'
import { message } from 'ant-design-vue/es/components'
import { VAceEditor } from 'vue3-ace-editor'
import 'ace-builds/src-noconflict/mode-javascript' // Load the language definition file used below
import 'ace-builds/src-noconflict/theme-chrome' // Load the theme definition file used below
import { getParam, getRetrun } from '@/utils/tool'
import paramsTable from './component/params-table.vue'
import responseTable from './component/response-table.vue'
import mockModal from './component/mock-modal.vue'
import { DownCircleOutlined, UpCircleOutlined } from '@ant-design/icons-vue'
import paramsList from './component/params-list.vue'
import { getdraft, savedraft } from '@/components/code-generation/code-generation'

const dataSource = ref<any[]>([])
const value = ref([])
const options = ref<any[]>([])
const open2 = ref(false)
const open3 = ref(false)
const openHeader = ref(false)
const obj = ref<any>({})
const treeArray = ref<any[]>([])
const jsonInput = ref('')
const leftTree = ref<any[]>([])
const selectData = ref<any>({})
const requestData = ref<any>({})
const commonHead = ref<any>({
  Params: [],
  Cookie: [],
  Headers: [],
})
const projectData = ref<any>({})
const formValue = ref<any>({})
const modulesList = ref<any>([])
const interfaceInfos = ref<any>([])
const paramsChecked = ref(false)
const responseChecked = ref(false)
const mockOpen = ref(false)
const activeKey = ref('Params')

const columns = [
  {
    title: '项目/模块/接口名',
    dataIndex: 'title',
    key: 'title',
    with: '100px',
  },
  {
    title: '入参',
    dataIndex: 'params',
    key: 'params',
    with: '500px',
  },
  {
    title: '返回值',
    dataIndex: 'response',
    key: 'response',
    width: 500,
  },
  {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
    width: 200,
  },
]

function handleChange(params: string) {
  const item = options.value.find((item) => item.project === params)
  formValue.value.module = undefined
  projectData.value = item
  if (item) {
    modulesList.value = item.modules
    const index = modulesList.value.findIndex((item: any) => item.moduleName === '全部模块')
    if (index === -1) modulesList.value.unshift({ moduleName: '全部模块' })
    formValue.value.module = '全部模块'
    modulesChange('全部模块')
  }
}

async function savecommonHead() {
  await savedraft({
    name: formValue.value.project + '-commonHead',
    content: { ...commonHead.value },
  })
  openHeader.value = false
}

function modulesChange(params: string) {
  if (params === '全部模块') {
    dataSource.value = []
    modulesList.value.map((item: any) => {
      if (item.moduleName !== '全部模块') {
        item.interfaceInfos.map((item2: any) => {
          dataSource.value.push(item2)
        })
      }
    })

    interfaceInfos.value = dataSource.value
    handle()
    return
  }
  const item = modulesList.value.find((item: any) => item.moduleName === params)
  if (item) {
    interfaceInfos.value = item.interfaceInfos
    handle()
  }
}

async function getCommonHead() {
  const { data } = await getdraft({
    name: formValue.value.project + '-commonHead',
  })
  if (data?.data) {
    commonHead.value = data.data
  }
}

async function getApiHandle() {
  const { data } = await getApiData()
  data.map((item: any, index: number) => {
    item.label = item.project
    item.value = item.project
    item.children = item.modules
    item.children.map((item2: any) => {
      item2.interfaceInfos.map((item3: any) => {
        item3.title =
          item3.fullPath + `（${item.project}\\${item2.moduleNameCn}\\${item3.interfaceNameCn}）`
        obj.value[item3.fullPath] = item3
        if (index === 0) {
          // 初始化默认展示第一个项目
          dataSource.value.push(item3)
        }
      })
    })
    if (index === 0) {
      treeArray.value = convertToTreeArray(dataSource.value)
      formValue.value.project = item.project
    }
  })
  options.value = data

  handleChange(formValue.value.project)
  formValue.value.module = '全部模块'
}

async function generateHandle() {
  await generateDeploy({ project: treeArray.value })
  message.success('部署成功')
  open2.value = false
}

function handle() {
  const arr: any[] = []
  interfaceInfos.value.map((item: any) => {
    arr.push(obj.value[item.fullPath])
  })
  dataSource.value = arr
  treeArray.value = convertToTreeArray(interfaceInfos.value)
  value.value = []
}

function convertToTreeArray(input: any) {
  const tree: any = []
  const projectSet = new Set()
  input.forEach((item: any) => {
    const result = item.fullPath.substring(1).split('/')
    const projectName = result[0]
    if (!projectSet.has(projectName)) {
      projectSet.add(projectName)
      const newProject = {
        project: projectName,
        modules: [],
      }
      tree.push(newProject)
    }
  })
  input.forEach((item: any) => {
    const result = item.fullPath.substring(1).split('/')
    const projectName = result[0]
    const moduleName = result[1]
    const interfaceName = result[result.length - 1]
    const projectObj = tree.find((obj: { project: any }) => obj.project === projectName)
    const moduleObj = projectObj.modules.find(
      (obj: { moduleName: any }) => obj.moduleName === moduleName,
    ) || {
      moduleName: moduleName,
      interfaceInfos: [],
    }
    if (!projectObj.modules.find((obj: { moduleName: any }) => obj.moduleName === moduleName)) {
      projectObj.modules.push(moduleObj)
    }
    moduleObj.interfaceInfos.push({
      interfaceName,
    })
  })
  return tree
}

function generateMadol() {
  open2.value = true
  const data = filterData(options.value, treeArray.value)
  data.map((item: any) => {
    item.title = item.project
    item.value = item.project
    item.children = item.modules
    delete item.modules
    item.children.map((item2: any) => {
      item2.title = `${item2?.moduleNameCn} ${item2.moduleName}`
      item2.value = item2.moduleName
      item2.children = item2.interfaceInfos
      delete item2.interfaceInfos
      item2.children.map((item3: any) => {
        item3.title = `${item3?.interfaceNameCn} ${item3.interfaceName}`
        item3.value = item3.interfaceName
      })
    })
  })
  leftTree.value = data
}

// 过滤出包含在 treeData 中的数据的项目、模块和接口信息
function filterData(data: any, treeData: any) {
  const filteredData: any[] = []
  data.forEach((item: any) => {
    const matchingTreeItem = treeData.find(
      (treeItem: { project: any }) => treeItem.project === item.project,
    )
    if (matchingTreeItem) {
      const filteredProject: any = {
        project: item.project,
        modules: [],
      }
      item.modules.forEach((moduleItem: any) => {
        const matchingModule = matchingTreeItem.modules.find(
          (treeModule: { moduleName: any }) => treeModule.moduleName === moduleItem.moduleName,
        )
        if (matchingModule) {
          const filteredModule = {
            moduleName: moduleItem.moduleName,
            moduleNameCn: moduleItem.moduleNameCn,
            interfaceInfos: moduleItem.interfaceInfos.filter(
              (interfaceItem: { interfaceName: any }) =>
                matchingModule.interfaceInfos.some(
                  (treeInterface: { interfaceName: any }) =>
                    treeInterface.interfaceName === interfaceItem.interfaceName,
                ),
            ),
          }
          filteredProject.modules.push(filteredModule)
        }
      })
      filteredData.push(filteredProject)
    }
  })
  return filteredData
}

function handleClick(i: any, event: any) {
  const item = event.node
  if (!item?.fullPath) return
  const content = `export async function ${item.interfaceName}(${getParam(item.params)}): Promise<${getRetrun(item.response)}>{
    return service({
      method: '${item.httpMethodName}',
      url: '${item.fullPath}',
      ${getBodyParmas(item.httpMethodName)}
    }, MOCK_DATA['${item.interfaceName}']);
  }
`
  jsonInput.value = content
}

/**
 * 根据请求方法获取请求体参数的描述
 * @param {string} methods - 请求方法
 * @returns {string} - 请求体参数的描述
 */
function getBodyParmas(methods: string) {
  if (methods === 'get') {
    return 'params: data'
  }
  return 'data'
}

function detail(record: any) {
  record.responseShow = false
  open3.value = true
  selectData.value = JSON.parse(JSON.stringify(record))
}

function dataChange(event: any, type: string) {
  dataSource.value.map((item: any) => {
    if (type === 'params') {
      item.paramsShow = event
    } else if (type === 'response') {
      item.responseShow = event
    }
  })
}

function mockHandle(record: any) {
  mockOpen.value = true
  let obj = JSON.parse(JSON.stringify(record))
  obj.url = projectData.value.url
  requestData.value = obj
}

onMounted(async () => {
  await getApiHandle()
  await getCommonHead()
})
</script>
<style scoped lang="less">
.add-box {
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
}
.box {
  display: flex;
  .left-box {
    width: 40%;
    border: 1px solid #ccc;
    padding: 12px;
    max-height: 600px;
    min-width: 300px;
    overflow: auto;
    .interfaceName {
      cursor: pointer;
    }
  }
  .right-box {
    width: 60%;
  }
}
.detail-box {
  max-height: 600px;
  overflow: auto;
}
.mg-right20 {
  margin-right: 20px;
}
:deep(.ant-table-cell) {
  vertical-align: top !important;
}
</style>
