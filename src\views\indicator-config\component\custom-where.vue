<template>
  <div class="where-comp">
    <div class="where-title">筛选器</div>
    <div class="where-container">
      <div class="where-comp-item" v-for="(item, index) in modelValue" :key="index">
        <where
          v-model:model-value="modelValue[index]"
          :tableName="tableName"
          :columns="columns"
          @submit="(e, tableName) => handleSubmit(e, 'update', tableName)"
        >
          <template #default>
            <div v-if="item" class="where-comp-tag">
              <span style="margin-right: 10px">{{ formatValue(item) }}</span>
              <CloseCircleOutlined @click.stop="handleRemove(index)" class="remove-btn" />
            </div>
          </template>
        </where>
      </div>
      <div class="where-comp-item">
        <where
          v-model="newOperateValue"
          :tableName="tableName"
          :columns="columns"
          @submit="(e, tableName) => handleSubmit(e, 'add', tableName)"
        >
          <template #default v-if="modelValue?.length">
            <div class="where-comp-tag">
              <PlusOutlined style="font-size: 22px" />
            </div>
          </template>
        </where>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { PlusOutlined, CloseCircleOutlined } from '@ant-design/icons-vue'
import where from '@/components/where/index.vue'
import type { dataColumn, operateValue } from '@/components/data-columns/type'

defineProps<{
  tableName: string
  columns: dataColumn[]
}>()

const newOperateValue = ref<operateValue>({
  column: null,
  operateValue: {
    operator: '',
    value: '',
  },
})
const modelValue = defineModel<operateValue[]>({
  default: () => [],
})

function formatValue(item?: operateValue) {
  if (!item) return ''
  const { column, operateValue } = item
  const name = column?.title
  const op = operateValue.operator
  let value = operateValue.value
  const type = operateValue.value?.type
  if (type) {
    const v = operateValue.value.value.filter((v: any) => v)
    value = type + ' ' + v.join(' 一 ')
  }
  if (['IS_NULL', 'NOT_NULL'].includes(op)) {
    return `${name} ${op}`
  }
  return `${name} ${op} ${value}`
}

function handleRemove(index: number) {
  modelValue.value = modelValue.value.filter((_, i) => i !== index)
}

function handleSubmit(e: operateValue, type: 'add' | 'update', tableName: string) {
  if (type === 'add') {
    modelValue.value = [...(modelValue.value || []), { ...e, tableName }]
  } else {
    modelValue.value[modelValue.value.indexOf(e)] = { ...e, tableName }
  }
  newOperateValue.value = {
    column: null,
    operateValue: {
      operator: '',
      value: '',
    },
  }
}
</script>

<style scoped lang="less">
.remove-btn:hover {
  background-color: #fff;
  color: #7172ad;
  border-radius: 50%;
}
.where-title {
  margin-bottom: 0.5rem;
  color: #7172ad;
  font-weight: bold;
  display: flex;
}
.where-container {
  display: flex;
  -webkit-box-align: center;
  align-items: center;
  flex-wrap: wrap;
  border-radius: 8px;
  background-color: rgba(113, 114, 173, 0.1);
  padding: 14px;
  color: rgb(113, 114, 173);
}
.where-comp {
  :deep(&-tag) {
    color: #fff;
    display: flex;
    -webkit-box-align: center;
    align-items: center;
    padding: 10px;
    background-color: rgb(113, 114, 173);
    border-radius: 6px;
    transition: background 300ms linear 0s;
    margin-right: 10px;
    cursor: pointer;
  }
  &-tag:hover {
    background-color: rgb(135, 136, 197);
  }
}
</style>
