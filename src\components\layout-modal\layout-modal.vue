<template>
  <a-drawer
    :width="500"
    height="420"
    title="生成布局"
    placement="bottom"
    :open="newModelValue"
    @close="newModelValue = false"
  >
    <a-tabs v-model:activeKey="activeKey">
      <a-tab-pane key="layout" tab="布局">
        <div class="layout-list">
          <div
            class="layout-list-item"
            :class="{ active: item.key === activeLayout }"
            v-for="(item, index) in components"
            :key="index"
          >
            <div class="style1" @mouseenter="showMask(item)">
              <component :is="item.name" v-bind="bindData"> </component>
            </div>
            <div
              class="mask-layer"
              v-if="item.isMaskVisible"
              @mouseleave="hideMask(item)"
              @click="activeLayout = item.key"
            >
              选择
            </div>
            <a-button class="preview-btn" @click="openModal(item)">预览</a-button>
          </div>
        </div>
      </a-tab-pane>
      <a-tab-pane key="login" tab="登录" force-render>
        <div class="layout-list">
          <div
            class="layout-list-item"
            :class="{ active: item.key === activeLogin }"
            v-for="(item, index) in componentsList"
            :key="index"
          >
            <div class="style2" @mouseenter="showMask(item)">
              <component :is="item.name" v-bind="bindData"> </component>
            </div>
            <div
              class="mask-layer"
              v-if="item.isMaskVisible"
              @mouseleave="hideMask(item)"
              @click="activeLogin = item.key"
            >
              选择
            </div>
            <a-button class="preview-btn" @click="openModal(item)">预览</a-button>
          </div>
        </div>
      </a-tab-pane>
    </a-tabs>
    <div class="footer drawer-footer">
      <a-button @click="newModelValue = false">取消</a-button>
      <a-button class="submit-btn" type="primary" @click="submit" :loading="loading">确定</a-button>
    </div>
  </a-drawer>

  <a-modal
    v-model:open="open"
    title="预览"
    width="1500px"
    :wrapClassName="{ 'modal-fs-layout': open }"
  >
    <div class="modal-content">
      <component :is="selectComp.name" v-bind="bindData" :preview="true" class="fs-layout-preview">
      </component>
    </div>
    <template #footer>
      <div class="footer">
        <a-button @click="open = false" style="margin-right: 7px">取消</a-button>
        <!-- <a-button type="primary" @click="submitComp">确定</a-button> -->
      </div>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import {
  Layout,
  LayoutLeftView,
  LayoutCrumbsView,
  LayoutTopView,
  LayoutWhiteView,
} from '@fs/fs-components'
import { storeToRefs } from 'pinia'
import { theme } from '@/config/antd-config-provider'
import type { UserMenu } from '@fs/fs-components/src/components/layout/layout'
import { useUserStore } from '@/stores/user'
import LoginView from './login-view.vue'
import LoginViewBg from './login-view-bg.vue'
import {
  getdraft,
  savedraft,
  formatting,
  saveLayoutLogin,
} from '@/components/code-generation/code-generation'
import { message } from 'ant-design-vue'

interface Modal {
  modelValue: boolean
}

const props: Modal = withDefaults(defineProps<Modal>(), {
  modelValue: false,
})

const components = ref<any[]>([
  {
    name: Layout,
    key: 0,
  },
  {
    name: LayoutLeftView,
    key: 1,
  },
  {
    name: LayoutCrumbsView,
    key: 2,
  },
  {
    name: LayoutTopView,
    key: 3,
  },
  {
    name: LayoutWhiteView,
    key: 4,
  },
])

const componentsList = ref<any[]>([
  {
    name: LoginView,
    key: 0,
  },
  {
    name: LoginViewBg,
    key: 1,
  },
])

const MenuData = ref<UserMenu[]>([])
const emit = defineEmits(['update:modelValue', 'update:formData'])
const userStore = useUserStore()
const { userMenu } = storeToRefs(userStore)
const open = ref(false)
const selectComp = ref<any>()
const activeLayout = ref(0)
const activeLogin = ref(0)
const loading = ref(false)

const newModelValue = computed({
  get: () => props.modelValue,
  set: (value) => {
    emit('update:modelValue', value)
  },
})

const bindData = computed(() => {
  return {
    title: '指标管理系统',
    menu: MenuData.value,
    'user-info': {
      userAvatar: '',
      userName: '小明',
    },
    'config-provider': { theme },
    breadcrumb: true,
    siderWidth: 150,
  }
})

const activeKey = ref('layout')

watch(
  newModelValue,
  async (newValue) => {
    if (newValue) {
      const { data } = await getdraft({ name: 'layout-login' })
      if (data.data) {
        activeLayout.value = data.data.activeLayout
        activeLogin.value = data.data.activeLogin
      }
    }
  },
  { deep: true, immediate: true },
)

watch(
  userMenu,
  (newValue) => {
    if (newValue) {
      MenuData.value = filterTree(JSON.parse(JSON.stringify(newValue)))
    }
  },
  { deep: true, immediate: true },
)

async function submit() {
  try {
    console.log(123)
    loading.value = true
    const obj = {
      layout: {
        pagePath: '/layout/layout-view.vue',
        content: await getLayoutContent(),
      },
      login: {
        pagePath: '/layout/login/login-view.vue',
        content: await getLoginContent(),
      },
    }
    console.log('🚀 ~ submit ~ obj:', obj)

    await saveLayoutLogin(obj)
    await savedraft({
      name: 'layout-login',
      content: { activeLayout: activeLayout.value, activeLogin: activeLogin.value },
    })
    newModelValue.value = false
    loading.value = false
    message.success('操作成功')
  } catch (err) {
    loading.value = false
    console.log('操作失败：' + err)
  }
}

async function getLoginContent() {
  const str =
    `<template>
  ${activeLogin.value === 1 ? '<login-view-bg />' : '<login-View />'}
</template>` +
    '<' +
    `script setup lang='ts'>
      ${activeLogin.value === 1 ? "import loginViewBg from '../components/login-view-bg.vue'" : " import loginView from '../components/login-view.vue'"}
    </` +
    `script>
    <style scoped lang="less"></style>
    `
  const { data } = await formatting(str)
  return data.data
}

async function getLayoutContent() {
  const str =
    `<template>
<${getCompName()} />
</template>` +
    '<' +
    `script setup lang='ts'>
${getImportName()}
    </` +
    `script>
    <style scoped lang="less"></style>
    `
  const { data } = await formatting(str)
  return data.data
}

function getCompName() {
  switch (activeLayout.value) {
    case 0:
      return `layout-default-view`
    case 1:
      return `layout-left-view`
    case 2:
      return `layout-crumbs-view`
    case 3:
      return `layout-top-view`
    case 4:
      return `layout-white-view`
    default:
      return `layout-default-view`
  }
}

function getImportName() {
  switch (activeLayout.value) {
    case 0:
      return `import layoutDefaultView from './layout-default-view.vue'`
    case 1:
      return `import layoutLeftView from './layout-left-view.vue'`
    case 2:
      return `import layoutCrumbsView from './layout-crumbs-view.vue'`
    case 3:
      return `import layoutTopView from './layout-top-view.vue'`
    case 4:
      return `import layoutWhiteView from './layout-white-view.vue'`
  }
}

function openModal(item: any) {
  open.value = true
  selectComp.value = item
}

// 当鼠标进入目标元素时，设置遮罩层可见
function showMask(item: any) {
  item.isMaskVisible = true
}

// 当鼠标离开目标元素时，设置遮罩层不可见
function hideMask(item: any) {
  item.isMaskVisible = false
}

function submitComp() {
  activeLayout.value = selectComp.value.key
  open.value = false
  console.log('selectComp', selectComp.value)
}

// 递归函数来筛选所有 menuType 等于 1 的菜单   1为菜单 2 是权限 3 页签
function filterTree(tree: UserMenu[]): UserMenu[] {
  const filterNode = (node: UserMenu): UserMenu | null => {
    if (node.isChildrenMenu === true || node.menuType === 2 || node.menuType === 3) {
      return null
    }
    let filteredChildren: UserMenu[] | undefined
    if (node.childrens) {
      filteredChildren = node.childrens
        .map(filterNode) // 递归过滤子节点
        .filter((childNode): childNode is UserMenu => childNode !== null) // 去掉值为 null 的节点
    }
    //@ts-ignore
    return { ...node, childrens: filteredChildren }
  }
  return tree.map(filterNode).filter((node): node is UserMenu => node !== null) // 最终去掉值为 null 的节点
}
</script>
<style lang="less">
.layout-list {
  display: flex;
  &-item {
    width: 200px;
    height: 140px;
    margin-right: 20px;
    position: relative;
    border: 3px solid transparent;
    // pointer-events: none;
    .preview-btn {
      position: absolute;
      bottom: -50px;
      left: 50%;
      margin-left: -32px;
    }
  }
  .active {
    border: 3px solid #1677ff;
  }

  .style1 {
    position: absolute;
    width: 1300px;
    height: 300px;
    transform: scale(0.15); /* 根据原始元素和外部盒子的尺寸比例进行缩放，0.67 = 200/300 */
    transform-origin: top left; /* 设置缩放的原点为左上角，保持位置相对正确 */
  }
  .style2 {
    position: absolute;
    width: 2000px;
    height: 1400px;
    transform: scale(0.1); /* 根据原始元素和外部盒子的尺寸比例进行缩放，0.67 = 200/300 */
    transform-origin: top left; /* 设置缩放的原点为左上角，保持位置相对正确 */
  }
  .mask-layer {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 140px;
    background-color: rgba(0, 0, 0, 0.5);
    /* 可以根据需求调整遮罩层的透明度、颜色等样式 */
    z-index: 10000;
    color: white;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
  }
}
.modal-content {
  width: 100%;
  height: 670px;
  transform-origin: top left; /* 设置缩放的原点为左上角，保持位置相对正确 */
}
.footer {
  text-align: center;
  .submit-btn {
    margin-left: 15px;
  }
}
.drawer-footer {
  margin-top: 60px;
}

:deep(.modal-fs-layout) {
  background: red;
  // .fs-layout,.layout-view-crumbs,.layout-view-top,.main,.preview {
  //   height: 100% !important;
  // }
}
.modal-fs-layout .fs-layout-preview {
  height: 100% !important;
}
.modal-fs-layout .preview .fs-login {
  width: 100%;
  top: 50%;
  left: 50%;
  margin-top: -195px;
  margin-left: -184px;
}
</style>
