<template>
  <div>
    <p class="menu-item-title">字段标题</p>
    <a-input v-model:value="defaultItem.field" :defaultValue="propsItem.field"></a-input>
    <p class="menu-item-title">显示为</p>
    <a-select v-model:value="defaultItem.titleShowType" style="width: 100%">
      <a-select-option value="text">文本</a-select-option>
      <a-select-option value="link">链接</a-select-option>
      <a-select-option value="email">Email地址</a-select-option>
      <a-select-option value="img">图片</a-select-option>
      <a-select-option value="auto">自动的</a-select-option>
    </a-select>
    <!-- 时间类型数据的设置 -->
    <div v-if="isTimeType(defaultItem.type)">
      <p class="menu-item-title">日期样式</p>
      <a-select v-model:value="defaultItem.dateFormat" style="width: 100%">
        <a-select-option value="YYYY-MM-DD">一月 31,2018</a-select-option>
        <a-select-option value="DD MMM, YYYY">31 一月，2018</a-select-option>
        <a-select-option value="dddd, MMM Do, YYYY">星期四，一月 31,2018</a-select-option>
        <a-select-option value="M/D/YYYY">1/31/2018</a-select-option>
        <a-select-option value="DD/MM/YYYY">31/1/2018</a-select-option>
        <a-select-option value="YYYY/M/D">2018/1/31</a-select-option>
      </a-select>
      <p class="menu-item-title">显示时间</p>
      <p class="menu-item-title">显示时间</p>
      <a-select v-model:value="defaultItem.titleShowType" style="width: 100%">
        <a-select-option value="off">关闭</a-select-option>
        <a-select-option value="HH:MM">HH:MM</a-select-option>
        <a-select-option value="HH:MM:SS">HH:MM:SS</a-select-option>
        <a-select-option value="HH:MM:SS:MS">HH:MM:SS:MS</a-select-option>
      </a-select>
      <p class="menu-item-title">时间格式</p>
      <a-radio-group v-model:value="defaultItem.titleShowType" style="width: 100%">
        <a-radio value="12">12小时制</a-radio>
        <a-radio value="24">24小时制</a-radio>
      </a-radio-group>
    </div>
    <!-- 数字类型数据设置 -->
    <div v-else-if="propsItem.type === 'number'">
      <p class="menu-item-title">显示一个迷你条形图</p>
      <a-switch v-model:checked="defaultItem.showProgress" :disabled="false" :loading="false" />
      <p class="menu-item-title">样式</p>
      <a-select v-model:value="defaultItem.textStyle" style="width: 100%">
        <a-select-option value="normal">正常</a-select-option>
        <a-select-option value="percent">百分比</a-select-option>
        <a-select-option value="scientific">科学的</a-select-option>
        <a-select-option value="monetary">货币</a-select-option>
      </a-select>
      <p class="menu-item-title">分割样式</p>
      <a-select v-model:value="defaultItem.splitStyle" style="width: 100%">
        <a-select-option value="1">100,000.00</a-select-option>
        <a-select-option value="2">100 000.00</a-select-option>
        <a-select-option value="3">100.000.00</a-select-option>
        <a-select-option value="4">100000.00</a-select-option>
        <a-select-option value="5">100'000.00</a-select-option>
      </a-select>
      <p class="menu-item-title">小数位数</p>
      <a-input placeholder="1" v-model:value="defaultItem.decimal"></a-input>
      <p class="menu-item-title">乘以一个数字</p>
      <a-input placeholder="1" v-model:value="defaultItem.multiple"></a-input>
      <p class="menu-item-title">添加一个前缀</p>
      <a-input placeholder="$" v-model:value="defaultItem.prefix"></a-input>
      <p class="menu-item-title">添加一个后缀</p>
      <a-input placeholder="美元" v-model:value="defaultItem.suffix"></a-input>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { isTimeType } from '@/utils/tool'
const defaultItem: any = ref()

const props: any = defineProps<{
  propsItem: any
}>()

onMounted(() => {
  defaultItem.value = props.propsItem
})
</script>
