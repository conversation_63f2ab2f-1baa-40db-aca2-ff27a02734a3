<template>
  <div class="card-box-comp">
    <div class="box-header-g">
      <div class="box-left-g">
        <div class="box-title-g">
          <slot name="title">{{ title }}</slot>
        </div>
        <div class="box-sub-title-g" v-if="subTitle">
          <slot name="subTitle">{{ subTitle }}</slot>
        </div>
      </div>
      <div class="box-right-g">
        <slot name="headerRight"></slot>
      </div>
    </div>
    <div class="box-form-g" v-if="showForm">
      <slot name="form"></slot>
    </div>
    <div class="box-main" :class="{ bg: showBg }">
      <slot></slot>
    </div>
  </div>
</template>

<script lang="ts" setup>
defineProps({
  title: {
    type: String,
    default: '',
  },
  subTitle: {
    type: String,
    default: '',
  },
  showForm: {
    type: Boolean,
    default: false,
  },
  showBg: {
    type: Boolean,
    default: false,
  },
})
</script>

<style lang="less" scoped>
.card-box-comp {
  display: flex;
  flex-direction: column;
  height: 100%;
  .box-header-g {
    display: flex;
    align-items: start;
    justify-content: space-between;
    min-height: 58px;
    .box-left-g {
      .box-title-g {
        font-size: 20px;
        font-weight: bold;
        color: #141414;
        word-break: break-all;
        text-align: left;
      }
      .box-sub-title-g {
        font-size: 14px;
        color: #999;
        margin-top: 5px;
        word-break: break-all;
        text-align: left;
      }
    }
    .box-right-g {
      margin-left: 16px;
    }
    margin-bottom: 20px;
  }
  .box-form-g {
    margin-bottom: 36px;
    min-height: 32px;
  }
  .box-main {
    flex: auto;
    // height: 0;
    overflow: auto;
    &.bg {
      background-color: #f9fafb;
    }
  }
}
</style>
