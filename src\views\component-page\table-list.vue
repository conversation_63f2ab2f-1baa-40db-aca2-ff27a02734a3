<template>
  <a-card class="order-page">
    <Table :columns="columns" :getData="getTableList" ref="tableRef" :searchFormState="form">
      <template #operate>
        <a-button type="primary" @click="addTable" v-action:addJob>新增</a-button>
      </template>
      <template #search>
        <a-form-item label="岗位名称" name="岗位名称" :rules="[{ message: '请输入岗位名称' }]">
          <a-input v-model:value="form.jobName" placeholder="请输入岗位名称" />
        </a-form-item>
        <a-form-item label="是否启用">
          <a-radio-group v-model:value="form.isUse" placeholder="请选择是否启用">
            <a-radio value="1">是</a-radio>
            <a-radio value="0">否</a-radio>
          </a-radio-group>
        </a-form-item>
      </template>
    </Table>
    <manage-modal ref="positionModal" @modalHandleOk="modalHandleOk"></manage-modal>
  </a-card>
</template>
<script setup lang="ts">
import { Table } from '@fs/fs-components'
import { useUserStore } from '@/stores/user'
import Modal from 'ant-design-vue/es/modal/Modal'
import { PositionManageDTO } from '@/utils/column'
import { getDeptListFn } from '@/components/permission/common'
import { ref, onBeforeMount, h, createVNode, watch, withDirectives, resolveDirective } from 'vue'
import { ExclamationCircleOutlined } from '@ant-design/icons-vue'
import { deleteJob, getJobList } from '@/api/services/permission'
import type { Column } from '@fs/fs-components/src/components/table/type'
import { Button, message, type CascaderProps } from 'ant-design-vue/es/components'
let columns = ref<Column[]>([])
const tableRef = ref<InstanceType<typeof Table> | null>(null)
const modalOpen = ref<boolean>(false)
const disabled = ref<boolean>(false)
const positionModal = ref<any | null>(null)
const viewDeptListOptions = ref<CascaderProps['options']>([]) // 组织
const form: Record<string, any> = ref({
  jobName: '',
  isUse: null,
  deptId: '',
})
const Enabled: { [key: string]: string } = {
  '1': '是',
  '0': '否',
}
const userStore = useUserStore()
watch(
  () => userStore.userInfo.buId,
  (Val) => {
    if (Val) {
      form.value.buId = Val
      tableRef.value?.getTableData()
    }
  },
)

function modalHandleOk() {
  tableRef.value?.getTableData()
}

onBeforeMount(() => {
  initColumns()
  getDeptListFn().then((res) => {
    viewDeptListOptions.value = res
  })
})

function getTableList() {
  return new Promise<any>((resolve) => {
    resolve({
      data: {
        records: [
          {
            buId: 'system_bu',
            jobName: '测试12',
            isUse: 1,
            jobId: '667d283cfa12fb4aca8db898',
            isUseName: '是',
            responsibility: '哈哈1',
            createTime: '2024-06-28 10:22:30',
            tenantId: 'system',
            updateUser: '666adae27d423d24e8ce6e87',
            createUser: '6667a9ba0472066050d77892',
            updateTime: '2024-07-05 11:15:45',
            userName: '系统管理员',
          },
          {
            buId: 'system_bu',
            jobName: '测试13',
            isUse: 1,
            jobId: '667d283cfa12fb4aca8db897',
            isUseName: '是',
            responsibility: '哈哈2',
            createTime: '2024-06-29 09:45:22',
            tenantId: 'system',
            updateUser: '666adae27d423d24e8ce6e86',
            createUser: '6667a9ba0472066050d77891',
            updateTime: '2024-07-06 13:05:50',
            userName: '系统管理员',
          },
          {
            buId: 'system_bu',
            jobName: '测试14',
            isUse: 1,
            jobId: '667d283cfa12fb4aca8db896',
            isUseName: '是',
            responsibility: '哈哈3',
            createTime: '2024-06-30 12:14:50',
            tenantId: 'system',
            updateUser: '666adae27d423d24e8ce6e85',
            createUser: '6667a9ba0472066050d77890',
            updateTime: '2024-07-07 15:25:30',
            userName: '系统管理员',
          },
          {
            buId: 'system_bu',
            jobName: '测试15',
            isUse: 1,
            jobId: '667d283cfa12fb4aca8db895',
            isUseName: '是',
            responsibility: '哈哈4',
            createTime: '2024-07-01 11:05:12',
            tenantId: 'system',
            updateUser: '666adae27d423d24e8ce6e84',
            createUser: '6667a9ba0472066050d77889',
            updateTime: '2024-07-08 10:45:10',
            userName: '系统管理员',
          },
          {
            buId: 'system_bu',
            jobName: '测试16',
            isUse: 1,
            jobId: '667d283cfa12fb4aca8db894',
            isUseName: '是',
            responsibility: '哈哈5',
            createTime: '2024-07-02 08:32:45',
            tenantId: 'system',
            updateUser: '666adae27d423d24e8ce6e83',
            createUser: '6667a9ba0472066050d77888',
            updateTime: '2024-07-09 12:16:20',
            userName: '系统管理员',
          },
          {
            buId: 'system_bu',
            jobName: '测试17',
            isUse: 1,
            jobId: '667d283cfa12fb4aca8db893',
            isUseName: '是',
            responsibility: '哈哈6',
            createTime: '2024-07-03 14:56:22',
            tenantId: 'system',
            updateUser: '666adae27d423d24e8ce6e82',
            createUser: '6667a9ba0472066050d77887',
            updateTime: '2024-07-10 09:30:15',
            userName: '系统管理员',
          },
          {
            buId: 'system_bu',
            jobName: '测试18',
            isUse: 1,
            jobId: '667d283cfa12fb4aca8db892',
            isUseName: '是',
            responsibility: '哈哈7',
            createTime: '2024-07-04 13:22:50',
            tenantId: 'system',
            updateUser: '666adae27d423d24e8ce6e81',
            createUser: '6667a9ba0472066050d77886',
            updateTime: '2024-07-11 15:45:55',
            userName: '系统管理员',
          },
          {
            buId: 'system_bu',
            jobName: '测试19',
            isUse: 1,
            jobId: '667d283cfa12fb4aca8db891',
            isUseName: '是',
            responsibility: '哈哈8',
            createTime: '2024-07-05 16:10:33',
            tenantId: 'system',
            updateUser: '666adae27d423d24e8ce6e80',
            createUser: '6667a9ba0472066050d77885',
            updateTime: '2024-07-12 11:22:40',
            userName: '系统管理员',
          },
          {
            buId: 'system_bu',
            jobName: '测试20',
            isUse: 1,
            jobId: '667d283cfa12fb4aca8db890',
            isUseName: '是',
            responsibility: '哈哈9',
            createTime: '2024-07-06 10:45:55',
            tenantId: 'system',
            updateUser: '666adae27d423d24e8ce6e79',
            createUser: '6667a9ba0472066050d77884',
            updateTime: '2024-07-13 14:05:12',
            userName: '系统管理员',
          },
          {
            buId: 'system_bu',
            jobName: '测试21',
            isUse: 1,
            jobId: '667d283cfa12fb4aca8db889',
            isUseName: '是',
            responsibility: '哈哈10',
            createTime: '2024-07-07 14:10:12',
            tenantId: 'system',
            updateUser: '666adae27d423d24e8ce6e78',
            createUser: '6667a9ba0472066050d77883',
            updateTime: '2024-07-14 13:22:55',
            userName: '系统管理员',
          },
        ],
        totalRecords: 11,
      },
    })
  })
}

function addTable() {
  modalOpen.value = true
  disabled.value = false
  positionModal.value?.show('add')
}

async function initColumns() {
  columns.value = [
    {
      title: '岗位编号',
      dataIndex: 'jobId',
      key: 'jobId',
    },
    {
      title: '岗位名称',
      dataIndex: 'jobName',
      searchInput: undefined,
      key: 'jobName',
    },
    {
      title: '是否启用',
      dataIndex: 'isUse',
      searchInput: undefined,
      key: 'isUse',
      customRender: (...arg: [any]): string => {
        const { record } = arg[0]
        return Enabled[record.isUse]
      },
    },
    {
      title: '创建人',
      dataIndex: 'userName',
      key: 'userName',
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
    },
  ]
  columns.value.push({
    key: 'action',
    title: '操作',
    customRender(data: Record<string, any>) {
      return [
        withDirectives(
          h(
            Button,
            {
              type: 'link',
              onClick() {
                positionModal.value?.show('edit', data.record)
              },
            },
            {
              default: () => '编辑',
            },
          ),
          [[resolveDirective('action'), '', 'updateJob']],
        ),
        withDirectives(
          h(
            Button,
            {
              type: 'link',
              onClick() {
                Modal.confirm({
                  title: '提示',
                  icon: createVNode(ExclamationCircleOutlined),
                  content: createVNode('div', { style: 'color:red;' }, '确认执行删除操作？'),
                  async onOk() {
                    try {
                      return await new Promise((resolve, reject) => {
                        // deleteJob({ jobId: data?.record?.jobId })
                        //   .then((res: any) => {
                        //     if (res.code === '000000') {
                        message.success('删除成功')
                        resolve(true)
                        tableRef.value?.resetForm()
                        //     }
                        //   })
                        //   .catch(() => {
                        //     reject()
                        //   })
                      })
                    } catch {
                      return console.log('Oops errors!')
                    }
                  },
                  class: 'test',
                })
              },
            },
            {
              default: () => '删除',
            },
          ),
          [[resolveDirective('action'), '', 'deleteJob']],
        ),
      ]
    },
  })
}
</script>
