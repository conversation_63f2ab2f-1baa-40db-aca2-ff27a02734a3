<template>
  <div class="request-box">
    <div class="parameter-box">
      <a-input-group compact>
        <div class="url-box">
          <a-select v-model:value="requestData.parameter.methods" style="width: 100px">
            <a-select-option :value="item.value" v-for="item in methodsList" :key="item.value">{{
              item.label
            }}</a-select-option>
          </a-select>
          <a-input v-model:value="requestData.parameter.url" placeholder="请输入url" />
          <a-button type="primary" class="mg-5" @click="sendHandle" :loading="loading"
            >Send</a-button
          >
        </div>
      </a-input-group>
      <div class="params-box">
        <a-tabs v-model:activeKey="activeKey">
          <a-tab-pane key="Params" :tab="`Params （${requestData.parameter.Params.length}）`">
            <paramsList
              :options="options"
              :list="requestData.parameter.Params"
              type="Params"
            ></paramsList>
          </a-tab-pane>
          <a-tab-pane key="Body" tab="Body">
            <paramsList
              :data="requestData.parameter.Body"
              :options="options"
              type="Body"
              @update:data="updateData"
            ></paramsList>
          </a-tab-pane>
          <a-tab-pane key="Cookie" :tab="`Cookie (${requestData.parameter.Cookie.length})`">
            <paramsList
              :options="options"
              :list="requestData.parameter.Cookie"
              type="Cookie"
            ></paramsList>
          </a-tab-pane>
          <a-tab-pane key="Headers" :tab="`Headers (${requestData.parameter.Headers.length})`">
            <paramsList
              :options="options"
              :list="requestData.parameter.Headers"
              type="Headers"
            ></paramsList>
          </a-tab-pane>
        </a-tabs>
      </div>
    </div>
    <div class="response-box">
      <div class="title">
        <template v-if="requestData.response.code">
          <a-tag class="tag" :color="requestData.response.code === 200 ? 'green' : 'red'"
            >{{ requestData.response.code }}
            {{ requestData.response.code === 200 ? 'ok' : '' }}</a-tag
          >
          <a-tag class="tag">{{ requestData.response.time }} ms</a-tag>
          <a-tag class="tag">{{ requestData.response['content-length'] }} B</a-tag>
        </template>
      </div>
      <div>
        <a-tabs v-model:activeKey="resActiveKey">
          <a-tab-pane key="Body" tab="Body">
            <a-radio-group v-model:value="resultType" button-style="solid" size="small">
              <a-radio-button value="Preview">Preview</a-radio-button>
              <a-radio-button value="Raw">Raw</a-radio-button>
            </a-radio-group>
            <div class="editor">
              <v-ace-editor
                v-model:value="previewValue"
                lang="json"
                theme="chrome"
                :options="{ useWorker: true }"
                :style="{ height: '450px' }"
                :readonly="true"
                v-if="resultType === 'Preview'"
              />
              <pre v-else :style="{ height: '440px' }">{{ previewValue }}</pre>
            </div>
          </a-tab-pane>
          <a-tab-pane key="Headers" tab="Headers">
            <a-table
              :dataSource="requestData.response.Headers"
              :columns="columns"
              size="small"
              :pagination="false"
            >
            </a-table>
          </a-tab-pane>
          <a-tab-pane key="content" tab="请求内容">
            <v-ace-editor
              v-model:value="configData"
              theme="chrome"
              :options="{ useWorker: true }"
              :style="{ height: '450px' }"
              :readonly="true"
              v-if="resultType === 'Preview'"
            />
          </a-tab-pane>
        </a-tabs>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import paramsList from './params-list.vue'
import { getMockTest, mockFormDataTest } from '@/api/services/manage'
import { setLocalItem, getUrl, parseUrl } from '@/utils/tool'
import moment from 'moment'
import { VAceEditor } from 'vue3-ace-editor'
import 'ace-builds/src-noconflict/mode-json' // Load the language definition file used below
import 'ace-builds/src-noconflict/theme-chrome' // Load the theme definition file used below

interface Data {
  data: any
  commonHead: any
  rawData: any
}

const props: Data = withDefaults(defineProps<Data>(), {})
const activeKey = ref('Params')
const resultType = ref('Preview')
const resActiveKey = ref('Body')
const previewValue = ref('')
const configData = ref('暂无数据')

const columns = [
  {
    title: 'Key',
    dataIndex: 'name',
    key: 'name',
    width: 200,
  },
  {
    title: 'Value',
    dataIndex: 'value',
    key: 'value',
  },
]

const emit = defineEmits(['update:value', 'update:list'])

const options = computed({
  get: () => {
    let list = []
    const { Params, Body, Headers } = requestData.value.parameter
    if (activeKey.value === 'Params') {
      list = Params.map((item: { name: any }) => {
        return { text: item.name, value: item.name }
      })
    }
    if (activeKey.value === 'Body') {
      const arr = Body.value
      if (Body.type === 'Form-Data') {
        list = arr.map((item: { name: any }) => {
          return { text: item.name, value: item.name }
        })
      }
    }
    if (activeKey.value === 'Headers') {
      list = Headers.map((item: { name: any }) => {
        return { text: item.name, value: item.name }
      })
    }
    list = list.filter((item: any) => item.text !== '')
    return list
  },
  set: () => {
    //
  },
})

const requestData = computed({
  get: () => props.data,
  set: (value) => {
    emit('update:value', value)
  },
})

const methodsList = [
  {
    label: 'GET',
    value: 'GET',
  },
  {
    label: 'POST',
    value: 'POST',
  },
  {
    label: 'PUT',
    value: 'PUT',
  },
  {
    label: 'PATCH',
    value: 'PATCH',
  },
  {
    label: 'DELETE',
    value: 'DELETE',
  },
]

const loading = ref(false)

const updateData = (data: any) => {
  emit('update:value', 'Body', data)
}

const sendHandle = async () => {
  try {
    loading.value = true
    const { parameter, response } = JSON.parse(JSON.stringify(requestData.value))
    let headers = {}
    let formData = new FormData()
    let handle
    const commonHeadParams = props?.commonHead?.Params || []
    const Params = [...commonHeadParams, ...parameter.Params]
    parameter.url = getUrl(parameter.url, Params)
    if (parameter.Body.type === 'Form-Data') {
      headers = { 'Content-Type': 'multipart/form-data' }
      parameter.Body.value.forEach((item: any) => {
        formData.append(item.name, item.value)
      })
      const str = parameter.Headers.reduce((acc: any, cur: any, index: number) => {
        return acc + `${cur.name}:${cur.value}${index < parameter.Headers.length - 1 ? ',' : ''}`
      }, '')
      if (str) {
        formData.append('Headers-value', str)
      }
      const cookieStr = parameter.Cookie.reduce((acc: any, cur: any, index: number) => {
        return acc + `${cur.name}:${cur.value}${index < parameter.Cookie.length - 1 ? ',' : ''}`
      }, '')
      if (cookieStr) {
        formData.append('Cookie-value', cookieStr)
      }
      formData.append('Url-value', parameter.url)
      formData.append('Methods-value', parameter.methods)
      handle = mockFormDataTest
    } else {
      const commonHeadCookie = props?.commonHead?.Cookie || []
      const Cookie = [...commonHeadCookie, ...parameter.Cookie]
      parameter.Cookie = Cookie
      const commonHeadHeaders = props?.commonHead?.Headers || []
      const Headers = [...commonHeadHeaders, ...parameter.Headers]
      parameter.Headers = Headers
      formData = parameter
      handle = getMockTest
    }
    const { data } = await handle(formData, headers)
    Object.assign(response, data.data)
    previewValue.value = JSON.stringify(data.data.Preview, null, 2)
    requestData.value.time = moment().format('YYYY-MM-DD HH:mm:ss')
    requestData.value.response = response
    const obj = parseUrl(props.rawData.url)
    const url = obj.ipWithPort + props.rawData.fullPath
    configData.value = getConfigData(data.data?.config)
    setLocalItem(url, requestData.value)
    emit('update:list')
  } finally {
    loading.value = false
  }
}

const getConfigData = (data: any) => {
  if (!data) return ``
  let cookieData = []
  const config = JSON.parse(JSON.stringify(data))
  if (config.headers['Cookie']) {
    const Cookie = config.headers['Cookie']
    cookieData = Cookie.split(';')
    delete config.headers['Cookie']
  }
  let str = `请求地址：
${config.url}

请求头：
${Object.keys(config.headers).reduce((acc: any, cur: any) => {
  console.log('%c 🧀 cur: ', 'font-size:12px;background-color: #6EC1C2;color:#fff;', cur)
  return acc + `${cur}: ${config.headers[cur]} \r`
})}

Cookie：
${
  cookieData &&
  cookieData.reduce((acc: any, cur: any) => {
    const list = cur.split('=')
    return acc + `${list[0]}: ${list[1]} \n`
  }, '')
}

Body：
${config.data}

  `
  return str
}

const updataHandle = (data: any) => {
  const { response } = data
  previewValue.value = JSON.stringify(response.Preview, null, 2)
  configData.value = getConfigData(response?.config)
}

defineExpose({
  updataHandle,
})
</script>
<style scoped lang="less">
.request-box {
  display: flex;
  width: 100%;
  border: 1px solid #cccc;
  height: 600px;
  overflow: auto;
  .parameter-box {
    width: 50%;
    padding: 5px;
    .url-box {
      display: flex;
      width: 100%;
      .mg-5 {
        margin: 0 5px;
      }
    }
    .params-box {
      padding-left: 3px;
    }
  }
  .response-box {
    width: 50%;
    padding: 0 5px;
    border-left: 1px solid #cccc;
    .title {
      height: 40px;
      line-height: 40px;
      .tag {
        height: 28px;
        line-height: 28px;
      }
    }
    .editor {
      margin-top: 8px;
    }
  }
}
</style>
