<template>
  <a-dropdown v-model:open="visiblePopover" title="" placement="bottom" trigger="click">
    <a-button type="link" @click="visiblePopover = true"> Left Join </a-button>
    <template #overlay>
      <a-menu class="rela-menu">
        <a-menu-item
          :class="relaKey === 'left' ? 'menu-item menu-item-active' : 'menu-item'"
          @click="handleClick('left')"
        >
          <a href="javascript:;">Left Join</a>
        </a-menu-item>
        <a-menu-item
          :class="relaKey === 'right' ? 'menu-item menu-item-active' : 'menu-item'"
          @click="handleClick('right')"
        >
          <a href="javascript:;">Right Join</a>
        </a-menu-item>
        <a-menu-item
          :class="relaKey === 'inner' ? 'menu-item menu-item-active' : 'menu-item'"
          @click="handleClick('inner')"
        >
          <a href="javascript:;">inner Join</a>
        </a-menu-item>
      </a-menu>
    </template>
    <DownOutlined />
  </a-dropdown>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { BlockOutlined } from '@ant-design/icons-vue'

const visiblePopover = ref(false)
const relaKey: any = ref('left')

const handleClick = (key: string) => {
  relaKey.value = key
  visiblePopover.value = false
}
</script>

<style scoped lang="less">
.rela-menu {
  :deep(.menu-item) {
    padding: 10px 25px;
    transition: 0.1s all;
  }
  :deep(.menu-item):hover {
    background-color: #509ee3 !important;
    color: #fff !important;
  }
  :deep(.menu-item-active) {
    background-color: #509ee3 !important;
    color: #fff !important;
  }
}
</style>
