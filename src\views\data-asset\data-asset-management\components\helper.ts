export function removeNodeById(id: string, data: any) {
  // 从树形数据中删除节点
  const removeNode = (nodes: any[]): boolean => {
    for (let i = 0; i < nodes.length; i++) {
      if (nodes[i].id === id) {
        nodes.splice(i, 1)
        return true
      }
      if (nodes[i].children && nodes[i].children.length > 0) {
        if (removeNode(nodes[i].children)) {
          return true
        }
      }
    }
    return false
  }

  removeNode(data)

  return JSON.parse(JSON.stringify(data))
}
