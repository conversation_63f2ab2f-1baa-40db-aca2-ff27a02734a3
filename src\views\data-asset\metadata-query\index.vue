<template>
  <div class="metadata-query" :style="{ '--left-width': leftWidth + '%' }">
    <a-row justify="start" align="top" type="flex" :wrap="true" class="management-row">
      <a-col class="management-col" :style="{ width: leftWidth + '%' }">
        <directory-tree ref="directoryTreeRef" @add-asset="handleDatabaseSelect" />
      </a-col>
      <div class="resize-handle" @mousedown="startResize">
        <div class="panel-grabber-vertical">
          <div class="handle-icon handle-icon-vertical"></div>
        </div>
      </div>
      <a-col
        class="management-col content right-border"
        :style="{ width: 'calc(100% - ' + leftWidth + '% - 4px)', paddingLeft: '14px' }"
      >
        <database-table
          ref="databaseTableRef"
          :database-data="databaseData"
          :database-id="selectedDatabaseId"
          v-if="databaseData"
          @refresh="handleRefresh"
        />
        <a-empty v-else style="margin-top: 100px">
          <template #description>暂无数据，请在左侧选择数据库 </template>
        </a-empty>
      </a-col>
      <!-- <a-col :span="4" class="management-col" v-if="databaseData">
        <database-details ref="databaseDetailsRef" :database-id="selectedDatabaseId" />
      </a-col> -->
    </a-row>
  </div>
</template>

<script lang="ts" setup>
import { ref, onUnmounted } from 'vue'
import DirectoryTree from './components/directory-tree.vue'
import databaseTable from './components/database-table.vue'
// import databaseDetails from './components/database-details.vue'

defineOptions({
  name: 'metadata-query',
})

const directoryTreeRef = ref()
const databaseTableRef = ref()
const databaseDetailsRef = ref()
const selectedDatabaseId = ref<string>()
const databaseData = ref<Record<string, any>>()

// 拖拽相关的状态
const leftWidth = ref(20) // 初始宽度设为20%
const isResizing = ref(false)
const startX = ref(0)
const startLeftWidth = ref(0)

const handleDatabaseSelect = (data: any) => {
  console.log('🚀 ~ handleDatabaseSelect ~ data:', data)
  selectedDatabaseId.value = data.id
  databaseData.value = data
}

const handleRefresh = () => {
  databaseDetailsRef.value?.refresh()
}

// 开始拖拽
const startResize = (e: MouseEvent) => {
  isResizing.value = true
  startX.value = e.clientX
  startLeftWidth.value = leftWidth.value
  document.addEventListener('mousemove', handleMouseMove)
  document.addEventListener('mouseup', stopResize)
}

// 处理拖拽移动
const handleMouseMove = (e: MouseEvent) => {
  if (!isResizing.value) return

  const containerWidth = document.querySelector('.management-row')?.clientWidth || 0
  const deltaX = e.clientX - startX.value
  const deltaPercent = (deltaX / containerWidth) * 100

  const newWidth = Math.max(15, Math.min(60, startLeftWidth.value + deltaPercent))
  leftWidth.value = newWidth
}

// 停止拖拽
const stopResize = () => {
  isResizing.value = false
  document.removeEventListener('mousemove', handleMouseMove)
  document.removeEventListener('mouseup', stopResize)
}

// 组件卸载时清理事件监听
onUnmounted(() => {
  document.removeEventListener('mousemove', handleMouseMove)
  document.removeEventListener('mouseup', stopResize)
})
</script>

<style lang="less" scoped>
.metadata-query {
  height: 100%;
  user-select: none;
  .management-row {
    height: 100%;
    position: relative;
    .management-col {
      height: 100%;
      padding: 12px 12px 0 12px;
      background: white;
      flex-shrink: 0;
      overflow-y: auto;
      overflow-x: hidden;
      word-break: break-all;
      word-wrap: break-word;
      white-space: normal;
    }
    .content {
      padding: 0px;
      flex: 1;
    }
    .right-border {
      border-right: 1px solid #d7d7d7;
    }
  }
  .header-div {
    display: flex;
  }

  .resize-handle {
    width: 14px;
    height: 100%;
    background-color: transparent;
    cursor: col-resize;
    position: absolute;
    left: var(--left-width);
    z-index: 1;
    flex-shrink: 0;
    background-color: RGB(252, 252, 252) !important;
    border-right: 1px solid #d7d7d7;
    user-select: none;

    .panel-grabber-vertical {
      width: 14px;
      display: flex;
      flex-direction: row;
      height: 100%;
      .handle-icon-vertical {
        margin-left: 0px;
        margin: auto;
        height: 30px;
        width: 3px;
        background-color: #e8e8ed;
        border-radius: 6px;
      }
    }
  }
}
</style>
