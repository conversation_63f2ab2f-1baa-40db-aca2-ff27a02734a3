<template>
  <div class="radio-filter">
    <div class="content">
      <Radio.Group :options="options" v-model:value="data"></Radio.Group>
    </div>
    <a-divider style="margin: 8px 0 0 0"> </a-divider>
    <div class="text-right">
      <a-button type="primary" shape="default" size="middle" @click="handleConfirm">
        确定
      </a-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Radio } from 'ant-design-vue'
import { ref } from 'vue'

const props = defineProps<{ onConfirm: (value: boolean) => void }>()
const data = ref<boolean>(false)
const options = [
  { label: '真', value: true },
  { label: '假', value: false },
]
const handleConfirm = () => {
  props.onConfirm(data.value)
}
</script>

<style scoped lang="less">
.radio-filter {
  width: 380px;
  background-color: white;
  font-size: 14px;
  border-radius: 8px;
  box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.05);
  font-size: 14px;
  font-weight: 700;
  color: rgb(76, 87, 115);
  .content {
    padding: 12px 16px;
  }

  :deep(.ant-radio-group) {
    display: flex;
    flex-direction: column;
    gap: 10px;
  }
  .text-right {
    text-align: right;
    padding: 12px 16px;
  }
}
</style>
