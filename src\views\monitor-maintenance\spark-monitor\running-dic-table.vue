<template>
  <div class="list-content">
    <!-- <a-card class="table-card" :bordered="false"> -->
    <a-table
      ref="table"
      :rowKey="(record: Data, index: number) => index"
      :indentSize="40"
      :pagination="false"
      :data-source="rddList"
      :scroll="{ x: 1800, y: 650 }"
      :columns="rddColumns"
    >
      <template #bodyCell="{ column, record }: { column: any; record: Data }">
        <template v-if="column.key === 'percent'">
          <a-progress
            :percent="record.percent * 100"
            strokeColor="#1791FF"
            :format="(percent: number) => `${percent.toFixed(2)}%`"
          />
        </template>
        <template v-if="column.key === 'status'">
          <div v-if="record.status === 2">
            <span class="green-text"></span>
            成功
          </div>
          <div v-else-if="record.status === -1">
            <span class="error-text"></span>
            失败
          </div>
          <div v-else-if="record.status === -2">
            <span class="error-text"></span>
            已卸载
          </div>
          <div v-else-if="record.status === 5">
            <span class="run-text"></span>
            执行中
          </div>
        </template>
      </template>
    </a-table>
    <!-- 分页卡片-->
    <div class="card-pagination">
      <a-pagination
        size="small"
        @change="handelPageChange"
        v-model="pagination.pageIndex"
        :total="pagination.total"
        @showSizeChange="handelShowSizeChange"
        :defaultPageSize="pagination.pageSize"
        :pageSizeOptions="pagination.pageSizeOptions"
      />
    </div>
    <!-- </a-card> -->
  </div>
</template>
<script lang="ts" setup>
import { defineExpose, ref, reactive, onMounted } from 'vue'
import { getRunDic } from '@/api/services/monitor-maintenance/spark-monitor'
import { type Action } from './spark-monitor'

type Data = {
  id: number
  tableName: string
  count: number
  status: number
  percent: number
}

const pagination = reactive({
  pageIndex: 1,
  pageSize: 10,
  total: 0,
  pageSizeOptions: ['10', '20', '30', '40'],
})

const rddList = ref<Data[]>([])
const rddColumns = [
  // {
  //   title: 'taskid',
  //   dataIndex: 'taskid',
  //   key: 'taskid'
  // },
  {
    title: '表名',
    dataIndex: 'tableName',
    key: 'tableName',
    scopedSlots: { customRender: 'tableName' },
  },
  {
    title: '总数',
    dataIndex: 'count',
    key: 'count',
    scopedSlots: { customRender: 'count' },
  },
  {
    title: '已加载条数',
    dataIndex: 'complete',
    key: 'complete',
    width: 100,
    scopedSlots: { customRender: 'complete' },
  },
  {
    title: '预估内存',
    dataIndex: 'expectedMemory',
    key: 'expectedMemory',
    width: 100,
    scopedSlots: { customRender: 'expectedMemory' },
  },
  // {
  //   title: this.$t('runningdictable_35'),
  //   dataIndex: 'usedMemory',
  //   key: 'usedMemory',
  //   width: 100,
  //   scopedSlots: { customRender: 'usedMemory' }
  // },
  {
    title: '总内存',
    dataIndex: 'totalMemory',
    key: 'totalMemory',
    scopedSlots: { customRender: 'totalMemory' },
  },
  {
    title: '磁盘使用',
    dataIndex: 'usedDiskSize',
    key: 'usedDiskSize',
    width: 100,
    scopedSlots: { customRender: 'usedDiskSize' },
  },
  {
    title: '耗时',
    dataIndex: 'timeCost',
    key: 'timeCost',
    width: 100,
    scopedSlots: { customRender: 'timeCost' },
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 90,
    scopedSlots: { customRender: 'status' },
  },
  {
    title: '百分比',
    dataIndex: 'percent',
    key: 'percent',
    width: 120,
    scopedSlots: { customRender: 'percent' },
  },
  {
    title: '作业流',
    dataIndex: 'name',
    key: 'name',
    scopedSlots: { customRender: 'name' },
  },
  {
    title: '组件描述',
    dataIndex: 'comp_desc',
    key: 'comp_desc',
    width: 100,
    scopedSlots: { customRender: 'comp_desc' },
  },
  {
    title: '操作人',
    dataIndex: 'ownerId',
    key: 'ownerId',
    scopedSlots: { customRender: 'ownerId' },
  },
  {
    title: '开始时间',
    dataIndex: 'inTime',
    key: 'inTime',
    scopedSlots: { customRender: 'inTime' },
  },
  {
    title: '结束时间',
    dataIndex: 'endTime',
    key: 'endTime',
    scopedSlots: { customRender: 'endTime' },
  },
]

async function getList() {
  const params = {
    pageIndex: pagination.pageIndex,
    pageSize: pagination.pageSize,
    code: 12,
  }

  const resp = await getRunDic({ ...params })

  if (resp.data.obj) {
    rddList.value = resp.data.obj.list
    pagination.total = resp.data.obj.total
  }
}
defineExpose({ getList } satisfies Action)

// 切换分页
function handelPageChange(pageIndex: number, pageSize: number) {
  pagination.pageIndex = pageIndex
  pagination.pageSize = pageSize
  getList()
}

// 切换分页大小
function handelShowSizeChange(pageIndex: number, pageSize: number) {
  pagination.pageIndex = pageIndex
  pagination.pageSize = pageSize
  getList()
}

onMounted(getList)
</script>

<style lang="less">
.green-text {
  display: inline-block;
  background-color: #2eff00;
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.error-text {
  display: inline-block;
  background-color: red;
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.run-text {
  display: inline-block;
  background-color: #419aff;
  width: 12px;
  height: 12px;
  border-radius: 50%;
}
</style>
