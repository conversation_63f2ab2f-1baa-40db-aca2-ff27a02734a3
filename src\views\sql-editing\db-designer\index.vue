<script setup lang="ts">
import api from '@/api/sql-edit-sys'
import { useRequest } from 'vue-hooks-plus'
import { activeDbKey } from '../use-sql-editing'
import { NodeType } from '@/api/models/sql-tree'
import IframeClient from './iframe-client'
import type { DatabaseDesign } from './types'
import { copyText } from '@/utils/utils'

const props = defineProps<{
  mode?: 'read' | 'edit'
}>()
const { runAsync: getDBData, data: dbDesignData } = useRequest(api.queryDBTablesAndColumns, {
  manual: true,
  onSuccess() {
    if (!dbDesignData.value) return
    if (!iframeClient?.isReady) return
    setDbData(dbDesignData.value)
    loading.value = false
  },
})
const iframeRef = ref<HTMLIFrameElement | null>(null)
const loading = ref(true)
const iframeUrl = computed(() => {
  let url = `${window.ENV.IFRAME_VISUAL_DESIGNER_URL}/graphs/index`
  if (props.mode) url += `?mode=${props.mode}`
  return url
})
const activeDb = inject(activeDbKey)
let iframeClient: IframeClient | null = null

function initClient() {
  if (!iframeRef.value) return
  logPlus.red('🚀 初始化 iframeClient')

  destroyClient()
  iframeClient = new IframeClient(iframeRef.value)

  iframeClient.on('ready', (dbRes: any) => {
    logPlus.red('🚀 db-designer is ready:', dbRes)
    if (dbDesignData.value) {
      setDbData(dbDesignData.value)
    }
  })

  iframeClient.on('copy-to-clipboard', async (dbRes: any) => {
    // 复制到剪贴板
    await copyDbConfig(dbRes)
  })
}
async function copyDbConfig(dbRes: any) {
  const text = typeof dbRes === 'string' ? dbRes : JSON.stringify(dbRes, null, 2)
  await copyText(text)
}
async function setDbData(data: DatabaseDesign) {
  if (!iframeClient) return
  loading.value = true
  await iframeClient!.setData(toRaw(data))
  logPlus.red('🚀 设置数据响应')
  loading.value = false
}
function destroyClient() {
  if (iframeClient) {
    logPlus.red('🚀 销毁 iframeClient')
    iframeClient.destroy()
    iframeClient = null
  }
}

function onIframeLoad() {
  loading.value = false
  initClient()
}

function onIframeError() {
  loading.value = false
  logPlus.red('db-design 加载失败')
}

onBeforeUnmount(() => {
  destroyClient()
})

watch(
  [() => activeDb?.value?.title, () => activeDb?.value?.connectionId, () => activeDb?.value?.key],
  () => {
    const db = activeDb?.value
    if (
      !((db && db?.nodeType === NodeType.database.value) || db?.nodeType === NodeType.schema.value)
    )
      return
    if (!((db.dbName || db.dbSchema) && db.dbConnection?.id)) return
    loading.value = true
    getDBData({
      databaseName: db.dbName,
      id: db.dbConnection?.id,
      schemaName: db.dbSchema,
    })
  },
  {
    immediate: true,
  },
)
</script>
<template>
  <div class="visual-design size-full">
    <a-spin v-if="dbDesignData || loading" class="size-full" :spinning="loading" tip="加载中...">
      <iframe
        id="iframe-container"
        ref="iframeRef"
        class="iframe-container size-full min-h-600px"
        :src="iframeUrl"
        frameborder="0"
        @load="onIframeLoad"
        @error="onIframeError"
      ></iframe>
    </a-spin>
    <div v-if="dbDesignData"></div>
    <a-empty v-else description="暂无数据, 请选择数据库" />
  </div>
</template>

<style scoped>
:deep(.ant-spin-nested-loading),
:deep(.ant-spin-container) {
  width: 100%;
  height: 100%;
}
</style>
