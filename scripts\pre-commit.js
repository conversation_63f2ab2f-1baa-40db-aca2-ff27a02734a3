// const { execSync } = require('child_process')
import { execSync } from 'child_process'

console.log('检查文件命名规范...')

// 执行 git 命令获取提交中的文件夹列表
const fileNames = execSync('git diff --cached --name-only --diff-filter=A').toString().trim().split('\n')

// 检查文件夹名是否包含大写字母
const regex = /[A-Z]/
const filesWithUpperCase = fileNames.filter((fileName) => {
  if (fileName.startsWith('macos')) {
    console.log('忽略检查:', fileName)
    return false
  }
  return regex.test(fileName)
})

// 如果存在包含大写字母的文件夹，则输出错误信息并退出进程
if (filesWithUpperCase.length > 0) {
  console.error('\x1b[31m%s\x1b[0m', '不能提交大写文件名，请阅读 README.md 代码规范修改以下文件名:')
  console.error(filesWithUpperCase.join('\n'))
  // eslint-disable-next-line no-undef
  process.exit(1)
} else {
  console.log('\x1b[32m%s\x1b[0m', '符合规范')
}
