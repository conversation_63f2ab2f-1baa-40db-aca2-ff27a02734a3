/**
 * 获取项目列表响应数据
 */
export interface ProjectListResponse {
  code?: string
  data?: ProjectItem[]
  error?: { [key: string]: any }
  msg?: string
  reqid?: string
  timestamp?: Date
  [property: string]: any
}

/**
 * Project，项目
 */
export interface ProjectItem {
  /**
   * 自增ID主键
   */
  autoId?: number
  /**
   * 项目描述
   */
  description?: string
  /**
   * 项目名称
   */
  name?: string
  /**
   * 项目编号
   */
  number?: string
  /**
   * 项目包名
   */
  packageName?: string
  /**
   * 项目标签
   */
  tag?: string
}

/**
 * 获取函数列表响应数据
 */
export interface FunctionListResponse {
  code?: string
  data?: FunctionItem[]
  error?: { [key: string]: any }
  msg?: string
  reqid?: string
  timestamp?: Date
  [property: string]: any
}

/**
 * 函数
 */
export interface FunctionItem {
  /**
   * 自增ID主键
   */
  autoId?: number
  /**
   * 低代码
   */
  code?: string
  /**
   * 完整代码
   */
  completionCode?: string
  /**
   * 名称
   */
  name?: string
  /**
   * 项目ID
   */
  projectId?: number
  /**
   * 项目的包名
   */
  projectPackageName?: string
  /**
   * 状态
   */
  status?: string
  /**
   * 标签
   */
  tag?: string
  /**
   * 版本
   */
  version?: string
}

/**
 * 获取规则列表响应数据
 */
export interface RuleListResponse {
  code?: string
  data?: RuleItem[]
  error?: { [key: string]: any }
  msg?: string
  reqid?: string
  timestamp?: Date
  [property: string]: any
}

interface ParamItem {
  name: string
  valueType: string
  key: string
  keyName: string
}

/**
 * Rule，规则
 */
export interface RuleItem {
  /**
   * 自增ID主键
   */
  autoId?: number
  /**
   * 规则表达式
   */
  expression?: string
  /**
   * 名称
   */
  name?: string
  /**
   * 项目ID
   */
  projectId?: number
  /**
   * 状态
   */
  status?: string
  /**
   * 标签
   */
  tag?: string
  /**
   * 版本
   */
  version?: string
  /**
   * 版本 0:自定义规则, 1: 可视化规则
   */
  ruleType?: number
  /**
   * 规则表达式类型
   */
  expressionType?: string
  /**
   * 规则参数信息
   */
  paramsInfo?: ParamItem[]
  /**
   * 可视化json数据
   */
  visualJson?: string | null
}
