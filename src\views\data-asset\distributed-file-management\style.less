.distributed-file-management {
  height: 100%;
  user-select: none;
  background: white;
  .management-row {
    height: 100%;
    position: relative;
    .management-col {
      height: 100%;
      padding: 16px 12px 0 16px;
      background: white;
      flex-shrink: 0;
      overflow-y: auto;
      overflow-x: hidden;
      word-break: break-all;
      word-wrap: break-word;
      white-space: normal;
    }
    .content {
      padding: 0px;
      flex: 1;
    }
    .right-border {
      border-right: 1px solid #d7d7d7;
    }
  }
  .header-div {
    display: flex;
  }

  .resize-handle {
    width: 14px;
    height: 100%;
    background-color: transparent;
    cursor: col-resize;
    position: absolute;
    left: var(--left-width);
    z-index: 1;
    flex-shrink: 0;
    background-color: RGB(252, 252, 252) !important;
    border-right: 1px solid #d7d7d7;
    user-select: none;

    .panel-grabber-vertical {
      width: 14px;
      display: flex;
      flex-direction: row;
      height: 100%;
      .handle-icon-vertical {
        margin-left: 0px;
        margin: auto;
        height: 30px;
        width: 3px;
        background-color: #e8e8ed;
        border-radius: 6px;
      }
    }
  }
}