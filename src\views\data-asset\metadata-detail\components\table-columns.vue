<template>
  <div class="data-preview">
    <a-row justify="start" align="top" type="flex" :wrap="true" class="data-preview-row">
      <a-col :span="24" class="data-preview-col">
        <div class="content">
          <div class="search-container">
            <a-input
              v-model:value="searchText"
              placeholder="请输入字段名搜索"
              style="width: 400px; margin-bottom: 12px"
              allow-clear
            />
          </div>
          <!-- 列的内容 -->
          <a-table
            :dataSource="filteredData"
            :columns="columns"
            :pagination="false"
            rowKey="columnName"
            :scroll="{ y: 'calc(100vh - 350px)' }"
          >
            <!-- 使用插槽方式添加操作列 -->
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'action'">
                <a-button type="link" @click="openRecordModal(record)">修改记录</a-button>
              </template>
              <template v-if="column.key === 'customComment'">
                <EditOutlined @click.prevent="openEditModal(record)" v-if="record.inuse === 'Y'" />
                {{ record?.customComment }}
              </template>
              <template v-if="column.key === 'inuse'">
                <a-tag style="font-size: 13px" :color="record.inuse === 'Y' ? 'green' : 'red'">{{
                  record.inuse === 'Y' ? '有效' : '无效'
                }}</a-tag>
              </template>
              <template v-if="column.key === 'columnName'">
                <div style="display: flex; align-items: center">
                  <a-tooltip placement="bottom" v-if="record.columnKey === 'PRI'">
                    <template #title>
                      <span>主键</span>
                    </template>
                    <img
                      :src="Pri"
                      alt=""
                      style="width: 18px; height: 18px; margin-right: 5px; margin-top: 2px"
                    />
                  </a-tooltip>
                  <a-tooltip placement="bottom" v-else-if="record.isNullable === 'NO'">
                    <template #title>
                      <span>非空</span>
                    </template>
                    <img
                      :src="noNull"
                      alt=""
                      style="width: 18px; height: 18px; margin-right: 5px; margin-top: 2px"
                    />
                  </a-tooltip>
                  {{ record.columnName }}
                </div>
              </template>
            </template>
          </a-table>
        </div>
      </a-col>
    </a-row>
  </div>

  <!-- 编辑注释弹窗 -->
  <a-modal
    v-model:visible="editModalVisible"
    title="编辑注释"
    @ok="handleEditOk"
    :confirmLoading="submitLoading"
    @cancel="handleEditCancel"
  >
    <a-form :model="editForm" :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }">
      <a-form-item label="自定义注释" name="customComment">
        <a-input v-model:value="editForm.customComment" placeholder="请输入自定义注释" />
      </a-form-item>
    </a-form>
  </a-modal>

  <!-- 修改记录弹窗 -->
  <a-modal
    v-model:visible="recordModalVisible"
    title="修改记录"
    @cancel="handleRecordModalCancel"
    width="1000px"
  >
    <a-table
      :columns="nestedColumns"
      :dataSource="currentRecordData"
      :pagination="false"
      :loading="recordLoading"
      :scroll="{ x: 1600, y: 400 }"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'event'">
          <a-tag style="font-size: 13px" :color="getEventColor(record.event)">{{
            getEventText(record.event)
          }}</a-tag>
        </template>
        <template v-if="column.key === 'columnName'">
          <div
            style="display: flex; align-items: center; overflow: hidden"
            :title="record.columnName"
          >
            <a-tooltip placement="bottom" v-if="record.columnKey === 'PRI'">
              <template #title>
                <span>主键</span>
              </template>
              <img
                :src="Pri"
                alt=""
                style="width: 18px; height: 18px; margin-right: 5px; margin-top: 2px"
              />
            </a-tooltip>
            <a-tooltip placement="bottom" v-else-if="record.isNullable === 'NO'">
              <template #title>
                <span>非空</span>
              </template>
              <img
                :src="noNull"
                alt=""
                style="width: 18px; height: 18px; margin-right: 5px; margin-top: 2px"
              />
            </a-tooltip>
            {{ record.columnName }}
          </div>
        </template>
      </template>
    </a-table>
    <template #footer>
      <a-button type="primary" @click="handleRecordModalCancel">关闭</a-button>
    </template>
  </a-modal>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import {
  getColumnListByDb,
  getColumnModifyList,
} from '@/api/assetmanager/metadatamanage/metadatamanage'
import { columnsinfoUpdate } from '@/api/services/data-asset/hbase-management'
import { message } from 'ant-design-vue'
import noNull from '@/assets/data-base/nempty.png'
import Pri from '@/assets/data-base/pri.png'
import { EditOutlined } from '@ant-design/icons-vue'

const route = useRoute()
// 提取路由参数到顶部，避免重复声明
const tableName = computed(() => (route.query.tableName as string) || '')
const datasourceId = computed(() => route.query.datasourceId as string | number)
const databaseName = computed(() => (route.query.databaseName as string) || '')

type ResponseData = Awaited<ReturnType<typeof getColumnListByDb>>['data']
type TableDetail = ResponseData & { columnsList?: ResponseData['columnList'] }

const tableDetailRef = ref<TableDetail>()
const columns = [
  { title: '字段名', dataIndex: 'columnName', key: 'columnName' },
  { title: '字段序号', dataIndex: 'ordinalPosition', key: 'ordinalPosition' },
  { title: '默认值', dataIndex: 'columnDefault', key: 'columnDefault' },
  // { title: '可否为空', dataIndex: 'isNullable', key: 'isNullable' },
  { title: '数据类型', dataIndex: 'columnType', key: 'columnType' },
  // { title: '主键外键约束', dataIndex: 'columnKey', key: 'columnKey' },
  {
    dataIndex: 'columnComment',
    title: '字段注释',
    key: 'columnComment',
    width: 150,
  },
  {
    dataIndex: 'customComment',
    title: '自定义注释',
    key: 'customComment',
  },
  {
    dataIndex: 'inuse',
    title: '是否有效字段',
    key: 'inuse',
  },
  {
    dataIndex: 'endTime',
    title: '失效日期',
    key: 'endTime',
  },
  {
    title: '操作',
    key: 'action',
    width: 140,
  },
]

// 嵌套表格的列定义
const nestedColumns = [
  { title: '字段名', dataIndex: 'columnName', key: 'columnName', width: 200 },
  { title: '字段序号', dataIndex: 'ordinalPosition', key: 'ordinalPosition', width: 100 },
  { title: '默认值', dataIndex: 'columnDefault', key: 'columnDefault', width: 100 },
  // { title: '可否为空', dataIndex: 'isNullable', key: 'isNullable', width: 100 },
  { title: '数据类型', dataIndex: 'columnType', key: 'columnType', width: 140 },
  // { title: '主键外键约束', dataIndex: 'columnKey', key: 'columnKey', width: 150 },
  {
    dataIndex: 'columnComment',
    title: '字段注释',
    key: 'columnComment',
    width: 150,
  },
  {
    dataIndex: 'customComment',
    title: '自定义注释',
    key: 'customComment',
    width: 150,
  },
  {
    dataIndex: 'inuse',
    title: '是否有效字段',
    key: 'inuse',
    width: 150,
  },
  {
    dataIndex: 'taskUpdateTime',
    title: '任务更新时间',
    key: 'taskUpdateTime',
    width: 200,
  },
  {
    dataIndex: 'endTime',
    title: '失效日期',
    key: 'endTime',
    width: 200,
  },
  {
    title: '修改类型',
    dataIndex: 'event',
    key: 'event',
    fixed: 'right',
    width: 100,
  },
]

// 嵌套表格数据
const nestedTableData = ref<Record<string, any[]>>({})
const searchText = ref('')

// 修改记录弹窗相关
const recordModalVisible = ref(false)
const currentRecordData = ref<any[]>([])
const recordLoading = ref(false)

// 打开修改记录弹窗
const openRecordModal = async (record: any) => {
  const columnName = record.columnName
  recordModalVisible.value = true
  recordLoading.value = true

  try {
    if (!nestedTableData.value[columnName]) {
      const res = await getColumnModifyList({
        datasourceId: datasourceId.value as number,
        databaseName: databaseName.value,
        tableName: tableName.value,
        columnName: record.columnName,
      })
      nestedTableData.value[columnName] = res.data
    }

    currentRecordData.value = nestedTableData.value[columnName] || []
  } catch (error) {
    console.error('获取修改记录失败:', error)
    message.error('获取修改记录失败')
  } finally {
    recordLoading.value = false
  }
}

// 添加过滤后的数据计算属性
const filteredData = computed(() => {
  if (!searchText.value) {
    return tableDataSource.value
  }
  return tableDataSource.value.filter((item: any) =>
    item.columnName.toLowerCase().includes(searchText.value.toLowerCase()),
  )
})

// 获取事件文本
const getEventText = (event: string): string => {
  const eventMap: Record<string, string> = {
    'modify column': '更新',
    'add column': '新增',
    'dropped column': '删除',
    'modified column': '更新前记录',
  }
  return event ? eventMap[event] || event : '初始字段'
}

// 获取事件颜色
const getEventColor = (event: string): string => {
  const colorMap: Record<string, string> = {
    'modify column': 'blue',
    'add column': 'green',
    'dropped column': 'red',
  }
  return event ? colorMap[event] || 'default' : 'default'
}

// 关闭修改记录弹窗
const handleRecordModalCancel = () => {
  recordModalVisible.value = false
}

// 获取记录历史的模拟函数，实际使用时应替换为真实API调用
const fetchRecordHistory = async (columnName: string) => {
  // 模拟API延迟
  await new Promise((resolve) => setTimeout(resolve, 500))

  // 返回模拟数据
  return [
    {
      recordId: '1',
      recordValue: '示例值1',
      recordType: '字符串',
      createTime: '2023-01-01 12:00:00',
    },
    {
      recordId: '2',
      recordValue: '示例值2',
      recordType: '字符串',
      createTime: '2023-01-02 13:00:00',
    },
    {
      recordId: '3',
      recordValue: '示例值3',
      recordType: '字符串',
      createTime: '2023-01-03 14:00:00',
    },
  ]
}

// 编辑弹窗相关
const editModalVisible = ref(false)
const currentRecord = ref<any>(null)
const editForm = ref({
  customComment: '',
})
const submitLoading = ref(false)

// 打开编辑弹窗
const openEditModal = (record: any) => {
  currentRecord.value = record
  editForm.value.customComment = record.customComment || ''
  editModalVisible.value = true
}

// 提交编辑注释
const handleEditOk = async () => {
  submitLoading.value = true
  try {
    // 使用计算属性获取参数值
    await columnsinfoUpdate({
      customComment: editForm.value.customComment,
      tableName: tableName.value,
      tableDbname: databaseName.value,
      tableSchema: databaseName.value,
      dbid: datasourceId.value,
      columnName: currentRecord.value.columnName,
    })
    message.success('更新注释成功')
    editModalVisible.value = false
    fetchTableDetail()
  } catch (error) {
    console.error('更新注释失败:', error)
  } finally {
    submitLoading.value = false
  }
}

// 取消编辑
const handleEditCancel = () => {
  editModalVisible.value = false
}

const tableDataSource = computed(() => tableDetailRef.value ?? [])
const fetchTableDetail = async () => {
  try {
    const response = await getColumnListByDb(
      {
        tableName: tableName.value,
        datasourceId: datasourceId.value as number,
        databaseName: databaseName.value,
      },
      {},
    )
    tableDetailRef.value = response.data
  } catch (error) {
    console.error('Error fetching table detail:', error)
  }
}

onMounted(() => {
  fetchTableDetail()
})
</script>

<style lang="less" scoped>
.data-preview {
  width: 100%;
  height: 100%;
  overflow: auto;
}
</style>
