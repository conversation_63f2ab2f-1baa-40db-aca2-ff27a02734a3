<script lang="ts" setup>
import Statistics from './statistics.vue'
import { type ResBarChartData, type ResDetailData } from './data-source'
import { ref, defineExpose, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
// import DlgEcharts from './dlg-echarts.vue'
import * as echarts from 'echarts'
import { reqGetBarChart, reqGetQualityDetail } from '@/api/services/dataquality'
import dayjs, { Dayjs } from 'dayjs'
import DataDetailModal from './data-detail-modal.vue'

const router = useRouter()

const dlgEchartsRef = ref<any>(null)
const detailData = ref<ResDetailData>({})
const barChartData = ref<ResBarChartData>({})
const dateFormat = 'YYYY-MM-DD'
const dateValue = ref<[Dayjs, Dayjs]>([
  dayjs().subtract(6, 'day').startOf('day'),
  dayjs().endOf('day'),
])

watch(dateValue, (val) => {
  getDetailData()
  getBarChart()
})

const handleDateChange = (val: any) => {
  console.log(val)
}

const getDetailData = async () => {
  const { compId, startTime, endTime, compDesc } = router.currentRoute.value.query
  const params = {
    compId: compId as string,
    startTime: dateValue.value?.[0].format(dateFormat) || '',
    endTime: dateValue.value?.[1].format(dateFormat) || '',
  }
  const res = await reqGetQualityDetail(params)
  console.log('%c [ res ]-58', 'font-size:13px; background:#b3f81b; color:#f7ff5f;', res)
  detailData.value = res
  detailData.value.compDesc = compDesc as string
}

const getBarChart = async () => {
  const { compId, startTime, endTime } = router.currentRoute.value.query
  const params = {
    compId: compId as string,
    startTime: dateValue.value?.[0].format(dateFormat) || '',
    endTime: dateValue.value?.[1].format(dateFormat) || '',
  }
  const res = await reqGetBarChart(params)
  console.log('%c [  ]-57', 'font-size:13px; background:#912d98; color:#d571dc;', res)
  barChartData.value = res
  // detailData.value = res
}

onMounted(() => {
  getDetailData()
  getBarChart()
})

// #region
// echarts数据

const open = ref<boolean>(false)
const chartRef = ref<HTMLElement | null>(null)
const chartRef2 = ref<HTMLElement | null>(null)
let chartInstance: echarts.ECharts | null = null
let chartInstance2: echarts.ECharts | null = null

const showModal = () => {
  open.value = true
}

// 条形+折线图数据
const xData = ['一月', '二月', '三月', '四月', '五月', '六月']
const barData = [120, 200, 150, 80, 70, 110]
const lineData = [100, 180, 130, 60, 90, 150]

const option = {
  title: {
    text: '质量执行统计',
    left: 'left',
    top: 'top',
    textStyle: {
      fontSize: 18,
      color: '#444',
    },
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      // 坐标轴指示器，坐标轴触发有效
      type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
    },
  },
  color: ['#ccc'], // 顺序对应 series 或 data
  legend: {
    data: ['111', '222', '333'], // 和 series name 保持一致
  },
  xAxis: {
    type: 'category',
    data: xData,
  },
  yAxis: {
    type: 'value',
  },
  series: [
    {
      name: '111',
      type: 'bar',
      // barWidth: '15%',
      data: [
        {
          value: 200,
          itemStyle: {
            color: '#f5222d',
          },
        },
        {
          value: 500,
          itemStyle: {
            color: '#4cca9f',
          },
        },
      ],
    },
    {
      name: '222',
      type: 'bar',
      // barWidth: '15%',
      data: [
        {
          value: 200,
          itemStyle: {
            color: '#f5222d',
          },
        },
        {
          value: 300,
          itemStyle: {
            color: '#4cca9f',
          },
        },
      ],
    },
    {
      name: '333',
      type: 'bar',
      // barWidth: '15%',
      data: [
        {
          value: 200,
          itemStyle: {
            color: '#f5222d',
          },
        },
        {
          value: 300,
          itemStyle: {
            color: '#4cca9f',
          },
        },
      ],
    },
  ],
  dataZoom: [
    {
      type: 'slider', // 显示拖拽条
      show: true,
      xAxisIndex: 0, // 针对第一个x轴
      height: 24,
      bottom: 10,
      start: 0, // 默认起始百分比
      end: 100, // 默认结束百分比
    },
  ],
}

// 饼图数据
const pieOption = {
  title: {
    text: '质量执行通过率',
    left: 'right',
    top: 'top',
    textStyle: {
      color: '#444',
    },
  },
  tooltip: {
    trigger: 'item',
    formatter: '{b}: {c} ({d}%)',
  },
  legend: {
    orient: 'vertical',
    left: 10,
    data: ['通过', '不通过'],
  },
  color: ['#4cca9f', '#f5222d'], // 按顺序对应 data 的每一项
  series: [
    {
      name: '类别分布',
      type: 'pie',
      radius: '60%',
      center: ['50%', '55%'],
      data: [
        { value: 0, name: '通过' },
        { value: 0, name: '不通过' },
      ],
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.3)',
        },
      },
    },
  ],
}

watch(barChartData, (val) => {
  console.log(
    '%c [ val ]-217',
    'font-size:13px; background:#d2677c; color:#ffabc0;',
    toRaw(val.xAxis),
  )
  const colorMap = {
    通过: '#4cca9f',
    不通过: '#f5222d',
  }
  const colorArr = val.result?.map((item) => colorMap[item])

  const series = val.name?.map((item: string, index: number, arr: string[]) => {
    const data = val.xAxis?.map((ii: string, idx: number) => {
      console.log(
        '%c [  ]-236',
        'font-size:13px; background:#249b16; color:#68df5a;',
        val.data?.[item]?.[idx],
      )
      return {
        // @ts-ignore
        value: val.data?.[index]?.[item]?.[idx] || 0,
        itemStyle: {
          color: colorArr?.[idx],
        },
      }
    })
    return {
      name: item,
      type: 'bar',
      data,
      // data: [
      //   {
      //     value: 200,
      //     itemStyle: {
      //       color: '#f5222d',
      //     },
      //   },
      //   {
      //     value: 500,
      //     itemStyle: {
      //       color: '#4cca9f',
      //     },
      //   },
      // ],
    }
  })

  nextTick(() => {
    if (chartInstance) {
      chartInstance.dispose()
      chartInstance = null
    }
    chartInstance = echarts.init(chartRef.value)
    chartInstance.setOption({
      ...option,
      xAxis: {
        ...option.xAxis,
        data: val.xAxis,
      },
      legend: {
        ...option.legend,
        data: val.name,
      },
      series: (series || []).map((item) => ({
        ...item,
        label: {
          show: true,
          position: 'top',
          formatter: '{c}',
          fontSize: 12,
          color: '#666',
        },
      })),
    })

    // Add click event handler
    chartInstance.on('click', (params) => {
      const dataIndex = params.dataIndex
      const val = barChartData.value.result?.[dataIndex]
      if (val === '不通过') {
        // Pass flowInstanceId and other parameters to the modal
        const { compId, flowInstanceId } = router.currentRoute.value.query
        dataDetailModalRef.value?.showModal({
          flowInstanceId,
          compId: compId as string,
          startTime: dateValue.value[0].format(dateFormat),
          endTime: dateValue.value[1].format(dateFormat),
        })
      }
      console.log('🚀 ~ chartInstance.on ~ val:', val)
      console.log('Bar clicked:', params)
      console.log('barChartData.value', barChartData.value)
    })
  })
})

watch(detailData, (val) => {
  nextTick(() => {
    if (chartInstance2) {
      chartInstance2.dispose()
      chartInstance2 = null
    }
    chartInstance2 = echarts.init(chartRef2.value)
    chartInstance2.setOption({
      ...pieOption,
      series: [
        {
          ...pieOption.series[0],
          data: [
            { value: val.executeSuccessCnt, name: '通过' },
            { value: val.executeFailedCnt, name: '不通过' },
          ],
        },
      ],
    })
  })
})

onMounted(() => {
  // chartInstance?.resize()
  // chartInstance2?.resize()

  window.addEventListener('resize', handleResize)
})

onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize)
  chartInstance?.dispose()
  chartInstance2?.dispose()
})

function handleResize() {
  chartInstance?.resize()
  chartInstance2?.resize()
}

// #endregion

const dataDetailModalRef = ref<any>(null)

defineExpose({ showModal })
</script>

<template>
  <div class="reporter-detail-page">
    <a-page-header :title="detailData.compDesc" @back="() => router.back()" />
    <section style="margin-bottom: 24px">
      <Statistics :detailData="detailData" />
    </section>
    <section style="margin-bottom: 24px">
      <a-range-picker v-model:value="dateValue" :format="dateFormat" @change="handleDateChange" />
    </section>
    <!-- <a-table :columns="detailColumns" :data-source="dataSource">
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'name'">
          <div style="display: flex; align-items: center">
            <a-tooltip :title="statusTextMap[record.status]">
              <div class="color-block" :style="{ background: statusMap[record.status] }"></div>
            </a-tooltip>
            <a-button style="padding: 0" type="link" @click="handleGoDetail(record)">{{
              record.name
            }}</a-button>
          </div>
        </template>
        <template v-if="column.key === 'operation'">
          <a-button style="padding: 0" danger type="link" @click="handleDel(record)">删除</a-button>
        </template>
      </template>
    </a-table> -->
    <div class="chart-container">
      <div class="part-item" ref="chartRef"></div>
      <div class="part-item1" ref="chartRef2"></div>
    </div>
    <DataDetailModal ref="dataDetailModalRef" />
  </div>
  <!-- <DlgEcharts ref="dlgEchartsRef"></DlgEcharts> -->
</template>

<style lang="less" scoped>
.color-block {
  display: inline-block;
  width: 16px;
  height: 16px;
  border-radius: 4px;
  margin-right: 4px;
}
:deep(.ant-page-header) {
  padding: 0;
  padding-bottom: 20px;
}
.reporter-detail-page {
  :deep(.ant-page-header-heading-title) {
    font-weight: 500;
    font-size: 16px;
    color: #000000e0;
  }
  :deep(.ant-table) {
    border: 1px solid #ccc;
  }
}
.chart-container {
  display: flex;
  height: 400px;
  justify-content: space-between;
  width: 100%;
  .part-item {
    height: 100%;
    border: 1px solid #ccc;
    border-radius: 10px;
    width: 65%;
    padding: 10px;
  }
  .part-item1 {
    height: 100%;
    border: 1px solid #ccc;
    border-radius: 10px;
    width: 34%;
    padding: 10px;
  }
}
</style>
