import { expect, test } from 'vitest'
import { createModal } from './cktvkajpih'

test('createModal', async () => {
  const { data } = await createModal({ title: '*0bw', content: 'CCWLDi' })

  expect(data).toHaveProperty('code')
  expect(data).toHaveProperty('msg')
  expect(data.data).toHaveProperty('id'),
    expect(data.data).toHaveProperty('title'),
    expect(data.data).toHaveProperty('content'),
    expect(data.data).toHaveProperty('buttons')
})
