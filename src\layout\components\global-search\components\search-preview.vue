<template>
  <div
    class="search-preview"
    v-show="visible && (hasResults || loading || (!hasResults && keyword))"
  >
    <div class="preview-container" @mousedown="handleMouseDown">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-state">
        <a-spin size="small">
          <div class="loading-text">正在搜索...</div>
        </a-spin>
      </div>

      <!-- 搜索结果 -->
      <div v-if="!loading && hasResults" class="result-section">
        <div class="section-header">
          <i class="iconfont" :class="typeConfig.icon"></i>
          <span class="section-title">{{ typeConfig.title }}</span>
        </div>
        <div class="result-list">
          <div
            v-for="(item, index) in filteredResults"
            :key="`${searchType}-${index}`"
            class="result-item"
            :class="{ active: selectedIndex === index }"
            @click="handleItemClick(item)"
            @mouseenter="handleItemHover(index)"
          >
            <div class="item-icon">
              <!-- 数据库类型显示对应的 SVG 图标 -->
              <type-icon
                v-if="isDatabaseType(item) && item.dbType"
                style="margin-right: 10px"
                :type="item.dbType"
              />
              <!-- 其他类型显示字体图标 -->
              <i v-else class="iconfont" :class="typeConfig.icon"></i>
            </div>
            <div class="item-content">
              <div class="item-title" v-html="highlightKeyword(item.name)"></div>
              <div class="item-description" v-if="item.description">
                {{ item.description }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-if="!loading && !hasResults && keyword" class="empty-state">
        <i class="iconfont icon-sousuo"></i>
        <div class="empty-text">没有找到相关结果</div>
        <div class="empty-suggestion">试试其他关键词</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, watch, nextTick, onBeforeUnmount, onMounted } from 'vue'
import { type SearchResultItem, SearchType, SearchTypeLabels } from '../search'
import TypeIcon from '@/views/data-asset/metadata-query/components/type-icon.vue'

// 搜索类型配置
interface TypeConfig {
  icon: string
  title: string
  maxResults: number
}

// Props 定义
interface Props {
  visible: boolean
  keyword: string
  results: SearchResultItem[]
  selectedIndex: number
  searchType: SearchType
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
})

// Emits 定义
const emit = defineEmits<{
  select: [item: SearchResultItem]
  hover: [index: number]
  navigate: [direction: 'up' | 'down']
  close: []
}>()

// 搜索类型配置映射
const typeConfigMap: Record<string, TypeConfig> = {
  [SearchType.DATABASE]: {
    icon: 'icon-shujuku',
    title: SearchTypeLabels[SearchType.DATABASE],
    maxResults: 5,
  },
  [SearchType.TABLE]: {
    icon: 'icon-shujubiaoge',
    title: SearchTypeLabels[SearchType.TABLE],
    maxResults: 8,
  },
  [SearchType.ASSET]: {
    icon: 'icon-zichanku',
    title: SearchTypeLabels[SearchType.ASSET],
    maxResults: 5,
  },
  [SearchType.ASSET_TABLE]: {
    icon: 'icon-zichanbiaodan',
    title: SearchTypeLabels[SearchType.ASSET_TABLE],
    maxResults: 5,
  },
  [SearchType.METRIC]: {
    icon: 'icon-zhibiao',
    title: SearchTypeLabels[SearchType.METRIC],
    maxResults: 5,
  },
  [SearchType.API_LIST]: {
    icon: 'icon-apiliebiao',
    title: SearchTypeLabels[SearchType.API_LIST],
    maxResults: 5,
  },
}

// 获取当前搜索类型的配置
const typeConfig = computed((): TypeConfig => {
  return (
    typeConfigMap[props.searchType] || {
      icon: 'icon-sousuo',
      title: SearchTypeLabels[props.searchType] || '搜索结果',
      maxResults: 5,
    }
  )
})

// 根据搜索类型过滤结果
const filteredResults = computed((): SearchResultItem[] => {
  if (!props.searchType) {
    return props.results.slice(0, 8) // 全部类型时显示前8条
  }
  return props.results
    .filter((item) => item.type === props.searchType)
    .slice(0, typeConfig.value.maxResults)
})

// 是否有搜索结果
const hasResults = computed((): boolean => {
  return filteredResults.value.length > 0
})

// 总结果项（用于键盘导航）
const totalItems = computed((): SearchResultItem[] => {
  return filteredResults.value
})

// 高亮关键字
const highlightKeyword = (text: string): string => {
  if (!props.keyword || !text) return text

  const regex = new RegExp(`(${props.keyword})`, 'gi')
  return text.replace(regex, '<span class="highlight">$1</span>')
}

// 判断是否为数据库类型的搜索结果
const isDatabaseType = (item: SearchResultItem): boolean => {
  return props.searchType === SearchType.DATABASE || item.type === SearchType.DATABASE
}

// 处理项目点击
const handleItemClick = (item: SearchResultItem): void => {
  emit('select', item)
}

// 处理预览容器的鼠标按下事件，防止失去焦点
const handleMouseDown = (event: MouseEvent): void => {
  // 阻止默认行为，避免输入框失去焦点
  event.preventDefault()
}

// 处理项目悬停
const handleItemHover = (index: number): void => {
  emit('hover', index)
}

// 滚动到选中项
const scrollToSelectedItem = (): void => {
  nextTick(() => {
    if (props.selectedIndex < 0) return

    const container = document.querySelector('.preview-container') as HTMLElement
    if (!container) return

    const activeItem = container.querySelector('.result-item.active') as HTMLElement
    if (!activeItem) return

    // 计算相对于容器的位置
    const itemTop = activeItem.offsetTop
    const itemHeight = activeItem.offsetHeight
    const containerScrollTop = container.scrollTop
    const containerHeight = container.clientHeight

    // 如果选中项在可视区域上方
    if (itemTop < containerScrollTop) {
      container.scrollTop = itemTop
    } else if (itemTop + itemHeight > containerScrollTop + containerHeight) {
      // 如果选中项在可视区域下方
      container.scrollTop = itemTop + itemHeight - containerHeight
    }
  })
}

// 处理键盘事件
const handleKeydown = (event: KeyboardEvent): void => {
  if (!props.visible || !hasResults.value) return

  switch (event.key) {
    case 'ArrowDown':
      event.preventDefault()
      emit('navigate', 'down')
      break
    case 'ArrowUp':
      event.preventDefault()
      emit('navigate', 'up')
      break
    case 'Enter':
      event.preventDefault()
      if (props.selectedIndex >= 0 && props.selectedIndex < totalItems.value.length) {
        handleItemClick(totalItems.value[props.selectedIndex])
      }
      break
    case 'Escape':
      event.preventDefault()
      emit('close')
      break
  }
}

// 监听选中索引变化，自动滚动
watch(
  () => props.selectedIndex,
  () => {
    scrollToSelectedItem()
  },
)

// 监听 visible 属性变化
watch(
  () => props.visible,
  (newVal, oldVal) => {
    if (newVal) {
      nextTick(() => {
        document.addEventListener('keydown', handleKeydown)
      })
    } else {
      document.removeEventListener('keydown', handleKeydown)
    }
  },
)

// 组件卸载时清理事件监听器
onBeforeUnmount(() => {
  document.removeEventListener('keydown', handleKeydown)
})
</script>

<style lang="less" scoped>
.search-preview {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 1050;
  margin-top: 4px;

  .preview-container {
    background: #fff;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    max-height: 400px;
    overflow-y: auto;
  }

  .result-section {
    .section-header {
      display: flex;
      align-items: center;
      padding: 8px 16px;
      background: #fafafa;
      border-bottom: 1px solid #f0f0f0;
      font-size: 12px;
      color: #8c8c8c;
      font-weight: 500;

      i {
        margin-right: 6px;
        font-size: 14px;
      }

      .section-title {
        font-weight: 500;
      }
    }

    .result-list {
      .result-item {
        display: flex;
        align-items: center;
        padding: 12px 16px;
        cursor: pointer;
        transition: background-color 0.2s;
        border-bottom: 1px solid #f5f5f5;

        &:last-child {
          border-bottom: none;
        }

        &:hover,
        &.active {
          background: #f0f9ff;
        }

        .item-icon {
          margin-right: 12px;

          i {
            font-size: 16px;
            // color: #1890ff;
          }

          .db-icon {
            width: 24px;
            height: 24px;
            object-fit: contain;
          }
        }

        .item-content {
          flex: 1;
          min-width: 0;

          .item-title {
            font-size: 14px;
            color: #262626;
            font-weight: 500;
            margin-bottom: 2px;

            /deep/ .highlight {
              color: #1890ff;
              background: #e6f7ff;
              padding: 1px 2px;
              border-radius: 2px;
              font-weight: 600;
            }
          }

          .item-description {
            font-size: 12px;
            color: #8c8c8c;
            line-height: 1.4;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }
      }
    }
  }

  .loading-state {
    padding: 30px 20px;
    text-align: center;
    color: #8c8c8c;

    .loading-text {
      margin-top: 8px;
      font-size: 14px;
      color: #8c8c8c;
    }
  }

  .empty-state {
    padding: 40px 20px;
    text-align: center;
    color: #8c8c8c;

    i {
      font-size: 32px;
      color: #d9d9d9;
      margin-bottom: 12px;
    }

    .empty-text {
      font-size: 14px;
      color: #8c8c8c;
      margin-bottom: 4px;
    }

    .empty-suggestion {
      font-size: 12px;
      color: #bfbfbf;
    }
  }
}

// 滚动条样式
.preview-container {
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f5f5f5;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #d9d9d9;
    border-radius: 3px;

    &:hover {
      background: #bfbfbf;
    }
  }
}
</style>
