<template>
  <div class="tips">
    <p>
      {{ data.title }}
    </p>
    <div style="text-align: right">
      <a-button type="primary" @click="nextHandle">{{ data?.btnText ?? '下一步' }}</a-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

interface Tips {
  data: any
}
const props: Tips = withDefaults(defineProps<Tips>(), {})

function nextHandle() {
  props.data.callback()
}
</script>
<style scoped lang="less"></style>
