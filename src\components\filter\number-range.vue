<template>
  <a-space>
    <InputNumber v-model:value="data[0]" :min="min" :max="data[1]" placeholder="最小值" />
    <span>到</span>
    <InputNumber v-model:value="data[1]" :min="data[0]" :max="max" placeholder="最大值" />
  </a-space>
</template>

<script setup lang="ts">
import { InputNumber } from 'ant-design-vue'
const props = withDefaults(
  defineProps<{
    min?: number
    max?: number
  }>(),
  {
    min: 0,
    max: 9999,
  },
)
const data = defineModel<number[]>('value', { required: true })
</script>

<style scoped></style>
