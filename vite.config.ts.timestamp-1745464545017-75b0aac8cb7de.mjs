// vite.config.ts
import { fileURLToPath, URL } from 'node:url'
import { defineConfig } from 'file:///D:/1-work/data-platform-app/node_modules/vite/dist/node/index.js'
import vue from 'file:///D:/1-work/data-platform-app/node_modules/@vitejs/plugin-vue/dist/index.mjs'
import vueJsx from 'file:///D:/1-work/data-platform-app/node_modules/@vitejs/plugin-vue-jsx/dist/index.mjs'
import Components from 'file:///D:/1-work/data-platform-app/node_modules/unplugin-vue-components/dist/vite.js'
import { AntDesignVueResolver } from 'file:///D:/1-work/data-platform-app/node_modules/unplugin-vue-components/dist/resolvers.js'
import { codeInspectorPlugin } from 'file:///D:/1-work/data-platform-app/node_modules/code-inspector-plugin/dist/index.mjs'
import UnoCSS from 'file:///D:/1-work/data-platform-app/node_modules/unocss/dist/vite.mjs'
import AutoImport from 'file:///D:/1-work/data-platform-app/node_modules/unplugin-auto-import/dist/vite.js'
var __vite_injected_original_import_meta_url = 'file:///D:/1-work/data-platform-app/vite.config.ts'
var vite_config_default = defineConfig({
  plugins: [
    vue(),
    vueJsx(),
    UnoCSS(),
    AutoImport({
      imports: [
        'vue',
        // 自动导入 Vue 相关的 API
        'vue-router',
      ],
      dts: 'src/auto-imports.d.ts',
      // 生成自动导入的 TypeScript 声明文件
    }),
    Components({
      resolvers: [
        AntDesignVueResolver({
          importStyle: false,
          // css in js
        }),
      ],
    }),
    codeInspectorPlugin({
      bundler: 'vite',
    }),
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', __vite_injected_original_import_meta_url)),
    },
  },
  server: {
    host: '0.0.0.0',
    port: 5163,
    proxy: {
      '/server': {
        target: 'http://localhost:3001',
        changeOrigin: true,
      },
      '/api': {
        target: 'http://**************:12500',
        changeOrigin: true,
      },
      '/student': {
        target: 'http://**************:8112',
        changeOrigin: true,
      },
      '/v1': {
        target: 'http://************:7996',
        changeOrigin: true,
      },
      '/indexManage': {
        // target: 'http://**************:8112',
        target: 'http://*************:9091',
        changeOrigin: true,
      },
    },
  },
})
export { vite_config_default as default }
//# sourceMappingURL=data:application/json;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
