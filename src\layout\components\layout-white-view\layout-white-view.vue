<template>
  <a-config-provider v-bind="configProvider">
    <a-layout class="fs-layout">
      <a-layout-header class="layout-header">
        <slot name="logo">
          <div class="logo" @click="logoClick">
            <a-button type="text" @click="collapsed = !collapsed">
              <MenuOutlined />
            </a-button>
            <img v-if="logo" class="img" :src="logo" alt="logo" />
            <img v-else class="img" src="@/assets/logo.png" alt="logo" />
            <h1 class="tit">{{ title }}</h1>
          </div>
        </slot>

        <div class="header-bar">
          <slot name="header"></slot>
        </div>
      </a-layout-header>
      <a-layout class="layout-internal" v-show="!collapsed">
        <a-layout-sider
          v-if="!isSecondary"
          class="layout-sider"
          :trigger="null"
          collapsible
          :width="siderWidth"
        >
          <div class="section-sider">
            <a-menu
              class="menu scrollbar-style"
              mode="inline"
              :openKeys="menuOpenKeys"
              :selectedKeys="menuSelectedKeys"
              :items="userMenu"
              @click="handleClickMenuItem"
            ></a-menu>
            <div class="menu-slot">
              <slot name="menu-slot"></slot>
            </div>
            <!-- <div class="collapsed" v-if="collapsed">
              <VerticalAlignTopOutlined
                class="collapsed-icon"
                :class="{ close: collapsed }"
                @click="collapsed = !collapsed"
              />
            </div> -->
          </div>
        </a-layout-sider>
        <a-layout class="layout-section-right">
          <a-layout-content class="layout-content" :class="{ secondary: isSecondary }">
            <!-- <div v-if="!isSecondary && breadcrumb" class="breadcrumb">
              <AppBreadcrumb :breadcrumb="breadcrumbData" />
            </div> -->
            <slot name="content"></slot>
          </a-layout-content>
          <a-layout-footer v-if="$slots.footer" class="layout-footer">
            <slot name="footer">飞算 ©2024</slot>
          </a-layout-footer>
        </a-layout>
      </a-layout>
    </a-layout>
  </a-config-provider>
</template>

<script setup lang="ts">
import { ref, computed, h } from 'vue'
import { VerticalAlignTopOutlined, MenuOutlined } from '@ant-design/icons-vue'
import { type MenuProps, Modal } from 'ant-design-vue'
import type { MenuInfo } from 'ant-design-vue/es/menu/src/interface'
import type { LayoutProps, UserMenu, BreadcrumbItem } from './layout'

const props = withDefaults(defineProps<LayoutProps>(), {
  title: '',
  breadcrumb: true,
})

const emit = defineEmits<{
  clickMenuItem: [menuInfo: MenuInfo]
  logout: []
  logoClick: []
}>()

/**
 * 菜单
 */
const userMenu = computed(() => transformMenuData(props.menu))

/**
 * 菜单Map
 */
const userMenuMap = computed(() => {
  const obj: Record<string, UserMenu> = {}
  const buildIdMap = (items: UserMenu[]) => {
    items.forEach((item) => {
      obj[item.menuId] = item
      if (item.childrens?.length) {
        buildIdMap(item.childrens)
      }
    })
  }
  buildIdMap(props.menu)
  return obj
})

/**
 * 面包屑数据
 */
const breadcrumbData = computed(() => {
  const res: BreadcrumbItem[] = []
  props.menuSelectedKeys?.forEach((key) => {
    if (userMenuMap.value[key]) {
      const { menuUrl, menuName, menuId } = userMenuMap.value[key]
      res.push({
        title: menuName,
        path: menuUrl,
        id: menuId,
      })
    }
  })
  return res
})

const collapsed = ref(false)

const handleClickMenuItem: MenuProps['onClick'] = (e) => {
  emit('clickMenuItem', e)
}

const logoClick = () => {
  emit('logoClick')
}

/**
 * 退出登录
 */
function handleLogout() {
  Modal.confirm({
    title: '确定要退出登录吗？',
    cancelText: '取消',
    okText: '确定',
    onOk() {
      emit('logout')
    },
  })
}

/**
 * 转换菜单项
 */
function transformMenuData(data: UserMenu[] = []): UserMenu[] {
  return data.map((item) => {
    return {
      ...item,
      key: item.menuId,
      label: item.menuName,
      title: item.menuName,
      icon: item.menuIcon,
      children:
        Array.isArray(item.childrens) && item.childrens.length
          ? transformMenuData(item.childrens)
          : null,
    }
  })
}
</script>

<style scoped src="./layout-white-view.less"></style>
