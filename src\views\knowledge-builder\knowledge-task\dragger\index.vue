<template>
  <a-drawer
    v-model:open="open"
    class="custom-class"
    root-class-name="root-class-name"
    :bodyStyle="{ padding: 0 }"
    :headerStyle="{ borderBottom: 'none' }"
    :root-style="{ color: 'blue' }"
    width="70%"
    placement="right"
    :getContainer="false"
    :mask-closable="true"
    :maskStyle="{ background: 'transparent' }"
    @after-open-change="afterOpenChange"
    :push="false"
    destroyOnClose
    @close="titleValue = ShowDotPage.DESCRIPTION"
  >
    <template #title>
      <header class="title">{{ currentEchartsData?.nameZh }}</header>
    </template>
    <div class="radio-wrap">
      <a-radio-group class="title-select" v-model:value="titleValue">
        <a-radio-button :value="ShowDotPage.DESCRIPTION">描述</a-radio-button>
        <!-- <a-radio-button :value="ShowDotPage.PROP">属性</a-radio-button> -->
        <a-radio-button :value="ShowDotPage.RELATIONSHIP">关系</a-radio-button>
        <!-- <a-radio-button :value="ShowDotPage.SAMPLING">抽样</a-radio-button> -->
      </a-radio-group>
    </div>
    <!-- <description
      :currentEchartsData="currentEchartsData"
      v-if="titleValue === ShowDotPage.DESCRIPTION"
    />
    <prop v-if="titleValue === ShowDotPage.PROP" />
    <relationship v-if="titleValue === 2" :currentEchartsData="currentEchartsData" />
    <sampling v-if="titleValue === 3" /> -->
    <keep-alive>
      <component
        :is="pageMap[titleValue]"
        :currentEchartsData="currentEchartsData"
        :chartData="chartData"
      ></component>
    </keep-alive>
  </a-drawer>
</template>

<script lang="ts" setup>
import { ref, defineProps } from 'vue'
import sampling from './sampling.vue'
import description from './description.vue'
import prop from './prop.vue'
import relationship from './relationship.vue'
import { ShowDotPage } from '../../type'
const pageMap = {
  [ShowDotPage.DESCRIPTION]: description,
  [ShowDotPage.PROP]: prop,
  [ShowDotPage.RELATIONSHIP]: relationship,
  [ShowDotPage.SAMPLING]: sampling,
}
const props = defineProps<{
  currentEchartsData: any
  chartData: any
}>()
const open = ref<boolean>(false)
const titleValue = ref(ShowDotPage.DESCRIPTION)

const afterOpenChange = (bool: boolean) => {
  console.log('open', bool)
}

const showDrawer = () => {
  open.value = true
}

defineExpose({
  showDrawer,
})
</script>
<style scoped lang="less">
.root-class-name {
  /deep/ .ant-drawer-header {
    border-bottom: none;
  }
  .radio-wrap {
    padding: 0 24px 16px;
  }
}
.title {
}
</style>
