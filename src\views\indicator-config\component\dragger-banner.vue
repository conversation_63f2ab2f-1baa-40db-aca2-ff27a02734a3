<template>
  <div class="dragger-banner">
    <div class="banner-left" ref="leftRef">
      <slot name="left"></slot>
    </div>
    <div class="resize" ref="resizeDom" v-show="showRight">⋮</div>
    <div class="banner-right" ref="rightRef" v-show="showRight && !isCustomMode">
      <slot name="right"></slot>
    </div>
  </div>
</template>
<script setup lang="ts">
import { onBeforeUnmount, onMounted, ref, watch } from 'vue'

export type Layout = [number, number]

const props = withDefaults(
  defineProps<{
    layout?: Layout
    minLeftWidth?: number
    minRightWidth?: number
    showRight?: boolean
    isCustomMode?: boolean
  }>(),
  {
    layout: () => [2, 1],
    minLeftWidth: 0,
    minRightWidth: 0,
    showRight: true,
  },
)

const leftRef = ref<HTMLElement | null>(null)
const rightRef = ref<HTMLElement | null>(null)
const resizeDom = ref<HTMLElement | null>(null)

let isDragging = false
let startX = 0
let startLeftWidth = 0

const onMouseMove = (event: MouseEvent) => {
  if (!isDragging || !leftRef.value || !rightRef.value) return
  const deltaX = event.clientX - startX
  const newLeftWidth = startLeftWidth + deltaX
  const containerWidth = leftRef.value.parentElement?.getBoundingClientRect().width || 0
  const newRightWidth = containerWidth - newLeftWidth - resizeDom.value!.offsetWidth

  if (newLeftWidth >= props.minLeftWidth && newRightWidth >= props.minRightWidth) {
    leftRef.value.style.flex = `0 0 ${newLeftWidth}px`
    rightRef.value.style.flex = `1 1 ${newRightWidth}px`
  }
}

const onMouseUp = () => {
  isDragging = false
  document.removeEventListener('mousemove', onMouseMove)
  document.removeEventListener('mouseup', onMouseUp)
}

const onMouseDown = (event: MouseEvent) => {
  if (!leftRef.value) return
  isDragging = true
  startX = event.clientX
  startLeftWidth = leftRef.value.getBoundingClientRect().width
  document.addEventListener('mousemove', onMouseMove)
  document.addEventListener('mouseup', onMouseUp)
}

// 根据 layout 设置宽度
const setWidthByLayout = () => {
  if (!leftRef.value || !rightRef.value) return
  const containerWidth = leftRef.value.parentElement?.getBoundingClientRect().width || 0
  const resizeWidth = resizeDom.value?.offsetWidth || 0
  const totalRatio = props.layout![0] + props.layout![1]
  const leftWidth = (props.layout![0] / totalRatio) * (containerWidth - resizeWidth)
  const rightWidth = (props.layout![1] / totalRatio) * (containerWidth - resizeWidth)

  leftRef.value.style.flex = `0 0 ${leftWidth}px`
  rightRef.value.style.flex = `0 0 ${rightWidth}px`
}

onMounted(() => {
  if (props.showRight) {
    setWidthByLayout() // 初始化时根据 layout 设置宽度
  } else {
    if (leftRef.value) {
      leftRef.value.style.flex = '1 1 100%'
    }
  }
  if (resizeDom.value) {
    resizeDom.value.addEventListener('mousedown', onMouseDown)
  }
})

watch(
  () => props.showRight,
  (newValue) => {
    if (!newValue) {
      if (leftRef.value) {
        leftRef.value.style.flex = '1 1 100%'
      }
    } else {
      setWidthByLayout() // 显示右侧时根据 layout 设置宽度
      // if (
      //   leftRef.value &&
      //   leftRef.value.parentElement &&
      //   leftRef.value.parentElement.parentElement
      // ) {
      //   leftRef.value.parentElement.parentElement.scrollTo({ top: 0, behavior: 'smooth' })
      // }
    }
  },
)

// 添加窗口大小变化监听，保持宽度比例
window.addEventListener('resize', () => {
  if (props.showRight) {
    setWidthByLayout()
  }
})

onBeforeUnmount(() => {
  if (resizeDom.value) {
    resizeDom.value.removeEventListener('mousedown', onMouseDown)
  }
  window.removeEventListener('resize', setWidthByLayout)
})
</script>

<style scoped lang="less">
.dragger-banner {
  display: flex;
  width: 98%;
}
.banner-left {
  flex: 1 1 auto;
  min-width: 0;
}
.resize {
  cursor: ew-resize;
  flex-basis: 5px;
  display: flex;
  justify-content: center;
  align-items: center;
  user-select: none;
  border: 1px solid #ccc;
  transition: all 0.3s ease-in-out;
  color: #ccc;
  &:hover {
    flex-basis: 10px;
  }
}
.banner-right {
  flex: 1;
}
.right-panel {
  transform: translateX(100%);
  transition: transform 0.3s ease-in-out;
}
.right-panel.show {
  transform: translateX(0);
}
</style>
