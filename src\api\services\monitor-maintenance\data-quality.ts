import service from '@/api'
import type { Parameter } from '../data-asset/hbase-management'
import { SODATAFLINK } from '@/api/base'

// 规则管理DTO
export class ruleManageDTO {
  /* ID */
  id!: string

  /* 数据库类型 */
  dbType?: string

  /* 规则名 */
  name?: string

  /* 规则名 */
  ruleId?: string

  /* 查询sql */
  sql?: string

  /* 描述 */
  desc?: string

  /* 是否聚合 */
  isAggregation?: string
}

export class RuleVariableDTO {
  /* ID */
  id!: string

  /* 变量名 */
  varName?: string

  /* 默认值 */
  varDefaultValue?: string | Array<string>

  /* 描述 */
  varDesc?: string

  editable?: boolean;

  [key: string]: any
}

/* ----------------- 规则管理 ------------------- */

// 查询规则列表-分页
export function getRuleList(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/dataquality/qualiryparms/qualitisregulerdefPageList`,
    params,
  })
}

// 新增规则
export function addRule(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/dataquality/qualiryparms/qualitisregulerdefInsert`,
    method: 'post',
    data: params,
  })
}

// 删除规则
export function deleteRule(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/dataquality/qualiryparms/qualitisregulerdefDelete/${params.id}`,
    method: 'delete',
    data: params,
  })
}

// 修改规则
export function editRule(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/dataquality/qualiryparms/qualitisregulerdefUpdate/${params.id}`,
    method: 'put',
    data: params,
  })
}

// 查询规则列表
export function queryRuleList(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/dataquality/qualiryparms/qualitisregulerdefList`,
    method: 'get',
    params,
  })
}

// 按id查询规则
export function queryRuleBySQL(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/dataquality/qualiryparms/getVarNameParma`,
    method: 'post',
    data: params,
  })
}

// 测试规则
export function testRule(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/dataquality/qualiryparms/testQuality`,
    method: 'post',
    data: params,
  })
}

// 预览规则SQL
export function previewRuleSQL(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/dataquality/qualiryparms/previewSqlRule`,
    method: 'post',
    data: params,
  })
}

// 检测查询SQL变量名
export function checkVarName(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/dataquality/qualiryparms/checkVarName`,
    method: 'post',
    data: params,
  })
}

// 获取表列字段
export function getColumnField(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/dataquality/qualiryparms/getColumn`,
    method: 'post',
    data: params,
  })
}

/* ----------------- 规则变量 ------------------- */

// 查询变量列表-分页
export function getVariableList(params: Parameter) {
  return service({
    method: 'get',
    url: `${SODATAFLINK}/dataquality/qualiryparms/qualitisregulervarPageList`,
    params,
  })
}
// 查询全部变量列表
export function getAllVariableList(params: Parameter) {
  return service({
    method: 'get',
    url: `${SODATAFLINK}/dataquality/qualiryparms/qualitisregulervarList`,
    params,
  })
}

// 新增变量
export function addVariable(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/dataquality/qualiryparms/qualitisregulervarInsert`,
    method: 'post',
    data: params,
  })
}

// 删除变量
export function deleteVariable(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/dataquality/qualiryparms/qualitisregulervarDelete/${params.id}`,
    method: 'delete',
    data: params,
  })
}

// 修改变量
export function editVariable(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/dataquality/qualiryparms/qualitisregulervarUpdate/${params.id}`,
    method: 'put',
    data: params,
  })
}

/* ----------------- 规则查询 ------------------- */

// 规则查询-分页
export function ruleQueryList(params: Parameter) {
  return service({
    method: 'get',
    url: `${SODATAFLINK}/new2dataplatform/rule/schequalitisruleinfoPageList`,
    params,
  })
}

/* -------------------数据校验--------------------- */
//  校验任务列表
export function getRuleTaskList(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/flink_data_monitor/dqrulerelationPageList`,
    method: 'post',
    data: params,
  })
}

//  规则列表查询
export function getDqRuleList(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/flink_data_monitor/dqruleList`,
    method: 'post',
    data: params,
  })
}

//  保存规则任务
export function saveRuleTask(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/dataQuailty/saveRuleInfo`,
    method: 'post',
    data: params,
  })
}

//  运行规则任务
export function runRuleTask(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/dataQuailty/runRuleTask`,
    method: 'post',
    data: params,
  })
}

//  自动生成比对表格内容
export function createComparisonField(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/flink_data_monitor/returnComparisonFieldFun`,
    method: 'post',
    data: params,
  })
}

//  批次校验结果列表
export function getBatchCheckRusultList(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/flink_data_monitor/dqRuleInstancePageList`,
    method: 'post',
    data: params,
  })
}

//  实时表查询
export function getRealTimeQueryList(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/flink_data_monitor/realtimesyncmappingPageList`,
    method: 'post',
    data: params,
  })
}

//  实时表任务查询
export function getRealTimeTaskList(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/flink_data_monitor/dqtaskPageList`,
    method: 'post',
    data: params,
  })
}

//  实时表结果查询
export function getRealTimeResultList(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/flink_data_monitor/dqRuleStreamingPageList`,
    method: 'post',
    data: params,
  })
}

//  生成任务
export function ceateRealTimeTask(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/flink_data_monitor/mulGenRealTimeCheckTaskfun`,
    method: 'post',
    data: params,
  })
}

//  批量校验编辑
export function bachCheckTask(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/flink_data_monitor/bachCheckEditor`,
    method: 'post',
    data: params,
  })
}

//  批量删除
export function deleteRuleInfo(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/flink_data_monitor/deleteRuleInfoFun`,
    method: 'post',
    data: params,
  })
}

//  实时任务作业流
export function queryRealTimeFlow(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/flink_data_monitor/real_time_flow_monitor`,
    method: 'post',
    data: params,
  })
}

//  获取字段
export function getColumnInfo(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/flink_data_monitor/dataQuaityColumnInfoFun`,
    method: 'post',
    data: params,
  })
}

//  查询所有作业流
export function getAllRealTimeFlow(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/flink_data_monitor/queryRealTimeFlow`,
    method: 'post',
    data: params,
  })
}

//  新增实时作业流监控
export function addFlowRealTimeTask(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/dataQuailty/addCheckTask`,
    method: 'post',
    data: params,
  })
}

//  编辑定时器
export function realTimeUopdateCron(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/flink_data_monitor/realTimeUopdateCron`,
    method: 'post',
    data: params,
  })
}

//  取消任务监控
export function cancelBatch(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/dataQuailty/batchCancle`,
    method: 'post',
    data: params,
  })
}

//  批量删除批次校验结果
export function batchDeleteBatchTask(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/flink_data_monitor/delete_dq_rule_instance`,
    method: 'post',
    data: params,
  })
}

//  校验实时任务
export function mulGenCheckTaskByFlowIdFun(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/flink_data_monitor/mulGenCheckTaskByFlowIdFun`,
    method: 'post',
    data: params,
  })
}

//  批量取消
export function batchCancleTask(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/dataQuailty/cancleTask`,
    method: 'post',
    data: params,
  })
}

//  批量更新任务状态
export function updateTaskConfigs(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/dataQuailty/updateTaskConfigs`,
    method: 'post',
    data: params,
  })
}

//  批次校验任务结果历史记录
export function dqRuleInstancePageListHistory(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/flink_data_monitor/dqRuleInstancePageListHistory`,
    method: 'post',
    data: params,
  })
}
