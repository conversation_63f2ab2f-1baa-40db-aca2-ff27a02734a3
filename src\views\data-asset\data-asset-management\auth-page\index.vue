<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { reqDelDataList, reqGetAuthList } from './api'
import { columns, ActionType, type AuthItem } from './datas'
import { useRouter, useRoute } from 'vue-router'
import DlgModal from './dlg-edit.vue'
import { message } from 'ant-design-vue'
import { LeftOutlined } from '@ant-design/icons-vue'

const router = useRouter()
const route = useRoute()
const searchValue = ref<string>('')
const pageIndex = ref<number>(1)
const pageSize = ref<number>(10)
const totalRecords = ref<number>(0)
const authList = ref<AuthItem[]>([])
const dlgModal = ref<any>(null)
const actionType = ref<ActionType>(ActionType.ADD)
const tableLoading = ref<boolean>(false)

onMounted(() => {
  getAuthList()
  console.log(router)
})

const goBack = () => {
  router.back()
}

const getAuthList = async (userName?: string) => {
  tableLoading.value = true
  const tableId = route.params.authId
  const params = {
    userName: userName,
    tableId,
    pageIndex: pageIndex.value,
    pageSize: pageSize.value,
  }
  const res = await reqGetAuthList(params).finally(() => {
    tableLoading.value = false
  })
  authList.value = res.data.records
  totalRecords.value = res.data.totalRecords
  console.log('%c [  ]-8', 'font-size:13px; background:#33668d; color:#77aad1;', res)
}

const onDelete = async (key: string) => {
  console.log(`Deleting record with key: ${key}`)
  await reqDelDataList(key)
  await getAuthList()
  message.success('删除成功')
}

const handleModify = (record: AuthItem) => {
  console.log('Modify record:', record)
  actionType.value = ActionType.EDIT
  dlgModal.value.showModal(record)
  // Logic to handle modification of the record
}

const handleUser = (action: ActionType) => {
  actionType.value = action
  dlgModal.value.showModal()
}

const handleSearch = (e: any) => {
  pageIndex.value = 1
  const userName = e.target.value
  getAuthList(userName)
}

const handleChange = (e: any) => {
  if (e.target.value === '') {
    pageIndex.value = 1
    getAuthList()
  }
}

const pagination = computed(() => ({
  current: pageIndex.value,
  pageSize: pageSize.value,
  total: totalRecords.value,
  showSizeChanger: true,
  showQuickJumper: true,
  size: 'small',
  showTotal: (total: number) => `共 ${total} 条`,
  onChange: (currentPage: number, currentPageSize: number) => {
    pageIndex.value = currentPage
    pageSize.value = currentPageSize
    getAuthList(searchValue.value)
  },
}))
</script>

<template>
  <div class="auth-page">
    <header>
      <div class="title-left">
        <p>权限列表</p>
        <div class="desc">当前表名：{{ route.query?.tableName ?? '-' }}</div>
      </div>
      <div class="title-right">
        <a-button type="link" @click="goBack"><LeftOutlined />返回</a-button>
        <a-button type="primary" @click="handleUser(ActionType.ADD)">新增用户</a-button>
      </div>
    </header>
    <a-input
      v-model:value="searchValue"
      placeholder="请输入搜索内容"
      allow-clear
      style="max-width: 500px"
      @pressEnter="handleSearch"
      @change="handleChange"
    />
    <a-table
      :loading="tableLoading"
      :dataSource="authList"
      :columns="columns"
      class="auth-table"
      :pagination="pagination"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'operation'">
          <a @click.prevent="handleModify(record)">修改</a>
          <a-popconfirm
            v-if="authList.length"
            title="确认要删除吗?"
            @confirm="onDelete(record.permissionId)"
          >
            <a style="color: #f00; margin-left: 8px">删除</a>
          </a-popconfirm>
        </template>
      </template>
    </a-table>
    <DlgModal ref="dlgModal" :actionType="actionType" :getAuthList="getAuthList" />
  </div>
</template>

<style lang="less" scoped>
.auth-page {
  padding: 24px;
  background: white;
  height: 100%;
  & > header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
  }
  .title-left {
    > p {
      font-size: 16px;
      color: #292929;
      font-weight: 600;
      line-height: 1.5;
      margin-bottom: 4px;
    }
    .desc {
      color: rgba(0, 0, 0, 0.65);
    }
  }
  .auth-table {
    margin-top: 16px;
    /deep/ .ant-table {
      border: 1px solid #e8e8e8;
    }
  }
}
</style>
