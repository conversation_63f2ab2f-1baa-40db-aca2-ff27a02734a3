<script setup lang="ts">
import { type KernelInfo } from '@/api/services/kernel-allocation'
import type { TableColumnType } from 'ant-design-vue'
import type { Key } from 'ant-design-vue/es/table/interface'

defineOptions({
  name: 'KernelTable',
})

const tableData = defineModel<KernelInfo[]>('tableData', { required: true })
const columns = ref<TableColumnType[]>([
  {
    title: '内核名称',
    dataIndex: 'displayName',
    key: 'displayName',
  },
  {
    title: '描述',
    dataIndex: 'description',
    key: 'description',
  },
])
const selectedKeys = ref<Key[]>([])
watch(
  tableData,
  (newVal) => {
    selectedKeys.value = newVal.filter((item) => item.allowFlag).map((item) => item.autoId)
  },
  {
    immediate: true,
  },
)
const rowSelection = computed(() => ({
  selectedRowKeys: selectedKeys.value,
  onChange: (selectedRowKeys: Key[]) => {
    selectedKeys.value = selectedRowKeys
    updateTableData()
  },
}))

const updateTableData = () => {
  const newTableData = tableData.value.map((item) => {
    if (selectedKeys.value.includes(item.autoId)) {
      return { ...item, allowFlag: true }
    }
    return { ...item, allowFlag: false }
  })
  tableData.value = newTableData
}
</script>

<template>
  <div>
    <a-table
      :data-source="tableData"
      :columns="columns"
      row-key="autoId"
      :row-selection="rowSelection"
    />
  </div>
</template>

<style scoped></style>
