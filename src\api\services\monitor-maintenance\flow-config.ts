import service from '@/api'
import type { Parameter } from '../data-asset/hbase-management'
import { SODATAFLINK } from '@/api/base'
// 工作流配置DTO
export class jobFlowConfigDTO {
  /* ID */
  id!: string

  /* 版本号 */
  versionId?: string

  /* 流程名称 */
  name?: string

  /* 所有者 */
  ownerId?: string

  /* 流程描述 */
  description?: string

  /* 流程定义json */
  flowDefJson?: string

  /* 全局参数 */
  globalParams?: string

  /* 流程执行开始时间 */
  startTime?: string

  /* 执行频率表达式 */
  freExpression?: string

  /* 超时单位 */
  timeout?: string

  /* 最大失败重试次数 */
  maxRetryNum?: string

  /* 是否生效 */
  isEnabled?: string

  /* 创建时间 */
  createTime?: string

  /* 更新时间 */
  updateTime?: string

  /* 工作节点 */
  workerId?: string

  /* 编辑模式 */
  editMode?: number
}

// 工作流实例DTO
export class JobFlowInstanceDTO {
  /* 流程实例id */
  id!: number

  /* 流程实例名 */
  name?: string

  /* 流程配置ID */
  flowDefId?: string

  /* 流程定义json */
  flowDefJson?: string

  /* 任务状态 */
  taskStatus?: number

  /* 作业流ID集合 */
  sonFlow?: string

  /* 流程开始时间 */
  startTime?: string

  /* 流程结束时间 */
  endTime?: string

  /* 创建时间 */
  createTime?: string

  /* 更新时间 */
  updateTime?: string

  /* 编辑模式 */
  editMode?: number

  /* 作业流 */
  children?: Array<JobFlowInstanceDTO>
}

// 工作流服务DTO
export class jobFlowServiceDTO {
  /* ID */
  id!: string

  /* 服务类型 */
  serviceType?: number

  /* IP地址 */
  serviceIp?: string

  /* 端口 */
  servicePort?: number

  /* 线程 */
  threadNum?: number

  /* 用户名 */
  hostUser?: string

  /* 密码 */
  hostPwd?: string

  /* 部署路径 */
  hostPath?: string

  /* 数据文件路径 */
  dataPath?: string

  /* 执行文件路径 */
  execPath?: string

  /* 日志文件路径 */
  logPath?: string

  /* 创建时间 */
  createTime?: string

  /* 更新时间 */
  updateTime?: string

  /** 当前行是否编辑 */
  editable?: boolean
}

// 任务实例DTO
export class taskInstanceDTO {
  /* 主键 */
  id?: number
  /* 任务描述 */
  description?: string
  /* 任务名 */
  name?: string
  /* 任务类型 */
  taskType?: string
  /* 流程实例id */
  flowInstanceId?: number
  /* 入参 */
  inputParams?: string
  /* 出参 */
  outputParams?: string
  /* 任务状态 */
  state?: string
  /* 任务提交时间 */
  submitTime?: string
  /* 任务开始时间 */
  startTime?: string
  /* 任务结束时间 */
  endTime?: string
  /* 执行节点 */
  host?: string
  /* 执行路径 */
  executePath?: string
  /* 日志路径 */
  logPath?: string
  /* 已重试次数 */
  retryTimes?: string
  /* 最大重试次数 */
  maxRetryTimes?: string
}

export class projectDTO {
  id!: string
  name!: string
  description?: string
  ownerId?: string
}

export class relationDTO {
  sourceId!: string
  targetId!: string
  relationId!: string
}

/* ----------------- 工作流配置 ------------------- */

// 查询工作流配置列表
export function getJobFlowConfigList(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/new2dataplatform/scheflowdefPageList`,
    method: 'get',
    params,
  })
}

// 查询工作流配置列表-根据项目ID
export function getJobFlowConfigListByProjectId(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/new2dataplatform/projectAndFlow/scheFlowRelation/getFlowDefByProjectPageList`,
    method: 'get',
    params,
  })
}

// 获取工作流配置下的作业流
export function getConfigJobFlow(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/new2dataplatform/nodedef/getsonFlow`,
    method: 'get',
    params,
  })
}

// 查询工作流配置
export function getJobFlowConfigById(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/new2dataplatform/scheflowdefSingleInfo/${params.id}`,
    method: 'get',
    params,
  })
}

// 删除工作流配置
export function deleteJobFlowConfig(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/new2dataplatform/scheflowdefDelete/${params.id}`,
    method: 'delete',
    data: params,
  })
}

// 新增工作流配置
export function addJobFlowConfig(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/new2dataplatform/scheflowdefInsert`,
    method: 'post',
    data: params,
  })
}

// 批量运行作业流
export function mulRunFlow(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/run/mulRunFlowFun`,
    method: 'post',
    data: params,
  })
}

// 批量跳过作业流
export function mulSkipComp(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/rerun/mulSkipCompFun`,
    method: 'post',
    data: params,
  })
}

// 修改工作流配置
export function editJobFlowConfig(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/new2dataplatform/scheflowdefUpdate/${params.id}`,
    method: 'put',
    data: params,
  })
}

// 编辑作业流
export function editJobFlow(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/new2dataplatform/nodedef/savaflow/${params.id}`,
    method: 'put',
    data: params,
  })
}

// 运行作业流
export function runJobFlow(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/new2dataplatform/nodedef/flowrun/${params.id}`,
    method: 'put',
    data: params,
  })
}

// 发布作业流版本
export function releaseVersion(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/new2dataplatform/flowjsonversion/scheflowjsonversionInsert`,
    method: 'post',
    data: params,
  })
}

// 作业流配置生成任务
export function generateTask(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/scheduler/generateFlowInstance`,
    method: 'post',
    data: params,
  })
}

// 获取作业流配置版本列表
export function getJobFlowVersionListById(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/new2dataplatform/flowjsonversion/flowidgetversionIdList`,
    method: 'get',
    params,
  })
}

// 按版本获取作业流图
export function getJobFlowConfigByVersion(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/new2dataplatform/flowjsonversion/scheflowjsonversionList`,
    method: 'get',
    params,
  })
}

/**
 * 测试sql
 * @param params
 */

export function testSql(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/redefine/sqltest`,
    method: 'post',
    data: params,
  })
}

/**
 * 新测试sql
 * @param params
 */

export function newTestSql(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/redefine/sqltest2`,
    method: 'post',
    data: params,
  })
}

/**
 * 测试Spark-Sql
 * @param params
 */
export function testSparkSql(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/config/TransformRule/testSpark`,
    method: 'post',
    data: params,
  })
}

/**
 * 测试Spark-Sql
 * @param params
 */
export function testFlinkSql(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/flink/FlinkTable/testSpark`,
    method: 'post',
    data: params,
  })
}

/**
 * 获取Spark-Sql目标表列
 * @param params
 */
export function getTableColumn(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/config/SparkTableManager/testSparkvaluetype`,
    method: 'post',
    data: params,
  })
}

/**
 * 获取flink-Sql目标表列
 * @param params
 */
export function getTableColumnToFlink(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/flink/FlinkTable/testSparkvaluetype`,
    method: 'post',
    data: params,
  })
}

/**
 * spark检查字典表
 * @param params
 */
export function sparkCheckDictTable(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/config/SparkTableManager/checkDicIsExist`,
    method: 'post',
    data: params,
    extraParams: {
      showLoading: false,
    },
  })
}

/**
 * spark加载字典表
 * @param params
 */
export function sparkLoadDictTable(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/config/SparkTableManager/loadDicTable`,
    method: 'post',
    data: params,
    extraParams: {
      showLoading: false,
    },
  })
}

/**
 * flink检查字典表
 * @param params
 */
export function flinkCheckDictTable(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/flink/FlinkTable/checkDicIsExist`,
    method: 'post',
    data: params,
    extraParams: {
      showLoading: false,
    },
  })
}

/**
 * flink加载字典表
 * @param params
 */
export function flinkLoadDictTable(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/flink/FlinkTable/loadDicTable`,
    method: 'post',
    data: params,
    extraParams: {
      showLoading: false,
    },
  })
}

/**
 * 获取批量生成批量同步组件节点数据
 * @param params
 */
export function getBatchCreateDbImportData(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/new2dataplatform/nodedef/generateDBMulTask`,
    method: 'post',
    data: params,
  })
}

/**
 * @desc 获取工作流配置关系
 * @param params
 */
export function getJobFlowConfigRelation(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/new2dataplatform/projectAndFlow/scheFlowRelation/projectFlowRelationView`,
    method: 'post',
    data: params,
  })
}

/**
 * @desc 获取工作流实例关系
 * @param params
 */
export function getJobFlowInstanceRelation(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/new2dataplatform/projectAndFlow/scheFlowRelation/projectInstanceFlowRelationView`,
    method: 'post',
    data: params,
  })
}

/**
 * @desc 导出工作流配置
 * @param params
 */
export function exportJobFlow(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/new2dataplatform/flow/export`,
    method: 'post',
    data: params,
  })
}

/**
 * @desc 导入工作流配置
 * @param params
 */
export function importJobFlow(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/new2dataplatform/flow/import`,
    method: 'post',
    data: params,
  })
}

/**
 * @desc 复制工作流配置
 * @param params
 */
export function batchDeletionJobFlow(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/new2dataplatform/flow/mulFlowDelete`,
    method: 'post',
    data: params,
  })
}

/**
 * @desc 复制工作流配置
 * @param params
 */
export function copyJobFlow(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/rerun/cppyFlow`,
    method: 'post',
    data: params,
  })
}

/* ----------------- 工作流实例 ------------------- */

// 查询工作流实例列表
export function getJobFlowInstanceList(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/new2dataplatform/scheflowinstancePageList`,
    method: 'get',
    params,
  })
}

// 查询工作流实例列表-根据项目ID
export function getJobFlowInstanceListByProjectId(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/new2dataplatform/projectAndFlow/getInstanceByIdPageList`,
    method: 'get',
    params,
  })
}

// 删除工作流实例
export function deleteJobFlowInstance(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/new2dataplatform/scheflowinstanceDelete`,
    method: 'delete',
    data: params,
  })
}

// 删除历史更多
export function deleteJobFlowInstanceHistory(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/backrollSaveInstanceInfo/scheflowinstancehistoryDelete`,
    method: 'delete',
    data: params,
  })
}

// 删除重跑工作流实例
export function deleteResetDetails(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/backrollSaveInstanceInfo/scheflowinstancefollowerDelete`,
    method: 'post',
    data: params,
  })
}

// 根据ID获取工作流实例
export function getJobFlowInstanceById(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/scheduler/drawDagWithTaskState`,
    method: 'get',
    params,
  })
}

// 根据ID获取历史工作流实例
export function getHistoryJobFlowInstanceById(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/backrollSaveInstanceInfo/getDrawWithTaskStatusHstoryFun`,
    method: 'post',
    data: params,
  })
}

// 根据ID获取历史工作流实例
export function getResetFlowInstance(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/backrollSaveInstanceInfo/getDrawWithTaskStatusFollowerFun`,
    method: 'post',
    data: params,
  })
}

/* ----------------- 工作流服务配置 ------------------- */

// 查询工作流服务配置列表
export function getJobFlowServiceList(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/new2dataplatform/scheserviceinfoPageList`,
    method: 'get',
    params,
  })
}

// 新增工作流服务配置
export function addJobFlowService(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/new2dataplatform/scheserviceinfoInsert`,
    method: 'post',
    data: params,
  })
}

// 删除工作流服务配置
export function deleteJobFlowService(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/new2dataplatform/scheserviceinfoDelete`,
    method: 'post',
    data: params,
  })
}

// 修改工作流服务配置
export function editJobFlowService(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/new2dataplatform/scheserviceinfoUpdate/${params.id}`,
    method: 'put',
    data: params,
  })
  // return service({
  //   url: `${SODATAFLINK}/new2dataplatform/scheserviceinfoInsert`,
  //   method: 'post',
  //   data: params
  // })
}

// 获取作业流使用库表信息
export function getJobFlowLibraryTableInfo(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/redefine/queryTabDbInFlow`,
    method: 'get',
    params,
  })
}

/* ----------------- 任务实例 ------------------- */

// 查询任务实例列表
export function getTaskInstanceList(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/new2dataplatform/schetaskinstancePageList`,
    method: 'get',
    params,
  })
}

// 查询任务实例日志信息
export function getTaskLogInfo(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/new2dataplatform/scheComponent/getlog`,
    method: 'get',
    params,
  })
}

/**
 * 流实例状态更新为跳过
 * @param params
 * @returns
 */
export function flowInstanceUpdateState(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/new2dataplatform/flow/flowInstanceUpdateState`,
    method: 'put',
    data: params,
  })
}

/**
 * 任务实例状态更新为跳过
 * @param params
 * @returns
 */
export function taskInstanceUpdateState(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/new2dataplatform/flow/taskInstanceUpdateState/${params.id}`,
    method: 'put',
    data: params,
  })
}

/**
 * 实例任务重跑
 * @param params
 * @returns
 */
export function taskInstanceRerun(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/rerun/backroll`,
    method: 'post',
    data: params,
  })
}

/**
 * 实例任务取消
 * @param params
 * @returns
 */
export function taskInstanceCancel(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/rerun/cancelTaskFun`,
    method: 'post',
    data: params,
  })
}

/**
 * 实例任务获取参数
 * @param params
 * @returns
 */
export function getTaskInstanceParams(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/new2dataplatform/schetaskinstanceSingleInfo/${params.id}`,
    method: 'get',
    params,
  })
}

/**
 * 实例重跑
 * @param params
 * @returns
 */
export function rerunInstance(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/rerun/batchRollBack`,
    method: 'post',
    data: params,
  })
}

/**
 * job详情
 * @param params
 * @returns
 */
export function jobDetail(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/flink/resourceManager/realtimeTaskInstanceToFlinkCluster`,
    method: 'post',
    data: params,
  })
}

/**
 * 实例重跑失败
 * @param params
 * @returns
 */
export function rerunFailInstance(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/rerun/cancelFlowInstance`,
    method: 'post',
    data: params,
  })
}

/**
 * 模拟数据
 * @param params
 * @returns
 */
export function mockData(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/SyncCompensstion/queryServerCdcTableFun`,
    method: 'post',
    data: params,
  })
}

/**
 * 查询实例历史列表
 * @param params
 * @returns
 */
export function getInstanceHistoryList(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/flink/RuningInstanceHistoryQuery/scheflowinstancePageList`,
    method: 'get',
    params,
  })
}

/**
 * 查询更多实例详情
 * @param params
 * @returns
 */
export function getInstanceHistoryListDetails(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/backrollSaveInstanceInfo/schetaskinstancehistoryPageList`,
    method: 'get',
    params,
  })
}

/**
 * 查询更多实例历史列表
 * @param params
 * @returns
 */
export function getInstanceHistoryListAll(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/backrollSaveInstanceInfo/scheflowinstancehistoryPageList`,
    method: 'get',
    params,
  })
}

/**
 * 查询重跑任务列表
 * @param params
 * @returns
 */
export function getResetTaskList(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/backrollSaveInstanceInfo/scheflowinstancefollowerPageList`,
    method: 'get',
    params,
  })
}

/**
 * 查询重跑详情列表
 * @param params
 * @returns
 */
export function getResetDetailsList(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/backrollSaveInstanceInfo/schetaskinstancefollowerPageList`,
    method: 'get',
    params,
  })
}

/* ----------------- 运行组件 ------------------- */
/**
 * 运行组件
 * @param params
 * @returns
 */
export function getRunComponent(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/flink/taskStateQuery/schetaskinstanceList`,
    method: 'get',
    params,
  })
}

/* -----------------实时表管理--------------------- */
/**
 * 获取实时表管理接口
 * @param params
 * @returns
 */
export function getRealTimeTableList(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/realtimeTableManager/realtimesyncmappingPageList`,
    method: 'post',
    data: params,
  })
}

/* ----------------- 全量初始化 ------------------- */
/**
 * 获取全量初始化数据列表
 * @param params
 * @returns
 */
export function getfullCompensationList(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/SyncCompensstion/realtimesyncmapping`,
    method: 'post',
    data: params,
  })
}

/**
 * 运行全量初始化
 * @param params
 * @returns
 */
export function runFullCompenstion(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/SyncCompensstion/runFullCompenstion`,
    method: 'post',
    data: params,
  })
}

/**
 * 查询全量初始化作业流
 * @param params
 * @returns
 */
export function getFullCompenstionJobFlowList(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/SyncCompensstion/getAvailableFullCompenstionList`,
    method: 'post',
    data: params,
  })
}

/**
 * 根据列表ID获取全量初始化切分字段
 * @param params
 * @returns
 */
export function getSplitFieldListById(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/SyncCompensstion/querySrcColumn`,
    method: 'post',
    data: params,
    extraParams: {
      showLoading: false,
    },
  })
}

/**
 * 批量保存全量初始化
 * @param params
 * @returns
 */
export function saveFullCompenstion(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/SyncCompensstion/updateRealTimeSyncMappingFun`,
    method: 'post',
    data: params,
  })
}

/* ----------------- 全量初始化任务 ------------------- */
/**
 * 查询全量初始化任务
 * @param params
 * @returns
 */
export function getfullCompensationTaskList(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/SyncCompensstion/realsyncfullcompenstionPageList`,
    method: 'get',
    params,
  })
}

/**
 * 历史实例耗时
 * @param params
 * @returns
 */
export function getHistoryTimeChartData(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/backrollSaveInstanceInfo/historicalTaskTrendChartFun`,
    method: 'post',
    data: params,
  })
}
