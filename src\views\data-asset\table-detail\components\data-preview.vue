<template>
  <div class="data-preview">
    <a-row justify="start" align="top" type="flex" :wrap="true" class="data-preview-row">
      <a-col :span="24" class="data-preview-col">
        <div class="content">
          <a-alert
            v-show="errorMsg !== null"
            message="无法预览数据"
            :description="errorMsg"
            type="error"
          />

          <!-- 数据预览内容 -->
          <div class="data-preview-table">
            <a-table
              v-show="errorMsg === null"
              class="data-table"
              :dataSource="tableDataSource"
              :columns="columns"
              :pagination="false"
              tableLayout="fixed"
              :scroll="{ y: 'calc(100vh - 350px)' }"
            />
          </div>
          <a-pagination
            class="pagination"
            v-show="errorMsg === null"
            :current="pagination.current"
            :total="pagination.total"
            :page-size="pagination.pageSize"
            :show-size-changer="true"
            :show-total="(total: number) => `共 ${total} 条`"
            :page-size-options="[10, 20, 30, 40]"
            @change="onPageChange"
            @showSizeChange="onShowSizeChange"
          />
        </div>
      </a-col>
    </a-row>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { useRoute } from 'vue-router'
import { previewData } from '@/api/assetmanager/dataassertmanage/dataassertmanage'

const route = useRoute()
type ElementType<T extends Iterable<any>> = T extends Iterable<infer E> ? E : never
type ResponseData = Awaited<ReturnType<typeof previewData>>['data']
type DataType = Pick<ElementType<ResponseData>, 'columnList'> & {
  totalSize: number
  dataList?: Array<Record<string, any>>
}

const responseDataRef = ref<DataType>()
const columns = computed(
  () =>
    responseDataRef.value?.columnList.map((column) => ({
      title: column.columnComment || column.columnName,
      dataIndex: column.columnName,
      key: column.columnName,
      ellipsis: true,
      customHeaderCell() {
        return {
          style: {
            width: 'auto',
            maxWidth: '200px',
            overflow: 'hidden',
          },
        }
      },
    })) ?? [],
)
const tableDataSource = computed(() => responseDataRef.value?.dataList ?? [])

const errorMsg = ref<string | null>(null)

const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 0,
})
const fetchTableDetail = async (tableId: string, pageIndex = 1, pageSize = 10) => {
  try {
    const response = await previewData({ tableId, pageIndex, pageSize }, {})
    const data = response.data as unknown as DataType
    pagination.value.total = data.totalSize ?? 0
    responseDataRef.value = data

    const dataList = data.dataList ?? []
    if (dataList.length === 1 && Reflect.has(dataList[0], 'msg')) {
      errorMsg.value = dataList[0].msg as string
    } else {
      errorMsg.value = null
    }
    console
  } catch (error) {
    console.error('Error fetching table detail:', error)
  }
}

const loadingRef = ref<boolean>(false)
const onPageChange = async (page: number, pageSize: number) => {
  loadingRef.value = true
  pagination.value.current = page
  pagination.value.pageSize = pageSize

  try {
    await fetchTableDetail(
      route.query.tableId as string,
      pagination.value.current,
      pagination.value.pageSize,
    )
  } catch (error) {
    console.error('Error fetching previous page:', error)
  } finally {
    loadingRef.value = false
  }
}

const onShowSizeChange = async (_current: number, pageSize: number) => {
  pagination.value.current = 1
  pagination.value.pageSize = pageSize
  try {
    await fetchTableDetail(route.query.tableId as string, 1, pageSize)
  } catch (error) {
    console.error('Error fetching data with new page size:', error)
  }
}

onMounted(() => {
  const tableId = route.query.tableId as string
  fetchTableDetail(tableId, pagination.value.current, pagination.value.pageSize)
})
</script>

<style lang="less" scoped>
.data-preview {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.pagination {
  margin: 10px 0;
}

.data-table :deep(table) {
  min-width: 100%;
  width: auto;
}

.data-preview-table {
  width: 100%;
  height: auto;
  overflow-x: auto;
}
</style>
