<template>
  <div class="sort-filter">
    <a-popover trigger="click" v-model:open="visible" @openChange="handleVisibleChange">
      <template #content>
        <div class="popover-content">
          <div class="filter-main">
            <a-input v-model:value="filterValue" placeholder="查找..." allow-clear>
              <template #prefix>
                <SearchOutlined />
              </template>
            </a-input>
            <a-divider style="margin: 10px 0"></a-divider>
            <div class="data-source">
              <div class="data-source-title">
                <DatabaseOutlined />
                <span style="margin-top: -4px; margin-left: 10px">{{ tableName }}</span>
              </div>
              <data-columns :data="filterColumns" @handleClick="handleClick($event)"></data-columns>
            </div>
          </div>
        </div>
      </template>
      <div class="sort-action">
        <slot>
          <PlusOutlined style="font-size: 22px" />
        </slot>
      </div>
    </a-popover>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { SearchOutlined, DatabaseOutlined, PlusOutlined } from '@ant-design/icons-vue'
import type { dataColumn } from '../data-columns/type'

const props = defineProps<{
  tableName: string
  columns: dataColumn[]
}>()
const emits = defineEmits(['handleClick'])

const visible = ref(false)

const handleVisibleChange = (status: boolean) => {
  visible.value = status
}

const filterValue = ref('')
const filterColumns = computed(() => {
  return filterValue.value
    ? props.columns.filter((column) => column.title.includes(filterValue.value))
    : props.columns
})

function handleClick(e: dataColumn) {
  visible.value = false
  emits('handleClick', {
    tableName: props.tableName,
    fieldName: e.title,
    sort: 'desc',
  })
}
</script>

<style lang="less" scoped>
.sort-action {
  color: #fff;
  padding: 10px;
  cursor: pointer;
  background-color: rgba(147, 161, 171, 0.8);

  border-radius: 6px;
  display: flex;
  -webkit-box-align: center;
  align-items: center;
}
.data-source {
  &-title {
    display: flex;
    align-items: center;

    font-size: 20px;
    font-weight: bold;
  }
}
</style>
