<template>
  <a-modal
    v-model:visible="visible"
    title="分配规则模型"
    @ok="handleOk"
    @cancel="handleCancel"
    :width="800"
    :confirmLoading="confirmLoading"
  >
    <a-table
      :columns="columns"
      :data-source="dataSource"
      :row-selection="{
        selectedRowKeys: selectedKeys,
        onChange: rowSelectionOnChange,
        type: 'checkbox',
      }"
      :pagination="false"
      style="margin-top: 10px"
      rowKey="autoId"
      v-if="visible2"
    >
    </a-table>
  </a-modal>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import type { TableColumnType } from 'ant-design-vue'
import { getProjectRole, updateByRole } from '@/views/rule-engine/service'
import { message } from 'ant-design-vue'

interface DataItem {
  key: string
  name: string
  description: string
}

const visible = ref(false)
const visible2 = ref(false)
const selectedRows = ref<DataItem[]>([])
const confirmLoading = ref(false)
const roleData = ref<any>({})

// 表格列定义
const columns: TableColumnType[] = [
  {
    title: '名称',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '包名',
    dataIndex: 'packageName',
    key: 'packageName',
  },
  {
    title: '标签',
    dataIndex: 'tag',
    key: 'tag',
  },
  {
    title: '描述',
    dataIndex: 'description',
    key: 'description',
  },
]

// 模拟数据
const dataSource = ref<DataItem[]>([])
const selectedKeys = ref<any[]>([])

// 复选框配置
const rowSelectionOnChange = (selectedRowKeys: string[], selected: DataItem[]) => {
  selectedRows.value = selected
  console.log('selectedRowKeys:', selectedRowKeys)
  console.log('selected:', selected)
  selectedKeys.value = selectedRowKeys
}

// 打开modal的方法
const showModal = async (record: any) => {
  console.log('🚀 ~ showModal ~ record:', record)
  visible.value = true
  roleData.value = record
  visible2.value = false
  try {
    // getSelectedProject(record.autoId)
    const res = await getProjectRole({ roleId: record.roleId })
    selectedKeys.value = res.data
      .filter((item: any) => item.allowFlag)
      .map((item: any) => item.autoId)

    visible2.value = true
    dataSource.value = res.data || []
  } catch (error) {
    console.error('获取项目列表时发生错误:', error)
  }
}

// 确认按钮处理函数
const handleOk = async () => {
  // 设置loading状态
  confirmLoading.value = true

  try {
    dataSource.value.forEach((item: any) => {
      const val = selectedRows.value.find((row: any) => row.autoId === item.autoId)
      if (val) {
        item.allowFlag = true
      } else {
        item.allowFlag = false
      }
    })

    // 这里可以处理选中的数据
    console.log('确认选择的数据:', selectedRows.value)
    await updateByRole({ roleId: roleData.value.roleId, roleProjects: dataSource.value })
    message.success('操作成功')
    visible.value = false
    confirmLoading.value = false
  } catch (error) {
    console.error('处理数据时发生错误:', error)
    confirmLoading.value = false
  }
}

// 取消按钮处理函数
const handleCancel = () => {
  visible.value = false
  selectedRows.value = []
}

// 导出方法供父组件使用
defineExpose({
  showModal,
})
</script>

<style scoped></style>
