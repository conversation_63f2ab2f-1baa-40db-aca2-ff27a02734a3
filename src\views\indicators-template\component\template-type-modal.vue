<template>
  <a-modal
    v-model:visible="businessTypeModalVisible"
    title="业务类型"
    width="50%"
    :footer="null"
    @ok="handleOk"
    @cancel="handleOk"
  >
    <templateType></templateType>
  </a-modal>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import templateType from './template-type.vue'

const emit = defineEmits(['handleOk'])

const businessTypeModalVisible = ref(false)
const businessType = ref('')

function showModal(value: any) {
  businessTypeModalVisible.value = true
  businessType.value = value
}

function updateBusinessType(value: any) {
  businessType.value = value
}

function handleOk() {
  emit('handleOk', businessType.value)
  businessTypeModalVisible.value = false
}

defineExpose({
  showModal,
})
</script>

<style scoped></style>
