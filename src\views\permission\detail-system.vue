<template>
  <main class="detail-system">
    <Container>
      <template #headContent>
        <div class="left-box">
          <a-button type="primary" class="btn" @click="addMenuHandle">新增根菜单</a-button>
          <a-button type="primary" class="btn" @click="leadMenuHandle">导入菜单</a-button>
          <a-button type="primary" class="btn" @click="exportMenuHandle">导出菜单</a-button>
        </div>
      </template>
      <a-row class="aRow">
        <a-col :span="6" class="hfull">
          <div class="left overflow-y-auto">
            <a-row class="nav">
              <a-col class="navs" :span="6">菜单管理</a-col>
            </a-row>
            <div class="content">
              <a-tree
                v-model:selectedKeys="selectedKeys"
                :tree-data="treeData"
                show-icon
                block-node
                draggable
                :field-names="fieldNames"
                @select="treeSelect"
                @drop="onDrop"
                v-if="treeData.length > 0"
              >
                <template #title="{ ...record }">
                  <span>
                    {{ record.menuName }}
                    <a-dropdown placement="bottomLeft" class="tree-dropdown" @click.stop>
                      <a class="ant-dropdown-link" @click.prevent>
                        <MoreOutlined class="tree-title-custom" />
                      </a>
                      <template #overlay>
                        <a-menu>
                          <a-menu-item-group title="新增">
                            <a-menu-item v-if="record.menuType === 1">
                              <a-button block type="text" @click="operation('add', record)"
                                >新增本级菜单</a-button
                              >
                            </a-menu-item>
                            <a-menu-item v-if="record.menuType === 1">
                              <a-button block type="text" @click="operation('addNext', record)"
                                >新增下级菜单</a-button
                              >
                            </a-menu-item>
                            <a-menu-item
                              v-if="
                                record.menuType === 1 ||
                                record.menuType === 3 ||
                                record.menuType === 4
                              "
                            >
                              <a-button block type="text" @click="operation('addChilren', record)"
                                >新增子权限</a-button
                              >
                            </a-menu-item>
                          </a-menu-item-group>
                          <a-menu-item-group title="删除">
                            <a-menu-item v-if="record.menuType === 1">
                              <a-button
                                block
                                type="text"
                                v-action:delete
                                @click="operation('remove', record)"
                                >删除菜单</a-button
                              >
                            </a-menu-item>
                            <a-menu-item v-if="record.menuType != 1">
                              <a-button
                                block
                                type="text"
                                v-action:delPermission
                                @click="operation('remove', record)"
                                >删除权限</a-button
                              >
                            </a-menu-item>
                          </a-menu-item-group>
                          <a-menu-item-group title="生成" v-if="record.menuType === 1">
                            <a-menu-item>
                              <a-button block type="text" @click="generateFunc(record)"
                                >生成路由</a-button
                              >
                            </a-menu-item>
                          </a-menu-item-group>
                        </a-menu>
                      </template>
                    </a-dropdown>
                  </span>
                </template>
                <template #switcherIcon="{ switcherCls }">
                  <CaretDownOutlined :class="switcherCls" />
                </template>
                <template #icon="{ menuType }">
                  <template v-if="menuType === 1">
                    <UnorderedListOutlined />
                  </template>
                  <template v-else-if="menuType === 2">
                    <TabIcon />
                  </template>
                  <OutterLinkIcon v-else-if="menuType === 3" />
                  <InnerLinkIcon v-else-if="menuType === 4" />
                  <SafetyOutlined v-else-if="menuType === 5" />
                  <template v-else>
                    <MenuOutlined />
                  </template>
                </template>
              </a-tree>
              <div v-else>
                <a-empty />
              </div>
            </div>
          </div>
        </a-col>
        <a-col :span="18" v-show="firstState" class="hfull">
          <div class="right overflow-y-auto">
            <div class="example" v-show="spinModal">
              <a-spin />
            </div>
            <div class="addMenu">
              <div class="addMenu-nav">
                <span>{{ title }}</span>
                <div>
                  <a-button type="primary" v-if="isDetail" @click="isDetail = false">编辑</a-button>
                  <template v-if="!isDetail">
                    <a-button @click="cancel" style="margin-right: 20px">取消</a-button>
                    <a-button
                      type="primary"
                      @click="submit"
                      :loading="submitLoading"
                      v-action:update
                      >提交</a-button
                    >
                  </template>
                </div>
              </div>
              <div class="centent">
                <Form
                  :items="items"
                  v-model="form"
                  ref="FormRefs"
                  :layout="{
                    layout: 'horizontal',
                    labelCol: { span: 6 },
                    wrapperCol: { span: 16 },
                  }"
                  :isDetail="isDetail"
                  :detailData="detailData"
                  :extral="{
                    operationBtn: false,
                  }"
                  @submit="onSubmit"
                  :key="componentKey"
                ></Form>
                <div style="text-align: right" v-if="!isDetail">
                  <a-button @click="addForm('add')" type="primary">新增表单</a-button>
                </div>
                <div class="form-list-table">
                  <a-table
                    :dataSource="formList"
                    :columns="formListColumns"
                    :pagination="false"
                    size="middle"
                  >
                    <template #bodyCell="{ column, record }">
                      <template v-if="column.key === 'action'">
                        <a-space>
                          <a type="link" @click="editForm(record)" v-if="!isDetail">编辑</a>
                          <a-popconfirm
                            title="是否确认删除"
                            ok-text="是"
                            cancel-text="否"
                            @confirm="deleteForm(record)"
                          >
                            <a v-if="!isDetail">删除</a>
                          </a-popconfirm>
                          <a type="link" @click="viewFormDetail(record)">详情</a>
                        </a-space>
                      </template>
                    </template>
                  </a-table>
                </div>
              </div>
            </div>
          </div>
        </a-col>
      </a-row>
    </Container>

    <!-- 导出菜单 -->
    <common-tree-node
      :exportModal="exportModal"
      title="导出菜单"
      :modalSystem="modalSystem"
      @systemSelectEmitHandle="systemSelectEmitHandle"
      @exportModalHandleOk="exportModalHandleOk"
      @exportModalCancel="exportModalCancel"
      :exportTreeData="exportTreeData"
      :confirmLoading="confirmLoading"
      :fNames="fNames"
    ></common-tree-node>
    <!-- 导入菜单 -->
    <system-import-menu
      :systemId="systemId"
      :openImportModal="openImportModal"
      @cancelHandle="cancelImportHadle"
      @importHandleSuccess="importHandleSuccess"
    ></system-import-menu>
    <confirm-delete-modal
      :removeConfirmLoading="removeConfirmLoading"
      :removeMenuModal="removeMenuModal"
      @removeModalhandleOk="removeModalhandleOk"
      @removeModalhandleCancel="removeModalhandleCancel"
    />

    <a-modal
      v-model:open="open"
      width="800px"
      title="提示"
      @ok="okHandle"
      :confirm-loading="generateStatus"
    >
      <div class="route-box">
        <div class="title">当前生成页面：</div>
        <a-table :dataSource="menuList" :columns="columns" :pagination="false">
          <template #bodyCell="{ column, record }">
            <template v-if="['isCover'].includes(column.key)">
              <a-switch v-model:checked="record[column.key]" />
            </template>
            <template v-if="['pageType'].includes(column.key)">
              <a-select ref="select" v-model:value="record[column.key]" style="width: 120px">
                <a-select-option value="normal">含侧边栏</a-select-option>
                <a-select-option value="full-screen">全屏</a-select-option>
                <a-select-option value="secondary-page">二级页面</a-select-option>
              </a-select>
            </template>
          </template>
        </a-table>
        <div class="title">生成路由会刷新页面，是否确认生成?</div>
      </div>
    </a-modal>

    <!-- 新增表单弹窗 -->
    <a-modal
      v-model:open="formModalVisible"
      :title="isFormDetail ? '表单详情' : '新增表单'"
      width="1000px"
      @ok="handleFormModalOk"
      @cancel="handleFormModalCancel"
      :maskClosable="false"
    >
      <a-form :model="formForm" :rules="formRules" ref="formFormRef">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="表单名称" name="gridName">
              <a-input
                v-model:value="formForm.gridName"
                placeholder="请输入表单名称"
                :disabled="isFormDetail"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="表单码" name="gridCode">
              <a-input
                v-model:value="formForm.gridCode"
                placeholder="请输入表单码"
                :disabled="isFormDetail"
              />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
      <div class="form-table">
        <div class="table-header">
          <span>字段列表</span>
          <a-space>
            <a-button type="primary" @click="addFormField" v-if="!isFormDetail">添加字段</a-button>
            <a-button type="primary" @click="importFormField" v-if="!isFormDetail">
              导入字段
            </a-button>
          </a-space>
        </div>
        <div class="draggable-table">
          <div class="table-headers">
            <div class="drag-handle"></div>
            <div class="header-content">
              <div class="header-item">标题</div>
              <div class="header-item">字段名</div>
              <div class="header-item">数据类型</div>
              <div class="header-item">格式化参数</div>
              <div class="header-item action">操作</div>
            </div>
          </div>
          <template v-if="formForm.gridColumns && formForm.gridColumns.length > 0">
            <vuedraggable
              v-model="formForm.gridColumns"
              item-key="id"
              handle=".drag-handle"
              :disabled="isFormDetail"
            >
              <template #item="{ element, index }">
                <div class="draggable-row">
                  <div class="drag-handle" v-if="!isFormDetail">
                    <MenuOutlined />
                  </div>
                  <div class="row-content">
                    <div class="content-item">
                      <a-input
                        v-model:value="element.columnCaption"
                        placeholder="请输入标题"
                        :disabled="isFormDetail"
                      />
                    </div>
                    <div class="content-item">
                      <a-input
                        v-model:value="element.columnName"
                        placeholder="请输入字段名"
                        :disabled="isFormDetail"
                      />
                    </div>
                    <div class="content-item">
                      <a-select
                        v-model:value="element.columnType"
                        placeholder="请输入数据类型"
                        style="width: 100%"
                        :disabled="isFormDetail"
                      >
                        <a-select-option
                          v-for="item in typedValues"
                          :value="item.value"
                          :key="item.value"
                        >
                          {{ item.type }}
                        </a-select-option>
                      </a-select>
                    </div>
                    <div class="content-item">
                      <a-input
                        v-model:value="element.columnTypeParam"
                        placeholder="请输入格式化参数"
                        :disabled="isFormDetail"
                      />
                    </div>
                    <div class="content-item action">
                      <a-button
                        type="link"
                        danger
                        @click="removeFormField(index)"
                        v-if="!isFormDetail"
                        >删除</a-button
                      >
                    </div>
                  </div>
                </div>
              </template>
            </vuedraggable>
          </template>
          <template v-else>
            <div class="empty-state">
              <a-empty description="暂无数据" />
            </div>
          </template>
        </div>
      </div>
    </a-modal>

    <!-- 导入字段弹窗 -->
    <a-modal
      v-model:open="importFieldModalVisible"
      title="导入字段"
      width="800px"
      @ok="handleImportFieldModalOk"
      @cancel="handleImportFieldModalCancel"
      :maskClosable="false"
    >
      <div class="import-field-content">
        <div class="import-tips">
          <a-alert
            message="JSON格式说明"
            description="请输入包含字段信息的JSON数组。如果您的JSON字段名与系统默认不同，请在下方配置字段映射关系。"
            type="info"
            show-icon
            style="margin-bottom: 16px"
          />
        </div>

        <!-- 字段映射配置 -->
        <div class="field-mapping-section">
          <div class="section-title">
            <span>字段映射配置</span>
            <a-button type="link" size="small" @click="resetFieldMapping">重置为默认</a-button>
          </div>
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="标题字段名">
                <a-input
                  v-model:value="fieldMapping.columnCaption"
                  placeholder="例如：columnCaption, title, name"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="字段名字段名">
                <a-input
                  v-model:value="fieldMapping.columnName"
                  placeholder="例如：columnName, fieldName, key"
                />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="数据类型字段名">
                <a-input
                  v-model:value="fieldMapping.columnType"
                  placeholder="例如：columnType, type, dataType"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="格式化参数字段名">
                <a-input
                  v-model:value="fieldMapping.columnTypeParam"
                  placeholder="例如：columnTypeParam, param, format"
                />
              </a-form-item>
            </a-col>
          </a-row>
        </div>

        <a-form-item label="JSON数据" required>
          <a-textarea
            v-model:value="importFieldJson"
            placeholder='请输入JSON格式的字段数据，例如：
默认格式：[{"columnCaption": "用户姓名", "columnName": "userName", "columnType": "string", "columnTypeParam": ""}]
自定义格式：[{"title": "用户姓名", "fieldName": "userName", "type": "string", "param": ""}]
（使用自定义格式时请配置上方字段映射）'
            :rows="12"
            :maxlength="5000"
            show-count
          />
        </a-form-item>

        <div class="import-options">
          <a-space>
            <a-checkbox v-model:checked="importFieldOptions.appendMode">
              追加模式（不清空现有字段）
            </a-checkbox>
            <a-button type="link" size="small" @click="previewImportData"> 预览转换结果 </a-button>
          </a-space>
        </div>
      </div>
    </a-modal>
  </main>
</template>
<script lang="ts" setup>
import TabIcon from '@/assets/icons/tab.vue'
import OutterLinkIcon from '@/assets/icons/outter-link.vue'
import InnerLinkIcon from '@/assets/icons/inner-link.vue'
import { ref, watch, computed, h } from 'vue'
import { useRoute } from 'vue-router'
import { type FormInstance, type TreeProps, message, Modal } from 'ant-design-vue'
import type { AntTreeNodeDropEvent, TreeDataItem } from 'ant-design-vue/es/tree'
import type { Rule } from 'ant-design-vue/es/form'
import {
  CaretDownOutlined,
  UnorderedListOutlined,
  SafetyOutlined,
  MenuOutlined,
  MoreOutlined,
} from '@ant-design/icons-vue'
import {
  getMenuList,
  addMenu,
  deleteMenu,
  viewMenuDtl,
  updateMenu,
  getSystemList,
  exportMenu,
} from '@/api/services/permission'
import { generationRouter, getGenerationRouter } from '@/components/code-generation/code-generation'
import { useUserStore } from '@/stores/user'
import { getUserMenuFn } from '@/utils/user'
import { Form, Container } from '@fs/fs-components'
import vuedraggable from 'vuedraggable'
import { createUuid } from '@/utils/utils'

defineOptions({
  name: 'detailSystem',
})

const fieldNames: TreeProps['fieldNames'] = {
  children: 'datas',
  title: 'menuName',
  key: 'menuId',
}
interface FormState {
  menuName: string
  menuIcon: string
  menuUrl: string
  sortId: string
  menuType: number
  actionName: string
  isDefaultGrant: boolean
  isUse: boolean
  parentId: string //父级菜单
  apiMethod: string | undefined //请求地址
  apiMode: string | undefined // 接口读写模式
  operation: false // 操作
  menuId?: '' //菜单id
  systemId?: '' //系统id
  isChildrenMenu?: boolean //是否为子菜单
  systemName?: string // 系统名称
  grids?: any[] // 表单列表
}
const operateTitle = ref('菜单')
const fNames = {
  children: 'datas',
  title: 'menuName',
  key: 'menuId',
}
const formType = ref('')
const typedValues = [
  { type: 'int', value: 'int' },
  { type: 'double', value: 'double' },
  { type: 'boolean', value: 'boolean' },
  { type: 'char', value: 'char' },
  { type: 'long', value: 'long' },
  { type: 'float', value: 'float' },
  { type: 'byte', value: 'byte' },
  { type: 'short', value: 'short' },
  { type: 'long', value: 'long' },
]
const obj = {
  menuName: '',
  menuIcon: '',
  menuUrl: '',
  sortId: '',
  menuType: 1,
  actionName: '',
  isDefaultGrant: true,
  isUse: true,
  parentId: '_top',
  apiMethod: undefined,
  apiMode: undefined,
  operation: false,
  isChildrenMenu: false,
}
const form = ref<FormState>(JSON.parse(JSON.stringify(obj)))

const detailData = ref<any>({}) // 表单详情数据
const cancelHidden = ref(false) // 取消时关闭右侧菜单
const componentKey = ref(0)
const FormRefs = ref<any>()
const route = useRoute()
const systemName = ref<string>('')
const isDetail = ref(true)
const systemId = ref<string>('')
const treeData = ref<any>([])
const exportTreeData = ref<any>([])
const selectedKeys = ref(['0-0-0'])
const title = ref<string>('新增本级菜单')
const exportModal = ref<boolean>(false)
const removeConfirmLoading = ref<boolean>(false)
const disabled = ref<boolean>(false)
const MenuPower = ref<boolean>(false)
const openImportModal = ref<boolean>(false)
const removeMenuModal = ref<boolean>(false)
const removeMenuModalValue = ref<Record<string, any>>({})
const spinModal = ref<boolean>(false)
const submitLoading = ref<boolean>(false)
const modalSystem = ref([])
const confirmLoading = ref<boolean>(false)
const operationStatus = ref<boolean>(true)
const open = ref(false)
const selectValue = ref<any>({})
const menuList = ref<any>([])
const generateStatus = ref<boolean>(false)
const firstState = ref<boolean>(false)
const columns = [
  {
    title: '页面名称',
    dataIndex: 'menuName',
    key: 'menuName',
  },
  {
    title: '页面路由：',
    dataIndex: 'menuUrl',
    key: 'menuUrl',
  },
  {
    title: '是否覆盖',
    dataIndex: 'isCover',
    key: 'isCover',
  },
  {
    title: '页面类型',
    dataIndex: 'pageType',
    key: 'pageType',
  },
]

const rules: Record<string, Rule[]> = {
  menuName: [{ required: true, message: `请输入名称`, trigger: 'change' }],
  menuUrl: [{ required: true, message: '请输入菜单地址', trigger: 'change' }],
  actionName: [{ required: true, message: `请输入标识`, trigger: 'change' }],
}

const items = computed(() => {
  const arr: any[] = [
    {
      name: 'menuName',
      label: operateTitle.value + '名称',
      span: 12,
      rules: [
        { required: true, message: '请输入' + operateTitle.value + '名称', trigger: 'change' },
      ],
    },
    {
      name: 'systemName',
      label: '所属系统',
      span: 12,
      compProps: {
        disabled: true,
      },
    },
    {
      name: 'menuIcon',
      label: operateTitle.value + '图标',
      span: 12,
    },
    {
      name: 'menuUrl',
      label: operateTitle.value + '地址',
      required: true,
      span: 12,
      rules: [
        { required: true, message: '请输入' + operateTitle.value + '地址', trigger: 'change' },
      ],
    },
    {
      name: 'sortId',
      label: '排序号',
      span: 12,
    },
    {
      name: 'menuType',
      label: '菜单类型',
      span: 12,
      type: 'select',
      compProps: {
        options: [
          {
            label: '菜单',
            value: 1,
          },
          {
            label: '页签',
            value: 2,
          },
          {
            label: '跳转链接',
            value: 3,
          },
          {
            label: '内嵌',
            value: 4,
          },
        ],
      },
    },
    {
      name: 'actionName',
      label: operateTitle.value + '标识',
      span: 12,
      rules: [
        { required: true, message: '请输入' + operateTitle.value + '标识', trigger: 'change' },
      ],
    },
    {
      name: 'isDefaultGrant',
      label: '默认授权',
      type: 'radio',
      span: 12,
      compProps: {
        options: [
          {
            label: '是',
            value: true,
          },
          {
            label: '否',
            value: false,
          },
        ],
      },
    },
    {
      name: 'isUse',
      label: '是否启用',
      type: 'radio',
      span: 12,
      compProps: {
        options: [
          {
            label: '是',
            value: true,
          },
          {
            label: '否',
            value: false,
          },
        ],
      },
    },
    {
      name: 'isChildrenMenu',
      label: '按钮或Tab标签',
      type: 'radio',
      span: 12,
      compProps: {
        options: [
          {
            label: '是',
            value: true,
          },
          {
            label: '否',
            value: false,
          },
        ],
      },
    },
  ]
  if (operateTitle.value === '权限') {
    arr.splice(5, 1, {
      name: 'menuType',
      label: '菜单类型',
      span: 12,
      type: 'select',
      compProps: {
        disabled: true,
        options: [
          {
            label: '权限',
            value: 5,
          },
        ],
      },
    })
    arr.splice(5, 0, {
      name: 'apiMethod',
      label: '请求方式',
      span: 12,
      type: 'select',
      compProps: {
        options: [
          {
            label: 'GET',
            value: 'GET',
          },
          {
            label: 'POST',
            value: 'POST',
          },
          {
            label: 'PUT',
            value: 'PUT',
          },
          {
            label: 'DELETE',
            value: 'DELETE',
          },
          {
            label: 'PATCH',
            value: 'PATCH',
          },
        ],
      },
    })
    arr.splice(6, 0, {
      name: 'apiMode',
      label: '读写模式',
      span: 12,
      type: 'select',
      compProps: {
        options: [
          {
            label: 'r',
            value: 'r',
          },
          {
            label: 'w',
            value: 'w',
          },
        ],
      },
    })
  }
  return arr
})

// 监听类型变化 通过key变化刷新form表单
watch(
  () => operateTitle.value,
  () => {
    componentKey.value++
  },
  { immediate: true },
)

watch(
  () => route.query?.systemId,
  (newValue: any) => {
    if (newValue) {
      systemId.value = newValue
      systemName.value = route.query?.systemName as string
      getMenuLists()
      getSystemModal()
    }
  },
  { immediate: true },
)

function getSystemModal() {
  const data = {
    pageIndex: 1,
    pageSize: 999,
  }
  getSystemList(data).then((res: any) => {
    if (res.code === '000000') {
      modalSystem.value = res.data?.records
    }
  })
}

function systemSelectEmitHandle(key: string) {
  const data = {
    systemId: key,
  }
  getMenuList(data).then((result: any) => {
    if (result.code === '000000') {
      exportTreeData.value = result.data
    }
  })
}

function getMenuLists(flag = true) {
  const data = {
    systemId: systemId.value,
  }
  getMenuList(data).then((result: any) => {
    if (result.code === '000000') {
      treeData.value = result.data
      flag && resetForm()
    }
  })
}

//树控件的拖拽事件
const onDrop = (info: AntTreeNodeDropEvent) => {
  if (info.node?.menuType === 2) {
    return false
  }
  const dropKey = info.node.key
  const dragKey = info.dragNode.key
  const dropPos = info.node.pos?.split('-') || []
  const dropPosition = info.dropPosition - Number(dropPos[dropPos.length - 1])
  const loop = (data: TreeProps['treeData'], key: string | number, callback: any) => {
    data &&
      data.forEach((item: any, index: number) => {
        if (item.menuId === key) {
          return callback(item, index, data)
        }
        if (item.menuList) {
          return loop(item.menuList, key, callback)
        }
      })
  }

  const data = [...treeData.value]
  let dragObj: TreeDataItem
  loop(data, dragKey, (item: TreeDataItem, index: number, arr: TreeProps['treeData']) => {
    arr && arr.splice(index, 1)
    dragObj = item
  })
  if (!info.dropToGap) {
    loop(data, dropKey, (item: TreeDataItem) => {
      item.menuList = item.menuList || []
      item.menuList.unshift(dragObj)
    })
  } else if (
    (info.node.children || []).length > 0 && // Has children
    info.node.expanded && // Is expanded
    dropPosition === 1 // On the bottom gap
  ) {
    loop(data, dropKey, (item: TreeDataItem) => {
      item.menuList = item.menuList || []
      item.menuList.unshift(dragObj)
    })
  } else {
    let ar: TreeProps['treeData'] = []
    let i = 0
    loop(data, dropKey, (_item: TreeDataItem, index: number, arr: TreeProps['treeData']) => {
      ar = arr
      i = index
    })
    if (dropPosition === -1) {
      //@ts-ignore
      ar.splice(i, 0, dragObj)
    } else {
      //@ts-ignore
      ar.splice(i + 1, 0, dragObj)
    }
  }
  treeData.value = data
  updateMenuList(info)
}

// 修改菜单
function updateMenuList(info: any) {
  const { dropToGap, node, dragNode } = info
  const { menuName, key } = dragNode
  const { menuId } = node
  const dropParentId = node.parentId
  const data = {
    menuId: key,
    systemId: systemId.value,
    menuName,
    parentId: dropToGap ? dropParentId : menuId,
    isUse: true,
  }
  updateMenu(data)
}

const operation = (type: string, data: Record<string, any>) => {
  data.operation = false
  disabled.value = false
  form.value.menuType = 1
  operateTitle.value = '菜单'
  MenuPower.value = false
  operationStatus.value = true
  if (type === 'add') {
    title.value = '新增本级菜单'
    form.value.parentId = data?.parentId
    resetForm()
    Initialization()
  } else if (type === 'addNext') {
    title.value = '新增下级菜单'
    form.value.parentId = data?.menuId
    resetForm()
    Initialization()
  } else if (type === 'addChilren') {
    operateTitle.value = '权限'
    title.value = '新增子权限'
    disabled.value = true
    MenuPower.value = true
    form.value.menuType = 2
    form.value.parentId = data?.menuId
    resetForm()
    Initialization()
  } else if (type === 'remove') {
    removeMenuConfirmation(data)
  }
  isDetail.value = false
}

function submit() {
  FormRefs.value?.submit()
}

function onSubmit() {
  submitLoading.value = true
  if (!operationStatus.value) {
    editMenus()
  } else {
    const data = {
      ...form.value,
      systemId: systemId.value,
      grids: formList.value,
    }
    addMenu(data)
      .then((res: any) => {
        if (res.code === '000000') {
          message.success('新增成功!')
          getMenuLists(true)
          getDetailHandle(data)
        }
      })
      .finally(() => {
        submitLoading.value = false
      })
  }
  // })
  //     .catch((error) => {
  //       console.log('error', error)
  //     })
}

function removeMenuConfirmation(data: Record<string, any>) {
  removeMenuModal.value = true
  removeMenuModalValue.value = data
}

function removeModalhandleOk() {
  removeConfirmLoading.value = true
  const menuType =
    removeMenuModalValue.value?.menuType === 1
      ? '菜单'
      : removeMenuModalValue.value?.menuType === 2
        ? '权限'
        : '页签'
  return new Promise(() => {
    removeMenu(removeMenuModalValue.value).finally(function () {
      removeMenuModal.value = false
      removeConfirmLoading.value = false
      title.value = `新增${menuType}`
      operateTitle.value = menuType
    })
  })
}

function removeModalhandleCancel() {
  removeMenuModal.value = false
  removeConfirmLoading.value = false
}

function removeMenu(data: Record<string, any>) {
  return new Promise((resolve, reject) => {
    const dataParams = {
      menuId: data.menuId,
      systemId: systemId.value,
    }
    deleteMenu(dataParams)
      .then((res: any) => {
        message.success('删除成功!')
        getMenuLists()
        resolve(res)
      })
      .catch(() => {
        reject()
      })
  })
}

function treeSelect(selectedKeys: any, e: { node: any }) {
  operationStatus.value = false
  firstState.value = true
  isDetail.value = true
  resetForm()
  if (e?.node?.menuType === 1) {
    title.value = '编辑菜单'
    operateTitle.value = '菜单'
    MenuPower.value = false
  } else if (e?.node?.menuType === 5) {
    title.value = '编辑权限'
    operateTitle.value = '权限'
    MenuPower.value = true
  } else if (e?.node?.menuType === 3) {
    title.value = '编辑页签'
    operateTitle.value = '页签'
  }
  if (selectedKeys[0]) {
    spinModal.value = true
    viewMenus(selectedKeys[0])
  }
}

function viewMenus(menuId: string) {
  const params = {
    tenantId: 'system',
    menuId: menuId,
  }
  viewMenuDtl(params)
    .then((res: any) => {
      if (res.code === '000000') {
        form.value = res.data
        getDetailHandle(res.data)
      }
    })
    .finally(() => {
      spinModal.value = false
    })
}

const menyTypeMap: Record<number, string> = {
  1: '菜单',
  2: '页签',
  3: '跳转链接',
  4: '内嵌',
  5: '权限',
}

function getDetailHandle(data: any) {
  detailData.value = JSON.parse(JSON.stringify(data))
  detailData.value.menuType = menyTypeMap[data.menuType]
  detailData.value.isDefaultGrant = data.isDefaultGrant ? '是' : '否'
  detailData.value.isUse = data.isUse ? '是' : '否'
  detailData.value.isChildrenMenu = data.isChildrenMenu ? '是' : '否'
  detailData.value.systemName = systemName.value
  form.value.systemName = systemName.value
  formList.value = data?.grids ?? []
}

function editMenus() {
  const obj = { ...form.value }
  obj.grids = formList.value
  //@ts-ignore
  updateMenu({ ...obj })
    .then((result: any) => {
      if (result.code === '000000') {
        message.success('更新成功!')
        getMenuLists(false)
        isDetail.value = true
        getDetailHandle({ ...obj })
      }
    })
    .finally(() => {
      submitLoading.value = false
    })
}

function addMenuHandle() {
  title.value = '新增本级菜单'
  operateTitle.value = '菜单'
  isDetail.value = false
  FormRefs.value?.reset()
  form.value.systemName = systemName.value
  form.value = JSON.parse(JSON.stringify(obj))
  detailData.value = {}
  operationStatus.value = true
  Initialization()
}

function cancel() {
  isDetail.value = true
  if (cancelHidden.value) {
    cancelHidden.value = false
    firstState.value = false
  }
}

function leadMenuHandle() {
  openImportModal.value = true
}

function exportMenuHandle() {
  exportModal.value = true
  exportTreeData.value = []
}

function appendSelection(e: string) {
  if (e === 'addMenu') {
    title.value = '新增本级菜单'
    resetForm()
  } else if (e === 'leadMenu') {
    openImportModal.value = true
  } else if (e === 'exportMenu') {
    exportModal.value = true
    exportTreeData.value = []
  }
}

const onFinish = (values: any) => {
  console.log('Success:', values)
}

const onFinishFailed = (errorInfo: any) => {
  console.log('Failed:', errorInfo)
}

function exportModalHandleOk(checkedKeys: string[]) {
  const outputData = transformData(checkedKeys)
  confirmLoading.value = true
  exportMenu({ menuList: outputData })
    .then((res) => {
      //@ts-ignore
      const blob = new Blob([res], { type: 'application/octet-stream' })
      const link = document.createElement('a')
      link.href = window.URL.createObjectURL(blob)
      link.download = 'menuList.xlsx'
      link.click()
      exportModal.value = false
    })
    .finally(() => {
      confirmLoading.value = false
    })
}

function transformData(inputData: string | any[]) {
  const outputData = []
  for (let i = 0; i < inputData.length; i++) {
    outputData.push(inputData[i].menuId)
  }
  return outputData
}

function exportModalCancel() {
  exportModal.value = false
  confirmLoading.value = false
}

const resetForm = () => {
  FormRefs.value?.reset()
  form.value['menuType'] = title.value === '新增子权限' ? 5 : 1
}

const Initialization = () => {
  cancelHidden.value = true
  firstState.value = true
  form.value.systemName = systemName.value
}

function cancelImportHadle() {
  openImportModal.value = false
}

function importHandleSuccess() {
  openImportModal.value = false
  getMenuLists()
}

async function generateHandle() {
  await generationRouter({ routerList: menuList.value })
}

async function generateFunc(value: any) {
  try {
    delete value.data
    delete value.dataRef
    selectValue.value = JSON.parse(JSON.stringify(value))
    const token = useUserStore().token
    const { data } = await getGenerationRouter({ menu: JSON.parse(JSON.stringify(value)), token })
    const list = data.data
    list.forEach((item: any) => {
      if (!item.pageType) {
        item.pageType = 'normal'
      }
    })
    menuList.value = list
    open.value = true
  } catch {
    message.error('获取生成路由列表失败')
  }
}

async function okHandle() {
  try {
    generateStatus.value = true
    await getUserMenuFn(false)
    await generateHandle()
    message.success('生成路由成功')
    open.value = false
    generateStatus.value = false
  } catch {
    generateStatus.value = false
  }
}

const formModalVisible = ref(false)
const formFormRef = ref()
const formForm = ref<Record<string, any>>({
  gridCode: '',
  gridName: '',
  gridColumns: [] as any[],
  gridId: '',
  menuId: '',
})
const isFormDetail = ref(false) // 添加表单详情状态

// 导入字段相关变量
const importFieldModalVisible = ref(false)
const importFieldJson = ref('')
const importFieldOptions = ref({
  appendMode: false,
})

// 字段映射配置
const fieldMapping = ref({
  columnCaption: 'columnCaption',
  columnName: 'columnName',
  columnType: 'columnType',
  columnTypeParam: 'columnTypeParam',
})

// 默认字段映射
const defaultFieldMapping = {
  columnCaption: 'columnCaption',
  columnName: 'columnName',
  columnType: 'columnType',
  columnTypeParam: 'columnTypeParam',
}

const formRules = {
  gridCode: [{ required: true, message: '请输入表单码', trigger: 'blur' }],
  gridName: [{ required: true, message: '请输入表单名称', trigger: 'blur' }],
}

const addFormField = () => {
  formForm.value.gridColumns.push({
    id: createUuid,
    columnName: '',
    columnCaption: '',
    columnType: null,
    columnTypeParam: '',
  })
}
const importFormField = () => {
  importFieldModalVisible.value = true
  importFieldJson.value = ''
  importFieldOptions.value.appendMode = false
  // 重置字段映射为默认值
  fieldMapping.value = { ...defaultFieldMapping }
}

// 重置字段映射为默认值
const resetFieldMapping = () => {
  fieldMapping.value = { ...defaultFieldMapping }
}

// 预览导入数据
const previewImportData = () => {
  try {
    if (!importFieldJson.value.trim()) {
      message.error('请输入JSON数据')
      return
    }

    const parsedData = JSON.parse(importFieldJson.value.trim())

    if (!Array.isArray(parsedData)) {
      message.error('JSON数据必须是数组格式')
      return
    }

    // 验证数据格式，使用自定义字段映射
    const validFields = parsedData.filter((item: any) => {
      return (
        item &&
        typeof item === 'object' &&
        (item[fieldMapping.value.columnCaption] ||
          item[fieldMapping.value.columnName] ||
          item[fieldMapping.value.columnType])
      )
    })

    if (validFields.length === 0) {
      message.error('未找到有效的字段数据，请检查字段映射配置')
      return
    }

    // 转换数据格式，使用自定义字段映射
    const processedFields = validFields.map((item: any) => ({
      columnCaption: item[fieldMapping.value.columnCaption] || '',
      columnName: item[fieldMapping.value.columnName] || '',
      columnType: item[fieldMapping.value.columnType] || null,
      columnTypeParam: item[fieldMapping.value.columnTypeParam] || '',
    }))

    // 显示预览信息
    Modal.info({
      title: '转换结果预览',
      width: 600,
      content: h('div', [
        h('p', `共找到 ${validFields.length} 个有效字段：`),
        h('div', { style: 'max-height: 300px; overflow-y: auto;' }, [
          h('table', { style: 'width: 100%; border-collapse: collapse;' }, [
            h('thead', [
              h('tr', [
                h(
                  'th',
                  { style: 'border: 1px solid #d9d9d9; padding: 8px; background: #fafafa;' },
                  '标题',
                ),
                h(
                  'th',
                  { style: 'border: 1px solid #d9d9d9; padding: 8px; background: #fafafa;' },
                  '字段名',
                ),
                h(
                  'th',
                  { style: 'border: 1px solid #d9d9d9; padding: 8px; background: #fafafa;' },
                  '数据类型',
                ),
                h(
                  'th',
                  { style: 'border: 1px solid #d9d9d9; padding: 8px; background: #fafafa;' },
                  '格式化参数',
                ),
              ]),
            ]),
            h(
              'tbody',
              processedFields.map((field: any) =>
                h('tr', [
                  h(
                    'td',
                    { style: 'border: 1px solid #d9d9d9; padding: 8px;' },
                    field.columnCaption,
                  ),
                  h('td', { style: 'border: 1px solid #d9d9d9; padding: 8px;' }, field.columnName),
                  h('td', { style: 'border: 1px solid #d9d9d9; padding: 8px;' }, field.columnType),
                  h(
                    'td',
                    { style: 'border: 1px solid #d9d9d9; padding: 8px;' },
                    field.columnTypeParam,
                  ),
                ]),
              ),
            ),
          ]),
        ]),
      ]),
    })
  } catch (error) {
    console.error('JSON解析错误:', error)
    message.error('JSON格式错误，请检查输入的数据格式')
  }
}

// 处理导入字段弹窗确认
const handleImportFieldModalOk = () => {
  try {
    if (!importFieldJson.value.trim()) {
      message.error('请输入JSON数据')
      return
    }

    const parsedData = JSON.parse(importFieldJson.value.trim())

    if (!Array.isArray(parsedData)) {
      message.error('JSON数据必须是数组格式')
      return
    }

    // 验证数据格式，使用自定义字段映射
    const validFields = parsedData.filter((item: any) => {
      return (
        item &&
        typeof item === 'object' &&
        (item[fieldMapping.value.columnCaption] ||
          item[fieldMapping.value.columnName] ||
          item[fieldMapping.value.columnType])
      )
    })

    if (validFields.length === 0) {
      message.error('未找到有效的字段数据，请检查字段映射配置')
      return
    }

    // 转换数据格式，使用自定义字段映射
    const processedFields = validFields.map((item: any) => ({
      columnCaption: item[fieldMapping.value.columnCaption] || '',
      columnName: item[fieldMapping.value.columnName] || '',
      columnType: item[fieldMapping.value.columnType] || null,
      columnTypeParam: item[fieldMapping.value.columnTypeParam] || '',
      columnId: item.columnId || '',
    }))

    // 根据模式决定是追加还是替换
    if (importFieldOptions.value.appendMode) {
      // 追加模式：添加到现有字段后面
      formForm.value.gridColumns.push(...processedFields)
    } else {
      // 替换模式：清空现有字段并添加新字段
      formForm.value.gridColumns = processedFields
    }

    message.success(`成功导入 ${processedFields.length} 个字段`)
    importFieldModalVisible.value = false
    importFieldJson.value = ''
  } catch (error) {
    console.error('JSON解析错误:', error)
    message.error('JSON格式错误，请检查输入的数据格式')
  }
}

// 处理导入字段弹窗取消
const handleImportFieldModalCancel = () => {
  importFieldModalVisible.value = false
  importFieldJson.value = ''
  importFieldOptions.value.appendMode = false
}

const removeFormField = (index: number) => {
  formForm.value.gridColumns.splice(index, 1)
}

const handleFormModalOk = () => {
  formFormRef.value.validate().then(() => {
    // 检查是新增还是编辑
    if (formType.value === 'edit') {
      const index = formList.value.findIndex((item: any) => item.gridId === formForm.value.gridId)
      // 编辑
      formList.value[index] = { ...formForm.value }
    } else if (formType.value === 'add') {
      // 新增
      formList.value.push({ ...formForm.value })
    }
    formModalVisible.value = false
    isFormDetail.value = false
    formForm.value = {
      gridCode: '',
      gridName: '',
      gridColumns: [],
      id: createUuid(),
    }
  })
}

const handleFormModalCancel = () => {
  formModalVisible.value = false
  isFormDetail.value = false
  formForm.value = {
    gridCode: '',
    gridName: '',
    gridColumns: [],
    id: '',
  }
}

// 修改新增表单按钮的点击事件
const addForm = (type: string) => {
  formModalVisible.value = true
  formType.value = type
}

const formList = ref<any[]>([])
const formListColumns = [
  {
    title: '表单码',
    dataIndex: 'gridCode',
    key: 'gridCode',
  },
  {
    title: '表单名称',
    dataIndex: 'gridName',
    key: 'gridName',
  },
  {
    title: '操作',
    key: 'action',
    width: 150,
  },
]

const editForm = (record: any) => {
  formForm.value = { ...record }
  formForm.value.gridColumns = record.gridColumns.map((item: any) => {
    return {
      ...item,
    }
  })
  formModalVisible.value = true
  formType.value = 'edit'
}

const viewFormDetail = (record: any) => {
  isFormDetail.value = true
  formForm.value = { ...record }
  formForm.value.gridColumns = record.gridColumns.map((item: any) => {
    return {
      ...item,
      id: createUuid(),
    }
  })
  formModalVisible.value = true
}

const deleteForm = (record: any) => {
  // TODO: 实现删除表单的逻辑
  const index = formList.value.findIndex((item: any) => item.gridCode === record.gridCode)
  if (index > -1) {
    formList.value.splice(index, 1)
  }
}
</script>
<style lang="less" scoped>
main {
  height: 100%;
  .left-box {
    display: flex;
    .btn {
      margin-right: 10px;
    }
  }
  .left {
    height: 100%;
    border: 1px solid rgba(0, 0, 0, 0.04);
    border-radius: 4px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);

    /* 更大的模糊半径和渐变颜色 */
    .nav {
      border-bottom: 1px solid #e8e8e8;
      border-radius: 2px 2px 0 0;
      display: flex;
      align-items: center;

      .navs {
        display: inline-block;
        padding: 16px 0;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        color: rgba(0, 0, 0, 0.85);
        font-weight: 500;
        font-size: 16px;
        padding-left: 16px;
      }

      .select {
        display: flex;
        justify-content: flex-end;
        .btn {
          margin-right: 10px;
          cursor: pointer;
        }
      }
    }

    .tree-dropdown {
      position: absolute;
      right: 0px;
      top: 0px;
      padding-left: 20px;
      padding-right: 10px;
    }

    .content {
      padding: 16px;
    }
  }

  .right {
    margin: 0 16px;
    margin-right: 0px;
    position: relative;
    border: 1px solid rgba(0, 0, 0, 0.04);
    border-radius: 4px;
    text-align: center;
    height: 100%;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);

    /* 更大的模糊半径和渐变颜色 */
    .addMenu {
      .addMenu-nav {
        border-bottom: 1px solid #e8e8e8;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-right: 20px;

        span {
          padding: 16px;
          font-size: 16px;
        }
      }
    }

    .centent {
      padding: 16px;
    }

    .form-row {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 10px;
    }
  }

  .aRow {
    height: 100%;
  }
}

/deep/.selectSelector {
  .ant-select-selector {
    border-radius: 5px;
  }
}

.example {
  text-align: center;
  background: rgba(0, 0, 0, 0.01);
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 100;
  display: flex;
  align-items: center;
  justify-content: center;
}

.form-table {
  margin-top: 16px;
  .table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    span {
      font-size: 16px;
      font-weight: 500;
    }
  }
}

.form-list-table {
  margin-top: 16px;
  background: #fff;
  padding: 16px;
  border-radius: 4px;
}

.draggable-table {
  .table-headers {
    display: flex;
    align-items: center;
    padding: 8px;
    background: #fafafa;
    border-bottom: 1px solid #f0f0f0;
    font-weight: 500;

    .drag-handle {
      width: 32px;
      flex-shrink: 0;
    }

    .header-content {
      flex: 1;
      display: flex;
      gap: 8px;
    }

    .header-item {
      flex: 1;
      padding: 0 8px;

      &.action {
        width: 80px;
        flex: none;
      }
    }
  }

  .draggable-row {
    display: flex;
    align-items: center;
    padding: 8px;
    border-bottom: 1px solid #f0f0f0;
    background: #fff;

    &:hover {
      background: #fafafa;
    }

    .drag-handle {
      width: 32px;
      flex-shrink: 0;
      cursor: move;
      color: #999;
      display: flex;
      align-items: center;
      justify-content: center;

      &:hover {
        color: #1890ff;
      }
    }

    .row-content {
      flex: 1;
      display: flex;
      gap: 8px;
      align-items: center;

      .content-item {
        flex: 1;

        &.action {
          width: 80px;
          flex: none;
          display: flex;
          justify-content: center;
        }

        .ant-input,
        .ant-select {
          width: 100%;
        }
      }
    }
  }

  .empty-state {
    padding: 32px 0;
    background: #fff;
  }
}

.import-field-content {
  .import-tips {
    margin-bottom: 16px;
  }

  .field-mapping-section {
    margin-bottom: 16px;
    padding: 16px;
    background: #fafafa;
    border-radius: 6px;
    border: 1px solid #f0f0f0;

    .section-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      font-weight: 500;
      color: #262626;
    }
  }

  .import-options {
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid #f0f0f0;
  }
}
</style>
<style lang="less">
.popoverOption {
  width: 150px;
}

.ant-form-item-radios {
  .ant-form-item-control-input-content {
    display: flex;
  }
}

.ant-dropdown-menu {
  .ant-dropdown-menu-item:hover {
    background-color: transparent !important;
  }
}
.route-box {
  .title {
    font-size: 15px;
    font-weight: bold;
    margin-bottom: 5px;
  }
  .title:last-child {
    margin-top: 5px;
  }
  .route-item {
    display: flex;
    .route-item-name {
      display: inline-block;
      width: 300px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .route-item-path {
      display: inline-block;
      width: 300px;
    }
  }
}
.fs-container .container-content {
  padding-bottom: 0px !important;
}
.detail-system {
  height: 100%;
  background: white;
}
</style>
