import { expect, test } from 'vitest'
import {
  addDataAssetsDirectory,
  updateDataAssetsDirectory,
  delDataAssetsDirectory,
  getListDataAssetsDirectory,
} from './catalogadmin'

test('addDataAssetsDirectory', async () => {
  const { data } = await addDataAssetsDirectory({
    name: 'P2kv',
    description: 'Z(*',
  })

  expect(data).toBeTruthy()
})

test('updateDataAssetsDirectory', async () => {
  const { data } = await updateDataAssetsDirectory({
    parentId: '@9meb9v',
    name: 'MC)Wlp',
    description: 'Zr@4H',
  })

  expect(data).toBeTruthy()
})

test('delDataAssetsDirectory', async () => {
  const { data } = await delDataAssetsDirectory({})

  expect(data).toBeTruthy()
})

test('getListDataAssetsDirectory', async () => {
  const { data } = await getListDataAssetsDirectory({ name: '&UoHqM' })

  expect(data).toHaveProperty('code')
  expect(data).toHaveProperty('msg')
  expect(data.data[0]).toHaveProperty('id'),
    expect(data.data[0]).toHaveProperty('parentId'),
    expect(data.data[0]).toHaveProperty('name'),
    expect(data.data[0]).toHaveProperty('description'),
    expect(data.data[0]).toHaveProperty('createdTime'),
    expect(data.data[0]).toHaveProperty('createdUser'),
    expect(data.data[0]).toHaveProperty('updatedTime'),
    expect(data.data[0]).toHaveProperty('updatedUser')
})
