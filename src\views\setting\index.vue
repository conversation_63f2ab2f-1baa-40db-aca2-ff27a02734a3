<template>
  <div class="set-config">
    <div class="nav-page-title">
      <div class="title-left">
        <p>设置</p>
        <div class="desc">能够根据需要设置大数据开发管理平台配置</div>
      </div>
    </div>
    <div class="set-config__card-list">
      <CardOption
        v-for="item in cardList"
        :key="item.title"
        :title="item.title"
        :desc="item.desc"
        :icon-color="item.iconColor"
        :icon="item.icon"
        @click="handleCardClick(item)"
        v-action:[item.actionName]
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import CardOption from './card-option.vue'
import dataSource from '@/assets/icons/data-source.png'
import permission from '@/assets/icons/permission.png'
import access_control from '@/assets/icons/access_control.png'
import setting from '@/assets/icons/setting.png'

interface CardItem {
  title: string
  desc: string
  iconColor?: string
  icon?: string
  key: string
  path: string
  actionName?: string
}

const router = useRouter()

const cardList = ref<CardItem[]>([
  {
    title: '管理配置',
    desc: '配置获取不同来源的数据源连接，提取数据资源',
    iconColor: '#5b8ff9',
    icon: dataSource,
    key: 'service',
    path: '/setting/management',
    actionName: 'management',
  },
  {
    title: '团队和用户管理',
    desc: '配置平台设计用户和部门的管理',
    iconColor: '#ff7c50',
    icon: permission, // 占位
    key: 'permission',
    path: '/setting/permission',
    actionName: 'permission',
  },
  {
    title: '访问控制',
    desc: '管理用户权限与部门资源访问',
    iconColor: '#ff7c50',
    icon: access_control, // 占位
    key: 'access-control',
    path: '/setting/access-control',
    actionName: 'access-control',
  },
  {
    title: '系统设置',
    desc: '配置平台基础参数与功能选项',
    iconColor: '#ff7c50',
    icon: setting, // 占位
    key: 'permission',
    path: '/setting/system-settings',
    actionName: 'system',
  },
  {
    title: '服务配置',
    desc: '管理平台资源服务与作业流服务',
    iconColor: '#ff7c50',
    icon: setting, // 占位
    key: 'service-config',
    path: '/setting/service-config',
    actionName: 'service-config',
  },
  {
    title: '模板管理',
    desc: '管理平台Java、Python、Shell脚本模板',
    iconColor: '#ff7c50',
    icon: setting,
    key: 'templateManager',
    path: '/setting/template-manager',
    actionName: 'service-config',
  },
])

function handleCardClick(item: CardItem) {
  router.push(item.path)
}
</script>

<style lang="less" scoped>
@import '@/assets/main.less';
@import './page.less';

.set-config {
  width: 100%;
  height: ~'calc(100vh - @{layout-header-height})';
  overflow-y: auto;
  // background: #fafbfc;
  box-sizing: border-box;
  &__title {
    font-size: 18px;
    font-weight: 600;
    color: #222;
    margin-bottom: 8px;
  }
  &__card-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 430px));
    gap: 16px 24px;
    width: 100%;
    margin: 24px auto;
  }
}
</style>
