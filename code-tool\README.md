# 自动化指令手册

## 基于 Swagger 文档生成前端 API 层代码

### 配置，修改 `.env.local` 文件：

swagger 文档示例地址：<code>http://<span title="HOST" style="color: red; font-weight: bold">*************:8112</span>/?redirect=/login#/swaggerHome?fs-tenant=null&uid=<span style="color: green; font-weight: bold">67170e8fb15842e0645c1f2b</span>&formShare=0</code>

基于后端提供的 swagger 文档地址，提取 HOST 和 UID，替换配置文件中`HOST` 和 `UID`

<p><code>VITE_APP_API_URL='http://<span style="color: red; font-weight: bold">{HOST}</span>/flow/swagger/share?uid=<span style="color: green; font-weight: bold">{UID}</span>&fs-tenant=null'</code></p>

### 生成代码：

- `yarn generate:api`，生成所有接口代码
- `yarn generate:api modeName functionName functionName2 functionName3`，生成具体模块的接口

## 基于用户权限中心生成前端路由代码

### 本地启动框架，登录获取 uid, token:

1. 启动：`yarn dev`
2. 访问：[http://localhost:5173](http://localhost:5173) 登录
3. 打开 Chrome 控制台，找到 uid, token 字段的值

### 1. 配置，修改 `.env.local` 文件：

<div>
<div><code>VITE_APP_MENU_URL='http://**************:9087/fsAuthorityCenter/systemManage/getUserMenu'</code></div>
<div><code>VITE_APP_MENU_UERRID='<span style="color: red">UID<span>'</code></div>
<div><code>VITE_APP_MENU_TOKEN='<span style="color: red">TOKEN<span>'</code></div>
</div>

### 生成代码：

`yarn generate:router`
