<script lang="ts" setup>
import * as echarts from 'echarts'
import type { ECBasicOption } from 'echarts/types/dist/shared'
import Dragger from './extract-dragger/index.vue'
import { demoDataList, initLinkList } from '../knowledge-model/assets-data'

// eslint-disable-next-line @typescript-eslint/no-unused-vars
let timer: NodeJS.Timeout | null = null
let myCharts: any = null

const props = defineProps<{ extractData: any; sampleMap?: any }>()
const currentEchartsData = ref()
const chartRef = ref(null)
const contentRef = ref<HTMLElement | null>(null)
const draggerRef = ref<{ showDrawer: () => void } | null>(null)

const option = reactive<ECBasicOption>({
  animationDurationUpdate: 1000, //数据更新动画的时长。
  animationEasingUpdate: 'quinticInOut', //数据更新动画的缓动效果。
  stateAnimation: {
    duration: 300,
    easing: 'cubicOut',
  },
  series: [
    {
      type: 'graph',
      layout: 'force',
      draggable: true,
      symbolSize: 20,
      autoMultLines: true,
      autoSelf: true,
      roam: true,
      label: {
        show: true,
        formatter: function (param) {
          return param?.data?.name
        },
      },
      edgeSymbol: ['none', 'arrow'],
      edgeSymbolSize: [10, 10],
      edgeLabel: {
        show: true,
        fontSize: 14,
        position: 'middle',
        formatter: function (param) {
          console.log('param', param)

          return param?.data?.label
        },
      },
      // data: props.extractData.demoDataListData || [],
      // links: props.extractData.initLinkListData || [],
      data: props.extractData.demoDataListData || [],
      links: props.extractData.initLinkListData || [],
      emphasis: {
        focus: 'adjacency',
        lineStyle: {
          color: '#FF6B6B', // 自环颜色
          width: 3,
          curveness: 0.3,
        },
      },
      force: {
        repulsion: 100,
        edgeLength: 90,
        friction: 0.6,
        layoutAnimation: true,
      },
      zoom: 2,
      scaleLimit: {
        min: 1,
        max: 5,
      },
      left: 10,
      right: 10,
      top: 10,
      bottom: 10,
    },
  ],
})
const showDrawerHandler = (params: any) => {
  console.log('数据', params)
  currentEchartsData.value = params.data
  draggerRef.value?.showDrawer?.()
}
const initRelationshipChart = () => {
  myCharts = echarts.init(chartRef.value)
  myCharts.setOption(option)
  myCharts.on('click', showDrawerHandler)
}

watch(
  () => props.extractData,
  (newVal) => {
    if (newVal.demoDataListData.length) {
      option.series[0].data = newVal.demoDataListData || []
      option.series[0].links = newVal.initLinkListData || []

      initRelationshipChart()
    }
  },
  { immediate: true, deep: true },
)

const resizeHandler = () => {
  console.log('窗口变化啦')
  timer = setTimeout(function () {
    myCharts.resize()
    timer = null
  }, 200)
}

onMounted(() => {
  //随着屏幕大小调节图表
  window.addEventListener('resize', resizeHandler)
})
onUnmounted(() => {
  window.removeEventListener('resize', resizeHandler)
  myCharts?.dispose()
})
</script>

<template>
  <div class="extract-wrap">
    <div class="extract-container" ref="chartRef"></div>
    <Dragger ref="draggerRef" :sampleMap="sampleMap" :currentEchartsData="currentEchartsData" />
  </div>
</template>

<style lang="less" scoped>
.extract-wrap {
  width: 100%;
  height: 100%;
  background: #fff;
  position: relative;
  right: 0;
  border-radius: 4px;
}
.extract-container {
  box-sizing: border-box;
  overflow: hidden;
  // width: 100%;
  height: 100%;
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.1);
}
</style>
