<template>
  <div class="grid">
    <Grid @update:columnData="getColumnDataHandle" ref="gridRef"></Grid>
    <div class="footer">
      <a-button @click="saveDraft" style="margin-right: 16px">保存草稿</a-button>
      <a-button @click="generateCode" type="primary" :loading="confirmLoading">生成代码</a-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { Grid } from '@fs/fs-components'
import { formatting, getdraft } from '../code-generation'

interface gridProps {
  routerPath: string
}

const props: gridProps = withDefaults(defineProps<gridProps>(), {})

const columnData = ref<any>({})
const oldColumnData = ref({})
const gridRef = ref()
const confirmLoading = ref(false)

const emit = defineEmits(['saveDraft', 'generateCode'])

function getColumnDataHandle(data: type) {
  columnData.value = data
}

function saveDraft(state = true) {
  emit('saveDraft', { ...columnData.value, pageType: 'grid' }, state)
}

async function generateCode() {
  try {
    confirmLoading.value = true
    const content = await getAllGridCode()
    confirmLoading.value = false
    emit('generateCode', { content })
    saveDraft(false)
  } catch (error) {
    console.log('生成代码错误：' + error)
  }
}

async function getAllGridCode() {
  const str =
    `
  <template>
    ${getComponent(columnData.value.children)}
   </template>
  ` +
    '<' +
    `script setup lang='ts'>
    import { ref } from 'vue'

     </` +
    `script>
    <style scoped lang="less">
    .content{
    display: flex;
    align-items: center;
    text-align: center;
    }
    </style>
    `
  const { data } = await formatting(str)
  return data.data
}

function getComponent(list: any) {
  return list
    .map((item: any) => {
      const newData = JSON.parse(JSON.stringify(item.comProps))
      delete newData.style
      const style = item.comProps.style
      return `
    <${item.compnentName} v-bind='${JSON.stringify(newData)}'  ${item.compnentName === 'div' ? `class='content' :style='${style ? JSON.stringify(style) : '{}'}'` : ''}>
      ${item.children ? getComponent(item.children) : ''}
  ${item.compnentName === 'div' ? '内容区' : ''}
    </${item.compnentName}>
        `
    })
    .join('') // 不用逗号分隔，直接拼接成一个完整的字符串
}

onMounted(async () => {
  try {
    const val = await getdraft({ name: props.routerPath })
    const pageType = val?.data?.data?.pageType ?? ''
    // 草稿箱为表格类型时 读取草稿数据
    if (pageType && pageType === 'grid') {
      if (val?.data?.data && JSON.stringify(val?.data?.data) !== '{}') {
        // oldColumnData.value = val.data.data
        gridRef.value.setcolumnData(val.data.data)
      }
    }
  } catch (error) {
    console.log('%c 🍤 error: ', 'font-size:12px;background-color: #B03734;color:#fff;', error)
  }
})
</script>
<style scoped lang="less">
.grid {
  width: 100%;
  .footer {
    display: flex;
    justify-content: center;
    margin-top: 20px;
  }
}
</style>
