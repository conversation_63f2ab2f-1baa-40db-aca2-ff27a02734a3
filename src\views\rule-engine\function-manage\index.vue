<template>
  <div class="function-manage-container">
    <Title title="数据模型规则管理" desc="编辑、创建数据模型的函数，查询函数列表">
      <template #right>
        <Space>
          <Button type="primary" @click="handleAdd">新增</Button>
          <Button type="primary" @click="handleRefresh" :loading="loading">刷新</Button>
        </Space>
      </template>
    </Title>
    <div flex="~ justify-between">
      <Space>
        <Button type="text" @click="handleBack">
          <template #icon>
            <ArrowLeftOutlined />
          </template>
        </Button>
        <h1>函数列表</h1>
      </Space>
    </div>
    <Table :columns="columns" :dataSource="dataSource" :loading="loading">
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <div>
            <Button type="link" size="small" @click="handleEdit(record)">编辑</Button>
            <Popconfirm title="确定删除吗？" @confirm="handleDelete(record)">
              <Button type="link" size="small" danger>删除</Button>
            </Popconfirm>
          </div>
        </template>
      </template>
    </Table>
    <AddFunctionModal
      v-model:open="addModalVisible"
      :data="currentFunction"
      :title="!!currentFunction ? '编辑函数' : '新增函数'"
      @ok="handleAddOk"
      @cancel="handleCancel"
    />
  </div>
</template>

<script setup lang="ts">
import { Table, Tag, Button, message, Popconfirm, Space } from 'ant-design-vue'
import { useRoute } from 'vue-router'
import { deleteFunction, getFunctionList } from '../service'
import type { FunctionItem } from '../data'
import { columns } from './columns'
import AddFunctionModal from '../components/add-function-modal.vue'
import { ArrowLeftOutlined } from '@ant-design/icons-vue'
const route = useRoute()
const projectId = route.query.projectId
const dataSource = ref<FunctionItem[]>([])
const addModalVisible = ref(false)
const currentFunction = ref<FunctionItem>()

const loading = ref(false)
const fetchData = async () => {
  if (!projectId) {
    message.warning('请先选择项目')
    return
  }
  try {
    loading.value = true
    const res = await getFunctionList(projectId as string)
    dataSource.value = res.data || []
    loading.value = false
  } catch (error) {
    console.error('获取函数列表失败:', error)
  } finally {
    loading.value = false
  }
}
const router = useRouter()
const handleBack = () => {
  router.back()
}
const handleEdit = (record: FunctionItem) => {
  currentFunction.value = record
  addModalVisible.value = true
}
const handleDelete = async (record: FunctionItem) => {
  try {
    const res = await deleteFunction(record.autoId!)
    if (res.code === '000000') {
      message.success('删除成功')
      await fetchData()
    } else {
      message.error(res.msg || '删除失败')
    }
  } catch (error) {
    console.error('删除失败:', error)
  }
}
const handleAdd = () => {
  addModalVisible.value = true
}
const handleRefresh = async () => {
  await fetchData()
}
const handleAddOk = async () => {
  addModalVisible.value = false
  currentFunction.value = void 0
  await fetchData()
}
const handleCancel = () => {
  currentFunction.value = void 0
  addModalVisible.value = false
}
onMounted(() => {
  fetchData()
})
</script>

<style scoped>
.function-manage-container {
}
</style>
