<template>
  <a-modal
    :open="open"
    @ok="modalHandleOk"
    :title="modalType === 'edit' ? '编辑' : '新增'"
    @cancel="cancel"
    width="800px"
    :confirm-loading="confirmLoading"
  >
    <a-form
      ref="formRef"
      :model="form"
      :rules="rules"
      :label-col="labelCol"
      :wrapper-col="wrapperCol"
    >
      <a-form-item label="序号" name="sortId">
        <a-input v-model:value="form.sortId" placeholder="请输入序号" :disabled="disabled" />
      </a-form-item>
      <a-form-item label="系统名称" name="systemName">
        <a-input v-model:value="form.systemName" placeholder="请输入系统名称" />
      </a-form-item>
      <a-form-item label="系统类别" name="systemType">
        <a-select
          v-model:value="form.systemType"
          show-search
          placeholder="请选择系统类别"
          :options="form.systemTypeOptions"
          :field-names="{ label: 'dataName', value: 'dataId' }"
          :filter-option="filterOption"
          @change="handleChange"
        ></a-select>
      </a-form-item>
      <a-form-item label="系统标识" name="systemCode">
        <a-input
          v-model:value="form.systemCode"
          :disabled="modalType === 'edit'"
          placeholder="请输入系统标识"
        />
      </a-form-item>
      <a-form-item label="是否启用" name="isUse">
        <a-radio-group v-model:value="form.isUse" placeholder="请选择是否启用">
          <a-radio :value="1">是</a-radio>
          <a-radio :value="0">否</a-radio>
        </a-radio-group>
      </a-form-item>
    </a-form>
  </a-modal>
</template>
<script lang="ts" setup>
import { message } from 'ant-design-vue/es/components'
import { defineEmits, defineProps, defineExpose, ref } from 'vue'
import type { Rule, FormInstance } from 'ant-design-vue/es/form'
import { updateSystem, addSystem } from '@/api/services/permission'
interface FormState {
  isUse: string
  sortId: string
  systemCode: string
  systemName: string
  systemType: string | undefined
  systemId?: string
  systemTypeOptions?: Data[]
}
interface Data {
  dataId: string
  dataName: string
}
defineProps({
  open: {
    type: Boolean,
    default: false,
  },
  disabled: Boolean,
})

const emits = defineEmits(['modalHandleOk', 'modalCancel'])
const formRef = ref<FormInstance>()
const labelCol = { span: 5 }
const wrapperCol = { span: 13 }
const modalType = ref<string>('')
const confirmLoading = ref<boolean>(false)
const form = ref<FormState>({
  isUse: '',
  sortId: '',
  systemCode: '',
  systemName: '',
  systemType: undefined,
  systemTypeOptions: [],
})

const rules: Record<string, Rule[]> = {
  sortId: [
    { required: true, message: '请序号', trigger: 'blur' },
    { pattern: /^\d{1,8}$/, message: '请输入8位以内的数字', trigger: 'change' },
  ],
  isUse: [{ required: true, message: '请选择是否启用', trigger: 'blur' }],
  systemCode: [{ required: true, message: '请输入系统标识', trigger: 'blur' }],
  systemName: [{ required: true, message: '请输入系统名称', trigger: 'blur' }],
  systemType: [{ required: true, message: '请选择系统类别', trigger: 'blur' }],
}

const handleChange = (value: string) => {
  form.value.systemType = value
}

const filterOption = (input: string, option: any) => {
  return option.value.toLowerCase().indexOf(input.toLowerCase()) >= 0
}

function modalHandleOk() {
  formRef.value &&
    formRef.value
      .validate()
      .then(() => {
        const { isUse, sortId, systemCode, systemId, systemName, systemType } = form.value
        const data = { isUse, sortId, systemCode, systemName, systemType }
        confirmLoading.value = true
        if (modalType.value === 'edit') {
          //@ts-ignore
          data['systemId'] = systemId
          updateSystem(data)
            .then(() => {
              emits('modalHandleOk', form.value)
              message.success('编辑成功!')
            })
            .finally(() => {
              confirmLoading.value = false
            })
        } else {
          addSystem(data)
            .then(() => {
              emits('modalHandleOk')
              message.success('新增成功!')
            })
            .finally(() => {
              confirmLoading.value = false
            })
        }
      })
      .catch((error: any) => {
        console.log(error)
      })
}

function cancel() {
  emits('modalCancel')
}

function show(type: string, data?: Record<string, any>) {
  resetForm()
  modalType.value = type
  if (type === 'edit') {
    editForm(data)
  } else if (type === 'add') {
    form.value.systemTypeOptions = data?.systemTypeOptions
  }
}

const resetForm = () => {
  formRef.value?.resetFields()
  form.value = {
    isUse: '',
    sortId: '',
    systemCode: '',
    systemName: '',
    systemType: '',
    systemTypeOptions: [],
  }
}

function editForm(data?: any) {
  if (data) {
    const copyData = JSON.parse(JSON.stringify(data))
    // 转换数据类型显示
    copyData['isUse'] = copyData?.isUseName === '是' ? 1 : 0
    copyData['systemType'] = String(copyData?.systemType)
    form.value = copyData
  }
}
defineExpose({ show })
</script>
