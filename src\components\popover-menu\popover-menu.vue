<template>
  <a-dropdown v-model:open="visiblePopover" title="" placement="bottom" trigger="click">
    <div @click="visiblePopover = true">
      <slot name="trigger"></slot>
    </div>
    <template #overlay>
      <a-menu class="rela-menu">
        <a-menu-item
          v-for="(item, index) in menuList"
          :key="index"
          :class="relaKey === item.value ? 'menu-item menu-item-active' : 'menu-item'"
          @click="handleClick(item.value)"
        >
          <a href="javascript:;">
            <template v-if="mode === 'default'">
              {{ item.label }}
            </template>
            <template v-else>
              <span class="field-type-icon">{{ item.columnType }}</span>
              <span class="filed_en">{{ item.title }}</span>
              <a-tooltip placement="right">
                <template #title>{{ item.comment }}</template>
                <span class="filed_zh">{{ item.comment || '-' }}</span>
              </a-tooltip>
            </template>
          </a>
        </a-menu-item>
      </a-menu>
    </template>
    <DownOutlined />
  </a-dropdown>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { DownOutlined } from '@ant-design/icons-vue'

const props = defineProps({
  mode: {
    type: String,
    default: 'default',
  },
  menuList: {
    type: Array<any>,
    default: () => [],
  },
  handleClickCallback: {
    type: Function,
    default: () => {},
  },
  index: {
    type: Number,
    default: 0,
    required: false,
  },
  joinIndex: {
    type: Number,
    default: 0,
    required: false,
  },
})

const visiblePopover = ref(false)
const relaKey: any = ref('left')

const handleClick = (key: string) => {
  relaKey.value = key
  visiblePopover.value = false
  props.handleClickCallback && props.handleClickCallback(key, props.index, props.joinIndex)
}
</script>

<style scoped lang="less">
.rela-menu {
  max-height: 60vh;
  overflow-y: scroll;
  :deep(.menu-item) {
    // padding: 10px 25px;
    transition: 0.1s all;
    font-weight: bold;
    // color: #fff;
    color: #509ee3;
    margin-bottom: 2px;
  }
  :deep(.menu-item):hover {
    color: #509ee3;
    background-color: #edf2f5 !important;
    color: #fff !important;
  }
  :deep(.menu-item-active),
  :deep(.menu-item-active:hover) {
    color: #fff !important;
    background-color: #509ee3 !important;
    color: #fff !important;

    span {
      color: #fff !important;
    }

    .field-type-icon {
      font-size: 10px;
      border-color: #fff;
    }
  }
}

.field-type-icon {
  display: inline-block;
  width: 46px;
  height: 12px;
  text-align: center;
  line-height: 12px;
  font-size: 10px;
  border: 1px solid #509ee3;
  border-radius: 4px;
  color: #509ee3;
  background-color: rgba(80, 158, 227, 0.1);
  padding: 1px;
  font-weight: bold;
  vertical-align: middle;
}
.filed_en {
  display: inline-block;
  width: 120px;
  margin: 0 6px;
  white-space: nowrap; /* 不换行 */
  overflow: hidden; /* 超出部分隐藏 */
  text-overflow: ellipsis; /* 显示省略号 */
  vertical-align: middle;
  font-weight: bold;
  color: #333;
}
.filed_zh {
  display: inline-block;
  width: 120px;
  margin: 0 6px;
  white-space: nowrap; /* 不换行 */
  overflow: hidden; /* 超出部分隐藏 */
  text-overflow: ellipsis; /* 显示省略号 */
  vertical-align: middle;
  color: #666;
}
</style>
