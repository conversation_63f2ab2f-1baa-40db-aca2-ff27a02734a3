<template>
  <a-radio-group v-model:value="modelValue">
    <a-radio
      :style="radioStyle"
      :value="item.value"
      v-for="item in data.options"
      :key="item.value"
      >{{ item.label }}</a-radio
    >
  </a-radio-group>
</template>

<script setup lang="ts">
defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
})

const modelValue = defineModel()

const radioStyle = {
  display: 'flex',
  height: '30px',
  lineHeight: '30px',
}
</script>

<style lang="less" scoped></style>
