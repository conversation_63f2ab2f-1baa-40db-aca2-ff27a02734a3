<template>
  <Modal
    :title="title"
    :open="open"
    @ok="handleOk"
    @cancel="handleCancel"
    :confirmLoading="confirmLoading"
    destroy-on-close
    width="80%"
  >
    <Form
      ref="formRef"
      :model="formState"
      :label-col="{ span: 3 }"
      :wrapper-col="{ span: 20 }"
      :rules="rules"
    >
      <FormItem label="名称" name="name">
        <Input v-model:value="formState.name" placeholder="请输入函数名称" />
      </FormItem>
      <FormItem label="版本" name="version">
        <Input v-model:value="formState.version" placeholder="请输入版本号" />
      </FormItem>
      <FormItem label="标签" name="tag">
        <Input v-model:value="formState.tag" placeholder="请输入标签" />
      </FormItem>
      <FormItem label="规则表达式" name="expression">
        <RadioGroup :value="formState.ruleType" name="radioGroup">
          <Radio :value="0" @click="handleRuleTypeChange(0)">手动输入</Radio>
          <Radio :value="1" @click="handleRuleTypeChange(1)">可视化</Radio>
        </RadioGroup>
        <template v-if="formState.ruleType === 0">
          <params-table v-model="formState.paramsInfo"></params-table>
          <VAceEditor
            v-model:value="formState.expression!"
            lang="javascript"
            theme="github"
            :options="editorOptions"
            style="height: 300px; width: 100%"
          />
        </template>
        <template v-if="formState.ruleType === 1">
          <decision-table
            ref="decisionTableRef"
            :generalRuleData="generalRuleData"
          ></decision-table>
        </template>
      </FormItem>
      <FormItem label="状态" name="status">
        <Select v-model:value="formState.status" placeholder="请选择状态">
          <SelectOption value="active">启用</SelectOption>
          <SelectOption value="inactive">禁用</SelectOption>
        </Select>
      </FormItem>
    </Form>
  </Modal>
</template>

<script setup lang="ts">
import type { RuleItem } from '../data'
import {
  Form,
  FormItem,
  Input,
  message,
  Modal,
  Select,
  SelectOption,
  RadioGroup,
  Radio,
} from 'ant-design-vue'
import type { FormInstance, Rule } from 'ant-design-vue/es/form'
import { addRule, updateRule } from '../service'
import { VAceEditor } from 'vue3-ace-editor'
import 'ace-builds/src-noconflict/mode-javascript' // 导入文本模式
import 'ace-builds/src-noconflict/theme-github' // 导入github主题
import decisionTable from './decision/decision-table.vue'
import paramsTable from './params-table.vue'
import { ref, watch } from 'vue'

const props = defineProps<{
  title: string
  data?: RuleItem
}>()

const formRef = ref<FormInstance>()
const decisionTableRef = ref<typeof decisionTable>()
const generalRuleData = ref({})
const getOriginalData = () => {
  return {
    expression: '',
    name: '',
    tag: '',
    version: '',
    status: 'active',
    ruleType: 0,
    paramsInfo: [],
    visualJson: '',
  }
}
const formState = ref<RuleItem>(getOriginalData())
const rules = computed<Record<string, Rule[]>>(() => ({
  name: [{ required: true, message: '请输入规则名称' }],
  expression: [{ required: formState.value.ruleType === 0, message: '请输入规则表达式' }],
}))

const open = defineModel<boolean>('open', { required: true })
const confirmLoading = ref(false)
const route = useRoute()
const projectId = route.query.projectId
const emit = defineEmits<{
  (e: 'ok', values: RuleItem): void
  (e: 'cancel'): void
}>()
const editorOptions = ref({
  fontSize: '14px', // 设置字体大小
  showPrintMargin: false, // 是否显示打印边距
})
watch(
  () => props.data,
  (data: any) => {
    if (data) {
      // 复制对象到表单
      formState.value = { ...data }
      formState.value.paramsInfo = data.paramsInfo.map((item: any) => {
        return {
          ...item,
          keyName: item.key,
        }
      })
      if (data?.visualJson) {
        generalRuleData.value = JSON.parse(data.visualJson)
      }
      if (!data.expression) formState.value.expression = ''
    } else {
      formState.value = getOriginalData()
      generalRuleData.value = {}
    }
  },
  { immediate: true },
)
watch(
  () => formState.value.ruleType,
  () => {
    formRef.value?.clearValidate('expression')
  },
)
const handleCreate = async () => {
  try {
    const obj = {
      ...formState.value,
    }
    obj.paramsInfo =
      formState.value?.paramsInfo &&
      formState.value?.paramsInfo.map((item: any) => {
        return {
          ...item,
          key: item.keyName,
        }
      })
    const res = await addRule({ ...obj, projectId: Number(projectId) })
    if (res.code === '000000') {
      message.success('新增成功')
      emit('ok', { ...formState.value })
    } else {
      message.error(res.msg || '新增失败')
    }
    formRef.value?.resetFields()
    formState.value = getOriginalData()
  } catch (error) {
    console.error('新增失败:', error)
  }
}
const handleUpdate = async () => {
  try {
    const obj = {
      ...formState.value,
    }
    obj.paramsInfo =
      formState.value?.paramsInfo &&
      formState.value?.paramsInfo.map((item: any) => {
        return {
          ...item,
          key: item.keyName,
        }
      })
    const res = await updateRule(obj)
    if (res.code === '000000') {
      message.success('更新成功')
      emit('ok', { ...formState.value })
    } else {
      message.error(res.msg || '更新失败')
    }
  } catch (error) {
    console.error('更新失败:', error)
  }
}
const isEdit = computed(() => !!props.data)
const handleOk = async () => {
  try {
    console.log('formState', formState.value)
    await formRef.value?.validate()
    if (formState.value.ruleType === 1) {
      const val = await decisionTableRef.value?.nextStep()
      formState.value.visualJson = val ? JSON.stringify(val) : null
      if (!val) return
    }
    confirmLoading.value = true
    if (isEdit.value) {
      await handleUpdate()
    } else {
      await handleCreate()
    }
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    confirmLoading.value = false
  }
}
const handleCancel = () => {
  formRef.value?.resetFields()
  formState.value = getOriginalData()
  open.value = false
  emit('cancel')
}
const handleRuleTypeChange = (newType: number) => {
  if (formState.value.ruleType === newType) return

  Modal.confirm({
    title: '切换规则类型',
    content: '切换规则类型将清空当前已输入的内容，是否确认切换？',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      formState.value.ruleType = newType
      // 清空相关数据
      if (newType === 0) {
        formState.value.expression = ''
        formState.value.paramsInfo = []
      } else {
        generalRuleData.value = {}
      }
    },
  })
}
</script>

<style scoped></style>
