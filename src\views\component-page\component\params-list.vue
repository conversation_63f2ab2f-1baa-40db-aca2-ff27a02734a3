<template>
  <div class="controls" v-if="type === 'Body'">
    <a-select ref="select" v-model:value="data!.type" style="width: 120px" @change="typeChange">
      <a-select-option value="JSON">JSON</a-select-option>
      <a-select-option value="Form-Data">Form Data</a-select-option>
    </a-select>
  </div>
  <div class="controls" v-if="!isJson">
    <a-button :icon="h(PlusOutlined)" @click="addItem">添加</a-button>
  </div>
  <template v-if="isJson">
    <v-ace-editor
      v-model:value="jsonValue"
      lang="json"
      theme="chrome"
      :options="{ useWorker: true }"
      :style="{ height: '450px' }"
      @input="debouncedHandleInput"
    />
  </template>
  <a-table :dataSource="dataSource" :columns="columns" size="small" :pagination="false" v-else>
    <template #bodyCell="{ column, record, index }">
      <template v-if="column.key === 'name'">
        <template v-if="!isCommonHead">
          <a-auto-complete
            v-model:value="record[column.key]"
            :options="options"
            style="width: 200px"
            placeholder="input here"
        /></template>

        <a-input v-else type="text" v-model:value="record[column.key]" placeholder="请输入value" />
      </template>
      <template v-else-if="column.key === 'value'">
        <div class="display-flex">
          <template
            v-if="type === 'Body' && data!.type === 'Form-Data' && record['type'] !== 'Text'"
            ><a-upload
              style="width: 100%"
              :file-list="fileList"
              :before-upload="(file: any) => beforeUpload(file, record)"
              :showUploadList="false"
            >
              <a-button style="width: 100%" @click="uploadClick">
                <span class="file-name" :title="record['fileName'] ?? '选择文件'">
                  <upload-outlined v-if="!record['fileName']"></upload-outlined>
                  {{ record['fileName'] ?? '选择文件' }}</span
                >
              </a-button>
            </a-upload></template
          >
          <template v-else>
            <a-input type="text" v-model:value="record[column.key]" placeholder="请输入value" />
          </template>
          <a-dropdown :trigger="['click']" v-if="type === 'Body'">
            <CaretDownOutlined />
            <template #overlay>
              <a-menu @click="changeType($event, record)" style="width: 100px">
                <a-menu-item key="Text">
                  <a href="javascript:;">Text</a>
                </a-menu-item>
                <a-menu-item key="File">
                  <a href="javascript:;">File</a>
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </div>
      </template>
      <template v-else-if="column.key === 'action'">
        <a-button type="link" @click="delItem(index)">删除</a-button>
      </template>
    </template>
  </a-table>

  <a-modal :open="open" title="提示" width="800px" @cancel="open = false" @ok="typeSubmit">
    切换会清空当前body数据，是否确认切换
  </a-modal>
</template>

<script setup lang="ts">
import { ref, h, computed, nextTick } from 'vue'
import { PlusOutlined, CaretDownOutlined, UploadOutlined } from '@ant-design/icons-vue'
import { debounce } from '@/utils'
import { VAceEditor } from 'vue3-ace-editor'
import 'ace-builds/src-noconflict/mode-json' // Load the language definition file used below
import 'ace-builds/src-noconflict/theme-chrome' // Load the theme definition file used below

import ace from 'ace-builds'
import workerJavascript from 'ace-builds/src-noconflict/worker-json?url' // For vite
ace.config.setModuleUrl('ace/mode/json_worker', workerJavascript)

interface Data {
  type: string
  options: Array<any>
  list?: any[]
  data?: Record<string, any>
  isCommonHead?: boolean
}

const props: Data = withDefaults(defineProps<Data>(), {
  options: () => [],
})
const jsonValue = ref(JSON.stringify(props?.data?.value || '', null, 2))
const oldType = ref(props?.data?.type)
const open = ref(false)
const fileList = ref<any[]>([])
const selectRecord = ref<any>({})

const emit = defineEmits(['update:list', 'update:data'])

const columns = [
  {
    title: 'Key',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: 'Value',
    dataIndex: 'value',
    key: 'value',
  },
  {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
    width: 100,
  },
]

const dataSource = computed({
  get: () => {
    if (props.type === 'Body') {
      return props?.data?.value
    }
    return props.list
  },
  set: (value) => {
    console.log('%c 🍺 value: ', 'font-size:12px;background-color: #FCA650;color:#fff;', value)
  },
})

const beforeUpload = (file: any, record: any) => {
  record.fileName = file.name
  fileList.value = [...(fileList.value || []), file]
  record.value = file
  selectRecord.value = file
  return false
}

const blurHandle = () => {
  try {
    console.log('previewValue')
    emit('update:data', { type: 'JSON', value: JSON.parse(jsonValue.value) })
  } catch (error) {
    // 传递错误json数据 让接口抛出错误
    emit('update:data', { type: 'JSON', value: jsonValue.value })
    console.log('%c 🥨 error: ', 'font-size:12px;background-color: #B03734;color:#fff;', error)
    // this.jsonOutput = '无效的 JSON 格式'
  }
}

const debouncedHandleInput = debounce(blurHandle, 300)

const isJson = computed(() => {
  return props.type === 'Body' && props?.data?.type === 'JSON'
})

const typeChange = () => {
  open.value = true
  props.data!.type = oldType.value
}

const typeSubmit = () => {
  if (oldType.value === 'JSON') {
    props.data!.type = 'Form-Data'
    oldType.value = 'Form-Data'
    if (jsonValue.value) {
      const obj = JSON.parse(jsonValue.value)
      let arr: any[] = []
      Object.keys(obj).forEach((key) => {
        arr.push({
          name: key,
          value: obj[key],
          type: 'Text',
        })
      })
      emit('update:data', { type: 'Form-Data', value: arr })
    } else {
      emit('update:data', { type: 'Form-Data', value: [] })
    }
    jsonValue.value = '{}'
  } else {
    props.data!.type = 'JSON'
    oldType.value = 'JSON'
    const obj: any = {}
    if (dataSource.value.length > 0) {
      dataSource.value.forEach((item: any) => {
        obj[item.name] = item.type === 'File' ? '' : item.value
      })
    }
    emit('update:data', { type: 'JSON', value: obj })
    jsonValue.value = JSON.stringify(obj, null, 2)
  }
  open.value = false
}

const addItem = () => {
  dataSource.value.push({
    name: '',
    value: '',
    type: 'Text',
  })
}

const changeType = (data: any, record: any) => {
  const key = data.key
  record.type = key
}

const uploadClick = (data: any) => {
  selectRecord.value = data
}

const delItem = (index: number) => {
  dataSource.value.splice(index, 1)
}
</script>
<style scoped lang="less">
.controls {
  margin-bottom: 15px;
}
.display-flex {
  display: flex;
}
.file-name {
  display: inline-block;
  width: 100px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
