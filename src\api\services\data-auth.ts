import axios from './axios-instances'
import service from '../index'

// 1.AU001-获取角色授权项目列表
export function reqGetRoleProjectList(params: any = {}): Promise<any> {
  return service({
    method: 'get',
    url: '/api/sodataFlink/authorityMange/getRoleAthorityProjectList',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 2.AU002-获取角色授权数据源列表
export function reqGetDataSourceList(params: any = {}): Promise<any> {
  return service({
    method: 'get',
    url: '/api/sodataFlink/authorityMange/getRoleAthorityDscList',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 3.AU003-保存角色授权资源
export function reqSaveRoleAuth(data: any = {}): Promise<any> {
  return service({
    method: 'post',
    url: '/api/sodataFlink/authorityMange/saveRoleAthorityResource',
    data,
  })
}
// 4.AU004-获取角色已授权资源
export function reqGetHasAuthIds(params: any = {}): Promise<any> {
  return service({
    method: 'get',
    url: '/api/sodataFlink/authorityMange/getRoleCheckAthrorityResr',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 5.获取模型的授权列表
export function reqGetModelList(params: any = {}): Promise<any> {
  return axios({
    method: 'get',
    url: '/ajax-api/2.0/mlflow/privileges_models_list_by_role',
    params,
  })
}

// 5.获取实验授权列表
export function reqGetStudyList(params: any = {}): Promise<any> {
  return axios({
    method: 'get',
    url: '/ajax-api/2.0/mlflow/privileges_experiments_list_by_role',
    params,
  })
}

// 6.保存授权列表
export function reqSaveModel(data: any = {}): Promise<any> {
  return service({
    method: 'post',
    url: '/ajax-api/2.0/mlflow/privileges_models_grant_by_role',
    data,
  })
}
// 7.保存实验列表
export function reqSaveExperiments(data: any = {}): Promise<any> {
  return service({
    method: 'post',
    url: '/ajax-api/2.0/mlflow/privileges_experiments_grant_by_role',
    data,
  })
}
