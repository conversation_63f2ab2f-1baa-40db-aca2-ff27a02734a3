<script lang="ts" setup>
import { ref, reactive, computed } from 'vue'
import type { Ref, UnwrapRef } from 'vue'
import { PlusOutlined } from '@ant-design/icons-vue'
import {
  dlgColumns,
  indicatorNameArr1,
  indicatorNameArr2,
  indicatorType,
  type DataItem,
} from '../data'

const emit = defineEmits(['getTagsFn'])

const open = ref<boolean>(false)
const radioValue = ref<number>(1)
const count = computed(() => dataSource.value.length + 1)
const indicator = ref<number>(1)

const editableData: UnwrapRef<Record<string, DataItem>> = reactive({})

const initEditData = () => {
  // 初始化editableData
  Object.assign(
    editableData,
    dataSource.value.reduce(
      (acc, item) => {
        acc[item.key] = { ...item }
        return acc
      },
      {} as Record<string, DataItem>,
    ),
  )
}
onMounted(() => {
  initEditData()
})

const showModal = () => {
  open.value = true
}

const handleOk = (e: MouseEvent) => {
  emit('getTagsFn', toRaw(dataSource.value))
  open.value = false
}

const dataSource: Ref<DataItem[]> = ref([
  {
    key: 0,
    indicatorType: '投入',
    indicatorName: '实际开放床位数',
    indicatorValue: 1,
  },
  {
    key: 1,
    indicatorType: '产出',
    indicatorName: '平均住院天数',
    indicatorValue: 2,
  },
  {
    key: 2,
    indicatorType: '投入',
    indicatorName: '医护人数',
    indicatorValue: 3,
  },
])

const onDelete = (key: number) => {
  dataSource.value = dataSource.value.filter((item) => item.key !== key)
}

const findItem = (key: number, type: keyof DataItem) => {
  return dataSource.value.find((item) => item.key === key)![type]
}

const handleSelect = (value: string, key: number) => {
  console.log('%c [  ]: ', 'color: #bf2c9f; background: pink; font-size: 13px;', value, key)
  const item = dataSource.value.find((item) => item.key === key)
  if (item) {
    item.indicatorType = value
    item.indicatorName = value === '投入' ? '医护人数' : '住院人次(万)'
    editableData[key].indicatorType = value
    editableData[key].indicatorName = value === '投入' ? '医护人数' : '住院人次(万)'
  }
}

const handleAdd = () => {
  const newData = {
    key: count.value,
    indicatorType: '出院人数',
    indicatorName: '住院人次(万)',
    indicatorValue: 1,
  }
  dataSource.value.push(newData)
  initEditData()
}

defineExpose({
  showModal,
  radioValue,
})
</script>

<template>
  <a-modal style="width: 800px" centered v-model:open="open" title="选择指标参数" @ok="handleOk">
    <div class="scene-select-item">
      <span>场景选择：</span>
      <a-select v-model:value="indicator" placeholder="请选择指标类型" allow-clear>
        <a-select-option :value="1">综合有效性</a-select-option>
        <a-select-option :value="2">整体有效性</a-select-option>
      </a-select>
    </div>
    <div class="scene-select-item">
      <span>分析模式：</span>
      <a-radio-group v-model:value="radioValue">
        <a-radio :value="1">现状分析</a-radio>
        <a-radio :value="2">仿真分析</a-radio>
      </a-radio-group>
    </div>
    <div class="scene-select-item">
      <div>指标类型和参数设置：</div>
      <a-table
        bordered
        :data-source="dataSource"
        :columns="dlgColumns"
        :pagination="false"
        :scroll="{ y: 300 }"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'indicatorType'">
            <a-select
              :value="editableData[record.key].indicatorType"
              @change="handleSelect($event, record.key)"
              :options="indicatorType"
            />
          </template>
          <template v-else-if="column.dataIndex === 'indicatorName'">
            <a-select
              v-model:value="editableData[record.key].indicatorName"
              :options="record.indicatorType === '投入' ? indicatorNameArr1 : indicatorNameArr2"
            />
          </template>
          <template v-else-if="column.dataIndex === 'indicatorValue'">
            <div class="editable-cell">
              <a-input v-model:value="editableData[record.key].indicatorValue" />
            </div>
          </template>
          <template v-else-if="column.dataIndex === 'operation'">
            <a-popconfirm
              v-if="dataSource.length"
              title="Sure to delete?"
              @confirm="onDelete(record.key)"
            >
              <a style="color: #f00">删除</a>
            </a-popconfirm>
          </template>
        </template>
      </a-table>
      <a-button style="margin: 8px 0 50px" type="dashed" block @click="handleAdd">
        <PlusOutlined />
        Add sights
      </a-button>
    </div>
  </a-modal>
</template>

<style lang="less" scoped>
.scene-select-item {
  margin-bottom: 8px;
}
</style>
