<template>
  <div class="knowledge-builder">
    <a-tabs v-model:activeKey="activeKey" @change="handleKnowledgeTabChange">
      <template #leftExtra>
        <span type="link" @click="goBack" class="go-back-btn">
          <ArrowLeftOutlined />
        </span>
      </template>
      <a-tab-pane key="1" tab="知识任务"></a-tab-pane>
      <a-tab-pane key="2" tab="知识模型"></a-tab-pane>
      <a-tab-pane key="3" tab="模型推算"></a-tab-pane>
    </a-tabs>
    <router-view></router-view>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ArrowLeftOutlined } from '@ant-design/icons-vue'

const activeKey = ref('1')
const router = useRouter()
const route = useRoute()

const goBack = () => {
  router.replace({
    path: `/knowledge-home`,
  })
}

// 根据路由设置 activeKey
const setActiveKeyByRoute = () => {
  if (route.path.includes('knowledgeTask')) {
    activeKey.value = '1'
  } else if (route.path.includes('knowledgeModel')) {
    activeKey.value = '2'
  } else if (route.path.includes('calculation')) {
    activeKey.value = '3'
  }
}

// 初始化
setActiveKeyByRoute()

// 监听路由变化
watch(
  () => route.path,
  () => setActiveKeyByRoute(),
)

const handleKnowledgeTabChange = (key: string) => {
  if (key === '1') {
    router.replace({
      path: `/knowledgeBuilder/knowledgeTask`,
      query: { projectId: route.query?.projectId },
    })
  } else if (key === '2') {
    router.replace({
      path: `/knowledgeBuilder/knowledgeModel`,
      query: { projectId: route.query?.projectId },
    })
  } else if (key === '3') {
    router.replace({
      path: `/knowledgeBuilder/calculation`,
      query: { projectId: route.query?.projectId },
    })
  }
}

// 这里可以添加你的逻辑
</script>

<style lang="less" scoped>
.knowledge-builder {
  height: 100%;
  // padding: 4px 24px 0 24px;
}
.go-back-btn {
  color: var(--primary-color);
  // margin-left: 5px;
  margin-right: 10px;
  cursor: pointer;
}
</style>
