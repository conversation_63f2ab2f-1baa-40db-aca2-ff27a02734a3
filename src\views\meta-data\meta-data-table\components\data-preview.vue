<template>
  <div class="data-preview">
    <a-row justify="start" align="top" type="flex" :wrap="true" class="data-preview-row">
      <a-col :span="24" class="data-preview-col">
        <div class="content">
          <a-alert
            v-show="errorMsg !== null"
            message="无法预览数据"
            :description="errorMsg"
            type="error"
          />

          <!-- 数据预览内容 -->
          <div class="data-preview-table">
            <a-table
              v-show="errorMsg === null"
              class="data-table"
              :scroll="{ x: scrollWidth }"
              :expand-column-width="150"
              :dataSource="responseDataRef"
              :columns="columns"
            />
          </div>
        </div>
      </a-col>
    </a-row>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { useRoute } from 'vue-router'
import { queryDefaultableData } from '@/api/matedata/metadatamanagement/metadatamanagement'

const route = useRoute()

const props = defineProps<{
  tableData: {
    dbName: string
    id: number
    schema: string
    tabName: string
  }
}>()

const tableData = computed(() => props.tableData)

const responseDataRef = ref<Array<Record<string, string | number | boolean>>>([])
const columns = computed(() => {
  const firstItem = responseDataRef.value?.[0]
  if (!firstItem) {
    return []
  }

  const keys = Object.keys(firstItem)

  return (
    keys?.map((key, index) => ({
      title: key,
      dataIndex: key,
      key: key,
      ellipsis: true,
      fixed: index < 2 ? 'left' : undefined,
    })) ?? []
  )
})
const scrollWidth = computed(() => columns.value.length * 150)

const errorMsg = ref<string | null>(null)

type Param = Parameters<typeof queryDefaultableData>[0]
const fetchTableData = async (data: Param) => {
  try {
    const response = await queryDefaultableData(data)

    const obj = response.data.obj
    const dataObj = JSON.parse(obj) as { data: Array<Record<string, string | number | boolean>> }

    responseDataRef.value = dataObj.data

    // if (dataList.length === 1 && Reflect.has(dataList[0], 'msg')) {
    //   errorMsg.value = dataList[0].msg as string
    // } else {
    //   errorMsg.value = null
    // }
  } catch (error) {
    console.error('Error fetching table detail:', error)
  }
}

const loadingRef = ref<boolean>(false)

watch(
  tableData,
  (newVal) => {
    console.log('🚀 ~ watch ~ newVal:', newVal)
    fetchTableData(newVal)
  },
  { deep: true },
)
</script>

<style lang="less" scoped>
.data-preview {
  width: 100%;
  height: 100%;
}

.pagination {
  margin: 10px 0;
}

.data-table :deep(table) {
  min-width: 100%;
  width: auto;
}

.data-preview-table {
  width: 100%;
  height: auto;
  overflow-x: auto;
}
</style>
