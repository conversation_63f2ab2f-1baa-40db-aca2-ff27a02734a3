import { renderItemByCallCount } from './tools.js'
import prettier from 'prettier'
import parseBabel from 'prettier/parser-babel'
import parseHtml from 'prettier/parser-html'

export async function getTableTamplate(tableExample) {
  const list = renderItemByCallCount('formColumns', tableExample)
  const editDataHanlde = renderItemByCallCount('editData', tableExample)
  const editDataImport = renderItemByCallCount('editData', tableExample)
  const addDataHandle = renderItemByCallCount('addData', tableExample)
  const addDataImport = renderItemByCallCount('addData', tableExample)
  const delDataHanlde = renderItemByCallCount('addData', tableExample)
  const delDataImport = renderItemByCallCount('addData', tableExample)
  const str =
    `<template>
  <Table :columns="columns" :getData="${renderItemByCallCount('getData', tableExample)}" ref="tableRef" :searchFormState="form">
    <template #operate>
      <a-button type="primary" @click="addTableHandel">新增</a-button>
    </template>
    <template #search>
    ${renderItemByCallCount('searchColumns', tableExample).reduce(
      (acc, item) => acc + renderItem(item, 'form'),
      '',
    )}
    </template>
    <template #bodyCell="data">
      <template v-if="data.column.dataIndex === 'action'">
        <a-button type="link" @click="editTableHandle(data.record)">编辑</a-button>
      <a-popconfirm title="是否确认删除" @confirm="confirm(data.record)">
        <a-button type="link">删除</a-button>
      </a-popconfirm>
      </template>
      <template v-else>
        {{ data.value }}
      </template>
    </template>
  </Table>

  <a-modal
    v-model:open="modalValue"
    @ok="modalHandleOk"
    :title="modalType === 'edit' ? '编辑' : '新增'"
    @cancel="cancel"
    width="800px"
    :confirm-loading="confirmLoading"
  >
    <a-form
      ref="formRef"
      :model="modalForm"
      :label-col="labelCol"
      :wrapper-col="wrapperCol"
    >
${list.reduce((acc, item) => acc + renderItem(item), '')}
    </a-form>
  </a-modal>
</template>
` +
    '<' +
    `script setup lang='ts'>
import { ref } from 'vue'
import { Table, getPattern } from '@fs/fs-components'
import type { Rule } from 'ant-design-vue/es/form'
import { message } from 'ant-design-vue/es/components'
${renderItemByCallCount('getData', tableExample)}
${editDataImport}
${addDataImport}
${delDataImport}

const labelCol = { span: 5 };
const wrapperCol = { span: 13 };
const modalForm: Record<string, any> = ref({})
const modalValue = ref(false)
const modalType = ref<string>('')
const formRef = ref<HTMLFormElement | null>(null)
const confirmLoading = ref<boolean>(false);
const tableRef = ref<InstanceType<typeof Table> | null>(null);
const form: Record<string, any> = ref({
    ${list.reduce((cur, item) => {
      return cur + `${item.name}: ${item.type === 'select' ? 'undefined' : "''"},`
    }, '')}
});

const columns = ${JSON.stringify(renderItemByCallCount('getColumns', tableExample))}

function addTableHandel() {
  modalValue.value = true
  modalType.value = 'add'
}

function editTableHandle(data: any) {
  modalForm.value = data
  modalType.value = 'edit'
  modalValue.value = true
}

function cancel() {
  modalValue.value = false
  formRef.value?.resetFields()
}

async function confirm(data: any) {
  try {
    await ${delDataHanlde}(data);
    message.success("删除成功");
  } catch (error) {
    console.log(error);
  }
}

async function modalHandleOk() {
  try{
     if(!formRef.value) {
        console.error('获取form表单失败')
        return
      }
     await formRef.value.validate()
      confirmLoading.value = true
      let handle
      if (modalType.value === 'edit') {
        handle = ${editDataHanlde}
      } else {
        handle = ${addDataHandle}
      }
     await handle(modalForm.value)
      tableRef.value?.getTableData()
      message.success('操作成功')
      modalValue.value = false
      confirmLoading.value = false
    } catch(error){
       confirmLoading.value = false
       console.log(error)
    }
}

    </` +
    `script>`
  const data = await prettier.format(str, { parser: 'vue', plugins: [parseHtml, parseBabel] })
  return data
}

function renderItem(item, form = 'modalForm') {
  return `
    <a-form-item
      label="${item?.label ?? item.name}"
      name="${item.name}"
    >
    <a-input v-model:value="${form}.${item.name}" placeholder="请输入${item?.label ?? item.name}" />
  </a-form-item>
  `
}
