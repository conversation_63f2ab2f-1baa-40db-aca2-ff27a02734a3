<template>
  <div style="width: 100%; height: 100%">
    <div style="margin: 0 auto; padding: 100px; text-align: center" v-if="loading">
      <a-spin tip="加载..." />
    </div>
    <iframe
      v-show="!loading"
      :src="url"
      style="width: 100%; height: 100%"
      frameborder="0"
      ref="frameRef"
      @load="onFrameLoad"
    ></iframe>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { useRoute } from 'vue-router'

// 声明window.ENV的类型
declare global {
  interface Window {
    ENV: {
      API_MANAGE_FRAME_URL: string
      IFRAME_FILE_CONFIG_URL: string
      EXTERNAL_LOGIN_URL: string
      TO_THIRD_LOGIN: boolean
      [key: string]: any
    }
  }
}

// 响应式数据
const host = ref(window.ENV.API_MANAGE_FRAME_URL)
console.log('🚀 ~ host:', host)
const url = ref('')
const loading = ref(true)
const frameRef = ref<HTMLIFrameElement>()

const route = useRoute()

// 更新URL参数的方法
const updateUrlWithParams = () => {
  const path = route.path
  const query = route.query

  // 构建查询字符串
  const queryString =
    Object.keys(query).length > 0
      ? '?' + new URLSearchParams(query as Record<string, string>).toString()
      : ''

  url.value = `${host.value}/#${path}${queryString}`
  loading.value = true // 每次切换 url 时重置 loading
}

// iframe加载完成回调
const onFrameLoad = () => {
  loading.value = false
}

// 监听query参数变化
watch(
  () => route.query,
  (newQuery) => {
    if (newQuery.apiName) {
      // 从当前 URL 中提取 query 参数
      const currentUrl = new URL(url.value)
      const currentApiName = currentUrl.searchParams.get('apiName')
      const newApiName = newQuery.apiName as string

      // 只有在 apiName 不相等时才更新
      if (currentApiName !== newApiName) {
        updateUrlWithParams()
      }
    }
  },
  { immediate: false },
)

// 处理消息的方法
const handleMessage = (event: MessageEvent) => {
  // const { data } = event
  // const { path, query, type } = data
  // if (type === 'navigateToPage') {
  //   router.push({
  //     path,
  //     query
  //   })
  // }
}

// 组件挂载时的逻辑
onMounted(() => {
  // 开发环境使用本地地址
  if (import.meta.env.MODE === 'development') {
    // host.value = 'http://**************:8085'
    host.value = 'http://*************:8085'
  }
  updateUrlWithParams()
  // window.addEventListener('message', handleMessage)
})
</script>
