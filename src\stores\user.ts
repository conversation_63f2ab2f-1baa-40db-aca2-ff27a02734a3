import { defineStore } from 'pinia'
import { computed } from 'vue'
import { useLocalStorage } from '@vueuse/core'
import type { UserMenu } from '@fs/fs-components/src/components/layout/layout'
import { COOKIE_CONFIG, CookieManager } from '@/utils/cookie'

interface UserInfo {
  [propName: string]: any
}

export const useUserStore = defineStore('user', () => {
  // const token = useLocalStorage('token', '')
  const token = ref(CookieManager.get(COOKIE_CONFIG.TOKEN_KEY) || '')
  const userInfo = useLocalStorage<UserInfo>('userInfo', {})
  const isLoggedIn = computed(() => !!token.value)
  const currentMenuKeyPath = useLocalStorage<string[]>('currentMenuKeyPath', []) // 当前已选菜单路径
  const userMenu = ref<UserMenu[]>([])
  const userMenuInitialized = ref(false)
  const interfaceData = ref<UserMenu[]>([])

  /**
   * 设置token（同时更新Cookie）
   */
  function setToken(newToken: string) {
    token.value = newToken
    if (newToken) {
      CookieManager.set(COOKIE_CONFIG.TOKEN_KEY, newToken, {
        expires: COOKIE_CONFIG.EXPIRES_DAYS,
        path: '/',
        secure: location.protocol === 'https:', // 生产环境使用HTTPS时启用
        sameSite: 'lax',
      })
    } else {
      CookieManager.remove(COOKIE_CONFIG.TOKEN_KEY, '/', COOKIE_CONFIG.DOMAIN)
    }
  }
  /**
   * 清除登录信息
   */
  function clearLoginData() {
    setToken('')
    userInfo.value = {}
    currentMenuKeyPath.value = []
    userMenu.value = []
    userMenuInitialized.value = false
  }

  /**
   * 检查并同步Cookie中的token
   */
  function syncTokenFromCookie() {
    const cookieToken = CookieManager.get(COOKIE_CONFIG.TOKEN_KEY)
    if (cookieToken && cookieToken !== token.value) {
      token.value = cookieToken
    }
  }

  return {
    token,
    userInfo,
    isLoggedIn,
    interfaceData,
    currentMenuKeyPath,
    clearLoginData,
    userMenu,
    userMenuInitialized,
    setToken,
    syncTokenFromCookie,
  }
})
