import {
  readSystemFile,
  writeVirtualJson,
  writeFolderToDisk,
  writeFolderToFile,
  getJsonPath,
} from '../../base-business/virtual-file-system.js'
import { extractMenuType, flattenMenuData, getViewMenuDtl } from '../../tools/file-api.js'
import { generateTestCases } from '../../handler-pages/test-mock.js'
import { generateRouterViews } from '../../handler-pages/generate-router.js'
import { writeFilesApi } from '../../handler-pages/generate-api.js'
import { getApiRequest, getAxios } from '../../tools/file-api.js'
import path from 'path'
import { promises as fs } from 'fs'
import FormData from 'form-data'
import { createReadStream } from 'node:fs'
import prettier from 'prettier'
import parseBabel from 'prettier/parser-babel'
import parseHtml from 'prettier/parser-html'

import fsExtra from 'fs-extra'
const { ensureFile } = fsExtra
export const fsFile = fs

import formidable from 'formidable'

const { cacheDirPath } = getJsonPath()
// 缓存目录文件路径
export const draftPath = path.join(cacheDirPath, 'draft.json')
// 缓存目录json文件路径
export const virtualFileSystemJson = path.join(cacheDirPath, 'virtual-file-system.json')

let draftData = {}
let virtualData = {}
let apiData = []

/**
 * 从指定目录中提取函数信息并返回。
 * @returns {Array} 包含函数信息的数组。
 */
export async function getApiJson(data) {
  // 获取虚拟文件系统的 JSON 数据
  const virtualFileSystem = await readSystemFile()
  let apiData = virtualFileSystem.api || {}
  if (data) {
    apiData = data
  }
  if (virtualFileSystem['gpt-api']) {
    apiData = Object.assign(apiData, { 'gpt-api': virtualFileSystem['gpt-api'] })
  }

  // 用于存储提取的函数信息
  const functionsData = []

  /**
   * 遍历节点并解析接口信息，将解析结果存储在指定对象中。
   * @param {string} key - 节点的键值，用于标识节点所在的位置
   * @param {object} obj - 存储解析结果的对象
   * @returns {void}
   */
  function visitNode(key, obj, projectName) {
    // 检查 apiData 中是否存在对应的键值，并解析为对象
    const item = apiData[projectName][key] && JSON.parse(apiData[projectName][key])

    // 遍历接口信息数组
    item.interfaceInfos &&
      item.interfaceInfos.map((val) => {
        // 将解析结果存储在指定对象的 interfaceInfos 属性中
        obj.interfaceInfos.push({
          ...val,
        })
      })
  }
  for (const val in apiData) {
    const obj = { project: val, modules: [] }
    for (const item in apiData[val]) {
      const system = JSON.parse(apiData[val][item])
      const obj2 = {
        moduleName: item,
        moduleId: item.moduleId,
        moduleNameCn: system.moduleNameCn,
        interfaceInfos: [],
      }
      //调用 visitNode 来遍历 AST
      if (item === 'system-config-detail') {
        obj.url = system.url
        obj.time = system.time
      } else {
        visitNode(item, obj2, val)
        obj.modules.push(obj2)
      }
    }
    functionsData.push(obj)
  }

  return functionsData
}

/**
 * 设置 API 数据的 JSON
 * @param {Array} apiData - API 数据数组
 * @returns {Promise<void>} - 无返回值的 Promise 对象
 */
export async function setApiJson(apiData) {
  try {
    // 获取虚拟文件系统的 JSON 数据
    const virtualFileSystem = await readSystemFile()
    const api = virtualFileSystem.api
    // 更新 API 数据
    updataApi(api, apiData)
    // 将更新后的虚拟文件系统写入 JSON 文件
    writeVirtualJson(virtualFileSystem)

    // 将虚拟文件系统写入磁盘
    writeFolderToDisk(virtualFileSystem)
  } catch (err) {
    console.log('%c 🍦 err: ', 'font-size:12px;background-color: #7F2B82;color:#fff;', err)
  }
}

/**
 * 更新 API 数据
 * @param {Object} api - API 对象
 * @param {Array} apiData - API 数据数组
 */
function updataApi(api, apiData) {
  apiData.map((val) => {
    if (val.project !== 'gpt-api') {
      const data = api[val.project]
      const system = data['system-config-detail']
      val.modules.map((it) => {
        data[it.moduleName] = JSON.stringify(it)
      })
      data['system-config-detail'] = system
    }
  })
}

/**
 * 在 API 数据中查找指定接口名称的接口信息
 * @param {Array} apiData - 包含 API 数据的数组
 * @param {string} targetInterfaceName - 要查找的接口名称
 * @returns {Object|null} - 找到的接口信息对象，如果未找到则返回 null
 */
export function findInterfaceInfo(apiData, targetInterfaceName) {
  // 遍历 apiData 数组中的每个项目
  for (const item of apiData) {
    // 遍历每个项目中的模块
    for (const moduleItem of item.modules) {
      // 遍历每个模块中的接口信息
      for (const interfaceInfo of moduleItem.interfaceInfos) {
        // 如果当前接口信息的接口名称与目标接口名称匹配
        if (interfaceInfo.interfaceName === targetInterfaceName) {
          // 返回找到的接口信息
          return interfaceInfo
        }
      }
    }
  }
  // 如果没有找到匹配的接口信息，返回 null
  return null
}

export async function updateApiJson(project, list) {
  const apiData = await getNoGptApiJson()
  const data = apiData.find((item) => item.project === project)
  if (Array.isArray(list)) {
    list.forEach((item) => {
      const module = data.modules.find((it) => it.moduleName === item.moduleName)
      if (!module) {
        item.interfaceInfos.forEach((it) => {
          checkInterfaceName(item, it.interfaceName)
        })
        data.modules.push(item)
      } else {
        module.moduleId = item.moduleId
        item.interfaceInfos.forEach((it) => {
          checkInterfaceName(module, it.interfaceName)
          const index = module.interfaceInfos.findIndex((i) => i.interfaceName === it.interfaceName)
          index === -1 ? module.interfaceInfos.push(it) : (module.interfaceInfos[index] = it)
        })
      }
    })
  }
  setApiJson(apiData)
}

/**
 * 检查接口名称是否合法
 * @param {Object} module - 包含模块信息的对象
 * @param {string} interfaceName - 要检查的接口名称
 * @throws {Error} 如果接口名称以数字开头，将抛出错误
 */
function checkInterfaceName(module, interfaceName) {
  // 定义一个正则表达式，用于匹配以数字开头的字符串
  let pattern = /^\d/
  // 使用 test 方法检查接口名称是否以数字开头
  if (pattern.test(interfaceName)) {
    // 如果接口名称以数字开头，抛出错误，包含模块名称和接口名称信息
    throw new Error(
      `接口名称不能以数字开头, 请检查模块${module?.moduleName}下接口名称: ${interfaceName}`,
    )
  }
}

export async function getVirtualData(project) {
  try {
    // 获取虚拟文件系统的 JSON 数据
    const virtualFileSystem = await readSystemFile()
    const api = virtualFileSystem.api
    const obj = {}
    if (!project || !Array.isArray(project)) return
    // 当前需要生成的
    project.map((val) => {
      // 获取当前项目api数据
      const newObj = JSON.parse(JSON.stringify(api[val.project]))
      // 获取当前需要生成项目所有模块
      const arr = val.modules.map((it) => {
        return it.moduleName
      })
      const newObj2 = {}
      // 过滤出需要使用的模块
      arr.forEach((key) => {
        if (!newObj2[key]) {
          newObj2[key] = JSON.parse(newObj[key])
        }
      })
      // 循环生成文件
      val.modules.map((it) => {
        const itObj = newObj2[it.moduleName]
        // 获取当前项目需要生成的所有接口
        const arr2 = it.interfaceInfos.map((it) => {
          return it.interfaceName
        })
        // 过滤出已使用的接口
        if (itObj?.interfaceInfos && itObj.interfaceInfos.length > 0) {
          itObj.interfaceInfos = itObj.interfaceInfos.filter((it) => {
            return arr2.includes(it.interfaceName)
          })
        }

        newObj2[it.moduleName] = JSON.stringify(newObj2[it.moduleName])
      })

      obj[val.project] = newObj2
    })
    // 过滤出来的数据写入文件夹
    writeFolderToFile(obj, 'api')
    // 生成测试用例
    generateTestCases(obj)
  } catch (error) {
    console.error('写入文件失败:', error)
  }
}

/**
 * 删除虚拟文件系统中指定项目的API数据
 * @param {string} project - 要删除的项目名称
 * @returns {Promise<void>} - 当删除操作完成时解析的 Promise
 */
export async function delVirtualData(project) {
  // 获取虚拟文件系统的 JSON 数据
  const virtualFileSystem = await readSystemFile()
  const api = virtualFileSystem.api
  delete api[project]
  // 将更新后的虚拟文件系统写入 JSON 文件
  writeVirtualJson(virtualFileSystem)
}

/**
 * 解析 URL 并提取协议、IP 地址、端口和 UID
 * @param {string} url - 要解析的 URL
 * @returns {Object} - 包含协议、IP 地址、端口和 UID 的对象
 */
export function parseUrl(url) {
  // 使用 URL 类解析传入的 URL 字符串
  const urlObj = new URL(url)

  // 获取协议、IP 地址和端口，并格式化为字符串
  const ipWithPort = `${urlObj.protocol}//${urlObj.hostname}:${urlObj.port}`

  // 获取 URL 的哈希部分
  const hash = urlObj.hash

  // 如果哈希部分存在，则解析它以获取 UID 参数
  if (hash) {
    // 将哈希部分分割成键值对字符串
    const hashParams = new URLSearchParams(hash.split('?')[1])

    // 从键值对字符串中获取 UID 参数的值
    const uid = hashParams.get('uid')

    // 返回包含协议、IP 地址、端口和 UID 的对象
    return { ipWithPort, uid }
  }

  // 如果哈希部分不存在，则只返回协议、IP 地址和端口
  return { ipWithPort }
}

/**
 * 生成视图文件
 * @async
 * @param {Object} data - 包含视图内容和页面路径的数据对象
 * @param {string} data.content - 视图文件的内容
 * @param {string} data.pagePath - 视图文件的页面路径
 * @returns {Promise} - 当文件写入完成时解决的 Promise
 */
export async function generationView(data) {
  try {
    const { content, pagePath } = data
    const { srcViewsDirPath } = getJsonPath()
    const filePath = path.join(srcViewsDirPath, pagePath)
    await ensureFile(filePath)
    if (!content) throw new Error('views文件内容为空')
    fsFile.writeFile(filePath, content)
  } catch (error) {
    console.error('创建views文件失败:', error)
  }
}

async function generationLayoutLogin(data) {
  try {
    const { srcDirPath } = getJsonPath()
    for (let key in data) {
      const filePath = path.join(srcDirPath, data[key].pagePath)
      await ensureFile(filePath)
      if (!data[key].content) throw new Error('views文件内容为空')
      fsFile.writeFile(filePath, data[key].content)
    }
  } catch (error) {
    console.error('创建layout、login文件失败:', error)
  }
}

/**
 * 生成路由文件
 * @async
 * @param {Object} menu - 包含菜单数据的对象
 * @returns {Promise} - 当路由文件生成完成时解决的 Promise
 */
export async function generationRouter(routerList) {
  await saveRourerListDraft(routerList)
  const pageList = draftData['router-list-data'] || []
  await generateRouterViews(routerList, pageList)
}

// 需要生成的路由菜单
async function getGenerationRouterList(menu, token) {
  // 使用 replaceFieldInList 函数处理菜单数据，将结果存储在 resultList 中
  const resultList = await replaceFieldInList([menu], token)
  // 使用 extractMenuType 函数从 resultList 中提取菜单类型，将结果存储在 newList 中
  const newList = extractMenuType(resultList)
  // 使用 flattenMenuData 函数将 newList 展开为一维数组，将结果存储在 list 中
  const arr = flattenMenuData(newList)
  // 匹配存储数据 回显
  const list = await getLocalData(arr)
  return list
}

async function saveRourerListDraft(list) {
  try {
    let routerList = draftData['router-list-data'] || []
    const listMap = Object.fromEntries(routerList.map((it, index) => [it.menuId, index]))

    list.forEach((item) => {
      if (listMap[item.menuId] !== undefined) {
        routerList[listMap[item.menuId]] = item
      } else {
        routerList.push(item)
      }
    })

    await handleSavedraft({ body: { name: 'router-list-data', content: routerList } })
  } catch (error) {
    console.log('缓存路由数据失败:', error)
  }
}

async function getLocalData(arr) {
  const data = draftData
  if (data && data['router-list-data']) {
    const list = data['router-list-data']
    arr.forEach((item, index) => {
      const findItem = list.find((it) => it?.menuId === item?.menuId)
      if (findItem) {
        arr[index] = { ...item, ...findItem }
      }
    })
    return arr
  }
  return arr
}

/**
 * 替换列表中对象的字段
 * @async
 * @param {Array} list - 包含对象的列表
 * @returns {Promise} - 当字段替换完成时解决的 Promise
 */
async function replaceFieldInList(list, token) {
  const newList = []
  for (const item of list) {
    // 获取菜单详情，提取 menuUrl
    const { data } = await getViewMenuDtl(
      { params: { menuId: item.menuId } },
      { authorization: token },
    )
    item.menuUrl = data?.menuUrl
    // 将 datas 字段重命名为 childrens
    if ('datas' in item) {
      item.childrens = item.datas
      delete item.datas
    }
    // 递归处理子对象
    for (const key in item) {
      if (typeof item[key] === 'object') {
        item[key] = await replaceFieldInList(item[key], token)
      }
    }
    newList.push(item)
  }
  return newList
}

export async function handleRequestWithDynamicHandler(req, res, dynamicHandler) {
  try {
    const handleDataResult = await dynamicHandler(req)
    if (handleDataResult) {
      return res.json(handleDataResult)
    }
  } catch (error) {
    console.error('Error:', error.message)
    return res.json({ code: '000001', msg: error?.message || '操作失败' })
  }
}

// 保存草稿箱
export async function handleSavedraft(req) {
  const { name, content } = req.body
  draftData[name] = content

  // 将文件内容写入磁盘
  fsFile.writeFile(draftPath, JSON.stringify(draftData, null, 2))
  return { code: '000000', data: '数据已保存' }
}

// 获取草稿箱
export async function handleGetdraft(req) {
  const { name } = req.body
  return { code: '000000', data: draftData[name] || null }
}

// 格式化代码
export async function handleFormatting(req) {
  const { content } = req.body
  const data = await prettier.format(content, { parser: 'vue', plugins: [parseHtml, parseBabel] })
  return { code: '000000', data }
}

// 从虚拟文件系统获取api数据
export async function handleGtApiData(req) {
  const { interfaceName } = req.body
  apiData = await getApiJson()
  if (interfaceName) {
    const foundInterfaceInfo = findInterfaceInfo(apiData, interfaceName)
    if (foundInterfaceInfo) {
      return { code: '000000', data: foundInterfaceInfo }
    }
  }
  return { code: '000000', data: apiData }
}

// 保存api数据到虚拟文件系统
export async function handleSaveApiData(req) {
  const { apiData } = req.body
  await setApiJson(apiData)
  return { code: '000000', data: '保存成功' }
}

// 生成代码
export async function handleGenerationCode(req) {
  await generationView(req.body)
  return { code: '000000', data: '生成代码成功' }
}

// 增加swagger文档接口
export async function handleSaveUrlData(req) {
  const { url } = req.body
  const data = parseUrl(url)
  await writeFilesApi(data.ipWithPort, { uid: data.uid }, false)
  return { code: '000000', data: '新增成功' }
}

// 修改swagger文档接口
export async function handleEditUrlData(req) {
  const { url, path } = req.body
  await delVirtualData(path)
  const data = parseUrl(url)
  await writeFilesApi(data.ipWithPort, { uid: data.uid }, false)
  return { code: '000000', data: '编辑成功' }
}

// 删除swagger文档接口
export async function handleDelUrl(req) {
  const { path } = req.body
  await delVirtualData(path)
  return { code: '000000', data: '删除成功' }
}

// 查询swagger文档数据
export async function handleGetUrlDetail(req) {
  const { url } = req.body
  const val = parseUrl(url)
  const swaggerUrl = val.ipWithPort + '/flow/swagger/share'
  const data = await getApiRequest(swaggerUrl, { uid: val.uid })
  return { code: '000000', data: data?.modules || [] }
}

// swagger文档同步
export async function handleSynchronous(req) {
  const { project, list } = req.body
  await updateApiJson(project, list)
  return { code: '000000', data: '同步成功' }
}

// 代码部署
export async function handleGenerateDeploy(req) {
  const { project } = req.body
  getVirtualData(project)
  return { code: '000000', data: '部署成功' }
}

// 生成路由
export async function handleGenerationRouter(req) {
  const { routerList } = req.body
  await generationRouter(routerList)
  return { code: '000000', data: '生成成功' }
}

// 生成路由
export async function handleGetGenerationRouter(req) {
  const { menu, token } = req.body
  const list = await getGenerationRouterList(menu, token)
  return { code: '000000', data: list }
}

// mock test 请求
export async function handleMockTest(req, res) {
  const start = Date.now()
  try {
    const { url, methods, Body, Cookie = [], Headers = [] } = req.body
    const Data = Body.value
    const request = {
      url,
      methods,
      data: getParam(Data, Body.type),
      headers: getHeader(Headers, Cookie),
    }
    const data = await getAxios(request)
    const obj = getResponse(data, data, start)
    res.json(obj)
  } catch (error) {
    const { response } = error
    const obj = getResponse(response, response, start)
    res.json(obj)
  }
}

export function handleMockFormDataTest(req, res) {
  const start = Date.now()
  try {
    const form = formidable({})
    const formData = new FormData()
    form.parse(req, async (err, fields, files) => {
      try {
        for (let key in fields) {
          // 过滤接口数据
          if (!['Url-value', 'Methods-value', 'Cookie-value', 'Headers-value'].includes(key)) {
            formData.append(key, fields[key][0])
          }
        }
        for (let key in files) {
          const filepath = files[key][0].filepath
          const fileStream = createReadStream(filepath)
          formData.append(key, fileStream)
        }
        const url = fields['Url-value'][0]
        const methods = fields['Methods-value'][0]
        const Cookie = (fields['Cookie-value'] && fields['Cookie-value'][0]) || []
        const HeadersValue = (fields['Headers-value'] && fields['Headers-value'][0]) || []
        const headers = getHeaderCookie(HeadersValue, Cookie)
        const request = {
          url,
          methods,
          data: formData,
          headers: {
            ...headers,
            'Content-Type': 'multipart/form-data',
          },
        }

        const data = await getAxios(request)
        const obj = getResponse(data, data, start)
        res.json(obj)
      } catch (error) {
        const { response } = error
        const obj = getResponse(response, response, start)
        res.json(obj)
      }
    })
  } catch (error) {
    const { response } = error
    const obj = getResponse(response, response, start)
    res.json(obj)
  }
}

export async function handleSaveLayoutLogin(req) {
  await generationLayoutLogin(req.body)
  return { code: '000000', data: '生成成功' }
}

function getResponse(response, axiosData, start) {
  try {
    const headers = axiosData.headers
    const cofigHeaders = axiosData.config.headers
    const config = axiosData.config
    const end = Date.now()
    const headersArray = []
    for (const key in headers) {
      headersArray.push({ name: key, value: headers[key] })
    }
    const elapsedTime = end - start
    const contentLength = cofigHeaders['Content-Length']
    return {
      code: '000000',
      data: {
        code: response.status,
        time: elapsedTime,
        'content-length': contentLength,
        Headers: headersArray,
        Preview: response.data,
        config,
      },
    }
  } catch (error) {
    return { code: '000001', data: '操作失败：' + error }
  }
}

function getHeaderCookie(Headers, Cookie) {
  try {
    const obj = {}
    if (Headers.length > 0) {
      Headers.split(';').forEach((item) => {
        const [key, value] = item.trim().split(':')
        obj[key] = value
      })
    }
    if (Cookie.length > 0) {
      Cookie.split(';').forEach((item) => {
        const [key, value] = item.trim().split(':')
        obj[key] = value
      })
    }
    return obj
  } catch (error) {
    console.log('解析header cookie 错误' + error)
  }
}

function getHeader(Headers, Cookie) {
  const headers = {}
  if (Headers.length > 0) {
    Headers.map((item) => {
      headers[item.name] = item.value
    })
  }
  if (Cookie.length > 0) {
    const arr = Cookie.map((item) => item.name + '=' + item.value)
    headers['Cookie'] = arr.join(';')
  }
  return headers
}

/**
 * 根据指定类型处理参数
 * @param {Array} params - 参数数组，每个元素包含 name 和 value 属性
 * @param {string} type - 参数类型，可以是 'form-data' 或其他类型
 * @returns {FormData|Object} - 根据类型处理后的参数对象
 * @description
 * 这个函数用于根据指定的类型处理参数数组。如果类型是 'form-data'，则将参数转换为 FormData 对象；否则，将参数转换为普通对象。
 *
 * @example
 * const params = [
 *   { name: 'param1', value: 'value1' },
 *   { name: 'param2', value: 'value2' }
 * ];
 * const formDataParams = getParam(params, 'form-data');
 * console.log(formDataParams); // 输出: FormData 对象，包含 param1 和 param2
 *
 * const objectParams = getParam(params, 'object');
 * console.log(objectParams); // 输出: { param1: 'value1', param2: 'value2' }
 */
const getParam = (params, type) => {
  if (type === 'form-data') {
    const formData = new FormData()
    params.forEach((item) => {
      formData.append(item.name, item.value)
    })
    return formData
  } else {
    return params
  }
}

/**
 * 异步函数，用于获取草稿数据。
 * @async
 * @function getDraftData
 * @returns {Promise<void>} 返回一个Promise，表示函数执行完成。
 */
export async function getDraftData() {
  try {
    ensureFile(draftPath)
    // 使用fs模块的readFile方法读取draftPath路径下的文件内容，并指定编码为utf8
    const content = await fsFile.readFile(draftPath, 'utf8')
    // 将读取到的内容解析为JSON对象，并赋值给draftData变量
    draftData = JSON.parse(content)
    return draftData
  } catch {
    // 如果发生错误，则打印错误信息
    console.log('draft.json文件草稿文件不存在')
  }
}

/**
 * 异步函数，用于获取草稿数据。
 * @async
 * @function getDraftData
 * @returns {Promise<void>} 返回一个Promise，表示函数执行完成。
 */
export async function getVirtualJson() {
  try {
    ensureFile(virtualFileSystemJson)
    const content = await fsFile.readFile(virtualFileSystemJson, 'utf8')
    virtualData = JSON.parse(content)
    return virtualData
  } catch {
    // 如果发生错误，则打印错误信息
    console.log('draft.json文件草稿文件不存在')
  }
}

async function getNoGptApiJson() {
  const apiData = await getApiJson()
  const index = apiData.findIndex((item) => item.project === 'gpt-api')
  if (index !== -1) apiData.splice(index, 1)
  return apiData
}
