<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed, watch, onBeforeMount } from 'vue'

defineOptions({
  name: 'Splitter',
})

interface Props {
  // 分割方向：horizontal | vertical
  direction?: 'horizontal' | 'vertical'
  // 初始分割位置：可以是百分比 number 或字符串 '240px' / '40%'
  initialSplit?: number | string
  // 最小宽度/高度（像素）
  minSize?: number
  // 分割条宽度
  splitterSize?: number
}

const props = withDefaults(defineProps<Props>(), {
  direction: 'horizontal',
  initialSplit: 50,
  minSize: 100,
  splitterSize: 6,
})

// 响应式数据
const containerRef = ref<HTMLElement>()
const leftPanelRef = ref<HTMLElement>()
const rightPanelRef = ref<HTMLElement>()
const splitterRef = ref<HTMLElement>()
const isDragging = ref(false)
const initialPercentGuess =
  typeof props.initialSplit === 'number'
    ? props.initialSplit
    : typeof props.initialSplit === 'string' && props.initialSplit.trim().endsWith('%')
      ? parseFloat(props.initialSplit)
      : 50
const splitPosition = ref<number>(initialPercentGuess)

// 拖拽相关状态
let startPosition = 0
let startSplit = 0

// 开始拖拽
const handleMouseDown = (e: MouseEvent) => {
  e.preventDefault()
  isDragging.value = true
  startPosition = props.direction === 'horizontal' ? e.clientX : e.clientY
  startSplit = splitPosition.value

  document.addEventListener('mousemove', handleMouseMove)
  document.addEventListener('mouseup', handleMouseUp)
  document.body.style.cursor = props.direction === 'horizontal' ? 'col-resize' : 'row-resize'
  document.body.style.userSelect = 'none'
}

// 拖拽中
const handleMouseMove = (e: MouseEvent) => {
  if (!isDragging.value || !containerRef.value) return

  const containerRect = containerRef.value.getBoundingClientRect()
  const currentPosition = props.direction === 'horizontal' ? e.clientX : e.clientY
  const containerSize =
    props.direction === 'horizontal' ? containerRect.width : containerRect.height

  const delta = currentPosition - startPosition
  const deltaPercent = (delta / containerSize) * 100

  let newSplit = startSplit + deltaPercent

  // 限制最小和最大范围
  const minPercent = (props.minSize / containerSize) * 100
  const maxPercent = 100 - minPercent

  newSplit = Math.max(minPercent, Math.min(maxPercent, newSplit))
  splitPosition.value = newSplit
}

// 结束拖拽
const handleMouseUp = () => {
  isDragging.value = false
  document.removeEventListener('mousemove', handleMouseMove)
  document.removeEventListener('mouseup', handleMouseUp)
  document.body.style.cursor = ''
  document.body.style.userSelect = ''
}

// 根据 props.initialSplit 设置初始百分比（支持 px 与 %）
onMounted(() => {
  if (!containerRef.value) return
  const containerRect = containerRef.value.getBoundingClientRect()
  const containerSize =
    props.direction === 'horizontal' ? containerRect.width : containerRect.height
  const v = props.initialSplit
  if (typeof v === 'number') {
    splitPosition.value = v
  } else if (typeof v === 'string') {
    const s = v.trim()
    if (s.endsWith('%')) {
      const p = parseFloat(s)
      if (!Number.isNaN(p)) splitPosition.value = p
    } else if (s.toLowerCase().endsWith('px')) {
      const px = parseFloat(s)
      if (!Number.isNaN(px) && containerSize > 0) {
        // 转换为百分比
        const p = (px / containerSize) * 100
        // 需要按照最小尺寸限制进行夹取
        const minPercent = (props.minSize / containerSize) * 100
        const maxPercent = 100 - minPercent
        splitPosition.value = Math.max(minPercent, Math.min(maxPercent, p))
      }
    }
  }
})

// 监听 initialSplit 变化
watch(
  () => props.initialSplit,
  (v) => {
    if (!containerRef.value) return
    const containerRect = containerRef.value.getBoundingClientRect()
    const containerSize =
      props.direction === 'horizontal' ? containerRect.width : containerRect.height
    if (typeof v === 'number') {
      splitPosition.value = v
    } else if (typeof v === 'string') {
      const s = v.trim()
      if (s.endsWith('%')) {
        const p = parseFloat(s)
        if (!Number.isNaN(p)) splitPosition.value = p
      } else if (s.toLowerCase().endsWith('px')) {
        const px = parseFloat(s)
        if (!Number.isNaN(px) && containerSize > 0) {
          const p = (px / containerSize) * 100
          const minPercent = (props.minSize / containerSize) * 100
          const maxPercent = 100 - minPercent
          splitPosition.value = Math.max(minPercent, Math.min(maxPercent, p))
        }
      }
    }
  },
)

// 清理事件监听
onUnmounted(() => {
  document.removeEventListener('mousemove', handleMouseMove)
  document.removeEventListener('mouseup', handleMouseUp)
})

// 计算样式
const containerStyle = computed(() => ({
  display: 'flex' as const,
  flexDirection: (props.direction === 'horizontal' ? 'row' : 'column') as 'row' | 'column',
  height: '100%',
  width: '100%',
}))

const leftPanelStyle = computed(() => ({
  [props.direction === 'horizontal' ? 'width' : 'height']: `${splitPosition.value}%`,
  minWidth: props.direction === 'horizontal' ? `${props.minSize}px` : 'auto',
  minHeight: props.direction === 'vertical' ? `${props.minSize}px` : 'auto',
  overflow: 'auto' as const,
}))

const splitterStyle = computed(() => ({
  [props.direction === 'horizontal' ? 'width' : 'height']: `${props.splitterSize}px`,
  cursor: props.direction === 'horizontal' ? 'col-resize' : 'row-resize',
}))
</script>

<template>
  <div ref="containerRef" class="splitter-container" :style="containerStyle">
    <div ref="leftPanelRef" class="splitter-panel splitter-left" :style="leftPanelStyle">
      <slot name="left" />
    </div>

    <div
      ref="splitterRef"
      class="splitter-handle"
      :style="splitterStyle"
      @mousedown="handleMouseDown"
    >
      <div class="splitter-grip" />
    </div>

    <div ref="rightPanelRef" class="splitter-panel splitter-right flex-1 overflow-auto">
      <slot name="right" />
    </div>
  </div>
</template>

<style scoped>
.splitter-container {
  position: relative;
  user-select: none;
}

.splitter-panel {
  position: relative;
  overflow: hidden;
}

.splitter-handle {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f0f0f04a;
  border: 1px solid #d9d9d946;
  transition: background-color 0.2s;
}

.splitter-handle:hover {
  background-color: #ebebeb65;
}

.splitter-grip {
  width: 2px;
  height: 20px;
  background-color: #d3d3d3;
  border-radius: 1px;
}

.splitter-handle:hover .splitter-grip {
  background-color: #616161a1;
}

/* 垂直分割时的样式调整 */
.splitter-container[style*='flex-direction: column'] .splitter-grip {
  width: 20px;
  height: 2px;
}
</style>
