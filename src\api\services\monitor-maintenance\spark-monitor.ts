import service from '@/api'
import { SODATAFLINK } from '@/api/base'
import type { Parameter } from '../data-asset/hbase-management'

export class gridColumnDTO {
  /** ID */
  id?: string

  /** key */
  key?: string

  /** value */
  value?: string
  /* 组 */
  keyGroup?: string | number

  /** 注释 */
  description?: string

  /** 当前行是否编辑 */
  editable?: boolean
  ownerId?: string
}

export class gridobj {
  data!: {
    outputMsg: string
  }
}

/**
 * 页面内容加载
 * @param params
 */
export function getSparkConfiList(params: Parameter) {
  return service({
    method: 'get',
    url: `${SODATAFLINK}/new2dataplatform/globalConfig/globalconfigPageList`,
    params,
  })
}

/**
 * 页面内容加载
 * @param params
 */
export function getFlinkConfiList(params: Parameter) {
  return service({
    method: 'post',
    url: `${SODATAFLINK}/flink/FlinkTable/trunFlinkUrl`,
    data: params,
  })
}

/**
 * 页面内容新增
 * @param params
 */
export function addPageList(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/new2dataplatform/globalConfig/globalconfigInsert`,
    method: 'post',
    data: params,
  })
}

/**
 * 页面内容删除
 * @param params
 */
export function deletePageList(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/new2dataplatform/globalConfig/globalconfigDelete/${params.id}`,
    method: 'delete',
    data: params,
  })
}

/**
 * 页面内容编辑
 * @param params
 */
export function editPageList(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/new2dataplatform/globalConfig/globalconfigUpdate/${params.id}`,
    method: 'put',
    data: params,
  })
}

/**
 * 获取连接
 * @param params
 */
export function getDbUrlAPI(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/datapreview/etlschemainfoList`,
    method: 'post',
    data: params,
  })
}

/**
 * 获取表名称
 * @param params
 */
export function getDbDataAPI(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/redefine/getSqlDbAndTables`,
    method: 'post',
    data: params,
  })
}

/**
 * 数据预览历史查询
 * @param params
 */
export function getDataPreviewHistory(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/datapreview/datapreviewquerylogPageList`,
    method: 'get',
    params,
  })
}

/**
 * 实时预览获取表名称
 * @param params
 */
export function getSqlResultAPI(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/datapreview/getSqlResult`,
    method: 'post',
    data: params,
  })
}

/**
 * 实时预览获取库表树结构
 * @param params
 */
export function getDbAndTablesTreeAPI(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/datapreview/getDbAndTablesTree`,
    method: 'post',
    data: params,
  })
}

/**
 * SPARK-SQL 查询
 * @param params
 */
export function getSparkSqlResultAPI(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/config/SparkTableManager/SparkPreview`,
    method: 'post',
    data: params,
    extraParams: {
      showLoading: false,
    },
    timeout: 300000,
  })
}

/**
 * FLINK-SQL 查询
 * @param params
 */
export function getFlinkSqlResultAPI(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/flink/FlinkTable/SparkPreview`,
    method: 'post',
    data: params,
    extraParams: {
      showLoading: false,
    },
    timeout: 300000,
  })
}

/**
 * Spark生成SQL
 * @param params
 */
export function sparkGenerateSQL(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/datapreview/testSparkvaluetype`,
    method: 'post',
    data: params,
  })
}

/**
 * Flink生成SQL
 * @param params
 */
export function flinkGenerateSQL(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/datapreview/getFlinkMemoryTableColumn`,
    method: 'post',
    data: params,
  })
}

/**
 * 获取Spark 内存表使用率
 * @param params
 */
export function getSparkMemoryUse(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/config/SparkTableManager/getApplicationMemoryPercentFun`,
    method: 'post',
    data: params,
  })
}

/**
 * 获取Srorage信息
 * @param params
 */
export function getSrorageInfo(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/config/SparkTableManager/getStorageInfoFun`,
    method: 'post',
    data: params,
  })
}

/**
 * 获取Executors信息
 * @param params
 */
export function getExecutorsInfo(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/config/SparkTableManager/getAllExecuterInfoFun`,
    method: 'post',
    data: params,
  })
}

/**
 * 获取字典表加载进度
 * @param params
 */
export function getRunDic(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/config/SparkTableManager/getRunningDicInfoFun`,
    method: 'post',
    data: params,
  })
}

/**
 * 查询内存表执行的SQL 状态
 * @param params
 */
export function queryMemorySqlStatus(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/config/SparkTableManager/queryMemorySqlStatus`,
    method: 'post',
    data: params,
  })
}

/**
 * 预估内存
 * @param params
 */
export function getEstimateMemory(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/config/SparkTableManager/estimateMemory`,
    method: 'post',
    data: params,
  })
}

/**
 * 执行计划
 * @param params
 */
export function getSparkExplain(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/config/SparkTableManager/SparkExplain`,
    method: 'post',
    data: params,
  })
}

/**
 * 初始化全局配置
 * @param params
 */
export function refreshGlobalConfig(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/MasterControl/entranceGateWay/refreshGlobalConfigFun`,
    method: 'post',
    data: params,
  })
}

/**
 * 检查人机交互入口
 * @param params
 */
export function checkCatBotEntrance(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/MasterControl/entranceGateWay/checkCatBotEntranceFun`,
    method: 'post',
    data: params,
  })
}
