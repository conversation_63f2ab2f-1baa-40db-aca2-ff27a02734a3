<template>
  <div class="relationship">
    <a-collapse v-model:activeKey="activeKey">
      <a-collapse-panel key="1" header="一度展开关系">
        <div class="mini-echarts" ref="miniChartRef"></div>
      </a-collapse-panel>
      <!-- <a-collapse-panel key="2" header="概念语义关系">
        <a-table :dataSource="relationList" :columns="relationshipColumns" :pagination="false">
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'point'">
              <a-tag>{{ relationMap[record.relationCategory as keyof typeof relationMap] }}</a-tag>
              {{ record.startEntity.nameZh }}({{ record.startEntity.name }})
            </template>
          </template>
        </a-table>
      </a-collapse-panel>
      <a-collapse-panel key="3" header="标准语义关系">
        <a-table
          :dataSource="relationshipData"
          :columns="relationshipColumns"
          :pagination="false"
        ></a-table>
      </a-collapse-panel>
      <a-collapse-panel key="4" header="实体类型关系">
        <a-table
          :dataSource="relationshipData"
          :columns="relationshipColumns"
          :pagination="false"
        ></a-table>
      </a-collapse-panel>
      <a-collapse-panel key="5" header="事件类型关系">
        <a-table
          :dataSource="relationshipData"
          :columns="relationshipColumns"
          :pagination="false"
        ></a-table>
      </a-collapse-panel> -->
    </a-collapse>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, reactive, watch, defineProps } from 'vue'
import * as echarts from 'echarts'
// import { relationshipData, relationshipColumns, relationMap } from './data-source'
import type { ECBasicOption } from 'echarts/types/dist/shared'
import { reqGetAttributeSummary } from '@/api/services/knowledge'

const props = defineProps<{
  currentEchartsData: any
  chartData: any
}>()
const activeKey = ref(['1'])
const miniChartRef = ref()
const timmer = ref()
// interface RelationRecord {
//   relationCategory: keyof typeof relationMap
//   [key: string]: any
// }

// const relationList = ref<RelationRecord[]>([])
let myCharts: any = null

const option = reactive<ECBasicOption>({
  animationDurationUpdate: 1000, //数据更新动画的时长。
  animationEasingUpdate: 'quinticInOut', //数据更新动画的缓动效果。
  stateAnimation: {
    duration: 300,
    easing: 'cubicOut',
  },
  series: [
    {
      type: 'graph',
      layout: 'force',
      draggable: true,
      symbolSize: 20,
      autoMultLines: true,
      autoSelf: true,
      roam: true,
      label: {
        show: true,
        formatter: function (param: any) {
          return param?.data?.nameZh
        },
      },
      edgeSymbol: ['none', 'arrow'],
      edgeSymbolSize: [10, 10],
      edgeLabel: {
        show: true,
        fontSize: 14,
        position: 'middle',
        formatter: function (param: any) {
          return param?.data?.name
        },
      },
      data: [],
      links: [],
      emphasis: {
        focus: 'adjacency',
        lineStyle: {
          color: '#FF6B6B', // 自环颜色
          width: 3,
          curveness: 0.3,
        },
      },
      force: {
        repulsion: 100,
        edgeLength: 90,
        friction: 0.6,
        layoutAnimation: true,
      },
      zoom: 2,
      scaleLimit: {
        min: 1,
        max: 5,
      },
      left: 10,
      right: 10,
      top: 10,
      bottom: 10,
    },
  ],
})

watch(
  () => props.currentEchartsData,
  () => {
    nextTick(() => {
      initEchartFn()
    })
  },
)

// const getRelationData = async () => {
//   const res = await reqGetAttributeSummary({
//     projectId: props.currentEchartsData.id,
//   })
//   console.log('%c [ res ]-136', 'font-size:13px; background:#422b68; color:#866fac;', res)
//   // relationList.value = res.relations
//   // console.log('关系list', res)
// }

const resizeHandler = () => {
  console.log('窗口变化啦')
  timmer.value = setTimeout(function () {
    myCharts.resize()
  }, 200)
}

const initEchartFn = () => {
  myCharts = echarts.init(miniChartRef.value)
  const currentId = props.currentEchartsData.id
  // 根据当前id去过滤data
  const filterLinks = props.chartData?.initLinkListData.filter(
    (item: any) => item.startEntity.id === currentId || item.endEntity.id === currentId,
  )
  const filterDemoData = props?.chartData?.demoDataListData.filter((item: any) => {
    const isSelf = item.id === currentId
    const res =
      filterLinks.find((i: any) => i.startEntity.id === item.id || i.endEntity.id === item.id) ||
      isSelf
    if (res) {
      return true
    } else {
      return false
    }
  })
  const newSeries = (option.series as any[]).map((item) => {
    return {
      ...item,
      data: filterDemoData,
      links: filterLinks,
    }
  })
  option.series = newSeries
  myCharts.setOption(option)
}

onMounted(() => {
  initEchartFn()
  getRelationData()
  //随着屏幕大小调节图表
  window.addEventListener('resize', resizeHandler)
})

onUnmounted(() => {
  // 移除监听
  window.removeEventListener('resize', resizeHandler) // 组件销毁前移除事件监听
  myCharts?.dispose()
})
</script>

<style scoped lang="less">
.relationship {
  padding-left: 24px;
  .mini-echarts {
    height: 500px;
  }
}
</style>
