<template>
  <a-modal
    :open="visible"
    title="编辑表信息"
    @ok="handleOk"
    @cancel="handleCancel"
    :maskClosable="false"
    :confirmLoading="loading"
    width="700px"
  >
    <a-form
      ref="formRef"
      :model="formState"
      :rules="rules"
      :label-col="{ span: 5 }"
      :wrapper-col="{ span: 16 }"
    >
      <a-form-item label="表名" name="tableName">
        {{ formState.tableName }}
      </a-form-item>
      <a-form-item label="表描述" name="tableName">
        <a-input v-model:value="formState.description" placeholder="请输入表描述" />
      </a-form-item>
      <a-form-item label="表上架字段" name="selectedColumns">
        <ColumnSelector
          v-model="formState.selectedColumns"
          :table-id="props?.tableData?.tableId"
          @select-columns="handleSelectColumns"
          v-if="visible"
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script lang="ts" setup>
import { ref, defineProps, defineEmits, watch } from 'vue'
import { message } from 'ant-design-vue'
import {
  getDataAssetTableDetail,
  updateDataAssetTable,
} from '@/api/assetmanager/dataassertmanage/dataassertmanage'
import ColumnSelector from './column-selector.vue'

interface Column {
  columnName: string
  columnComment: string
  dataType: string
  columnType: string
}

const props = defineProps<{
  visible: boolean
  tableData: Record<string, any>
  databaseData?: Record<string, any>
}>()

const emit = defineEmits(['update:visible', 'success', 'refresh'])

const formRef = ref()
const formState = ref({
  tableName: '',
  description: '',
  selectedColumns: [] as string[],
  tableId: '',
})

const selectedColumns = ref<any[]>([])

const rules = {
  selectedColumns: [{ required: true, message: '请选择表字段' }],
}

const loading = ref(false)

watch(
  () => props.visible,
  async (newVal) => {
    if (props?.tableData?.tableName && newVal) {
      formState.value.tableName = props?.tableData?.tableName
      try {
        formState.value.description = props?.tableData?.description
        const res: any = await getDataAssetTableDetail(
          {
            tableId: props?.tableData?.tableId || '',
          },
          {},
        )
        if (res.data?.columnsList) {
          formState.value.selectedColumns = res.data.columnsList
            .filter((col: Column) => col.columnName)
            .map((col: Column) => col.columnName)
          selectedColumns.value = res.data.columnsList
        }
      } catch (error) {
        console.error('获取表字段失败:', error)
      }
    }
  },
  { immediate: true },
)

const handleSelectColumns = async (data: any[]) => {
  selectedColumns.value = data
}

const handleOk = async () => {
  try {
    loading.value = true
    await formRef.value.validate()
    const params = {
      tableId: props.tableData.tableId,
      tableName: formState.value.tableName,
      description: formState.value.description,
      columnList: selectedColumns.value,
    }

    await updateDataAssetTable(params, {})
    message.success('修改成功')
    emit('success')
    emit('refresh')
    handleCancel()
  } catch (error) {
    console.error('修改失败:', error)
  } finally {
    loading.value = false
  }
}

const handleCancel = () => {
  formRef.value?.resetFields()
  emit('update:visible', false)
}
</script>

<style lang="less" scoped>
:deep(.ant-checkbox-group) {
  width: 100%;
}
</style>
