<template>
  <div class="sql-ace-editor-container">
    <!-- 编辑器工具栏 -->
    <div class="sql-editor-toolbar">
      <div class="toolbar-left">
        <span class="theme-label">SQL 编辑器</span>
      </div>
      <div v-if="showToolbar" class="toolbar-right">
        <a-tooltip title="手动触发智能提示 (Ctrl+Space)">
          <a-button type="text" size="small" @click="triggerAutoComplete" class="refresh-btn">
            💡 智能提示
          </a-button>
        </a-tooltip>
        <a-tooltip title="如果光标位置不正确，点击此按钮修复">
          <a-button type="text" size="small" @click="forceRefreshEditor" class="refresh-btn">
            🔄 修复光标
          </a-button>
        </a-tooltip>
      </div>
    </div>

    <!-- Ace 编辑器 -->
    <div class="editor-wrapper" :style="editorWrapperStyle">
      <VAceEditor
        ref="aceEditorRef"
        v-model:value="modelValue"
        lang="sql"
        theme="github"
        :options="aceOptions"
        :readonly="readonly"
        style="height: 100%; width: 100%"
        @init="onInit"
        @update:value="onUpdate"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
/**
 * 基础 sql 编辑器
 * 基本配置，包括适用于项目的主题、字体、大小、行号、缩进、换行等
 * 静态补全器，包括 SQL 关键字补全器
 * 动态补全器由父组件传入
 * 基本方法：刷新补全器、插入文本、强制刷新编辑器（针对光标位置不正确的情况）、手动触发智能提示、获取编辑器实例
 */
import { ref, computed, nextTick, onMounted, watch } from 'vue'
import { VAceEditor } from 'vue3-ace-editor'

// Ace 相关导入
import 'ace-builds/src-noconflict/mode-sql'
import 'ace-builds/src-noconflict/theme-github'
import 'ace-builds/src-noconflict/ext-language_tools'
import ace from 'ace-builds/src-noconflict/ace'
import type { Ace } from 'ace-builds'
import type { Completer } from './types'

// Props 定义
interface Props {
  readonly?: boolean
  height?: string | number
  enableAutoCompletion?: boolean
  otherCompleters?: Completer[]
  showToolbar?: boolean
}

defineOptions({
  name: 'SqlEditor',
})

const props = withDefaults(defineProps<Props>(), {
  readonly: false,
  height: '400px',
  enableAutoCompletion: true,
  showToolbar: false,
})

// Events 定义
const emit = defineEmits<{
  init: [editor: any]
  change: [value: string]
}>()

// 双向绑定
const modelValue = defineModel<string>({ default: '' })

// 编辑器引用
const aceEditorRef = ref()
const editorInstance = ref<Ace.Editor | null>(null)

// 编辑器包装器样式
const editorWrapperStyle = computed(() => {
  const height = typeof props.height === 'number' ? `${props.height}px` : props.height
  return {
    height: `calc(${height} - 38px)`,
    width: '100%',
  }
})

// Ace 编辑器选项
const aceOptions = computed(() => ({
  fontSize: 14, // 字体大小
  fontFamily: 'Monaco, Menlo, "Ubuntu Mono", Consolas, "Courier New", monospace',
  showPrintMargin: false, // 显示打印边距
  highlightActiveLine: true, // 突出显示活动行
  showGutter: true, // 显示行号
  tabSize: 2, // 制表符大小
  wrap: false, // 不换行
  useSoftTabs: true, // 使用软标签
  enableBasicAutocompletion: props.enableAutoCompletion, // 启用基本自动补全
  enableLiveAutocompletion: props.enableAutoCompletion, // 启用实时自动补全
  enableSnippets: true, // 启用代码片段
  showLineNumbers: true, // 显示行号
  displayIndentGuides: true, // 显示缩进引导
  showFoldWidgets: true, // 显示折叠小部件
  highlightSelectedWord: true, // 突出显示选中的单词
  behavioursEnabled: true, // 启用行为
  wrapBehavioursEnabled: true, // 启用换行行为
}))

// 处理编辑器初始化
const onInit = (editor: Ace.Editor) => {
  console.log('编辑器初始化完成')
  editorInstance.value = editor

  if (editor) {
    // 基本配置
    editor.setReadOnly(props.readonly)

    // 设置字体
    editor.setOptions({
      fontFamily: 'Monaco, Menlo, "Ubuntu Mono", Consolas, "Courier New", monospace',
      fontSize: 14,
    })

    // 确保编辑器获得焦点（如果不是只读）
    if (!props.readonly) {
      nextTick(() => {
        editor.focus()
        editor.renderer.scrollCursorIntoView()
      })
    }

    // 设置智能提示
    if (props.enableAutoCompletion) {
      setupAutoCompletion()
    }

    // 发出初始化事件
    emit('init', editor)
  }
}

// 处理值更新
const onUpdate = (value: string) => {
  emit('change', value)
}
const keywordCompleter: Completer = {
  getCompletions: (editor, session, pos, prefix, callback) => {
    // SQL关键字
    const sqlKeywords = [
      'SELECT',
      'FROM',
      'WHERE',
      'INSERT',
      'UPDATE',
      'DELETE',
      'CREATE',
      'DROP',
      'ALTER',
      'INDEX',
      'TABLE',
      'DATABASE',
      'AND',
      'OR',
      'NOT',
      'NULL',
      'ORDER BY',
      'GROUP BY',
      'HAVING',
      'LIMIT',
      'DISTINCT',
      'COUNT',
      'SUM',
      'AVG',
      'MAX',
      'MIN',
      'JOIN',
      'LEFT JOIN',
      'RIGHT JOIN',
      'INNER JOIN',
    ]
    const completions = sqlKeywords.map((keyword) => ({
      caption: keyword,
      value: keyword,
      meta: 'SQL关键字',
      type: 'keyword',
      score: 500,
    }))
    callback(null, completions)
  },
}
// 设置智能提示
const setupAutoCompletion = () => {
  try {
    const editor = editorInstance.value
    if (!editor) return
    const langTools = ace.require('ace/ext/language_tools')

    if (!langTools) {
      console.warn('Language tools not available')
      return
    }

    // 更新补全器列表 - 添加动态参数补全器
    langTools.setCompleters([keywordCompleter, ...(props.otherCompleters || [])])
  } catch (error) {
    console.error('设置智能提示失败:', error)
  }
}

// 刷新智能提示
const refreshCompletions = () => {
  if (editorInstance.value && props.enableAutoCompletion) {
    setupAutoCompletion()
  }
}

// 手动触发智能提示
const triggerAutoComplete = () => {
  if (editorInstance.value) {
    try {
      editorInstance.value.focus()
      editorInstance.value.execCommand('startAutocomplete')
      console.log('手动触发智能提示')
    } catch (error) {
      console.error('触发智能提示失败:', error)
    }
  }
}

// 强制刷新编辑器
const forceRefreshEditor = () => {
  if (editorInstance.value) {
    try {
      const cursorPosition = editorInstance.value.getCursorPosition()

      // 重新调整大小和刷新
      editorInstance.value.resize(true)
      editorInstance.value.renderer.updateFull(true)

      // 恢复光标位置
      editorInstance.value.moveCursorToPosition(cursorPosition)
      editorInstance.value.focus()

      console.log('编辑器已强制刷新')
    } catch (error) {
      console.error('刷新编辑器失败:', error)
    }
  }
}

// 插入文本
const insertText = (text: string) => {
  if (!editorInstance.value) {
    console.warn('编辑器未初始化，无法插入文本')
    return
  }

  try {
    const currentValue = modelValue.value || ''

    if (!currentValue.trim()) {
      modelValue.value = text
    } else {
      const needSpace = !currentValue.endsWith(' ') && !currentValue.endsWith('\n')
      modelValue.value = currentValue + (needSpace ? ' ' : '') + text
    }

    // 移动光标到末尾
    nextTick(() => {
      if (editorInstance.value) {
        const session = editorInstance.value.getSession()
        const lastRow = session.getLength() - 1
        const lastColumn = session.getLine(lastRow).length
        editorInstance.value.moveCursorTo(lastRow, lastColumn)
        editorInstance.value.focus()
      }
    })

    console.log('已插入文本:', text)
  } catch (error) {
    console.error('插入文本失败:', error)
  }
}

// 获取编辑器实例
const getEditor = () => {
  return editorInstance.value
}

// 暴露方法给父组件
defineExpose({
  insertText,
  forceRefreshEditor,
  getEditor,
  refreshCompletions,
})

watch(
  () => props.otherCompleters,
  () => {
    refreshCompletions()
  },
  { deep: false },
)
</script>

<style scoped lang="less">
.sql-ace-editor-container {
  border-radius: 6px;
  overflow: hidden;
  border: 1px solid #d9d9d9;
  background: #fff;
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;

  .editor-wrapper {
    flex: 1;
    position: relative;
    overflow: hidden;
  }

  // 编辑器样式覆盖
  :deep(.ace_editor) {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', 'Courier New', monospace !important;
    font-size: 14px !important;
    line-height: 1.5 !important;
  }

  :deep(.ace_gutter) {
    background: #f8f9fa !important;
    border-right: 1px solid #e9ecef !important;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', 'Courier New', monospace !important;
  }

  :deep(.ace_cursor) {
    color: #000000 !important;
    border-left: 2px solid #000000 !important;
  }

  :deep(.ace_content) {
    cursor: text !important;
  }

  :deep(.ace_text-layer) {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', 'Courier New', monospace !important;
    font-size: 14px !important;
  }

  // GitHub主题语法高亮
  :deep(.ace-github) {
    .ace_keyword {
      color: #d73a49 !important;
      font-weight: bold !important;
    }

    .ace_string {
      color: #032f62 !important;
    }

    .ace_constant.ace_numeric {
      color: #005cc5 !important;
    }

    .ace_comment {
      color: #6a737d !important;
      font-style: italic !important;
    }

    .ace_support.ace_function {
      color: #6f42c1 !important;
    }

    .ace_variable {
      color: #e36209 !important;
    }

    .ace_operator {
      color: #d73a49 !important;
    }

    .ace_selection {
      background: #c8e6f5 !important;
    }

    .ace_selected-word {
      background: #ffeaa7 !important;
      border: 1px solid #fdcb6e !important;
    }

    .ace_marker-layer .ace_active-line {
      background: #f6f8fa !important;
    }
  }

  // 智能提示样式
  :deep(.ace_autocomplete) {
    border: 1px solid #d9d9d9 !important;
    border-radius: 4px !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
    background: #fff !important;
    width: 400px !important;
    min-width: 300px !important;
    max-width: 600px !important;
  }

  :deep(.ace_autocomplete .ace_line) {
    padding: 8px 12px !important;
    line-height: 1.4 !important;
    border-bottom: 1px solid #f0f0f0 !important;
  }

  :deep(.ace_autocomplete .ace_line.ace_selected) {
    background: #e6f7ff !important;
  }

  :deep(.ace_autocomplete .ace_completion-meta) {
    color: #666 !important;
    font-size: 12px !important;
  }
}

.sql-editor-toolbar {
  height: 38px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 12px;
  background: #fafafa;
  border-bottom: 1px solid #e9ecef;
  flex-shrink: 0;

  .toolbar-left {
    display: flex;
    align-items: center;

    .theme-label {
      font-size: 12px;
      color: #666;
      font-weight: 500;
    }
  }

  .toolbar-right {
    display: flex;
    align-items: center;
  }

  .refresh-btn {
    font-size: 12px;
    color: #666;

    &:hover {
      color: #1890ff;
      background: #f0f8ff;
    }
  }
}
// 动态参数智能提示特殊样式
:deep(.ace_autocomplete .ace_line) {
  // 动态参数项的特殊样式
  &[data-type='dynamic_param'] {
    background: linear-gradient(90deg, #f0f9ff 0%, #e0f2fe 100%) !important;
    border-left: 3px solid #0ea5e9 !important;

    &.ace_selected {
      background: linear-gradient(90deg, #dbeafe 0%, #bfdbfe 100%) !important;
    }

    .ace_completion-meta {
      color: #0ea5e9 !important;
      font-weight: 500 !important;
    }
  }
}
// 动态参数提示图标
:deep(.ace_autocomplete .ace_line[data-type='dynamic_param']) {
  &:before {
    content: '🔧';
    margin-right: 8px;
    font-size: 14px;
  }
}
</style>
