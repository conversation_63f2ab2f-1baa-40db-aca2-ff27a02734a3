<template>
  <div class="sort">
    <div class="sort-title">排序</div>
    <div class="sort-containter">
      <div class="sort-item" v-for="(item, index) in modelValue" :key="index">
        <sort
          :tableName="tableName"
          :columns="columns"
          @handleClick="handleClick($event, 'edit', index)"
        >
          <template #default>
            <div class="group-add">
              <span style="margin-right: 5px">
                <ArrowUpOutlined
                  @click.stop="item.sort = 'desc'"
                  v-if="item.sort !== 'desc'"
                  class="close-icon-button"
                />
                <ArrowDownOutlined
                  @click.stop="item.sort = 'asc'"
                  v-else
                  class="close-icon-button"
                />
              </span>
              <span style="margin-right: 10px">{{ item.fieldName }}</span>
              <CloseOutlined @click.stop="handleRemove(index)" class="close-icon-button" />
            </div>
          </template>
        </sort>
      </div>
      <div class="sort-item">
        <sort
          :tableName="tableName"
          :columns="columns"
          @handleClick="handleClick($event, 'add')"
        ></sort>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { CloseOutlined, ArrowUpOutlined, ArrowDownOutlined } from '@ant-design/icons-vue'
import type { dataColumn } from '@/components/data-columns/type'
import Sort from '@/components/sort/index.vue'

defineProps<{
  tableName: string
  columns: dataColumn[]
}>()

const emits = defineEmits(['change'])

const modelValue = defineModel<any[]>({
  default: () => [],
})

function handleRemove(index: number) {
  modelValue.value = modelValue.value.filter((_, i) => i !== index)
  emits('change')
}

function handleClick(e: dataColumn, type: 'add' | 'edit', index = 0) {
  if (type === 'add') {
    modelValue.value = [...(modelValue.value || []), e]
  } else {
    modelValue.value[index] = e
  }
  emits('change')
}
</script>

<style lang="less" scoped>
.close-icon-button {
  position: relative;
  // right: -10px;
  padding: 2px;
  &:hover {
    // background-color: #333;
    color: #509ee3;
    background-color: #fff;
    border-radius: 50%;
  }
}
.sort-title {
  margin-bottom: 0.5rem;
  color: rgb(147, 161, 171);
  font-weight: bold;
  display: flex;
}
.sort-containter {
  display: flex;
  -webkit-box-align: center;
  align-items: center;
  flex-wrap: wrap;
  border-radius: 8px;
  background-color: rgba(147, 161, 171, 0.1);
  padding: 14px;
  color: rgb(147, 161, 171);
  gap: 10px;
}
.sort-item {
  // color: #fff;
  // padding: 10px;
  // cursor: pointer;
  // background-color: rgba(147, 161, 171, 0.8);

  // border-radius: 6px;
  // display: flex;
  // -webkit-box-align: center;
  // align-items: center;
}
</style>
