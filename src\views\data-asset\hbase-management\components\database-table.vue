<template>
  <div class="database-table">
    <div class="content-div">
      <cardBox
        :title="`数据库表列表${databaseData?.name ? '（' + databaseData?.name + '）' : ''}`"
        subTitle="数据库表列表"
      >
        <template #title>
          <div class="cardbox-head">
            <type-icon style="margin-right: 10px" :type="'hbase'" />
            <span>{{ `${databaseData?.name ? databaseData?.name : ''}` }}</span>
          </div>
        </template>
        <template #headerRight>
          <a-button type="primary" @click="handleAddTable"> 新增表 </a-button>
        </template>
        <div class="cardbox-content">
          <a-tabs v-model:activeKey="activeKey" class="table-detail-tabs">
            <a-tab-pane key="1">
              <template #tab>
                <span> 数据列表 </span>
              </template>
              <Table
                :columns="columns"
                :getData="transHbaseTableApi"
                ref="tableRef"
                :autoRequest="false"
                :searchFormState="formState"
              >
                <template #search>
                  <a-form-item label="" name="tableName">
                    <a-input
                      v-model:value="formState.tableName"
                      placeholder="请输入表名"
                      allow-clear
                    />
                  </a-form-item>
                </template>
                <template #bodyCell="{ column, record }">
                  <template v-if="column.key === 'operate'">
                    <a-space class="action-container">
                      <a @click.prevent="handleEdit(record)">编辑</a>
                      <a-popconfirm
                        title="是否确认删除"
                        ok-text="是"
                        cancel-text="否"
                        @confirm="handleDel(record)"
                      >
                        <a>删除</a>
                      </a-popconfirm>
                      <a @click.prevent="toDetail(record)">详情</a>
                    </a-space>
                  </template>
                </template>
              </Table>
            </a-tab-pane>
          </a-tabs>
        </div>
      </cardBox>
    </div>

    <!-- Edit Table Modal -->
    <edit-table-modal
      v-if="editModalVisible"
      v-model:visible="editModalVisible"
      :table-data="currentTable"
      :is-edit="isEdit"
      :database-data="databaseData"
      @success="handleEditSuccess"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue'
import cardBox from '@/components/card-box/card-box.vue'
import {
  getHbaseTableList,
  deleteHbaseTable,
  // getHbaseTableColumnList,
} from '@/api/services/data-asset/hbase-management'
import EditTableModal from './edit-table-modal.vue'
import { message } from 'ant-design-vue'
import TypeIcon from '@/views/data-asset/metadata-query/components/type-icon.vue'
import { Table } from '@fs/fs-components'

import { useRouter } from 'vue-router'
const router = useRouter()

const props = defineProps<{
  databaseId?: string
  databaseData?: Record<string, any>
}>()
const activeKey = ref('1')
const isEdit = ref(false)

const emit = defineEmits(['refresh'])

const records = ref<any[]>([])

const columns = ref<any[]>([
  {
    dataIndex: 'tableName',
    title: '表名',
    key: 'tableName',
  },
  {
    dataIndex: 'operate',
    title: '操作',
    key: 'operate',
    width: 260,
  },
])

const editModalVisible = ref(false)
const currentTable = ref<Record<string, any>>({})

const tableRef = ref<InstanceType<typeof Table> | null>(null)
const formState = ref<any>({
  id: undefined,
  namespace: '',
  tableName: '',
})

const transHbaseTableApi = (params: any) =>
  getHbaseTableList(params).then((d: any) => {
    // 接口应该返回records 前端做处理
    d.data.records = d.data.records?.map((item: string) => {
      return {
        tableName: item,
      }
    })
    return d
  })

const getTableLists = async () => {
  try {
    formState.value = {
      ...formState.value,
      id: props.databaseId,
      namespace: props.databaseData?.name ?? '',
    }
    setTimeout(() => {
      tableRef.value?.getTableData()
    }, 20)
  } catch (error) {
    console.error('获取数据表列表失败:', error)
  }
}

const toDetail = async (record: any) => {
  // try {
  // console.log('toDetail:', record)
  const params = {
    id: props.databaseData?.dbUrlId,
    namespace: props.databaseData?.name,
    tableName: record.tableName,
    tableComment: record.tableComment,
  }
  //   const res: any = await getHbaseTableColumnList(params)
  //   if ((res.data?.length ?? 0) > 0) {
  //     detailData.value.colFamily = res.data
  //     detailData.value.tableName = record.tableName
  //   }
  // } catch (error) {
  //   console.error('获取表字段失败:', error)
  // }
  if (params.id.length === 0) {
    message.error('数据库信息编号不能为空')
    return
  }
  if (params.namespace.length === 0) {
    message.error('数据库名不能为空')
    return
  }
  if (params.tableName.length === 0) {
    message.error('表名不能为空')
    return
  }
  // console.log('detailData:',detailData.value)
  router.push({
    name: 'hbase-table-detail',
    query: {
      ...params,
    },
  })
}

const handleDel = async (record: any) => {
  try {
    const params = {
      namespace: props.databaseData?.name ?? '',
      id: props.databaseId,
      tableName: record.tableName,
    }
    await deleteHbaseTable(params)
    message.success('删除成功')
    getTableLists()
  } catch (error) {
    console.error(error)
  }
}

const handleAddTable = () => {
  isEdit.value = false
  // tableModalForm.value = initTable()
  editModalVisible.value = true
  // tableModalTitle.value = '新增表'
}

const handleEdit = (record: Record<string, any>) => {
  currentTable.value = record
  isEdit.value = true
  editModalVisible.value = true
}

const handleEditSuccess = () => {
  editModalVisible.value = false
  getTableLists()
}

watch(
  () => props.databaseData,
  (newVal) => {
    if (newVal) {
      // fetchTableData()
      getTableLists()
    }
  },
  { immediate: true },
)
</script>

<style lang="less" scoped>
.database-table {
  height: 100%;
  .header-div {
    display: flex;
    align-items: center;
    height: 48px;
    padding: 0px 12px 0 20px;
    border-bottom: 1px solid #0000001a;
  }
  .content-div {
    padding: 12px 12px 0 12px;
    height: calc(100% - 48px);
  }
}
// :deep(.fs-table .search-btn) {
//   display: none;
// }
// :deep(.ant-btn:hover) {
//   .clear-icon {
//     opacity: 1 !important;
//   }
// }
.cardbox-content {
  position: relative;
  width: 100%;
  height: 100%;
  :global(.filter-form) {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  :global(.search-box) {
    max-width: 400px;
  }
}
.add-btn {
  position: absolute;
  right: 0;
  top: 6px;
}
:deep(.card-box-comp .box-header-g) {
  margin-bottom: 0;
}
.cardbox-head {
  display: flex;
  align-items: flex-start;
  :deep(.db-icon img) {
    width: auto !important;
  }
}
</style>
