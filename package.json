{"name": "vue3-seed-framework", "version": "0.0.1", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "start-http": "set PORT=3001 && node code-tool/http-servers/server.js", "preview": "vite preview", "test:unit": "vitest", "test:e2e": "playwright test", "build-only": "vite build", "type-check": "vue-tsc --build --force", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/", "precommit": "lint-staged", "generate:api": "node code-tool/handler-pages/generate-api.js generate", "generate:router": "node code-tool/handler-pages/generate-router.js generate-router", "generate:deploy": "node code-tool/handler-pages/deploy-file.js", "test:mock": "node code-tool/handler-pages/test-mock.js test", "test:mock-ui": "set VITE_MOCK_TEST=true && vitest --environment=node --ui", "server": "set VITE_MOCK_VARIABLE=true && run-p dev start-http", "release": "vite build && node scripts/upload.js"}, "simple-git-hooks": {"pre-commit": "node scripts/pre-commit.js && npm run precommit"}, "lint-staged": {"*.{vue,js,jsx,cjs,mjs,ts,tsx,cts,mts}": ["eslint --fix", "prettier --write"]}, "dependencies": {"@airpower/enum": "^1.3.3", "@airpower/transformer": "^1.3.3", "@antv/g6": "^4.8.21", "@form-create/designer": "^3", "@form-create/element-ui": "^3", "@fs/fs-components": "0.3.45", "@types/mockjs": "^1.0.10", "@vueuse/core": "^10.9.0", "ace-builds": "^1.39.0", "ant-design-vue": "4.x", "axios": "^1.6.8", "body-parser": "^1.20.3", "clipboard-copy": "^4.0.1", "codemirror": "^6.65.7", "dayjs": "^1.11.10", "doctrine": "^3.0.0", "dotenv": "^16.4.7", "echarts": "^5.5.1", "express": "^4.21.0", "form-data": "^4.0.1", "formidable": "^3.5.2", "highlight.js": "^11.11.1", "html2canvas": "^1.4.1", "jsondiffpatch": "^0.6.0", "mockjs": "^1.1.0", "moment": "^2.30.1", "monaco-editor": "^0.29.1", "monaco-editor-webpack-plugin": "^5.0.0", "numeral": "^2.0.6", "pinia": "^2.1.7", "prettier-plugin-vue": "^1.1.6", "prismjs": "^1.29.0", "splitpanes": "^3.2.0", "ts-morph": "^23.0.0", "vue": "^3.4.15", "vue-hooks-plus": "^2.4.0", "vue-router": "^4.5.1", "vue-router-tab": "^1.2.11", "vue3-ace-editor": "^2.2.4", "vue3-tabor": "^0.2.2", "vuedraggable": "^4.1.0", "yarn": "^1.22.22"}, "devDependencies": {"@playwright/test": "^1.41.1", "@rushstack/eslint-patch": "^1.3.3", "@tsconfig/node20": "^20.1.2", "@types/jsdom": "^21.1.6", "@types/node": "^20.11.10", "@types/numeral": "^2.0.5", "@types/prismjs": "^1.26.5", "@types/splitpanes": "^2.2.6", "@unocss/preset-attributify": "^66.1.0-beta.6", "@vitejs/plugin-vue": "^5.0.3", "@vitejs/plugin-vue-jsx": "^3.1.0", "@vitest/ui": "2.1.2", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/test-utils": "^2.4.4", "@vue/tsconfig": "^0.5.1", "eslint": "^8.57.0", "eslint-plugin-vue": "^9.17.0", "jsdom": "^24.0.0", "less": "^4.2.0", "lint-staged": "^15.2.2", "npm-run-all2": "^6.1.1", "ora": "^8.1.1", "prettier": "^3.3.3", "scp2": "^0.5.0", "simple-git-hooks": "^2.10.0", "typescript": "~5.3.0", "unocss": "^66.1.0-beta.6", "unplugin-auto-import": "^19.1.1", "unplugin-vue-components": "^0.26.0", "vite": "^5.0.11", "vite-plugin-vue-devtools": "^7.7.7", "vitest": "^2.1.2", "vue-tsc": "^1.8.27"}, "resolutions": {"strip-ansi": "6.0.1", "string-width": "4.2.2", "wrap-ansi": "7.0.0"}, "packageManager": "yarn@1.22.22+sha1.ac34549e6aa8e7ead463a7407e1c7390f61a6610"}