<template>
  <main>
    <a-row class="aRow">
      <a-col :span="5">
        <div class="left">
          <div class="title">组织结构</div>
          <tree-node
            :treeData="treeData"
            @treeSelect="treeSelect"
            :fNames="fNames"
            :checkable="false"
            :draggable="true"
            @draghandle="treeDrag"
          >
            <template #menus="{ record }">
              <a-menu-item>
                <a-button v-action:addDept block type="text" @click="operation('addNext', record)"
                  >新增下级组织</a-button
                >
              </a-menu-item>
              <a-menu-item v-action:updateDept>
                <a-button block type="text" @click="operation('edit', record)">编辑组织</a-button>
              </a-menu-item>
              <a-menu-item v-action:deleteDept>
                <a-button block type="text" @click="operation('remove', record)">删除组织</a-button>
              </a-menu-item>
            </template>
          </tree-node>
        </div>
      </a-col>
      <a-col :span="19">
        <div class="right">
          <div class="example" v-show="spinModal">
            <a-spin />
          </div>
          <cardBox :title="title" subTitle="组织架构管理，权责清晰协同高效">
            <template #headerRight>
              <a-button
                type="primary"
                @click="addOrganizationalTable('add')"
                :loading="submitLoading"
                style="margin-right: 20px"
                v-action:addDept
                >新增一级组织</a-button
              >
              <a-button
                type="primary"
                @click="addOrganizationalTable('addNext')"
                :loading="submitLoading"
                v-action:addDept
                >新增下级组织</a-button
              >
            </template>
            <Table
              :columns="columns"
              :getData="getChildDeptList"
              ref="tableRef"
              :searchFormState="form"
              :autoRequest="false"
              :pagination="false"
            >
              <template #search>
                <a-form-item label="" name="组织名称" :rules="[{ message: '请输入组织名称' }]">
                  <a-input v-model:value="form.deptName" placeholder="请输入组织名称" />
                </a-form-item>
                <a-form-item label="">
                  <a-select v-model:value="form.isUse" placeholder="请选择是否启用" allow-clear>
                    <a-select-option value="1">是</a-select-option>
                    <a-select-option value="0">否</a-select-option>
                  </a-select>
                </a-form-item>
              </template>
            </Table>
          </cardBox>
        </div>
      </a-col>
    </a-row>
    <organizational-modal
      @modalCancel="modalCancel"
      @modalHandleOk="modalHandleOk"
      ref="modalRef"
    ></organizational-modal>
    <confirm-delete-modal
      :removeConfirmLoading="removeConfirmLoading"
      :removeMenuModal="removeMenuModal"
      @removeModalhandleOk="removeModalhandleOk"
      @removeModalhandleCancel="removeModalhandleCancel"
    />
  </main>
</template>
<script lang="ts" setup>
import {
  createVNode,
  h,
  onBeforeMount,
  onMounted,
  ref,
  resolveDirective,
  withDirectives,
} from 'vue'
import { ExclamationCircleOutlined } from '@ant-design/icons-vue'
import { Table } from '@fs/fs-components'
import { type FormInstance, message, Button, Modal } from 'ant-design-vue'
import {
  getDeptList,
  getChildDeptList,
  getDeptTypeList,
  deleteDept,
  updateDept,
  getDataDictList,
} from '@/api/services/permission'
import { useRoute } from 'vue-router'
import type { Column } from '@fs/fs-components/src/components/table/type'
import { OrganizationalManagementDTO } from '@/utils/column'
import type { Data } from 'ant-design-vue/es/_util/type'
import { useUserStore } from '@/stores/user'
import cardBox from '@/components/card-box/card-box.vue'

defineOptions({
  name: 'organizationalManagement',
})

interface FormState {
  deptName: string
  isUse: boolean | null | ''
  deptId?: string
}
const fNames = {
  children: 'deptList',
  title: 'deptName',
  key: 'deptId',
}
const route = useRoute()
const modalRef = ref<any | null>(null)
const formRef = ref<FormInstance>()
const systemName = ref<string>('')
const treeData = ref<any>([])
const title = ref<string>('组织管理')
const removeConfirmLoading = ref<boolean>(false)
const disabled = ref<boolean>(false)
const MenuPower = ref<boolean>(false)
const removeMenuModal = ref<boolean>(false)
const removeMenuModalValue = ref<Record<string, any>>({})
const tableRef = ref<InstanceType<typeof Table> | null>(null)
const spinModal = ref<boolean>(false)
const submitLoading = ref<boolean>(false)
let columns = ref<Column[]>([])
const modalOpen = ref<boolean>(false)
const deptType = ref<Data[]>([])
const selectedKeysVal = ref<string>('')
const userStore = useUserStore()
const form = ref<FormState>({
  deptName: '',
  isUse: null,
})
onMounted(() => {
  systemName.value = route.query?.systemName as string
  getTreeLists(false)
})

onBeforeMount(() => {
  initColumns()
  getDeptType()
})

function getTreeLists(flag = true) {
  const params = {
    buId: userStore.userInfo.buId as string,
    isUse: 1,
  }
  getDeptList(params).then((result: any) => {
    if (result.code === '000000') {
      treeData.value = result.data
      flag && resetForm()
      if (!flag) {
        form.value.deptId = result.data[0].deptId
      }
      tableRef.value?.getTableData()
    }
  })
}

const operation = (type: string, data: Record<string, any>) => {
  resetForm()
  data.operation = false
  disabled.value = false
  MenuPower.value = false
  if (type === 'add') {
    addOrganizationalTable(type)
  } else if (type === 'addNext') {
    const deptId = data?.deptId
    addOrganizationalTable(type, deptId)
  } else if (type === 'edit') {
    console.log(type)
    const paramsData = {
      parentId: data?.deptId,
      record: data,
      deptTypeList: deptType.value,
      type: 'addNextMenus',
    }
    modalRef.value?.show(type, paramsData)
  } else if (type === 'remove') {
    removeMenuConfirmation(data)
  }
}

function removeMenuConfirmation(data: Record<string, any>) {
  removeMenuModal.value = true
  removeMenuModalValue.value = data
}

function removeModalhandleOk() {
  removeConfirmLoading.value = true
  const data = {
    deptId: removeMenuModalValue.value?.deptId,
  }
  return new Promise(() => {
    deleteDept(data).finally(function () {
      removeMenuModal.value = false
      removeConfirmLoading.value = false
      message.success('删除成功')
      form.value.deptId = removeMenuModalValue.value?.deptId
      getTreeLists()
    })
  })
}

function removeModalhandleCancel() {
  removeConfirmLoading.value = false
  removeMenuModal.value = false
}

async function treeSelect(selectedKeys: any) {
  selectedKeysVal.value = selectedKeys
  resetForm()
  if (selectedKeys) {
    spinModal.value = true
    form.value.deptId = selectedKeys
    await tableRef.value?.getTableData()
    spinModal.value = false
  }
}

function addOrganizationalTable(type: string, deptId?: string) {
  let parentId = ''
  if (deptId) {
    parentId = deptId
  } else {
    parentId = selectedKeysVal.value
  }
  const data = {
    parentId: type === 'add' ? '' : parentId,
    deptTypeList: deptType.value,
  }
  if (type === 'addNext' && selectedKeysVal.value === '') {
    message.info('请先选择的父级组织!')
  } else {
    modalRef.value?.show(type, data)
    modalOpen.value = true
  }
}

//获取组织类型
function getDeptType() {
  const params = { dictType: 'deptType' }
  getDataDictList(params).then((res: any) => {
    deptType.value = res?.data
  })
  // const params = {
  //   isUse: 1,
  // }
  // getDeptTypeList(params).then((res: any) => {
  //   deptType.value = res?.data
  // })
}

function modalCancel() {
  modalOpen.value = false
}

function modalHandleOk() {
  modalOpen.value = false
  form.value.deptId = selectedKeysVal.value || treeData.value[0].deptId
  getTreeLists()
}

const resetForm = () => {
  formRef.value && formRef.value.resetFields()
}

function treeDrag(info: any) {
  const { dropToGap, node, dragNode } = info
  const { deptName, key, deptType, dutyUserId } = dragNode
  const { deptId } = node
  const dropParentId = node.parentId
  const data = {
    deptId: key,
    deptName,
    deptType,
    dutyUserId,
    parentId: dropToGap ? dropParentId : deptId,
    isUse: true,
  }
  updateDept(data).then(() => {
    tableRef.value?.getTableData()
  })
}

async function initColumns() {
  columns.value = OrganizationalManagementDTO.map((item) => {
    return {
      ...item,
      dataIndex: item.key,
    }
  })
  columns.value.push({
    key: 'action',
    title: '操作',
    customRender(data: Record<string, any>) {
      return [
        withDirectives(
          h(
            Button,
            {
              type: 'link',
              onClick() {
                modalOpen.value = true
                disabled.value = true
                ;(data.deptTypeList = deptType.value), modalRef.value?.show('edit', data)
              },
            },
            {
              default: () => '编辑',
            },
          ),
          [[resolveDirective('action'), '', 'updateDept']],
        ),
        withDirectives(
          h(
            Button,
            {
              type: 'link',
              onClick() {
                Modal.confirm({
                  title: '提示',
                  icon: createVNode(ExclamationCircleOutlined),
                  content: createVNode('div', { style: 'color:red;' }, '确认执行删除操作？'),
                  async onOk() {
                    try {
                      return await new Promise((resolve, reject) => {
                        deleteDept({ deptId: data?.record?.deptId })
                          .then((res: any) => {
                            if (res.code === '000000') {
                              message.success('删除成功')
                              tableRef.value?.resetForm()
                              getTreeLists()
                              resolve(res)
                            }
                          })
                          .catch(() => {
                            reject()
                          })
                      })
                    } catch {
                      return console.log('Oops errors!')
                    }
                  },
                  class: 'test',
                })
              },
            },
            {
              default: () => '删除',
            },
          ),
          [[resolveDirective('action'), '', 'deleteDept']],
        ),
      ]
    },
  })
}
</script>
<style lang="less" scoped>
main {
  height: 100%;
  background: white;
  .left {
    height: 100%;
    border-right: 1px solid #0000001a;
    border-radius: 4px;
    min-width: 260px;
    padding: 12px;
    .tree-dropdown {
      position: absolute;
      right: 0px;
      top: 0px;
      padding-left: 20px;
      padding-right: 10px;
    }

    .content {
      padding: 0;
    }
    .title {
      margin-right: auto;
      font-size: 16px;
      font-style: normal;
      font-weight: 500;
      line-height: 24px;
      display: flex;
      justify-content: space-between;
      padding-right: 12px;
      padding-bottom: 16px;
    }
  }

  .right {
    position: relative;
    border-radius: 4px;
    text-align: center;
    height: 100%;
    padding: 12px;

    /* 更大的模糊半径和渐变颜色 */
    .addMenu {
      .addMenu-nav {
        border-bottom: 1px solid #e8e8e8;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-right: 20px;

        span {
          padding: 16px;
          font-size: 16px;
        }
      }
    }

    .centent {
      padding: 16px;
    }
  }

  .aRow {
    height: 100%;
  }
}

.example {
  text-align: center;
  background: rgba(0, 0, 0, 0.01);
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 100;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
<style lang="less">
.popoverOption {
  width: 150px;
}
</style>
