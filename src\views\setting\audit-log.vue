<template>
  <div class="data-source-page">
    <cardBox title="审计日志" subTitle="审计日志信息">
      <Table
        :columns="auditLogColumns"
        :getData="logoperateinfoPageList"
        ref="tableRef"
        :searchFormState="formState"
      >
        <template #search>
          <a-form-item name="ownerId">
            <a-input v-model:value="formState.ownerId" allowClear placeholder="请输入操作人" />
          </a-form-item>
          <a-form-item name="createTime">
            <a-date-picker
              style="width: 100%"
              v-model:value="formState.createTime"
              placeholder="请选择创建时间"
            />
          </a-form-item>
        </template>
        <template #bodyCell="{ column, text }">
          <template
            v-if="column.key === 'operateOriginalValue' || column.key === 'operateAfterlValue'"
            ><a class="table-cell" @click="handleCheckDetail(text)">{{ text }}</a></template
          >
        </template>
      </Table>
    </cardBox>
  </div>
</template>

<script lang="ts" setup>
import cardBox from '@/components/card-box/card-box.vue'
import { onMounted, reactive, ref } from 'vue'
import { logModal } from '@/utils/log-modal'
import { logoperateinfoPageList } from '@/api/setting'
import { auditLogColumns } from './columns'
import { Table } from '@fs/fs-components'

defineOptions({
  name: 'setting/auditLog',
})

const formRef = ref()

const formState = reactive({
  ownerId: '',
  createTime: '',
})

const sourceDataList = ref<any>([])
const pageIndex = ref(1)
const pageSize = ref(10)
const total = ref(0)
const loading = ref(false)

onMounted(() => {
  logoperateinfoPageListHandle()
})

const handleQuery = async () => {
  formRef.value?.validate().then(() => {
    pageIndex.value = 1
    pageSize.value = 10
    const params = {
      ownerId: formState.ownerId,
      createTime: formState.createTime,
      pageIndex: 1,
      pageSize: 10,
    }
    logoperateinfoPageListHandle(params)
  })
}

const handleReset = async () => {
  formRef.value?.resetFields()
  logoperateinfoPageListHandle()
}

const logoperateinfoPageListHandle = async (searchParams: any = {}) => {
  loading.value = true
  const res = await logoperateinfoPageList({
    pageIndex: pageIndex.value,
    pageSize: pageSize.value,
    ...searchParams,
  }).finally(() => {
    loading.value = false
  })
  sourceDataList.value = res.data.records
  total.value = res.data.totalRecords
}

const handleCheckDetail = (text: any) => {
  logModal(text, '查看详情', true, 60, null, { type: 'json' })
}

const pagination = computed(() => ({
  current: pageIndex.value,
  pageSize: pageSize.value,
  total: total.value,
  showSizeChanger: true,
  showQuickJumper: true,
  size: 'small',
  showTotal: (total: number) => `共 ${total} 条`,
  onChange: (currentPage: number, currentPageSize: number) => {
    pageIndex.value = currentPage
    pageSize.value = currentPageSize
    logoperateinfoPageListHandle()
  },
}))
</script>

<style lang="less" scoped>
.data-source-page {
  height: calc(100vh - 40px);
  padding: 24px;
  :deep(.box-form-g) {
    margin-bottom: 24px;
  }
}
.table-cell {
  display: block;
  width: 100%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
</style>
