<template>
  <a-modal
    :open="open"
    @ok="modalHandleOk"
    :title="modalType === 'edit' ? '编辑' : '新增'"
    @cancel="cancel"
    width="800px"
    :confirm-loading="confirmLoading"
  >
    <a-form
      ref="formRef"
      :model="form"
      :rules="rules"
      :label-col="labelCol"
      :wrapper-col="wrapperCol"
    >
      <a-form-item label="参数编号" name="paramId">
        <a-input v-model:value="form.paramId" :disabled="disabled" placeholder="请输入参数编号" />
      </a-form-item>
      <a-form-item label="参数说明" name="paramRemark">
        <a-input v-model:value="form.paramRemark" placeholder="请输入参数说明" />
      </a-form-item>
      <a-form-item label="参数值" name="paramValue">
        <a-input v-model:value="form.paramValue" placeholder="请输入参数值" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>
<script lang="ts" setup>
import { defineEmits, defineProps, defineExpose, ref } from 'vue'
import type { Rule, FormInstance } from 'ant-design-vue/es/form'
import { updateSystemParams } from '@/api/services/permission'
import { message } from 'ant-design-vue'
interface FormState {
  paramId: string
  paramRemark: string
  paramValue: string
}

defineProps({
  open: {
    type: Boolean,
    default: false, // 设置默认值为 0
  },
  disabled: Boolean,
})

const emits = defineEmits(['modalHandleOk', 'modalCancel'])
const formRef = ref<FormInstance>()
const labelCol = { span: 5 }
const wrapperCol = { span: 13 }
const addon = ref<string>('')
const modalType = ref<string>('')
const confirmLoading = ref<boolean>(false)
const form = ref<FormState>({
  paramId: '',
  paramRemark: '',
  paramValue: '',
})

const rules: Record<string, Rule[]> = {
  paramId: [{ required: true, message: '请输入参数编号', trigger: 'blur' }],
  paramRemark: [{ required: true, message: '请输入参数说明', trigger: 'blur' }],
  paramValue: [{ required: true, message: '请输入参数值', trigger: 'blur' }],
}

function modalHandleOk() {
  formRef.value &&
    formRef.value
      .validate()
      .then(() => {
        const { paramId, paramRemark, paramValue } = form.value
        confirmLoading.value = true
        const data = {
          paramId,
          paramRemark,
          paramValue,
        }
        if (modalType.value === 'edit') {
          updateSystemParams(data)
            .then(() => {
              emits('modalHandleOk', form.value)
              message.success('编辑成功!')
            })
            .finally(() => {
              confirmLoading.value = false
            })
        }
      })
      .catch((error: any) => {
        console.log(error)
      })
}

function cancel() {
  emits('modalCancel')
}

function show(type: string, data?: Record<string, any>) {
  resetForm()
  modalType.value = type
  if (type === 'edit') {
    addon.value = ''
    editForm(data)
  }
}

const resetForm = () => {
  formRef.value?.resetFields()
  form.value = {
    paramId: '',
    paramRemark: '',
    paramValue: '',
  }
}

function editForm(data?: any) {
  if (data) {
    const copyData = JSON.parse(JSON.stringify(data))
    form.value = copyData
  }
}
defineExpose({ show })
</script>
