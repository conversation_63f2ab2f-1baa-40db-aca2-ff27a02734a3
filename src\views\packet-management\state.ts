import {
  addNewIndexGroup,
  getIndexGroupTreeList,
  modifyIndexGroupData,
} from '@/api/indexmanage/indexmanage/indexmanage'
import { message, type TreeProps } from 'ant-design-vue'
import { ref, watch, reactive, computed } from 'vue'
export default function usePacketManagementState() {
  const loading = ref(false)
  const transformData = (data: any[]) => {
    return data.map((item) => {
      const newItem = {
        ...item,
        title: item.groupName,
        key: item.groupId,
      }
      if (item.children) {
        newItem.children = transformData(item.children)
      }
      return newItem
    })
  }
  const treeData = ref<TreeProps['treeData']>([])

  const expandedKeys = ref<string[]>([])
  const selectedKeys = ref<string[]>([])
  watch(expandedKeys, () => {
    console.log('expandedKeys', expandedKeys)
  })
  watch(selectedKeys, () => {
    console.log('selectedKeys', selectedKeys)
  })

  // 控制添加节点弹窗的显示
  const showAddNodeModal = ref(false)
  // 当前操作的节点
  const currentNode = reactive({
    key: '',
    title: '',
  })

  const handleAddNode = (key: string, title: string) => {
    currentNode.key = key
    currentNode.title = title
    showAddNodeModal.value = true
  }

  const handleAddNodeConfirm = async (data: { parentKey: string; nodeName: string }) => {
    loading.value = true
    // 假设添加成功后的逻辑
    const findAndAddNode = (treeNodes: any[], parentKey: string, newNode: any): boolean => {
      for (let i = 0; i < treeNodes.length; i++) {
        const node = treeNodes[i]
        if (node.key === parentKey) {
          // 如果找到父节点，添加新节点
          if (!node.children) {
            node.children = []
          }
          // 创建新节点，生成唯一key
          const newNodeKey = `new-${Date.now()}`
          node.children.push({
            title: newNode.nodeName,
            key: newNodeKey,
            groupName: newNode.nodeName,
            groupId: newNodeKey,
          })
          return true
        }
        // 如果当前节点有子节点，递归搜索
        if (node.children && node.children.length > 0) {
          if (findAndAddNode(node.children, parentKey, newNode)) {
            return true
          }
        }
      }
      return false
    }
    // 复制一份treeData，然后修改它
    const newTreeData = Array.isArray(treeData.value) ? [...treeData.value] : []
    findAndAddNode(newTreeData, data.parentKey, { nodeName: data.nodeName })
    // 更新treeData
    treeData.value = newTreeData

    showAddNodeModal.value = false
    // 调用API添加节点
    const res = await addNewIndexGroup({
      groupName: data.nodeName,
      parentId: data.parentKey,
    })
    if (res.code === '000000') {
      message.success('添加成功')
      await getTreeData()
      loading.value = false
    } else {
      loading.value = false
      message.error('添加失败')
    }
  }

  const handleAddNodeCancel = () => {
    showAddNodeModal.value = false
  }

  // 控制修改节点弹窗的显示
  const showEditNodeModal = ref(false)
  // 当前编辑的节点
  const currentEditNode = reactive({
    key: '',
    title: '',
  })
  // 当前编辑节点的父节点ID
  const currentEditNodeParentId = ref('')

  // 获取当前节点的父节点ID
  const findParentKey = (nodes: any[], childKey: string, parentKey: string = ''): string => {
    for (const node of nodes) {
      if (node.key === childKey) {
        return parentKey
      }

      if (node.children && node.children.length > 0) {
        const foundParentKey = findParentKey(node.children, childKey, node.key)
        if (foundParentKey) {
          return foundParentKey
        }
      }
    }
    return ''
  }

  // 计算可用作父节点的节点列表（排除当前节点及其子节点）
  const availableParentNodes = computed(() => {
    const allNodes: Array<{ key: string; title: string }> = []

    // 递归收集所有节点
    const collectNodes = (nodes: any[], currentKey: string, excludeKeys: Set<string>) => {
      for (const node of nodes) {
        if (!excludeKeys.has(node.key)) {
          allNodes.push({
            key: node.key,
            title: node.title,
          })
        }

        if (node.children && node.children.length > 0) {
          collectNodes(node.children, currentKey, excludeKeys)
        }
      }
    }

    // 收集当前节点的所有子节点key
    const collectChildKeys = (nodes: any[], targetKey: string, keys: Set<string>) => {
      for (const node of nodes) {
        if (node.key === targetKey) {
          keys.add(node.key)
          if (node.children) {
            for (const child of node.children) {
              keys.add(child.key)
              if (child.children) {
                collectChildKeys([child], child.key, keys)
              }
            }
          }
          return true
        }

        if (node.children && node.children.length > 0) {
          if (collectChildKeys(node.children, targetKey, keys)) {
            return true
          }
        }
      }
      return false
    }

    // 当有选中节点时才执行
    if (currentEditNode.key) {
      // 收集要排除的节点key（当前节点及其所有子节点）
      const excludeKeys = new Set<string>()
      collectChildKeys(treeData.value || [], currentEditNode.key, excludeKeys)

      // 收集所有可用的父节点
      collectNodes(treeData.value || [], currentEditNode.key, excludeKeys)
    }

    return allNodes
  })

  // 在选择节点进行编辑时，更新父节点ID
  const updateCurrentParentId = () => {
    if (currentEditNode.key) {
      // 确保 treeData.value 不为 undefined
      const treeDataValue = treeData.value || []
      currentEditNodeParentId.value = findParentKey(treeDataValue, currentEditNode.key)
    }
  }

  const handleEdit = (key: string) => {
    // 找到对应节点的信息
    const findNode = (nodes: any[], targetKey: string): any => {
      for (const node of nodes) {
        if (node.key === targetKey) {
          return node
        }

        if (node.children && node.children.length > 0) {
          const foundNode = findNode(node.children, targetKey)
          if (foundNode) {
            return foundNode
          }
        }
      }
      return null
    }

    const node = findNode(treeData.value || [], key)
    if (node) {
      currentEditNode.key = node.key
      currentEditNode.title = node.title
      // 更新父节点ID
      updateCurrentParentId()
      showEditNodeModal.value = true
    }
  }

  const handleEditNodeConfirm = async (data: {
    nodeKey: string
    newParentKey: string
    newNodeName: string
  }) => {
    loading.value = true
    showEditNodeModal.value = false
    try {
      // 调用API修改节点
      const res = await modifyIndexGroupData({
        groupId: data.nodeKey,
        groupName: data.newNodeName,
        parentId: data.newParentKey,
      })
      if (res.code === '000000') {
        message.success('修改成功')
        await getTreeData()
        loading.value = false
      } else {
        loading.value = false
      }
    } catch (error) {
      console.log('修改节点失败', error)
      loading.value = false
      message.error('修改失败')
    }
  }

  const handleEditNodeCancel = () => {
    showEditNodeModal.value = false
  }

  const handleDeleteConfirm = async (key: string) => {
    console.log('确认删除节点', key)
    loading.value = true
    try {
      // 调用API删除节点
      const res = await modifyIndexGroupData({
        groupId: key,
        status: 0,
      })
      if (res.code === '000000') {
        message.success('删除成功')
        await getTreeData()
        loading.value = false
      } else {
        loading.value = false
        message.error('删除失败')
      }
    } catch (error) {
      console.log('删除节点失败', error)
      loading.value = false
      message.error('删除失败')
    }
  }

  const handleAddRootNode = () => {
    handleAddNode('', '根节点')
  }

  const getTreeData = async () => {
    loading.value = true
    const res = await getIndexGroupTreeList()
    treeData.value = transformData(res.data)
    loading.value = false
  }

  onMounted(() => {
    getTreeData()
  })

  return {
    treeData,
    expandedKeys,
    selectedKeys,
    showAddNodeModal,
    currentNode,
    handleAddNode,
    handleAddNodeConfirm,
    handleAddNodeCancel,
    showEditNodeModal,
    currentEditNode,
    currentEditNodeParentId,
    handleEdit,
    handleEditNodeConfirm,
    handleEditNodeCancel,
    handleDeleteConfirm,
    availableParentNodes,
    handleAddRootNode,
    loading,
  }
}
