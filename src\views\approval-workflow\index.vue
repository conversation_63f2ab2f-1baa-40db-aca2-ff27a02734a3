<template>
  <div class="workflow-page">
    <cardBox
      title="流程审批配置"
      subTitle="流程审批配置是用于管理审批流程的工具。它定义了审批的各个环节、审批人、审批顺序等。"
    >
      <template #headerRight>
        <a-button type="primary" @click="showModal = true">新增工作流</a-button>
      </template>
      <Table
        :columns="columns"
        :getData="getApprovalWorkflowList"
        ref="tableRef"
        :searchFormState="searchForm"
      >
        <template #search>
          <a-form-item label="">
            <a-input
              v-model:value="searchForm.name"
              placeholder="请输入名称"
              style="width: 200px"
              @pressEnter="handleSearch"
            />
          </a-form-item>
        </template>
        <template #bodyCell="{ column, record, text }">
          <template v-if="column.key === 'id'">
            <a-tooltip :title="text" placement="top">
              <div class="text-ellipsis">{{ text }}</div>
            </a-tooltip>
          </template>
          <template v-if="column.key === 'description'">
            <a-tooltip :title="text" placement="top">
              <div class="text-ellipsis">{{ text }}</div>
            </a-tooltip>
          </template>
          <template v-if="column.key === 'category'">
            {{ WorkflowTypeMap[record.category as keyof typeof WorkflowTypeMap] }}
          </template>
          <template v-if="column.key === 'operation'">
            <a-button size="small" type="link" @click="editWorkflow(record)"> 编辑 </a-button>
            <a-popconfirm title="确定删除吗？" @confirm="handleDelete(record)">
              <a-button size="small" type="link">删除</a-button>
            </a-popconfirm>
          </template>
        </template>
      </Table>
    </cardBox>

    <a-modal
      :title="modalTitle"
      :open="showModal"
      :footer="null"
      width="800px"
      :closable="false"
      destroy-on-close
    >
      <workflow-form :data="editingWorkflow" @save="handleSave" @cancel="closeModal" />
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { message } from 'ant-design-vue'
import WorkflowForm from './components/workflow-form.vue'
import type { ListItem } from './type'
import { listItem2WorkflowItem } from './transform-util'
import { WorkflowTypeMap } from './constant'
import {
  addWorkflow,
  deleteWorkflow,
  getApprovalWorkflowList,
  updateWorkflow,
} from '@/api/approval-workflow'
import cardBox from '@/components/card-box/card-box.vue'
import { Table } from '@fs/fs-components'

// 响应式数据
const showModal = ref(false)
const tableRef = ref<any>()
const editingWorkflow = ref<any>(null)

// 查询表单数据
const searchForm = reactive({
  id: '',
  name: '',
})

// 分页配置
const pagination = reactive({
  pageSize: 10,
  current: 1,
  total: 0,
  showSizeChanger: true,
  pageSizeOptions: ['1', '10', '50', '100'],
})

// 表格数据
const dataSource = ref<ListItem[]>([])

// 计算属性
const modalTitle = computed(() => {
  return editingWorkflow.value ? '编辑审批' : '新增审批'
})

// 表格列配置
const columns = [
  {
    title: '名称',
    dataIndex: 'name',
    key: 'name',
    align: 'center',
    width: 150,
  },
  {
    title: '描述',
    dataIndex: 'description',
    align: 'center',
    key: 'description',
    width: 280,
  },
  {
    title: '类型',
    dataIndex: 'workflowTypeName',
    key: 'workflowTypeName',
    align: 'center',
    width: 100,
  },
  {
    title: '通知地址',
    dataIndex: 'pushResultUrl',
    align: 'center',
    key: 'pushResultUrl',
    width: 300,
  },
  {
    title: '更新时间',
    dataIndex: 'updatedTime',
    align: 'center',
    key: 'updatedTime',
    width: 180,
  },
  {
    title: '更新人',
    dataIndex: 'updatedUserName',
    align: 'center',
    key: 'updatedUserName',
    width: 120,
  },
  {
    title: '操作',
    key: 'operation',
    width: 150,
    fixed: 'right',
  },
]

// 查询数据源
const queryDataSource = () => {
  tableRef.value?.getTableData()
}

// 保存处理
const handleSave = async (approval: ListItem, callback: () => void) => {
  const apiData = listItem2WorkflowItem(approval)
  console.log('%c 保存审批', 'background: blue; color: white;', approval)
  console.groupCollapsed('转换数据')
  console.table(apiData)
  console.groupEnd()
  console.groupCollapsed('转换数据.nodes')
  console.table(apiData.nodes)
  console.groupEnd()

  if (editingWorkflow.value?.id) {
    // 调用接口更新
    try {
      const res = await updateWorkflow({ ...apiData, id: approval.id } as any)
      if (res.code === '000000') {
        message.success('更新成功')
        showModal.value = false
        queryDataSource()
      } else {
        message.error(res.msg || '更新失败')
      }
    } catch (error) {
      // this.$store.commit('SET_IS_SPINNING', false)
      message.error('更新失败')
    } finally {
      callback()
    }
    editingWorkflow.value = null
  } else {
    // 新增审批
    // 调用接口新增
    try {
      const res = await addWorkflow(apiData as any)
      if (res.code === '000000') {
        // dataSource.value.push(approval)
        message.success('新增成功')
        showModal.value = false
        queryDataSource()
      } else {
        message.error(res.msg || '新增失败')
      }
    } catch (error) {
      // this.$store.commit('SET_IS_SPINNING', false)
      message.error('新增失败')
    } finally {
      callback()
    }
  }
}

// 关闭模态框
const closeModal = () => {
  showModal.value = false
  editingWorkflow.value = null
}

// 编辑工作流
const editWorkflow = (record: ListItem) => {
  console.log('编辑', record)
  editingWorkflow.value = record
  showModal.value = true
}

// 删除处理
const handleDelete = async (record: ListItem) => {
  // 调用接口删除
  try {
    const res = await deleteWorkflow(record.id!)
    if (res.code === '000000') {
      dataSource.value = dataSource.value.filter((item) => item.id !== record.id)
      message.success('删除成功')
      queryDataSource()
    } else {
      message.error(res.msg || '删除失败')
    }
  } catch (error) {
    // 关闭全局loading遮罩层
    // this.$store.commit('SET_IS_SPINNING', false)
    message.error('删除失败')
  }
}

// 查询方法
const handleSearch = () => {
  // 重置到第一页
  pagination.current = 1
  queryDataSource()
}
</script>

<style lang="less" scoped>
.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
}

.workflow-page {
  padding: 24px;
  height: calc(100vh - 40px);

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    .left {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      .title {
        font-size: 20px;
        font-weight: bold;
        color: #141414;
        word-break: break-all;
        margin-bottom: 0;
      }
      .desc {
        font-size: 14px;
        color: #999;
        margin-top: 5px;
        word-break: break-all;
      }
    }
    .right {
      display: flex;
      align-items: center;
    }
  }
}

.search-form {
  margin-bottom: 24px;
}
:deep(.ant-table-small) {
  border-width: 0;
}
</style>
