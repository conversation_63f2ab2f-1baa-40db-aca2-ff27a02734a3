import type { UploadFile } from 'ant-design-vue'

export type TaskStatus = 'PENDING' | 'PROCESSING' | 'FINISH' | 'CANCELLED'
export interface TaskItem {
  projectId?: number | string
  createUser?: string
  file?: UploadFile<any> | null
  fileList?: UploadFile<any>[]
  jobName?: string
  owner?: string
  id?: number | string
  taskId?: number | string
  dataType?: DotType
  modelType?: DotModelType
  connectionInfo?: any
  modelId?: string
}

export interface TaskParam {
  pageIndex?: number | string
  pageSize?: number | string
  jobName?: string
  projectId?: number | string
}

export enum ShowPage {
  EXTRACT = 'Extract',
  SCHEMA = 'Schema',
}

export enum ShowChart {
  SCHEMA = 'Schema',
  TREE = 'tree',
}

export enum ShowDotPage {
  DESCRIPTION = 'Description',
  PROP = 'Prop',
  RELATIONSHIP = 'Relationship',
  SAMPLING = 'Sampling',
}

// 上传数据类型
export enum DotType {
  DOTDATA,
  RELATIONSHIP,
}

// 模型服务类型
export enum DotModelType {
  FILE,
  DATABASE,
}

// 算法类型
export enum AlgorithmType {
  Centrality, // 中心性算法
  Community, // 社区检测算法
  Similarity, // 相似度算法
  Path, // 路径查找算法
  Dag, // Dag算法
  Node, // 节点嵌入算法
}
