export const columns = [
  {
    title: '用户',
    dataIndex: 'userName',
    key: 'userName',
  },
  {
    title: '列配置',
    dataIndex: 'columns',
    key: 'columns',
  },
  {
    title: '创建人',
    dataIndex: 'createUser',
    key: 'createUser',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
  },
  {
    title: '操作',
    dataIndex: 'operation',
  },
]

export enum ActionType {
  ADD = 'ADD',
  EDIT = 'EDIT',
}

export interface AuthItem {
  tableId: string // 数据表ID
  userId: string // 用户ID
  userName: string // 资产状态1已发布0未发布
  configValue: string // "行级别配置信息[{"columnName":"id"
  columns: string // 字段列权限配置，逗号隔开
  createTime: string // 创建时间
  createUser: string // 创建人
  updateTime: string // 更新时间
  updateUser: string // 更新人
  permissionId?: string // 权限ID
}

export interface ParamItem {
  columnType: string // 字段类型
  isNullable: string // 是否为空
  dataType: string // 数据类型
  columnComment: string // 字段备注
  ordinalPosition: string // 字段序号
  columnKey: string // 字段键类型
  tableName: string // 表名
  selected: boolean // 是否选中
  columnName: string // 字段名
}

export interface UserItem {
  deptName: string
  disable: boolean
  jobName: string
  userId: string
  userName: string
}
