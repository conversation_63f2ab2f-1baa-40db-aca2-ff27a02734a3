export const columns = [
  {
    title: '组件描述',
    dataIndex: 'compDesc',
    key: 'compDesc',
  },
  {
    title: '最新运行情况',
    dataIndex: 'time',
    key: 'time',
  },
  {
    title: '对应作业流实例',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '操作',
    dataIndex: 'operation',
    key: 'operation',
  },
]

export const detailColumns = [
  {
    title: '名称',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '列',
    dataIndex: 'column',
    key: 'column',
  },
  {
    title: '最近运行',
    dataIndex: 'runtime',
    key: 'runtime',
  },
  {
    title: '事件',
    dataIndex: 'event',
    key: 'event',
  },
  {
    title: '操作',
    dataIndex: 'operation',
    key: 'operation',
  },
]

export interface ResQualityList {
  compDesc: string
  endTime: string
  flowInstanceId: string
  startTime: string
  state: string
}

export interface ResDetailData {
  compDesc?: string // 质量描述
  compId?: string // 组件id
  executeFailedCnt?: number // 失败次数
  executeSuccessCnt?: number // 成功次数
  failedPct?: number // 失败率
  successPct?: number // 成功率
  totalExecuteCnt?: number // 总执行次数
}

export interface ResBarChartData {
  xAxis?: Array<string>
  name?: Array<string>
  data?: Array<{ [key: string]: any }>
  result?: Array<'通过' | '不通过'>
}
