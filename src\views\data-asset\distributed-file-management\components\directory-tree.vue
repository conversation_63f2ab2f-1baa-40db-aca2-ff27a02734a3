<template>
  <div class="directory-tree">
    <div class="title">HDFS文件管理</div>
    <div class="title-header">
      <a-select
        v-model:value="modalForm.id"
        placeholder="请选择数据源"
        style="width: 100%"
        show-search
        @change="handleChange($event)"
      >
        <a-select-option v-for="(item, index) in scameInfoList" :key="index" :value="item.id">{{
          item.dbDesc
        }}</a-select-option>
      </a-select>
      <a-tooltip placement="top">
        <template #title>
          <span>新增根目录</span>
        </template>
        <FolderAddOutlined style="font-size: 20px; margin-left: 12px" @click="showAddModal" />
      </a-tooltip>
    </div>
    <!-- 先不做搜索 -->
    <!-- <a-input
      v-model:value="searchName"
      placeholder="请输入内容"
      :bordered="true"
      @change="handleSearch"
      style="margin-bottom: 10px"
    /> -->
    <a-tree
      v-if="filteredTreeData.length > 0"
      :tree-data="filteredTreeData"
      v-model:expandedKeys="expandedKeys"
      v-model:selectedKeys="selectedKeys"
      :fieldNames="{
        children: 'children',
        title: 'name',
        key: 'id', // 最好换一个
      }"
      class="custom-tree"
      showIcon
      :load-data="onLoadData"
      @select="handleSelect"
      @expand="handleExpand"
    >
      <template #icon="{ dataRef }">
        <FolderOutlined style="font-size: 18px" v-if="dataRef.isDirectory" />
        <DatabaseOutlined style="font-size: 18px" v-else />
      </template>
      <template #title="{ name, dataRef }">
        <div class="tree-node-content">
          <span class="node-name">{{ name }}</span>
          <div class="node-actions">
            <a-dropdown>
              <PlusOutlined class="action-icon" @click.stop />
              <template #overlay>
                <a-menu>
                  <a-menu-item
                    key="1"
                    v-if="dataRef.isDirectory"
                    @click="handleAddDirectory(dataRef)"
                    >新增文件目录</a-menu-item
                  >
                </a-menu>
              </template>
            </a-dropdown>
            <a-dropdown v-if="dataRef.isDirectory">
              <MoreOutlined class="action-icon" @click.stop />
              <template #overlay>
                <a-menu>
                  <a-menu-item key="1" @click="handleRename(dataRef)">重命名</a-menu-item>
                  <a-menu-item key="2" @click="handleDelete(dataRef)">删除</a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </div>
        </div>
      </template>
    </a-tree>
    <div class="no-data" v-else>暂无数据</div>
    <!-- 新增/编辑弹窗 -->
    <a-modal
      v-model:visible="modalVisible"
      :title="modalTitle"
      @ok="handleModalOk"
      @cancel="handleModalCancel"
    >
      <a-form
        ref="formRef"
        :model="modalForm"
        :rules="rules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 16 }"
      >
        <a-form-item label="数据库名称" name="dbDesc">
          <a-input
            v-model:value="modalForm.dbDesc"
            :disabled="true"
            placeholder="请输入数据库名称"
          />
        </a-form-item>
        <a-form-item label="目录名称" name="name">
          <a-input v-model:value="modalForm.name" placeholder="请输入目录名称" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import {
  addCategoryItem,
  getFileDirectoryById,
  getScameInfoList,
  editCategoryName,
  type IScameInfoList,
  type IScameItemParams,
  deleteCategoryItem,
} from '@/api/services/data-asset/distributed-file-management'
import {
  FolderAddOutlined,
  FolderOutlined,
  DatabaseOutlined,
  PlusOutlined,
  MoreOutlined,
} from '@ant-design/icons-vue'
import { message, Modal, type TreeProps } from 'ant-design-vue'
import { useDirectoryTreeStore } from '@/stores/page/directory-tree'
import type { RuleObject } from 'ant-design-vue/es/form'
import { useUserStore } from '@/stores/user'

const props = defineProps<{
  params?: any
  refreshCount?: number
}>()

const emit = defineEmits(['add-directory-tree'])
const directoryTreeStore = useDirectoryTreeStore()
const { setTreeDbData } = directoryTreeStore
// 数据源列表
const scameInfoList = ref<IScameInfoList[]>([])
// const scameId = ref<any>(null)
const initForm = () => {
  return {
    id: undefined,
    name: '',
    dbDesc: '', // 展示名称
    fullPath: '', // 用来存放全路径
  }
}

// 弹窗相关
const modalVisible = ref(false)
const modalTitle = ref('新增目录')
const formRef = ref()
const modalForm = ref<Record<string, any>>(initForm())

const validateName = async (_rule: RuleObject, value: string) => {
  if (value === '') {
    return Promise.reject('请输入目录名称')
  }
  if (/[^\w\s\u4e00-\u9fa5]/.test(value)) {
    return Promise.reject('输入的目录名称里不支持包含特殊符号')
  }
  return Promise.resolve()
}

const rules = {
  name: [{ required: true, validator: validateName }],
  id: [{ required: true, message: '请输入数据库信息编号' }],
}

// 目录树相关
const expandedKeys = ref<string[]>([])
const selectedKeys = ref<string[]>([])
const treeData = ref<any>([])
// 创建过滤后的目录树数据
const filteredTreeData = computed(() => {
  return filterLeaves(treeData.value, [])
})

const filterLeaves = (nodes: any, target: any[]) => {
  if (!Array.isArray(nodes)) {
    return target
  }
  for (const node of nodes) {
    const children = node.children
    if (node.isDirectory) {
      const clone = { ...node }
      if (Array.isArray(children)) {
        clone.children = filterLeaves(children, [])
      }
      target.push(clone)
    }
  }
  return target
}

onMounted(async () => {
  await fetchScameInfoList()
  modalForm.value = {
    ...modalForm.value,
    id: scameInfoList.value?.length > 0 ? scameInfoList.value[0].id : null,
    dbDesc: scameInfoList.value?.length > 0 ? scameInfoList.value[0].dbDesc : null,
  }
  fetchData()
})

// 切换数据源
function handleChange(params: string) {
  modalForm.value.id = params
  modalForm.value.dbDesc = scameInfoList.value?.find((item) => `${item.id}` === `${params}`)?.dbDesc
  fetchData()
  // 重置右侧文件列表
  emit('add-directory-tree', {
    id: modalForm.value.id,
    fullPath: modalForm.value.fullPath,
    list: [],
  })
}

const showAddModal = () => {
  modalTitle.value = '新增目录'
  modalForm.value = {
    ...modalForm.value,
    name: '',
  }
  modalVisible.value = true
}

// 新增/编辑
const handleModalOk = async () => {
  try {
    await formRef.value.validate()
    // 组装全路径
    let arr = `${modalForm.value.fullPath}`.split('/')
    let newStr = [...arr.slice(0, -1), `${modalForm.value.name}`].join('/')
    let res: any
    if (modalTitle.value === '新增目录') {
      // 新建的一定在顶层
      const newFullPath = modalForm.value.fullPath.length === 0 ? `/${newStr}` : newStr
      const params: any = {
        path: newFullPath,
        id: modalForm.value.id,
      }
      res = await addCategoryItem(params)
    } else {
      const params: any = {
        newPath: newStr,
        id: modalForm.value.id,
        oldPath: modalForm.value.fullPath,
      }
      res = await editCategoryName(params)
    }
    if (res?.code === '000000') {
      await handleAssetSuccess()
      modalForm.value = {
        ...modalForm.value,
        name: '',
        fullPath: '',
      }
      message.success('操作成功')
      modalVisible.value = false
    }
  } catch (error) {
    console.error('操作失败:', error)
  }
}

// 更新文件
const handleAssetSuccess = () => {
  // 重新加载当前节点的子节点数据
  if (modalTitle.value !== '新增目录') {
    // 重命名情况：更新当前节点名称
    const updateNodeName = (nodes: any[]) => {
      for (let node of nodes) {
        if (node.id === modalForm.value.fullPath) {
          node.name = modalForm.value.name
          return true
        }
        if (node.children && node.children.length > 0) {
          if (updateNodeName(node.children)) return true
        }
      }
      return false
    }
    updateNodeName(treeData.value)
  } else {
    // 新增目录情况：添加到父节点下
    const addToParent = (nodes: any[]) => {
      for (let node of nodes) {
        if (`${node.id}/` === modalForm.value.fullPath || modalForm.value.fullPath.length === 0) {
          // 确保children数组存在
          if (!node.children) {
            node.children = []
          }
          const newFullPath =
            modalForm.value.fullPath.length === 0
              ? `/${modalForm.value.name}`
              : `${modalForm.value.fullPath}/${modalForm.value.name}`
          // 创建新节点
          const newNode = {
            path: modalForm.value.name,
            name: modalForm.value.name,
            id: newFullPath,
            isLeaf: true,
            isDirectory: true,
            children: [],
          }
          // console.log('node:',node,nodes)
          // 添加新节点
          if (modalForm.value.fullPath.length === 0) {
            nodes.unshift(newNode)
          } else {
            node.children.unshift(newNode)
          }
          // 更新父节点的isLeaf状态
          node.isLeaf = false
          return true
        }
        if (node.children && node.children.length > 0) {
          if (addToParent(node.children)) return true
        }
      }
      return false
    }
    addToParent(treeData.value)
  }
}

const handleModalCancel = () => {
  modalVisible.value = false
  formRef.value?.resetFields()
}

const onLoadData: TreeProps['loadData'] = (treeNode: any) => {
  return new Promise<void>((resolve) => {
    if (!treeNode.dataRef.isDirectory) {
      resolve()
      return
    }
    if (treeNode.dataRef.path) {
      handelTreeData(treeNode, resolve)
    }
  })
}

// 在嵌套结构中查找匹配项并追加子节点
function appendChildOnMatch(
  array: any[],
  targetKey: string,
  targetValue: string,
  newChild: any[],
  childrenKey = 'children',
) {
  // 遍历当前层级的每个元素
  for (let i = 0; i < array.length; i++) {
    const item = array[i]

    // 检查当前元素是否匹配
    if (item[targetKey] === targetValue) {
      // 确保子节点数组存在
      if (!Array.isArray(item[childrenKey])) {
        item[childrenKey] = []
      }

      // 追加子节点（处理单个对象或数组）
      if (Array.isArray(newChild)) {
        item[childrenKey] = [...newChild]
      } else {
        item[childrenKey].push(newChild)
      }
      return true
    }

    // 如果当前元素有子数组，则递归搜索
    if (item[childrenKey] && item[childrenKey].length > 0) {
      const found = appendChildOnMatch(
        item[childrenKey],
        targetKey,
        targetValue,
        newChild,
        childrenKey,
      )

      // 找到匹配项后提前返回
      if (found) return true
    }
  }

  return false
}
const handelTreeData = (treeNode: any, resolve: () => void) => {
  modalForm.value.fullPath = treeNode.dataRef.id
  getFileDirectoryById({
    path: treeNode.dataRef.id,
    id: modalForm.value.id,
  })
    .then(({ data }) => {
      // 是否存在非目录子级
      const hasChild = data.filter((item) => !item.isDirectory)
      emit('add-directory-tree', {
        id: modalForm.value.id,
        fullPath: modalForm.value.fullPath,
        list: hasChild.length !== 0 ? [...hasChild] : [],
        treeNode,
      })
      if (data.length > 0) {
        const list = data.map((item) => ({
          ...item,
          id: `${treeNode.dataRef.id}/${item.path}`,
          name: item.path,
          isLeaf: !item.isDirectory,
        }))
        if (!treeNode?.dataRef?.children) treeNode.dataRef.children = []
        treeNode.dataRef.children = [...list]
        // 改变目录树
        appendChildOnMatch(
          treeData.value,
          'fullPath',
          treeNode.dataRef.fullPath,
          treeNode.dataRef.children,
        )

        // 结果验证
        // console.log(treeData.value,'追加新子节点');
        treeData.value = [...treeData.value]
        setTreeDbData('treeData', treeData.value)
      } else {
        console.error('加载数据失败')
      }
      resolve()
    })
    .catch((error) => {
      console.error('加载数据失败:', error)
      resolve()
    })
}

// 点击树节点触发
const handleSelect = (selectedKeysArr: string[], info: any) => {
  modalForm.value.fullPath = selectedKeysArr?.[0] || modalForm.value.fullPath
  setTreeDbData('selectedKeys', selectedKeysArr)
  setTreeDbData('expandedKeys', expandedKeys.value)
  setTreeDbData('treeData', treeData.value)
  const dataRef = info.node.dataRef
  setTreeDbData('dataRef', dataRef)
  // 点击节点后需要重新组装树结构
  handelTreeData(info.node, () => {})
  // 要存在文件 而非文件夹
  const filedList = dataRef?.children?.filter((item: any) => !item.isDirectory) ?? []
  emit('add-directory-tree', {
    id: modalForm.value.id,
    fullPath: modalForm.value.fullPath,
    list: [...filedList],
    treeNode: info.node,
  })
}
const handleExpand = (expandedKeysArr: string[]) => {
  expandedKeys.value = expandedKeysArr
  setTreeDbData('expandedKeys', expandedKeysArr)
  setTreeDbData('treeData', treeData.value)
}

const fetchScameInfoList = async () => {
  try {
    const userStore = useUserStore()
    const params: IScameItemParams = {
      pageIndex: 1,
      pageSize: 9999,
      dbType: 21,
      ownerId: userStore.userInfo.loginAccount,
    }
    const res = await getScameInfoList(params)
    if (res.code === '000000') {
      scameInfoList.value = [...res.data.records]
    }
  } catch (error) {
    console.error('获取数据失败:', error)
  }
}

// 重命名
const handleRename = (data: Record<string, any>) => {
  modalTitle.value = '重命名目录'
  modalForm.value = {
    ...modalForm.value,
    name: data.path,
    fullPath: data.id,
  }
  modalVisible.value = true
}

// 删除
const handleDelete = (data: Record<string, any>) => {
  Modal.confirm({
    title: '确认删除',
    content: '是否确认删除该目录？',
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      try {
        await deleteCategoryItem({ id: modalForm.value.id, path: data.id })
        message.success('删除成功')

        // 从树形数据中删除节点
        const removeNode = (nodes: any[]): boolean => {
          for (let i = 0; i < nodes.length; i++) {
            if (nodes[i].id === data.id) {
              nodes.splice(i, 1)
              return true
            }
            if (nodes[i].children && nodes[i].children.length > 0) {
              if (removeNode(nodes[i].children)) return true
            }
          }
          return false
        }

        removeNode(treeData.value)
        // 强制更新树形数据
        treeData.value = JSON.parse(JSON.stringify(treeData.value))
      } catch (error) {
        console.error('删除失败:', error)
      }
    },
  })
}

const handleAddDirectory = (data: Record<string, any>) => {
  modalTitle.value = '新增目录'
  modalForm.value = {
    ...modalForm.value,
    name: modalForm.value.name,
    fullPath: `${data.id}/${modalForm.value.name}`,
  }
  modalVisible.value = true
}

const fetchData = async (path = '/') => {
  try {
    if (!modalForm.value.id) {
      return
    }
    const params = {
      path,
      id: modalForm.value.id,
    }
    const res = await getFileDirectoryById(params)
    if (res.code === '000000') {
      treeData.value = res.data.map((item: any) => {
        return {
          isLeaf: false,
          ...item,
          id: `/${item.path}`,
          name: item.path,
        }
      })
      setTreeDbData('treeData', treeData.value)
    }
  } catch (error) {
    treeData.value = []
    console.error('获取数据失败:', error)
  }
}

watch(
  () => props.refreshCount,
  (newData) => {
    if (newData && newData > 0) {
      onLoadData(props.params.treeNode)
    }
  },
  { immediate: true },
)

defineExpose({
  fetchData,
})
</script>

<style lang="less" scoped>
.directory-tree {
  height: 100%;

  .custom-tree {
    margin-top: 10px;
    :deep(.ant-tree-node-content-wrapper) {
      width: 100%;
      &:hover {
        background-color: #f5f5f5;
      }
    }
    :deep(.ant-tree-node-selected) {
      background-color: #e6f7ff;
    }
    :deep(.ant-tree-title) {
      width: 100%;
    }
    .tree-node-content {
      width: 100%;
      padding-right: 10px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .node-name {
        flex: 1;
        white-space: nowrap;
      }
      .node-actions {
        display: none;
      }
      &:hover .node-actions {
        display: flex;
        align-items: center;
        gap: 8px;

        .action-icon {
          font-size: 16px;
          color: #666;
          cursor: pointer;

          &:hover {
            color: #1890ff;
          }
        }
      }
    }
  }

  :deep(.ant-tree) {
    .ant-tree-treenode {
      width: 100%;
      height: 40px;
      display: flex;
      align-items: center;
      .ant-tree-switcher {
        line-height: 40px;
      }
      .ant-tree-iconEle {
        width: 18px;
        height: 18px;
        line-height: 18px;
        margin-right: 5px;
      }
      .ant-tree-title {
        width: 100%;
      }
      .ant-tree-node-content-wrapper {
        width: 100%;
        height: 40px;
        display: flex;
        align-items: center;
      }
    }
  }
  .no-data {
    height: calc(100% - 100px);
    width: 100%;
    text-align: center;
    color: rgba(0, 0, 0, 0.25);
  }
  .title {
    margin-right: auto;
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 24px;
    padding-bottom: 16px;
    display: flex;
    justify-content: space-between;
    padding-right: 12px;
  }
  .title-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 12px;
    flex-wrap: nowrap;
  }
}
</style>
