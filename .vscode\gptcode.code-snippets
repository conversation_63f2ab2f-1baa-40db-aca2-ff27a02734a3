{"a-affix": {"prefix": "fs-a-affix", "body": ["/**", " * 根据 <template> 模板中 <a-affix> 组件，生成其相关属性的数据声明、事件函数等", "**/"], "description": "<a-affix> 提示指令"}, "a-anchor": {"prefix": "fs-a-anchor", "body": ["/**", " * 根据 <template> 模板中 <a-anchor> 组件，生成其相关属性的数据声明、事件函数等", "**/"], "description": "<a-anchor> 提示指令"}, "a-auto-complete": {"prefix": "fs-a-auto-complete", "body": ["/**", " * 根据 <template> 模板中 <a-auto-complete> 组件，生成其相关属性的数据声明、事件函数等", "**/"], "description": "<a-auto-complete> 提示指令"}, "a-alert": {"prefix": "fs-a-alert", "body": ["/**", " * 根据 <template> 模板中 <a-alert> 组件，生成其相关属性的数据声明、事件函数等", "**/"], "description": "<a-alert> 提示指令"}, "a-avatar": {"prefix": "fs-a-avatar", "body": ["/**", " * 根据 <template> 模板中 <a-avatar> 组件，生成其相关属性的数据声明、事件函数等", "**/"], "description": "<a-avatar> 提示指令"}, "a-back-top": {"prefix": "fs-a-back-top", "body": ["/**", " * 根据 <template> 模板中 <a-back-top> 组件，生成其相关属性的数据声明、事件函数等", "**/"], "description": "<a-back-top> 提示指令"}, "a-badge": {"prefix": "fs-a-badge", "body": ["/**", " * 根据 <template> 模板中 <a-badge> 组件，生成其相关属性的数据声明、事件函数等", "**/"], "description": "<a-badge> 提示指令"}, "a-breadcrumb": {"prefix": "fs-a-breadcrumb", "body": ["/**", " * 根据 <template> 模板中 <a-breadcrumb> 组件，生成其相关属性的数据声明、事件函数等", "**/"], "description": "<a-breadcrumb> 提示指令"}, "a-button": {"prefix": "fs-a-button", "body": ["/**", " * 根据 <template> 模板中 <a-button> 组件，生成其相关属性的数据声明、事件函数等", "**/"], "description": "<a-button> 提示指令"}, "a-calendar": {"prefix": "fs-a-calendar", "body": ["/**", " * 根据 <template> 模板中 <a-calendar> 组件，生成其相关属性的数据声明、事件函数等", "**/"], "description": "<a-calendar> 提示指令"}, "a-card": {"prefix": "fs-a-card", "body": ["/**", " * 根据 <template> 模板中 <a-card> 组件，生成其相关属性的数据声明、事件函数等", "**/"], "description": "<a-card> 提示指令"}, "a-carousel": {"prefix": "fs-a-carousel", "body": ["/**", " * 根据 <template> 模板中 <a-carousel> 组件，生成其相关属性的数据声明、事件函数等", "**/"], "description": "<a-carousel> 提示指令"}, "a-cascader": {"prefix": "fs-a-cascader", "body": ["/**", " * 根据 <template> 模板中 <a-cascader> 组件，生成其相关属性的数据声明、事件函数等", "**/"], "description": "<a-cascader> 提示指令"}, "a-checkbox": {"prefix": "fs-a-checkbox", "body": ["/**", " * 根据 <template> 模板中 <a-checkbox> 组件，生成其相关属性的数据声明、事件函数等", "**/"], "description": "<a-checkbox> 提示指令"}, "a-col": {"prefix": "fs-a-col", "body": ["/**", " * 根据 <template> 模板中 <a-col> 组件，生成其相关属性的数据声明、事件函数等", "**/"], "description": "<a-col> 提示指令"}, "a-collapse": {"prefix": "fs-a-collapse", "body": ["/**", " * 根据 <template> 模板中 <a-collapse> 组件，生成其相关属性的数据声明、事件函数等", "**/"], "description": "<a-collapse> 提示指令"}, "a-comment": {"prefix": "fs-a-comment", "body": ["/**", " * 根据 <template> 模板中 <a-comment> 组件，生成其相关属性的数据声明、事件函数等", "**/"], "description": "<a-comment> 提示指令"}, "a-config-provider": {"prefix": "fs-a-config-provider", "body": ["/**", " * 根据 <template> 模板中 <a-config-provider> 组件，生成其相关属性的数据声明、事件函数等", "**/"], "description": "<a-config-provider> 提示指令"}, "a-date-picker": {"prefix": "fs-a-date-picker", "body": ["/**", " * 根据 <template> 模板中 <a-date-picker> 组件，生成其相关属性的数据声明、事件函数等", "**/"], "description": "<a-date-picker> 提示指令"}, "a-descriptions": {"prefix": "fs-a-descriptions", "body": ["/**", " * 根据 <template> 模板中 <a-descriptions> 组件，生成其相关属性的数据声明、事件函数等", "**/"], "description": "<a-descriptions> 提示指令"}, "a-divider": {"prefix": "fs-a-divider", "body": ["/**", " * 根据 <template> 模板中 <a-divider> 组件，生成其相关属性的数据声明、事件函数等", "**/"], "description": "<a-divider> 提示指令"}, "a-drawer": {"prefix": "fs-a-drawer", "body": ["/**", " * 根据 <template> 模板中 <a-drawer> 组件，生成其相关属性的数据声明、事件函数等", "**/"], "description": "<a-drawer> 提示指令"}, "a-dropdown": {"prefix": "fs-a-dropdown", "body": ["/**", " * 根据 <template> 模板中 <a-dropdown> 组件，生成其相关属性的数据声明、事件函数等", "**/"], "description": "<a-dropdown> 提示指令"}, "a-empty": {"prefix": "fs-a-empty", "body": ["/**", " * 根据 <template> 模板中 <a-empty> 组件，生成其相关属性的数据声明、事件函数等", "**/"], "description": "<a-empty> 提示指令"}, "a-form": {"prefix": "fs-a-form", "body": ["/**", " * 根据 <template> 模板中 <a-form> 组件，生成其相关属性的数据声明、事件函数等", "**/"], "description": "<a-form> 提示指令"}, "a-grid": {"prefix": "fs-a-grid", "body": ["/**", " * 根据 <template> 模板中 <a-grid> 组件，生成其相关属性的数据声明、事件函数等", "**/"], "description": "<a-grid> 提示指令"}, "a-icon": {"prefix": "fs-a-icon", "body": ["/**", " * 根据 <template> 模板中 <a-icon> 组件，生成其相关属性的数据声明、事件函数等", "**/"], "description": "<a-icon> 提示指令"}, "a-image": {"prefix": "fs-a-image", "body": ["/**", " * 根据 <template> 模板中 <a-image> 组件，生成其相关属性的数据声明、事件函数等", "**/"], "description": "<a-image> 提示指令"}, "a-input": {"prefix": "fs-a-input", "body": ["/**", " * 根据 <template> 模板中 <a-input> 组件，生成其相关属性的数据声明、事件函数等", "**/"], "description": "<a-input> 提示指令"}, "a-input-number": {"prefix": "fs-a-input-number", "body": ["/**", " * 根据 <template> 模板中 <a-input-number> 组件，生成其相关属性的数据声明、事件函数等", "**/"], "description": "<a-input-number> 提示指令"}, "a-layout": {"prefix": "fs-a-layout", "body": ["/**", " * 根据 <template> 模板中 <a-layout> 组件，生成其相关属性的数据声明、事件函数等", "**/"], "description": "<a-layout> 提示指令"}, "a-list": {"prefix": "fs-a-list", "body": ["/**", " * 根据 <template> 模板中 <a-list> 组件，生成其相关属性的数据声明、事件函数等", "**/"], "description": "<a-list> 提示指令"}, "a-locale-provider": {"prefix": "fs-a-locale-provider", "body": ["/**", " * 根据 <template> 模板中 <a-locale-provider> 组件，生成其相关属性的数据声明、事件函数等", "**/"], "description": "<a-locale-provider> 提示指令"}, "a-menu": {"prefix": "fs-a-menu", "body": ["/**", " * 根据 <template> 模板中 <a-menu> 组件，生成其相关属性的数据声明、事件函数等", "**/"], "description": "<a-menu> 提示指令"}, "a-mentions": {"prefix": "fs-a-mentions", "body": ["/**", " * 根据 <template> 模板中 <a-mentions> 组件，生成其相关属性的数据声明、事件函数等", "**/"], "description": "<a-mentions> 提示指令"}, "a-modal": {"prefix": "fs-a-modal", "body": ["/**", " * 根据 <template> 模板中 <a-modal> 组件，生成其相关属性的数据声明、事件函数等", "**/"], "description": "<a-modal> 提示指令"}, "a-page-header": {"prefix": "fs-a-page-header", "body": ["/**", " * 根据 <template> 模板中 <a-page-header> 组件，生成其相关属性的数据声明、事件函数等", "**/"], "description": "<a-page-header> 提示指令"}, "a-pagination": {"prefix": "fs-a-pagination", "body": ["/**", " * 根据 <template> 模板中 <a-pagination> 组件，生成其相关属性的数据声明、事件函数等", "**/"], "description": "<a-pagination> 提示指令"}, "a-popconfirm": {"prefix": "fs-a-popconfirm", "body": ["/**", " * 根据 <template> 模板中 <a-popconfirm> 组件，生成其相关属性的数据声明、事件函数等", "**/"], "description": "<a-popconfirm> 提示指令"}, "a-popover": {"prefix": "fs-a-popover", "body": ["/**", " * 根据 <template> 模板中 <a-popover> 组件，生成其相关属性的数据声明、事件函数等", "**/"], "description": "<a-popover> 提示指令"}, "a-progress": {"prefix": "fs-a-progress", "body": ["/**", " * 根据 <template> 模板中 <a-progress> 组件，生成其相关属性的数据声明、事件函数等", "**/"], "description": "<a-progress> 提示指令"}, "a-radio": {"prefix": "fs-a-radio", "body": ["/**", " * 根据 <template> 模板中 <a-radio> 组件，生成其相关属性的数据声明、事件函数等", "**/"], "description": "<a-radio> 提示指令"}, "a-rate": {"prefix": "fs-a-rate", "body": ["/**", " * 根据 <template> 模板中 <a-rate> 组件，生成其相关属性的数据声明、事件函数等", "**/"], "description": "<a-rate> 提示指令"}, "a-result": {"prefix": "fs-a-result", "body": ["/**", " * 根据 <template> 模板中 <a-result> 组件，生成其相关属性的数据声明、事件函数等", "**/"], "description": "<a-result> 提示指令"}, "a-row": {"prefix": "fs-a-row", "body": ["/**", " * 根据 <template> 模板中 <a-row> 组件，生成其相关属性的数据声明、事件函数等", "**/"], "description": "<a-row> 提示指令"}, "a-select": {"prefix": "fs-a-select", "body": ["/**", " * 根据 <template> 模板中 <a-select> 组件，生成其相关属性的数据声明、事件函数等", "**/"], "description": "<a-select> 提示指令"}, "a-skeleton": {"prefix": "fs-a-skeleton", "body": ["/**", " * 根据 <template> 模板中 <a-skeleton> 组件，生成其相关属性的数据声明、事件函数等", "**/"], "description": "<a-skeleton> 提示指令"}, "a-slider": {"prefix": "fs-a-slider", "body": ["/**", " * 根据 <template> 模板中 <a-slider> 组件，生成其相关属性的数据声明、事件函数等", "**/"], "description": "<a-slider> 提示指令"}, "a-space": {"prefix": "fs-a-space", "body": ["/**", " * 根据 <template> 模板中 <a-space> 组件，生成其相关属性的数据声明、事件函数等", "**/"], "description": "<a-space> 提示指令"}, "a-spin": {"prefix": "fs-a-spin", "body": ["/**", " * 根据 <template> 模板中 <a-spin> 组件，生成其相关属性的数据声明、事件函数等", "**/"], "description": "<a-spin> 提示指令"}, "a-statistic": {"prefix": "fs-a-statistic", "body": ["/**", " * 根据 <template> 模板中 <a-statistic> 组件，生成其相关属性的数据声明、事件函数等", "**/"], "description": "<a-statistic> 提示指令"}, "a-steps": {"prefix": "fs-a-steps", "body": ["/**", " * 根据 <template> 模板中 <a-steps> 组件，生成其相关属性的数据声明、事件函数等", "**/"], "description": "<a-steps> 提示指令"}, "a-switch": {"prefix": "fs-a-switch", "body": ["/**", " * 根据 <template> 模板中 <a-switch> 组件，生成其相关属性的数据声明、事件函数等", "**/"], "description": "<a-switch> 提示指令"}, "a-table": {"prefix": "fs-a-table", "body": ["/**", " * 根据 <template> 模板中 <a-table> 组件，生成其相关属性的数据声明、事件函数等", "**/"], "description": "<a-table> 提示指令"}, "a-tabs": {"prefix": "fs-a-tabs", "body": ["/**", " * 根据 <template> 模板中 <a-tabs> 组件，生成其相关属性的数据声明、事件函数等", "**/"], "description": "<a-tabs> 提示指令"}, "a-tag": {"prefix": "fs-a-tag", "body": ["/**", " * 根据 <template> 模板中 <a-tag> 组件，生成其相关属性的数据声明、事件函数等", "**/"], "description": "<a-tag> 提示指令"}, "a-time-picker": {"prefix": "fs-a-time-picker", "body": ["/**", " * 根据 <template> 模板中 <a-time-picker> 组件，生成其相关属性的数据声明、事件函数等", "**/"], "description": "<a-time-picker> 提示指令"}, "a-timeline": {"prefix": "fs-a-timeline", "body": ["/**", " * 根据 <template> 模板中 <a-timeline> 组件，生成其相关属性的数据声明、事件函数等", "**/"], "description": "<a-timeline> 提示指令"}, "a-tooltip": {"prefix": "fs-a-tooltip", "body": ["/**", " * 根据 <template> 模板中 <a-tooltip> 组件，生成其相关属性的数据声明、事件函数等", "**/"], "description": "<a-tooltip> 提示指令"}, "a-transfer": {"prefix": "fs-a-transfer", "body": ["/**", " * 根据 <template> 模板中 <a-transfer> 组件，生成其相关属性的数据声明、事件函数等", "**/"], "description": "<a-transfer> 提示指令"}, "a-tree": {"prefix": "fs-a-tree", "body": ["/**", " * 根据 <template> 模板中 <a-tree> 组件，生成其相关属性的数据声明、事件函数等", "**/"], "description": "<a-tree> 提示指令"}, "a-tree-select": {"prefix": "fs-a-tree-select", "body": ["/**", " * 根据 <template> 模板中 <a-tree-select> 组件，生成其相关属性的数据声明、事件函数等", "**/"], "description": "<a-tree-select> 提示指令"}, "a-typography": {"prefix": "fs-a-typography", "body": ["/**", " * 根据 <template> 模板中 <a-typography> 组件，生成其相关属性的数据声明、事件函数等", "**/"], "description": "<a-typography> 提示指令"}, "a-upload": {"prefix": "fs-a-upload", "body": ["/**", " * 根据 <template> 模板中 <a-upload> 组件，生成其相关属性的数据声明、事件函数等", "**/"], "description": "<a-upload> 提示指令"}, "a-watermark": {"prefix": "fs-a-watermark", "body": ["/**", " * 根据 <template> 模板中 <a-watermark> 组件，生成其相关属性的数据声明、事件函数等", "**/"], "description": "<a-watermark> 提示指令"}}