<template>
  <div class="body-cell">
    <!-- 邮件 -->
    <a v-if="column.type === ColumnType.EMAIL" class="link"> {{ (data as any)[column.field] }} </a>
  </div>
</template>

<script setup lang="ts">
import type { CardData, IColumn } from '@/api/services/indicator/type'
import { ColumnType } from '@/api/services/indicator/type'
// 划分类型：文本、邮件、数字、日期、布尔

const props = defineProps<{ data: CardData; column: IColumn }>()
onMounted(() => {})
</script>

<style scoped></style>
