<script setup lang="ts">
import type { TableProps } from 'ant-design-vue'
import { SearchOutlined } from '@ant-design/icons-vue'
import { Table } from 'ant-design-vue'
import { useRequest } from 'vue-hooks-plus'
import api from '@/api/sql-edit-sys'
import { activeDbKey } from '../use-sql-editing'
import type { SqlDataNode } from '@/components/sql-tree-comp/types'
import { NodeType } from '@/api/models/sql-tree'
import FullSizeBox from '@/components/full-size-box/index.vue'
import { getDbIcon } from '@/utils/utils'
import { logPlus } from '@/utils/log-plus'
import type { DbTableInfo } from '@/api/models/sql-editing'

defineOptions({
  name: 'DBTablesView',
})

const tabKeys = {
  table: 'table',
  er: 'er',
}
const isShowEmpty = computed(() => {
  return !(activeDb?.value?.dbName || activeDb?.value?.dbSchema)
})
const activeKey = ref(tabKeys.table)
const searchText = ref('')
const columns = ref<TableProps['columns']>([
  {
    title: '表名',
    dataIndex: 'tableName',
  },
  {
    title: '数据长度',
    dataIndex: 'dataSize',
  },
  {
    title: '表类型',
    dataIndex: 'tableType',
  },
  {
    title: '行格式',
    dataIndex: 'rowFormat',
  },
  {
    title: '注释',
    dataIndex: 'comment',
    width: 200,
  },
])
const activeDb = inject<Ref<SqlDataNode | undefined>>(activeDbKey)
const {
  data: tableData,
  run: getTableData,
  loading: loadingTable,
} = useRequest(api.getDbTablesInfo, {
  manual: true,
})
const filteredTableData = computed(() => {
  if (!searchText.value || !tableData.value?.length) {
    return tableData.value || []
  }
  return tableData.value.filter((item) =>
    item.tableName.toLowerCase().includes(searchText.value.toLowerCase()),
  )
})

function go2TableDetail(record: DbTableInfo) {
  logPlus.red('跳转到表详情', record)
}

watch(
  () => activeDb?.value,
  (db) => {
    if (
      db?.dbConnection?.id &&
      db?.dbConnection?.dbType &&
      (db?.nodeType === NodeType.database.value || db?.nodeType === NodeType.schema.value)
    ) {
      getTableData({
        id: db?.dbConnection?.id,
        dbSchema: db?.dbSchema,
        dbType: db?.dbConnection?.dbType,
        dbName: db?.dbName,
        type: db?.nodeType,
      })
    }
  },
  {
    immediate: true,
    deep: true,
  },
)
</script>

<template>
  <div class="attribute-container size-full pb-4" flex="~ col gap-4">
    <div class="attribute-content flex-auto overflow-hidden">
      <a-empty v-if="isShowEmpty" description="请先选择数据库"></a-empty>
      <template v-else>
        <FullSizeBox
          v-if="activeKey === tabKeys.table"
          class="attribute-table-container overflow-auto"
        >
          <template #default="{ size }">
            <Table
              :columns="columns"
              :data-source="filteredTableData"
              :loading="loadingTable"
              :scroll="{ x: 1100, y: size.height - 200 }"
            >
              <template #title>
                <div flex="~ gap-4">
                  <a-input
                    v-model:value="searchText"
                    placeholder="请输入表名"
                    style="width: 300px"
                    allow-clear
                  >
                    <template #prefix>
                      <SearchOutlined />
                    </template>
                  </a-input>
                  <a-tag :closable="false" flex="~ items-center">
                    Tables: &nbsp;{{ filteredTableData?.length ?? 0 }}
                  </a-tag>
                </div>
              </template>
              <template #bodyCell="{ column, record }">
                <template v-if="column.dataIndex === 'tableName'">
                  <a @click="go2TableDetail(record as DbTableInfo)">{{ record.tableName }}</a>
                </template>
              </template>
            </Table>
          </template>
        </FullSizeBox>
        <div v-if="activeKey === tabKeys.er" class="attribute-er">
          <div class="attribute-er-header">
            <div class="attribute-er-header-title">ER 图</div>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<style scoped lang="less">
:deep(.ant-btn) {
  padding-left: 0;
  padding-right: 0;
}
</style>
