<template>
  <a-modal
    v-model:open="modalValue"
    title="选择分组"
    :footer="null"
    width="800px"
    :body-style="{ minHeight: '360px', overflowY: 'auto' }"
  >
    <a-spin :spinning="loading">
      <div class="search-bar">
        <a-input
          placeholder="搜索分组"
          v-model:value="searchText"
          @change="onSearch"
          style="margin-bottom: 16px"
        >
          <template #prefix>
            <search-outlined />
          </template>
        </a-input>
      </div>
      <a-tree
        v-model:expandedKeys="expandedKeys"
        v-model:selectedKeys="selectedKeys"
        :tree-data="treeData"
        :replaceFields="{ title: 'title', key: 'key', children: 'children' }"
        @select="onSelectGroup"
        :width="400"
        :height="350"
      >
        <template #title="{ title, key }">
          <span class="ellipsis" @click.stop="handleCheck(key)">{{ title }}</span>
        </template>
      </a-tree>
    </a-spin>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { SearchOutlined } from '@ant-design/icons-vue'
import { getIndexGroupTreeList } from '@/api/indexmanage/indexmanage/indexmanage'

const props = defineProps({
  handleClickGroup: {
    type: Function,
    default: () => {},
  },
})

const treeData = ref<any[]>([])
const expandedKeys = ref<string[]>([])
const selectedKeys = ref<string[]>([])
const modalValue = ref(false)
const loading = ref(false)
const searchText = ref('')

// 转换数据为树形结构
const transformData = (data: any[]) => {
  return data.map((item) => {
    const newItem = {
      ...item,
      title: item.groupName,
      key: item.groupId,
    }
    if (item.children) {
      newItem.children = transformData(item.children)
    }
    return newItem
  })
}

// 获取分组树数据
const getTreeData = async () => {
  try {
    loading.value = true
    const res = await getIndexGroupTreeList()
    treeData.value = transformData(res.data)

    // 展开所有节点
    const allExpandedKeys: string[] = []
    const collectExpandedKeys = (nodes: any[]) => {
      nodes.forEach((node) => {
        if (node.children?.length) {
          allExpandedKeys.push(node.key)
          collectExpandedKeys(node.children)
        }
      })
    }
    collectExpandedKeys(treeData.value)
    expandedKeys.value = allExpandedKeys
  } catch (error) {
    console.error('获取分组树失败', error)
  } finally {
    loading.value = false
  }
}

function show() {
  modalValue.value = true
  if (treeData.value.length === 0) {
    getTreeData()
  }
}

function onSelectGroup(selectedKeys: string[], info: any) {
  if (selectedKeys.length > 0) {
    // 可以在这里处理选择逻辑，也可以使用下面的handleCheck方法
  }
}

function handleCheck(key: string) {
  // 查找完整的节点数据
  const findNodeData = (nodes: any[], targetKey: string): any => {
    for (const node of nodes) {
      if (node.key === targetKey) {
        return node
      }
      if (node.children?.length) {
        const found = findNodeData(node.children, targetKey)
        if (found) return found
      }
    }
    return null
  }

  const nodeData = findNodeData(treeData.value, key)
  if (nodeData) {
    props.handleClickGroup(nodeData)
    modalValue.value = false
  }
}

function onSearch() {
  // 可以实现搜索功能，根据searchText过滤树节点
  // 这里是简单示例，实际可能需要更复杂的逻辑
  if (!searchText.value) {
    getTreeData() // 重置为完整树
    return
  }

  // 示例：根据节点标题过滤
  const filterTreeData = (nodes: any[], keyword: string) => {
    return nodes.filter((node) => {
      const isMatch = node.title.toLowerCase().includes(keyword.toLowerCase())
      const childrenMatch = node.children ? filterTreeData(node.children, keyword) : []

      if (childrenMatch.length) {
        node.children = childrenMatch
        return true
      }

      return isMatch
    })
  }

  // 过滤树数据
  const originalData = JSON.parse(JSON.stringify(treeData.value))
  treeData.value = filterTreeData(originalData, searchText.value)
}

onMounted(() => {
  // 组件挂载时可以预加载数据，也可以等到show时再加载
})

defineExpose({
  show,
})
</script>

<style scoped>
.modal-content {
  min-height: 400px;
}
</style>
