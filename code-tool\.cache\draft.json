{"layout-login": {"activeLayout": 0, "activeLogin": 0}, "router-list-data": [{"menuName": "指标配置模版", "menuUrl": "indicators-template", "menuId": "67bbce109fabe8551b3ad4ce", "pageType": "normal", "isCover": false}, {"menuName": "指标列表", "menuUrl": "indicator-list", "menuId": "67bbce4e9fabe8551b3ad4d1", "pageType": "normal"}, {"menuName": "新增编辑指标", "menuUrl": "indicator-config", "menuId": "67bc123b9fabe8551b3ad9f7", "pageType": "normal"}, {"menuName": "新增指标模版", "menuUrl": "indicators-template/create", "menuId": "67c0066c9fabe8551b3adb36", "pageType": "secondary-page", "isCover": true}, {"menuName": "分组管理", "menuUrl": "packet-management", "menuId": "67c148169fabe8551b3adb58", "pageType": "normal"}, {"menuName": "创建指标", "menuUrl": "indicator-list/create", "menuId": "67c011f79fabe8551b3adb49", "pageType": "normal"}, {"menuName": "规则引擎", "menuUrl": "rule-engine", "menuId": "6809957b9fabe8551b3adee3", "pageType": "normal"}, {"menuName": "规则管理", "menuUrl": "rule-engine/rule-manage", "menuId": "680ad8569fabe8551b3adef7", "pageType": "secondary-page", "isCover": true}, {"menuName": "函数管理", "menuUrl": "rule-engine/function-manage", "menuId": "680ad8919fabe8551b3adefa", "pageType": "secondary-page", "isCover": true}], "/indicators-template": {"request": [{"name": "查询", "api": "getIndexFormDataListByPage"}, {"name": "新增", "api": "addIndexTemplate"}, {"name": "编辑", "api": "updateIndexTemplate"}, {"name": "删除", "api": "delIndexTemplate"}], "search": [{"name": "formName", "type": "input", "description": "指标配置模板名称", "required": false}, {"name": "isDeleted", "type": "input", "description": "是否启用:1是0否", "required": false}, {"name": "status", "type": "input", "description": "状态:0编辑中1已提交", "required": false}, {"name": "startTime", "type": "input", "description": "开始时间", "required": false}, {"name": "endTime", "type": "input", "description": "结束时间", "required": false}, {"name": "isRelease", "type": "input", "description": "是否已发布:1是0否", "required": false}], "form": [{"name": "formCode", "type": "input", "description": "指标配置模板编号", "required": true, "span": 24}, {"name": "formName", "type": "input", "description": "指标配置模板名称", "required": true, "span": 24}, {"name": "remarks", "type": "input", "description": " 备注", "required": true, "span": 24}, {"name": "fieldsData", "type": "input", "description": "字段数据", "required": false, "span": 24}, {"name": "compData", "type": "input", "description": "组件数据", "required": false, "span": 24}], "interfaceInfos": [], "dataSource": [{"name": "records", "isArray": true, "description": "业务数据记录内容", "type": [{"name": "formId", "isArray": false, "description": "主键", "type": "string"}, {"name": "formCode", "isArray": false, "description": "指标配置模板编号", "type": "string"}, {"name": "formName", "isArray": false, "description": "指标配置模板名称", "type": "string"}, {"name": "isDeleted", "isArray": false, "description": "是否启用:1是0否", "type": "number"}, {"name": "status", "isArray": false, "type": "number"}, {"name": "revision", "isArray": false, "description": "乐观锁", "type": "number"}, {"name": "remarks", "isArray": false, "description": "备注", "type": "string"}, {"name": "tenantId", "isArray": false, "description": "租户号", "type": "string"}, {"name": "createUser", "isArray": false, "description": "创建人", "type": "string"}, {"name": "createUsername", "isArray": false, "description": "创建人名称", "type": "string"}, {"name": "createTime", "isArray": false, "description": "创建时间", "type": "string"}, {"name": "updateUser", "isArray": false, "description": "更新人", "type": "string"}, {"name": "updateUsername", "isArray": false, "description": "更新人名称", "type": "string"}, {"name": "updateTime", "isArray": false, "description": "更新时间", "type": "string"}, {"name": "isRelease", "isArray": false, "description": "是否已发布(1是0否)", "type": "number"}, {"name": "currentVersion", "isArray": false, "description": "当前版本号", "type": "number"}]}], "project": "indexManage", "module": "indexForm", "pageType": "table"}, "/indicator-list": {"request": [{"name": "查询", "api": "getIndexGroupListByPage"}, {"name": "新增", "api": "addNewIndexData"}, {"name": "编辑", "api": "modifyIndexData"}, {"name": "删除"}], "search": [{"name": "groupName", "type": "input", "description": "分组名称", "required": false}, {"name": "status", "type": "input", "description": "状态:1有效0无效", "required": false}], "form": [{"name": "formHistoryId", "type": "input", "description": "指标配置模板版本编号", "required": true, "span": 24}, {"name": "indexName", "type": "input", "description": "指标名称", "required": true, "span": 24}, {"name": "groupId", "type": "input", "description": "分组编号", "required": false, "span": 24}, {"name": "parentId", "type": "input", "description": "指标父id", "required": false, "span": 24}, {"name": "calFormula", "type": "input", "description": "计算公式/规则逻辑", "required": false, "span": 24}, {"name": "dataSourceId", "type": "input", "description": "数据源编号", "required": false, "span": 24}, {"name": "formData", "type": "input", "description": "指标配置数据", "required": false, "span": 24}, {"name": "basicRuleList", "type": "input", "description": "指标计算规则列表", "required": false, "children": [{"name": "ruleType", "type": "number", "description": "规则类型:1SQL脚本2shell脚本3python脚本", "required": true}, {"name": "ruleText", "type": "string", "description": "参数配置", "required": true}, {"name": "ruleScript", "type": "string", "description": "计算规则脚本", "required": true}], "isList": true, "span": 24}, {"name": "remarks", "type": "input", "description": "描述", "required": false, "span": 24}], "interfaceInfos": [], "dataSource": [{"name": "records", "isArray": true, "description": "业务数据记录内容", "type": [{"name": "groupId", "isArray": false, "description": "分组主键", "type": "string"}, {"name": "groupName", "isArray": false, "description": "组名称", "type": "string"}, {"name": "status", "isArray": false, "description": "状态:1有效0无效", "type": "number"}, {"name": "remarks", "isArray": false, "description": "备注", "type": "string"}, {"name": "tenantId", "isArray": false, "description": "租户号", "type": "string"}, {"name": "createUser", "isArray": false, "description": "创建人id", "type": "string"}, {"name": "createUsername", "isArray": false, "description": "创建人名称", "type": "string"}, {"name": "createTime", "isArray": false, "description": "创建时间", "type": "string"}, {"name": "updateUser", "isArray": false, "description": "更新人", "type": "string"}, {"name": "updateUsername", "isArray": false, "description": "更新人名称", "type": "string"}, {"name": "updateTime", "isArray": false, "description": "更新时间", "type": "string"}]}], "project": "indexManage", "module": "indexManage", "pageType": "table"}, "/indicator-config": {"compnentName": "a-flex", "comProps": {"vertical": true, "gap": 0}, "children": [{"compnentName": "a-flex", "comProps": {"vertical": false, "gap": 0}, "children": [{"compnentName": "div", "comProps": {"title": "内容区", "style": {"margin": 0, "padding": 0, "width": "100%", "height": "300px"}}}]}], "pageType": "grid"}, "/port-manage": {"request": [{"name": "查询", "api": "getIndexFormDataListByPage"}, {"name": "新增", "api": "addIndexTemplate"}, {"name": "编辑", "api": "updateIndexTemplate"}, {"name": "删除", "api": "delIndexTemplate"}], "search": [{"name": "formName", "type": "input", "description": "指标配置模板名称", "required": false, "key": 7953630536567575}, {"name": "isDeleted", "type": "input", "description": "是否启用:1是0否", "required": false, "key": 2517373742562059}, {"name": "status", "type": "input", "description": "状态:0编辑中1已提交", "required": false, "key": 178300872082895}, {"name": "startTime", "type": "input", "description": "开始时间", "required": false, "key": 5933934241024904}, {"name": "endTime", "type": "input", "description": "结束时间", "required": false, "key": 2338191938986095}, {"name": "isRelease", "type": "input", "description": "是否已发布:1是0否", "required": false, "key": 5181421138479200}, {"name": "businessType", "type": "input", "description": "业务类型1=301", "required": false, "key": 6455755034279766}], "form": [{"name": "formCode", "type": "input", "description": "指标配置模板编号", "required": true, "span": 24}, {"name": "formName", "type": "input", "description": "指标配置模板名称", "required": true, "span": 24}, {"name": "remarks", "type": "input", "description": " 备注", "required": true, "span": 24}, {"name": "fieldsData", "type": "input", "description": "字段数据", "required": false, "span": 24}, {"name": "compData", "type": "input", "description": "组件数据", "required": false, "span": 24}, {"name": "businessType", "type": "input", "description": "业务类型1=301", "required": true, "span": 24}], "interfaceInfos": [], "dataSource": [{"name": "records", "isArray": true, "description": "业务数据记录内容", "type": [{"name": "formId", "isArray": false, "description": "主键", "type": "string"}, {"name": "formCode", "isArray": false, "description": "指标配置模板编号", "type": "string"}, {"name": "formName", "isArray": false, "description": "指标配置模板名称", "type": "string"}, {"name": "isDeleted", "isArray": false, "description": "是否启用:1是0否", "type": "number"}, {"name": "status", "isArray": false, "type": "number"}, {"name": "revision", "isArray": false, "description": "乐观锁", "type": "number"}, {"name": "remarks", "isArray": false, "description": "备注", "type": "string"}, {"name": "tenantId", "isArray": false, "description": "租户号", "type": "string"}, {"name": "createUser", "isArray": false, "description": "创建人", "type": "string"}, {"name": "createUsername", "isArray": false, "description": "创建人名称", "type": "string"}, {"name": "createTime", "isArray": false, "description": "创建时间", "type": "string"}, {"name": "updateUser", "isArray": false, "description": "更新人", "type": "string"}, {"name": "updateUsername", "isArray": false, "description": "更新人名称", "type": "string"}, {"name": "updateTime", "isArray": false, "description": "更新时间", "type": "string"}, {"name": "isRelease", "isArray": false, "description": "是否已发布(1是0否)", "type": "number"}, {"name": "currentVersion", "isArray": false, "description": "当前版本号", "type": "number"}]}], "project": "indexManage", "module": "indexForm", "pageType": "table"}, "/packet-management": {"request": [{"name": "查询", "api": "getIndexGroupListByPage"}, {"name": "新增", "api": "addNewIndexGroup"}, {"name": "编辑", "api": "modifyIndexGroupData"}, {"name": "删除"}], "search": [{"name": "groupName", "type": "input", "description": "分组名称", "required": false}, {"name": "status", "type": "input", "description": "状态:1有效0无效", "required": false}], "form": [{"name": "groupName", "type": "input", "description": "分组名称", "required": true, "span": 24}, {"name": "remarks", "type": "input", "description": "备注", "required": false, "span": 24}], "interfaceInfos": [], "dataSource": [{"name": "records", "isArray": true, "description": "业务数据记录内容", "type": [{"name": "groupId", "isArray": false, "description": "分组主键", "type": "string"}, {"name": "groupName", "isArray": false, "description": "组名称", "type": "string"}, {"name": "status", "isArray": false, "description": "状态:1有效0无效", "type": "number"}, {"name": "remarks", "isArray": false, "description": "备注", "type": "string"}, {"name": "tenantId", "isArray": false, "description": "租户号", "type": "string"}, {"name": "createUser", "isArray": false, "description": "创建人id", "type": "string"}, {"name": "createUsername", "isArray": false, "description": "创建人名称", "type": "string"}, {"name": "createTime", "isArray": false, "description": "创建时间", "type": "string"}, {"name": "updateUser", "isArray": false, "description": "更新人", "type": "string"}, {"name": "updateUsername", "isArray": false, "description": "更新人名称", "type": "string"}, {"name": "updateTime", "isArray": false, "description": "更新时间", "type": "string"}]}], "project": "indexManage", "module": "indexManage", "pageType": "table"}}