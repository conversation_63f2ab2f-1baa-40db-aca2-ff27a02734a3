<script lang="ts" setup>
import * as echarts from 'echarts'
import type { ECBasicOption } from 'echarts/types/dist/shared'
import Dragger from './dragger/index.vue'
import { ref, reactive, watch, onMounted, onUnmounted } from 'vue'

let myCharts: any = null

const props = defineProps<{ schemaData: any }>()
const currentEchartsData = ref()
const chartRef = ref(null)
const contentRef = ref<HTMLElement | null>(null)
const draggerRef = ref<{ showDrawer: () => void } | null>(null)

const option = reactive<ECBasicOption>({
  animationDurationUpdate: 1000, //数据更新动画的时长。
  animationEasingUpdate: 'quinticInOut', //数据更新动画的缓动效果。
  stateAnimation: {
    duration: 300,
    easing: 'cubicOut',
  },
  series: [
    {
      type: 'graph',
      layout: 'force',
      draggable: true,
      symbolSize: 20,
      autoMultLines: true,
      autoSelf: true,
      roam: true,
      label: {
        show: true,
        formatter: function (param: any) {
          return param?.data?.nameZh
        },
      },
      edgeSymbol: ['none', 'arrow'],
      edgeSymbolSize: [10, 10],
      edgeLabel: {
        show: true,
        fontSize: 14,
        position: 'middle',
        formatter: function (param: any) {
          return param?.data?.name
        },
      },
      data: props.schemaData.demoDataListData || [],
      links: props.schemaData.initLinkListData || [],
      emphasis: {
        focus: 'adjacency',
        lineStyle: {
          color: '#FF6B6B', // 自环颜色
          width: 3,
          curveness: 0.3,
        },
      },
      force: {
        repulsion: 100,
        edgeLength: 120,
        friction: 0.6,
        layoutAnimation: true,
      },
      zoom: 2,
      scaleLimit: {
        min: 1,
        max: 5,
      },
      left: 10,
      right: 10,
      top: 10,
      bottom: 10,
    },
  ],
})
const showDrawerHandler = (params: any) => {
  console.log('数据', params)
  currentEchartsData.value = params.data
  draggerRef.value?.showDrawer?.()
}

const initRelationshipChart = () => {
  myCharts = echarts.init(chartRef.value)
  myCharts.setOption(option)
  myCharts.on('click', showDrawerHandler)
}

watch(
  () => props.schemaData,
  (newVal) => {
    console.log(
      '%c [ newVal ]-88',
      'font-size:13px; background:#c3f5f2; color:#ffffff;',
      chartRef.value,
    )
    if (newVal.demoDataListData.length) {
      ;(option.series as any)[0].data = newVal.demoDataListData || []
      ;(option.series as any)[0].links = newVal.initLinkListData || []
      if (chartRef.value) {
        initRelationshipChart()
      }
    }
  },
  { immediate: true, deep: true },
)
const resizeHandler = () => {
  console.log('窗口变化啦')
  setTimeout(function () {
    myCharts.resize()
  }, 200)
}
onMounted(() => {
  // initRelationshipChart()

  //随着屏幕大小调节图表
  window.addEventListener('resize', resizeHandler)
})
onUnmounted(() => {
  // 移除监听
  window.removeEventListener('resize', resizeHandler) // 组件销毁前移除事件监听
  myCharts?.dispose()
})
</script>

<template>
  <div class="schema-wrap" ref="contentRef">
    <div class="schema-container" ref="chartRef"></div>
    <Dragger ref="draggerRef" :currentEchartsData="currentEchartsData" :chartData="schemaData" />
  </div>
</template>

<style lang="less" scoped>
.schema-wrap {
  width: 100%;
  height: 100%;
  background: #fff;
  position: relative;
  right: 0;
  border-radius: 4px;
}
.schema-container {
  box-sizing: border-box;
  overflow: hidden;
  // width: 100%;
  height: 100%;
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
}
</style>
