<template>
  <a-modal
    centered
    v-model:open="open"
    title="数据质量报告详情"
    @ok="handleOk"
    destroyOnClose
    width="800px"
  >
    <div ref="chartRef" style="width: 100%; height: 400px"></div>
    <div ref="chartRef2" style="width: 100%; height: 400px"></div>
  </a-modal>
</template>
<script lang="ts" setup>
import { ref, defineExpose, onMounted, watch } from 'vue'
import * as echarts from 'echarts'

const open = ref<boolean>(false)
const chartRef = ref<HTMLElement | null>(null)
const chartRef2 = ref<HTMLElement | null>(null)
let chartInstance: echarts.ECharts | null = null
let chartInstance2: echarts.ECharts | null = null

const showModal = () => {
  open.value = true
}

const handleOk = (e: MouseEvent) => {
  open.value = false
}

// 条形+折线图数据
const xData = ['一月', '二月', '三月', '四月', '五月', '六月']
const barData = [120, 200, 150, 80, 70, 110]
const lineData = [100, 180, 130, 60, 90, 150]

const option = {
  tooltip: {
    trigger: 'axis',
  },
  legend: {
    data: ['条形', '折线'],
  },
  xAxis: {
    type: 'category',
    data: xData,
  },
  yAxis: {
    type: 'value',
  },
  series: [
    {
      name: '条形',
      type: 'bar',
      data: barData,
      barWidth: 30,
      itemStyle: {
        color: '#4cca9f',
      },
    },
    {
      name: '折线',
      type: 'line',
      data: lineData,
      smooth: true,
      itemStyle: {
        color: '#108ee9',
      },
    },
  ],
}

// 饼图数据
const pieOption = {
  tooltip: {
    trigger: 'item',
    formatter: '{b}: {c} ({d}%)',
  },
  legend: {
    orient: 'vertical',
    left: 10,
    data: ['A类', 'B类', 'C类', 'D类'],
  },
  series: [
    {
      name: '类别分布',
      type: 'pie',
      radius: '60%',
      center: ['50%', '55%'],
      data: [
        { value: 335, name: 'A类' },
        { value: 310, name: 'B类' },
        { value: 234, name: 'C类' },
        { value: 135, name: 'D类' },
      ],
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.3)',
        },
      },
    },
  ],
}

const renderChart = () => {
  // 销毁旧实例，防止多次初始化
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }
  if (chartInstance2) {
    chartInstance2.dispose()
    chartInstance2 = null
  }
  if (chartRef.value) {
    chartInstance = echarts.init(chartRef.value)
    chartInstance.setOption(option)
  }
  if (chartRef2.value) {
    chartInstance2 = echarts.init(chartRef2.value)
    chartInstance2.setOption(pieOption)
  }
}

onMounted(() => {
  watch(open, (val) => {
    if (val) {
      setTimeout(() => {
        renderChart()
        chartInstance?.resize()
        chartInstance2?.resize()
      }, 100)
    }
  })
})

defineExpose({ showModal })
</script>
