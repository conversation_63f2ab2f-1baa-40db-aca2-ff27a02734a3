import prettier from 'prettier'

/**
 * 生成模板路由代码
 * @param {Array} list - 路由列表
 * @returns {string} - 生成的模板路由代码
 */
export async function generateTemplateRouter(list) {
  // 构建路由代码内容
  const content = `import type { RouteRecordRaw } from 'vue-router'
    /**
     * 生成路由
     */
    const routes: RouteRecordRaw[] = [
    ${list.map((item) => getTemplateRouter(item))}
    ]
    export default routes
        `

  // 使用prettier格式化代码
  return prettier.format(content, { parser: 'typescript' })
}

/**
 * 生成模板路由对象的代码
 * @param {Object} router - 路由对象
 * @param {string} menuUrl - 路由的菜单URL
 * @param {string} pageType - 页面类型 full-screen 全屏 normal 含侧边栏 secondary-page 二级页面
 * @returns {string} - 生成的模板路由对象的代码
 */
function getTemplateRouter(router) {
  return `{
    path: '/${router.menuUrl}',
    name: '${router.menuUrl}',
    component: () => import('@/views/${router.menuUrl}/index.vue'),
    meta: {
${router.pageType === 'full-screen' ? `isSecondary: true,` : ''}
${router.pageType === 'secondary-page' ? `breadcrumb: false` : ''}
    }
}
    `
}
