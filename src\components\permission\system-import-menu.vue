<template>
  <a-modal
    v-model:open="open"
    :confirm-loading="confirmLoading"
    title="导入菜单"
    @ok="handleOk"
    @cancel="cancelHadle"
  >
    <!-- <a-checkbox class="checkboxStyle" v-model:checked="checked">是否更新相关节点</a-checkbox> -->
    <a-upload-dragger
      v-model:fileList="fileList"
      name="file"
      :before-upload="beforeUpload"
      @drop="handleDrop"
    >
      <p class="ant-upload-drag-icon">
        <inbox-outlined></inbox-outlined>
      </p>
      <p class="ant-upload-text">单击或拖动文件到此区域进行上传</p>
      <p class="ant-upload-hint">请选择一个菜单文件</p>
    </a-upload-dragger>
  </a-modal>
</template>
<script lang="ts" setup>
import { ref, watch } from 'vue'
import { InboxOutlined } from '@ant-design/icons-vue'
import { message, type UploadProps } from 'ant-design-vue'
import { importMenu } from '@/api/services/permission'

const fileList = ref<UploadProps['fileList']>([])
const confirmLoading = ref<boolean>(false)
const open = ref(false)
const files = ref()

const uploading = ref<boolean>(false)

const props = defineProps({
  openImportModal: {
    type: Boolean,
    default: false, // 设置默认值为 0
  },
  systemId: String,
})

const emits = defineEmits(['cancelHandle', 'importHandleSuccess'])

watch(
  () => props.openImportModal,
  (newValue) => {
    open.value = newValue
  },
)

const beforeUpload: UploadProps['beforeUpload'] = (file) => {
  fileList.value = [...(fileList.value || []), file]
  files.value = file
  return false
}

function handleDrop(e: DragEvent) {
  console.log(e)
}

function cancelHadle() {
  emits('cancelHandle')
}

function handleOk() {
  if (fileList.value && fileList.value.length > 0) {
    confirmLoading.value = true
    const formData = new FormData()
    formData.append('file', files.value)
    formData.append('systemId', props.systemId as string)
    uploading.value = true
    importMenu(formData)
      .then(() => {
        message.success('导入成功!')
        fileList.value = []
        emits('importHandleSuccess')
      })
      .finally(() => {
        confirmLoading.value = false
      })
  } else {
    message.info('请上传菜单文件')
  }
}
</script>
<style lang="less">
.checkboxStyle {
  margin-bottom: 16px;
}
</style>
