<template>
  <!-- 分类弹框 -->
  <a-modal
    v-model:open="showDialog"
    :title="`${dialogType === 'edit' ? '编辑' : '新增'}分类`"
    @ok="okHandle"
    @cancel="canceHandle"
    width="600px"
  >
    <a-form
      :model="formData"
      name="basic"
      :label-col="{ span: 4 }"
      :wrapper-col="{ span: 16 }"
      autocomplete="off"
      ref="formRef"
    >
      <a-form-item
        label="分类名称"
        name="categoryName"
        :rules="[{ required: true, message: '请输入分类名称' }]"
      >
        <a-input
          show-count
          :maxlength="30"
          v-model:value="formData.categoryName"
          placeholder="请输入分类名称"
        />
      </a-form-item>
      <a-form-item label="描述" name="description">
        <a-textarea
          :auto-size="{ minRows: 3, maxRows: 6 }"
          v-model:value="formData.description"
          placeholder="请输入描述"
          show-count
          :maxlength="120"
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script lang="ts" setup>
import {
  addTagCategoryItem,
  editTagCategoryItem,
  type AssetResponse,
  type ITagCategory,
} from '@/api/services/data-asset/tag-management'
import { message } from 'ant-design-vue'

const props = defineProps({
  currentItem: {
    type: Object,
    required: false,
    defualt: null,
  },
  visibleDialog: {
    type: Boolean,
    default: false,
  },
  dialogType: {
    type: String,
    default: 'add', // add edit
  },
})
const emit = defineEmits(['okHandle', 'canceHandle'])

const initForm = () => {
  return {
    categoryName: '',
    description: '',
  }
}
const formRef = ref()
const showDialog = ref(false)
const formData = ref<ITagCategory>(initForm())

watch(
  () => props.visibleDialog,
  () => {
    showDialog.value = props.visibleDialog
    if (props.currentItem) {
      formData.value = {
        categoryName: props.currentItem.categoryName,
        description: props.currentItem.description,
      }
    }
  },
  { immediate: true },
)

// 分类保存
const okHandle = async () => {
  formRef.value.validate().then(async () => {
    const params: ITagCategory = formData.value
    let res: AssetResponse<string>
    if (props.dialogType === 'add') {
      res = await addTagCategoryItem(params)
    } else {
      params.categoryId = props.currentItem?.categoryId ?? -1
      res = await editTagCategoryItem(params)
    }
    if (res.code === '000000') {
      message.success('操作成功')
      canceHandle()
      emit('okHandle')
    } else {
      message.error(res.msg)
    }
  })
}

// 取消操作
const canceHandle = () => {
  showDialog.value = false
  formData.value = initForm()
  emit('canceHandle')
}
</script>
