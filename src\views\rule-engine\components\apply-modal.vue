<template>
  <Modal
    v-model:open="open"
    title="应用规则"
    height="200px"
    ok-text="复制"
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <Form :label-col="{ span: 4 }" :wrapper-col="{ span: 24 }" layout="vertical">
      <FormItem label="规则名称">
        <Input :value="props.data?.name || ''" disabled />
      </FormItem>
      <FormItem label="参数配置">
        <AttributeConfig v-model:value="args" :paramsInfo="props?.data?.paramsInfo" />
      </FormItem>
      <FormItem>
        <Button type="primary" @click="handleGenerateCURL">生成CURL</Button>
      </FormItem>
      <VAceEditor
        v-model:value="curlValue"
        lang="text"
        theme="github"
        :options="editorOptions"
        style="height: 100px; width: 100%"
      />
    </Form>
  </Modal>
</template>

<script setup lang="ts">
import { VAceEditor } from 'vue3-ace-editor'
import 'ace-builds/src-noconflict/mode-text' // 导入文本模式
import 'ace-builds/src-noconflict/theme-github' // 导入github主题
import { message, Modal, Form, FormItem, Input, Button } from 'ant-design-vue'
import AttributeConfig from './attribute-config.vue'
import type { RuleItem } from '../data'
import { ref } from 'vue'
import { useClipboard } from '@vueuse/core'

const props = defineProps<{ data?: RuleItem }>()
const open = defineModel<boolean>('open', { required: true })
const emit = defineEmits<{
  (e: 'ok'): void
  (e: 'cancel'): void
}>()
const editorOptions = {
  fontSize: 14, //设置字号
  tabSize: 2, // 标签大小
  showPrintMargin: false, //去除编辑器里的竖线
  highlightActiveLine: true,
}
const args = ref<Record<string, any>>({})
const genCurl = (method: string, url: string, data: any) => {
  // 将数据转换为字符串，确保使用双引号
  const dataString = JSON.stringify(data).replace(/"/g, '\\"') // 转义双引号为 \"

  // 在 Windows cmd 中，需要使用双引号包裹整个数据
  return `curl -X ${method} "${url}" -H "Content-Type: application/json" -d "${dataString}"`
}
const { copy, isSupported } = useClipboard()
const handleGenerateCURL = () => {
  const params = {
    projectId: props.data?.projectId,
    ruleId: props.data?.autoId,
    args: args.value,
  }
  curlValue.value = genCurl('POST', `${window.location.origin}/api/rule/apply`, params)
}
const handleOk = async () => {
  try {
    if (isSupported.value) {
      await copy(curlValue.value)
      message.success('复制成功')
      open.value = false
      emit('ok')
    } else {
      message.info('当前浏览器不支持复制功能，请手动复制')
    }
  } catch (error) {
    console.error('复制失败:', error)
    message.info('复制失败，请手动复制')
  }
}
const handleCancel = () => {
  open.value = false
  emit('cancel')
}
const curlValue = ref('')
// watch(
//   () => props.data,
//   (val) => {
//     if (val) {
//       console.log("🚀 ~ val:", val)
// const { projectId, autoId, ...rest } = val
// const params = {
//   projectId,
//   ruleId: autoId,
//   args: rest,
// }
// curlValue.value = genCurl('POST', `${window.location.origin}/api/rule`, params)
// }
//   },
//   { immediate: true },
// )
</script>

<style scoped></style>
