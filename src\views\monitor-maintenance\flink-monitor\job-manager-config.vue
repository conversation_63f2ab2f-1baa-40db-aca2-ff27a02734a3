<template>
  <div style="height: calc(100vh - 150px); width: 100%; overflow-y: auto; padding: 8px 24px">
    <a-descriptions
      layout="vertical"
      bordered
      size="small"
      :column="{ xxl: 4, xl: 3, lg: 2, md: 2, sm: 2, xs: 1 }"
    >
      <a-descriptions-item :label="key" :span="1" v-for="(value, key) of clusterlInfo" :key="key">
        {{ value ? value : '--' }}
      </a-descriptions-item>
    </a-descriptions>
  </div>
</template>
<script lang="ts" setup>
import { ref } from 'vue'
import { getJobManagerConfig } from '@/api/services/monitor-maintenance/flink-monitor'

const clusterlInfo = ref<any>({})

// 获取列表数据
const getList = () => {
  getJobManagerConfig({}).then((res) => {
    clusterlInfo.value = res.data.obj
  })
}

onMounted(() => {
  getList()
})
</script>
