<template>
  <div
    class="w-410px h-216px cursor-pointer"
    b="2px dashed #e3e4e6 hover:primary rd-8px"
    flex="~ justify-center items-center"
  >
    <a-space size="small" direction="horizontal" align="center">
      <a-button
        type="text"
        shape="default"
        size="small"
        :loading="false"
        :disabled="false"
        @click="handleClick"
        text="16px primary"
      >
        <template #icon>
          <FolderAddOutlined />
        </template>
        创建知识库
      </a-button>
    </a-space>
  </div>
</template>

<script setup lang="ts">
import { FolderAddOutlined } from '@ant-design/icons-vue'
const handleClick = () => {}
</script>

<style scoped></style>
