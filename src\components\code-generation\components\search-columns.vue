<template>
  <div class="table-columns-add">
    <a-button type="primary" @click="addHandle">新增</a-button>
  </div>
  <a-table
    :dataSource="newModelValue"
    :columns="columns"
    :customRow="customRow"
    :pagination="false"
  >
    <template #bodyCell="{ column, record }">
      <template v-if="column.key === 'operate'">
        <a-button type="link" @click="del(record)">删除</a-button>
      </template>
      <template v-else-if="column.key === 'pattern'">
        <a-auto-complete
          v-model:value="record[column.key]"
          :options="patternList"
          style="width: 120px"
          placeholder="请选择正则表达式"
          :disabled="!record['required']"
        />
        <FormOutlined
          v-if="record[column.key] === 'select'"
          @click="addOptions(record)"
          style="margin-left: 3px"
        />
      </template>
      <template v-else-if="column.key === 'required'">
        <a-switch v-model:checked="record[column.key]" />
      </template>
      <template v-else-if="column.key === 'type'">
        <a-select
          v-model:value="record[column.key]"
          @change="changeType(record)"
          style="width: 120px"
          placeholder="请选择类型"
        >
          <a-select-option :value="item.value" v-for="item in typeList" :key="item.value">{{
            item.text
          }}</a-select-option>
        </a-select>
        <FormOutlined
          v-if="['select', 'radio', 'checkbox'].includes(record['type'])"
          @click="addOptions(record)"
          style="margin-left: 3px"
        />
      </template>
      <template v-else>
        <div class="input-group">
          <span
            v-if="column.key === 'name'"
            class="icon"
            @mouseenter="draggable = true"
            @mouseleave="draggable = false"
            ><HolderOutlined
          /></span>
          <a-input
            type="text"
            v-model:value="record[column.key]"
            :placeholder="'请输入' + column.title"
          />
        </div>
      </template>
    </template>
  </a-table>

  <a-modal v-model:open="open" title="选项列表" @ok="open = false">
    <div>
      <a-button @click="addValue">新增</a-button>
    </div>
    <a-table :dataSource="selectValue.options" :columns="optionsColumns">
      <template #bodyCell="{ column, record, index }">
        <template v-if="column.key === 'operate'">
          <a-button type="link" @click="delOptions(index)">删除</a-button>
        </template>
        <a-input
          v-else
          type="text"
          v-model:value="record[column.key]"
          :placeholder="'请输入' + column.title"
        />
      </template>
    </a-table>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { FormOutlined, HolderOutlined } from '@ant-design/icons-vue'
import { generateDataFromList } from '@/utils/tool'

interface Columns {
  modelValue: any[]
  type?: string
}

const open = ref(false)
const selectValue: Record<string, any> = ref({})

const draggable = ref(false)
const dragItem = ref<any>({})
const targItem = ref<any>({})
const typeValue = ref('input')

const emit = defineEmits(['update:modelValue'])

const props: Columns = withDefaults(defineProps<Columns>(), {
  modelValue: () => [],
  type: 'search',
})

const optionsColumns = ref([
  {
    title: '名称',
    dataIndex: 'label',
    key: 'label',
  },
  {
    title: '值',
    dataIndex: 'value',
    key: 'value',
  },
  {
    title: '操作',
    dataIndex: 'operate',
    key: 'operate',
  },
])

const typeList = ref([
  {
    text: 'input 输入框',
    value: 'input',
  },
  {
    text: 'select 下拉框',
    value: 'select',
  },
  {
    text: 'date 日期',
    value: 'date',
  },
  {
    text: 'radio 单选框',
    value: 'radio',
  },
  {
    text: 'checkbox 复选框',
    value: 'checkbox',
  },
])

const columns = ref<any[]>([
  {
    title: '字段名',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '字段描述',
    dataIndex: 'description',
    key: 'description',
  },
  // {
  //   title: '是否必填',
  //   dataIndex: 'required',
  //   key: 'required',
  // },
  // {
  //   title: '正则表达式',
  //   dataIndex: 'pattern',
  //   key: 'pattern',
  // },
  {
    title: '类型',
    dataIndex: 'type',
    key: 'type',
  },
  {
    title: '操作',
    dataIndex: 'operate',
    key: 'operate',
  },
])

const patternList = [
  {
    text: '整数',
    value: 'INTEGER',
  },
  {
    text: '数字',
    value: 'FLOAT',
  },
  {
    text: '电子邮箱',
    value: 'EMAIL',
  },
  {
    text: 'URL',
    value: 'URL',
  },
  {
    text: '电话号码',
    value: 'PHONE',
  },
  {
    text: '身份证号码',
    value: 'ID_CARD',
  },
]

const newModelValue = computed({
  get: () => {
    const list = props.modelValue
    list.forEach((item: any) => {
      item.key = generateDataFromList('number')
      if (item?.children && item.children.length > 0) {
        item.children.forEach((child: any) => {
          child.key = generateDataFromList('number')
        })
      }
    })
    return list
  },
  set: (value) => {
    emit('update:modelValue', value)
  },
})

const customRow = (record: any, index: number) => {
  return {
    draggable: draggable.value,
    ondrag() {
      dragItem.value = {
        record: record,
        index,
      }
    },
    ondrop() {
      targItem.value = {
        record: record,
        index,
      }
    },
    ondragend() {
      if (targItem.value && dragItem.value && dragItem.value !== targItem.value) {
        const sourceIndex = dragItem.value.index
        const targetIndex = targItem.value.index
        const temp = JSON.parse(JSON.stringify(newModelValue.value[targetIndex]))
        newModelValue.value[sourceIndex] = temp
        newModelValue.value[targetIndex] = dragItem.value.record
      }
    },
    ondragover(e: any) {
      e.preventDefault()
      return false
    },
  }
}

const addHandle = () => {
  let obj: any = {
    name: '',
    desc: '',
    type: typeValue.value,
  }
  if (props.type !== 'search') {
    obj.required = false
    obj.pattern = ''
  }
  if (props.type === 'form-column') {
    obj.span = 24
  }

  newModelValue.value.push(obj)
}

const changeType = (data: any) => {
  if (data.type === 'select') data.options = []
}

const addValue = () => {
  if (!selectValue.value?.options) selectValue.value.options = []
  selectValue.value.options.push({ label: '', value: '' })
}

const delOptions = (index: number) => {
  selectValue.value.options.splice(index, 1)
}

const addOptions = (data: any) => {
  open.value = true
  selectValue.value = data
}

const del = (record: any) => {
  const { key } = record
  deleteKey(key, newModelValue.value)
}

const deleteKey = (key: string, list: any[]) => {
  const index = list.findIndex((item: any) => item.key === key)
  if (index !== -1) {
    list.splice(index, 1)
  } else {
    list.forEach((item: any) => {
      if (item.children && item.children.length > 0) {
        deleteKey(key, item.children)
      }
    })
  }
}

const upData = () => {
  emit('update:modelValue', newModelValue.value)
}

onMounted(() => {
  if (props.type === 'form' || props.type === 'form-column') {
    columns.value.splice(2, 0, {
      title: '是否必填',
      dataIndex: 'required',
      key: 'required',
    })
    columns.value.splice(3, 0, {
      title: '正则表达式',
      dataIndex: 'pattern',
      key: 'pattern',
    })
  }
  if (props.type === 'form-column') {
    columns.value.splice(2, 0, {
      title: '栅格宽度',
      dataIndex: 'span',
      key: 'span',
    })
  }
  if (['params', 'response'].includes(props.type!)) {
    typeList.value = [
      {
        text: 'string',
        value: 'string',
      },
      {
        text: 'number',
        value: 'number',
      },
      {
        text: 'long',
        value: 'long',
      },
      {
        text: 'double',
        value: 'double',
      },
      {
        text: 'boolean',
        value: 'boolean',
      },
      {
        text: 'char',
        value: 'char',
      },
      {
        text: 'Object',
        value: 'Object',
      },
      {
        text: 'Integer',
        value: 'Integer',
      },
      {
        text: 'Float',
        value: 'Float',
      },
      {
        text: 'Double',
        value: 'Double',
      },
      {
        text: 'Character',
        value: 'Character',
      },
      {
        text: 'LocalDate',
        value: 'LocalDate',
      },
      {
        text: 'List',
        value: 'List',
      },
    ]
    typeValue.value = 'string'
    if (['params'].includes(props.type!)) {
      columns.value.splice(3, 0, {
        title: '是否必填',
        dataIndex: 'required',
        key: 'required',
        width: 100,
      })
    }
  }
  if (props.type === 'default') {
    columns.value.splice(2, 1)
  }
})

// 导出 show 方法
defineExpose({
  upData,
})
</script>

<style scoped lang="less">
.table-columns-add {
  text-align: right;
  margin-bottom: 16px;
}
.input-group {
  display: flex;
  align-items: center;
  .icon {
    margin-right: 5px;
    cursor: pointer;
  }
}
</style>
