import service from '@/api'
import { ASSETMANAGER } from '@/api/base'
// import type { VNode } from 'vue'

export interface AssetResponse<T = any> {
  data: T
  code: string
  msg: string
  result: string
}

export interface ISelectItem<T = any> {
  value: T
  label: string
}

export interface ISubscriptIteSet {
  configName: string // 配置名称
  indexDataId: string // 订阅指标ID
  serverId: string | null // 订阅者数据表ID
  pushType: string // 推送方式1每日2每周3每月
  pushDate?: string //  推送日期，1,3每周代表为每周一周三，每月代表每月1号三号
  pushTime?: string // 推送时间，09:00:00
  description?: string // 配置描述
  configId?: string // 新增后才有
  pushDateArray?: string[] // 推送日期数组
  status?: number
  indexName?: string
}

// 订阅配置列表参数详情
export interface ISubscriptConfigList {
  approvalStatus: number
  configId: string
  configName: string
  createTime: string
  createUser: string
  description: string
  indexDataId: string
  pushDate: string
  pushTime: string
  pushType: number
  serverId: string
  status: number // 订阅状态1启用2禁用'
}

// 获取订阅配置列表
export function getSubscriptSetList(): Promise<AssetResponse<ISubscriptConfigList[]>> {
  return service({
    method: 'GET',
    url: `${ASSETMANAGER}/dataSubscriptionConfig/getDataSubscripConfig`,
  })
}

export interface IPageParams<T = any> {
  pageIndex: number
  pageSize: number
  totalPages: number
  totalRecords: number
  records: T
}

// 分页查询数据订阅服务配置
export async function getSubscripConfigListByPages(
  data: {
    configName?: string
    pageIndex: number
    pageSize: number
  },
  headers?: {
    opUser: string
    opUserName?: string
  },
): Promise<AssetResponse<IPageParams<ISubscriptConfigList[]>>> {
  return service({
    method: 'GET',
    url: `${ASSETMANAGER}/dataSubscriptionConfig/getPageDataSubscripConfig`,
    params: data,
    headers,
  })
}

export interface IServerIdItem {
  apiHeaders: string
  apiHost: string
  apiPath: string
  apiType: string
  createTime: string
  createUser: string
  fromAddress: string
  host: string
  password: string
  recEmail: string
  serverId: string
  serverName: string
  updateTime: string
  userName: string
}

// 查询数据订阅服务
export function fetchServerIdByName(serverName: string): Promise<AssetResponse<IServerIdItem[]>> {
  return service({
    method: 'GET',
    url: `${ASSETMANAGER}/dataSubscriptionAdmin/getDataSubscripServer`,
    data: { serverName },
  })
}

// 新增订阅配置
export function addSubscriptSetItem(data: ISubscriptIteSet): Promise<AssetResponse<string>> {
  return service({
    method: 'post',
    url: `${ASSETMANAGER}/dataSubscriptionConfig/addDataSubscripConfig`,
    data: data,
  })
}

// 编辑订阅配置
export function editSubscriberSetItem(data: ISubscriptIteSet): Promise<AssetResponse<string>> {
  return service({
    method: 'post',
    url: `${ASSETMANAGER}/dataSubscriptionConfig/updateDataSubscripConfig`,
    data: data,
  })
}
// 删除数据订阅服务配置
export function deleteSubscriberSetItem(configId: string): Promise<AssetResponse<string>> {
  return service({
    method: 'post',
    url: `${ASSETMANAGER}/dataSubscriptionConfig/delDataSubscripConfig`,
    data: { configId },
  })
}
export interface IIndexItem {
  indexId: string // 指标ID
  indexName: string // 指标名称
}

// 指标下拉列表
export async function getIndexDataListByPages(data: {
  indexName?: string
  pageIndex: number
  pageSize: number
}): Promise<AssetResponse<IPageParams<ISubscriptConfigList[]>>> {
  return service({
    method: 'GET',
    url: `${ASSETMANAGER}/dataSubscriptionConfig/getIndexDataList`,
    params: data,
  })
}

export interface ISubStatusItem {
  configId: string
  changeType: number // 操作类型1启用2禁用
}

// 禁用/启用订阅配置
export function changeSubStatusApi(params: ISubStatusItem): Promise<AssetResponse<string>> {
  return service({
    method: 'post',
    url: `${ASSETMANAGER}/dataSubscriptionConfig/changeSubStatus`,
    data: params,
  })
}

export interface IPushrecordList {
  autoId: string // 自增主键
  configId: string // 订阅配置主键
  startTime: string // 开始时间
  finishTime: string // 完成时间
  status: string // 推送任务状态 0执行中1成功2失败
  pushType: string // 推送方式 1自动2手动
  createTime: string // 创建时间
  updateTime: string // 更新时间
  datas: string // 推送数据json
}

// 查询订阅推送记录
export async function getPushrecordListByPages(
  data: {
    configId?: string
    pageIndex: number
    pageSize: number
  },
  headers?: {
    opUser: string
    opUserName?: string
  },
): Promise<AssetResponse<IPageParams<ISubscriptConfigList[]>>> {
  return service({
    method: 'GET',
    url: `${ASSETMANAGER}/dataSubscriptionConfig/datasubscrippushrecordPageList`,
    params: data,
    headers,
  })
}
