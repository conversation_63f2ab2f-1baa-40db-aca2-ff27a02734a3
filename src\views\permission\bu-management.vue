<template>
  <div class="bu-management">
    <cardBox title="BU管理" subTitle="BU数据权限隔离管理">
      <template #headerRight>
        <a-button type="primary" v-action:addBu @click="openAddModal('add')">新增</a-button>
      </template>
      <Table
        class="section-table"
        ref="tableRef"
        serial-no
        :columns="columns"
        :get-data="getBUList"
        :search-form-state="formState"
        :tableConfig="{
          rowKey: 'buId',
        }"
      >
        <template #search>
          <a-form-item label="" name="buName">
            <a-input v-model:value.trim="formState.buName" placeholder="请输入名称"></a-input>
          </a-form-item>
          <a-form-item label="" name="isUse">
            <a-select v-model:value="formState.isUse" placeholder="请选择是否启用" allow-clear>
              <a-select-option value="1">是</a-select-option>
              <a-select-option value="0">否</a-select-option>
            </a-select>
          </a-form-item>
        </template>
        <template #bodyCell="{ record, column }">
          <template v-if="column.key === 'action'">
            <a-dropdown>
              <a class="ant-dropdown-link" @click.prevent>
                更多
                <DownOutlined />
              </a>
              <template #overlay>
                <a-menu>
                  <a-menu-item>
                    <a-Button
                      type="text"
                      v-action:addBu
                      style="width: 100%"
                      @click="handleActionClick('edit', record)"
                      >编辑</a-Button
                    >
                  </a-menu-item>
                  <template v-if="record?.buId != 'system_bu'">
                    <a-menu-item>
                      <a-Button
                        type="text"
                        style="width: 100%"
                        @click="handleActionClick('view', record)"
                        v-action:viewMenu
                        >分配可查看菜单</a-Button
                      >
                    </a-menu-item>
                    <a-menu-item>
                      <a-Button
                        type="text"
                        style="width: 100%"
                        @click="handleActionClick('impower', record)"
                        v-action:accreditMenu
                        >分配可授权菜单</a-Button
                      >
                    </a-menu-item>
                  </template>
                  <a-menu-item>
                    <a-Button
                      type="text"
                      style="width: 100%"
                      @click="handleActionClick('assigned', record)"
                      v-action:distributionSystem
                      >已分配系统</a-Button
                    >
                  </a-menu-item>
                  <a-menu-item>
                    <a-Button
                      type="text"
                      style="width: 100%"
                      @click="handleActionClick('resetPassword', record)"
                      v-action:resetPassword
                      >重置密码</a-Button
                    >
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </template>
        </template>
      </Table>
    </cardBox>

    <!-- 新增BU -->
    <AddBuModal
      v-if="addBuModalOpen"
      v-model="addBuModalOpen"
      :type="addBuModalType"
      :record="currentRecord"
      @submit="tableRef?.getTableData()"
    />
    <!-- 分配可查看菜单 -->
    <common-tree-node
      :exportModal="MenusModal"
      ref="Modalmenus"
      :title="title"
      :modalSystem="modalSystem"
      :exportTreeData="exportTreeData"
      :fNames="fNames"
      :MenusModalClear="MenusModalClear"
      :confirmLoading="confirmLoading"
      @systemSelectEmitHandle="systemSelectEmitHandle"
      @exportModalHandleOk="exportModalHandleOk"
      @exportModalCancel="exportModalCancel"
    ></common-tree-node>
    <!-- 已分配系统 -->
    <AssignedMenuModal ref="assignedModal" :roleId="currentRecord?.buManageRoleId" />
    <ResetPasswordModal
      v-if="resetPasswordModalOpen"
      v-model="resetPasswordModalOpen"
      :user-id="currentUserId"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Table } from '@fs/fs-components'
import { getBUList, getRoleMenuList, getUserSystem, saveRoleMneu } from '@/api/services/permission'
import AddBuModal, { type AddBuModalProps } from '@/components/permission/add-bu-modal.vue'
import { message, type RadioGroupProps } from 'ant-design-vue'
import AssignedMenuModal from '@/components/permission/assigned-menu-modal.vue'
import { DownOutlined } from '@ant-design/icons-vue'
import { storeToRefs } from 'pinia'
import { useUserStore } from '@/stores/user'
import cardBox from '@/components/card-box/card-box.vue'

defineOptions({
  name: 'BuSelect',
})

const formState = ref({
  buName: '',
  isUse: null,
})
const isUseOptions = ref<RadioGroupProps['options']>([
  { label: '是', value: true },
  { label: '否', value: false },
])
const addBuModalOpen = ref(false)
const addBuModalType = ref<AddBuModalProps['type']>()
const tableRef = ref<InstanceType<typeof Table> | null>(null)
const currentRecord = ref()
const MenusModal = ref<boolean>(false)
const assignedModal = ref<any>(null)
const modalSystem = ref<any>([])
const exportTreeData = ref<any>([])
const systemId = ref('')
const title = ref('')
const MenusModalClear = ref<boolean>(false)
const confirmLoading = ref<boolean>(false)
const queryType = ref<number>(1)
const userStore = useUserStore()
const { userInfo } = storeToRefs(userStore)
const checkedDataRoleId = ref<string>('')
const resetPasswordModalOpen = ref(false)
const currentUserId = ref('')
const fNames = {
  children: 'datas',
  title: 'menuName',
  key: 'menuId',
}
const columns = ref([
  {
    title: '事业单位名称',
    dataIndex: 'buName',
  },
  {
    title: '管理员账号',
    dataIndex: 'buManageUserAccount',
  },
  {
    title: '是否启用',
    dataIndex: 'isUse',
  },
  {
    title: '备注',
    dataIndex: 'remark',
  },
  {
    title: '创建人',
    dataIndex: 'userName',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 150,
  },
  {
    title: '操作',
    key: 'action',
    width: 150,
  },
])

function openAddModal(type: 'add' | 'edit', record?: any) {
  addBuModalOpen.value = true
  addBuModalType.value = type
  currentRecord.value = record
}

function handleActionClick(type: string, record: any) {
  currentRecord.value = record
  currentUserId.value = record.buManageUserId || ''
  switch (type) {
    case 'edit':
      openAddModal(type, record)
      break
    case 'assigned':
      assignedModal.value?.show()
      break
    case 'view':
      checkedDataRoleId.value = record?.buManageRoleId
      queryType.value = 1
      title.value = '分配可查看菜单'
      getUserSystemModal()
      break
    case 'impower':
      checkedDataRoleId.value = record?.buManageRoleId
      queryType.value = 2
      title.value = '分配可授权菜单'
      getUserSystemModal()
      break
    case 'resetPassword':
      resetPasswordModalOpen.value = true
      break
    default:
      break
  }
}

// 获取查看菜单接口
function getUserSystemModal() {
  const data = {
    userId: userInfo.value.userId,
    queryType: queryType.value,
  }
  getUserSystem(data).then((res: any) => {
    MenusModal.value = true
    modalSystem.value = res.data
  })
}

// 分配可查看授权菜单
function systemSelectEmitHandle(id: string) {
  systemId.value = id
  const data = {
    roleId: checkedDataRoleId.value,
    systemId: systemId.value,
    queryType: queryType.value,
  }
  getRoleMenuList(data).then((res) => {
    exportTreeData.value = res.data
  })
}

//新增用户组选中菜单
async function exportModalHandleOk(data: Record<string, any>) {
  confirmLoading.value = true
  const menuList = data
  const params = {
    menuList,
    queryType: queryType.value,
    roleId: checkedDataRoleId.value,
    systemId: systemId.value,
  }
  saveRoleMneu(params).then(() => {
    message.success('新增成功!')
    confirmLoading.value = false
    MenusModal.value = false
    exportTreeData.value = []
  })
}
// 分配可查看菜单
function exportModalCancel() {
  exportTreeData.value = []
  MenusModal.value = false
}
</script>

<style scoped lang="less">
.bu-management {
  background: white;
}
</style>
<style lang="less">
.ant-dropdown-menu {
  .ant-dropdown-menu-item:hover {
    background-color: transparent !important;
  }
}
.bu-management {
  height: 100%;
  background: white;
}
</style>
