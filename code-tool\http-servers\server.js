/* global process */
import express from 'express'
import bodyParser from 'body-parser'
import {
  handleRequestWithDynamicHandler,
  handleSavedraft,
  handleGetdraft,
  getDraftData,
  getVirtualJson,
  handleFormatting,
  handleGtApiData,
  handleSaveApiData,
  handleGenerationCode,
  handleSaveUrlData,
  handleEditUrlData,
  handleDelUrl,
  handleGetUrlDetail,
  handleSynchronous,
  handleGenerateDeploy,
  handleGenerationRouter,
  handleGetGenerationRouter,
  handleMockTest,
  handleMockFormDataTest,
  handleSaveLayoutLogin,
} from './module/index.js'
import {
  handleGetGptMoudles,
  handleAddGptMoudles,
  handleAddInterface,
  handleGenerationGpt,
  handleDelGenerationGpt,
  handleGetApiTemplate,
  handleSaveApiMock,
} from './module/gpt.js'
import { handleGetCoustomTemplate, generationCoustomTemplate } from './module/custom.js'
const app = express()

const port = process.env.PORT || 3001

// 使用 body-parser 中间件
app.use(bodyParser.json({ limit: '10mb' }))
app.use(bodyParser.urlencoded({ limit: '10mb', extended: true }))

// 从虚拟文件系统获取api数据
app.post('/server/getApiData', async (req, res) => {
  handleRequestWithDynamicHandler(req, res, handleGtApiData)
})

// 保存api数据到虚拟文件系统
app.post('/server/saveApiData', async (req, res) => {
  handleRequestWithDynamicHandler(req, res, handleSaveApiData)
})

// 生成代码
app.post('/server/generationCode', async (req, res) => {
  handleRequestWithDynamicHandler(req, res, handleGenerationCode)
})

// 保存草稿箱
app.post('/server/savedraft', (req, res) => {
  handleRequestWithDynamicHandler(req, res, handleSavedraft)
})

// 获取草稿箱
app.post('/server/getdraft', (req, res) => {
  handleRequestWithDynamicHandler(req, res, handleGetdraft)
})

// 格式化代码
app.post('/server/formatting', async (req, res) => {
  handleRequestWithDynamicHandler(req, res, handleFormatting)
})

// 增加swagger文档接口
app.post('/server/saveUrlData', async (req, res) => {
  handleRequestWithDynamicHandler(req, res, handleSaveUrlData)
})

// 修改swagger文档接口
app.post('/server/editUrlData', async (req, res) => {
  handleRequestWithDynamicHandler(req, res, handleEditUrlData)
})

// 删除swagger文档数据
app.post('/server/delUrl', async (req, res) => {
  handleRequestWithDynamicHandler(req, res, handleDelUrl)
})

// 查询swagger文档数据
app.post('/server/getUrlDetail', async (req, res) => {
  handleRequestWithDynamicHandler(req, res, handleGetUrlDetail)
})

// swagger文档同步
app.post('/server/synchronous', async (req, res) => {
  handleRequestWithDynamicHandler(req, res, handleSynchronous)
})

// 代码部署
app.post('/server/generateDeploy', async (req, res) => {
  handleRequestWithDynamicHandler(req, res, handleGenerateDeploy)
})

// 生成路由
app.post('/server/generationRouter', async (req, res) => {
  handleRequestWithDynamicHandler(req, res, handleGenerationRouter)
})

// 获取需要生成路由列表
app.post('/server/getGenerationRouter', async (req, res) => {
  handleRequestWithDynamicHandler(req, res, handleGetGenerationRouter)
})

// mock调用测试
app.post('/server/mockTest', async (req, res) => {
  handleMockTest(req, res)
})

// mock调用测试formdata格式
app.post('/server/mockFormDataTest', async (req, res) => {
  handleMockFormDataTest(req, res)
})

// 生成布局代码
app.post('/server/saveLayoutLogin', async (req, res) => {
  handleRequestWithDynamicHandler(req, res, handleSaveLayoutLogin)
})

// 获取gpt模块数据
app.post('/server/gpt/getGptMoudles', async (req, res) => {
  handleRequestWithDynamicHandler(req, res, handleGetGptMoudles)
})

// 新增gpt模块数据
app.post('/server/gpt/addGptMoudles', async (req, res) => {
  handleRequestWithDynamicHandler(req, res, handleAddGptMoudles)
})

// 新增gpt模块下接口数据
app.post('/server/gpt/gptMoudles/addInterface', async (req, res) => {
  handleRequestWithDynamicHandler(req, res, handleAddInterface)
})

// 生成gpt模块接口代码
app.post('/server/gpt/generationGpt', async (req, res) => {
  handleRequestWithDynamicHandler(req, res, handleGenerationGpt)
})

// 删除gpt模块接口代码
app.post('/server/gpt/delGenerationGpt', async (req, res) => {
  handleRequestWithDynamicHandler(req, res, handleDelGenerationGpt)
})

// 获取接口ts函数字符串模板
app.post('/server/getApiTemplate', async (req, res) => {
  handleRequestWithDynamicHandler(req, res, handleGetApiTemplate)
})

// 保存模块mock数据
app.post('/server/gpt/saveApiMock', async (req, res) => {
  handleRequestWithDynamicHandler(req, res, handleSaveApiMock)
})

// 获取自定义模板初始数据
app.post('/server/custom/getTemplate', async (req, res) => {
  handleRequestWithDynamicHandler(req, res, handleGetCoustomTemplate)
})

// 生成自定义模板字符串据
app.post('/server/custom/generationCoustomTemplate', async (req, res) => {
  handleRequestWithDynamicHandler(req, res, generationCoustomTemplate)
})

app.listen(port, async () => {
  await getDraftData()
  await getVirtualJson()
  console.log(`服务器运行在端口 ${port}`)
})
