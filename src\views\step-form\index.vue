<template>
  <Table :columns="columns" :getData="addGoodsCategory" ref="tableRef" :searchFormState="form">
    <template #operate>
      <a-button type="primary" @click="addTableHandel">新增</a-button>
    </template>
    <template #search>
      <a-form-item label="店铺编号" name="storeId">
        <a-input v-model:value="form.storeId" placeholder="请输入店铺编号" />
      </a-form-item>
      <a-form-item label="商品分类名称" name="categoryName">
        <a-input v-model:value="form.categoryName" placeholder="请输入商品分类名称" />
      </a-form-item>
      <a-form-item label="分类图标" name="categoryUrl">
        <a-input v-model:value="form.categoryUrl" placeholder="请输入分类图标" />
      </a-form-item>
      <a-form-item label="上级分类" name="parentId">
        <a-input v-model:value="form.parentId" placeholder="请输入上级分类" />
      </a-form-item>
    </template>
    <template #bodyCell="data">
      <template v-if="data.column.dataIndex === 'action'">
        <a-button type="link" @click="editTableHandle(data.record)">编辑</a-button>
        <a-popconfirm title="是否确认删除" @confirm="confirm(data.record)">
          <a-button type="link">删除</a-button>
        </a-popconfirm>
      </template>
      <template v-else>
        {{ data.value }}
      </template>
    </template>
  </Table>

  <a-modal
    v-model:open="modalValue"
    @ok="modalHandleOk"
    :title="modalType === 'edit' ? '编辑' : '新增'"
    @cancel="cancel"
    width="800px"
    :confirm-loading="confirmLoading"
  >
    <a-form
      ref="formRef"
      :model="modalForm"
      :rules="rules"
      :label-col="labelCol"
      :wrapper-col="wrapperCol"
    >
      <a-form-item
        label="店铺编号"
        name="storeId"
        :rules="[{ required: true, message: '请输入店铺编号' }]"
      >
        <a-input v-model:value="modalForm.storeId" placeholder="请输入店铺编号" />
      </a-form-item>

      <a-form-item
        label="商品分类名称"
        name="categoryName"
        :rules="[{ required: true, message: '请输入商品分类名称' }]"
      >
        <a-input v-model:value="modalForm.categoryName" placeholder="请输入商品分类名称" />
      </a-form-item>

      <a-form-item
        label="分类图标"
        name="categoryUrl"
        :rules="[{ required: false, message: '请输入分类图标' }]"
      >
        <a-input v-model:value="modalForm.categoryUrl" placeholder="请输入分类图标" />
      </a-form-item>

      <a-form-item
        label="上级分类"
        name="parentId"
        :rules="[{ required: false, message: '请输入上级分类' }]"
      >
        <a-input v-model:value="modalForm.parentId" placeholder="请输入上级分类" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Table, getPattern } from '@fs/fs-components'
import type { Rule } from 'ant-design-vue/es/form'
import { message } from 'ant-design-vue/es/components'

const labelCol = { span: 5 }
const wrapperCol = { span: 13 }
const form: Record<string, any> = ref({
  storeId: '',
  categoryName: '',
  categoryUrl: '',
  parentId: '',
})
const columns = [
  {
    title: '操作',
    key: 'action',
    dataIndex: 'action',
  },
]
const rules: Record<string, Rule[]> = {}
const modalForm: Record<string, any> = ref({})
const modalValue = ref(false)
const modalType = ref<string>('')
const formRef = ref<HTMLFormElement | null>(null)
const confirmLoading = ref<boolean>(false)
const tableRef = ref<InstanceType<typeof Table> | null>(null)

function addTableHandel() {
  modalValue.value = true
  modalType.value = 'add'
}

function editTableHandle(data: any) {
  modalForm.value = data
  modalType.value = 'edit'
  modalValue.value = true
}

function cancel() {
  modalValue.value = false
  formRef.value?.resetFields()
}

async function confirm(data: any) {
  try {
    message.success('删除成功')
  } catch (error) {
    console.log(error)
  }
}

function addGoodsCategory(value: any) {
  return Promise.resolve({
    code: '000000',
    msg: '新增成功',
    value,
  })
}

async function modalHandleOk() {
  try {
    if (!formRef.value) {
      console.error('获取form表单失败')
      return
    }
    await formRef.value.validate()
    confirmLoading.value = true
    let handle
    if (modalType.value === 'edit') {
      handle = addGoodsCategory
    } else {
      handle = addGoodsCategory
    }
    await handle(modalForm.value)
    tableRef.value?.getTableData()
    message.success('操作成功')
    modalValue.value = false
    confirmLoading.value = false
  } catch (error) {
    confirmLoading.value = false
    console.log(error)
  }
}
</script>
