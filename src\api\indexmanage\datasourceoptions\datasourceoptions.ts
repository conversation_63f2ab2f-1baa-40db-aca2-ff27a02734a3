import service from '@/api'
import { MOCK_DATA } from './datasourceoptions-mock'

/**
 * 新增接口 - 获取数据源列表
 * @param {number} dbType - 数据库类型(1:mysql;2:kudu;3:tidb;4:hive;5:oracle;6:sqlserver;7:cache;8:mongodb;9:file;10:Http WebApi)
 * @param {string} ownerId - 用户所属编号
 * @param {string} opUser - 操作人
 * @param {string} opUserName - 操作人名称
 * @param {string} tenantId - 租户编号
 * @returns {any} - 返回一个解析为响应数据的Promise
 */

export async function getSqlSourceDataList(
  data: {
    dbType?: number
    ownerId?: string
  },
  headers?: {
    opUser?: string
    opUserName?: string
    tenantId?: string
  },
): Promise<{
  code: string
  msg: string
  data: Array<{ id: number; dbType: number; dbUrl: string; dbOther: string; dbDesc: string }>
}> {
  return service(
    {
      method: 'GET',
      url: '/indexManage/dataSourceOptions/getSqlSourceDataList',
      params: data,
      headers,
    },
    MOCK_DATA.getSqlSourceDataList,
  )
}

/**
 * 新增接口 - 获取数据源的库信息列表
 * @param {string} json - json参数(示例:"{\"id\":1}")
 * @param {string} opUser - 操作人
 * @param {string} opUserName - 操作人名称
 * @param {string} tenantId - 租户编号
 * @returns {any} - 返回一个解析为响应数据的Promise
 */

export async function getSqlDataBaseInfoList(
  data: {
    json: string
  },
  headers?: {
    opUser?: string
    opUserName?: string
    tenantId?: string
  },
): Promise<{
  code: string
  msg: string
  data: Array<{ dbTypMap: string; scopedSlots: { icon: string }; title: string; key: string }>
}> {
  return service(
    {
      method: 'POST',
      url: '/indexManage/dataSourceOptions/getSqlDataBaseInfoList',
      data,
      headers,
    },
    MOCK_DATA.getSqlDataBaseInfoList,
  )
}

/**
 * 新增接口 - 获取数据源的表信息列表
 * @param {number} dataSourceId - 数据源ID
 * @param {string} dataBaseName - 数据库名称
 * @param {string} opUser - 操作人
 * @param {string} opUserName - 操作人名称
 * @param {string} tenantId - 租户编号
 * @returns {any} - 返回一个解析为响应数据的Promise
 */

export async function getSqlDateBaseTableInfoList(
  data: {
    dataSourceId: number
    dataBaseName: string
  },
  headers?: {
    opUser?: string
    opUserName?: string
    tenantId?: string
  },
): Promise<{
  code: string
  msg: string
  data: Array<{ scopedSlots: { icon: string }; title: string; key: string }>
}> {
  return service(
    {
      method: 'POST',
      url: '/indexManage/dataSourceOptions/getSqlDateBaseTableInfoList',
      data,
      headers,
    },
    MOCK_DATA.getSqlDateBaseTableInfoList,
  )
}

/**
 * 新增接口 - 获取数据源的列字段信息列表
 * @param {number} dataSourceId - 数据源ID
 * @param {string} dataBaseName - 数据库名称
 * @param {string} tableName - 表名称
 * @param {string} opUser - 操作人
 * @param {string} opUserName - 操作人名称
 * @param {string} tenantId - 租户编号
 * @returns {any} - 返回一个解析为响应数据的Promise
 */

export async function getSqlDateBaseColumnParamsInfoList(
  data: {
    dataSourceId: number
    dataBaseName: string
    tableName: string
  },
  headers?: {
    opUser?: string
    opUserName?: string
    tenantId?: string
  },
): Promise<{
  code: string
  msg: string
  data: Array<{
    scopedSlots: { icon: string }
    title: string
    key: string
    desc: string
    disabled: boolean
    columnType: string
    columnName: string
  }>
}> {
  return service(
    {
      method: 'POST',
      url: '/indexManage/dataSourceOptions/getSqlDateBaseColumnParamsInfoList',
      data,
      headers,
    },
    MOCK_DATA.getSqlDateBaseColumnParamsInfoList,
  )
}

/**
 * 新增接口 - 执行SQL并返回执行结果数据
 * @param {number} id - 数据源主键
 * @param {string} dbName - 库名称
 * @param {string} querysql - 执行SQL
 * @param {string} ownerId - 所属用户编号
 * @param {string} tabName - undefined
 * @param {number} pageNumber - 页码
 * @param {number} pageSize - 页大小
 * @param {string} opUser - 操作人
 * @param {string} opUserName - 操作人名称
 * @param {string} tenantId - 租户编号
 * @returns {any} - 返回一个解析为响应数据的Promise
 */

export async function executeSqlAndReturnResultData(
  data: {
    id: number
    dbName: string
    querysql: string
    ownerId: string
    tabName?: string
    pageNumber: number
    pageSize: number
  },
  headers?: {
    opUser?: string
    opUserName?: string
    tenantId?: string
  },
): Promise<{
  code: string
  msg: string
  data: undefined
}> {
  return service(
    {
      method: 'POST',
      url: '/indexManage/dataSourceOptions/executeSqlAndReturnResultData',
      data,
      headers,
    },
    MOCK_DATA.executeSqlAndReturnResultData,
  )
}

/**
 * 新增接口 - 生成SQL
 * @param {number} dataSourceId - 数据源id
 * @param {string} queryJson - 查询参数json
 * @param {string} opUser - 操作人
 * @param {string} opUserName - 操作人名称
 * @param {string} tenantId - 租户编号
 * @returns {any} - 返回一个解析为响应数据的Promise
 */

export async function getGenerateSql(
  data: {
    dataSourceId: number
    queryJson: string
  },
  headers?: {
    opUser?: string
    opUserName?: string
    tenantId?: string
  },
): Promise<{
  code: string
  msg: string
  data: { generateSql: string }
}> {
  return service(
    {
      method: 'POST',
      url: '/indexManage/dataSourceOptions/getGenerateSql',
      data,
      headers,
    },
    MOCK_DATA.getGenerateSql,
  )
}

/**
 * 新增接口 - 数据预览
 * @param {number} dataSourceId - 数据源id
 * @param {string} queryJson - 查询参数json
 * @param {number} previewType - 预览类型：1=正常模式，2=自定义SQL模式
 * @param {string} opUser - 操作人
 * @param {string} opUserName - 操作人名称
 * @param {string} tenantId - 租户编号
 * @returns {any} - 返回一个解析为响应数据的Promise
 */

export async function getDataReview(
  data: {
    dataSourceId: number
    dynamicParam?: string
    queryJson: string
    previewType?: number // 1=正常模式，2=自定义SQL模式
    previewSql?: string // 自定义SQL字段
    databaseName?: string // 数据库名称
  },
  headers?: {
    opUser?: string
    opUserName?: string
    tenantId?: string
  },
): Promise<{
  code: string
  msg: string
  data: { cost: number; dataList: undefined }
}> {
  return service(
    {
      method: 'POST',
      url: '/indexManage/dataSourceOptions/getDataReview',
      data,
      headers,
    },
    MOCK_DATA.getDataReview,
  )
}

/**
  *  - 查询数据库类型集合
  
  * @returns {any} - 返回一个解析为响应数据的Promise
*/

export async function getDBTypeList(data?: any): Promise<any> {
  return service(
    {
      method: 'GET',
      url: '/indexManage/dataSourceOptions/getDBTypeList',
      params: data,
    },
    MOCK_DATA.getDBTypeList,
  )
}

/**
 * 新增接口 - 获取资产目录库信息列表
 * @param {string} dbType - 数据库类型
 * @param {string} opUser - 操作人
 * @param {string} opUserName - 操作人名称
 * @param {string} tenantId - 租户编号
 * @returns {any} - 返回一个解析为响应数据的Promise
 */

export async function getDataAssertDBList(
  data: {
    dbType: string
  },
  headers?: {
    opUser?: string
    opUserName?: string
    tenantId?: string
  },
): Promise<{
  code: string
  msg: string
  data: Array<{ dbTypMap: string; scopedSlots: { icon: string }; title: string; key: string }>
}> {
  return service(
    {
      method: 'GET',
      url: '/indexManage/dataSourceOptions/getDataAssertDBList',
      params: data,
      headers,
    },
    MOCK_DATA.getDataAssertDBList,
  )
}

/**
 * 新增接口 - 获取资产目录表信息列表
 * @param {string} databaseId - 数据源ID
 * @param {string} opUser - 操作人
 * @param {string} opUserName - 操作人名称
 * @param {string} tenantId - 租户编号
 * @returns {any} - 返回一个解析为响应数据的Promise
 */

export async function getDataAssetTableList(
  data: {
    databaseId: string
  },
  headers?: {
    opUser?: string
    opUserName?: string
    tenantId?: string
  },
): Promise<{
  code: string
  msg: string
  data: Array<{ title: string; key: string; tableId: string; databaseId: string; comment: string }>
}> {
  return service(
    {
      method: 'GET',
      url: '/indexManage/dataSourceOptions/getDataAssetTableList',
      params: data,
      headers,
    },
    MOCK_DATA.getDataAssetTableList,
  )
}

/**
 * 新增接口 - 获取资产目录表字段信息列表
 * @param {string} tableId - 表名称
 * @param {string} opUser - 操作人
 * @param {string} opUserName - 操作人名称
 * @param {string} tenantId - 租户编号
 * @returns {any} - 返回一个解析为响应数据的Promise
 */

export async function getDataAssetColumnList(
  data: {
    tableId: string
  },
  headers?: {
    opUser?: string
    opUserName?: string
    tenantId?: string
  },
): Promise<{
  code: string
  msg: string
  data: Array<{
    scopedSlots: { icon: string }
    title: string
    key: string
    desc: string
    disabled: boolean
    columnType: string
    columnName: string
    columnTypeName: string
    comment: string
  }>
}> {
  return service(
    {
      method: 'GET',
      url: '/indexManage/dataSourceOptions/getDataAssetColumnList',
      params: data,
      headers,
    },
    MOCK_DATA.getDataAssetColumnList,
  )
}
