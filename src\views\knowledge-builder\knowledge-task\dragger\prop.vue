<template>
  <div class="prop">
    <header>
      <h3>属性列表</h3>
    </header>
    <a-collapse v-model:activeKey="activeKey">
      <a-collapse-panel key="1" header="基础属性">
        <a-table :dataSource="tableData" :columns="columns" :pagination="false">
          <template #bodyCell="{ column, text, record }">
            <template v-if="column.key === 'nameZh'">
              <!-- 中文属性 -->
              <div class="txt">
                {{ text }}
                <a-tooltip title="继承的属性" v-if="record.isInherit">
                  <span class="dltxlL"></span>
                </a-tooltip>
              </div>
            </template>
            <template v-if="column.key === 'action'">
              <a @click="handleConfig(record)">高级配置</a>

              <a-tooltip
                title="有 约束 配置, 请点击高级配置查看"
                v-if="record?.constraints?.length"
              >
                <a-tag style="margin: 0 8px">高</a-tag>
              </a-tooltip>
            </template>
          </template>
        </a-table>
      </a-collapse-panel>
    </a-collapse>
    <high-config ref="highConfigRef" />
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue'
import { columns } from './data-source'
import highConfig from './high-config.vue'

const prop = defineProps<{ currentEchartsData: any }>()
const activeKey = ref(['1'])
const highConfigRef = ref()

const tableData = computed(() => {
  const inheritList = prop.currentEchartsData.inheritedPropertyList.map((item: any) => {
    return {
      ...item,
      isInherit: true,
    }
  })
  const otherList = prop.currentEchartsData.propertyList.map((item: any) => {
    return {
      ...item,
      isInherit: false,
    }
  })
  return [...inheritList, ...otherList]
})

const handleConfig = (record: any) => {
  highConfigRef.value.showDrawer(record)
}

onMounted(() => {
  console.log('aaaaaa', prop)
})
</script>
<style scoped lang="less">
.prop {
  header {
    padding: 8px 24px;
    h3 {
      margin: 0;
      line-height: 24px;
      color: #000;
      font-weight: 500;
    }
  }
  .txt {
    position: relative;
  }
  .dltxlL {
    position: absolute;
    top: -16px;
    left: -16px;
    width: 0;
    height: 0;
    border-top: 16px solid #ef7100;
    border-right: 16px solid transparent;
  }
}
</style>
