import { fileURLToPath, URL } from 'node:url'
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'
import Components from 'unplugin-vue-components/vite'
import { AntDesignVueResolver } from 'unplugin-vue-components/resolvers'
import UnoCSS from 'unocss/vite'
import AutoImport from 'unplugin-auto-import/vite'
import { execSync } from 'node:child_process'
import fs from 'node:fs'
import path from 'node:path'
import vueDevTools from 'vite-plugin-vue-devtools'

function injectGitCommitId() {
  return {
    name: 'inject-git-commit-id',
    closeBundle() {
      const distPath = path.resolve(__dirname, 'dist/index.html')
      if (!fs.existsSync(distPath)) return
      let html = fs.readFileSync(distPath, 'utf-8')
      let commitId = ''
      try {
        commitId = execSync('git rev-parse HEAD').toString().trim()
      } catch (e) {
        commitId = 'unknown'
      }
      // 在 </body> 前插入
      html = html.replace(/<\/head>/i, `<!-- COMMIT ID: ${commitId} --></head>`)
      fs.writeFileSync(distPath, html, 'utf-8')
      console.log('[vite] 已将 git commit id 注入 index.html')
    },
  }
}

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    vueJsx(),
    UnoCSS(),
    AutoImport({
      imports: [
        'vue', // 自动导入 Vue 相关的 API
        'vue-router',
        {
          '@/utils/log-plus': ['logPlus'],
        },
      ],
      dts: 'src/auto-imports.d.ts', // 生成自动导入的 TypeScript 声明文件
    }),
    Components({
      resolvers: [
        AntDesignVueResolver({
          importStyle: false, // css in js
        }),
      ],
    }),
    vueDevTools({
      componentInspector: {
        toggleComboKey: 'alt-shift',
      },
    }),
    injectGitCommitId(), // <-- 插件加在最后
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url)),
    },
  },
  server: {
    host: '0.0.0.0',
    port: 5163,
    proxy: {
      '/server': {
        target: 'http://localhost:3001',
        changeOrigin: true,
      },
      '^/api/(project|function|rule)': {
        target: 'http://*************:9081',
        changeOrigin: true,
        configure: (proxy, options) => {
          // 配置此项可在响应头中看到请求的真实地址
          proxy.on('proxyRes', (proxyRes, req) => {
            proxyRes.headers['a-real-url'] =
              new URL(req.url || '', options.target as string)?.href || ''
          })
        },
      },
      '/api/sodataFlink': {
        // target: 'http://**************:28089',
        // target: 'http://*************:5163', // 本地
        // target: 'http://*************:8112', // 李炜嘉本地
        // target: '**************:8112', // 潘东辉本地
        target: 'http://**************:8080', // 测试
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, ''),
        configure: (proxy, options) => {
          // 配置此项可在响应头中看到请求的真实地址
          proxy.on('proxyRes', (proxyRes, req) => {
            proxyRes.headers['a-real-url'] =
              new URL(req.url || '', options.target as string)?.href || ''
          })
        },
      },
      '/api/jupyter': {
        // target: 'http://**************:28089',
        // target: 'http://************:9081', // 测试
        target: 'http://*************:9081', // 测试
        changeOrigin: true,
      },
      '/api/301UC': {
        target: 'http://*************:9087',
        rewrite: (path) => path.replace(/^\/api/, ''),
        configure: (proxy, options) => {
          // 配置此项可在响应头中看到请求的真实地址
          proxy.on('proxyRes', (proxyRes, req) => {
            proxyRes.headers['x-real-url'] =
              new URL(req.url || '', options.target as string)?.href || ''
          })
        },
      },
      '/assetManager': {
        target: 'http://*************:9092',
        changeOrigin: true,
        configure: (proxy, options) => {
          // 配置此项可在响应头中看到请求的真实地址
          proxy.on('proxyRes', (proxyRes, req) => {
            proxyRes.headers['a-real-url'] =
              new URL(req.url || '', options.target as string)?.href || ''
          })
        },
      },
      '^/uip(Admin|Monitor)': {
        target: 'http://*************:8080',
        // target: 'http://*************:8080',
        // secure: false,
        changeOrigin: true,
        // ws: true,
        configure: (proxy, options) => {
          // 配置此项可在响应头中看到请求的真实地址
          proxy.on('proxyRes', (proxyRes, req) => {
            proxyRes.headers['a-real-url'] =
              new URL(req.url || '', options.target as string)?.href || ''
          })
        },
      },
      '/student': {
        target: 'http://**************:8112',
        changeOrigin: true,
      },
      '/v1': {
        // target: 'http://************:7996',
        target: 'http://************:8887',
        changeOrigin: true,
      },
      '/public/v1': {
        target: 'http://************:8887',
        changeOrigin: true,
      },
      '/knowledge/': {
        target: 'http://*************:8086',
        // target: 'http://*************:8112',
        changeOrigin: true,
      },
      '/ajax-api/': {
        target: 'http://************:5000',
        changeOrigin: true,
      },
      '/indexManage': {
        // target: 'http://**************:8112',
        target: 'http://*************:9091',
        changeOrigin: true,
        configure: (proxy, options) => {
          // 配置此项可在响应头中看到请求的真实地址
          proxy.on('proxyRes', (proxyRes, req) => {
            proxyRes.headers['a-real-url'] =
              new URL(req.url || '', options.target as string)?.href || ''
          })
        },
      },
    },
  },
})
