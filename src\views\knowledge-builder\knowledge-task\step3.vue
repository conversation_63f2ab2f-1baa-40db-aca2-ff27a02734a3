<template>
  <div class="step-content">
    <a-form
      layout="horizontal"
      label-align="right"
      class="form-content w-50%"
      :label-col="{ span: 4 }"
      :wrapper-col="{ span: 20 }"
      ref="formRef"
      :model="formState"
      :rule="rules"
    >
      <a-form-item name="llmType" label="抽取模型" required>
        <a-select v-model:value="formState.llmType" :options="modelOptions"> </a-select>
      </a-form-item>

      <a-form-item label="提示词" name="llmPrompt">
        <Textarea
          v-model:value="formState.llmPrompt"
          :auto-size="{ minRows: 3 }"
          :bordered="true"
          :allow-clear="true"
          placeholder="请输入提示词"
          :maxlength="1500"
        />
      </a-form-item>

      <a-form-item label="抽取效果" name="autoSchema">
        <a-checkbox v-model:checked="formState.autoSchema" :disabled="true">
          支持抽取schema外的效果（抽取内容更加全面丰富，但可能需要修改schema）
        </a-checkbox>
      </a-form-item>

      <a-form-item label="导入方式" name="autoWrite">
        <a-checkbox v-model:checked="formState.autoWrite" :disabled="true">
          抽取后无需确认，直接导入
        </a-checkbox>
      </a-form-item>
    </a-form>
  </div>
</template>

<script setup lang="ts">
import { Textarea } from 'ant-design-vue'
import type { TaskItem } from '../type'
import { reqGetModelList } from '@/api/services/knowledge'
const modelOptions = [{ label: 'Default', value: 'Default' }]
const modelList = ref([])
const formRef = ref()

const rules = {
  llmType: [{ required: true, message: '抽取模型不能为空' }],
}

const getModelList = async () => {
  const res = await reqGetModelList({ name: 'LLMType' })
  modelList.value =
    res?.map((item) => ({
      label: item.text,
      value: item.name,
    })) || []
}

onMounted(() => {
  getModelList()
})

const formState = defineModel<TaskItem>('data', { required: true })

const stepSubmit = async () => {
  const res = await formRef.value.validate().catch((error) => {
    console.log('error', error)
  })
  if (res) return true
}

defineExpose({
  stepSubmit,
})
</script>
<style scoped>
.step-content {
  flex: 3 1 0%;
  display: flex;
  flex-direction: column;
  align-items: center;
}
:deep(.ant-form-item-control-input-content) {
  text-align: left;
}
:deep(.ant-form-item-explain-error) {
  text-align: left;
}
</style>
