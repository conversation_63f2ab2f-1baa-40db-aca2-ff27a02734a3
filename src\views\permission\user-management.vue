<template>
  <div class="user-management">
    <cardBox title="用户管理" subTitle="用户信息与权限配置管理">
      <template #headerRight>
        <a-button
          type="primary"
          v-action:addUser
          @click="openAddUserModal('add')"
          style="margin-right: 10px"
          >新增</a-button
        >
        <a-button
          type="primary"
          v-action:deleteUser
          @click="handleDelete"
          :disabled="!selectedRowKeys.length"
          >删除</a-button
        >
      </template>
      <Table
        class="section-table"
        ref="tableRef"
        serial-no
        :columns="columns"
        :get-data="getUserList"
        :search-form-state="formState"
        @reset="cascaderValue = ''"
        :tableConfig="{
          rowSelection,
          rowKey: 'userId',
        }"
      >
        <template #search>
          <a-form-item label="" name="loginAccount">
            <a-input
              v-model:value.trim="formState.loginAccount"
              placeholder="请输入登录账号"
            ></a-input>
          </a-form-item>
          <a-form-item label="" name="userName">
            <a-input v-model:value.trim="formState.userName" placeholder="请输入用户名称"></a-input>
          </a-form-item>
          <a-form-item label="" name="mobile">
            <a-input v-model:value.trim="formState.mobile" placeholder="请输入手机号码"></a-input>
          </a-form-item>
          <a-form-item label="" name="deptId">
            <a-cascader
              v-model:value="cascaderValue"
              :field-names="{ label: 'deptName', value: 'deptId', children: 'deptList' }"
              :options="cascaderOptions"
              expand-trigger="hover"
              change-on-select
              placeholder="请选择组织架构"
            ></a-cascader>
          </a-form-item>
          <a-form-item label="查找范围" name="queryType" v-if="cascaderValue">
            <a-radio-group v-model:value="formState.queryType" placeholder="请选择查找范围">
              <a-radio :value="1">只查本级组织架构</a-radio>
              <a-radio :value="2">查本级及下级组织架构</a-radio>
            </a-radio-group>
          </a-form-item>
        </template>
        <template #bodyCell="{ record, column }">
          <template v-if="column.key === 'action'">
            <a-dropdown>
              <a class="ant-dropdown-link" @click.prevent>
                更多
                <DownOutlined />
              </a>
              <template #overlay>
                <a-menu>
                  <template v-if="record.userStatus === 1">
                    <a-menu-item>
                      <a-Button
                        type="text"
                        style="width: 100%"
                        @click="handleActionClick('disable', record)"
                        v-action:disableUser
                        >禁用</a-Button
                      >
                    </a-menu-item>
                    <a-menu-item>
                      <a-Button
                        type="text"
                        style="width: 100%"
                        @click="handleActionClick('locking', record)"
                        v-action:updateUserStatus
                        >锁定账号</a-Button
                      >
                    </a-menu-item>
                  </template>
                  <template v-else-if="record.userStatus === 2">
                    <a-menu-item>
                      <a-Button
                        type="text"
                        style="width: 100%"
                        @click="handleActionClick('enable', record)"
                        v-action:disableUser
                        >启用</a-Button
                      >
                    </a-menu-item>
                    <a-menu-item>
                      <a-Button
                        type="text"
                        style="width: 100%"
                        @click="handleActionClick('locking', record)"
                        v-action:updateUserStatus
                        >锁定账号</a-Button
                      >
                    </a-menu-item>
                  </template>
                  <template v-else-if="record.userStatus === 3">
                    <a-menu-item>
                      <a-Button
                        type="text"
                        style="width: 100%"
                        @click="handleActionClick('disable', record)"
                        v-action:disableUser
                        >禁用</a-Button
                      >
                    </a-menu-item>
                    <a-menu-item>
                      <a-Button
                        type="text"
                        style="width: 100%"
                        @click="handleActionClick('unlock', record)"
                        v-action:updateUserStatus
                        >解除锁定</a-Button
                      >
                    </a-menu-item>
                  </template>
                  <a-menu-item>
                    <a-Button
                      type="text"
                      style="width: 100%"
                      @click="handleActionClick('edit', record)"
                      v-action:updateUser
                      >编辑</a-Button
                    >
                  </a-menu-item>
                  <a-menu-item>
                    <a-Button
                      type="text"
                      style="width: 100%"
                      @click="handleActionClick('details', record)"
                      v-action:viewUserDetail
                      >详情</a-Button
                    ></a-menu-item
                  >
                  <a-menu-item>
                    <a-Button
                      type="text"
                      style="width: 100%"
                      @click="handleActionClick('resetPassword', record)"
                      v-action:setUserPassword
                      >重置密码</a-Button
                    >
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </template>
        </template>
      </Table>
    </cardBox>

    <!-- 新增用户 -->
    <AddUserModal
      v-if="addUserModalOpen"
      v-model="addUserModalOpen"
      :type="addUserModalType"
      :user-id="currentUserId"
      @submit="tableRef?.getTableData()"
    />
    <!-- 用户详情 -->
    <UserDetailsModal
      v-if="userDetailsModalOpen"
      v-model="userDetailsModalOpen"
      :user-id="currentUserId"
    />
    <!-- 重置密码 -->
    <ResetPasswordModal
      v-if="resetPasswordModalOpen"
      v-model="resetPasswordModalOpen"
      :user-id="currentUserId"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onBeforeMount, watch } from 'vue'
import { Table } from '@fs/fs-components'
import { getUserList, batchDeleteUser, updateUserStatus } from '@/api/services/permission'
import { Modal, message, type CascaderProps, type TableProps } from 'ant-design-vue'
import AddUserModal, { type AddUserModalProps } from '@/components/permission/add-user-modal.vue'
import UserDetailsModal from '@/components/permission/user-details-modal.vue'
import ResetPasswordModal from '@/components/permission/reset-password-modal.vue'
import { DownOutlined } from '@ant-design/icons-vue'
import { getDeptListFn } from '@/components/permission/common'
import cardBox from '@/components/card-box/card-box.vue'

defineOptions({
  name: 'userManagementList',
})

const formState = ref({
  loginAccount: '',
  userName: '',
  mobile: '',
  deptId: '',
  queryType: 1,
})

const cascaderValue = ref()
const cascaderOptions = ref<CascaderProps['options']>([])

const addUserModalOpen = ref(false)
const userDetailsModalOpen = ref(false)
const resetPasswordModalOpen = ref(false)

const addUserModalType = ref<AddUserModalProps['type']>()
const currentUserId = ref('')

const tableRef = ref<InstanceType<typeof Table> | null>(null)
const selectedRowKeys = ref<(string | number)[]>([])

const rowSelection: TableProps['rowSelection'] = {
  onChange: (rowKeys: (string | number)[]) => {
    selectedRowKeys.value = rowKeys
  },
}

watch(
  cascaderValue,
  (val) => {
    formState.value.deptId = val ? val[val.length - 1] : ''
  },
  { immediate: true },
)

const columns = ref([
  {
    title: '登录账号',
    dataIndex: 'loginAccount',
  },
  {
    title: '用户名',
    dataIndex: 'userName',
  },
  {
    title: '手机号',
    dataIndex: 'mobile',
  },
  {
    title: '账号状态',
    dataIndex: 'userStatusName',
  },
  {
    title: '用户类型',
    dataIndex: 'userTypeName',
  },
  {
    title: '创建人',
    dataIndex: 'createUserName',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 150,
  },
  {
    title: '操作',
    key: 'action',
  },
])

onBeforeMount(() => {
  getDeptListFn().then((res) => {
    cascaderOptions.value = res
  })
})

function openAddUserModal(type: AddUserModalProps['type'], userId: string = '') {
  addUserModalOpen.value = true
  addUserModalType.value = type
  currentUserId.value = userId
}

function handleActionClick(type: string, record: any) {
  currentUserId.value = record.userId
  switch (type) {
    case 'enable':
    case 'unlock':
      updateUserStatusFn(record.userId, 1)
      break
    case 'disable':
      updateUserStatusFn(record.userId, 2)
      break
    case 'locking':
      updateUserStatusFn(record.userId, 3)
      break
    case 'edit':
      addUserModalOpen.value = true
      addUserModalType.value = type
      break
    case 'details':
      userDetailsModalOpen.value = true
      break
    case 'resetPassword':
      resetPasswordModalOpen.value = true
      break
    default:
      break
  }
}

/**
 * 删除
 */
function handleDelete() {
  if (!selectedRowKeys.value.length) return
  Modal.confirm({
    title: '确定要删除吗？',
    onOk: async () => {
      try {
        await batchDeleteUser({
          userIdList: selectedRowKeys.value.map((userId: string | number) => ({ userId })),
        })
        message.success('删除成功')
        tableRef.value?.resetForm()
      } catch (error) {
        console.log(error)
      }
    },
  })
}

/**
 * 更新用户状态 - 启用/禁用
 */
async function updateUserStatusFn(userId: string, userStatus: number) {
  try {
    await updateUserStatus({
      userId,
      userStatus,
    })
    tableRef.value?.getTableData()
  } catch (error) {
    console.log(error)
  }
}
</script>

<style scoped lang="less">
.section-table {
  height: 100%;
}
.user-management {
  height: 100%;
  background: white;
}
</style>
<style lang="less">
.ant-dropdown-menu {
  .ant-dropdown-menu-item:hover {
    background-color: transparent !important;
  }
}
.top-title {
  text-align: right;
}
</style>
