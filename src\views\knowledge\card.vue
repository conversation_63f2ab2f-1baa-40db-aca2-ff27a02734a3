<template>
  <a-card
    :title="`${data.displayName}（${data.enName}）`"
    :bordered="true"
    hoverable
    class="w-410px card-wrap"
    style="border-radius: 8px; overflow: hidden"
    :bodyStyle="{ padding: 0 }"
  >
    <img
      src="data:image/png;base64,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"
      alt="demo project image"
      style="width: 100%; height: 96px; object-fit: contain"
    />
    <template #extra>
      <a-popconfirm title="确定要删除吗？" @confirm="handleDel" ok-text="确认" cancel-text="取消">
        <a class="del"><DeleteOutlined /></a>
      </a-popconfirm>
    </template>
    <template #actions>
      <a-button type="text" text="#000a1a78 14px" @click="onBuildKnowledgeBase"
        >知识库构建</a-button
      >
      <a-button type="text" text="#000a1a78 14px" @click="onModifyKnowledgeBase"
        >修改知识库</a-button
      >
      <!-- <a-button type="text" text="#000a1a78 14px" @click="onBuildKnowledgeConfiguration"
        >知识库配置</a-button
      > -->
    </template>
  </a-card>
</template>

<script setup lang="ts">
import type { KnowledgeBase } from './type'
import { useRouter } from 'vue-router'
import { DeleteOutlined } from '@ant-design/icons-vue'

const props = defineProps<{ data: KnowledgeBase }>()
const emits = defineEmits<{
  (e: 'handleModifyKnowledgeBase', data: KnowledgeBase): void
  (e: 'delKnowledge', data: KnowledgeBase): void
}>()
const router = useRouter()
const onBuildKnowledgeBase = () => {
  router.push({ path: `/knowledgeBuilder/knowledgeTask`, query: { projectId: props.data.id } })
}
const onModifyKnowledgeBase = () => {
  emits('handleModifyKnowledgeBase', props.data)
}
const handleDel = async () => {
  return emits('delKnowledge', props.data)
}
</script>

<style lang="less" scoped>
.del {
  color: #f00;
  cursor: pointer;
  display: none;
  &:hover {
    color: #ff4d4f;
  }
}
.card-wrap {
  &:hover .del {
    display: block;
  }
}
</style>
