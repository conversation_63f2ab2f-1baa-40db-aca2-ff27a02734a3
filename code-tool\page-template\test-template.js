import { generateDataFromList, getToLocaleLowerCase } from '../tools/index.js'
import prettier from 'prettier'

/**
 * 生成test测试用例
 * @param {Object} data - 对应test数据
 */
export async function testApiTemplate(data) {
  const interfaceInfos = data?.interfaceInfos
  let content = `import { expect, test } from 'vitest'
import { ${interfaceInfos.map((item) => item.interfaceName)} } from './${getToLocaleLowerCase(data.moduleName)}'

${interfaceInfos.map((item) => testApiItemTemplate(item)).join('\n')}
`
  const formattedCode = await prettier.format(content, { parser: 'babel' })
  data.content = formattedCode
}

/**
 * 生成test测试用例函数
 * @param {Object} item - 对应函数数据
 */
function testApiItemTemplate(item) {
  if (!item?.response) {
    item.response = []
  }
  let content = `test('${item.interfaceName}', async () => {
const { data } = await ${item.interfaceName}(${JSON.stringify(generateDataFromList(item.params))})

${item.response
  .map((res) => {
    if (Array.isArray(res.type)) {
      return getTextMock(res)
    }
    return `expect(data).toHaveProperty('${res.name}')`
  })
  .join('\n')}

${!item?.response || item?.response.length <= 0 ? `expect(data).toBeTruthy()` : ''}
})
    `
  return content
}

function getTextMock(res) {
  if (res.isArray) {
    return `${res.type.map((type) => {
      return `expect(data.${res.name}[0]).toHaveProperty('${type.name}') \n`
    })}
    `
  } else {
    return `${res.type.map((type) => {
      return `expect(data.${res.name}).toHaveProperty('${type.name}') \n`
    })}
    `
  }
}
