<template>
  <div class="result-card" @click="handleClick">
    <!-- 第一行：标题 -->
    <div class="result-title">
      <div class="title-icon">
        <!-- 数据库类型显示对应的 SVG 图标 -->
        <type-icon
          v-if="item.type === SearchType.DATABASE && dbIcon"
          style="margin-right: 10px"
          :type="dbIcon"
        />
        <!-- 其他类型显示字体图标 -->
        <i v-else class="iconfont" :class="getIconClass(item.type)"></i>
      </div>
      <h3 class="title-text" v-html="highlightKeyword(item.name)"></h3>
      <a-tag class="type-tag">
        {{ getTypeLabel(item.type) }}
      </a-tag>
    </div>

    <!-- 第二行：描述 -->
    <div class="result-description" v-if="item.description">
      {{ item.description }}
    </div>

    <!-- 第二行：描述 -->
    <div class="result-description" v-if="item.nameList && item.nameList.length > 0">
      <div class="matches-text">
        <div v-for="(name, index) in item.nameList" :key="index">
          <span> - </span>
          <span v-html="highlightKeyword(name.name)" class="label"></span>:
          <span v-html="highlightKeyword(name.value)" class="value"></span>
        </div>
      </div>
    </div>

    <!-- 第三行：创建者/所有者 -->
    <div class="result-meta">
      <span class="meta-item" v-if="item.owner">
        <i class="iconfont icon-yonghu"></i>
        所有者：{{ item.owner }}
      </span>
    </div>

    <!-- 匹配详情 -->
    <div class="result-match-details">
      <span class="match-detail-item"> 匹配：{{ item.matchCount }} 个 </span>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  SearchType,
  SearchTypeLabels,
  type SearchResultItem,
} from '@/layout/components/global-search/search'
import TypeIcon from '@/views/data-asset/metadata-query/components/type-icon.vue'

// Props 定义
interface Props {
  item: SearchResultItem
  keyword?: string
  dbIcon?: string
}

const props = withDefaults(defineProps<Props>(), {
  keyword: '',
  dbIcon: '',
})

// Emits 定义
const emit = defineEmits<{
  click: [item: SearchResultItem]
}>()

// 获取图标组件
const getIconClass = (type: SearchType): string => {
  const iconMap: Record<string, string> = {
    [SearchType.DATABASE]: 'icon-shujuku',
    [SearchType.TABLE]: 'icon-shujubiaoge',
    [SearchType.ASSET]: 'icon-zichanku',
    [SearchType.ASSET_TABLE]: 'icon-zichanbiaodan',
    [SearchType.METRIC]: 'icon-zhibiao',
    [SearchType.API_LIST]: 'icon-apiliebiao',
  }
  return iconMap[type] || 'icon-shujuku'
}

// 获取类型标签
const getTypeLabel = (type: SearchType): string => {
  return SearchTypeLabels[type] || type
}

// 高亮关键字
const highlightKeyword = (text: string): string => {
  if (!props.keyword || !text) return text

  const regex = new RegExp(`(${props.keyword})`, 'gi')
  return text.replace(regex, '<span class="highlight">$1</span>')
}

// 格式化日期
const formatDate = (date: string): string => {
  if (!date) return '未知'
  return new Date(date).toLocaleDateString('zh-CN')
}

// 处理点击事件
const handleClick = (): void => {
  emit('click', props.item)
}
</script>

<style lang="less" scoped>
.result-card {
  padding: 16px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  margin-bottom: 12px;
  background: #fff;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    border-color: #d9d9d9;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  }

  &:last-child {
    margin-bottom: 0;
  }

  .result-title {
    display: flex;
    align-items: center;
    margin-bottom: 8px;

    .title-icon {
      margin-right: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 16px;
      height: 16px;

      .db-icon {
        width: 24px;
        height: 24px;
        object-fit: contain;
      }
    }

    .title-text {
      flex: 1;
      margin: 0;
      font-size: 16px;
      font-weight: 500;
      color: #262626;
      line-height: 1.4;

      /deep/ .highlight {
        color: #1890ff;
        background: #e6f7ff;
        padding: 1px 2px;
        border-radius: 2px;
        font-weight: 600;
      }
    }

    .type-tag {
      margin-left: 8px;
      font-size: 12px;
      border-radius: 2px;
    }
  }

  .result-description {
    color: #595959;
    font-size: 14px;
    line-height: 1.6;
    margin-bottom: 12px;

    .label {
      display: inline-block;
      min-width: 160px;
    }

    /deep/ .highlight {
      color: #1890ff;
      background: #e6f7ff;
      padding: 1px 2px;
      border-radius: 2px;
      font-weight: 600;
    }
  }

  .result-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    margin-bottom: 8px;

    .meta-item {
      display: flex;
      align-items: center;
      color: #8c8c8c;
      font-size: 12px;

      i {
        margin-right: 4px;
        font-size: 12px;
      }
    }
  }

  .result-matches {
    margin-top: 8px;
    padding-top: 8px;
    border-top: 1px solid #f5f5f5;

    .matches-text {
      color: #8c8c8c;
      font-size: 12px;
      line-height: 1.4;
    }
  }

  .result-match-details {
    margin-top: 4px;

    .match-detail-item {
      color: #8c8c8c;
      font-size: 12px;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .result-card {
    padding: 12px;

    .result-title .title-text {
      font-size: 14px;
    }

    .result-description {
      font-size: 13px;
    }

    .result-meta {
      flex-direction: column;
      gap: 4px;
    }
  }
}
</style>
