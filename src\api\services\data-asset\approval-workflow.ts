/**
 * 审批流相关接口
 */

import service from '@/api'
import axios from 'axios'

export interface WorkflowTypeItem {
  label: string
  value: string
  address: string
}
/* 基础响应类型
 * code 为 000000 表示成功
 */
export interface ServiceResponse<TData> {
  code: string
  data: TData
  error?: { [key: string]: any }
  msg?: string
}
export type WorkflowListResponse = ServiceResponse<WorkflowListData>

export interface WorkflowListData {
  pageIndex: number
  pageSize: number
  records: WorkflowItem[]
  statistics: { [key: string]: any }
  totalPages: number
  totalRecords: number
}

export interface WorkflowItem {
  createdTime?: string
  createUserId?: string
  createUserName?: string
  /** 审批流描述 */
  description?: string
  /** 审批流id */
  id?: string
  /** 审批流名称 */
  name?: string
  /** 审批流节点 */
  nodes?: WorkflowNode[]
  /** 通知地址，需要用户填写 */
  pushResultUrl?: string
  updatedTime?: string
  updateUserId?: string
  updateUserName?: string
  /** 审批流类型： 0-api上线，1-api下线 */
  workflowType?: string
}

export interface WorkflowNode {
  /** 审批人 id */
  approverId?: string
  /** 审批人类型，固定传 1 */
  approverType?: number
  createdTime?: string
  id?: string
  /** 审批人名称 */
  nodeName?: string
  /** 节点顺序 */
  nodeOrder?: number
  /** 节点类型，固定传 2 */
  nodeType?: number
  updatedTime?: string
  workflowId?: number
}

export interface ApprovalItem {
  id: string
  reqId: string
  workflowId: string
  /** 类型：0-api上线，1-api下线 */
  workflowType: string
  pushResultUrl: null
  currentNodeName: string
  businessKey: string
  /** 申请时间 */
  createdTime: string
  createUserId: string
  /** 申请人名称 */
  createUserName: string
  currentApprovalId: string
  currentNodeId: string
  nodeInstances: null
  /** 审批状态：1-进行中，2-已完成，3-已拒绝 */
  status: number
  updatedTime: string
  workflowInstanceName: null
}
export interface ApprovalListResponse {
  pageIndex: number
  pageSize: number
  records: ApprovalItem[]
  statistics: { [key: string]: any }
  totalPages: number
  totalRecords: number
}

export interface DeptUser {
  userId: string
  userName: string
  userStatus: number
  userType: number
  userTypeName: string
}

/**
 * 获取审批流列表
 * @param params 查询参数
 * @returns 审批流列表
 */
export function getApprovalWorkflowList(params: { pageIndex?: number; pageSize?: number }) {
  return axios<WorkflowListResponse>({
    url: `/uipAdmin/workflow/listWorkFlow`,
    method: 'get',
    params,
  })
}

/**
 * 添加审批流
 * @param data 审批流数据
 */
export function addWorkflow(data: WorkflowItem) {
  return service({
    url: `/uipAdmin/workflow/save`,
    method: 'post',
    data,
  })
}

/**
 * 更新审批流
 * @param data 审批流数据
 */
export function updateWorkflow(data: WorkflowItem) {
  return service({
    url: `/uipAdmin/workflow/update`,
    method: 'post',
    data,
  })
}

/**
 * 查询审批流
 * @param id 审批流id
 */
export function queryWorkflowDetail(workFlowId: string) {
  return axios<ServiceResponse<WorkflowItem>>({
    url: `/uipAdmin/workflow/details`,
    method: 'get',
    params: {
      workFlowId,
    },
  })
}

/**
 * 删除审批流
 * @param id 审批流id
 */
export function deleteWorkflow(id: string) {
  return axios({
    url: `/uipAdmin/workflow`,
    method: 'delete',
    params: {
      id,
    },
  })
}
export interface Parameter {
  [prop: string]: any
}
export function getApprovalList(params: Parameter) {
  return service({
    url: `/uipAdmin/workflow/listApproval`,
    method: 'get',
    params,
  })
}

export enum ApproveEnum {
  /** 通过 */
  APPROVE = 1,
  /** 拒绝 */
  REJECT = 2,
}
/**
 * 审批
 * @param data 审批操作
 * @param approvalResult 审批结果
 */
export function approveApproval(data: {
  approvalResult: ApproveEnum
  currentNodeId: string
  workFlowInstanceId: string
}) {
  return service({
    url: `/uipAdmin/workflow/approval`,
    method: 'post',
    data,
  })
}

/**
 * 获取部门列表
 * @returns 部门列表
 */
export function getDeptList() {
  return axios({
    url: `/api/301UC/deptManage/getDeptList`,
    method: 'get',
    customSystemCode: 'fsAuthorityCenter',
    headers: {
      tenantId: 'system',
      Buid: 'system_bu',
    },
  } as any)
}

/**
 * 获取部门子部门列表
 * @param params 查询参数
 * @returns 部门子部门列表
 */
export function getDeptChildList(params: { deptId: string; tenantId: string }) {
  return axios({
    url: `/api/301UC/deptManage/getChildDeptList`,
    method: 'get',
    params,
    customSystemCode: 'fsAuthorityCenter',
    headers: {
      tenantId: params.tenantId,
    },
  } as any)
}

/**
 * 基础响应列表类型
 */
export interface ServiceResponseList<TData> {
  code: string
  data: {
    totalRecords: number
    pageIndex: number
    records: TData[]
    totalPages: number
    pageSize: number
  }
  error?: { [key: string]: any }
  msg?: string
}

/**
 * 获取部门用户列表
 * @param params.deptId 部门id
 * @param params.pageIndex 页码
 * @param params.pageSize 每页条数
 * @param params.queryType 查询类型, 1-本级, 2-本级和下级
 * @returns 部门用户列表
 */
export function getDeptUserList(params: {
  deptId: string
  pageIndex: number
  pageSize: number
  queryType: number
  tenantId: string
}) {
  return axios<ServiceResponseList<DeptUser>>({
    url: `/api/301UC/userManage/getUserList`,
    method: 'get',
    params,
    customSystemCode: 'fsAuthorityCenter',
    headers: {
      tenantId: params.tenantId,
    },
  } as any)
}

/**
 * 获取审批流类型列表
 */
export async function getApprovalWorkflowTypeList(): Promise<ServiceResponse<WorkflowTypeItem[]>> {
  const res = await service({
    url: `/uipAdmin/workflow/config`,
    method: 'get',
    params: {
      configType: 1,
    },
  })
  return {
    code: res.code,
    data: res.data.map((item: any) => ({
      label: item.configName,
      value: item.configKey,
      address: item.configValue,
    })),
    msg: res.msg,
  }
}
