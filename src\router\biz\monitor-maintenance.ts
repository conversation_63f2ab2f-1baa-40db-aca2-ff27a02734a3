import type { RouteRecordRaw } from 'vue-router'

/**
 * 监控与日志
 */
const routes: RouteRecordRaw[] = [
  {
    path: '/monitorMaintenance/flinkMonitor',
    name: 'flinkMonitor',
    component: () => import('@/views/monitor-maintenance/flink-monitor/index.vue'),
    meta: {
      title: 'FLINK监控',
    },
  },
  {
    path: '/monitorMaintenance/sparkMonitor',
    name: 'sparkMonitor',
    component: () => import('@/views/monitor-maintenance/spark-monitor/index.vue'),
    meta: {
      title: 'SPARK监控',
    },
  },
  {
    path: '/monitorMaintenance/serverMonitor',
    name: 'serverMonitor',
    component: () => import('@/views/monitor-maintenance/server-monitor/index.vue'),
    meta: {
      title: '服务器监控',
    },
  },
  {
    path: '/dataCheck/realTimeTaskControl',
    name: 'realTimeTaskControl',
    component: () => import('@/views/monitor-maintenance/data-check/realtime-task-control.vue'),
    meta: {
      title: '实时表监控',
      keepAlive: false,
      permission: true,
    },
  },
  {
    path: '/realTimeTableManage',
    name: 'realTimeTableManage',
    component: () => import('@/views/monitor-maintenance/data-check/realtime-table-manage.vue'),
    meta: {
      title: '实时表管理',
      keepAlive: false,
      permission: true,
    },
  },
  {
    path: '/monitorMaintenance/processMonitor',
    name: 'processMonitor',
    component: () => import('@/views/monitor-maintenance/process-monitor/index.vue'),
    meta: {
      title: '进程监控',
      // keepAlive: true,
      // permission: true,
    },
  },
  {
    path: '/dataCheck/realTimeTableQuery',
    name: 'realTimeTableQuery',
    component: () => import('@/views/monitor-maintenance/data-check/realtime-table-query.vue'),
    meta: {
      // title: (route: any) =>
      //   `实时表监控${
      //     route.query.id ? '-' + route.query.id : ''
      //   }`,
      title: '实时表监控',
      keepAlive: true,
      permission: true,
    },
  },
]
export default routes
