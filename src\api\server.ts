import { Request } from '@fs/fs-components'
import { useUserStore } from '@/stores/user'
import { SYSTEM_CODE } from './base'
import { isDevMock } from '@/utils/index'
import { COOKIE_CONFIG, CookieManager } from '@/utils/cookie'
import { logoutFn } from '@/utils/user'

const serviceAxios = new Request({
  handleRequestInterceptor,
  handleUnauthorized,
}).service

/**
 * 请求拦截
 */
function handleRequestInterceptor(config: any) {
  config.headers.Systemcode = SYSTEM_CODE
  const userStore = useUserStore()
  let token = userStore.token
  if (!token) {
    token = CookieManager.get(COOKIE_CONFIG.TOKEN_KEY) || ''
    // 如果Cookie中有token，同步到store
    if (token) {
      userStore.setToken(token)
    }
  }

  const { userId, buId, tenantId, userName } = userStore.userInfo
  if (token) {
    config.headers = {
      Authorization: token,
      Opuser: userId,
      opUserName: encodeURIComponent(userName),
      Buid: buId,
      TenantId: tenantId,
      ...config.headers,
    }
  }
  // 如果禁用登录，定制 header 的字段
  if (import.meta.env.VITE_APP_NEED_AUTH === 'false') {
    config.headers = {
      Opuser: userId,
      opUserName: encodeURIComponent(userName),
      Authorization: token,
      Systemcode: 'new-bigData',
      Buid: buId,
      TenantId: tenantId,
    }
  }
  return config
}

/**
 * 未授权回调
 */
function handleUnauthorized() {
  logoutFn()
}

function service(data: any, mock?: any): any {
  if (mock && isDevMock()) {
    return Promise.resolve(mock)
  }
  return serviceAxios(data)
}

export default service
