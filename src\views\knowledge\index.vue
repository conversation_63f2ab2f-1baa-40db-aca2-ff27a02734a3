<template>
  <div class="home-container">
    <a-flex justify="space-between">
      <div class="title mb-20px" text="20px #000000d9">知识库列表</div>
      <Space size="small" direction="horizontal" align="start">
        <!-- <Select v-model:value="sort" :options="sequenceOptions" /> -->
        <InputSearch
          placeholder="输入项目名称"
          :allow-clear="true"
          :enter-button="false"
          :loading="loading"
          @change="handleInputChange"
          @search="onSearch"
        />
        <!-- <a-button type="default" shape="default" size="middle" @click="goToGC"> 全局配置 </a-button> -->
        <a-button type="primary" shape="default" size="middle" @click="goModelService">
          模型服务
        </a-button>
      </Space>
    </a-flex>
    <a-spin :indicator="indicator" :spinning="loading">
      <Flex wrap="wrap" :gap="20">
        <AddCardBtn @click="createBase" />
        <Card
          v-for="(item, index) in dataSource"
          :key="index"
          :data="item"
          @handleModifyKnowledgeBase="handleModifyKnowledgeBase"
          @delKnowledge="delKnowledge"
        />
      </Flex>
    </a-spin>
    <Modal
      v-model:open="showEditor"
      :title="`${isEdit ? '编辑' : '新建'}知识库`"
      :width="600"
      :footer="null"
      destroyOnClose
    >
      <CreateBase
        :onSave="addKnowledgeBase"
        :onCancel="closeEditor"
        :initFormState="initFormState"
        :createLoading="createLoading"
      />
    </Modal>
  </div>
</template>

<script setup lang="ts">
import { InputSearch, Select, Space, Flex, Modal, message } from 'ant-design-vue'
import Card from './card.vue'
import AddCardBtn from './add-card-btn.vue'
import CreateBase from './create-base.vue'
import type { KnowledgeBase } from './type'
import { useStorage } from '@vueuse/core'
import { getKnowledgelist } from '@/api/mock'
import type { KnowledgeBaseConfig } from '@/api/type.ts'
import {
  reqCreateKnowledge,
  reqDeleteKnowledge,
  reqEditKnowledge,
  reqGetKnowledgeList,
  reqLogin,
} from '@/api/services/knowledge'
import { TEMPCOOKIE } from '@/api/base'
import { useRouter } from 'vue-router'
import { LoadingOutlined } from '@ant-design/icons-vue'
import { h } from 'vue'

defineOptions({
  name: 'knowledge-home',
})

const router = useRouter()
const sort = ref<'0' | '1'>('1')
const sequenceOptions = [
  { label: '按时间从旧到新', value: '0' },
  { label: '按时间从新到旧', value: '1' },
]
const loading = ref(false)
const dataSource = ref<KnowledgeBase[]>([])
const showEditor = ref(false)
const createLoading = ref(false)
const getDataList = async (displayName: string | undefined = undefined) => {
  loading.value = true
  const res = await reqGetKnowledgeList({ queryType: sort.value, displayName }).finally(() => {
    loading.value = false
  })
  dataSource.value = res.records
}
const isEdit = ref(false)
const initFormState = ref<KnowledgeBase>({
  displayName: '',
  enName: '',
  desc: '',
  id: '',
  info: '',
})

const indicator = h(LoadingOutlined, {
  style: {
    fontSize: '24px',
  },
  spin: true,
})

watch(
  () => sort.value,
  (val) => {
    getDataList()
  },
)

onMounted(async () => {
  // const res = await reqLogin({})
  await getDataList()
})
// 搜索
const onSearch = (value: string) => {
  getDataList(value)
}
const createBase = () => {
  // router.push('/home/<USER>')
  showEditor.value = true
}
const closeEditor = () => {
  showEditor.value = false
  isEdit.value = false
  initFormState.value = {
    displayName: '',
    enName: '',
    desc: '',
    id: '',
    info: '',
  }
}
const handleInputChange = (e: Event) => {
  const input = e.target as HTMLInputElement
  if (input.value === '') {
    getDataList()
  }
}

function toCamelCase(str: string) {
  return str
    .replace(/[-_ ]+(\w)/g, (_, c) => (c ? c.toUpperCase() : ''))
    .replace(/^\w/, (c) => c.toLowerCase())
}

const addKnowledgeBase = async (formData: KnowledgeBase) => {
  createLoading.value = true

  if (isEdit.value) {
    const data = {
      id: initFormState.value.id,
      displayName: formData.displayName,
      desc: formData.desc,
      info: initFormState.value.info,
    }
    await reqEditKnowledge(data).finally(() => {
      createLoading.value = false
    })
  } else {
    const data = {
      ...formData,
      neo4jDatabaseName: toCamelCase(formData.enName),
    }
    await reqCreateKnowledge(data).finally(() => {
      createLoading.value = false
    })
  }
  message.success(`${isEdit.value ? '编辑' : '新建'}成功`)
  closeEditor()
  getDataList()
}

const handleModifyKnowledgeBase = async (data: KnowledgeBase) => {
  isEdit.value = true
  initFormState.value = {
    displayName: data.displayName,
    enName: data.enName,
    desc: data.desc,
    id: data.id,
    info: data.info,
  }
  createBase()
}

const delKnowledge = async (data: KnowledgeBase) => {
  await reqDeleteKnowledge({ id: data.id })
  message.success('删除成功')
  getDataList()
  return Promise.resolve(true)
}

const goModelService = () => {
  router.push('/model-service')
}
</script>

<style scoped>
.home-container {
  height: 100%;
  background-color: #fff;
}
</style>
