import { h, nextTick } from 'vue'
import { Modal, message } from 'ant-design-vue'
import Prism from 'prismjs'
import { CopyOutlined, DownloadOutlined } from '@ant-design/icons-vue'
import { VAceEditor } from 'vue3-ace-editor'
import 'ace-builds/src-noconflict/mode-java' // 导入文本模式
import 'ace-builds/src-noconflict/mode-json' // 导入文本模式
import 'ace-builds/src-noconflict/theme-chrome' // 导入github主题

interface optionsType {
  type: string
}

export function getModalData(str: any) {
  if (typeof str === 'object') {
    return JSON.stringify(str, null, 4)
  } else if (isJSON(str)) {
    return JSON.stringify(JSON.parse(str), null, 4)
  } else {
    return str
  }
}

function isJSON(str: any) {
  try {
    const obj = JSON.parse(str)
    return typeof obj === 'object' && obj !== null
  } catch (e) {
    return false
  }
}

export function logModal(
  data: any,
  title?: string,
  showCopy?: boolean,
  width?: number,
  callback?: any,
  options?: optionsType,
) {
  const content = getModalData(data)
  const maxHeight = document.body.offsetHeight * 0.7 + 'px'

  const style = {
    height: '100%',
    position: 'relative',
    maxHeight,
    overflow: 'auto',
  }

  const handleCopy = () => {
    const textArea = document.createElement('textarea')
    textArea.value = content
    document.body.appendChild(textArea)
    textArea.select()
    document.execCommand('copy')
    textArea.remove()
    message.success('复制成功')
  }

  const handleDownload = () => {
    if (!content) {
      message.warning('数据为空')
      return
    }
    const blob = new Blob([content], { type: 'text/json' })
    const a = document.createElement('a')
    a.download = 'file.json'
    a.href = window.URL.createObjectURL(blob)
    a.click()
  }

  const renderIcons = () => {
    const icons = []
    if (showCopy) {
      icons.push(
        h(CopyOutlined, {
          title: 'Copy',
          style: {
            fontSize: '24px',
            color: '#1890FF',
            cursor: 'pointer',
            position: 'absolute',
            right: '10px',
            top: '10px',
            zIndex: 10,
          },
          onClick: handleCopy,
        }),
      )
    }
    if (isJSON(content)) {
      icons.push(
        h(DownloadOutlined, {
          style: {
            fontSize: '24px',
            color: '#1890FF',
            cursor: 'pointer',
            position: 'absolute',
            right: '10px',
            top: '50px',
            zIndex: 10,
          },
          onClick: handleDownload,
        }),
      )
    }
    return icons
  }

  const contentNode = h('div', { style }, [
    h('div', { class: 'overflow-modal', style: { paddingRight: '40px' } }, [
      ...renderIcons(),
      h(VAceEditor, {
        value: content,
        lang: options && options?.type ? options?.type : 'java',
        theme: 'chrome',
        style: {
          height: '500px',
          width: '100%',
        },
      }),
    ]),
  ])

  Modal.info({
    title,
    width: `${width}%`,
    maskClosable: true,
    okText: 'OK',
    content: () => contentNode,
    onOk: () => {
      callback?.()
    },
  })

  nextTick(() => {
    Prism.highlightAll()
  })
}
