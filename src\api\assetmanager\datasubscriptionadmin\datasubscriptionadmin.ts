import service from '@/api'
import { MOCK_DATA } from './datasubscriptionadmin-mock'

/**
 *  - DSA001-新增数据订阅服务
 * @param {string} serverName - 服务名称
 * @param {string} apiHost - 订阅者服务地址
 * @param {string} apiPath - 订阅者服务路径
 * @param {string} apiHeaders - 订阅者API头参参数配置
 * @param {string} apiType - 订阅者API类型HTTP,EMAIL
 * @param {string} recEmail - 收件人邮箱
 * @param {string} host - 服务器host
 * @param {string} fromAddress - 发送地址
 * @param {string} userName - 发送邮箱
 * @param {string} password - 邮箱密码
 * @param {string} opUser -
 * @param {string} opUserName -
 * @returns {any} - 返回一个解析为响应数据的Promise
 */

export async function addDataSubscripServer(
  data: {
    serverName: string
    apiHost?: string
    apiPath?: string
    apiHeaders?: string
    apiType: string
    recEmail?: string
    host?: string
    fromAddress?: string
    userName?: string
    password?: string
  },
  headers?: {
    opUser?: string
    opUserName?: string
  },
): Promise<any> {
  return service(
    {
      method: 'POST',
      url: '/assetManager/dataSubscriptionAdmin/addDataSubscripServer',
      data,
      headers,
    },
    MOCK_DATA.addDataSubscripServer,
  )
}

/**
 *  - DSA002-修改数据订阅服务
 * @param {string} serverId - 主键
 * @param {string} serverName - 服务名称
 * @param {string} apiHost - 订阅者服务地址
 * @param {string} apiPath - 订阅者服务路径
 * @param {string} apiHeaders - 订阅者API头参参数配置
 * @param {string} apiType - 订阅者API类型HTTP,KAFKA
 * @param {string} recEmail - 收件人邮箱
 * @param {string} host - 服务器host
 * @param {string} fromAddress - 发送地址
 * @param {string} userName - 发送邮箱
 * @param {string} password - 邮箱密码
 * @param {string} opUser -
 * @param {string} opUserName -
 * @returns {any} - 返回一个解析为响应数据的Promise
 */

export async function updateDataSubscripServer(
  data: {
    serverId: string
    serverName?: string
    apiHost?: string
    apiPath?: string
    apiHeaders?: string
    apiType: string
    recEmail?: string
    host?: string
    fromAddress?: string
    userName?: string
    password?: string
  },
  headers?: {
    opUser?: string
    opUserName?: string
  },
): Promise<any> {
  return service(
    {
      method: 'POST',
      url: '/assetManager/dataSubscriptionAdmin/updateDataSubscripServer',
      data,
      headers,
    },
    MOCK_DATA.updateDataSubscripServer,
  )
}

/**
 *  - DSA003-删除数据订阅服务
 * @param {boolean} serverId - 主键
 * @param {string} opUser -
 * @param {string} opUserName -
 * @returns {any} - 返回一个解析为响应数据的Promise
 */

export async function delDataSubscripServer(
  data: {
    serverId: string
  },
  headers?: {
    opUser?: string
    opUserName?: string
  },
): Promise<any> {
  return service(
    {
      method: 'POST',
      url: '/assetManager/dataSubscriptionAdmin/delDataSubscripServer',
      data,
      headers,
    },
    MOCK_DATA.delDataSubscripServer,
  )
}

/**
 *  - DLM004-分页查询数据订阅服务
 * @param {number} pageIndex - undefined
 * @param {number} pageSize - undefined
 * @param {string} serverName - 服务名称
 * @param {string} apiHost - 订阅者服务地址
 * @param {string} apiPath - 订阅者服务路径
 * @param {string} apiType - 订阅者API类型HTTP
 * @param {string} opUser - undefined
 * @param {string} opUserName - undefined
 * @returns {any} - 返回一个解析为响应数据的Promise
 */

export async function getPageDataSubscripServer(
  data: {
    pageIndex: number
    pageSize: number
    serverName?: string
    apiHost?: string
    apiPath?: string
    apiType?: string
  },
  headers?: {
    opUser?: string
    opUserName?: string
  },
): Promise<{
  code: string
  msg: string
  data: Array<{
    serverId: string
    serverName: string
    apiHost: string
    apiPath: string
    apiHeaders: string
    apiType: string
    createTime: string
    createUser: string
    updateTime: string
    updateUser: string
  }>
}> {
  return service(
    {
      method: 'GET',
      url: '/assetManager/dataSubscriptionAdmin/getPageDataSubscripServer',
      params: data,
      headers,
    },
    MOCK_DATA.getPageDataSubscripServer,
  )
}

/**
 *  - DLM005-查询数据订阅服务
 * @param {string} serverName - 服务名称
 * @param {string} opUser - undefined
 * @param {string} opUserName - undefined
 * @returns {any} - 返回一个解析为响应数据的Promise
 */

export async function getDataSubscripServer(
  data: {
    serverName?: string
  },
  headers?: {
    opUser?: string
    opUserName?: string
  },
): Promise<{
  code: string
  msg: string
  data: Array<{
    serverId: string
    serverName: string
    apiHost: string
    apiPath: string
    apiHeaders: string
    apiType: string
    createTime: string
    createUser: string
    updateTime: string
    updateUser: string
  }>
}> {
  return service(
    {
      method: 'GET',
      url: '/assetManager/dataSubscriptionAdmin/getDataSubscripServer',
      params: data,
      headers,
    },
    MOCK_DATA.getDataSubscripServer,
  )
}
