<template>
  <div class="tebant-select">
    <cardBox title="租户管理" subTitle="租户服务开通与配置管理">
      <template #headerRight>
        <a-button type="primary" v-action:add @click="addTable">新增</a-button>
      </template>

      <Table
        :columns="columns"
        :getData="getTenantList"
        ref="tableRef"
        :searchFormState="formState"
      >
        <template #search>
          <a-form-item label="" name="租户名称" :rules="[{ message: '请输入租户名称' }]">
            <a-input v-model:value="formState.tenantName" placeholder="请输入租户名称" />
          </a-form-item>
          <a-form-item label="">
            <a-select v-model:value="formState.isUse" placeholder="请选择是否启用" allow-clear>
              <a-select-option value="1">是</a-select-option>
              <a-select-option value="0">否</a-select-option>
            </a-select>
          </a-form-item>
        </template>
      </Table>
    </cardBox>
    <tebant-modal
      :open="modalOpen"
      :disabled="disabled"
      @modalCancel="modalCancel"
      @modalHandleOk="modalHandleOk"
      ref="modalRef"
    ></tebant-modal>
    <tebant-details-modal ref="detailsModalRef"></tebant-details-modal>
  </div>
</template>
<script setup lang="ts">
import { Table } from '@fs/fs-components'
import { ref, onBeforeMount, h, withDirectives, resolveDirective } from 'vue'
import { TebantDTO } from '@/utils/column'
import { Button } from 'ant-design-vue/es/components'
import { getTenantList } from '@/api/services/permission'
import type { Column } from '@fs/fs-components/src/components/table/type'
import cardBox from '@/components/card-box/card-box.vue'

defineOptions({
  name: 'TebantSelect',
})

let columns = ref<Column[]>([])
const tableRef = ref<InstanceType<typeof Table> | null>(null)
const modalRef = ref<any | null>(null)
const detailsModalRef = ref<any | null>(null)
const modalOpen = ref<boolean>(false)
const disabled = ref<boolean>(true)
const formState: Record<string, any> = ref({
  tenantName: '',
  isUse: null,
})

function modalHandleOk() {
  tableRef.value?.getTableData()
  modalCancel()
}

function modalCancel() {
  modalOpen.value = false
}
import { useRoute } from 'vue-router'
const route = useRoute()
onBeforeMount(() => {
  initColumns()
})

function addTable() {
  modalOpen.value = true
  disabled.value = false
  modalRef.value?.show('add')
}

async function initColumns() {
  columns.value = TebantDTO.map((item) => {
    return {
      ...item,
      dataIndex: item.key,
    }
  })
  columns.value.push({
    key: 'action',
    title: '操作',
    customRender(data: Record<string, any>) {
      return [
        withDirectives(
          h(
            Button,
            {
              type: 'link',
              onClick() {
                modalOpen.value = true
                disabled.value = true
                modalRef.value?.show('edit', data.record)
              },
            },
            {
              default: () => '编辑',
            },
          ),
          [[resolveDirective('action'), '', 'edit']],
        ),
        withDirectives(
          h(
            Button,
            {
              type: 'link',
              onClick() {
                detailsModalRef.value?.show('details', data.record)
              },
              vDirectives: [
                {
                  name: 'action',
                  value: 'details',
                },
              ],
            },
            {
              default: () => '详情',
            },
          ),
          [[resolveDirective('action'), '', 'details']],
        ),
      ]
    },
  })
}
</script>
<style lang="less">
.tebant-select {
  height: 100%;
  background: white;
}
</style>
