.nav-page-title {
  display: flex;
  align-content: center;
  justify-content: space-between;
  width: 100%;
  background: #FFFFFF;
  // box-shadow: @shadow-1;
  // border-radius: 12px;
  padding: 12px 24px;


  .title-left {
    > p {
      font-size: 20px;
      font-weight: bold;
      color: #141414;
      word-break: break-all;
      margin-bottom: 0;
    }
    .desc {
      font-size: 14px;
      color: #999;
      margin-top: 5px;
      word-break: break-all;
    }
  }

  .go-back {
    font-size: 14px;
    margin-right: 36px;
    cursor: pointer;
    display: flex;
    align-items: center;

    i {
      margin-right: 8px;
    }
  }

  .list-top-left {
    flex: 1;
    margin: 0 10px;

    .ant-btn-group {
      button {
        width: 58px;
      }
    }

    .active-list {
      color: #1890FF;
    }
  }
}

.input-search-cls {
  align-self: flex-start;
  margin-left: 20px !important;
  margin-bottom: 8px !important;
}

.page-main {
  overflow: hidden !important;
}

.list-card {
  width: 100%;
  background: #FFFFFF;
  box-shadow: 0 8px 15px 0 rgba(149, 156, 182, 0.15);
  border-radius: 4px;
  border: 1px solid #EAEBF0;
  padding: 10px 18%;
  display: flex;
  align-content: center;
  justify-content: space-between;

  &-left {
    display: flex;
    align-items: center;

    > p {
      height: 37px;
      font-size: 26px;
      color: rgba(0, 0, 0, 0.85);
      line-height: 37px;

      > span {
        line-height: 37px;
        margin-left: 6px;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.45);
      }
    }
  }

  &-right {
    width: 54px;
    height: 54px;
    border-radius: 50%;
    overflow: hidden;
    border: 3px solid transparent;


    .icon-container {
      width: 100%;
      height: 100%;
      background-color: #FFA225;
      text-align: center;
      line-height: 54px;

      i {
        font-size: 26px;
        color: #fff;
        text-align: center;
        vertical-align: sub;
      }
    }
  }
}

.card-container {
  margin-bottom: 10px;
}

.query-container {
  width: 100%;
  background: #FFFFFF;
  // box-shadow: 0px 20px 27px rgba(0, 0, 0, 0.05);
  border-radius: 12px;
  margin: 10px 0;
  margin-bottom: 20px !important;
  background: white;

  .ant-row {
    .ant-form-item {
      display: flex;
      margin-bottom: 0px !important;
      .ant-form-item-label {
        display: none;
      }
      .ant-input {
        margin-left: 0;
      }
      .ant-form-item-control-wrapper {
        flex: 1;
      }
    }
  }
}

.list-mid {
  border: 1px solid #BAE7FF;
  background: #E6F7FF;
  padding: 10px;
  margin-bottom: 24px;

  .ant-row {
    display: flex;
    justify-content: space-between;

    .list-mid-left {
      display: flex;
      justify-content: flex-start;
      align-items: center;

      i {
        color: rgba(24, 144, 255, 1);
        margin-right: 6px;
      }
    }

    .list-mid-right {
      color: rgba(24, 144, 255, 1);

      span {
        cursor: pointer;
      }
    }

    &:after {
      content: none;
    }

    &:before {
      content: none;
    }
  }
}

.ant-table-pagination {
  float: none;
  display: flex;
  justify-content: center;
  margin: 10px 0 0;
}

.ant-table {
  .ant-table-thead > tr > th, .ant-table-tbody > tr > td {
    padding: 12px 16px;

    a {
      display: block;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }

  .table-expand {
    display: flex;
    justify-content: flex-start;

    > p {
      margin-left: 40px;
    }
  }
}

//卡片分页
// .card-pagination {
//   position: fixed;
//   bottom: 0;
//   left: 0;
//   right: 0;
//   width: 100%;
//   height: 48px;
//   background: white;

//   .ant-pagination {
//     margin: 5px auto;
//     display: flex;
//     align-items: center;
//     justify-content: center;

//     .ant-pagination-options-quick-jumper {
//       height: 100%;
//     }
//   }
// }
