<template>
  <a-modal
    v-model:open="open"
    :title="title"
    width="500px"
    :mask-closable="false"
    :confirm-loading="loading"
    @cancel="open = false"
    @ok="onSubmit"
  >
    <a-form
      ref="formRef"
      name="form"
      class="form"
      :model="formState"
      :rules="rules"
      layout="vertical"
    >
      <!-- <a-form-item name="oldPassword" label="旧密码">
        <a-input-password
          v-model:value.trim="formState.oldPassword"
          placeholder="请输入旧密码"
        ></a-input-password>
      </a-form-item> -->
      <a-form-item name="newPassword" label="新密码" :extra="extra">
        <a-input-password
          v-model:value.trim="formState.newPassword"
          placeholder="请输入新密码"
        ></a-input-password>
      </a-form-item>
      <a-form-item name="confirmPassword" label="确认密码">
        <a-input-password
          v-model:value.trim="formState.confirmPassword"
          placeholder="请再次输入新密码"
        ></a-input-password>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue'
import type { Rule } from 'ant-design-vue/es/form'
import { message } from 'ant-design-vue'
import {
  adminResetUserPassword,
  setUserPassword,
  viewSystemParamsList,
} from '@/api/services/permission'

const open = defineModel<boolean>({ default: false, required: true })
const props = withDefaults(defineProps<{ userId: string; title?: string }>(), {
  title: '重置密码',
})
const emits = defineEmits(['submit'])

interface FormState {
  newPassword: string
  confirmPassword: string
}

const formState = ref<FormState>({
  newPassword: '',
  confirmPassword: '',
})

const formRef = ref()
const min = ref(8)
const max = ref(32)
const special = ref('')
const loading = ref(false)
const regexRules = ref([])
const rules = ref()
const extra = ref(
  `新密码必须包含字母、数字和特殊字符(${special.value})，长度 ${min.value} 到${max.value} 字符之间`,
)
onMounted(() => {
  viewSystemParamsList({ paramRemark: '密码', pageSize: 30, pageIndex: 1 }).then((res: any) => {
    res?.data?.records.map((item: { paramId: string; paramValue: string | number }) => {
      if (item.paramId === 'password.min.length') {
        min.value = Number(item.paramValue)
      } else if (item.paramId === 'password.max.length') {
        max.value = Number(item.paramValue)
      } else if (item.paramId === 'password.special.characters') {
        special.value = item.paramValue as string
      }
      extra.value = `新密码必须包含字母、数字和特殊字符(${special.value})，长度 ${min.value} 到${max.value} 字符之间`
      rules.value = getPasswordRules()
    })
  })
})

const validatePass2 = async (_rule: Rule, value: string) => {
  if (value === '') {
    return Promise.reject('请再次输入新密码')
  } else if (value !== formState.value.newPassword) {
    return Promise.reject('密码不匹配！')
  } else {
    return Promise.resolve()
  }
}

/**
 * 表单校验规则
 */
const getPasswordRules = () => {
  return {
    newPassword: [
      { required: true, message: '请输入新密码', trigger: 'change' },
      {
        pattern: new RegExp(
          `^(?=.*[0-9])(?=.*[a-zA-Z${special.value}])[0-9a-zA-Z${special.value}]{${min.value},${max.value}}$`,
        ),
        message: `密码必须包含字母、数字、特殊字符(${special.value})，长度 ${min.value} 到${max.value} 字符之间`,
        trigger: 'change',
      },
    ],
    confirmPassword: [{ required: true, validator: validatePass2, trigger: 'change' }],
  }
}

/**
 * 提交
 */
async function onSubmit() {
  console.log(
    `^(?=.*[0-9])(?=.*[a-zA-Z${special.value}])[0-9a-zA-Z${special.value}]{${min.value},${max.value}}$`,
  )
  formRef.value
    .validate()
    .then(() => {
      const validationResult = validatePassword(formState.value.confirmPassword, regexRules.value)
      if (validationResult?.isValid) {
        setUserPasswordFn()
      } else {
        message.warning(validationResult?.message)
      }
    })
    .catch((error: any) => {
      console.log('error', error)
    })
}

function validatePassword(password: string, regexRules: string | any[]) {
  for (var i = 0; i < regexRules.length; i++) {
    var rule = regexRules[i]
    if (rule.enable) {
      var regex = new RegExp(rule.pattern)
      if (rule.match && !regex.test(password)) {
        return { isValid: false, message: rule.msg }
      } else if (!rule.match && regex.test(password)) {
        return { isValid: false, message: rule.msg }
      }
    }
  }
  // 如果通过所有规则，则密码有效
  return { isValid: true, message: '密码符合规则' }
}

/**
 * 重置密码
 */
async function setUserPasswordFn() {
  try {
    loading.value = true
    const { newPassword } = formState.value
    await adminResetUserPassword({
      password: btoa(newPassword),
      userId: props.userId,
    })
    message.success('操作成功')
    emits('submit')
  } catch (error) {
    console.log(error)
  } finally {
    loading.value = false
    open.value = false
  }
}
</script>

<style scoped lang="less">
.form {
  margin-top: 30px;
}
</style>
