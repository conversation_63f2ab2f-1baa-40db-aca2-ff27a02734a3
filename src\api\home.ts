import service from '@/api'
import { SODATAFLINK } from '@/api/base'

/**
 * 获取作业流统计
 * @param params
 */
export function getTotalDataStatistics(params: any): any {
  return service({
    url: `${SODATAFLINK}/new2dataplatform/indexpage/scheflowdefList`,
    method: 'get',
    params,
  })
}

/**
 * 获取作业流实例统计
 * @param params
 */
export function getJobFlowInstanceStatistics(params: any) {
  return service({
    url: `${SODATAFLINK}/new2dataplatform/indexpage/scheflowinstanceList`,
    method: 'get',
    params,
  })
}

/**
 * 获取首页统计
 * @param params
 */
export function getHomePageRealTime(params: any) {
  return service({
    url: `${SODATAFLINK}/homePage/countRealTimeAndNoRealTime`,
    method: 'post',
    data: params,
  })
}

/**
 * 获取首页实时数据
 * @param params
 */
export function getjudgeInfo(params: any) {
  return service({
    url: `${SODATAFLINK}/homePage/judgeflinkAndsparkisAlive`,
    method: 'post',
    params,
  })
}

/**
 * 服务启停
 * @param params
 */
export function setProcessMonitor(params: any) {
  return service({
    url: `${SODATAFLINK}/new2dataplatform/serviceMonitor/health`,
    method: 'post',
    data: params,
  })
}

/**
 * 获取首页组件统计数据
 * @param params
 */
export function getComponentStatistics(params: any) {
  return service({
    url: `${SODATAFLINK}/new2dataplatform/indexpage/moudletotal`,
    method: 'get',
    params,
  })
}

/**
 * 获取正在运行的作业流
 * @param params
 */
export function getRuningJobFlow(params: any) {
  return service({
    url: `${SODATAFLINK}/new2dataplatform/indexpage/getRuningFlows`,
    method: 'get',
    params,
  })
}
