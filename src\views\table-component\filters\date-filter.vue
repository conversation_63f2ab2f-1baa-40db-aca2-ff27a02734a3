<template>
  <div class="date-filter">
    <div class="fc-gap">
      <div v-for="item in options[0]" :key="item.value" class="option" @click="onClickOption(item)">
        {{ item.label }}
      </div>
    </div>

    <!-- 最近30天和上个月之间的分隔线 -->
    <a-divider />
    <div class="fc-gap">
      <div v-for="item in options[1]" :key="item.value" class="option" @click="onClickOption(item)">
        {{ item.label }}
      </div>
    </div>
    <!-- 过去12个月和具体日期之间的分隔线 -->
    <a-divider />
    <div class="fc-gap">
      <Popover trigger="click" placement="bottom" v-model:open="datePickerOpen">
        <div class="option">具体日期</div>
        <template #content>
          <CustomDatePicker :onConfirm="onDateConfirm" />
        </template>
      </Popover>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Popover } from 'ant-design-vue'
import { dateFilterOptions } from '../constant'
import { ref } from 'vue'
import CustomDatePicker from '@/components/filter/custom-date-picker.vue'
import type { Dayjs } from 'dayjs'
import type { ConditionEnum } from '@/components/filter/constant'
import dayjs from 'dayjs'

const props = defineProps<{
  onConfirm: (value: number | string | number[], condition?: ConditionEnum) => void
}>()
const options = [dateFilterOptions.slice(0, 5), dateFilterOptions.slice(5, 8)]
const datePickerOpen = ref(false)
const data = ref<number | string | number[]>()

const onDateConfirm = (value?: Dayjs[] | Dayjs, condition?: ConditionEnum) => {
  if (Array.isArray(value) && value.length > 0) {
    // 如果是数组，输出每个日期的时间戳
    const timestamps = value.map((dayjs) => dayjs.valueOf())
    console.log('日期数组时间戳:', timestamps)
    console.log('条件', condition)
    data.value = timestamps
    props.onConfirm(data.value, condition)
  } else if (dayjs.isDayjs(value)) {
    // 如果是单个日期，输出时间戳
    console.log('日期时间戳:', value.valueOf())
    console.log('条件', condition)
    data.value = value.valueOf()
    props.onConfirm(data.value, condition)
  }
  datePickerOpen.value = false
}
const onClickOption = (item: { label: string; value: string }) => {
  console.log('点击了:', item)
  data.value = item.value
  props.onConfirm(data.value)
}
</script>

<style scoped lang="less">
.date-filter {
  width: 287px;
  background-color: white;
  padding: 12px 16px;
  color: rgb(76, 87, 115);
  font-size: 14px;
  font-weight: 700;
  border-radius: 8px;
  box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.05);

  .fc-gap {
    display: flex;
    flex-direction: column;
    gap: 10px;

    .option {
      cursor: pointer;
    }
    .option:hover {
      color: rgb(80, 158, 227);
    }
  }
}
</style>
