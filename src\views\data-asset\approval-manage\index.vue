<template>
  <div class="page-full approval-manage-page">
    <div class="content-div">
      <cardBox :title="'审批管理'" :subTitle="'审批管理用于审批API发布、下线以及数据订阅'">
        <Table
          :columns="columns"
          :getData="transgetApprovalList"
          ref="tableRef"
          :autoRequest="false"
          :searchFormState="searchForm"
          :pagination="pagination"
        >
          <template #search>
            <a-form-item label="" name="workflowType">
              <a-select
                v-model:value="searchForm.workflowType"
                placeholder="请选择类型"
                allowClear
                style="width: 180px"
                :loading="optionsLoading"
              >
                <a-select-option
                  v-for="item in workflowTypeList"
                  :key="item.value"
                  :value="item.value"
                >
                  {{ item.label }}
                </a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="" name="status">
              <a-select
                v-model:value="searchForm.status"
                placeholder="请选择状态"
                allowClear
                style="width: 180px"
              >
                <a-select-option
                  v-for="(text, value) in statusOptions"
                  :key="value"
                  :value="Number(value)"
                >
                  {{ text }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </template>
          <template #bodyCell="{ column, record, text }">
            <template v-if="column.dataIndex === 'workflowType'">
              <a-spin :spinning="optionsLoading">
                {{ getWorkflowTypeText(text) }}
              </a-spin>
            </template>
            <template v-if="column.dataIndex === 'status'">
              <a-tag :color="getStatusColor(text)">{{ getStatusText(text) }}</a-tag>
            </template>
            <template v-if="column.dataIndex === 'operation'">
              <a-button type="link" @click="handleApprove(record)" :disabled="record.status !== 1">
                通过
              </a-button>
              <a-popconfirm title="确定要拒绝该审批吗？" @confirm="handleReject(record)">
                <a-button type="link" :disabled="record.status !== 1"> 拒绝 </a-button>
              </a-popconfirm>
            </template>
          </template>
        </Table>
      </cardBox>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { message } from 'ant-design-vue'
import { ref } from 'vue'
import { Table } from '@fs/fs-components'
import cardBox from '@/components/card-box/card-box.vue'

import {
  approveApproval,
  ApproveEnum,
  getApprovalList,
  getApprovalWorkflowTypeList,
  type ApprovalItem,
  type WorkflowTypeItem,
} from '@/api/services/data-asset/approval-workflow'

const searchForm = ref({
  workflowType: undefined as string | undefined,
  status: undefined as number | undefined,
})

const optionsLoading = ref(true)
const workflowTypeList = ref<WorkflowTypeItem[]>([])
// 状态选项
const statusOptions: { [key: number]: string } = {
  0: '待审批',
  1: '进行中',
  2: '已完成',
  3: '已拒绝',
}

const columns = ref<any[]>([
  {
    title: '申请人',
    dataIndex: 'createUserName',
    key: 'createUserName',
  },
  {
    title: '申请时间',
    dataIndex: 'createdTime',
    key: 'createdTime',
  },
  {
    title: '类型',
    dataIndex: 'workflowType',
    key: 'workflowType',
    scopedSlots: { customRender: 'workflowType' },
  },
  {
    title: '名称',
    dataIndex: 'workflowInstanceName',
    key: 'workflowInstanceName',
  },
  {
    title: '描述',
    dataIndex: 'description',
    key: 'description',
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    scopedSlots: { customRender: 'status' },
  },
  {
    title: '当前状况',
    dataIndex: 'currentNodeName',
    key: 'currentNodeName',
  },
  {
    title: '操作',
    key: 'operation',
    scopedSlots: { customRender: 'operation' },
  },
])

const tableRef = ref<InstanceType<typeof Table> | null>(null)
const loading = ref(false)
const pagination = ref({
  pageIndex: 1,
  pageSize: 10,
  // total: 0,
  // showSizeChanger: true,
  // showQuickJumper: true,
  // showTotal: (total: number) => `共 ${total} 条`,
})
const getStatusColor = (status: number) => {
  const colorMap: { [key: number]: string } = {
    1: 'orange',
    2: 'green',
    3: 'red',
  }
  return colorMap[status] || 'default'
}

const getStatusText = (status: number) => {
  return statusOptions[status] || status.toString()
}

const getWorkflowTypeText = (type: string) => {
  const item = workflowTypeList.value.find((item) => item.value === type)
  if (item) {
    return item.label
  }
  return ''
}

const handleApprove = async (record: ApprovalItem) => {
  try {
    const res = await approveApproval({
      approvalResult: ApproveEnum.APPROVE,
      currentNodeId: record.currentNodeId,
      workFlowInstanceId: record.id,
    })
    if (res.code === '000000') {
      message.success('审批已通过')
      fetchData()
    } else {
      message.error(res.msg || '操作失败')
    }
  } catch (error) {
    message.error('操作失败')
  }
}

const handleReject = async (record: ApprovalItem) => {
  try {
    // 调用拒绝接口
    await approveApproval({
      approvalResult: ApproveEnum.REJECT,
      currentNodeId: record.currentNodeId,
      workFlowInstanceId: record.id,
    })
    message.success('审批已拒绝')
    fetchData()
  } catch (error) {
    message.error('操作失败')
  }
}
/**
 * 获取审批流类型列表
 */
const queryWorkflowTypeList = async () => {
  try {
    optionsLoading.value = true
    const res = await getApprovalWorkflowTypeList()
    if (res.code === '000000' && res.data) {
      workflowTypeList.value = res.data
    } else {
      message.error(res.msg || '获取审批流类型列表失败')
    }
  } catch (error) {
    message.error('获取审批流类型列表失败')
  } finally {
    optionsLoading.value = false
  }
}

/* 获取列表数据 */
const transgetApprovalList = (params: any) =>
  getApprovalList(params).then((d: any) => {
    // 接口应该返回records 前端做处理
    // console.log('接口应该返回records 前端做处理', params, d)
    return d.records || []
  })

const fetchData = async () => {
  loading.value = true
  try {
    setTimeout(() => {
      tableRef.value?.getTableData()
    }, 20)
  } catch (error) {
    console.error('获取数据表列表失败:', error)
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  fetchData()
  queryWorkflowTypeList()
})
</script>
<style lang="less" scoped>
.approval-manage-page {
  height: 100%;

  .content-div {
    height: calc(100% - 48px);
  }
  :deep(.ant-btn) {
    margin-left: 8px;
  }
}
</style>
