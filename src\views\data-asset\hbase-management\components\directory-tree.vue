<template>
  <div class="directory-tree">
    <div class="title">
      <span>HBase存储管理</span>
    </div>
    <a-tree
      v-if="treeData.length > 0"
      :tree-data="treeData"
      v-model:expandedKeys="expandedKeys"
      v-model:selectedKeys="selectedKeys"
      :fieldNames="{
        children: 'children',
        title: 'name',
        key: 'id',
      }"
      class="custom-tree"
      showIcon
      :load-data="onLoadData"
      @select="handleSelect"
      @expand="handleExpand"
    >
      <template #icon="{ dataRef }">
        <CodeSandboxOutlined style="font-size: 18px" v-if="!dataRef.isDataBase" />
        <DatabaseOutlined style="font-size: 18px" v-else />
      </template>
      <template #title="{ name, dataRef }">
        <div
          class="tree-node-content"
          @mouseenter="handleMouseEnter(dataRef)"
          @mouseleave="handleMouseLeave(dataRef)"
        >
          <span class="node-name" :title="name">{{ name }}</span>
          <div class="node-actions" v-show="dataRef.isHovered">
            <a-dropdown>
              <MoreOutlined class="action-icon" @click.stop />
              <template #overlay>
                <a-menu>
                  <a-menu-item v-if="!dataRef.isDataBase" key="1" @click="handleAddAsset(dataRef)"
                    >新增库</a-menu-item
                  >
                  <a-menu-item v-else key="2" @click="handleDelete(dataRef)">删除</a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </div>
        </div>
      </template>
    </a-tree>
    <a-empty v-else style="margin-top: 100px">
      <template #description>暂无数据 </template>
    </a-empty>

    <!-- 新增资产弹窗 -->
    <add-hbase-modal
      v-model:visible="assetModalVisible"
      :parent-data="selectedNode"
      @success="handleAssetSuccess"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { message, Modal, type TreeProps } from 'ant-design-vue'
import { MoreOutlined, CodeSandboxOutlined, DatabaseOutlined } from '@ant-design/icons-vue'
import {
  getSchemaInfo,
  type ISchema,
  getDbNameList,
  deleteDbItem,
} from '@/api/services/data-asset/hbase-management'
import AddHbaseModal from './add-hbase-modal.vue'
// import { storeToRefs } from 'pinia'
import { useHbaseStore } from '@/stores/page/hbase'
import { useUserStore } from '@/stores/user'

const emit = defineEmits(['add-hbase'])
// const userStore = useUserStore()
const hbaseStore = useHbaseStore()

// const { userInfo } = storeToRefs(userStore)
// const { treeDbData } = storeToRefs(hbaseStore)
const { setTreeDbData } = hbaseStore

const expandedKeys = ref<string[]>([])
const selectedKeys = ref<string[]>([])
const treeData = ref<any>([])

onMounted(() => {
  // if (treeDbData.value.expandedKeys?.length) {
  //   expandedKeys.value = treeDbData.value.expandedKeys
  // }
  // if (treeDbData.value.selectedKeys?.length) {
  //   selectedKeys.value = treeDbData.value.selectedKeys
  //   emit('add-hbase', { ...treeDbData.value.dataRef })
  // }
  // if (treeDbData.value.treeData?.length) {
  //   treeData.value = treeDbData.value.treeData
  // } else {
  //   fetchSchemaData()
  // }
  fetchSchemaData()
})

// 弹窗相关
const isAddBaseData = ref(false)

// 处理树形数据，递归设置 isLeaf
const processTreeData = (data: any[]) => {
  return data.map((item) => {
    const processedItem = { ...item }
    if (processedItem.assertCount <= 0 && !processedItem?.children?.length) {
      processedItem.isLeaf = true
    }
    if (processedItem.children && processedItem.children.length > 0) {
      processedItem.children = processTreeData(processedItem.children)
    }
    return processedItem
  })
}

const handleSelect = (selectedKeysArr: string[], info: any) => {
  setTreeDbData('selectedKeys', selectedKeysArr)
  setTreeDbData('expandedKeys', expandedKeys.value)
  setTreeDbData('treeData', treeData.value)
  const dataRef = info.node.dataRef
  setTreeDbData('dataRef', dataRef)
  if (dataRef?.isDataBase) {
    emit('add-hbase', dataRef)
  }
}

const handleExpand = (expandedKeysArr: string[]) => {
  expandedKeys.value = expandedKeysArr
  setTreeDbData('expandedKeys', expandedKeysArr)
  setTreeDbData('treeData', treeData.value)
}

const onLoadData: TreeProps['loadData'] = (treeNode: any) => {
  // console.log('onLoadData', treeNode)
  return new Promise<void>((resolve) => {
    if (treeNode.dataRef.children) {
      resolve()
      return
    }
    getDbNameList({ id: treeNode.dataRef.id })
      .then((res) => {
        if (res.code === '000000') {
          const list = res.data.map((item) => ({
            id: `${treeNode.dataRef.id}/${item}`, // 区分不同数据源下的数据库列表
            name: item,
            isLeaf: true,
            isDataBase: true,
            dbUrlId: treeNode.dataRef.id,
          }))
          if (!treeNode?.dataRef?.children) treeNode.dataRef.children = []
          treeNode.dataRef.children = [...treeNode.dataRef.children, ...list]
          treeData.value = [...treeData.value]
          setTreeDbData('treeData', treeData.value)
        } else {
          console.error('加载数据失败:', res.msg)
        }
        resolve()
      })
      .catch((error) => {
        console.error('加载数据失败:', error)
        resolve()
      })
  })
}

const fetchSchemaData = async () => {
  try {
    const userStore = useUserStore()
    const params = {
      // ownerId:  `${userInfo.value.userName}@${userInfo.value.tenantCode}`,
      ownerId: userStore.userInfo.loginAccount, // 测试用
    }
    const res = await getSchemaInfo(params)
    if (res.code === '000000' && res.data.length) {
      treeData.value = (res.data.filter((v: ISchema) => v.dbType === 30) || []).map(
        (item: ISchema) => {
          return {
            isLeaf: false,
            ...item,
            name: item.dbDesc,
          }
        },
      )
      setTreeDbData('treeData', treeData.value)
    }
  } catch (error) {
    console.error('获取数据失败:', error)
  }
}

const handleMouseEnter = (dataRef: any) => {
  dataRef.isHovered = true
}

const handleMouseLeave = (dataRef: any) => {
  dataRef.isHovered = false
}

// 弹窗相关
const assetModalVisible = ref(false)
const selectedNode = ref<Record<string, any>>({})

const handleAddAsset = (data: Record<string, any>) => {
  selectedNode.value = data
  selectedNode.value.id = data?.id || ''
  assetModalVisible.value = true
  isAddBaseData.value = true
}

const handleDelete = async (data: Record<string, any>) => {
  Modal.confirm({
    title: '确认删除',
    content: '是否确认删除该数据库？',
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      try {
        await deleteDbItem({ id: data.dbUrlId, namespace: data.id.split('/')[1] })
        message.success('删除成功')

        // 从树形数据中删除节点
        const removeNode = (nodes: any[]): boolean => {
          for (let i = 0; i < nodes.length; i++) {
            if (nodes[i].id === data.id) {
              nodes.splice(i, 1)
              return true
            }
            if (nodes[i].children && nodes[i].children.length > 0) {
              if (removeNode(nodes[i].children)) return true
            }
          }
          return false
        }

        removeNode(treeData.value)
        // 强制更新树形数据
        treeData.value = JSON.parse(JSON.stringify(treeData.value))
      } catch (error) {
        console.error('删除失败:', error)
      }
    },
  })
}

const handleAssetSuccess = (data: any) => {
  // 重新加载当前节点的子节点数据
  const node = selectedNode.value

  if (node) {
    // 确保children数组存在
    if (!node.children) {
      node.children = []
    }
    emit('add-hbase', { ...data })
    selectedKeys.value = [data.id]
    // 如果已存在，则不添加
    if (!isAddBaseData.value) return

    // 创建新节点
    const newNode = {
      isLeaf: true,
      isDataBase: true,
      ...data,
    }
    // 添加新节点到父节点
    node.children.unshift(newNode)
    // 更新父节点的isLeaf状态
    node.isLeaf = false
    // 强制更新树形数据
    treeData.value = JSON.parse(JSON.stringify(treeData.value))
  }
  isAddBaseData.value = false
}

defineExpose({
  fetchSchemaData,
})
</script>

<style lang="less" scoped>
.directory-tree {
  height: 100%;

  .custom-tree {
    margin-top: 10px;
    :deep(.ant-tree-node-content-wrapper) {
      width: 100%;
      &:hover {
        background-color: #f5f5f5;
      }
    }
    :deep(.ant-tree-node-selected) {
      background-color: #e6f7ff;
    }
    :deep(.ant-tree-title) {
      width: 100%;
    }
    .tree-node-content {
      width: 100%;
      padding-right: 10px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .node-name {
        width: 85%;
        white-space: nowrap;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .node-actions {
        display: flex;
        align-items: center;
        gap: 8px;

        .action-icon {
          font-size: 16px;
          color: #666;
          cursor: pointer;

          &:hover {
            color: #1890ff;
          }
        }
      }
    }
  }

  :deep(.ant-tree) {
    .ant-tree-treenode {
      width: 100%;
      height: 40px;
      display: flex;
      align-items: center;
      .ant-tree-switcher {
        line-height: 40px;
      }
      .ant-tree-iconEle {
        width: 18px;
        height: 18px;
        line-height: 18px;
        margin-right: 5px;
      }
      .ant-tree-title {
        width: 100%;
      }
      .ant-tree-node-content-wrapper {
        width: 100%;
        height: 40px;
        display: flex;
        align-items: center;
      }
    }
    // .db-icon img {
    //   width: auto!important;
    // }
  }

  .title {
    margin-right: auto;
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 24px;
    padding-bottom: 16px;
    display: flex;
    justify-content: space-between;
    padding-right: 12px;
  }
}
</style>
