import service from '@/api'
import { MOCK_DATA } from './bussinesmanage-mock'

/**
 * 根据businessId删除指标业务类型表数据 - 删除业务类型
 * @param {number} businessId - 业务类型
 * @returns {any} - 返回一个解析为响应数据的Promise
 */

export async function indexbusinessDelete(data: { businessId: number }): Promise<any> {
  return service(
    {
      method: 'POST',
      url: '/indexManage/bussinesManage/indexbusinessDelete',
      data,
    },
    MOCK_DATA.indexbusinessDelete,
  )
}

/**
 * 新增指标业务类型表数据 - 新增业务类型
 * @param {string} businessName - 业务类型名称
 * @param {string} remark - 备注
 * @param {string} opUser -
 * @param {string} opUserName -
 * @returns {any} - 返回一个解析为响应数据的Promise
 */

export async function indexbusinessInsert(
  data: {
    businessName: string
    remark?: string
  },
  headers?: {
    opUser?: string
    opUserName?: string
  },
): Promise<any> {
  return service(
    {
      method: 'POST',
      url: '/indexManage/bussinesManage/indexbusinessInsert',
      data,
      headers,
    },
    MOCK_DATA.indexbusinessInsert,
  )
}

/**
 * 根据businessId修改指标业务类型表数据 - 修改业务类型
 * @param {string} businessName - 业务类型名称
 * @param {string} remark - 备注
 * @param {number} businessId -
 * @param {string} opUser -
 * @param {string} opUserName -
 * @returns {any} - 返回一个解析为响应数据的Promise
 */

export async function indexbusinessUpdate(
  data: {
    businessName?: string
    remark?: string
    businessId: number
  },
  headers?: {
    opUser?: string
    opUserName?: string
  },
): Promise<any> {
  return service(
    {
      method: 'POST',
      url: '/indexManage/bussinesManage/indexbusinessUpdate',
      data,
      headers,
    },
    MOCK_DATA.indexbusinessUpdate,
  )
}

/**
 * 列表查询指标业务类型表数据 - 查询业务类型列表
 * @param {string} businessName - 业务类型名称
 * @param {string} remark - 备注
 * @param {string} opUser -
 * @param {string} opUserName -
 * @returns {any} - 返回一个解析为响应数据的Promise
 */

export async function getIndexbusinessList(
  data: {
    businessName?: string
    remark?: string
  },
  headers?: {
    opUser?: string
    opUserName?: string
  },
): Promise<{
  code: string
  msg: string
  data: {
    businessId: number
    businessName: string
    remark: string
    createUser: string
    createTime: string
    formCount: number
  }[]
}> {
  return service(
    {
      method: 'GET',
      url: '/indexManage/bussinesManage/getIndexbusinessList',
      params: data,
      headers,
    },
    MOCK_DATA.getIndexbusinessList,
  )
}

/**
 * 分页查询指标业务类型表数据 - 分页查询业务类型列表
 * @param {string} businessName - 业务类型名称
 * @param {string} remark - 备注
 * @param {number} pageIndex - 查询当前页数
 * @param {number} pageSize - 每页显示的记录条数
 * @param {string} opUser -
 * @param {string} opUserName -
 * @returns {any} - 返回一个解析为响应数据的Promise
 */

export async function getBusinessPageList(
  data: {
    businessName?: string
    remark?: string
    pageIndex: number
    pageSize: number
  },
  headers?: {
    opUser?: string
    opUserName?: string
  },
): Promise<{
  msg: string
  code: string
  data: {
    totalRecords: number
    pageIndex: number
    pageSize: number
    totalPages: number
    records: Array<{
      businessId: number
      businessName: string
      remark: string
      createUser: string
      createTime: string
    }>
  }
}> {
  return service(
    {
      method: 'GET',
      url: '/indexManage/bussinesManage/getBusinessPageList',
      params: data,
      headers,
    },
    MOCK_DATA.getBusinessPageList,
  )
}
