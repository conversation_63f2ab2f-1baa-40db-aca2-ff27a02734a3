import type { ColumnType } from '@/api/services/indicator/type'
import type { ConditionEnum, FormType } from './constant'

/**
 * 选项结构
 */
export interface SelectOption {
  value: string
  label: string
}
/**
 * 过滤条目
 */
export interface FilterItem {
  field: string // 字段名称
  originType: FormType // 初始类型
  columnType: ColumnType
  type?: FormType // 表单类型
  value: any // 当前选中的值
  componentProps: Record<string | symbol, any>
  condition?: ConditionEnum
  comment?: string // 描述
}
