<script lang="ts" setup>
import * as echarts from 'echarts'

const chartRef = ref(null)
const option = {
  title: {
    text: '有效得分占比',
    left: 'center',
    bottom: 0,
    textStyle: {
      fontSize: 14,
      fontWeight: 'normal',
    },
  },
  tooltip: {
    trigger: 'item',
    formatter: '{b}: {c} ({d}%)',
  },
  legend: {
    orient: 'vertical',
    right: '10%',
    top: 'center',
    textStyle: {
      color: '#666',
    },
  },
  series: [
    {
      name: '得分区间',
      type: 'pie',
      radius: ['5%', '80%'], // 设置内外半径，形成环形
      roseType: 'radius', // 南丁格尔玫瑰图的关键设置
      avoidLabelOverlap: false,
      label: {
        show: true,
        position: 'outside',
        formatter: '{b}',
        color: '#007bff',
      },
      labelLine: {
        show: true,
        length: 10,
        length2: 15,
        lineStyle: {
          color: '#ccc',
        },
      },
      itemStyle: {
        borderRadius: 5,
        borderColor: '#fff',
        borderWidth: 2,
      },
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)',
        },
      },
      data: [
        { value: 20, name: '0.7~0.79', itemStyle: { color: '#5470C6' } },
        { value: 25, name: '0.8~0.94', itemStyle: { color: '#91CC75' } },
        { value: 30, name: '0.95以上', itemStyle: { color: '#FAC858' } },
        { value: 25, name: '其他', itemStyle: { color: '#EE6666' } },
      ],
    },
  ],
}

onMounted(() => {
  const chart = echarts.init(chartRef.value, null, { renderer: 'canvas', height: 320 })
  chart.setOption(option)
})
</script>

<template>
  <div class="download-chart" ref="chartRef"></div>
</template>

<style lang="scss" scoped></style>
