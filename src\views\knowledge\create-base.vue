<template>
  <div class="content">
    <div class="form-content">
      <!-- 表单 -->
      <a-form
        :model="formState"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 24 }"
        layout="vertical"
        :colon="true"
        label-align="right"
        auto-complete="off"
        @finish="onFinish"
        @finish-failed="onFinishFailed"
        class="mauto"
      >
        <a-form-item
          label="中文名称"
          name="displayName"
          :rules="[{ required: true, message: '请输入中文名称', trigger: 'blur' }]"
        >
          <a-input v-model:value="formState.displayName" placeholder="请输入中文名称" />
        </a-form-item>
        <a-form-item
          label="英文名称"
          name="enName"
          :rules="[{ required: true, message: '请输入英文名称', trigger: 'blur' }]"
        >
          <a-input
            :disabled="!!initFormState?.enName"
            v-model:value="formState.enName"
            placeholder="请输入英文名称"
          />
        </a-form-item>
        <a-form-item label="描述" name="desc">
          <a-textarea v-model:value="formState.desc" placeholder="请输入描述" />
        </a-form-item>
        <!-- 图存储配置 -->
        <!-- 需要一个单选框: 默认配置 或 自定义配置 -->
        <a-form-item style="text-align: center">
          <a-button type="primary" html-type="submit" :loading="createLoading">保存</a-button>
          <a-button type="default" class="ml-5" @click="onCancel">取消</a-button>
        </a-form-item>
      </a-form>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { Select } from 'ant-design-vue'
import type { KnowledgeBase } from './type'
const props = defineProps<{
  onSave: (value: KnowledgeBase) => void
  onCancel: () => void
  initFormState?: KnowledgeBase
  createLoading: boolean
}>()

const formState = reactive<KnowledgeBase>({
  displayName: '',
  enName: '',
  desc: '',
})

onMounted(() => {
  if (props.initFormState) {
    Object.assign(formState, props.initFormState)
  }
})
const onFinish = (values: KnowledgeBase) => {
  props.onSave(values)
}
const onFinishFailed = (errorInfo: any) => {
  console.log('Failed:', errorInfo)
}
</script>
<style scoped lang="less">
.form-content {
  display: flex;
  flex-direction: column;
}
</style>
