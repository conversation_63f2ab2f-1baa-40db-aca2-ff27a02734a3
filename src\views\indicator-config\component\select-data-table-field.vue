<template>
  <a-modal
    v-model:visible="visible"
    title="选择初始数据"
    @ok="handleOk"
    @cancel="handleCancel"
    :width="1200"
    :confirmLoading="confirmLoading"
    wrapClassName="data-assets-modal"
    :destroyOnClose="true"
  >
    <a-row class="content-row">
      <a-col flex="400px" class="left-col">
        <a-tree
          v-model:selectedKeys="selectedTreeKeys"
          v-model:checkedKeys="checkedTreeKeys"
          v-model:expandedKeys="expandedTreeKeys"
          :tree-data="treeData"
          :checkable="false"
          @select="onTreeSelect"
          @check="onTreeCheck"
          :checkStrictly="true"
        >
          <template #title="{ title, dataRef }">
            <div class="tree-node-content" :title="title">
              <FolderOutlined
                style="font-size: 18px; margin-right: 10px; color: #677164"
                v-if="dataRef.nodeType === 1"
              />
              <type-icon
                v-else-if="dataRef.nodeType === 2"
                style="margin-right: 10px"
                :type="dataRef?.databaseType"
              />
              <TableOutlined
                style="font-size: 18px; margin-right: 10px; color: #677164"
                v-else-if="dataRef.nodeType === 3"
              />
              <span class="node-name">
                {{ title }}
              </span>
            </div>
          </template>
        </a-tree>
      </a-col>
      <a-col flex="auto" class="right-col">
        <div class="button-wrapper">
          <span class="table-name">{{ selectInfo?.tableName }}</span>
          <!-- <a-button
            type="primary"
            @click="openHandleColumn"
            v-if="selectInfo && selectInfo.nodeType === 3"
            >行控制</a-button
          > -->
        </div>
        <a-table
          :columns="columns"
          :data-source="dataSource"
          :loading="loadingColumns"
          :row-selection="{
            selectedRowKeys: selectedColumns,
            onChange: onSelectionChange,
            type: 'checkbox',
          }"
          :pagination="false"
          rowKey="title"
          :scroll="{ y: 500 }"
        >
        </a-table>
      </a-col>
    </a-row>
  </a-modal>

  <!-- <a-modal
    v-model:open="open"
    title="行控制"
    destroyOnClose
    width="700px"
    @ok="handleOkColumn"
    @cancel="handleCancelColumn"
  >
    <a-form
      ref="formRef"
      :model="formState"
      name="basic"
      :label-col="{ span: 5 }"
      :wrapper-col="{ span: 16 }"
    >
      <a-form-item label="行配置" name="configValue">
        <a-space
          v-for="(user, index) in formState.configValue"
          :key="user.id"
          style="display: flex"
          align="baseline"
        >
          <a-form-item style="width: 120px" :name="['configValue', index, 'columnName']">
            <a-select
              placeholder="columnName"
              :options="paramList"
              :fieldNames="{ label: 'columnName', value: 'columnName' }"
              v-model:value="user.columnName"
            ></a-select>
          </a-form-item>
          <a-form-item style="width: 80px" :name="['configValue', index, 'operator']">
            <a-select
              placeholder="operator"
              :options="[
                { label: '大于', value: '>' },
                { label: '小于', value: '<' },
                { label: '等于', value: '=' },
                { label: '不等于', value: '!=' },
              ]"
              v-model:value="user.operator"
            ></a-select>
          </a-form-item>
          <a-form-item :name="['configValue', index, 'value']">
            <a-input v-model:value="user.value" placeholder="value" />
          </a-form-item>
          <MinusCircleOutlined @click="removeUser(user)" />
        </a-space>
        <a-button type="dashed" block @click="addUser">
          <PlusOutlined />
          添加行配置
        </a-button>
      </a-form-item>
    </a-form>
  </a-modal> -->
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import type { TableColumnType } from 'ant-design-vue'
import { reqGetParamsList } from '@/views/data-asset/data-asset-management/auth-page/api/index'
import {
  MinusCircleOutlined,
  PlusOutlined,
  FolderOutlined,
  TableOutlined,
} from '@ant-design/icons-vue'
//   import {
//     getDiretoryTree,
//     getColumnsListByRoleId,
//     saveDataPermissionRole,
//   } from '@/api/assetmanager/dataassertmanage/dataassertmanage'
import TypeIcon from '@/views/data-asset/metadata-query/components/type-icon.vue'
import { message } from 'ant-design-vue'
import { getDiretoryTree } from '@/api/indexmanage/indexmanage/indexmanage'
import { getDataAssetColumnList } from '@/api/indexmanage/datasourceoptions/datasourceoptions'

interface ColumnItem {
  scopedSlots?: { icon: string }
  title: string
  key: string
  desc: string
  disabled: boolean
  columnType: string
  columnName?: string
  columnTypeName: string
  comment: string
}
interface DataItem {
  key: string
  name: string
  description: string
}

interface FormState {
  userIdList: string | string[]
  columns: string[]
  configValue: ConfigValueType[]
}

interface ConfigValueType {
  columnName: string | undefined
  operator: string | undefined
  value: string | undefined
  id: number
}

interface TreeNode {
  title: string
  key: string
  tableId?: string
  columns?: string
  tableName?: string
  configValue?: ConfigValueType[]
  children?: TreeNode[]
  nodeType?: number
  databaseName?: string
  databaseId?: string
  datasourceId?: string
  datasourceName?: string
  databaseType?: string
}

const visible = ref(false)
const open = ref(false)
const selectedRows = ref<DataItem[]>([])
const confirmLoading = ref(false)
const selectInfo = ref<any>(null)
const tableList = ref<any[]>([])
const checkedAllData = ref<any[]>([])
const roleData = ref<any>({})
const emit = defineEmits(['confirmData'])
const loadingColumns = ref(false)

const formState = reactive<FormState>({
  userIdList: [],
  columns: [],
  configValue: [
    {
      columnName: undefined,
      operator: undefined,
      value: undefined,
      id: Date.now(),
    },
  ],
})

// 表格列定义
const columns: TableColumnType[] = [
  {
    title: '列名',
    dataIndex: 'title',
    key: 'title',
  },
  {
    title: '注释',
    dataIndex: 'comment',
    key: 'comment',
  },
]

// 模拟数据
const dataSource = ref<ColumnItem[]>([])
const paramList = ref<any[]>([])
const selectedColumns = ref<string[]>([])

const onSelectionChange = (selectedKeys: string[]) => {
  selectedColumns.value = selectedKeys
  const index = tableList.value.findIndex((item: any) => selectInfo.value.key === item.key)
  selectInfo.value.columns = selectedKeys
  if (index !== -1) {
    tableList.value[index].columns = selectedColumns.value
  }
}

// 树形数据
const treeData = ref<TreeNode[]>([])

const selectedTreeKeys = ref<string[]>([])
const checkedTreeKeys = ref<any>({})
const expandedTreeKeys = ref<string[]>([])

// 获取所有子节点的key
const getAllChildrenKeys = (node: any): string[] => {
  const keys: string[] = []
  if (node.key) {
    keys.push(node.key)
  }
  if (node.children && node.children.length > 0) {
    node.children.forEach((child: any) => {
      keys.push(...getAllChildrenKeys(child))
    })
  }
  return keys
}

// 获取节点及其所有子节点
const getNodeAndChildren = (node: any): any[] => {
  const nodes: any[] = [node]
  if (node.children && node.children.length > 0) {
    node.children.forEach((child: any) => {
      nodes.push(...getNodeAndChildren(child))
    })
  }
  return nodes
}

// 在树中查找节点
const findNodeByKey = (key: string, nodes: TreeNode[]): TreeNode | null => {
  for (const node of nodes) {
    if (node.key === key) {
      return node
    }
    if (node.children) {
      const found = findNodeByKey(key, node.children)
      if (found) {
        return found
      }
    }
  }
  return null
}

// 在 script setup 部分添加获取父节点的方法
const getParentKey = (key: string, tree: TreeNode[]): string | null => {
  let parentKey = null
  for (const node of tree) {
    if (node.children) {
      if (node.children.some((item: TreeNode) => item.key === key)) {
        parentKey = node.key
      } else {
        const grandParentKey = getParentKey(key, node.children)
        if (grandParentKey) {
          parentKey = grandParentKey
        }
      }
    }
  }
  return parentKey
}

// 树节点选择事件处理
const onTreeSelect = async (selectedKeys: string[], info: any) => {
  const { dataRef, checked } = info.node
  if (dataRef.nodeType !== 3) return
  loadingColumns.value = true
  const res = await getDataAssetColumnList({
    tableId: dataRef.tableId,
  })
  console.log('🚀 ~ onTreeSelect ~ res:', res)
  console.log('🚀 ~ onTreeSelect ~ dataRef:', dataRef)

  dataSource.value = res.data as any

  // 确保selectInfo包含完整的数据库信息
  const enhancedDataRef = {
    ...dataRef,
    // 如果dataRef没有databaseName，从父节点获取
    databaseName: dataRef.databaseName || findDatabaseNameFromParent(dataRef),
    datasourceId: dataRef.datasourceId || findDatasourceIdFromParent(dataRef),
  }

  selectInfo.value = enhancedDataRef
  loadingColumns.value = false
  dataRef.columns = res.data

  console.log('🚀 ~ 增强后的selectInfo:', selectInfo.value)

  if (checked) {
    if (dataRef.columns) {
      selectedColumns.value = dataRef.columns
    } else {
      selectedColumns.value = res.data.map((item: any) => item.title)
    }
  }

  const index = tableList.value.findIndex((item: any) => dataRef.tableId === item.tableId)
  if (index === -1) {
    tableList.value.push({
      ...enhancedDataRef,
      selected: 'YES',
      configValue: '',
    })
    selectInfo.value = tableList.value[tableList.value.length - 1]
  } else {
    selectInfo.value = tableList.value[index]
    // 确保现有项也有完整信息
    Object.assign(selectInfo.value, enhancedDataRef)
  }
}

// 辅助函数：从父节点获取数据库名称
const findDatabaseNameFromParent = (tableNode: any): string => {
  const parentKey = getParentKey(tableNode.key || tableNode.tableId, treeData.value)
  if (parentKey) {
    const parentNode = findNodeByKey(parentKey, treeData.value)
    if (parentNode && parentNode.databaseName) {
      console.log('从父节点获取数据库名称:', parentNode.databaseName)
      return parentNode.databaseName
    }
  }
  return tableNode.databaseName || ''
}

// 获取指定数据库下的所有表数据
const getAllTablesInCurrentDatabase = (databaseName: string): any[] => {
  const allTables: any[] = []

  const findTablesInDatabase = (nodes: TreeNode[], targetDatabaseName: string) => {
    for (const node of nodes) {
      if (node.nodeType === 2 && node.databaseName === targetDatabaseName) {
        // 找到目标数据库，收集其下的所有表
        if (node.children) {
          node.children.forEach((tableNode) => {
            if (tableNode.nodeType === 3) {
              allTables.push({
                tableId: tableNode.tableId,
                tableName: tableNode.tableName,
                tableComment: tableNode.title || tableNode.tableName,
                databaseName: node.databaseName,
                databaseId: node.databaseId,
                datasourceId: node.datasourceId,
                datasourceName: node.datasourceName,
                databaseType: node.databaseType,
                nodeType: 3,
                key: tableNode.key,
                title: tableNode.title || tableNode.tableName,
              })
            }
          })
        }
        break
      }

      if (node.children) {
        findTablesInDatabase(node.children, targetDatabaseName)
      }
    }
  }

  findTablesInDatabase(treeData.value, databaseName)

  console.log(`获取数据库 ${databaseName} 下的所有表:`, allTables)
  return allTables
}

// 辅助函数：从父节点获取数据源ID
const findDatasourceIdFromParent = (tableNode: any): string => {
  const parentKey = getParentKey(tableNode.key || tableNode.tableId, treeData.value)
  if (parentKey) {
    const parentNode = findNodeByKey(parentKey, treeData.value)
    if (parentNode && parentNode.datasourceId) {
      console.log('从父节点获取数据源ID:', parentNode.datasourceId)
      return parentNode.datasourceId
    }
  }
  return tableNode.datasourceId || ''
}

// 树节点勾选事件处理
const onTreeCheck = (checkedKeys: any, info: any) => {
  const { checked, node } = info
  let newCheckedKeys = Array.isArray(checkedKeys) ? checkedKeys : checkedKeys.checked || []

  if (checked) {
    // 获取所有父节点的key
    let parentKey = getParentKey(node.key, treeData.value)
    while (parentKey) {
      if (!newCheckedKeys.includes(parentKey)) {
        newCheckedKeys.push(parentKey)
      }
      parentKey = getParentKey(parentKey, treeData.value)
    }
    node.dataRef.selected = 'YES'
    // 获取所有子节点的key
    const allChildrenKeys = getAllChildrenKeys(node)
    newCheckedKeys = Array.from(new Set([...newCheckedKeys, ...allChildrenKeys]))

    // 获取所有子节点数据
    const allNodes = getNodeAndChildren(node)
    allNodes.forEach((node) => {
      const index = tableList.value.findIndex((item: any) => node.tableId === item.tableId)
      if (index === -1 && node.nodeType === 3) {
        console.log('🚀 ~ allNodes.forEach ~ node:', node)
        const obj = node?.dataRef || node
        tableList.value.push({
          ...obj,
          selected: 'YES',
          configValue: null,
        })
      }
    })
    console.log('🚀 ~ allNodes.forEach ~ tableList.value:', tableList.value)
    // 表格选中
    if (selectInfo.value && node.dataRef.key === selectInfo.value.key) {
      selectInfo.value.columns = dataSource.value.map((item: any) => item.title)
      const val = tableList.value.find((item: any) => item.key === node.dataRef.key)
      if (val) {
        val.columns = selectInfo.value.columns
      }
      selectedColumns.value = dataSource.value.map((item: any) => item.title)
    }
  } else {
    // 取消勾选时的逻辑保持不变
    const allChildrenKeys = getAllChildrenKeys(node)
    newCheckedKeys = newCheckedKeys.filter((key: any) => !allChildrenKeys.includes(key))

    const allNodes = getNodeAndChildren(node)
    allNodes.forEach((node) => {
      const index = tableList.value.findIndex((item: any) => node.tableId === item.tableId)
      if (index !== -1) {
        tableList.value.splice(index, 1)
      }
    })
    node.dataRef.selected = 'NO'
    // 取消表格已选中
    if (selectInfo.value && node.dataRef.key === selectInfo.value.key) {
      selectInfo.value.columns = []
      selectedColumns.value = []
    }
  }

  checkedTreeKeys.value = { checked: newCheckedKeys, halfChecked: [] }
  checkedAllData.value = newCheckedKeys
    .map((key: any) => findNodeByKey(key, treeData.value))
    .filter(Boolean)
}

// 处理树形数据的函数
const handleTreeData = (data: any[]): TreeNode[] => {
  return data.map((item) => {
    const node: TreeNode = {
      ...item,
      title: getNodeTitle(item),
      key: getNodeKey(item),
      children: item.children ? handleTreeData(item.children) : undefined,
    }
    if (item?.columns) {
      node.columns = item.columns.split(',')
    }
    if (item?.configValue) {
      node.configValue = JSON.parse(item.configValue)
    }
    return node
  })
}

const getNodeTitle = (item: any) => {
  switch (item.nodeType) {
    case 1:
      return item.name
    case 2:
      return item.databaseName
    case 3:
      return item.tableName
    default:
      return ''
  }
}

const getNodeKey = (item: any) => {
  switch (item.nodeType) {
    case 1:
      return item.directoryId
    case 2:
      return item.databaseId
    case 3:
      return item.tableId
    default:
      return ''
  }
}

// 添加一个递归函数来获取所有选中的节点
const getSelectedKeys = (nodes: any[]): string[] => {
  let keys: string[] = []
  nodes.forEach((node) => {
    if (node.checked === 'YES') {
      keys.push(node.key)
      // 表格数据
      if (node.nodeType === 3) {
        tableList.value.push({
          ...node,
        })
        // 目录数据和数据库数据
      } else {
        checkedAllData.value.push({
          ...node,
        })
      }
    }
    if (node.children && node.children.length > 0) {
      keys = [...keys, ...getSelectedKeys(node.children)]
    }
  })
  return keys
}

// 修改 showModal 方法，支持数据回显
const showModal = async (echoData?: {
  tableId?: string
  tableName?: string
  databaseName?: string
  checkedColumns?: string[]
  dbColumns?: any[]
}) => {
  // 重置状态
  selectedColumns.value = []
  dataSource.value = []
  tableList.value = []
  selectedTreeKeys.value = []
  expandedTreeKeys.value = []
  selectInfo.value = null
  visible.value = true
  checkedAllData.value = []

  console.log('🚀 ~ showModal ~ 回显数据:', echoData)

  try {
    // 初始化加载所有数据
    const res = await getDiretoryTree()
    treeData.value = handleTreeData(res.data)

    // 如果有回显数据，进行数据回显
    if (echoData && echoData.tableId) {
      await handleDataEcho(
        echoData as {
          tableId: string
          tableName?: string
          databaseName?: string
          checkedColumns?: string[]
          dbColumns?: any[]
        },
      )
    } else {
      // 获取所有选中的节点的key（原有逻辑）
      checkedTreeKeys.value = getSelectedKeys(treeData.value)

      // 默认展开第一层节点，方便用户浏览
      expandedTreeKeys.value = treeData.value
        .filter((node) => node.nodeType === 1) // 只展开目录节点
        .map((node) => node.key)
    }
  } catch (error) {
    console.error('获取项目列表时发生错误:', error)
  }
}

// 数据回显处理函数
const handleDataEcho = async (echoData: {
  tableId: string
  tableName?: string
  databaseName?: string
  checkedColumns?: string[]
  dbColumns?: any[]
}) => {
  console.log('🚀 ~ 开始数据回显:', echoData)

  try {
    // 1. 在树中查找对应的表节点
    const targetTableNode = findTableNodeById(echoData.tableId, treeData.value)
    if (!targetTableNode) {
      console.warn('未找到对应的表节点:', echoData.tableId)
      return
    }

    console.log('🚀 ~ 找到目标表节点:', targetTableNode)

    // 2. 设置树的选中状态和展开状态
    const tableKey = targetTableNode.key
    const parentKeys = getParentKeys(tableKey, treeData.value)
    const allKeys = [...parentKeys, tableKey]

    checkedTreeKeys.value = { checked: allKeys, halfChecked: [] }
    selectedTreeKeys.value = [tableKey]
    // 展开所有父节点，让目标表节点可见
    expandedTreeKeys.value = parentKeys

    console.log('🚀 ~ 数据回显 - 展开的节点keys:', parentKeys)
    console.log('🚀 ~ 数据回显 - 选中的节点key:', tableKey)
    console.log('🚀 ~ 数据回显 - 勾选的节点keys:', allKeys)

    // 3. 获取表字段数据
    const res = await getDataAssetColumnList({
      tableId: echoData.tableId,
    })

    if (res.code === '000000' && res.data) {
      dataSource.value = res.data as any

      // 4. 设置selectInfo
      const enhancedDataRef = {
        ...targetTableNode,
        databaseName:
          targetTableNode.databaseName ||
          echoData.databaseName ||
          findDatabaseNameFromParent(targetTableNode),
        datasourceId: targetTableNode.datasourceId || findDatasourceIdFromParent(targetTableNode),
        tableName: targetTableNode.tableName || echoData.tableName,
        tableId: echoData.tableId,
        nodeType: 3,
      }

      selectInfo.value = enhancedDataRef

      // 5. 设置tableList
      tableList.value = [
        {
          ...enhancedDataRef,
          selected: 'YES',
          configValue: '',
          columns: res.data,
        },
      ]

      // 6. 设置选中的字段
      if (echoData.checkedColumns && echoData.checkedColumns.length > 0) {
        selectedColumns.value = echoData.checkedColumns
      } else {
        // 默认选中所有字段
        selectedColumns.value = res.data.map((item: any) => item.title)
      }

      console.log('🚀 ~ 数据回显完成:', {
        selectInfo: selectInfo.value,
        selectedColumns: selectedColumns.value,
        tableList: tableList.value,
      })
    }
  } catch (error) {
    console.error('数据回显失败:', error)
  }
}

// 辅助函数：根据tableId在树中查找表节点
const findTableNodeById = (tableId: string, nodes: TreeNode[]): TreeNode | null => {
  for (const node of nodes) {
    if (node.nodeType === 3 && node.tableId === tableId) {
      return node
    }
    if (node.children) {
      const found = findTableNodeById(tableId, node.children)
      if (found) return found
    }
  }
  return null
}

// 辅助函数：获取节点的所有父节点keys
const getParentKeys = (nodeKey: string, nodes: TreeNode[]): string[] => {
  const keys: string[] = []
  let currentKey = nodeKey

  while (currentKey) {
    const parentKey = getParentKey(currentKey, nodes)
    if (parentKey) {
      keys.unshift(parentKey) // 添加到开头，保持层级顺序
      currentKey = parentKey
    } else {
      break
    }
  }

  return keys
}

// 确认按钮处理函数
const handleOk = async () => {
  confirmLoading.value = true

  try {
    console.log('🚀 ~ tableList.value:', tableList.value)
    console.log('🚀 ~ selectInfo.value:', selectInfo.value)

    // 检查是否选择了表和字段
    if (!selectInfo.value || selectInfo.value.nodeType !== 3) {
      message.warning('请选择一个数据表')
      confirmLoading.value = false
      return
    }

    if (!selectedColumns.value || selectedColumns.value.length === 0) {
      message.warning('请至少选择一个字段')
      confirmLoading.value = false
      return
    }

    // 获取数据源ID - 从树结构中查找
    const datasourceId = findDatasourceIdFromTree(selectInfo.value)
    if (!datasourceId) {
      message.warning('无法获取数据源ID，请重新选择')
      confirmLoading.value = false
      return
    }

    // 构造dbColumns数据
    const dbColumns = dataSource.value.map((column: any) => ({
      columnType: column.columnType || 'varchar',
      disable: 1,
      tableId: selectInfo.value.tableId,
      comment: column.comment || '',
      columnTypeName: column.columnTypeName || column.columnType,
      title: column.title,
      key: `${selectInfo.value.tableId}_${column.title}`,
      desc: column.columnType || 'varchar',
    }))

    // 构造fields数据 - 只包含选中的字段
    const fields = selectedColumns.value.map((fieldName: string) => ({
      fieldName: fieldName,
      alias: fieldName,
      selected: true,
    }))

    // 构造activeDataBase数据
    const activeDataBase = {
      databaseId: selectInfo.value.databaseId || '',
      databaseName: selectInfo.value.databaseName || '',
      title: selectInfo.value.databaseName || '',
      databaseType: selectInfo.value.databaseType || '',
      datasourceId: datasourceId,
      datasourceName: selectInfo.value.datasourceName || '',
    }

    // 获取当前数据库下的所有表数据
    const allTablesInDatabase = getAllTablesInCurrentDatabase(selectInfo.value.databaseName)

    // 构造传递给父组件的数据
    const confirmData = {
      // 基础信息
      dataSourceId: datasourceId,
      tableId: selectInfo.value.tableId,
      tableName: selectInfo.value.tableName,
      databaseName: selectInfo.value.databaseName,

      // 字段信息
      dbColumns: dbColumns,
      fields: fields,
      checkedColumns: selectedColumns.value,

      // 数据库信息
      activeDataBase: activeDataBase,

      // 当前数据库下的所有表数据
      allTablesInCurrentDatabase: allTablesInDatabase,

      // 用于editDataList的完整数据结构
      editDataItem: {
        type: 'dataSource',
        value: {
          tableId: selectInfo.value.tableId,
          tableName: selectInfo.value.tableName,
          databaseName: selectInfo.value.databaseName,
          fields: fields,
          dbColumns: dbColumns,
        },
        preview: false,
      },
    }

    console.log('🚀 ~ 传递给父组件的数据:', confirmData)
    console.log('🚀 ~ editDataItem结构检查:', {
      type: confirmData.editDataItem.type,
      databaseName: confirmData.editDataItem.value.databaseName,
      tableName: confirmData.editDataItem.value.tableName,
      tableId: confirmData.editDataItem.value.tableId,
      fieldsCount: confirmData.editDataItem.value.fields?.length,
      dbColumnsCount: confirmData.editDataItem.value.dbColumns?.length,
    })

    // 发射确认事件
    emit('confirmData', confirmData)

    message.success('数据选择成功')
    visible.value = false
    confirmLoading.value = false
  } catch (error) {
    console.error('确认数据时出错:', error)
    message.error('确认失败，请重试')
    confirmLoading.value = false
  }
}

// 辅助函数：从树结构中查找数据源ID
const findDatasourceIdFromTree = (tableNode: any): string => {
  console.log('查找数据源ID，表节点:', tableNode)

  // 如果节点本身有datasourceId
  if (tableNode.datasourceId) {
    console.log('从表节点直接获取datasourceId:', tableNode.datasourceId)
    return tableNode.datasourceId
  }

  // 从树结构中向上查找数据库节点
  const findDatabaseNode = (nodeKey: string, nodes: TreeNode[]): any => {
    for (const node of nodes) {
      if (node.key === nodeKey) {
        return node
      }
      if (node.children) {
        const found = findDatabaseNode(nodeKey, node.children)
        if (found) return found
      }
    }
    return null
  }

  // 递归查找包含数据源ID的祖先节点
  const findDatasourceInAncestors = (nodeKey: string): string => {
    const parentKey = getParentKey(nodeKey, treeData.value)
    if (!parentKey) return ''

    const parentNode = findDatabaseNode(parentKey, treeData.value)
    if (parentNode) {
      console.log('找到父节点:', parentNode)

      // 如果父节点有datasourceId，返回
      if (parentNode.datasourceId) {
        console.log('从父节点获取datasourceId:', parentNode.datasourceId)
        return parentNode.datasourceId
      }

      // 如果父节点没有datasourceId，继续向上查找
      return findDatasourceInAncestors(parentKey)
    }

    return ''
  }

  // 查找祖先节点中的数据源ID
  const ancestorDatasourceId = findDatasourceInAncestors(tableNode.key)
  if (ancestorDatasourceId) {
    return ancestorDatasourceId
  }

  // 最后的降级方案：从原始节点数据中查找
  console.warn('无法从树结构中找到数据源ID，使用节点原始数据')
  return tableNode.datasourceId || tableNode.databaseId || ''
}

// 取消按钮处理函数
const handleCancel = () => {
  visible.value = false
  selectedRows.value = []
}

const addUser = () => {
  formState.configValue.push({
    columnName: undefined,
    operator: undefined,
    value: undefined,
    id: Date.now(),
  })
}

const handleOkColumn = () => {
  if (selectInfo.value) {
    selectInfo.value.configValue = formState.configValue
  }
  open.value = false
}

const handleCancelColumn = () => {
  open.value = false
}

const removeUser = (item: ConfigValueType) => {
  const index = formState.configValue.indexOf(item)
  if (index !== -1) {
    formState.configValue.splice(index, 1)
  }
}

const openHandleColumn = () => {
  open.value = true
  formState.configValue = selectInfo.value?.configValue || []
  paramList.value = dataSource.value
  console.log('🚀 ~ openHandleColumn ~ formState.configValue:', formState.configValue)
}

// 导出方法供父组件使用
defineExpose({
  showModal,
})
</script>

<style lang="less">
.data-assets-modal {
  .content-row {
    border: 1px solid #f0f0f0;
    min-height: 400px;
    display: flex;
    flex-wrap: nowrap;
  }

  .left-col {
    padding: 16px;
    border-right: 1px solid #f0f0f0;
    height: 600px;
    overflow-y: auto;
  }

  .right-col {
    padding: 16px;

    .button-wrapper {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      .table-name {
        font-size: 16px;
        font-weight: 600;
      }
    }
  }
  .tree-node-content {
    display: flex;
    align-items: center;
    white-space: nowrap; /* 禁止换行 */
    overflow: hidden; /* 隐藏溢出内容 */
    text-overflow: ellipsis; /* 显示省略号 */
  }
}
</style>
