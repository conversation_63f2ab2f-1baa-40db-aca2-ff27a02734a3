<template>
  <a-card class="order-page">
    <Table
      :columns="columns"
      :getData="getDeptTypeList"
      ref="tableRef"
      :searchFormState="formState"
      :pagination="() => {}"
    >
      <template #operate>
        <a-button type="primary" @click="addTable">新增</a-button>
      </template>
      <template #search>
        <a-form-item label="组织名称">
          <a-input v-model:value="formState.deptTypeName" placeholder="请输入组织名称" />
        </a-form-item>
      </template>
    </Table>
    <organization-type-modal
      ref="organizationModal"
      @modalHandleOk="modalHandleOk"
    ></organization-type-modal>
  </a-card>
</template>
<script setup lang="ts">
import { Table } from '@fs/fs-components'
import { useUserStore } from '@/stores/user'
import Modal from 'ant-design-vue/es/modal/Modal'
import { organizationTypeDTO } from '@/utils/column'
import { Button, message } from 'ant-design-vue/es/components'
import { ref, onBeforeMount, h, createVNode, watch } from 'vue'
import { ExclamationCircleOutlined } from '@ant-design/icons-vue'
import { deleteDeptType, getDeptTypeList } from '@/api/services/permission'
import type { Column } from '@fs/fs-components/src/components/table/type'
let columns = ref<Column[]>([])
const tableRef = ref<InstanceType<typeof Table> | null>(null)
const modalOpen = ref<boolean>(false)
const disabled = ref<boolean>(false)
const organizationModal = ref<any | null>(null)
const formState: Record<string, any> = ref({
  deptTypeName: '',
  isUse: 1,
})
const userStore = useUserStore()
watch(
  () => userStore.userInfo.buId,
  (Val) => {
    if (Val) {
      formState.value.buId = Val
      tableRef.value?.getTableData()
    }
  },
)

function modalHandleOk() {
  tableRef.value?.getTableData()
}

onBeforeMount(() => {
  initColumns()
})

function addTable() {
  modalOpen.value = true
  disabled.value = false
  organizationModal.value?.show('add')
}

async function initColumns() {
  columns.value = organizationTypeDTO.map((item) => {
    return {
      ...item,
      dataIndex: item.key,
    }
  })
  columns.value.push({
    key: 'action',
    title: '操作',
    customRender(data: Record<string, any>) {
      return [
        h(
          Button,
          {
            type: 'link',
            onClick() {
              organizationModal.value?.show('edit', data.record)
            },
          },
          {
            default: () => '编辑',
          },
        ),
        h(
          Button,
          {
            type: 'link',
            onClick() {
              Modal.confirm({
                title: '提示',
                icon: createVNode(ExclamationCircleOutlined),
                content: createVNode('div', { style: 'color:red;' }, '确认执行删除操作？'),
                async onOk() {
                  try {
                    return await new Promise((resolve, reject) => {
                      deleteDeptType({ deptId: data?.record?.deptTypeId })
                        .then((res: any) => {
                          if (res.code === '000000') {
                            message.success('删除成功')
                            resolve(true)
                            tableRef.value?.resetForm()
                          }
                        })
                        .catch(() => {
                          reject()
                        })
                    })
                  } catch {
                    return console.log('Oops errors!')
                  }
                },
                class: 'test',
              })
            },
          },
          {
            default: () => '删除',
          },
        ),
      ]
    },
  })
}
</script>
