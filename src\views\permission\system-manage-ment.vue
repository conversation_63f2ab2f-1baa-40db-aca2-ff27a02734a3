<template>
  <div class="order-page">
    <cardBox title="系统管理" subTitle="系统参数与基础配置管理">
      <template #headerRight>
        <a-button type="primary" @click="addTable" v-action:addSystem>新增</a-button>
      </template>
      <Table
        :columns="columns"
        :getData="getSystemList"
        ref="tableRef"
        :searchFormState="formState"
      >
        <template #search>
          <a-form-item label="" name="系统名称" :rules="[{ message: '请输入系统名称' }]">
            <a-input v-model:value="formState.systemName" placeholder="请输入系统名称" />
          </a-form-item>
          <a-form-item label="">
            <a-select v-model:value="formState.isUse" placeholder="请选择是否启用" allow-clear>
              <a-select-option value="1">是</a-select-option>
              <a-select-option value="0">否</a-select-option>
            </a-select>
          </a-form-item>
        </template>
      </Table>
    </cardBox>
    <system-modal
      :open="modalOpen"
      :disabled="disabled"
      @modalCancel="modalCancel"
      @modalHandleOk="modalHandleOk"
      ref="modalRef"
    ></system-modal>
  </div>
</template>
<script setup lang="ts">
import { ref, onBeforeMount, onMounted, h, resolveDirective, withDirectives } from 'vue'
import { useRouter } from 'vue-router'
import { Table } from '@fs/fs-components'
import { SystemManageDTO } from '@/utils/column'
import { DownOutlined } from '@ant-design/icons-vue'
import type { Column } from '@fs/fs-components/src/components/table/type'
import { Button, Dropdown, Menu, MenuItem } from 'ant-design-vue/es/components'
import { getDataDictList, getSystemList } from '@/api/services/permission'

defineOptions({
  name: 'SystemManagement',
})

const router = useRouter()
let columns = ref<Column[]>([])
const tableRef = ref<InstanceType<typeof Table> | null>(null)
interface Data {
  dataId: string
  dataName: string
}
interface FormState {
  appId: string
  appCode: string
  appName: string
  callbackUrl: string
  isUse: string
  isVerify: string
}
const modalRef = ref<any | null>(null)
const modalOpen = ref<boolean>(false)
const disabled = ref<boolean>(false)
const formState: Record<string, any> = ref({
  systemName: '',
  isUse: null,
})
const systemType = ref<Data[]>([])

onMounted(() => {
  getSystemType()
})

function modalHandleOk(formData: FormState) {
  console.log('%单点登录成功', formData)
  tableRef.value?.getTableData()
  modalCancel()
}

function modalCancel() {
  modalOpen.value = false
}

onBeforeMount(() => {
  initColumns()
})

function getSystemType() {
  const params = { dictType: 'systemType' }
  getDataDictList(params).then((res: any) => {
    systemType.value = res?.data
  })
}

function addTable() {
  modalOpen.value = true
  disabled.value = false
  const data = {
    systemTypeOptions: systemType.value,
  }
  modalRef.value?.show('add', data)
}

async function initColumns() {
  columns.value = SystemManageDTO.map((item) => {
    return {
      ...item,
      dataIndex: item.key,
    }
  })
  columns.value.unshift({
    key: 'index',
    title: '序号',
    width: 80,
    customRender: (data: Record<string, any>) => `${data.index + 1}`,
  })
  columns.value.push({
    key: 'action',
    title: '操作',
    width: 180,
    customRender(data: Record<string, any>) {
      return [
        withDirectives(
          h(
            Button,
            {
              type: 'link',
              onClick() {
                operationHandle('0', data.record)
              },
            },
            {
              default: () => '编辑',
            },
          ),
          [[resolveDirective('action'), '', 'updateSystem']],
        ),
        withDirectives(
          h(
            Button,
            {
              type: 'link',
              onClick() {
                operationHandle('1', data.record)
              },
            },
            {
              default: () => '系统详情',
            },
          ),
          [[resolveDirective('action'), '', 'systemDetails']],
        ),
      ]
    },
  })
}

function operationHandle(key: string, record: Record<string, any>) {
  if (key === '0') {
    modalOpen.value = true
    disabled.value = true
    record['systemTypeOptions'] = systemType.value
    modalRef.value?.show('edit', record)
  } else if (key === '1') {
    const { systemId, systemName } = record
    router.push({
      path: `/detailSystem`,
      query: { systemName: systemName, systemId },
    })
  }
}
</script>

<style lang="less">
.ant-dropdown-menu {
  .ant-dropdown-menu-item:hover {
    background-color: transparent !important;
  }
}
.order-page {
  height: 100%;
  background: white;
}
</style>
