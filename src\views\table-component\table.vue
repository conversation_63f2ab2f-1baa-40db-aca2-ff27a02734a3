<template>
  <a-table :columns="tableColumns" :data-source="dataSource" v-bind="bindValue"> </a-table>
</template>

<script setup lang="ts">
import { computed } from 'vue'

const props = defineProps({
  dataSource: {
    type: Array,
    default: () => [],
  },
  columns: {
    type: Array,
    default: () => [],
  },
  loading: {
    type: Boolean,
    default: false,
  },
  scroll: {
    type: Object,
    default: () => ({}),
  },
})

console.log('[ props.dataSource ] >', props.dataSource)

const tableColumns = computed(() => {
  return props.columns.map((item: any) => {
    return {
      ...item,
      ellipsis: true,
      width: 150,
    }
  })
})

const bindValue = computed(() => {
  return {
    size: 'small',
    bordered: true,
    pagination: false,
    scroll: { y: 350 },
    loading: props.loading,
  }
})
</script>

<style lang="less" scoped></style>
