<template>
  <a-modal
    destroyOnClose
    v-model:open="open"
    :title="`${modifyFlag ? '修改' : '新增'}模型`"
    @ok="handleOk"
  >
    <a-form ref="modalFormRef" :model="modalFormState" name="addModelForm" :label-col="{ span: 6 }">
      <a-form-item
        name="modelName"
        label="模型名称"
        :rules="[{ required: true, message: '模型名称不能为空' }]"
      >
        <a-input v-model:value="modalFormState.modelName" placeholder="例: xx模型" allowClear />
      </a-form-item>
      <a-form-item
        name="host"
        label="模型服务地址"
        :rules="[
          { required: true, message: '模型服务地址不能为空' },
          {
            pattern:
              /^(?:(?:https?:\/\/)?((25[0-5]|2[0-4]\d|1\d{2}|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d{2}|[1-9]?\d)){3}|([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}))(:\d{1,5})?$/,
            message: '请输入合法的IP或域名地址',
          },
        ]"
      >
        <a-input v-model:value="modalFormState.host" placeholder="例: *************" allowClear />
      </a-form-item>
      <a-form-item
        name="port"
        label="端口"
        :rules="[
          { required: true, message: '端口不能为空' },
          {
            pattern: /^(6553[0-5]|655[0-2]\d|65[0-4]\d{2}|6[0-4]\d{3}|[1-9]\d{0,3})$/,
            message: '请输入1~65535之间的端口号',
          },
        ]"
      >
        <a-input v-model:value="modalFormState.port" placeholder="例：8080" allowClear />
      </a-form-item>
      <a-form-item
        name="modelType"
        label="模型类别"
        :rules="[{ required: true, message: '模型类别不能为空' }]"
      >
        <a-select
          :options="modelTypeList"
          :fieldNames="{ label: 'dataName', value: 'dataId' }"
          v-model:value="modalFormState.modelType"
          placeholder="请选择模型类别"
          allowClear
          show-search
        ></a-select>
      </a-form-item>
      <a-form-item name="modelDesc" label="模型描述">
        <a-textarea
          v-model:value="modalFormState.modelDesc"
          placeholder="请输入模型描述"
          allowClear
        />
      </a-form-item>
    </a-form>
    <template #footer>
      <a-button @click="open = false">取消</a-button>
      <a-button
        type="primary"
        @click="handleVerify"
        :disabled="!modalFormState.host || !modalFormState.port"
        >校验</a-button
      >
      <a-button type="primary" @click="handleOk">确定</a-button>
    </template>
  </a-modal>
</template>
<script lang="ts" setup>
import { ref, reactive, defineExpose } from 'vue'

const props = defineProps<{
  modelTypeList: any[]
}>()
const emits = defineEmits(['handleSubmit'])

import { message, type FormInstance } from 'ant-design-vue'
import { reqCheckModel } from './api'

const modalFormRef = ref<FormInstance | null>(null)

const open = ref<boolean>(false)
const modalFormState = reactive({
  modelName: '', // 模型名称
  host: '', // 模型服务地址
  port: '', // 端口
  modelType: undefined, // 模型类别
  modelDesc: '', // 模型描述
})
const id = ref<string | number>('')

const modifyFlag = ref<boolean>(false)

const showModal = (flag = true, record?: any) => {
  if (record) {
    modifyFlag.value = true
    modalFormState.modelName = record.modelName
    modalFormState.host = record.host
    modalFormState.port = record.port
    modalFormState.modelType = record.type
    modalFormState.modelDesc = record.modelDesc
    id.value = record.id
  } else {
    modifyFlag.value = false
    modalFormState.modelName = ''
    modalFormState.host = ''
    modalFormState.port = ''
    modalFormState.modelType = undefined
    modalFormState.modelDesc = ''
    id.value = ''
  }
  open.value = flag
}

const handleOk = (e: MouseEvent) => {
  console.log(e)
  modalFormRef.value?.validate().then((res) => {
    console.log('%c [ res ]-50', 'font-size:13px; background:#1049c4; color:#548dff;', res)
    if (id.value) {
      res.id = id.value
    }
    emits('handleSubmit', res, modifyFlag.value)
  })
  // open.value = false
}

const handleVerify = async () => {
  const params = {
    host: modalFormState.host,
    port: modalFormState.port,
  }
  const res = await reqCheckModel(params)
  if (res.result === '1') {
    message.success(res.data)
  } else {
    message.error(res.data)
  }
}

defineExpose({
  showModal,
})
</script>
