<template>
  <div class="indicator-list-container">
    <cardBox title="指标管理" subTitle="用于创建、编辑管理，查询、筛选指标列表">
      <template #headerRight>
        <template-modal></template-modal>
      </template>
      <Table
        :columns="COLUMS"
        :getData="getData"
        :table-config="{
          scroll: { x: '1800px' },
        }"
        :pagination="pageEnterprisePagination"
        ref="tableRef"
        @reset="resetSeachForm"
        :searchFormState="form"
      >
        <template #search>
          <a-form-item label="" name="businessType">
            <a-select
              v-model:value="form.businessType"
              :options="businessTypeOptions"
              placeholder="请选择模板业务类型"
              @change="getTempData"
            />
          </a-form-item>
          <a-form-item label="" name="indexName">
            <a-input v-model:value="form.indexName" placeholder="请输入指标名称" />
          </a-form-item>
          <a-form-item label="" name="groupId">
            <a-tree-select
              v-model:value="value"
              show-search
              tree-default-expand-all
              style="width: 100%; background-color: #fff"
              :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
              placeholder="请选择指标分组"
              allow-clear
              :tree-data="groupTreeData"
              tree-node-filter-prop="label"
            >
              <template #title="{ value: val, label }">
                <b v-if="val === 'parent 1-1'" style="color: #08c">sss</b>
                <template v-else>{{ label }}</template>
              </template>
            </a-tree-select>
          </a-form-item>
          <a-form-item label="" name="status">
            <a-select
              ref="select"
              v-model:value="form.isRelease"
              allow-clear
              placeholder="请选择是否发布"
            >
              <a-select-option value="">全部</a-select-option>
              <a-select-option value="1">是</a-select-option>
              <a-select-option value="0">否</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item v-for="item in fcRule" :key="item.id" label="" :name="item.field">
            <a-input
              v-if="item.type === 'input'"
              v-model:value="tempForm[item.field]"
              :placeholder="'请输入' + item.title"
            />

            <a-select
              v-else-if="['select', 'radio'].includes(item.type)"
              :placeholder="'请选择' + item.title"
              v-model:value="tempForm[item.field]"
              allow-clear
            >
              <a-select-option v-for="i in item.options" :key="i.value" :value="i.value">{{
                i.label
              }}</a-select-option>
            </a-select>
            <a-time-picker
              v-else-if="item.type === 'timePicker'"
              v-model:value="tempForm[item.field]"
              :placeholder="'请选择' + item.title"
            />
            <a-date-picker
              v-else-if="item.type === 'datePicker' && item._fc_drag_tag === 'datePicker'"
              v-model:value="tempForm[item.field]"
              :placeholder="'请选择' + item.title"
            />
            <a-range-picker
              v-else-if="item.type === 'datePicker' && item._fc_drag_tag === 'dateRange'"
              v-model:value="tempForm[item.field]"
              :placeholder="'请选择' + item.title"
            />
          </a-form-item>
        </template>
        <template #bodyCell="data">
          <template v-if="data.column.dataIndex === 'action'">
            <a-popconfirm title="是否确认发布" @confirm="hanleDeploy(data.record)">
              <a-button size="small" type="link">发布</a-button>
            </a-popconfirm>

            <a-button size="small" type="link" @click="editTableHandle(data.record)">编辑</a-button>
            <a-popconfirm title="是否确认删除" @confirm="confirm(data.record)">
              <a-button size="small" type="link">删除</a-button>
            </a-popconfirm>
            <a-button size="small" type="link" @click="handlePreview(data.record)"
              >查看详情</a-button
            >
            <a-button type="link" @click="openHistory(data.record)">历史记录</a-button>
            <ApiButton :autoId="data.record.autoId" :indexName="data.record.indexName" />
          </template>
        </template>
      </Table>
    </cardBox>
    <History v-model="open" :formId="formId"></History>
    <a-modal
      wrap-class-name="full-modal"
      width="80%"
      v-model:open="detailsOpen"
      :title="detailData?.indexName || '指标详情'"
      @ok="handleDetailOk"
      @cancel="handleDetailCancel"
      :destroyOnClose="true"
    >
      <Details
        :detailData="detailData"
        :formId="detailData?.formDataId || ''"
        :previewType="previewType"
        :previewSql="previewSql"
        :databaseName="databaseName"
      ></Details>
    </a-modal>

    <a-modal
      v-model:open="modalValue"
      @ok="modalHandleOk"
      :title="modalType === 'edit' ? '编辑' : '新增'"
      @cancel="cancel"
      width="800px"
      :confirm-loading="confirmLoading"
    >
      <a-form
        ref="formRef"
        :model="modalForm"
        :rules="rules"
        :label-col="labelCol"
        :wrapper-col="wrapperCol"
      >
        <a-form-item
          label="指标配置模板版本编号"
          name="formHistoryId"
          :rules="[{ required: true, message: '请输入指标配置模板版本编号' }]"
        >
          <a-input
            v-model:value="modalForm.formHistoryId"
            placeholder="请输入指标配置模板版本编号"
          />
        </a-form-item>

        <a-form-item
          label="指标名称"
          name="indexName"
          :rules="[{ required: true, message: '请输入指标名称' }]"
        >
          <a-input v-model:value="modalForm.indexName" placeholder="请输入指标名称" />
        </a-form-item>

        <a-form-item
          label="分组编号"
          name="groupId"
          :rules="[{ required: false, message: '请输入分组编号' }]"
        >
          <a-input v-model:value="modalForm.groupId" placeholder="请输入分组编号" />
        </a-form-item>

        <a-form-item
          label="指标父id"
          name="parentId"
          :rules="[{ required: false, message: '请输入指标父id' }]"
        >
          <a-input v-model:value="modalForm.parentId" placeholder="请输入指标父id" />
        </a-form-item>

        <a-form-item
          label="计算公式/规则逻辑"
          name="calFormula"
          :rules="[{ required: false, message: '请输入计算公式/规则逻辑' }]"
        >
          <a-input v-model:value="modalForm.calFormula" placeholder="请输入计算公式/规则逻辑" />
        </a-form-item>

        <a-form-item
          label="数据源编号"
          name="dataSourceId"
          :rules="[{ required: false, message: '请输入数据源编号' }]"
        >
          <a-input v-model:value="modalForm.dataSourceId" placeholder="请输入数据源编号" />
        </a-form-item>

        <a-form-item
          label="指标配置数据"
          name="formData"
          :rules="[{ required: false, message: '请输入指标配置数据' }]"
        >
          <a-input v-model:value="modalForm.formData" placeholder="请输入指标配置数据" />
        </a-form-item>

        <a-form-item
          label="指标计算规则列表"
          name="basicRuleList"
          :rules="[{ required: false, message: '请输入指标计算规则列表' }]"
        >
          <a-input v-model:value="modalForm.basicRuleList" placeholder="请输入指标计算规则列表" />
        </a-form-item>

        <a-form-item
          label="描述"
          name="remarks"
          :rules="[{ required: false, message: '请输入描述' }]"
        >
          <a-input v-model:value="modalForm.remarks" placeholder="请输入描述" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { Table } from '@fs/fs-components'
import type { Rule } from 'ant-design-vue/es/form'
import { message } from 'ant-design-vue/es/components'
import FcDesigner from '@form-create/designer'
import {
  addNewIndexData,
  modifyIndexData,
  deployIndexData,
  getIndexDataListByPages,
  getIndexGroupTreeList,
} from '@/api/indexmanage/indexmanage/indexmanage'
import { getIndexFormByType } from '@/api/indexmanage/indexform/indexform'
import { getIndexbusinessList } from '@/api/indexmanage/bussinesmanage/bussinesmanage'
import Details from './details.vue'
import templateModal from './template-modal.vue'
import type { IndexListItem } from '@/api/services/indicator/type'
import History from './history.vue'
import Title from '@/components/title/title.vue'
import { COLUMS } from './const'
import ApiButton from './api.vue'

const router = useRouter()
const labelCol = { span: 5 }
const wrapperCol = { span: 13 }
const form: Record<string, any> = ref({
  indexName: '',
  isRelease: '',
  groupId: undefined,
})
const value = ref<string>()
console.log('🚀 ~ form:', form)
const tempForm: any = ref({})
const fcRule: any = ref([])
const fcOption: any = ref({})
const open = ref(false)
const detailsOpen = ref(false)
const formId = ref('')
const previewType = ref(1)
const previewSql = ref('')
const databaseName = ref('')

const rules: Record<string, Rule[]> = {}
const modalForm: Record<string, any> = ref({})
const detailData = ref<IndexListItem>()
const modalValue = ref(false)
const modalType = ref<string>('')
const formRef = ref<HTMLFormElement | null>(null)
const confirmLoading = ref<boolean>(false)
const tableRef = ref<InstanceType<typeof Table> | null>(null)
const indicatorsTemplate: any = ref()
const pageEnterprisePagination = ref({
  pageIndex: 1,
  pageSize: 10,
})
const businessTypeOptions = ref<any[]>([])
const groupTreeData = ref<any[]>([])
const transformData = (data: any[]) => {
  return data.map((item) => {
    const newItem = {
      ...item,
      label: item.groupName,
      value: item.groupId,
    }
    if (item.children) {
      newItem.children = transformData(item.children)
    }
    return newItem
  })
}
const getTreeData = async () => {
  const res = await getIndexGroupTreeList()
  groupTreeData.value = transformData(res.data)
}
getTreeData()
function handleDetailOk() {
  detailsOpen.value = false
}
function handleDetailCancel() {
  detailsOpen.value = false
}

function editTableHandle(data: any) {
  router.push('/indicator-list/edit?id=' + data.autoId)
  // modalForm.value = data
  // modalType.value = 'edit'
  // modalValue.value = true
}

const getData = () => {
  // 过滤掉 tempForm 中值为 null 的字段
  const filteredTempForm = Object.entries(tempForm.value).reduce<Record<string, any>>(
    (acc, [key, value]) => {
      if (value) {
        acc[key] = value
      }
      return acc
    },
    {},
  )
  console.log('filteredTempForm', filteredTempForm)
  return getIndexDataListByPages({
    pageIndex: pageEnterprisePagination.value.pageIndex,
    pageSize: pageEnterprisePagination.value.pageSize,
    formData: Object.keys(filteredTempForm).length > 0 ? JSON.stringify(filteredTempForm) : '',
    ...form.value,
  })
}

function cancel() {
  modalValue.value = false
  formRef.value?.resetFields()
}

const handlePreview = (record: IndexListItem) => {
  previewType.value = record.basicRuleList[0]?.ruleType || 1
  previewSql.value = record.basicRuleList[0]?.ruleScript || ''
  databaseName.value = record.basicRuleList[0]?.databaseName || ''
  console.log('previewType', previewSql.value, databaseName.value)
  detailData.value = record
  formId.value = record.formDataId
  detailsOpen.value = true
}

async function confirm(data: any) {
  console.log('🚀 ~ confirm ~ data:', data)
  try {
    await modifyIndexData({ ...data, isDeleted: 0 })
    tableRef.value?.getTableData()
    message.success('删除成功')
  } catch (error) {
    console.log(error)
  }
}

const resetSeachForm = () => {
  form.value = {}
  Object.keys(tempForm.value).forEach((key) => {
    tempForm.value[key] = null
  })
  tableRef.value?.getTableData()
}

const hanleDeploy = async (record: any) => {
  const id = record.autoId
  const res: any = await deployIndexData({ autoId: id })
  if (res.code === '000000') {
    message.success('发布成功')
    tableRef.value?.getTableData()
  }
}

function openHistory(record: any) {
  formId.value = record.autoId
  open.value = true
}

async function modalHandleOk() {
  try {
    if (!formRef.value) {
      console.error('获取form表单失败')
      return
    }
    await formRef.value.validate()
    confirmLoading.value = true
    let handle: any = null
    if (modalType.value === 'edit') {
      handle = modifyIndexData
    } else {
      handle = addNewIndexData
    }
    await handle(modalForm.value)
    tableRef.value?.getTableData()
    message.success('操作成功')
    modalValue.value = false
    confirmLoading.value = false
  } catch (error) {
    confirmLoading.value = false
    console.log(error)
  }
}

const getTempData = async () => {
  try {
    const { data } = await getIndexFormByType({ businessType: +form.value.businessType })
    indicatorsTemplate.value = data
    const fcFieldsList = FcDesigner.formCreate.parseJson(data.fieldsData)
    fcRule.value = fcFieldsList.filter((item: any) => {
      return item.type !== 'checkbox' && item.type !== 'switch'
    })
    fcOption.value = JSON.parse(FcDesigner.formCreate.parseJson(data.compData))
    fcRule.value.forEach((item: any) => {
      tempForm.value[item.field] = null
    })
  } catch (error) {
    console.log('error', error)
  }
}

async function getBusinessTypeOptions() {
  try {
    const { data } = await getIndexbusinessList({})
    const list = data
      .filter((item: any) => Number(item.formCount) > 0)
      .map((item: any) => {
        return {
          label: item.businessName,
          value: item.businessId,
        }
      })
    console.log('list', list)
    businessTypeOptions.value = list
    if (list.length > 0) {
      form.value.businessType = businessTypeOptions.value[0].value
      getTempData()
    }
  } catch (error) {
    console.error('获取业务类型失败', error)
  }
}

function getGroupOptions() {
  console.log('获取指标分组选项')
}

getBusinessTypeOptions()
getGroupOptions()
</script>
<style lang="less">
.indicator-list-container {
  background: white;
  height: 100%;
}
// #form_item_groupId {
//   background-color: #fff !important;
// }
</style>
