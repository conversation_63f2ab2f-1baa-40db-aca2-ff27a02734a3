<script lang="ts" setup>
import { ExclamationCircleOutlined, PlusOutlined } from '@ant-design/icons-vue'

import { orderBy } from 'lodash'
import cardBox from '@/components/card-box/card-box.vue'
import { getDbTypeList } from '../utils'
import { columns } from '../utils'
import { checkServer, deleteDataSource, reqGetDataSourceList } from '../api'
import { useUserStore } from '@/stores/user'
import { useRouter } from 'vue-router'
import { createVNode, onMounted, reactive, ref } from 'vue'
import { message, Modal } from 'ant-design-vue'
import { getDbIcon } from '@/utils/utils'
import { logModal } from '@/utils/log-modal'
import { Table } from '@fs/fs-components'

defineOptions({
  name: 'setting/dataSource',
})

const userStore = useUserStore()
const router = useRouter()

const formMsg = reactive({
  dbType: null,
  dbDesc: '',
  dbUrl: '',
  ownerId: userStore.userInfo.loginAccount,
})

const sourceDataList = ref<any>([])
const pageIndex = ref(1)
const pageSize = ref(10)
const total = ref(0)
const loading = ref(false)

const dbTypeList = ref(orderBy(getDbTypeList().concat([]), ['label'], ['asc']))

onMounted(() => {
  getDataSourceList()
})

const getDataSourceList = async (searchParams: any = {}) => {
  loading.value = true
  const res = await reqGetDataSourceList({
    pageIndex: pageIndex.value,
    pageSize: pageSize.value,
    ownerId: userStore.userInfo.loginAccount,
    ...searchParams,
  }).finally(() => {
    loading.value = false
  })
  sourceDataList.value = res.data.records
  total.value = res.data.totalRecords
}

const handleCheck = (record: any) => {
  const params = { id: record.id, isRunning: true }
  Modal.confirm({
    title: '提示',
    icon: createVNode(ExclamationCircleOutlined),
    content: '确定要检查吗?',
    async onOk() {
      const res = await checkServer(params)
      logModal(res, '检查', true, 60)
      console.log('%c [ res ]-79', 'font-size:13px; background:#2cb1a1; color:#70f5e5;', res)
    },
    onCancel() {},
  })
}

const handleEdit = (record: any) => {
  router.push('/setting/editService?id=' + record.id)
}

const handleDelete = (id: any) => {
  Modal.confirm({
    title: '提示',
    icon: createVNode(ExclamationCircleOutlined),
    content: createVNode('div', {}, '确认要删除吗？'),
    async onOk() {
      const params = { id, ownerId: userStore.userInfo.loginAccount }
      await deleteDataSource(params)
      message.success('删除成功')
      pageIndex.value = 1
      getDataSourceList({
        pageIndex: 1,
      })
    },
    onCancel() {},
  })
}

const goMetaDataManagement = (record: any) => {
  // mysql TiDB sqlserver oracle dm gbase hive postgresql greenplum kingbase   支持元数据
  if ([1, 3, 5, 4, 6, 12, 13, 16, 19, 29].includes(record.dbType as number)) {
    router.push(`/meta-data-management?datasourceId=${record.id}`)
  } else {
    message.info('元数据提取暂不支持该数据类型')
  }
}

const handleGoAdd = () => {
  router.push('/setting/addService')
}

const handleCheckDetail = (text: any) => {
  logModal(text, '查看详情', true, 60)
}
</script>

<template>
  <div class="data-source-page">
    <cardBox title="数据源配置" subTitle="数据源配置列表">
      <template #headerRight>
        <a-button type="primary" @click="handleGoAdd"><PlusOutlined />新增</a-button>
      </template>
      <Table
        :columns="columns"
        :getData="reqGetDataSourceList"
        ref="tableRef"
        :searchFormState="formMsg"
      >
        <template #search>
          <a-form-item name="dbType">
            <a-select
              v-model:value="formMsg.dbType"
              optionFilterProp="label"
              showSearch
              allowClear
              placeholder="请选择数据库类型"
            >
              <a-select-option
                v-for="(option, optKey) in dbTypeList"
                :key="optKey"
                :label="option.label"
                :value="option.value"
                >{{ option.label }}</a-select-option
              >
            </a-select>
          </a-form-item>
          <a-form-item name="dbDesc">
            <a-input v-model:value="formMsg.dbDesc" allowClear placeholder="请输入描述信息" />
          </a-form-item>
          <a-form-item name="dbUrl">
            <a-input v-model:value="formMsg.dbUrl" allowClear placeholder="请输入连接URL" />
          </a-form-item>
        </template>
        <template #bodyCell="{ column, text, record }">
          <template v-if="column.key === 'dbType'">
            <div style="width: 100%; text-align: left; display: flex; align-items: center">
              <img
                :src="getDbIcon(text)"
                alt=""
                style="margin-right: 10px; width: 20px; height: 20px"
              />
              <span>{{
                dbTypeList.find((i) => i.value === Number(record.dbType))?.label || '--'
              }}</span>
            </div>
          </template>
          <template v-if="column.key === 'dbDesc'"
            ><a @click="goMetaDataManagement(record)">{{ text ? text : '--' }}</a></template
          >
          <template v-if="column.key === 'dbUrl'"
            ><a @click="handleCheckDetail(text)">{{ text ? text : '--' }}</a></template
          >
          <template v-if="column.key === 'dbPassword'">{{ text ? '******' : '--' }}</template>
          <template v-if="column.key === 'operation'">
            <a-button type="link" size="small" @click="() => goMetaDataManagement(record)"
              >元数据</a-button
            >
            <a-button type="link" size="small" @click="handleCheck(record)">检查</a-button>
            <a-button type="link" size="small" @click="() => handleEdit(record)">修改</a-button>
            <a-button type="link" size="small" @click="handleDelete(record.id)">删除</a-button>
          </template>
        </template>
      </Table>
    </cardBox>
  </div>
</template>

<style lang="less" scoped>
.data-source-page {
  height: calc(100vh - 40px);
  padding: 24px;
  :deep(.box-form-g) {
    margin-bottom: 0;
  }
}
</style>
