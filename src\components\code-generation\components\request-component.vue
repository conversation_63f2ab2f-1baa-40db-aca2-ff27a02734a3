<template>
  <a-table :dataSource="modelValue" :columns="columns">
    <template #bodyCell="{ column, record }">
      <template v-if="column.key === 'name'">
        {{ record[column.key] }}
      </template>
      <template v-else>
        <a-select
          @change="selectHandle(record)"
          v-model:value="record[column.key]"
          style="width: 500px"
          placeholder="请选择类型"
          allowClear
        >
          <a-select-option
            v-for="item in options"
            :key="item.interfaceName"
            :value="item.interfaceName"
            >{{ item.interfaceNameCn }} {{ item.interfaceName }}</a-select-option
          >
        </a-select>
      </template>
    </template>
  </a-table>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { getData } from '../code-generation'

interface Columns {
  modelValue: any[]
  options: any[]
  type?: string
}

const emit = defineEmits(['update:modelValue', 'updateSource', 'updateSearch', 'updateForm'])

const props: Columns = withDefaults(defineProps<Columns>(), {
  modelValue: () => [],
  options: () => [],
})

const columns = ref([
  {
    title: '方法名',
    dataIndex: 'name',
    key: 'name',
    width: 300,
  },
  {
    title: '请求函数',
    dataIndex: 'api',
    key: 'api',
  },
])

const upData = () => {
  emit('update:modelValue', props.modelValue)
}

const selectHandle = async (value: any) => {
  if (value.name === '查询') {
    const { data } = await getData({ interfaceName: value.api })
    if (data?.data?.response) {
      const response = data?.data?.response.filter((item: { name: string }) => item.name === 'data')
      if (response.length > 0) {
        const columns = response[0].type.map((item: any) => {
          return item
        })
        emit('updateSource', columns)
      }
    } else {
      emit('updateSource', [])
    }
    const params = JSON.parse(JSON.stringify(data.data?.params || []))
    if (params.length > 0 && props.type === 'table') {
      const arr = params.filter(
        (item: { name: string }) => !['pageIndex', 'pageSize'].includes(item.name),
      )

      arr.forEach((element: { type: string; isUse: boolean; span: number }) => {
        element.type = 'input'
      })
      emit('updateSearch', arr)
    } else {
      emit('updateSearch', [])
    }
  }
  if (value.name === '新增') {
    const { data } = await getData({ interfaceName: value.api })
    const params = data?.data?.params || []
    params.forEach((element: { type: string; isUse: boolean; span: number }) => {
      element.type = 'input'
      element.span = 24
    })

    emit('updateForm', params)
  }
}

// 导出 show 方法
defineExpose({
  upData,
})
</script>
<style scoped lang="less">
.table-columns-add {
  text-align: right;
  margin-bottom: 16px;
}
</style>
