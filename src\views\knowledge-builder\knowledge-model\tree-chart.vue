<template>
  <!-- <a-row>
    <a-col :span="24">
      <div ref="chartRef" class="chart-content" style="width: 100%; height: 800px"></div>
    </a-col>
  </a-row> -->
  <div class="tree-wrap" ref="contentRef">
    <div class="tree-container" ref="chartRef"></div>
    <Dragger ref="draggerRef" :currentEchartsData="currentEchartsData" :chartData="schemaData" />
  </div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted, ref, reactive, nextTick, watch } from 'vue'
import * as echarts from 'echarts/core'
import type { TreeSeriesOption } from 'echarts/charts'
import { TreeChart } from 'echarts/charts'
import { TooltipComponent } from 'echarts/components'
import { CanvasRenderer } from 'echarts/renderers'
import Dragger from '@/views/knowledge-builder/knowledge-task/dragger/index.vue'
import type { ECBasicOption } from 'echarts/types/dist/shared'

type TreeSeriesNodeItemOption = NonNullable<
  TreeSeriesOption['data'] // 获取data属性的类型
>[number] // 获取数组元素类型
const props = defineProps<{ treeData: any; schemaData?: any }>()
echarts.use([TooltipComponent, TreeChart, CanvasRenderer])

const chartRef = ref<HTMLElement>()
const currentEchartsData = ref()
const draggerRef = ref<{ showDrawer: () => void } | null>(null)
let chartInstance: echarts.ECharts | null = null
const option = reactive<ECBasicOption>({
  tooltip: {
    trigger: 'item',
    formatter: (params: any) => {
      if (!params?.data) return ''
      const node = params.data?.entityTypeDTO
      return `
      ${node.nameZh}<br/>
      ${node.name || ''}
    `
    },
  },
  series: [
    {
      type: 'tree',
      width: '20%',
      data: [props.treeData],
      orient: 'LR', // 垂直方向布局
      layout: 'orthogonal',
      expandAndCollapse: true, // 展开折叠
      roam: true, // 开启平移
      // 节点样式配置
      itemStyle: {
        color: '#1890ff', // 蓝色节点
        borderWidth: 0,
      },
      left: '20%',

      // 标签样式
      label: {
        color: 'white',
        padding: [5, 5],
        backgroundColor: '#818aa5',
        fontSize: 14,
        position: 'left',
        verticalAlign: 'middle',
        align: 'left',
        distance: 10,
        formatter: function (param) {
          console.log('param', param)

          return `${param?.data?.entityTypeDTO?.nameZh}(${param?.data?.entityTypeDTO?.name})`
        },
      },

      // 叶子节点特殊配置
      leaves: {
        label: {
          backgroundColor: 'transparent',
          color: '#7d7d7d',
          distance: 15,
          position: 'inside',
          verticalAlign: 'middle',
          align: 'left',
        },
      },

      // 连接线样式
      lineStyle: {
        color: '#1890ff',
        width: 2,
        curveness: 0.5, // 边的曲度
      },

      // 符号样式（圆点标记）
      symbol: 'circle',
      symbolSize: 8,
    },
  ],
})

const showDrawerHandler = (params: any) => {
  const obj = {
    ...params.data.entityTypeDTO,
    children: params.data.children,
  }
  currentEchartsData.value = obj
  draggerRef.value?.showDrawer?.()
}

const initRelationshipChart = () => {
  chartInstance = echarts.init(chartRef.value)
  chartInstance.setOption(option)
  chartInstance.on('click', showDrawerHandler)
}

// 窗口自适应
const resizeHandler = () => {
  console.log('窗口变化啦')
  setTimeout(function () {
    chartInstance?.resize()
  }, 200)
}

watch(
  () => props.treeData,
  (newVal) => {
    console.log('有变化啦~~~', newVal, chartRef)

    if (newVal?.children?.length) {
      nextTick(() => {
        initRelationshipChart()
      })
    }
  },
  { immediate: true, deep: true },
)

onMounted(() => {
  window.addEventListener('resize', resizeHandler)
})
onUnmounted(() => {
  window.removeEventListener('resize', resizeHandler)
  chartInstance?.dispose()
})
</script>
<style lang="less" scoped>
.tree-wrap {
  width: 100%;
  height: 100%;
  background: #fff;
  position: relative;
  right: 0;
  border-radius: 4px;
}
.tree-container {
  box-sizing: border-box;
  overflow: hidden;
  // width: 100%;
  height: 100%;
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.1);
}
</style>
