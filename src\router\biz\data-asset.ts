import type { RouteRecordRaw } from 'vue-router'

/**
 * 数据资产目录
 */
const routes: RouteRecordRaw[] = [
  {
    path: '/data-asset-management',
    name: 'data-asset-management',
    component: () => import('@/views/data-asset/data-asset-management/index.vue'),
    meta: {
      title: '数据资产管理',
    },
  },
  {
    path: '/metadata-query',
    name: 'metadata-query',
    component: () => import('@/views/data-asset/metadata-query/index.vue'),
    meta: {
      title: '元数据查询',
      keepAlive: false,
    },
  },
  {
    path: '/table-detail',
    name: 'table-detail',
    component: () => import('@/views/data-asset/table-detail/index.vue'),
    meta: {
      isSecondary: true,
      title: '数据表详情',
    },
  },
  {
    path: '/metadata-detail',
    name: 'metadata-detail',
    component: () => import('@/views/data-asset/metadata-detail/index.vue'),
    meta: {
      title: '元数据表详情',
    },
  },
  {
    path: '/data-asset-management/auth/:authId',
    name: 'authPage',
    component: () => import('@/views/data-asset/data-asset-management/auth-page/index.vue'),
    meta: {
      title: '数据资产管理-权限配置',
    },
  },
  {
    path: '/subscription-settings',
    name: 'subscription-settings',
    component: () => import('@/views/data-asset/subscription-settings/index.vue'),
    meta: {
      title: '订阅配置管理',
    },
  },
  {
    path: '/tag-management',
    name: 'tag-management',
    component: () => import('@/views/data-asset/tag-management/index.vue'),
    meta: {},
  },
  {
    path: '/subscriber-management',
    name: 'subscriber-management',
    component: () => import('@/views/data-asset/subscriber-management/index.vue'),
    meta: {},
  },
  {
    path: '/hbase-management',
    name: 'hbase-management',
    component: () => import('@/views/data-asset/hbase-management/index.vue'),
    meta: {
      title: 'HBase存储管理',
      keepAlive: true,
    },
  },
  {
    path: '/hbase-table-detail',
    name: 'hbase-table-detail',
    component: () => import('@/views/data-asset/hbase-table-detail/index.vue'),
    meta: {
      isSecondary: true,
      title: '数据表详情',
    },
  },
  {
    path: '/distributed-file-management',
    name: 'distributed-file-management',
    component: () => import('@/views/data-asset/distributed-file-management/index.vue'),
    meta: {
      title: 'HDFS文件管理',
      keepAlive: true,
    },
  },
  {
    path: '/sparkManage',
    name: 'sparkManage',
    component: () => import('@/views/data-asset/spark-manage/index.vue'),
    meta: {
      title: '内存计算处理',
      keepAlive: true,
    },
  },
  {
    path: '/flinkManage',
    name: 'flinkManage',
    component: () => import('@/views/data-asset/flink-manage/index.vue'),
    meta: {
      title: '实时计算处理',
      keepAlive: true,
    },
  },
  {
    path: '/approvalManage',
    name: 'approvalManage',
    component: () => import('@/views/data-asset/approval-manage/index.vue'),
    meta: {
      title: '审核管理',
      keepAlive: true,
    },
  },
]
export default routes
