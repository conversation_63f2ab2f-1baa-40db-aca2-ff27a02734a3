<template>
  <div class="create-router">
    <a-form
      :model="formState"
      name="basic"
      :label-col="{ span: 8 }"
      :wrapper-col="{ span: 16 }"
      autocomplete="off"
      @finish="onFinish"
      @finishFailed="onFinishFailed"
    >
      <a-form-item
        label="路由名称"
        name="menuName"
        :rules="[{ required: true, message: '请输入路由名称' }]"
      >
        <a-input v-model:value="formState.menuName" placeholder="请输入路由名称" />
      </a-form-item>

      <a-form-item
        label="路由地址"
        name="menuUrl"
        :rules="[{ required: true, message: '请输入路由地址' }]"
      >
        <a-input v-model:value="formState.menuUrl" placeholder="请输入路由地址" />
      </a-form-item>

      <a-form-item
        label="路由标识"
        name="actionName"
        :rules="[{ required: true, message: '请输入路由标识' }]"
      >
        <a-input v-model:value="formState.actionName" placeholder="请输入路由标识" />
      </a-form-item>

      <a-form-item :wrapper-col="{ offset: 17, span: 4 }">
        <a-button type="primary" html-type="submit">确定</a-button>
      </a-form-item>
    </a-form>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { addMenu } from '@/api/services/permission'
import { getUserMenuFn } from '@/utils/user'

interface FormState {
  menuName: string
  menuUrl: string
  actionName: string
}

const formState = ref<FormState>({
  menuName: '',
  menuUrl: '',
  actionName: '',
})

const father = {
  parentId: '6763d0e59fabe8551b3ad2d1',
  systemId: 'F00001',
  systemName: '指标管理系统',
  isChildrenMenu: false,
  isDefaultGrant: true,
  isUse: true,
  menuType: 1,
}

const emit = defineEmits(['generation:code'])
async function onFinish() {
  const data = {
    ...formState.value,
    ...father,
  }
  await addMenu(data)
  // 刷新角色菜单授权
  await getUserMenuFn(false)
  // 防止页面生成部分过多 导致页面404
  setTimeout(() => {
    emit('generation:code', { type: 'create-router', content: data })
  }, 500)
}

function onFinishFailed() {}
</script>
<style scoped lang="less">
.create-router {
  margin-top: 30px;
  padding-right: 30px;
}
</style>
