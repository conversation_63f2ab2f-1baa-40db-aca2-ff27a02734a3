import service from '@/api'
import { SODATAFLINK } from '@/api/base'

type ArrayResponse<T> = {
  code: string
  data: { code: number; message: string; obj: Array<T> }
  msg: string
}

type DataResponse<T> = {
  code: string
  data: { code: number; message: string; obj: T }
  msg: string
}

/**
 *  -MDM008-查询数据源下数据库列表
 * @param {number} id -
 */

export async function dbNameFun(data: { id?: number }): Promise<
  ArrayResponse<{
    dbTypMap: string
    title: string
    key: string
    scopedSlots: {
      icon: string
    }
  }>
> {
  return service({
    method: 'POST',
    url: `${SODATAFLINK}/AISQL/dbNameFun`,
    data: { json: JSON.stringify(data) },
  })
}

/**
 *  -MDM008-查询数据源下数据库列表
 * @param {number} datasourceId -
 */

export async function PageListScameInfo(data: { ownerId: string }): Promise<{
  code: string
  msg: string
  data: Array<{
    dbUsername: string
    dbDesc: string
    dbType: number
    id: number
    dbUrl: string
    dbPassword: string
  }>
}> {
  return service({
    method: 'POST',
    url: `${SODATAFLINK}/sqlEditor/PageListScameInfo`,
    data,
  })
}

/**
 *  MDM009-查询数据库下数据表列表
 * @param {number} datasourceId -
 */

export async function queryTabledescFun(data: {
  dbName: string
  dbSchema: string
  id: number
  type: string
}): Promise<
  ArrayResponse<{
    TABLE_CATALOG: string
    TABLE_SCHEMA: string
    TABLE_NAME: string
    TABLE_TYPE: string
    ENGINE: string
    VERSION: number
    ROW_FORMAT: string
    TABLE_ROWS: number
    AVG_ROW_LENGTH: number
    DATA_LENGTH: number
    MAX_DATA_LENGTH: number
    INDEX_LENGTH: number
    DATA_FREE: number
    CREATE_TIME: string
    TABLE_COLLATION: string
    CREATE_OPTIONS: string
    TABLE_COMMENT: string
  }>
> {
  return service({
    method: 'POST',
    url: `${SODATAFLINK}/sqlEditor/queryTabledescFun`,
    data,
  })
}

export function queryColumnInfoFun(data: {
  dbName: string
  id: number
  schema: string
  tabName: string
}): Promise<
  DataResponse<{
    charset: string
    comment: string
    dbName: string
    engine: string
    id: number
    schema: string
    tableName: string
    fieldData: Array<{
      columnName: string
      columnType: string
      comment: string
      decimal: string
      increment: boolean
      isNull: boolean
      length: string
      primaryKey: boolean
      virtual: boolean
    }>
    indexData: Array<{
      column: string
      comment: string
      indexFunc: string
      indexName: string
      indexType: string
    }>
  }>
> {
  return service({
    method: 'POST',
    url: `${SODATAFLINK}/sqlEditor/queryColumnInfoFun`,
    data,
  })
}

export function queryDefaultableData(data: {
  dbName: string
  id: number
  schema: string
  tabName: string
  schemaName: string
}): Promise<DataResponse<string>> {
  return service({
    method: 'POST',
    url: `${SODATAFLINK}/sqlEditor/queryDefaultableData`,
    data,
  })
}

export function queryGetDbLevelList(data: { json: string }): Promise<DataResponse<string>> {
  return service({
    method: 'POST',
    url: `${SODATAFLINK}/AISQL/dbnameTableParmaTranFun`,
    data,
  })
}
