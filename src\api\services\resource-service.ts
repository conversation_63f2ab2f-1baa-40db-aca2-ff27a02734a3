import service from '@/api'
import type { ApiResponse } from '../common'

export type ResourceServiceItem = {
  /** 服务类型 */
  serviceType: number
  /** 服务IP */
  serviceIp: string
  /** SSH端口 */
  sshPort: number
  /** 执行路径 */
  execPath: string
  /** 主机用户 */
  hostUser: string
  /** 创建时间 */
  createTime: string
  /** 日志路径 */
  logPath: string
  /** 主机密码 */
  hostPwd: string
  /** 更新时间 */
  updateTime: string
  /** 主键ID */
  id: number
  /** 服务端口 */
  servicePort: number
  /** 逻辑ID */
  logicId: string
  /** 是否选中，0-未选中，1-选中 */
  isCheck: number
}
/**
 * 获取资源服务列表
 * @param params 分页参数
 * @returns 资源服务列表
 */
export async function getResourceServiceList(params: {
  roleId: string
  pageIndex?: number
  pageSize?: number
}): Promise<ApiResponse<ResourceServiceItem, true>> {
  const { roleId, pageIndex = 1, pageSize = 100 } = params
  return service({
    method: 'get',
    url: '/api/sodataFlink/authorityMange/getRoleAthorityServiceList',
    params: {
      roleId,
      pageIndex,
      pageSize,
    },
  })
}

/**
 * 获取已授权的资源列表
 * @param roleId 角色ID
 * @returns 已授权的资源列表
 */
export async function getAthrorityRSList(roleId: string): Promise<ApiResponse<string[]>> {
  return service({
    method: 'get',
    url: '/api/sodataFlink/authorityMange/getRoleCheckAthrorityResr',
    params: {
      roleId,
      type: 3,
    },
  })
}

/**
 * 保存分配的资源服务
 * @param data 参数
 * @param data.roleId 角色ID
 * @param data.resourceIds 资源ID列表
 * @param data.resourceType 资源类型，此处传 3
 * @returns 分配的资源服务
 */
export async function setAthrorityRSList(data: {
  roleId: string
  resourceIds: string[]
  resourceType: number
}): Promise<ApiResponse<string>> {
  return service({
    method: 'post',
    url: '/api/sodataFlink/authorityMange/saveRoleAthorityResource',
    data,
  })
}
