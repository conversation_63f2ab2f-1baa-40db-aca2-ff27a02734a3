<template>
  <a-card class="order-page">
    <Table
      :columns="columns"
      :getData="getUserGroupList"
      ref="tableRef"
      :autoRequest="false"
      :searchFormState="formState"
    >
      <template #operate>
        <a-button type="primary" @click="addTable" v-action:addUserGroup>新增</a-button>
      </template>
      <template #search>
        <a-form-item
          label="用户组名称"
          name="用户组名称"
          :rules="[{ message: '请输入用户组名称' }]"
        >
          <a-input v-model:value="formState.userGroupName" placeholder="请输入用户组名称" />
        </a-form-item>
        <a-form-item label="是否启用">
          <a-radio-group v-model:value="formState.isUse">
            <a-radio value="1">是</a-radio>
            <a-radio value="0">否</a-radio>
          </a-radio-group>
        </a-form-item>
      </template>
    </Table>
    <!-- 编辑和新增用户组 -->
    <user-group-modal ref="modalRef" @modalHandleOk="modalHandleOk"></user-group-modal>
    <!-- 分配用户 -->
    <role-user-modal
      :requiredData="requiredData"
      :request="request"
      @modalCancel="userModalCancel"
      @deleteUser="deleteUser"
      ref="userModalRef"
    />
    <!-- 分配可查看菜单 -->
    <common-tree-node
      :exportModal="MenusModal"
      ref="Modalmenus"
      :title="title"
      :modalSystem="modalSystem"
      :exportTreeData="exportTreeData"
      :fNames="fNames"
      :MenusModalClear="MenusModalClear"
      :confirmLoading="confirmLoading"
      @systemSelectEmitHandle="systemSelectEmitHandle"
      @exportModalHandleOk="exportModalHandleOk"
      @exportModalCancel="exportModalCancel"
    ></common-tree-node>
    <!-- 已分配菜单 -->
    <assigned-user-system-modal
      ref="assignedModal"
      :userGroupId="userGroupId"
      @modalAssignedCancel="modalAssignedCancel"
      @modalAssignedOk="modalAssignedOk"
    ></assigned-user-system-modal>
  </a-card>
</template>
<script setup lang="ts">
import {
  ref,
  onBeforeMount,
  h,
  createVNode,
  onMounted,
  resolveDirective,
  withDirectives,
} from 'vue'
import { Table } from '@fs/fs-components'
import type { Column } from '@fs/fs-components/src/components/table/type'
import { Button, Dropdown, Menu, MenuItem, Modal, message } from 'ant-design-vue/es/components'
import { DownOutlined, ExclamationCircleOutlined } from '@ant-design/icons-vue'
import {
  deleteUserGroup,
  getUserGroupList,
  getSystemList,
  getUserGroupMenuList,
  userGroupSetMenu,
  deleteUserGroupUser,
} from '@/api/services/permission'
import { UserGroupListDTO } from '@/utils/column'
import { useUserStore } from '@/stores/user'

let columns = ref<Column[]>([])
const tableRef = ref<InstanceType<typeof Table> | null>(null)
const disabled = ref<boolean>(false)
const modalRef = ref<any | null>(null)

const formState: Record<string, any> = ref({
  userGroupName: null,
  isUse: null,
})
const userStore = useUserStore()
const requiredData = ref<object>({
  type: 'user',
})
const request = ref('user')
const userModalRef = ref<any | null>(null)
const MenusModal = ref<boolean>(false)
const title = ref('')
const modalSystem = ref<any>([])
const exportTreeData = ref<any>([])
const MenusModalClear = ref<boolean>(false)
const userGroupId = ref('')
const systemId = ref('')
const confirmLoading = ref<boolean>(false)
const roleId = ref<string>('')
const assignedModal = ref<any | null>(null)
const fNames = {
  children: 'datas',
  title: 'menuName',
  key: 'menuId',
}

onMounted(() => {
  const { buId, tenantId } = userStore.userInfo
  formState.value.buId = buId
  if (tenantId) {
    formState.value.tenantId = tenantId
    tableRef.value?.getTableData()
  }
})

onBeforeMount(() => {
  initColumns()
})

function modalHandleOk() {
  tableRef.value?.getTableData()
}

function deleteUser(list: any, callback: () => any) {
  deleteUserGroupUser({ userList: list }).then(() => {
    message.success('删除成功!')
    callback()
  })
}

function addTable() {
  disabled.value = false
  modalRef.value?.show('add')
}

function operationHandle(key: string, record: Record<string, any>) {
  userGroupId.value = record?.userGroupId
  if (key === '0') {
    modalRef.value?.show('edit', record)
  } else if (key === '1') {
    removeTable(record?.userGroupId)
  } else if (key === '2') {
    requiredData.value = record
    userModalRef.value?.show()
  } else if (key === '3') {
    title.value = '分配可查看菜单'
    console.log('=========1111')

    getSystemModal()
  } else if (key === '4') {
    title.value = '已分配系统'
    assignedModal.value?.show('edit', record)
  }
}

function userModalCancel() {
  requiredData.value = {}
}

// 获取查看菜单接口
function getSystemModal() {
  const data = {
    pageIndex: 1,
    pageSize: 999,
  }
  getSystemList(data).then((res) => {
    modalSystem.value = res.data?.records
    MenusModal.value = true
  })
}

// 分配可查看菜单
function systemSelectEmitHandle(key: string) {
  systemId.value = key
  if (key) {
    const params = {
      userGroupId: userGroupId.value,
      systemId: systemId.value,
    }
    getUserGroupMenuList(params).then((res) => {
      exportTreeData.value = res?.data
    })
  }
}

//新增用户组选中菜单
async function exportModalHandleOk(data: Record<string, any>) {
  confirmLoading.value = true
  const menuList = data
  const params = {
    menuList,
    systemId: systemId.value,
    userGroupId: userGroupId.value,
  }
  userGroupSetMenu(params).then(() => {
    message.success('新增成功!')
    confirmLoading.value = false
    MenusModal.value = false
    exportTreeData.value = []
  })
}

function modalAssignedOk() {
  roleId.value = ''
}

function modalAssignedCancel() {
  roleId.value = ''
}

// 分配可查看菜单
function exportModalCancel() {
  exportTreeData.value = []
  MenusModal.value = false
}

async function initColumns() {
  columns.value = UserGroupListDTO.map((item) => {
    return {
      ...item,
      dataIndex: item.key,
    }
  })
  columns.value.push({
    key: 'action',
    title: '操作',
    customRender(data: Record<string, any>) {
      const menuItems = [
        { key: '0', name: '编辑', meta: 'updateUserGroup' },
        { key: '1', name: '删除', meta: 'deleteUserGroup' },
        { key: '2', name: '分配用户', meta: 'addUserGroupUser' },
        { key: '3', name: '分配可查看菜单', meta: 'viewMenu' },
        { key: '4', name: '已分配系统', meta: 'getUserGroupSystemList' },
      ]
      const mappedMenuItems = menuItems.map((item) => {
        return h(MenuItem, { key: item.key }, () => {
          return withDirectives(
            h(
              Button,
              {
                type: 'text',
                style: { width: '100%' },
                onClick: () => {
                  operationHandle(item.key, data?.record)
                },
              },
              {
                default: () => `${item.name}`,
              },
            ),
            [[resolveDirective('action'), '', item.meta]],
          )
        })
      })
      return [
        h(
          Dropdown,
          {
            placement: 'bottom',
          },
          {
            default: () => [
              h(
                Button,
                {
                  type: 'link',
                },
                {
                  default: () => ['更多', h(DownOutlined, {}, { default: () => '更多' })],
                },
              ),
            ],
            overlay: () => h(Menu, null, () => [mappedMenuItems]),
          },
        ),
      ]
    },
  })
}

function removeTable(id: string) {
  Modal.confirm({
    title: '提示',
    icon: createVNode(ExclamationCircleOutlined),
    content: createVNode('div', { style: 'color:red;' }, '确认执行删除操作？'),
    async onOk() {
      try {
        return await new Promise((resolve, reject) => {
          deleteUserGroup({ userGroupId: id })
            .then(() => {
              message.success('删除成功!')
              tableRef.value?.getTableData()
              resolve(true)
            })
            .catch(() => {
              reject
            })
        })
      } catch {
        return console.log('错误!')
      }
    },
    onCancel() {},
  })
}
</script>
<style lang="less">
.ant-dropdown-menu {
  .ant-dropdown-menu-item:hover {
    background-color: transparent !important;
  }
}
</style>
