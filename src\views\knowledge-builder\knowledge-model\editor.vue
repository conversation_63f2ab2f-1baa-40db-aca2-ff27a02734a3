<script lang="ts" setup>
import { VAceEditor } from 'vue3-ace-editor'
import 'ace-builds/src-noconflict/theme-chaos'
import { reqGetSchemaScript } from '@/api/services/knowledge'

const route = useRoute()
const content = ref('')
const language = ref('json')
const theme = ref('chaos')
const editorOptions = ref({
  fontSize: '14px', // 设置字体大小
  showPrintMargin: false, // 是否显示打印边距
  enableLiveAutocompletion: true, // 启用实时自动补全功能
})

// 获取当前schema脚本内容
const getSchemaScript = async () => {
  const res = await reqGetSchemaScript({
    projectId: route.query?.projectId,
  })
  content.value = res
}

defineExpose({
  content,
  getSchemaScript,
})
</script>

<template>
  <VAceEditor
    v-model:value="content"
    :lang="language"
    :theme="theme"
    :options="editorOptions"
    style="height: 100%; width: 100%"
  />
</template>

<style lang="scss" scoped></style>
