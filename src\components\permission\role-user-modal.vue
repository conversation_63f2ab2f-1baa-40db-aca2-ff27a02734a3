<template>
  <a-modal
    v-model:open="userModalOpen"
    title="分配用户"
    @cancel="cancel"
    width="1200px"
    :confirm-loading="confirmLoading"
    @ok="ok"
  >
    <b style="position: relative; top: 28px; font-size: 16px; font-weight: 600">已分配用户</b>
    <Table
      :columns="columns"
      :getData="getData"
      :tableConfig="{ rowSelection: Selection, rowKey: 'autoId' }"
      ref="tableRef"
      class="tableOperate"
      :searchFormState="formState"
      v-if="tableSelectedKeys"
    >
      <template #operate>
        <div class="nav">
          <div>
            <a-button type="primary" @click="addUser">添加用户</a-button>
            <a-button type="primary" @click="removeUser">批量删除</a-button>
          </div>
        </div>
      </template>
      <template #search></template>
    </Table>
  </a-modal>
  <allocation-user-modal
    :open="userOpen"
    ref="allocationUser"
    :id="userId"
    @modalCancelHandleUser="modalCancelHandleUser"
    @submitHandle="submitHandle"
    @modalOkHandleUser="modalOkHandleUser"
    :request="request"
  ></allocation-user-modal>
</template>
<script lang="ts" setup>
import { Table } from '@fs/fs-components'
import { Button, Modal, message, type TableProps } from 'ant-design-vue/es/components'
import {
  defineEmits,
  defineProps,
  defineExpose,
  ref,
  watch,
  onBeforeMount,
  h,
  createVNode,
  computed,
} from 'vue'
import type { FormInstance } from 'ant-design-vue/es/form'
import { ExclamationCircleOutlined } from '@ant-design/icons-vue'
import { allocationUserDTO, UserGroupDTO } from '@/utils/column'
import { getRoleUserList, getUserGroupUserList } from '@/api/services/permission'
import type { Column } from '@fs/fs-components/src/components/table/type'
const props = defineProps({
  requiredData: {
    type: Object,
    default: () => {},
  },
  request: {
    type: String,
    default: () => '',
  },
})
const formState = ref<any>({
  roleId: null,
  userGroupId: null,
})
const formRef = ref<FormInstance>()
const modalType = ref<string>('')
const tableRef = ref<InstanceType<typeof Table> | null>(null)
const confirmLoading = ref<boolean>(false)
const allocationUser = ref<any | null>(null)
let columns = ref<Column[]>([])
const userOpen = ref<boolean>(false)
const selectedKeys = ref<string[]>([])
const userModalOpen = ref<boolean>(false)
const tableSelectedKeys = ref<boolean>(false)
const userId = ref<string>('')
watch(
  () => props.requiredData,
  (value) => {
    modalType.value = props.request
    if (modalType.value === 'user') {
      formState.value.userGroupId = value?.userGroupId
      formState.value.userGroupId && tableRef.value?.getTableData()
      userId.value = value?.userGroupId
    } else {
      formState.value.roleId = value?.roleId
      userId.value = value?.roleId
      value?.roleId && tableRef.value?.getTableData()
    }
  },
  { immediate: true, deep: true },
)

const getData = computed(() => {
  if (modalType.value === 'user') {
    return getUserGroupUserList
  } else {
    return getRoleUserList
  }
})

const emits = defineEmits(['modalCancel', 'deleteUser'])
const Selection: TableProps['rowSelection'] = {
  //@ts-ignore
  onChange: (selectedRowKeys: string[]) => {
    selectedKeys.value = selectedRowKeys
  },
}

onBeforeMount(() => {
  initColumns()
})

function cancel() {
  emits('modalCancel')
  userModalOpen.value = false
  tableSelectedKeys.value = false
}
function ok() {
  emits('modalCancel')
  userModalOpen.value = false
  tableSelectedKeys.value = false
}

function addUser() {
  userOpen.value = true
  allocationUser.value && allocationUser.value.show(props.request)
}

function submitHandle() {
  userOpen.value = false
  tableRef.value?.getTableData()
}

function modalCancelHandleUser() {
  userOpen.value = false
}

function modalOkHandleUser() {
  userOpen.value = false
}

async function show() {
  tableSelectedKeys.value = true
  await resetForm()
  userModalOpen.value = true
  selectedKeys.value = []
}

const resetForm = () => {
  formRef.value?.resetFields()
}

async function removeUser() {
  if (selectedKeys.value.length > 0) {
    const userList = selectedKeys.value.map((item) => {
      return {
        autoId: item,
      }
    })
    await emits('deleteUser', userList, () => {
      tableRef.value?.resetForm()
    })
  } else {
    message.info('请选择要删除的用户')
  }
}

async function initColumns() {
  const DTO = modalType.value === 'user' ? UserGroupDTO : allocationUserDTO
  columns.value = DTO.map((item) => {
    return {
      ...item,
      dataIndex: item.key,
    }
  })
  columns.value.push({
    key: 'action',
    title: '操作',
    customRender(data: Record<string, any>) {
      return [
        h(
          Button,
          {
            type: 'link',
            onClick() {
              Modal.confirm({
                title: '提示',
                icon: createVNode(ExclamationCircleOutlined),
                content: createVNode('div', { style: 'color:red;' }, '确认执行删除操作？'),
                async onOk() {
                  try {
                    const params = [{ autoId: data?.record?.autoId }]
                    await emits('deleteUser', params, () => {
                      tableRef.value?.resetForm()
                    })
                  } catch {
                    return console.log('Oops errors!')
                  }
                },
                class: 'test',
              })
            },
          },
          {
            default: () => '删除',
          },
        ),
      ]
    },
  })
}

defineExpose({ show })
</script>
<style lang="less" scoped>
.nav {
  width: 100%;
  display: flex;
  flex: 1;
  justify-content: space-between;
  align-items: center;
  margin-left: 20px;

  b {
    display: inline-block;
    font-size: 16px;
    font-weight: 600;
  }

  .ant-btn {
    margin-right: 20px;
  }
}

.tableOperate {
  /deep/.ant-space,
  /deep/.ant-space-item {
    width: 100%;
  }

  /deep/.ant-space .ant-space-item:nth-child(2) {
    display: none;
  }

  /deep/ .filter-box {
    display: flex;
    flex-wrap: unset;
    justify-content: flex-end;
  }
}
</style>
