// 指标配置页面样式文件
@default-text-color: #509ee3;

.left-wrap {
  padding: 16px;
  border-radius: 8px 0 0 8px;
  overflow: hidden;
  border: 1px solid #ccc;
  border-right: none;
  position: relative;
  height: 100%;
  width: 100%;
}

.right-wrap {
  padding-top: 24px;
  border-radius: 0 8px 8px 0;
  overflow: hidden;
  border: 1px solid #ccc;
  border-left: none;
  position: relative;
  height: 100%;
  width: 100%;
  
  .close-btn {
    position: absolute;
    right: 5px;
    top: 5px;
    cursor: pointer;
    color: #ccc;
    z-index: 10;
    transition: all 0.3s;
    
    &:hover {
      color: @default-text-color;
    }
  }
}

.title {
  color: @default-text-color;
  font-weight: bold;
  display: flex;
  margin-bottom: 0.5rem;
}

.emotion-box {
  display: flex;
  -webkit-box-align: center;
  align-items: center;
  flex-wrap: wrap;
  border-radius: 8px;
  background-color: rgba(80, 158, 227, 0.1);
  padding: 14px;
  color: rgb(80, 158, 227);
}

:deep(.return-btn) {
  .anticon,
  .anticon-arrow-left {
    padding: 10px;
  }
}

.editor-box-item {
  margin-bottom: 1rem;
}

.editor-action {
  width: 5%;
}

.previewAll {
  padding: 16px;
}

.editor-prevew {
  &-header {
    margin: 10px 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    
    .anticon {
      cursor: pointer;
    }
  }
  
  &-content {
    max-height: 400px;
    overflow-y: auto;
    box-shadow: 0 2px 2px rgb(0 0 0 / 13%);
    border: 1px solid #eeecec;
    border-radius: 8px;
  }
}

.editor-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;

  &-rule {
    position: relative;
    flex: 1;
  }
  
  .close {
    position: absolute;
    right: 0;
    top: 0px;
    display: none;
    cursor: pointer;

    .close-btn {
      padding: 5px;
      transition:
        background-color 0.3s,
        color 0.3s;
    }
    
    .close-btn:hover {
      background-color: #333;
      color: #fff;
    }
  }
  
  &:hover {
    // background-color: #000;
    .close {
      display: block;
    }
  }
  
  .play {
    width: 32px;
    height: 32px;
    margin-left: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 6px;
    cursor: pointer;
    
    &:hover {
      background-color: rgb(237, 238, 241);
    }
  }
}

.custom-sql-editor {
  border-radius: 6px;
  overflow: hidden;
  height: 400px;
  border: 1px solid #d9d9d9;

  // 确保draggerBanner组件占满容器高度
  :deep(.dragger-banner) {
    height: 100%;
  }
}

.sql-editor-left {
  height: 100%;
  border-right: 1px solid #e9ecef;
  background: #fafafa;
}

.sql-editor-right {
  height: 100%;
  background: #fff;
  position: relative;
}

:deep(.ant-switch.ant-switch-checked) {
  background-color: #47dd6f;
}
:deep(.ant-switch.ant-switch-checked:hover) {
  background-color: #47dd6f;
}
