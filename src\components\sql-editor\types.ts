import Ace from 'ace-builds'
import type { Completion } from 'ace-builds-internal/autocomplete'

export type Position = {
  row: number
  column: number
}
export type CompleterCallback = (error: any, completions: Completion[]) => void

/**
 * 作用: 定义补全器接口
 * 用途: 用于实现自定义的补全逻辑
 */
export interface Completer {
  /**
   * 作用: 定义标识符的正则表达式数组
   * 用途: 确定哪些字符被认为是标识符的一部分，影响补全触发时机
   */
  identifierRegexps?: Array<RegExp>
  /**
   * 作用: 获取补全建议
   * @param editor - 编辑器实例
   * @param session - 编辑会话, 提供当前会话的上下文信息
   * @param position - 当前光标位置
   * @param prefix - 用户输入的前缀
   * @param callback - 回调函数，用于返回补全建议
   */
  getCompletions(
    editor: Ace.Editor,
    session: Ace.EditSession,
    position: Position,
    prefix: string,
    callback: CompleterCallback,
  ): void
  /**
   * 作用: 为补全项提供文档提示/工具提示
   * @param item - 补全建议项
   * @returns 可以返回字符串或增强的Completion对象
   */
  getDocTooltip?(item: Completion): void | string | Completion
  /**
   * 作用: 当补全项被用户看到时触发
   * @param editor - 编辑器实例
   * @param completion - 补全建议项
   */
  onSeen?: (editor: Ace.Editor, completion: Completion) => void
  /**
   * 作用: 当补全项被用户插入时触发
   * @param editor - 编辑器实例
   * @param completion - 补全建议项
   */
  onInsert?: (editor: Ace.Editor, completion: Completion) => void
  /**
   * 作用: 取消补全操作
   */
  cancel?(): void
  /**
   * 作用: 补全项的唯一标识符
   */
  id?: string
  /**
   * 作用: 定义触发补全的字符数组
   * 示例: ['.', '#', '@'] - 当输入这些字符时自动触发补全
   */
  triggerCharacters?: string[]
  /**
   * 作用: 是否隐藏内联预览
   */
  hideInlinePreview?: boolean
  /**
   * 作用: 自定义插入补全项的方法
   * @param editor - 编辑器实例
   * @param data - 补全项数据
   */
  insertMatch?: (editor: Ace.Editor, data: Completion) => void
}
