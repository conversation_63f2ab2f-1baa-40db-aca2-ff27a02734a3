import { <PERSON><PERSON>, Transformer } from '@airpower/transformer'
import { BaseEnum } from '../common'

export class NodeType extends BaseEnum<string> {
  static readonly connection = new NodeType('connection', 'connection')
  static readonly database = new NodeType('database', 'database')
  static readonly schema = new NodeType('schema', 'schema')
  static readonly table = new NodeType('table', 'table')
}

/**
 * 数据连接
 */
export class DBConnection extends Transformer {
  id!: number
  dbType!: number

  @Alias('dbDesc')
  name!: string

  @<PERSON>as('dbUrl')
  url!: string

  @<PERSON><PERSON>('dbUsername')
  username!: string
}
/** 数据库表字段 */
export class DbTableColumn extends Transformer {
  /** 字段注释 */
  comment!: string
  /** 字段描述 */
  desc!: string
  /** 字段名称 */
  key!: string
  /** 字段图标 */
  @Alias('scopedSlots.icon')
  icon!: string
  /** 字段标题 */
  title!: string
}
