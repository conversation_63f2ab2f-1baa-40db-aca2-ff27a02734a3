<template>
  <a-modal
    v-model:open="open"
    :title="!isEdit ? '新增事业单位' : '编辑事业单位'"
    width="500px"
    :mask-closable="false"
    :confirm-loading="loading"
    @cancel="open = false"
    @ok="onSubmit"
  >
    <a-form
      ref="formRef"
      name="form"
      class="form"
      :model="formState"
      :rules="rules"
      layout="vertical"
    >
      <a-form-item label="BU名称" name="buName">
        <a-input v-model:value.trim="formState.buName" placeholder="请输入"> </a-input>
      </a-form-item>
      <a-form-item label="管理员账号" name="manageUserAccount">
        <a-input
          v-model:value.trim="formState.manageUserAccount"
          placeholder="请输入"
          :disabled="isEdit"
        >
        </a-input>
      </a-form-item>
      <a-form-item label="是否启用" name="isUse">
        <a-radio-group v-model:value="formState.isUse" :options="isUseOptions"></a-radio-group>
      </a-form-item>
      <a-form-item label="备注" name="remark">
        <a-input v-model:value.trim="formState.remark" placeholder="请输入"> </a-input>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, onBeforeMount, computed } from 'vue'
import type { Rule } from 'ant-design-vue/es/form'
import { message } from 'ant-design-vue'
import { addBu, updateBU } from '@/api/services/permission'
import type { RadioGroupProps } from 'ant-design-vue'

export interface AddBuModalProps {
  type: 'add' | 'edit'
  record: any
}

const open = defineModel<boolean>({ default: false, required: true })
const props = withDefaults(defineProps<AddBuModalProps>(), {
  type: 'add',
})
const emits = defineEmits(['submit'])

const isEdit = computed(() => props.type === 'edit')

const isUseOptions = ref<RadioGroupProps['options']>([
  { label: '是', value: true },
  { label: '否', value: false },
])

const formState = ref<any>({
  buName: '',
  isUse: true,
  manageUserAccount: '',
  remark: '',
})
const formRef = ref()
const loading = ref(false)

/**
 * 表单校验规则
 */
const rules: Record<string, Rule[]> = {
  buName: [{ required: true, message: '请输入', trigger: 'change' }],
  isUse: [{ required: true, message: '请选择', trigger: 'change' }],
  manageUserAccount: [{ required: true, message: '请输入', trigger: 'change' }],
}

onBeforeMount(() => {
  isEdit.value && initFormState()
})

function initFormState() {
  const { buName, buManageUserAccount, remark, isUse } = props.record
  formState.value.buName = buName
  formState.value.isUse = isUse === '是' ? true : false
  formState.value.manageUserAccount = buManageUserAccount
  formState.value.remark = remark
}

/**
 * 提交
 */
async function onSubmit() {
  formRef.value
    .validate()
    .then(() => {
      isEdit.value ? updateBUFn() : addBuFn()
    })
    .catch((error: any) => {
      console.log('error', error)
    })
}

/**
 * 新增BU
 */
async function addBuFn() {
  try {
    loading.value = true
    await addBu(formState.value)
    message.success('操作成功')
  } catch (error) {
    console.log(error)
  } finally {
    loading.value = false
    open.value = false
    emits('submit')
  }
}

/**
 * 更新BU
 */
async function updateBUFn() {
  try {
    loading.value = true
    const { isUse, buName, remark } = formState.value
    await updateBU({
      isUse,
      buName,
      remark,
      businessId: props.record.buId,
    })
    message.success('操作成功')
  } catch (error) {
    console.log(error)
  } finally {
    loading.value = false
    open.value = false
    emits('submit')
  }
}
</script>

<style scoped lang="less"></style>
