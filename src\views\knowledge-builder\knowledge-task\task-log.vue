<script lang="ts" setup>
import { ref } from 'vue'
import type { TaskItem } from '../type'
import { reqGetLog } from '@/api/services/knowledge'
const props = defineProps<{ data?: TaskItem; getTaskList?: () => void }>()
const open = ref<boolean>(false)
const logList = ref<any[]>([])
const currentLogData = ref<any>()
let timer: NodeJS.Timeout | null = null

const afterOpenChange = (bool: boolean) => {
  console.log('open', bool)
}

const showLogDrawer = (data: TaskItem) => {
  currentLogData.value = data
  open.value = true
}

const getLogList = async () => {
  const params = {
    id: props.data?.id || currentLogData.value?.id,
    jobId: props.data?.id || currentLogData.value?.id,
  }
  const res = await reqGetLog(params)
  const list = JSON.parse(res?.resultMessage || '[]')

  logList.value =
    list
      .map((item: any, index: number) => {
        if (item.status === 'RUNNING') {
          item.status = 'PROCESS'
        } else if (item.status === 'WAITING') {
          item.status = 'WAIT'
        }
        return {
          ...item,
          title: item.name,
          status: item.status.toLowerCase(),
          description: h(
            'pre',
            {
              style: {
                color: item.status === 'ERROR' ? 'red' : '#000000d9',
              },
            },
            item.traceLog,
          ),
        }
      })
      .reverse() || []
  console.log('logList', props.data, res, list)
  const hasRunning = list.some((item: any) => item.status === 'PROCESS')
  console.log('hasRunning', hasRunning)
  if (open.value && hasRunning) {
    timer = setTimeout(() => {
      getLogList()
    }, 3000)
  } else {
    clearTimeout(timer as NodeJS.Timeout)
  }
}

watch(open, (val) => {
  if (val) {
    getLogList()
  } else {
    logList.value = []
    clearTimeout(timer as NodeJS.Timeout)
    props?.getTaskList?.()
  }
})

defineExpose({
  showLogDrawer,
})
</script>

<template>
  <a-drawer
    :width="700"
    :destroy-on-close="true"
    v-model:open="open"
    class="custom-class"
    root-class-name="root-class-name"
    :root-style="{ color: 'blue' }"
    style="color: red"
    title="日志"
    placement="right"
    @after-open-change="afterOpenChange"
  >
    <a-steps direction="vertical" :percent="60" :items="logList"> </a-steps>
  </a-drawer>
</template>

<style scoped>
:deep(.ant-steps-item-description) {
  twhite-space: pre-wrap;
}
:deep(.ant-progress-circle) {
  animation: spin 1.5s linear infinite; /* 无限旋转 */
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
