<template>
  <a-form
    :model="modelValue"
    name="basic"
    :label-col="{ span: 4 }"
    :wrapper-col="{ span: 24 }"
    autocomplete="off"
    ref="formRef"
  >
    <a-form-item label="项目" name="project">
      <a-select
        v-model:value="modelValue.project"
        placeholder="请选择项目"
        @change="handleChange($event, false)"
      >
        <a-select-option
          v-for="(item, index) in props.projectList"
          :key="index"
          :value="item.project"
          >{{ item.project }}</a-select-option
        >
      </a-select>
    </a-form-item>
    <a-form-item label="模块" name="module" v-if="modelValue.project">
      <a-select
        v-model:value="modelValue.module"
        placeholder="请选择模块"
        @change="modulesChange($event, false)"
      >
        <a-select-option v-for="(item, index) in modulesList" :key="index" :value="item.moduleName"
          >{{ item.moduleNameCn }} {{ item.moduleName }}</a-select-option
        >
      </a-select>
    </a-form-item>
    <a-form-item label="接口" name="interface" v-if="modelValue.module">
      <a-select
        v-model:value="modelValue.interface"
        placeholder="请选择模块"
        @change="interfaceChange"
      >
        <a-select-option
          v-for="item in interfaceInfos"
          :key="item.interfaceName"
          :value="item.interfaceName"
          >{{ item.interfaceNameCn }} {{ item.interfaceName }}</a-select-option
        >
      </a-select>
    </a-form-item>
  </a-form>
</template>

<script setup lang="ts">
import { ref, watch, nextTick } from 'vue'

const modelValue = ref<any>({
  project: undefined,
  module: undefined,
  interface: undefined,
})

const modulesList = ref<any[]>([])
const interfaceInfos = ref<any[]>([])

const emit = defineEmits(['request'])

interface requestApi {
  projectList: any[]
  defaultData: Record<string, any>
}

const props: requestApi = withDefaults(defineProps<requestApi>(), {})

watch(
  () => props.defaultData,
  (newValue) => {
    if (newValue) {
      modelValue.value = newValue
      nextTick(() => {
        handleChange(newValue.project)
        modulesChange(newValue.module)
      })
    }
  },
  {
    deep: true,
  },
)

function handleChange(params: string, state = false) {
  const item = props.projectList?.find((item) => item.project === params)
  //   if (!state) {
  //     newModelValue.value.module = undefined
  //     newModelValue.value.request.map((item: { api: undefined }) => (item.api = undefined))
  //   }

  if (item) {
    modulesList.value = item.modules
  }
}

function modulesChange(params: string, state = false) {
  const item = modulesList.value.find((item) => item.moduleName === params)
  //   if (!state) {
  //     newModelValue.value.request.map((item: { api: undefined }) => (item.api = undefined))
  //   }
  if (item) {
    interfaceInfos.value = item.interfaceInfos
  }
}

function interfaceChange(params: string, state = false) {
  const interfaceData = interfaceInfos.value.find((item) => item.interfaceName === params)
  emit('request', {
    interface: params,
    project: modelValue.value.project,
    module: modelValue.value.module,
    interfaceData,
  })
}
</script>
<style scoped lang="less"></style>
