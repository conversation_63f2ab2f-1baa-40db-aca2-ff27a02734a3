<template>
  <a-select
    v-model:value="modelValue"
    style="width: 120px"
    :options="filterData"
    @change="handleSelectChange"
  />
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'

const emits = defineEmits(['selected'])
const props = defineProps({
  type: {
    type: String,
    default: '',
  },
})
const modelValue = defineModel()

const data = ref([
  {
    label: '等于',
    value: '=',
  },
  {
    label: '不等于',
    value: '!=',
  },
  {
    label: '小于',
    value: '<',
  },
  {
    label: '大于',
    value: '>',
  },
  {
    label: '包含',
    value: 'contains',
  },
  {
    label: '不包含',
    value: 'not_contains',
  },
  {
    label: '以...开始',
    value: 'start_with',
  },
  {
    label: '以...结束',
    value: 'end_with',
  },
  {
    label: '在...之间',
    value: 'BETWEEN',
  },
  {
    label: '大于或等于',
    value: '>=',
  },
  {
    label: '小于或等于',
    value: '<=',
  },
  {
    label: '为空',
    value: 'IS_NULL',
  },
  {
    label: '不为空',
    value: 'NOT_NULL',
  },
  // {
  //   label: '是',
  //   value: 'is_true',
  // },
  // {
  //   label: '否',
  //   value: 'is_false',
  // }
])

const filterData = computed(() => {
  const type = props.type
  let filterTypes: string[] = []
  if (['TEXT', 'ENUM'].includes(type)) {
    filterTypes = ['小于', '大于', '在...之间', '大于或等于', '小于或等于']
  }
  if (type === 'NUMBER') {
    filterTypes = ['包含', '不包含', '以...开始', '以...结束']
  }
  return data.value.filter((item) => !filterTypes.includes(item.label))
})
modelValue.value = filterData.value[0].value

function handleSelectChange(value: string) {
  modelValue.value = value
  emits('selected', value)
}
</script>

<style lang="less" scoped></style>
