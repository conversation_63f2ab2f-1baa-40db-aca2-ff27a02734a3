<template>
  <div class="column">
    <div
      class="column-item"
      :class="{ selected: item.key === selectedColumn?.key }"
      v-for="item in data"
      :key="item.key"
      @click="handleClick(item)"
    >
      <div class="column-icon">
        <a-popover trigger="hover">
          <template #content>
            <div class="column-desc">{{ item.comment || '没有描述' }}</div>
            <div class="column-type">
              <component :is="iconMap[item.columnType]"></component>
              <span>{{ columnTypeMap[item.columnType] }}</span>
            </div>
          </template>
          <div class="column-icon-type">
            <component :is="iconMap[item.columnType]"></component>
          </div>
          <div class="column-icon-info"><InfoCircleOutlined /></div>
        </a-popover>
      </div>
      <div class="column-name">
        <slot :item="item">{{ item.title }}</slot>
      </div>
      <div class="column-extal">
        <slot :item="item" name="extal"></slot>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  CalendarOutlined,
  FileTextOutlined,
  NumberOutlined,
  CheckSquareOutlined,
  CheckCircleOutlined,
  InfoCircleOutlined,
} from '@ant-design/icons-vue'
import type { dataColumn } from './type'
import { ref } from 'vue'
import type { ServerFieldItem } from '@/api/services/indicator/type'
const columnTypeMap: Record<string, string> = {
  TEXT: '文本',
  NUMBER: '数字',
  DATE: '日期',
  BOOLEAN: '布尔值',
  ENUM: '枚举',
}
const iconMap: Record<string, any> = {
  TEXT: FileTextOutlined,
  NUMBER: NumberOutlined,
  DATE: CalendarOutlined,
  BOOLEAN: CheckCircleOutlined,
  ENUM: CheckSquareOutlined,
}
const selectedColumn = ref<ServerFieldItem | null>(null)

defineProps<{
  data: ServerFieldItem[]
}>()
const emits = defineEmits(['handleClick'])

function handleClick(item: ServerFieldItem) {
  selectedColumn.value = item
  emits('handleClick', item)
}
</script>

<style lang="less" scoped>
.column {
  &-item {
    flex: 1;
    display: flex;
    border-radius: 4px;
    margin-top: 2px;
    margin-bottom: 2px;
    padding: 0.5rem;
    cursor: pointer;
    align-items: center;
    font-weight: 500;
    .column-icon-info {
      display: none;
    }
    .column-extal {
      display: none;
    }
    &:hover,
    &.selected {
      background-color: rgb(113, 114, 173);
      color: white;
      .column-icon-info {
        display: block;
      }
      .column-icon-type {
        display: none;
      }
      .column-extal {
        display: block;
      }
    }
  }
  &-icon {
    margin-right: 5px;
  }
  &-desc {
    white-space: pre-line;
    overflow: auto;
    margin-bottom: 0.5em;
    color: var(--mb-color-text-light, #949aab);
    font-weight: 700;
  }
  &-type {
    display: inline-flex;
    -webkit-box-align: center;
    align-items: center;
    column-gap: 0.3em;
    font-size: 1em;
    font-weight: normal;
    color: rgb(80, 158, 227);
    margin-bottom: 0.5rem;
  }
  &-name {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    flex: 1;
  }
}
</style>
