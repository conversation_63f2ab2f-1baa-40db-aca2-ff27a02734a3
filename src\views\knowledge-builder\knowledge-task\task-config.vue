<template>
  <a-tabs v-model:activeKey="activeKey">
    <!-- 基础配置 -->
    <a-tab-pane :key="TabEnum.basic" tab="基础配置">
      <!-- 基础配置内容 -->
      <a-space size="small" direction="vertical" align="start">
        <div v-for="item in baseConfig" :key="item.key">
          {{ item.label }}:
          <span v-if="item.key !== 'document'">{{ item.value }}</span>
          <span v-else class="ellipsis">{{ item.value }}</span>
        </div>
      </a-space>
    </a-tab-pane>

    <!-- 分段配置 -->
    <a-tab-pane :key="TabEnum.segment" tab="分段配置">
      <!-- 分段配置内容 -->
      <a-space size="small" direction="vertical" align="start">
        <a-space size="small" direction="horizontal" align="start">
          <span>分段方式</span>: <span>不使用语义切分</span>
        </a-space>
        <a-space size="small" direction="horizontal" align="start">
          <span>分段长度</span>: <span>{{ curSplitLength }}</span>
        </a-space>
      </a-space>
      <a-divider type="horizontal" orientation="left,right,center"> </a-divider>
      <a-button
        type="primary"
        shape="default"
        :loading="false"
        :disabled="false"
        @click="onPreviewText"
      >
        预览分段效果
      </a-button>
      <a-collapse v-show="showCollapse" v-model:activeKey="collapse" ghost>
        <a-collapse-panel key="1" header="分段预览效果">
          {{ previewText }}
        </a-collapse-panel>
      </a-collapse>
    </a-tab-pane>

    <!-- 抽取配置 -->
    <a-tab-pane :key="TabEnum.extract" tab="抽取配置">
      <!-- 抽取配置内容 -->
      <a-space size="small" direction="vertical" align="start">
        <a-space size="small" direction="horizontal" align="start">
          <span>抽取模型</span>: <span>Default</span>
        </a-space>
        <a-space size="small" direction="horizontal" align="start">
          <span>抽取结果</span>: <span>允许大模型完善抽取结果</span>
        </a-space>
        <span>提示词</span>
      </a-space>
    </a-tab-pane>
  </a-tabs>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { Drawer, Tabs, Form, Input } from 'ant-design-vue'
import type { TaskItem } from '../type'
import { getTaskDocPreview } from '@/api/mock'
import { reqPreviewSegment } from '@/api/services/knowledge'

const props = defineProps<{ data?: TaskItem }>()
const activeKey = ref<string>('basic')
const collapse = ref<string[]>([])
const showCollapse = ref(false)
enum TabEnum {
  basic = 'basic',
  segment = 'segment',
  extract = 'extract',
}
const baseConfig = reactive([
  {
    key: 'name',
    label: '任务名称',
    value: props.data?.jobName,
  },
  {
    key: 'type',
    label: '文件类型',
    value: '本地文件',
  },
  {
    key: 'document',
    label: '上传文档',
    value: props.data?.fileUrl,
  },
])
const previewText = ref('')
const onPreviewText = async () => {
  console.log('分段效果', props.data)
  const { extension, fileUrl, jobName, projectId, type } = props.data!
  const data = {
    extension,
    fileUrl,
    jobName,
    projectId,
    type,
  }
  const res: any = await reqPreviewSegment(data)
  previewText.value = res?.[0]
  console.log('111111res', res)
  showCollapse.value = !showCollapse.value
}

const curSplitLength = computed(() => {
  const extension = JSON.parse(props.data?.extension || '{}')
  return extension?.extractConfig?.splitConfig?.splitLength || 2000
  // return props.data?.fileUrl?.split('/').pop()
})

onMounted(async () => {
  // const res = await getTaskDocPreview()
  // previewText.value = res.data[0]
})
</script>

<style scoped>
.file-path-input {
  width: 80%;
}
.ant-form-item {
  margin-bottom: 24px;
}
</style>
