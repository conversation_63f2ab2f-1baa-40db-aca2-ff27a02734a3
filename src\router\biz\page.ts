import type { RouteRecordRaw } from 'vue-router'

const isSecondary = import.meta.env.VITE_APP_NEED_AUTH === 'false'

/**
 * 页面路由
 */
const routes: RouteRecordRaw[] = [
  // {
  //   path: '/page',
  //   name: 'pageHome',
  //   component: () => import('@/views/pages/home-view.vue'),
  // },
  {
    path: '/table-component',
    name: 'table-component',
    component: () => import('@/views/table-component/table-component.vue'),
    meta: {
      icon: 'HomeOutlined',
    },
  },
  {
    path: '/knowledge-home',
    name: 'knowledge-home',
    component: () => import('@/views/knowledge/index.vue'),
    meta: {
      isSecondary,
      icon: 'BlockOutlined',
    },
  },
  {
    path: '/knowledgeBuilder',
    name: 'knowledgeBuilder',
    component: () => import('@/views/knowledge-builder/index.vue'),
    children: [
      {
        path: '/knowledgeBuilder/knowledgeTask',
        name: 'knowledgeBuilder/knowledgeTask',
        component: () => import('@/views/knowledge-builder/knowledge-task/index.vue'),
        meta: {
          isSecondary: true,
        },
      },
      {
        path: '/knowledgeBuilder/knowledgeModel',
        name: 'knowledgeBuilder/knowledgeModel',
        component: () => import('@/views/knowledge-builder/knowledge-model/index.vue'),
        meta: {
          breadcrumb: true,
        },
      },
      {
        path: '/knowledgeBuilder/calculation',
        name: 'knowledgeBuilder/calculation',
        component: () => import('@/views/knowledge-builder/knowledge-calculation/index.vue'),
        meta: {
          breadcrumb: true,
        },
      },
    ],
  },
  {
    path: '/model-service',
    name: 'knowledgeModelService',
    component: () => import('@/views/model-service/index.vue'),
  },
  {
    path: '/reporter',
    name: 'reporter',
    component: () => import('@/views/reporter/index.vue'),
  },
  {
    path: '/reporterDetail',
    name: 'reporterDetail',
    component: () => import('@/views/reporter/reporter-detail.vue'),
  },
  {
    path: '/gen-bi-url',
    name: 'tool',
    component: () => import('@/views/tool/index.vue'),
  },
  {
    path: '/userCenter',
    name: 'userCenter',
    component: () => import('@/views/user-center/index.vue'),
    meta: {
      title: '用户中心',
    },
  },
  {
    path: '/setting/dataSource',
    name: 'dataSource',
    component: () => import('@/views/setting-page/data-source/index.vue'),
    meta: {
      isSecondary,
      title: '数据源管理',
    },
  },
  {
    path: '/setting/addService',
    name: 'addService',
    meta: { title: '添加数据源', hideInMenu: false, keepAlive: false },
    component: () => import('@/views/setting-page/data-source/add-service.vue'),
  },
  {
    path: '/setting/editService',
    name: 'editService',
    meta: { title: '编辑数据源', hideInMenu: false, keepAlive: false },
    component: () => import('@/views/setting-page/data-source/add-service.vue'),
  },
  {
    path: '/support-system',
    name: 'supportSystem',
    meta: { title: 'LQ医院管理决策支持' },
    component: () => import('@/views/support-system/index.vue'),
  },
  {
    path: '/setting/service-config',
    name: 'serviceConfig',
    component: () => import('@/views/setting/service-config.vue'),
    meta: {
      isSecondary,
      title: '服务配置',
    },
  },
]

export default routes
