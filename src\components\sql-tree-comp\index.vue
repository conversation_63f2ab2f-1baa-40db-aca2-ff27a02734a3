<!-- SQL 树目录组件
统一数据连接、数据库实例、数据表的图标、不同类型的数据库图标
支持搜索
不集成展开加载子节点、右键菜单等逻辑 -->
<script setup lang="ts">
import { Divider, Tree } from 'ant-design-vue'
import type { SqlDataNode } from './types'
import type { Key } from 'ant-design-vue/es/vc-table/interface'
import {
  CloudServerOutlined,
  DatabaseOutlined,
  TableOutlined,
  SearchOutlined,
} from '@ant-design/icons-vue'
import { getDbIcon } from '@/utils/utils'
import { useEventBus } from '@vueuse/core'

defineOptions({
  name: 'SqlTreeComp',
})
const evtBus = useEventBus<string, SqlDataNode>('sql-editing')
const emit = defineEmits(['select', 'loadData'])
const props = withDefaults(
  defineProps<{
    treeData: SqlDataNode[]
    loading: boolean
    showSearch?: boolean
    loadData: (nodeData: SqlDataNode) => Promise<void>
  }>(),
  {
    showSearch: true,
  },
)
const treeData = toRef(props, 'treeData')
const selectedKeys = ref<Key[]>([])
const expandedKeys = defineModel<Key[]>('expandedKeys', { default: () => [] })
const searchKeyword = ref('')
const filteredTreeData = computed(() => {
  if (!searchKeyword.value) return treeData.value
  return treeData.value.filter((item) => {
    return item.title.toLowerCase().includes(searchKeyword.value.toLowerCase())
  })
})

const onLoadData = async (treeNode: any) => {
  const nodeData: SqlDataNode = treeNode.dataRef
  await props.loadData(nodeData)
}

const onSelect = (keys: Key[], info: any) => {
  const nodeData: SqlDataNode = info.node.dataRef
  selectedKeys.value = keys
  emit('select', nodeData)
}

const onExpand = (keys: Key[]) => {
  expandedKeys.value = keys
}

/** 获取图标 */
const getIcon = (record: SqlDataNode) => {
  switch (record.nodeType) {
    case 'connection':
      return CloudServerOutlined
    case 'database': {
      const path = getDbIcon(record.dbTypeName)
      if (path) {
        return h('img', { src: path, class: 'db-icon' })
      }
      return DatabaseOutlined
    }
    case 'schema':
      return h('i', { class: 'iconfont icon-model' })
    case 'table':
      return TableOutlined
    default:
      return null
  }
}
const onSqlEditingEvent = (e: string, p?: SqlDataNode) => {
  if (e === 'update-selected-key' && p) {
    logPlus.orange('更新选中节点', p)
    selectedKeys.value = [p.key]
    emit('select', p)
  }
}
onMounted(() => {
  evtBus.on(onSqlEditingEvent)
})
onBeforeUnmount(() => {
  evtBus.reset()
})
</script>

<template>
  <div class="sql-tree-comp">
    <!-- 搜索框 -->
    <div v-if="showSearch">
      <a-input v-model:value="searchKeyword" placeholder="搜索..." allowClear>
        <template #prefix>
          <SearchOutlined />
        </template>
      </a-input>
      <Divider class="my-4" />
    </div>
    <a-spin :spinning="loading" style="min-height: 300px">
      <Tree
        :tree-data="filteredTreeData"
        :selectedKeys="selectedKeys"
        :expandedKeys="expandedKeys"
        :show-icon="true"
        :load-data="onLoadData"
        @select="onSelect"
        @expand="onExpand"
      >
        <template #icon="record">
          <component :is="getIcon(record)" />
        </template>
        <!-- 右键菜单 -->
        <template #title="record">
          <slot name="title" :record="record"></slot>
        </template>
      </Tree>
    </a-spin>
  </div>
</template>

<style scoped>
.db-icon {
  width: 24px;
  height: 24px;
  object-fit: contain;
}
:deep(.ant-tree .ant-tree-node-content-wrapper.ant-tree-node-selected) {
  background-color: #aedef7;
}
</style>
