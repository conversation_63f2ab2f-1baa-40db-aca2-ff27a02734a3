<script lang="ts" setup>
import { defineProps } from 'vue'
import { similarityColumn } from './data-source'
import { CodeSandboxOutlined } from '@ant-design/icons-vue'
const props = withDefaults(defineProps<{ pageData?: any }>(), {
  pageData: () => [], // 默认值为数组
})
</script>

<template>
  <div class="centrality-page">
    <a-table :columns="similarityColumn" :data-source="pageData">
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'department'">
          <span>{{ record.nodeInfo.department }}</span>
        </template>
        <template v-if="column.dataIndex === 'relation'">
          <div class="relation-box">
            <div class="relation-item">
              <span><CodeSandboxOutlined /> {{ record.node1.info.name }}</span>
              <!-- <span> 年龄：{{ record.node1.info.age }} </span>
              <span> 部门：{{ record.node1.info.department }} </span> -->
            </div>
            <div class="relation-item">
              <span><CodeSandboxOutlined /> {{ record.node2.info.name }}</span>
              <!-- <span> 年龄：{{ record.node2.info.age }} </span>
              <span> 部门：{{ record.node2.info.department }} </span> -->
            </div>
          </div>
        </template>
      </template>
    </a-table>
  </div>
</template>

<style lang="less" scoped></style>
