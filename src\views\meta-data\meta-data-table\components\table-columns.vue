<template>
  <div class="column-info">
    <a-table
      :columns="columns"
      :data-source="tablesRef"
      :pagination="{ pageSize: 10 }"
      :loading="loadingRef"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue'
import { queryColumnInfoFun } from '@/api/matedata/metadatamanagement/metadatamanagement'

const props = defineProps<{
  tableData: {
    dbName: string
    id: number
    schema: string
    tabName: string
    schemaName: string
  }
}>()

const tableData = computed(() => props.tableData)

const emit = defineEmits(['refresh'])

type TableData = Awaited<ReturnType<typeof queryColumnInfoFun>>['data']

const tablesRef = ref<TableData['obj']['fieldData']>([])
const loadingRef = ref<boolean>(false)

const columns = ref<any[]>([
  {
    dataIndex: 'columnName',
    title: '字段名',
    key: 'columnName',
  },
  {
    dataIndex: 'columnType',
    title: '字段类型',
    key: 'columnType',
  },
  {
    dataIndex: 'length',
    title: '长度',
    key: 'length',
  },
  {
    dataIndex: 'comment',
    title: '注释',
    key: 'comment',
  },
])

const fetchTableData = async () => {
  try {
    loadingRef.value = true
    console.log('🚀 ~ fetchTableData ~ tableData.value:', tableData.value)
    await queryColumnInfoFun(tableData.value).then((res) => {
      console.log('🚀 ~ fetchTableData ~ res:', res)
      if (res?.data) {
        tablesRef.value = res.data.obj.fieldData || []
      }
    })
  } catch (error) {
    console.error('获取数据表列表失败:', error)
  } finally {
    loadingRef.value = false
  }
}

watch(
  tableData,
  (newVal) => {
    console.log('🚀 ~ watch ~ newVal:', newVal)
    fetchTableData()
  },
  { deep: true },
)
</script>

<style lang="less" scoped>
.column-info {
  height: 100%;
  .header-div {
    display: flex;
    align-items: center;
    height: 48px;
    padding: 0px 12px 0 20px;
    border-bottom: 1px solid #0000001a;
  }
  .content-div {
    padding: 12px 12px 0 12px;
    height: calc(100% - 48px);
  }
}
:deep(.fs-table .search-btn) {
  display: none;
}
:deep(.ant-btn:hover) {
  .clear-icon {
    opacity: 1 !important;
  }
}
</style>
