<template>
  <a-modal
    v-model:open="show"
    title="修改节点"
    @ok="handleOk"
    @cancel="handleCancel"
    :maskClosable="false"
  >
    <div class="node-info">
      <a-form :model="formState" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
        <a-form-item label="父节点" name="parentNode">
          <a-tree-select
            v-model:value="formState.parentId"
            placeholder="请选择父节点"
            allow-clear
            style="width: 100%"
            :tree-data="treeData"
            :field-names="{
              children: 'children',
              label: 'title',
              value: 'key',
            }"
          >
          </a-tree-select>
        </a-form-item>
        <a-form-item label="节点名称" name="nodeName">
          <a-input v-model:value="formState.nodeName" placeholder="请输入节点名称" />
        </a-form-item>
      </a-form>
    </div>
  </a-modal>
</template>

<script lang="ts" setup>
import type { TreeProps } from 'ant-design-vue'
import { ref, reactive, defineProps, defineEmits, watchEffect } from 'vue'

const props = defineProps({
  // 当前节点信息
  currentNode: {
    type: Object,
    default: () => ({}),
    required: true,
  },
  // 可选的父节点列表
  parentNodes: {
    type: Array as PropType<TreeProps['treeData']>,
    default: () => [],
    required: true,
  },
  // 当前节点的父节点ID
  currentParentId: {
    type: String,
    default: '',
  },
})

const emit = defineEmits(['confirm', 'cancel'])
const treeData = computed(() => {
  if (!props.parentNodes) return []
  // 深度遍历父节点列表，提取出key和title属性
  // 如果节点的key与当前节点的key相等，则禁用该节点
  const processTreeData = (nodes: any[], currentNodeKey: string) => {
    if (!nodes || !Array.isArray(nodes)) return []

    return nodes.map((node) => {
      // 创建新节点对象，包含key、title和disabled属性
      const newNode: any = {
        key: node.key,
        title: node.title,
        disabled: node.key === currentNodeKey,
      }

      // 如果有子节点，递归处理
      if (node.children && node.children.length > 0) {
        newNode.children = processTreeData(node.children, currentNodeKey)
      }

      return newNode
    })
  }

  // 使用computed属性处理树数据
  return processTreeData(props.parentNodes, props.currentNode.key)
})
const show = ref(true)
const formState = reactive({
  nodeName: '',
  parentId: '',
})

// 监听props变化，更新表单值
watchEffect(() => {
  if (props.currentNode) {
    formState.nodeName = props.currentNode.title || ''
  }
  if (props.currentParentId) {
    formState.parentId = props.currentParentId
  }
})

const handleOk = () => {
  if (!formState.nodeName.trim()) {
    return
  }

  emit('confirm', {
    nodeKey: props.currentNode.key,
    newParentKey: formState.parentId,
    newNodeName: formState.nodeName,
  })

  // 重置表单
  formState.nodeName = ''
  formState.parentId = ''
  show.value = false
}

const handleCancel = () => {
  // 重置表单
  formState.nodeName = ''
  formState.parentId = ''
  show.value = false
  emit('cancel')
}
</script>

<style scoped>
.node-info {
  margin-bottom: 16px;
}
</style>
