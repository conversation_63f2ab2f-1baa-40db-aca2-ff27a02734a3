import type { TreeProps } from 'ant-design-vue'

const data = {
  '1': {
    title: '投量与时间变化',
    desc: '分析资源投入的动态趋势',
    options: {
      xAxis: {
        type: 'category',
        data: ['2015', '2016', '2017', '2018', '2019', '2020', '2021', '2022', '2023', '2024'],
      },
      yAxis: {
        type: 'value',
      },
      legend: {
        bottom: '0',
      },
      tooltip: {
        trigger: 'item', // 可以是 'item' 或 'axis'，'axis' 表示鼠标悬停在坐标轴上时显示，'item' 表示悬停在数据项上显示
      },
      label: {
        show: true, // 显示标签
        position: 'top', // 标签位置在顶部
        formatter: '{c}万元', // 显示数据值
      },
      grid: {
        left: '10px',
        right: '4%',
        top: '20px',
        bottom: '30px',
        containLabel: true,
      },
      series: [
        {
          data: [120, 132, 101, 134, 90, 230, 210, 250, 260, 270],
          type: 'line',
          itemStyle: { color: 'rgb(96, 198, 255)' },
        },
      ],
    },
  },
  '2': {
    title: '不同投向的投量对比',
    desc: '对比不同投向的投量以评估资源分配效果',
    options: {
      xAxis: {
        type: 'category',
        data: [
          '公共卫生',
          '医疗服务',
          '健康教育',
          '疾病预防',
          '康复服务',
          '心理健康',
          '营养指导',
          '体育健身',
          '环境健康',
          '职业健康',
        ],
      },
      legend: {
        bottom: '0',
      },
      label: {
        show: true, // 显示标签
        position: 'top', // 标签位置在顶部
        formatter: '{c}万元', // 显示数据值
      },
      yAxis: {
        type: 'value',
      },
      grid: {
        left: '10px',
        right: '4%',
        top: '20px',
        bottom: '30px',
        containLabel: true,
      },
      tooltip: {
        trigger: 'item', // 可以是 'item' 或 'axis'，'axis' 表示鼠标悬停在坐标轴上时显示，'item' 表示悬停在数据项上显示
      },
      series: [
        {
          data: [120, 200, 150, 180, 130, 110, 170, 160, 140, 190],
          type: 'bar',
          itemStyle: { color: 'rgb(96, 198, 255)' },
        },
      ],
    },
  },
  '3': {
    title: '投向的投量占比',
    desc: '分析各投向的投量占比及其资源配置比例',
    options: {
      title: {},
      tooltip: {
        trigger: 'item',
        formatter: '{c}万元',
      },
      legend: {
        bottom: '0',
      },
      grid: {
        left: '20px',
        right: '4%',
        top: '20px',
        bottom: '30px',
        containLabel: true,
      },
      series: [
        {
          name: '投向占比',
          type: 'pie',
          radius: '55%',
          data: [
            { value: 335, name: '公共卫生', itemStyle: { color: 'rgb(96, 198, 255)' } },
            { value: 310, name: '医疗服务', itemStyle: { color: 'rgb(255, 204, 133)' } },
            { value: 234, name: '健康教育', itemStyle: { color: 'rgb(244, 130, 112)' } },
            { value: 215, name: '疾病预防' },
            { value: 180, name: '康复服务' },
            { value: 150, name: '心理健康' },
            { value: 130, name: '营养指导' },
            { value: 120, name: '体育健身' },
            { value: 110, name: '环境健康' },
            { value: 100, name: '职业健康' },
          ],
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)',
            },
          },
        },
      ],
    },
  },
  '4': {
    title: '投向投量与效益',
    desc: '评估不同投向投量对效益的影响与回报',
    columns: [
      {
        title: '投向',
        dataIndex: 'direction',
        key: 'direction',
        sorter: (a: any, b: any) => a.age - b.age,
      },
      {
        title: '投量',
        dataIndex: 'amount',
        key: 'amount',
        sorter: (a: any, b: any) => a.age - b.age,
      },
      {
        title: '效益',
        dataIndex: 'effect',
        key: 'effect',
        sorter: (a: any, b: any) => a.age - b.age,
      },
      { title: '操作', dataIndex: 'operation', key: 'operation' },
    ],
    dataSource: [
      { key: '1', direction: '公共卫生', amount: '120万元', effect: '高' },
      { key: '2', direction: '医疗服务', amount: '200万元', effect: '中' },
      { key: '3', direction: '健康教育', amount: '150万元', effect: '低' },
      { key: '4', direction: '疾病预防', amount: '180万元', effect: '高' },
      { key: '5', direction: '康复服务', amount: '130万元', effect: '中' },
      { key: '6', direction: '心理健康', amount: '110万元', effect: '低' },
      { key: '7', direction: '营养指导', amount: '170万元', effect: '高' },
      { key: '8', direction: '体育健身', amount: '160万元', effect: '中' },
      { key: '9', direction: '环境健康', amount: '140万元', effect: '低' },
      { key: '10', direction: '职业健康', amount: '190万元', effect: '高' },
    ],
  },
  '5': {
    title: '投量对服务影响',
    desc: '不同投量对各项服务的绩效影响',
    options: {
      xAxis: {
        type: 'value',
        name: '投量 (万元)',
      },
      tooltip: {
        trigger: 'item',
      },
      yAxis: {
        type: 'value',
        name: '绩效得分',
      },
      legend: {
        bottom: '0',
      },
      series: [
        {
          name: '公共卫生',
          type: 'scatter',
          data: [
            [1200, 85],
            [1500, 88],
            [1800, 90],
          ],
        },
        {
          name: '医疗服务',
          type: 'scatter',
          data: [
            [1400, 82],
            [1700, 87],
            [2000, 89],
          ],
        },
        {
          name: '健康教育',
          type: 'scatter',
          data: [
            [1000, 78],
            [1300, 80],
            [1600, 85],
          ],
        },
        {
          name: '疾病预防',
          type: 'scatter',
          data: [
            [1100, 80],
            [1400, 83],
            [1700, 88],
          ],
        },
      ],
    },
  },
  '6': {
    title: '领域指标对比',
    desc: '对比分析不同领域的绩效差异',
    options: {
      tooltip: {
        trigger: 'item',
      },
      radar: {
        indicator: [
          { name: '公共卫生', max: 100 },
          { name: '医疗服务', max: 100 },
          { name: '健康教育', max: 100 },
          { name: '疾病预防', max: 100 },
          { name: '康复服务', max: 100 },
          { name: '心理健康', max: 100 },
          { name: '营养指导', max: 100 },
          { name: '体育健身', max: 100 },
          { name: '环境健康', max: 100 },
          { name: '职业健康', max: 100 },
        ],
      },
      series: [
        {
          name: '区域1 vs 区域2',
          type: 'radar',
          data: [
            {
              value: [80, 75, 85, 90, 70, 65, 60, 80, 85, 78],
              name: '区域1',
            },
            {
              value: [85, 80, 80, 85, 75, 70, 68, 82, 88, 80],
              name: '区域2',
            },
          ],
        },
      ],
    },
  },
  '7': {
    title: '领域绩效',
    desc: '投量与服务领域绩效的关联',
    options: {
      tooltip: {
        trigger: 'item',
      },
      grid: {
        left: '20px',
        right: '4%',
        top: '0',
        bottom: '30px',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        data: ['1000', '1200', '1500', '1800', '2000', '2200'],
      },
      yAxis: {
        type: 'category',
        data: [
          '公共卫生',
          '医疗服务',
          '健康教育',
          '疾病预防',
          '康复服务',
          '心理健康',
          '营养指导',
          '体育健身',
          '环境健康',
          '职业健康',
        ],
      },
      series: [
        {
          name: '投量-绩效',
          type: 'heatmap',
          data: [
            [0, 0, 5],
            [0, 1, 7],
            [0, 2, 6],
            [0, 3, 8],
            [0, 4, 7],
            [1, 0, 6],
            [1, 1, 9],
            [1, 2, 8],
            [1, 3, 9],
            [1, 4, 10],
            [2, 0, 7],
            [2, 1, 10],
            [2, 2, 9],
            [2, 3, 11],
            [2, 4, 10],
            [3, 0, 8],
            [3, 1, 11],
            [3, 2, 10],
            [3, 3, 12],
            [3, 4, 11],
            [4, 0, 9],
            [4, 1, 12],
            [4, 2, 11],
            [4, 3, 13],
            [4, 4, 12],
            [5, 0, 10],
            [5, 1, 13],
            [5, 2, 12],
            [5, 3, 14],
            [5, 4, 13],
            [1, 5, 9],
            [2, 6, 11],
            [3, 7, 13],
            [4, 8, 15],
            [5, 9, 14],
          ],
        },
      ],
      visualMap: {
        min: 5,
        max: 15,
        calculable: true,
        orient: 'vertical',
        left: 'right',
        top: 'center',
        inRange: {
          color: ['#e0ffff', '#006edd'],
        },
      },
    },
  },
}

export const treeData: TreeProps['treeData'] = [
  {
    title: '东部战区',
    key: '0-0',
    children: [
      {
        title: 'xx医院',
        key: '0-0-0',
        isLeaf: true,
      },
      {
        title: 'xx医院',
        key: '0-0-1',
        isLeaf: true,
      },
    ],
  },
  {
    title: '西部战区',
    key: '0-1',
    children: [
      {
        title: 'xx医院',
        key: '0-1-0',
        isLeaf: true,
      },
      {
        title: 'xx医院',
        key: '0-1-1',
        isLeaf: true,
      },
    ],
  },
  {
    title: '中部战区',
    key: '0-2',
    children: [
      {
        title: 'xx医院',
        key: '0-1-0',
        isLeaf: true,
      },
      {
        title: 'xx医院',
        key: '0-1-1',
        isLeaf: true,
      },
    ],
  },
  {
    title: '南部战区',
    key: '0-3',
    children: [
      {
        title: 'xx医院',
        key: '0-1-0',
        isLeaf: true,
      },
      {
        title: 'xx医院',
        key: '0-1-1',
        isLeaf: true,
      },
    ],
  },
  {
    title: '北部战区',
    key: '0-4',
    children: [
      {
        title: 'xx医院',
        key: '0-1-0',
        isLeaf: true,
      },
      {
        title: 'xx医院',
        key: '0-1-1',
        isLeaf: true,
      },
    ],
  },
]

export const tableColumns = [
  {
    title: '样本信息',
    children: [
      {
        title: '统计周期',
        dataIndex: 'statistical',
        key: 'statistical',
      },
      {
        title: '组织机构名称',
        dataIndex: 'organizationName',
        key: 'organizationName',
      },
    ],
  },
  {
    title: '有效性分析',
    children: [
      {
        title: '综合效率OE',
        dataIndex: 'comprehensive',
        key: 'comprehensive',
      },
      {
        title: '松弛变量S-',
        dataIndex: 'slackVariableDel',
        key: 'slackVariableDel',
      },
      {
        title: '松弛变量S+',
        dataIndex: 'slackVariableAdd',
        key: 'slackVariableAdd',
      },
      {
        title: '有效性',
        dataIndex: 'effective',
        key: 'effective',
      },
    ],
  },
]
export const tableColumns2 = [
  {
    title: '样本信息',
    children: [
      {
        title: '统计周期',
        dataIndex: 'statistical',
        key: 'Sstatistical',
      },
      {
        title: '组织机构名称',
        dataIndex: 'organizationName',
        key: 'organizationName',
      },
    ],
  },
  {
    title: '投入冗余分析',
    children: [
      {
        title: '松弛变量S-分析',
        children: [
          {
            title: '床位数',
            dataIndex: 'bedCount',
            key: 'bedCount',
          },
          {
            title: '医护数量',
            dataIndex: 'medicalStaff',
            key: 'medicalStaff',
          },
          {
            title: '汇总',
            dataIndex: 'collect',
            key: 'collect',
          },
        ],
      },
      {
        title: '投入冗余率',
        children: [
          {
            title: '床位数',
            dataIndex: 'bedCount2',
            key: 'bedCount2',
          },
          {
            title: '医护数量',
            dataIndex: 'medicalStaff2',
            key: 'medicalStaff2',
          },
        ],
      },
    ],
  },
]

export const tableColumns3 = [
  {
    title: '样本信息',
    children: [
      {
        title: '统计周期',
        dataIndex: 'statistical',
        key: 'Sstatistical',
      },
      {
        title: '组织机构名称',
        dataIndex: 'organizationName',
        key: 'organizationName',
      },
    ],
  },
  {
    title: '规模报酬分析',
    children: [
      {
        title: '规模报酬系数',
        dataIndex: 'returnScale',
        key: 'returnScale',
      },
      {
        title: '类型',
        dataIndex: 'type',
        key: 'type',
      },
    ],
  },
]
export const tableColumns4 = [
  {
    title: '样本信息',
    children: [
      {
        title: '统计周期',
        dataIndex: 'statistical',
        key: 'Sstatistical',
      },
      {
        title: '组织机构名称',
        dataIndex: 'organizationName',
        key: 'organizationName',
      },
    ],
  },
  {
    title: '产出不足分析',
    children: [
      {
        title: '松弛变量S+分析',
        children: [
          {
            title: '出院人次',
            dataIndex: 'dischargedPatients',
            key: 'dischargedPatients',
          },
          {
            title: '医护数量',
            dataIndex: 'averageStay',
            key: 'averageStay',
          },
          {
            title: '医疗服务收入占比',
            dataIndex: 'proportionMedical',
            key: 'proportionMedical',
          },
          {
            title: '汇总',
            dataIndex: 'collect',
            key: 'collect',
          },
        ],
      },
      {
        title: '产出不足率',
        children: [
          {
            title: '出院人次',
            dataIndex: 'dischargedPatients2',
            key: 'dischargedPatients2',
          },
          {
            title: '平均住院日',
            dataIndex: 'averageStay2',
            key: 'averageStay2',
          },
          {
            title: '医疗服务收入占比',
            dataIndex: 'income',
            key: 'income',
          },
        ],
      },
    ],
  },
]

export const tableDataSource = [
  {
    key: '1',
    statistical: '2021年',
    organizationName: '医院1',
    comprehensive: '1.000',
    slackVariableDel: '1.000',
    slackVariableAdd: '1.000',
    effective: 'DEA强有效',
  },
  {
    key: '2',
    statistical: '2021年',
    organizationName: '医院2',
    comprehensive: '0.950',
    slackVariableDel: '0.950',
    slackVariableAdd: '0.950',
    effective: '非DEA强有效',
  },
  {
    key: '3',
    statistical: '2021年',
    organizationName: '医院3',
    comprehensive: '0.8254',
    slackVariableDel: '0.8254',
    slackVariableAdd: '0.8254',
    effective: '非DEA强有效',
  },
  {
    key: '4',
    statistical: '2021年',
    organizationName: '医院4',
    comprehensive: '0.723',
    slackVariableDel: '0.723',
    slackVariableAdd: '0.723',
    effective: '非DEA强有效',
  },
  {
    key: '5',
    statistical: '2021年',
    organizationName: '医院5',
    comprehensive: '0.692',
    slackVariableDel: '0.692',
    slackVariableAdd: '0.692',
    effective: '非DEA强有效',
  },
]

export const tableDataSource2 = [
  {
    key: '1',
    statistical: '2021年',
    organizationName: '医院1',
    bedCount: '0',
    medicalStaff: '',
    collect: '',
    bedCount2: '',
    medicalStaff2: '',
  },
  {
    key: '2',
    statistical: '2022年',
    organizationName: '医院2',
    bedCount: '0',
    medicalStaff: '',
    collect: '',
    bedCount2: '',
    medicalStaff2: '',
  },
  {
    key: '3',
    statistical: '2023年',
    organizationName: '医院3',
    bedCount: '0',
    medicalStaff: '',
    collect: '',
    bedCount2: '',
    medicalStaff2: '',
  },
  {
    key: '4',
    statistical: '2024年',
    organizationName: '医院4',
    bedCount: '0',
    medicalStaff: '',
    collect: '',
    bedCount2: '',
    medicalStaff2: '',
  },
  {
    key: '5',
    statistical: '2025年',
    organizationName: '医院5',
    bedCount: '0',
    medicalStaff: '',
    collect: '',
    bedCount2: '',
    medicalStaff2: '',
  },
]
export const tableDataSource3 = [
  {
    key: '1',
    statistical: '2021年',
    organizationName: '医院1',
    returnScale: '1.0',
    type: '规模报酬不变（CRS）',
  },
  {
    key: '2',
    statistical: '2022年',
    organizationName: '医院2',
    returnScale: '0.92',
    type: '规模报酬递增（IRS）',
  },
  {
    key: '3',
    statistical: '2023年',
    organizationName: '医院3',
    returnScale: '0.12',
    type: '规模报酬递增（DRS）',
  },
  {
    key: '4',
    statistical: '2024年',
    organizationName: '医院4',
    returnScale: '0.342',
    type: '规模报酬递增（IRS）',
  },
  {
    key: '5',
    statistical: '2025年',
    organizationName: '医院5',
    returnScale: '0.342',
    type: '规模报酬递增（IRS）',
  },
]

export const tableDataSource4 = [
  {
    key: '1',
    statistical: '2021年',
    organizationName: '医院1',
    dischargedPatients: '',
    averageStay: '',
    proportionMedical: '',
    collect: '',
    dischargedPatients2: '',
    averageStay2: '',
    income: '',
  },
  {
    key: '2',
    statistical: '2022年',
    organizationName: '医院2',
    dischargedPatients: '',
    averageStay: '',
    proportionMedical: '',
    collect: '',
    dischargedPatients2: '',
    averageStay2: '',
    income: '',
  },
  {
    key: '3',
    statistical: '2023年',
    organizationName: '医院3',
    dischargedPatients: '',
    averageStay: '',
    proportionMedical: '',
    collect: '',
    dischargedPatients2: '',
    averageStay2: '',
    income: '',
  },
  {
    key: '4',
    statistical: '2024年',
    organizationName: '医院4',
    dischargedPatients: '',
    averageStay: '',
    proportionMedical: '',
    collect: '',
    dischargedPatients2: '',
    averageStay2: '',
    income: '',
  },
  {
    key: '5',
    statistical: '2025年',
    organizationName: '医院5',
    dischargedPatients: '',
    averageStay: '',
    proportionMedical: '',
    collect: '',
    dischargedPatients2: '',
    averageStay2: '',
    income: '',
  },
]

export const dlgColumns = [
  {
    title: '指标类型',
    dataIndex: 'indicatorType',
    width: '30%',
  },
  {
    title: '指标名称',
    dataIndex: 'indicatorName',
  },
  {
    title: '指标数值',
    dataIndex: 'indicatorValue',
  },
  {
    title: '操作',
    dataIndex: 'operation',
  },
]

export interface DataItem {
  key: number
  indicatorType: string
  indicatorName: string
  indicatorValue: number
}

export const indicatorNameArr1 = [
  { label: '医护人数', value: '医护人数' },
  { label: '实际开放床位数', value: '实际开放床位数' },
]
export const indicatorNameArr2 = [
  { label: '住院人次(万)', value: '住院人次(万)' },
  { label: '出院人次(万)', value: '出院人次(万)' },
  { label: '医疗收入占比', value: '医疗收入占比' },
  { label: '医疗收入(万)', value: '医疗收入(万)' },
  { label: '平均住院天数', value: '平均住院天数' },
  { label: '医疗服务占比', value: '医疗服务占比' },
]

export const indicatorType = [
  { label: '投入', value: '投入' },
  { label: '产出', value: '产出' },
]

export default data
