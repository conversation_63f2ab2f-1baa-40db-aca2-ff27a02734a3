<template>
  <div class="knowledge-exploration">
    <div class="header-box">
      <div class="left">知识探查</div>
    </div>
    <a-form
      ref="formRef"
      name="advanced_search"
      class="search-form"
      :model="formState"
      :rules="rules"
      @finish="onFinish"
    >
      <a-row :gutter="24">
        <a-col :span="8">
          <a-form-item name="label" label="知识类型">
            <a-select
              :field-names="{ label: 'allName', value: 'name' }"
              v-model:value="formState.label"
              :dropdown-style="{ minWidth: '300px' }"
              :options="options"
            ></a-select>
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item name="queryStr" label="知识名称">
            <a-input v-model:value="formState.queryStr" placeholder="请输入知识名称"></a-input>
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item style="text-align: right">
            <a-button @click="formRef?.resetFields()">重置</a-button>
            <a-button type="primary" style="margin-left: 8px" html-type="submit">查询</a-button>
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
    <a-table :columns="columns" :dataSource="tableData">
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'fields.name'">
          <span class="file-name">{{ record.fields.name }}</span>
        </template>
        <template v-if="column.key === 'fields.id'">
          <a-tooltip :title="record.fields.id">
            <span class="file-name">{{ record.fields.id }}</span>
          </a-tooltip>
        </template>
        <template v-if="column.key === 'fields.content'">
          <a-tooltip
            :title="record.fields.content"
            :overlayStyle="{ width: 500, background: '#f00' }"
            trigger="click"
          >
            <span class="file-name">{{ record.fields.content }}</span>
          </a-tooltip>
        </template>
      </template>
    </a-table>
  </div>
</template>

<script lang="ts" setup>
import { reactive, ref, onMounted, toRaw } from 'vue'
import { type FormInstance } from 'ant-design-vue'
import { reqGetChartData, reqGetKnowledgeData } from '@/api/services/knowledge'
import { useRoute } from 'vue-router'
import { columns } from './assets-data'

const initOption = [
  {
    label: '全部',
    allName: '全部',
    name: 'all',
    value: 'all',
  },
]

const route = useRoute()
const formRef = ref<FormInstance>()
const options = ref(initOption)
const formState = reactive<Record<string, any>>({
  label: 'all',
  queryStr: '',
})
const rules = reactive({
  label: [{ required: true, message: '知识类型不能为空' }],
  queryStr: [{ required: true, message: '知识名称不能为空', trigger: 'change' }],
})
const tableData = ref([])

const onFinish = (values: any) => {
  console.log('Received values of form: ', values)
  console.log('formState: ', toRaw(formState))
  getTableData()
}

const getSelectList = async () => {
  const res = await reqGetChartData({
    projectId: route.query?.projectId,
  })
  const list = res.entityTypeDTOList.map((item: any) => {
    return {
      ...item,
      allName: `${item.nameZh}(${item.name})`,
    }
  })
  options.value = [...initOption, ...list]
}

const getTableData = async () => {
  const params = {
    page: 1,
    size: 1000,
    projectId: route.query?.projectId,
    matchExactOnly: false,
    ...toRaw(formState),
  }
  const res = await reqGetKnowledgeData(params)
  tableData.value = res.results
  console.log('啦啦啦啦', res)
}

onMounted(() => {
  getSelectList()
})
</script>
<style scoped lang="less">
.knowledge-exploration {
  height: 100%;
  .header-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 50px;
    .left {
      font-size: 20px;
      line-height: 28px;
      font-weight: 500;
      color: #000;
    }
    .right {
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }
  .search-form {
    background: #fff;
    box-shadow:
      0px 0px 1px 0px #00000014,
      0px 1px 2px 0px #190f0f12,
      0px 2px 4px 0px #0000000d;
    padding-bottom: 0;
    border-radius: 4px;
    margin-bottom: 16px;
  }
  .file-name {
    display: inline-block;
    width: 200px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}
</style>
