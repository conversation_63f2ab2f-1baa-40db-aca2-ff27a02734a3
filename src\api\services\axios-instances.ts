import axios, { type AxiosRequestConfig } from 'axios'
import { message } from 'ant-design-vue'
import { useUserStore } from '@/stores/user'
import { SYSTEM_CODE } from '../base'

axios.defaults.withCredentials = true
// document.cookie = TEMPCOOKIE
// Cookies.clearLoginData()
// Cookies.set(
//   'OPEN_SPG_TOKEN',
//   '9rh2KRIbSE7K7dtcMZBDqSVfPKprlmWx2R1B6I+ZLVU5pe6I7XUPcMFNScdXfkkKrO/7WJoElGz2aUvL+td5uNYSf+h4cU4l',
// )
// Cookies.set('x-hng', 'lang=zh-CN')
// Cookies.set('ctoken', 'bigfish_ctoken_19h4g5f99b')

axios.defaults.withCredentials = true

// 设置请求拦截器
axios.interceptors.request.use(
  (config: any) => {
    // 添加通用的 Cookie
    // config.headers.Cookie = TEMPCOOKIE;
    config.headers.Systemcode = SYSTEM_CODE
    const userStore = useUserStore()
    const token = userStore.token
    const { userId, buId, tenantId, userName } = userStore.userInfo
    if (token) {
      config.headers = {
        Authorization: token,
        Opuser: userId,
        opUserName: encodeURIComponent(userName),
        Buid: buId,
        TenantId: tenantId,
        ...config.headers,
      }
    }
    // 返回修改后的配置
    return config
  },
  (error) => {
    // 处理请求错误
    return Promise.reject(error)
  },
)

// 设置响应拦截器
axios.interceptors.response.use(
  (response) => {
    // 对响应数据进行处理
    if (response.data && response.data.code !== '000000') {
      // 如果后端返回的状态码不是 200，显示错误信息
      message.error(response.data.errorMsg || '请求失败')
      return Promise.reject(response.data)
    }
    return response.data.data // 返回处理后的数据
  },
  (error) => {
    // 处理 HTTP 错误
    if (error.response) {
      const { status } = error.response

      if (status === 401) {
        // 未授权，跳转到登录页面
        message.error('登录已过期，请重新登录')
        // router.push('/login');
      } else if (status === 500) {
        // 服务器错误
        message.error('服务器内部错误，请稍后重试')
      } else {
        // 其他错误
        message.error(error.response.data.errorMsg || '请求失败')
      }
    } else {
      // 网络错误或其他问题
      message.error('网络错误，请检查您的网络连接')
    }
    return Promise.reject(error) // 返回错误信息
  },
)

export default axios

export const request = {
  get<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
    return axios.get(url, config)
  },
  post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    return axios.post(url, data, config)
  },
  put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    return axios.put(url, data, config)
  },
  delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
    return axios.delete(url, config)
  },
  patch<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    return axios.patch(url, data, config)
  },
}
