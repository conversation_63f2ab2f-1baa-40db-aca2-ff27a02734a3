@import './base.css';
@import '@fs/fs-components/dist/style.css';

:root {
  --primary-color: #3568d8;
  --color-text: #333;
  --new-primary-color:  rgb(9, 104, 218);
  --new-hover-primary-color:  #2e87e6;
}

/* layout布局的 header 高度 */
@layout-header-height: 58px;
/* layout布局的 sidebar 宽度 */
@layout-sidebar-width: 60px;


ul,
li {
  list-style-type: none;
  padding: 0;
  margin: 0;
}

#app {
  color: var(--color-text);
}

// 生成布局弹窗下layout样式兼容处理
.modal-fs-layout {
  .modal-content {
    .fs-layout,
    .layout-view-crumbs,
    .layout-view-top,
    .main,
    .preview {
      height: 100% !important;
    }
    .children-menu-fixed {
      height: 670px;
      overflow: auto;
    }
    .ant-layout-sider {
      height: 670px;
      overflow: auto;
    }

    .preview {
      .login-container,
      .cloud-login-content,
      .cloud-login-content_form {
        height: 100%;
      }
      .fs-login {
        width: 100%;
        top: 50%;
        left: 50%;
        margin-top: -195px;
        margin-left: -184px;
      }
    }
  }
}

// 消除登录页自动填充背景色
.fs-login {
  input:-webkit-autofill,
  input:-webkit-autofill:hover,
  input:-webkit-autofill:focus,
  input:-webkit-autofill:active {
    transition: background-color 5000s ease-in-out 0s;
  }
}

.ml-10 {
  margin-left: 10px;
}

.mt-10 {
  margin-top: 10px;
}

.mb-10 {
  margin-bottom: 10px;
}

.mr-10 {
  margin-right: 10px;
}

.p-10 {
  padding: 10px;
}

.m-10 {
  margin: 10px;
}

body {
  color: #4c5773;
}

.menu-item-title {
  font-size: 12px;
  color: #696e7b;
  padding: 4px 0px;
  margin: 0;
  font-weight: 600;
  display: inline-block;
}

// 配置所有滚动条样式
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 10px;
}

.expression-editor {
  width: 472px;
  padding: 24px;
}

.text-right {
  text-align: right;
}
.log-text {
  max-width: 400px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
button {
  background-color: unset;
}
.bg-ease {
  transition: background 300ms ease 0s;
}
.where-btn {
  color: rgb(113, 114, 173);
  background-color: rgba(113, 114, 173, 0.2);
  .bg-ease;
  &:hover {
    color: rgb(93, 94, 160);
    background-color: rgba(113, 114, 173, 0.35);
  }
}

.group-btn,
.summary-btn {
  color: rgb(136, 191, 77);
  background-color: rgba(136, 191, 77, 0.2);
  .bg-ease;
  &:hover {
    color: rgb(121, 174, 63);
    background-color: rgba(136, 191, 77, 0.35);
  }
}
.joins-btn {
  color: rgb(80, 158, 227);
  background-color: rgba(80, 158, 227, 0.2);
  .bg-ease;
  &:hover {
    color: rgb(50, 141, 222);
    background-color: rgba(80, 158, 227, 0.35);
  }
}
.customField-btn,
.limitValue-btn,
.order-btn {
  color: rgb(147, 161, 171);
  .bg-ease;
  &:hover {
    color: rgb(126, 143, 155);
    background-color: rgb(236, 239, 240);
  }
}

// 数据资产管理 标签 级联
.database-table-cascader {
  .ant-cascader-menu {
    width: 200px;
  }
}

.fs-table .ant-table .ant-table-thead .ant-table-cell {
  color: #757575;
}
.ant-table .ant-table-thead .ant-table-cell {
  color: #757575;
}
.extract-task-log .fs-table  {
  height: auto !important;
  .search-btn{
    display: none;
  }
}

.ant-table {
  border-left: 1px solid #f0f0f0;
  border-right: 1px solid #f0f0f0;
  border-top: 1px solid #f0f0f0;
  border-bottom: 1px solid #f0f0f0;
  border-radius: 10px !important;
}
.ant-table .ant-table-row:last-child .ant-table-cell {
  border-bottom: 0px !important;
}

.fs-table {
  .ant-form .ant-form-item .ant-row .ant-input,.ant-select-selector,.ant-picker{
    background-color: white !important;
    border-radius: 4px !important;
  }
  .ant-picker {
    .ant-picker-input >input:placeholder-shown{
      background-color: white !important;
      border-radius: 4px !important;
    }
  }
  .ant-form-item {
    .ant-picker-input input{
      background-color: white !important;
        border-radius: 4px !important;
    }
    .ant-select-selection-search-input{
      background-color: transparent !important;
    }
  }
}
.fs-table,.ant-table :not(.ant-table-row-expand-icon-cell):not([colspan])::before{
  height: 0 !important;
}
.ant-modal .fs-table  {
  height: auto !important;
}

.ant-btn-primary{
  background: var(--new-primary-color) ;
  border-color: var(--new-primary-color) ;
}
.ant-btn-primary:not(:disabled):hover{
  background:   var(--new-hover-primary-color) ;
  border-color: var(--new-hover-primary-color) ;
}
.ant-btn-link{
  color: var(--new-primary-color);
}
.ant-btn-link:not(:disabled):hover{
  color: var(--new-hover-primary-color);
}
