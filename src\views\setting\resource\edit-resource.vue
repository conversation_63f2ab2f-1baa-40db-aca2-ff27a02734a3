<template>
  <a-modal
    :title="title"
    destroyOnClose
    v-model:open="modalVisible"
    :centered="true"
    :maskClosable="false"
  >
    <a-form layout="vertical" :model="formItem" :rules="formRules" ref="resourceForm">
      <a-form-item label="资源名称" prop="serviceName">
        <a-input v-model:value="formItem.serviceName" placeholder="请输入资源名称" />
      </a-form-item>
      <a-form-item label="资源类型" prop="serviceType">
        <a-select
          v-model:value="formItem.serviceType"
          @change="changeServiceType"
          placeholder="请选择数资源类型"
        >
          <a-select-option :value="2">执行脚本节点</a-select-option>
          <a-select-option :value="3">flink</a-select-option>
          <a-select-option :value="4">spark</a-select-option>
          <a-select-option :value="6">模型服务</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="IP地址" prop="serviceIp">
        <a-input v-model:value="formItem.serviceIp" placeholder="请输入IP地址" />
      </a-form-item>
      <a-form-item label="服务端口" v-if="[3, 4].includes(formItem.serviceType)" prop="servicePort">
        <a-input-number
          :min="0"
          v-model:value="formItem.servicePort"
          placeholder="请输入服务端口"
          style="width: 100%"
        />
      </a-form-item>
      <a-form-item label="SSH端口" prop="sshPort">
        <a-input-number
          :min="0"
          v-model:value="formItem.sshPort"
          placeholder="请输入SSH端口"
          style="width: 100%"
        />
      </a-form-item>
      <a-form-item prop="logicId">
        <template #label>逻辑ID<strong>【环境资源映射】</strong></template>
        <a-input v-model:value="formItem.logicId" placeholder="请输入逻辑ID" />
      </a-form-item>
      <a-form-item label="用户名" prop="hostUser">
        <a-input v-model:value="formItem.hostUser" placeholder="请输入用户名" />
      </a-form-item>
      <a-form-item label="密码" prop="hostPwd">
        <a-input v-model:value="formItem.hostPwd" type="password" placeholder="请输入密码" />
      </a-form-item>
      <a-form-item prop="execPath">
        <template #label>执行目录<strong>【需目录已存在】</strong></template>
        <a-input v-model:value="formItem.execPath" placeholder="请输入执行目录" />
      </a-form-item>
      <a-form-item prop="logPath">
        <template #label>日志目录<strong>【需目录已存在】</strong></template>
        <a-input v-model:value="formItem.logPath" placeholder="请输入日志目录" />
      </a-form-item>
    </a-form>
    <template v-slot:footer>
      <a-button key="back" @click="modalVisible = false">取消</a-button>
      <a-button key="submit" type="primary" :loading="submitLoading" @click="formSubmit"
        >提交</a-button
      >
    </template>
  </a-modal>
</template>

<script lang="ts" setup>
import { ref, watch, computed, reactive, defineExpose, defineEmits } from 'vue'
import { formatTime } from '@/utils/time'
import { addResource, editResource, ResourceDTO } from '@/api/setting/resource'
import { message } from 'ant-design-vue'
import { useUserStore } from '@/stores/user'
import { type ResouceAction } from './resource.d'

const emit = defineEmits(['success'])

const userStore = useUserStore()

const ownerId = computed(() => userStore.userInfo.ownerId)

const userId = computed(() => userStore.userInfo.userId)

const resourceForm = ref()

const modalVisible = ref(false)
const modalType = ref('add')
const submitLoading = ref(false)
const formItem = reactive<{
  serviceName: string
  serviceIp: string
  sshPort: number
  servicePort: string | number
  serviceType: number
  logicId: string
  hostUser: string
  hostPwd: string
  execPath: string
  logPath: string
}>({
  serviceName: '',
  serviceIp: '',
  sshPort: 22,
  servicePort: '',
  serviceType: 2,
  logicId: '',
  hostUser: '',
  hostPwd: '',
  execPath: '',
  logPath: '',
})
const formRules = {
  serviceIp: [
    {
      required: true,
      message: '请输入IP地址',
      trigger: 'blur',
    },
  ],
  sshPort: [
    {
      required: true,
      message: '请输入SSH端口',
      trigger: 'blur',
    },
  ],
  servicePort: [
    {
      required: true,
      message: '请输入服务端口',
      trigger: 'blur',
    },
  ],
  hostUser: [
    {
      required: true,
      message: '请输入用户名',
      trigger: 'blur',
    },
  ],
  hostPwd: [
    {
      required: true,
      message: '请输入密码',
      trigger: 'blur',
    },
  ],
  execPath: [
    {
      required: true,
      message: '请输入执行目录',
      trigger: 'blur',
    },
  ],
  logPath: [
    {
      required: true,
      message: '请输入日志目录',
      trigger: 'blur',
    },
  ],
}

watch(modalVisible, (val) => {
  if (!val) {
    Object.assign(formItem, {
      serviceName: '',
      serviceIp: '',
      sshPort: 22,
      servicePort: '',
      serviceType: undefined,
      logicId: '',
      hostUser: '',
      hostPwd: '',
      execPath: '',
      logPath: '',
    })
  }
})

const title = computed(() => {
  return modalType.value === 'add' ? '新增资源' : modalType.value === 'edit' ? '修改资源' : ''
})

function changeServiceType(value: number) {
  switch (value) {
    case 3:
      formItem.servicePort = 8087
      break
    case 4:
      formItem.servicePort = 9998
      break
    default:
      formItem.servicePort = ''
  }
}

function formSubmit() {
  resourceForm.value.validate().then(() => {
    submitLoading.value = true
    let params = JSON.parse(JSON.stringify(formItem))
    params.ownerId = ownerId.value
    params.createUser = userId.value
    if (params.id && modalType.value === 'edit') {
      params.updateTime = formatTime(new Date())
      params.createUser = userId.value
      if (/^\*+$/.test(params.hostPwd)) delete params.hostPwd
      editResource(params)
        .then((res) => {
          closeModal('修改成功')
        })
        .finally(() => {
          submitLoading.value = false
        })
    } else {
      params.createTime = formatTime(new Date())
      addResource(params)
        .then((res) => {
          closeModal('新增成功')
        })
        .finally(() => {
          submitLoading.value = false
        })
    }
  })
}

function showModal(_modalType: string, record?: ResourceDTO) {
  modalType.value = _modalType
  if (_modalType === 'edit') {
    const val = JSON.parse(JSON.stringify(record))
    Object.assign(formItem, {
      serviceName: val.serviceName,
      serviceIp: val.serviceIp,
      sshPort: val.sshPort,
      servicePort: val.servicePort,
      serviceType: val.serviceType,
      logicId: val.logicId,
      hostUser: val.hostUser,
      hostPwd: val.hostPwd,
      execPath: val.execPath,
      logPath: val.logPath,
    })
  }
  modalVisible.value = true
}

function closeModal(msg: string) {
  modalVisible.value = false
  emit('success')
  message.success(msg)
}
defineExpose({
  showModal,
} satisfies ResouceAction)
</script>
