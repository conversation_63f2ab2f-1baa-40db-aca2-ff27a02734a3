<template>
  <div class="meta-data-table">
    <cardBox :title="tableName" :subTitle="tableComment ? tableComment : '暂无注释'">
      <a-tabs v-model:activeKey="activeKey" class="meta-data-table-tabs">
        <template #leftExtra>
          <span type="link" @click="goBack" class="go-back-btn">
            <ArrowLeftOutlined />
          </span>
        </template>
        <a-tab-pane key="1">
          <template #tab>
            <span>
              <UnorderedListOutlined />
              列
            </span>
          </template>
          <TableColumns :table-data="queryParams" style="margin-top: 8px"></TableColumns>
        </a-tab-pane>
        <a-tab-pane key="2" force-render>
          <template #tab>
            <span>
              <TableOutlined />
              数据预览
            </span>
          </template>
          <DataPreview :table-data="queryParams" style="margin-top: 8px"></DataPreview>
        </a-tab-pane>
      </a-tabs>
    </cardBox>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { UnorderedListOutlined, TableOutlined, ArrowLeftOutlined } from '@ant-design/icons-vue'
import DataPreview from './components/data-preview.vue'
import TableColumns from './components/table-columns.vue'
import { useRouter, useRoute } from 'vue-router'
import cardBox from '@/components/card-box/card-box.vue'

const activeKey = ref('1')
const route = useRoute()
const router = useRouter()
const tableName = computed(() => (route.query.tabName as string) || '')
const tableComment = computed(() => (route.query.tableComment as string) || '')

type QueryParams = {
  id: number
  schema: string
  tabName: string
  dbName: string
  dbSchema: string
}
const queryParams = ref<QueryParams>()

onMounted(() => {
  const query = route.query as unknown as QueryParams
  queryParams.value = {
    id: Number(query.id),
    schema: query.schema,
    tabName: query.tabName,
    dbName: query.dbName,
    dbSchema: query.dbSchema,
  }
})

const goBack = () => {
  const { token, id, parentKey, dbName } = route.query
  router.push({
    name: 'meta-data-management',
    query: {
      token,
      id,
      parentKey,
      dbName,
    },
  })
}
</script>

<style lang="less" scoped>
.meta-data-table {
  flex: auto;
  background: white;
  height: 100%;
  .header-div {
    display: flex;
  }
  .table-detail-row {
    height: 100%;
  }
  .table-detail-col {
    height: 100%;
    border-right: 1px solid #d7d7d7;
    padding: 12px 12px 0 12px;
    background: white;
  }
  .content {
    padding: 0px;
  }
  .table-detail-col:last-of-type {
    border-width: 0px;
  }
}

.meta-data-table-tabs {
  width: 100%;
  height: 100%;
  display: flex;

  :deep(.ant-tabs-content) {
    height: 100%;
  }
  :deep(.ant-tabs-tabpane) {
    flex: auto;
    overflow: hidden;
  }
}
.go-back-btn {
  color: var(--primary-color);
  // margin-left: 5px;
  margin-right: 10px;
  cursor: pointer;
}
</style>
