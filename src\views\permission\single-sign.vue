<template>
  <a-card class="order-page">
    <Table :columns="columns" :getData="getSsoAppList" ref="tableRef" :searchFormState="formState">
      <template #operate>
        <a-button type="primary" @click="addTable">新增</a-button>
      </template>
      <template #search>
        <a-form-item label="应用名称" name="应用名称" :rules="[{ message: '请输入应用名称' }]">
          <a-input v-model:value="formState.appName" placeholder="请输入应用名称" />
        </a-form-item>
        <a-form-item label="是否启用">
          <a-radio-group v-model:value="formState.isUse">
            <a-radio value="1">是</a-radio>
            <a-radio value="0">否</a-radio>
          </a-radio-group>
        </a-form-item>
      </template>
    </Table>
    <table-modal
      :open="modalOpen"
      :disabled="disabled"
      @modalCancel="modalCancel"
      @modalHandleOk="modalHandleOk"
      @confirmLoadingHandle="confirmLoadingHandle"
      :confirm-loading="confirmLoading"
      ref="modalRef"
    ></table-modal>
  </a-card>
</template>
<script setup lang="ts">
import { Table } from '@fs/fs-components'
import Modal from 'ant-design-vue/es/modal/Modal'
import { ref, onBeforeMount, h, createVNode } from 'vue'
import { Button, message } from 'ant-design-vue/es/components'
import { ExclamationCircleOutlined } from '@ant-design/icons-vue'
import type { Column } from '@fs/fs-components/src/components/table/type'
import { deleteSsoApp, getSsoAppList } from '@/api/services/permission'
import { OrderDTO } from '@/utils/column'

let columns = ref<Column[]>([])
const tableRef = ref<InstanceType<typeof Table> | null>(null)
const modalRef = ref<any | null>(null)
const modalOpen = ref<boolean>(false)
const disabled = ref<boolean>(false)
const formState: Record<string, any> = ref({
  appName: '',
  isUse: '',
})
const confirmLoading = ref<boolean>(false)
function modalHandleOk() {
  tableRef.value?.getTableData()
  modalCancel()
  confirmLoading.value = false
}

function confirmLoadingHandle(data: boolean) {
  confirmLoading.value = data
}

function modalCancel() {
  modalOpen.value = false
}

onBeforeMount(() => {
  initColumns()
})

function addTable() {
  modalOpen.value = true
  disabled.value = false
  modalRef.value?.show('add')
}

async function initColumns() {
  columns.value = OrderDTO.map((item) => {
    return {
      ...item,
      dataIndex: item.key,
    }
  })
  columns.value.push({
    key: 'action',
    title: '操作',
    customRender(data: Record<string, any>) {
      return [
        h(
          Button,
          {
            type: 'link',
            onClick() {
              modalOpen.value = true
              disabled.value = true
              modalRef.value?.show('edit', data.record)
            },
          },
          {
            default: () => '编辑',
          },
        ),
        h(
          Button,
          {
            type: 'link',
            onClick() {
              Modal.confirm({
                title: '提示',
                icon: createVNode(ExclamationCircleOutlined),
                content: createVNode('div', { style: 'color:red;' }, '确认执行删除操作？'),
                async onOk() {
                  try {
                    return await new Promise((resolve, reject) => {
                      deleteSsoApp({ appId: data?.record?.appId })
                        .then((res: any) => {
                          if (res.code === '000000') {
                            message.success('删除成功')
                            resolve(true)
                            tableRef.value?.resetForm()
                          }
                        })
                        .catch(() => {
                          reject()
                        })
                    })
                  } catch {
                    return console.log('Oops errors!')
                  }
                },
                class: 'test',
              })
            },
          },
          {
            default: () => '删除',
          },
        ),
      ]
    },
  })
}
</script>
