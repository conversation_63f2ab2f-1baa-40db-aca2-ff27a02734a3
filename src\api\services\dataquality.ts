import axios from './axios-instances'
import { SODATAFLINK } from '@/api/base'

// 1.查询数据质量列表
export function reqGetDataQualityList(params: {
  flowInstanceId?: number | string | undefined
  pageIndex?: number
  pageSize?: number
  name?: string
}): Promise<{
  records: any[]
  total: number
}> {
  return axios({
    method: 'get',
    url: `${SODATAFLINK}/dataQuailty/getQuailtyDataList`,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 2.查询质量组件执行数据(详情)
export function reqGetQualityDetail(params: ReqQualityDetail): Promise<any> {
  return axios({
    method: 'get',
    url: `${SODATAFLINK}/dataQuailty/getQuailtyExecuteData`,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 3.查询数据质量:柱状图(详情)
export function reqGetBarChart(params: ReqQualityDetail): Promise<any> {
  return axios({
    method: 'get',
    url: `${SODATAFLINK}/dataQuailty/getDataQualityBarChart`,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 查询质量报告详情
export function getQuailtyDataDtl(params: any): Promise<any> {
  return axios({
    method: 'get',
    url: `${SODATAFLINK}/dataQuailty/getQuailtyDataDtl`,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 查询数据质量日志信息
export function getQuailtyDataLog(params: any): Promise<any> {
  return axios({
    method: 'get',
    url: `${SODATAFLINK}/dataQuailty/getQuailtyDataLog`,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

export interface ReqQualityDetail {
  compId: string
  startTime: string
  endTime: string
}
