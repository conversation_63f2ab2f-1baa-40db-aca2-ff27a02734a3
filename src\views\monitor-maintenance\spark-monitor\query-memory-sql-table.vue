<template>
  <div class="list-content">
    <!-- <a-card class="table-card" :bordered="false"> -->
    <a-table
      ref="table"
      :rowKey="(record: Data, index: number) => index"
      :indentSize="40"
      :pagination="false"
      :data-source="rddList"
      :columns="rddColumns"
    >
      <!-- <template slot="statue" slot-scope="text, record">
        <img :src="statusImg(text)" alt="" style="width: 24px">
        <span v-if="record.statue === '-1'">失败</span>
        <span v-else-if="record.statue === '1'">正在执行</span>
        <span v-else-if="record.statue === '2'">完成</span>
      </template> -->
      <template #bodyCell="{ column, record }: { column: any; record: Data }">
        <template v-if="column.key === 'statue'">
          <p class="task-status">
            <img :src="statusImg(record.statue)" alt="" />{{ taskStatus(record.statue) }}
          </p>
        </template>
        <template v-if="column.key === 'sql'">
          <a @click="$logModal(record.sql)"> {{ getDisplayText(record.sql) }}</a>
        </template>
      </template>
    </a-table>
    <!-- 分页卡片-->
    <div class="card-pagination">
      <a-pagination
        size="small"
        @change="handelPageChange"
        v-model="pagination.pageIndex"
        :total="pagination.total"
        @showSizeChange="handelShowSizeChange"
        :defaultPageSize="pagination.pageSize"
        :pageSizeOptions="pagination.pageSizeOptions"
      />
    </div>
    <!-- </a-card> -->
  </div>
</template>
<script lang="ts" setup>
import { defineExpose, ref, reactive, onMounted } from 'vue'
import { getDisplayText } from '@/utils/filters'
import { queryMemorySqlStatus } from '@/api/services/monitor-maintenance/spark-monitor'
import { type Action } from './spark-monitor'

type Data = {
  date: string
  memorySize: string
  statue: string
  sql: string
}

const pagination = reactive({
  pageIndex: 1,
  pageSize: 10,
  total: 0,
  pageSizeOptions: ['10', '20', '30', '40'],
})

const rddList = ref<Data[]>([])

const rddColumns = [
  {
    title: '开始时间',
    dataIndex: 'date',
    key: 'date',
  },
  {
    title: '预估内存',
    dataIndex: 'memorySize',
    key: 'memorySize',
  },
  {
    title: '状态',
    dataIndex: 'statue',
    key: 'statue',
    scopedSlots: { customRender: 'statue' },
  },
  {
    show: true,
    ellipsis: true,
    title: 'SQL',
    dataIndex: 'sql',
    key: 'sql',
    // width: 180,
    scopedSlots: { customRender: 'sql' },
  },
]

async function getList() {
  const params = {
    pageIndex: pagination.pageIndex,
    pageSize: pagination.pageSize,
    code: 11,
  }

  const resp = await queryMemorySqlStatus({ ...params })
  if (resp.data.obj) {
    rddList.value = resp.data.obj.list
    pagination.total = resp.data.obj.total
  }
}
defineExpose({ getList } satisfies Action)

function taskStatus(val: string | number) {
  let result = ''
  switch (Number(val)) {
    case -1:
      result = '失败'
      break
    case 1:
      result = '执行中'
      break
    case 2:
      result = '成功'
      break
    case 3:
      result = '待执行'
      break
    case 4:
      result = '跳过当前及后续'
      break
    case 5:
      result = '跳过当前'
      break
  }
  return result
}

function statusImg(val: string | number) {
  let result = ''
  switch (Number(val)) {
    case -1:
      result = require('@/assets/failure.svg')
      break
    case 1:
      result = require('@/assets/running.svg')
      break
    case 2:
      result = require('@/assets/success.svg')
      break
    case 3:
      result = require('@/assets/waiting.svg')
      break
    case 4:
      result = require('@/assets/jumPover-all.svg')
      break
    case 5:
      result = require('@/assets/jumpOver.svg')
      break
  }
  return result
}

// 切换分页
function handelPageChange(pageIndex: number, pageSize: number) {
  pagination.pageIndex = pageIndex
  pagination.pageSize = pageSize
  getList()
}

// 切换分页大小
function handelShowSizeChange(pageIndex: number, pageSize: number) {
  pagination.pageIndex = pageIndex
  pagination.pageSize = pageSize
  getList()
}

onMounted(getList)
</script>
<style lang="less" scoped>
.task-status {
  display: flex;
  align-items: center;
  margin-bottom: 0;

  img {
    width: 24px;
    height: 24px;
    margin-right: 6px;
  }
}
</style>
