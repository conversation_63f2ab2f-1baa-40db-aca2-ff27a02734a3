/**
 * 数据库可视化设计器的类型定义
 * 基于 DBER 数据格式规范
 */

// 字段类型枚举
export enum FieldType {
  INTEGER = 'INTEGER',
  SMALLINT = 'SMALLINT',
  BIGINT = 'BIGINT',
  NUMERIC = 'NUMERIC',
  FLOAT = 'FLOAT',
  DOUBLE = 'DOUBLE',
  BOOLEAN = 'BOOLEAN',
  CHARACTER = 'CHARACTER',
  VARCHAR = 'VARCHAR',
  TEXT = 'TEXT',
  DATE = 'DATE',
  TIME = 'TIME',
  TIMESTAMP = 'TIMESTAMP',
  JSON = 'JSON',
  BLOB = 'BLOB',
  DATETIME = 'DATETIME',
  DECIMAL = 'DECIMAL',
}

// 关系类型枚举
export enum RelationType {
  ONE = '1', // 一对一/一对多的一方
  MANY = '*', // 多方
}

// 字段约束标识
export type FieldConstraint = 'on' | undefined

// 数据库字段定义
export interface DatabaseField {
  id: string // 字段唯一标识
  name: string // 字段名
  type: FieldType | string // 字段类型
  note: string // 字段备注
  dbdefault: string // 默认值
  pk?: FieldConstraint // 是否为主键
  increment?: FieldConstraint // 是否自增
  unique?: FieldConstraint // 是否唯一
  notNull?: FieldConstraint // 是否非空
  length?: number // 字段长度（如 VARCHAR(255)）
  precision?: number // 精度（如 DECIMAL(10,2)）
  scale?: number // 小数位数
}

// 数据库表定义
export interface DatabaseTable {
  id: string // 表唯一标识
  name: string // 表名
  x: number // 表在设计图中的 x 坐标
  y: number // 表在设计图中的 y 坐标
  fields: DatabaseField[] // 表的字段列表
  comment?: string // 表备注
  engine?: string // 存储引擎
  charset?: string // 字符集
}

// 关系端点定义
export interface RelationEndpoint {
  id: string // 表的ID
  fieldId: string // 字段的ID
  relation: RelationType // 关系类型
}

// 表关系定义
export interface DatabaseRelation {
  id: string // 关系唯一标识
  name: string | null // 关系名称
  endpoints: RelationEndpoint[] // 关系的两个端点
  onUpdate?: string // 更新时的操作（CASCADE, SET NULL, RESTRICT等）
  onDelete?: string // 删除时的操作
}

// 视图框定义
export interface ViewBox {
  x: number // 视图框的 x 坐标
  y: number // 视图框的 y 坐标
  w: number // 视图框的宽度
  h: number // 视图框的高度
  clientW: number // 客户端宽度
  clientH: number // 客户端高度
}

// 数据库设计图完整定义
export interface DatabaseDesign {
  id: string // 设计图唯一标识
  name: string // 设计图名称
  tableDict: Record<string, DatabaseTable> // 表字典，key为表ID
  linkDict: Record<string, DatabaseRelation> // 关系字典，key为关系ID
  box: ViewBox // 视图框信息
  createdAt?: number // 创建时间戳
  updatedAt?: number // 更新时间戳
  version?: string // 版本号
  description?: string // 描述
  database?: string // 目标数据库类型（mysql, postgresql等）
}

// 数据库设计图的简化版本（用于列表显示）
export interface DatabaseDesignSummary {
  id: string
  name: string
  description?: string
  createdAt: number
  updatedAt: number
  tableCount?: number // 表的数量
  relationCount?: number // 关系的数量
}

// 创建数据库设计图的参数
export interface CreateDatabaseDesignParams {
  name: string
  description?: string
  database?: string
}

// 更新数据库设计图的参数
export interface UpdateDatabaseDesignParams {
  id: string
  name?: string
  description?: string
  tableDict?: Record<string, DatabaseTable>
  linkDict?: Record<string, DatabaseRelation>
  box?: ViewBox
}

// 导出选项
export interface ExportOptions {
  format: 'sql' | 'json' | 'xml' | 'markdown' // 导出格式
  database?: 'mysql' | 'postgresql' | 'sqlite' // 目标数据库
  includeData?: boolean // 是否包含示例数据
  includeIndexes?: boolean // 是否包含索引
  includeForeignKeys?: boolean // 是否包含外键约束
}

// 导入选项
export interface ImportOptions {
  source: 'sql' | 'json' | 'database' // 导入来源
  database?: string // 源数据库类型
  preserveIds?: boolean // 是否保留原有ID
}

// API 响应相关类型
export interface ApiResponse<T = any> {
  code: number
  data: T
  message: string
}

export interface PageResult<T> {
  list: T[]
  total: number
  page: number
  pageSize: number
}

// 常用的工具类型
export type DatabaseDesignList = PageResult<DatabaseDesignSummary>
export type TableList = DatabaseTable[]
export type FieldList = DatabaseField[]
export type RelationList = DatabaseRelation[]
