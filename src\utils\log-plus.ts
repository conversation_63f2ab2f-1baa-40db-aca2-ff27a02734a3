export const logPlus = {
  // 红色日志
  red(label: string, ...args: any[]): void {
    logPlus.log({
      color: '#ff0000',
      label,
      args,
    })
  },

  // 橙色日志
  orange(label: string, ...args: any[]): void {
    logPlus.log({
      color: '#ff8c00',
      label,
      args,
    })
  },

  // 黄色日志
  yellow(label: string, ...args: any[]): void {
    logPlus.log({
      color: '#ffff00',
      label,
      args,
    })
  },

  // 绿色日志
  green(label: string, ...args: any[]): void {
    logPlus.log({
      color: '#00ff00',
      label,
      args,
    })
  },

  // 青色日志
  cyan(label: string, ...args: any[]): void {
    logPlus.log({
      color: '#00ffff',
      label,
      args,
    })
  },

  // 蓝色日志
  blue(label: string, ...args: any[]): void {
    logPlus.log({
      color: '#0000ff',
      label,
      args,
    })
  },

  // 紫色日志
  purple(label: string, ...args: any[]): void {
    logPlus.log({
      color: '#800080',
      label,
      args,
    })
  },

  // 默认日志（保持原有 console.log 样式）
  log({ color, label, args }: { color: string; label: string; args: any[] }): void {
    console.log(`%c ${label}`, `color: ${color}; font-weight: bold;`, ...args)
  },
}
