<template>
  <div class="meta-data-management" :style="{ '--left-width': leftWidth + '%' }">
    <a-row justify="start" align="top" type="flex" :wrap="true" class="management-row">
      <a-col class="management-col" :style="{ width: leftWidth + '%' }">
        <directory-tree ref="directoryTreeRef" @add-asset="handleDatabaseSelect" />
      </a-col>
      <div class="resize-handle" @mousedown="startResize">
        <div class="panel-grabber-vertical">
          <div class="handle-icon handle-icon-vertical"></div>
        </div>
      </div>
      <a-col
        class="management-col content right-border"
        :style="{ width: 'calc(100% - ' + leftWidth + '% - 4px)', paddingLeft: '14px' }"
      >
        <CardBox>
          <template #title>
            <div style="display: flex; align-items: center" class="content-rl content-top">
              <type-icon
                v-if="databaseData?.dbTypMap !== undefined"
                :width="48"
                :height="36"
                :type="databaseData?.dbTypMap"
              />
              <span>{{ databaseData?.title ?? '' }}</span>
            </div>
          </template>
          <template #subTitle>
            <div class="content-rl">{{ databaseData?.dataSourceName ?? '' }}</div>
          </template>
          <a-tabs v-model:activeKey="activeKey" v-if="databaseData" class="tabs-wrapper">
            <a-tab-pane key="table" tab="数据库列表" :forceRender="true">
              <database-table
                ref="databaseTableRef"
                :database-data="databaseData"
                :table-list="tablesRef"
              />
            </a-tab-pane>
            <a-tab-pane key="meta" tab="元数据提取">
              <MetaDataExtract :database-data="databaseData" :table-list="tablesRef" />
            </a-tab-pane>
          </a-tabs>
          <a-empty v-else style="margin-top: 100px">
            <template #description>暂无数据，请在左侧选择数据库 </template>
          </a-empty>
        </CardBox>
      </a-col>
    </a-row>
  </div>
</template>
<script lang="ts" setup>
import { ref, provide } from 'vue'
import CardBox from '@/components/card-box/card-box.vue'
import TypeIcon from '@/views/data-asset/metadata-query/components/type-icon.vue'
import DirectoryTree from './components/directory-tree.vue'
import DatabaseTable from './components/database-table.vue'
import MetaDataExtract from './components/meta-data-extract.vue'
import { queryTabledescFun } from '@/api/matedata/metadatamanagement/metadatamanagement'

const activeKey = ref('table')
const directoryTreeRef = ref()

type DatabaseDataType = Parameters<typeof queryTabledescFun>[0]
const databaseData = ref<any>()

// 拖拽相关的状态
const leftWidth = ref(25) // 初始宽度设为20%
const isResizing = ref(false)
const startX = ref(0)
const startLeftWidth = ref(0)
const dbSchema = ref('')
provide('dbSchema', dbSchema)

// 开始拖拽
const startResize = (e: MouseEvent) => {
  isResizing.value = true
  startX.value = e.clientX
  startLeftWidth.value = leftWidth.value
  document.addEventListener('mousemove', handleMouseMove)
  document.addEventListener('mouseup', stopResize)
}

// 处理拖拽移动
const handleMouseMove = (e: MouseEvent) => {
  if (!isResizing.value) return

  const containerWidth = document.querySelector('.management-row')?.clientWidth || 0
  const deltaX = e.clientX - startX.value
  const deltaPercent = (deltaX / containerWidth) * 100

  const newWidth = Math.max(15, Math.min(60, startLeftWidth.value + deltaPercent))
  leftWidth.value = newWidth
}

// 停止拖拽
const stopResize = () => {
  isResizing.value = false
  document.removeEventListener('mousemove', handleMouseMove)
  document.removeEventListener('mouseup', stopResize)
}

const handleDatabaseSelect = (data: any, flag: boolean) => {
  if (!data) {
    tablesRef.value = []
    return
  }
  console.log('%c [  ]-101', 'font-size:13px; background:#67cc8f; color:#abffd3;', data)
  databaseData.value = data
  dbSchema.value = data.parent?.key ? data.title : ''
  fetchTableData({
    id: data.datasourceId,
    dbName: data.parent?.key ?? data.key,
    dbSchema: data.parent?.key ? data.title : '',
    type: 'database',
  })
}

type TableData = Awaited<ReturnType<typeof queryTabledescFun>>['data']
const tablesRef = ref<TableData['obj']>([])
const fetchTableData = async (params: DatabaseDataType) => {
  try {
    await queryTabledescFun(params).then((res) => {
      console.log('🚀 ~ fetchTableData ~ res:1', res)
      if (res?.data) {
        tablesRef.value = res.data.obj
      }
    })
  } catch (error) {
    console.error('获取数据表列表失败:', error)
  }
}

onMounted(async () => {})
</script>
<style lang="less" scoped>
.meta-data-management {
  height: 100%;
  user-select: none;
  .management-row {
    height: 100%;
    position: relative;
    .management-col {
      height: 100%;
      padding: 12px 0 0 12px;
      background: white;
      flex-shrink: 0;
      overflow-y: auto;
      overflow-x: hidden;
      word-break: break-all;
      word-wrap: break-word;
      white-space: normal;
    }
    .content {
      padding: 0px;
      flex: 1;
    }
    .right-border {
      border-right: 1px solid #d7d7d7;
    }
  }
  .header-div {
    display: flex;
  }

  .resize-handle {
    width: 14px;
    height: 100%;
    background-color: transparent;
    cursor: col-resize;
    position: absolute;
    left: var(--left-width);
    z-index: 1;
    flex-shrink: 0;
    background-color: RGB(252, 252, 252) !important;
    border-right: 1px solid #d7d7d7;
    user-select: none;

    .panel-grabber-vertical {
      width: 14px;
      display: flex;
      flex-direction: row;
      height: 100%;
      .handle-icon-vertical {
        margin-left: 0px;
        margin: auto;
        height: 30px;
        width: 3px;
        background-color: #e8e8ed;
        border-radius: 6px;
      }
    }
  }
}

.content-rl {
  padding: 0 12px;
}
.content-top {
  padding-top: 12px;
}

.tabs-wrapper {
  padding: 0 16px;
  height: 100%;
  :deep(.ant-tabs-content) {
    height: 100%;
  }
}
</style>
