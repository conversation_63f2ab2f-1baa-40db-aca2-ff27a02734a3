<template>
  <div>
    <a-popover title="" trigger="click" v-model:open="showMenu" :overlayStyle="{ padding: 0 }">
      <a-button type="link" @click="showMenu = !showMenu">
        {{ activeLabel }} <DownOutlined />
      </a-button>
      <template #content>
        <a-menu @click="handleClick" style="border: none">
          <a-menu-item v-for="item in conditionOptions" :key="item.value">{{
            item.label
          }}</a-menu-item>
        </a-menu>
      </template>
    </a-popover>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'
import { DownOutlined } from '@ant-design/icons-vue'
import { stringConditionOptions, numberConditionOptions, ConditionEnum } from './constant'

const props = defineProps<{
  value?: ConditionEnum
  isNumber?: boolean
  callback?: (value: ConditionEnum) => void
}>()
const showMenu = ref(false)
const activeLabel = ref<string>()
const conditionOptions = computed(() => {
  return props.isNumber ? numberConditionOptions : stringConditionOptions
})
onMounted(() => {
  const defaultLabel = stringConditionOptions[0].label
  if (props.value) {
    if (!props.isNumber)
      activeLabel.value =
        stringConditionOptions.find((item) => item.value === props.value)?.label ?? defaultLabel
    else
      activeLabel.value =
        numberConditionOptions.find((item) => item.value === props.value)?.label ?? defaultLabel
  } else {
    activeLabel.value = defaultLabel
  }
})

const handleClick = (e: any) => {
  // console.log('condition', e)
  showMenu.value = false
  const tar = conditionOptions.value.find((item) => item.value === e.key)!
  activeLabel.value = tar.label
  props.callback && props.callback(tar.value)
}
</script>

<style scoped lang="less"></style>
