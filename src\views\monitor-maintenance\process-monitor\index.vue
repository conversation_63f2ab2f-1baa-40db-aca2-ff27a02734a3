<template>
  <div class="page-full process-monitor-page">
    <div class="content-div">
      <cardBox :title="'进程监控'" :subTitle="'进程监控信息'">
        <template #headerRight>
          <a-button type="primary" @click="fetchTableData"> 刷新 </a-button>
        </template>
        <div class="content">
          <Table
            :columns="columns"
            :getData="transgetProcessMonitorApi"
            ref="tableRef"
            :autoRequest="false"
            :searchFormState="searchFormData"
            :pagination="false"
            :table-config="{
              scroll: { y: '500px' },
            }"
          >
            <template #bodyCell="{ column, record, text }">
              <template v-if="column.dataIndex === 'status'">
                <a-tag v-if="text === -1">未启动</a-tag>
                <a-tag color="#52c41a" v-if="text === 1">健康</a-tag>
                <a-tag color="#f50" v-if="text === 2">异常</a-tag>
              </template>
              <template v-if="column.dataIndex === 'tips'">
                <a class="table-cell" @click="logModal(text, '查看详情')" v-if="text">{{ text }}</a>
              </template>
              <template v-if="column.dataIndex === 'env'">
                <a v-if="text" @click="checkEnvModal(record)">查看详情</a>
              </template>
              <template v-if="column.dataIndex === 'action'">
                <a-space>
                  <a-button
                    size="small"
                    type="link"
                    :disabled="record.status !== -1"
                    @click="setProcessMonitorFun('start', record.serverId)"
                    >启动</a-button
                  >
                  <a-button
                    size="small"
                    type="link"
                    :disabled="record.status !== 1"
                    @click="setProcessMonitorFun('stop', record.serverId)"
                    >停止</a-button
                  >
                  <a-button
                    size="small"
                    type="link"
                    @click="setProcessMonitorFun('restart', record.serverId)"
                    >重启</a-button
                  >
                  <a-dropdown>
                    <a class="ant-dropdown-link" @click="(e) => e.preventDefault()"
                      >更多<a-icon type="down" />
                    </a>
                    <template v-slot:overlay>
                      <a-menu>
                        <a-menu-item @click="getLog('info', record.serverId)">日志</a-menu-item>
                        <a-menu-item @click="downLog('info', record.serverId)"
                          >下载日志</a-menu-item
                        >
                        <a-menu-item @click="getLog('error', record.serverId)"
                          >获取错误日志</a-menu-item
                        >
                      </a-menu>
                    </template>
                  </a-dropdown>
                </a-space>
              </template>
            </template>
          </Table>
        </div>
      </cardBox>
    </div>
    <EnvModal :open="envOpen" :envForm="envForm" @modalCancelHandleEnv="cancelHandleEnv" />
  </div>
</template>

<script lang="ts" setup>
import {
  getDownLogParam,
  getProcessMonitor,
  setProcessMonitor,
  tailLogFun,
  type ProcessMonitorDTO,
} from '@/api/services/monitor-maintenance/process-monitor'
import { ref } from 'vue'
import { logModal } from '@/utils/log-modal'
import cardBox from '@/components/card-box/card-box.vue'
import { Table } from '@fs/fs-components'
import { message } from 'ant-design-vue'
import { useRoute } from 'vue-router'
import EnvModal from '@/views/monitor-maintenance/process-monitor/env-modal.vue'

const route = useRoute()
const timer = ref<any>(null)
const envOpen = ref<boolean>(false)
const envForm = ref<any>({})
const tableRef = ref<InstanceType<typeof Table> | null>(null)
const columns = ref<any[]>([
  {
    key: 'type',
    align: 'center',
    title: '节点类型',
    dataIndex: 'type',
    scopedSlots: { customRender: 'type' },
  },
  {
    align: 'center',
    title: '最后请求时间',
    key: 'lastTime',
    dataIndex: 'lastTime',
    scopedSlots: { customRender: 'lastTime' },
  },
  {
    align: 'center',
    title: '平均间隔',
    key: 'avgTime',
    dataIndex: 'avgTime',
    scopedSlots: { customRender: 'avgTime' },
  },
  {
    align: 'center',
    title: '状态',
    key: 'status',
    dataIndex: 'status',
    scopedSlots: { customRender: 'status' },
  },
  {
    align: 'center',
    title: '提示信息',
    key: 'tips',
    dataIndex: 'tips',
    scopedSlots: { customRender: 'tips' },
  },
  {
    align: 'center',
    title: '环境信息',
    key: 'env',
    dataIndex: 'env',
    scopedSlots: { customRender: 'env' },
  },
  {
    align: 'center',
    title: '服务地址',
    key: 'serviceIp',
    dataIndex: 'serviceIp',
    scopedSlots: { customRender: 'serviceIp' },
  },
  {
    align: 'center',
    title: '服务端口',
    key: 'servicePort',
    dataIndex: 'servicePort',
    scopedSlots: { customRender: 'servicePort' },
  },
  {
    align: 'center',
    title: '操作',
    key: 'action',
    width: 250,
    dataIndex: 'action',
    scopedSlots: { customRender: 'action' },
  },
])
const searchFormData = ref<any>({ type: 'infos' })

const setProcessMonitorFun = (type: string, serverId: string) => {
  setProcessMonitor({ type, params: { serverId } }).then((res) => {
    message.success('操作成功')
    fetchTableData()
  })
}

const transgetProcessMonitorApi = (params: any) =>
  getProcessMonitor(params).then((d: any) => {
    const tableData = (d.data.infos as ProcessMonitorDTO[]).map((item) => {
      return {
        type: item.info.type,
        serverId: item.serverId,
        lastTime: item.info.lastTime,
        avgTime: item.info.avgTime,
        status: item.info.status,
        tips: item.info.tips,
        env: item.info.env,
        serviceIp: item.info.serviceInfo.serviceIp,
        servicePort: item.info.serviceInfo.servicePort,
        id: item.info.serviceInfo.id,
      }
    })
    // console.log(tableData, 'tableData')
    // 接口应该返回records 前端做处理
    d.data = tableData
    return d
  })

watch(
  () => route,
  (newRoute: any, oldRoute: any) => {
    if (newRoute.name === 'processMonitor') {
      loop()
    }
    if (oldRoute.name === 'processMonitor') {
      clearLoop()
    }
  },
  { immediate: true },
)

const loop = () => {
  fetchTableData()
  timer.value = setInterval(() => {
    fetchTableData()
  }, 10000)
}

const clearLoop = () => {
  clearInterval(timer.value)
}

const checkEnvModal = (record: any) => {
  envOpen.value = true
  envForm.value = record.env
}

const fetchTableData = async () => {
  try {
    setTimeout(() => {
      tableRef.value?.getTableData()
    }, 20)
  } catch (error) {
    console.error('获取数据表列表失败:', error)
  }
}

onMounted(() => {
  loop()
})

onBeforeUnmount(() => {
  clearLoop()
})

const getLog = (type: string, serverId: any) => {
  tailLogFun({
    json: JSON.stringify({
      type,
      serverId,
      lines: 300,
    }),
  }).then((res) => {
    if (res.data.code === 1) {
      logModal(res.data.obj, '日志', true, 80)
    }
  })
}

const downLog = (type: string, serverId: any) => {
  getDownLogParam({
    json: JSON.stringify({
      type,
      serverId,
      lines: 300,
    }),
  }).then((res) => {
    window.open(
      `http://${window.location.host}/download/resultFile?fileName=${res.data.obj.fileName}&type=data&filePath=${res.data.obj.filePath}`,
    )
  })
}

const cancelHandleEnv = () => {
  envOpen.value = false
}
</script>
<style lang="less" scoped>
.process-monitor-page {
  height: 100%;
  .content-div {
    height: calc(100% - 48px);
  }
  .content :deep(.filter-box) {
    display: none;
  }
}
</style>
