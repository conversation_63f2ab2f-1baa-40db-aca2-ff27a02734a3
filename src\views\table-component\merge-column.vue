<template>
  <Space class="merge-column" direction="vertical" size="large">
    <div text="16px b700" class="">将"{{ curColumn.field }}"与其它列合并</div>

    <Flex gap="10" v-for="(column, index) in selectedColumns" :key="column.field">
      <Space direction="vertical" v-show="isCustomSplit">
        <div text="3.5 b700">分离器</div>
        <Input v-model:value="delimiter[index]" class="w-50px" />
      </Space>
      <Space direction="vertical" size="small" class="wfull">
        <div text="3.5 b700">第 {{ index + 2 }} 栏</div>
        <Flex>
          <Select
            class="w-full"
            :options="columnOptions"
            v-model:value="selectedColumns[index].field"
          ></Select>
          <Button v-show="selectedColumns.length > 1" type="text" @click="onRemove(index)">
            <CloseOutlined />
          </Button>
        </Flex>
      </Space>
    </Flex>

    <Flex justify="space-between">
      <Button text="primary b700 3.6" type="text" @click="onClickSplit">
        {{ isCustomSplit ? '取消空间分隔' : '用空间分隔' }}
      </Button>
      <Button text="primary b700 3.6" type="text" @click="onAddColumn">添加栏目</Button>
    </Flex>

    <!-- 示例 -->
    <Space direction="vertical" size="small" class="wfull">
      <div class="" text="3.5 b700">示例</div>
      <div class="wfull bg-#f9fbfc p-2 rd-8px" b="1px solid #eeecec">{{ sampleText }}</div>
    </Space>

    <!-- 底部按钮 -->
    <div class="text-right">
      <Button type="primary" @click="handleConfirm">确定</Button>
    </div>
  </Space>
</template>

<script setup lang="ts">
import { Space, Select, Flex, Button, Input } from 'ant-design-vue'
import { computed, ref } from 'vue'
import { CloseOutlined } from '@ant-design/icons-vue'
import type { ColumnType } from '@/api/services/indicator/type'
import { columnTypeTextMap } from './constant'
import type { MergedColumnItem } from './type'

const props = defineProps<{
  curColumn: MergedColumnItem
  columns: MergedColumnItem[]
  onConfirm: (columns: MergedColumnItem[], delimiter?: string[]) => void
}>()
const selectedColumns = ref<MergedColumnItem[]>([props.columns[0]])
const columnOptions = ref<{ label: string; value: string }[]>([])
const isCustomSplit = ref(false)
/** 分隔器 */
const delimiter = ref<string[]>([''])
const sampleText = computed(() => {
  if (!isCustomSplit.value) {
    return (
      columnTypeTextMap[props.curColumn.kind] +
      ' ' +
      selectedColumns.value
        .map((column) => {
          return columnTypeTextMap[column.kind]
        })
        .join(' ')
    )
  } else {
    let len = selectedColumns.value.length
    let str = columnTypeTextMap[props.curColumn.kind]
    for (let i = 0; i < len; i++) {
      str += delimiter.value[i]
      str += columnTypeTextMap[selectedColumns.value[i].kind]
    }
    return str
  }
})
const onClickSplit = () => {
  isCustomSplit.value = !isCustomSplit.value
}
const onAddColumn = () => {
  if (selectedColumns.value.length >= props.columns.length) return
  selectedColumns.value.push(props.columns[selectedColumns.value.length])
  delimiter.value.push('')
}
const onRemove = (index: number) => {
  selectedColumns.value.splice(index, 1)
  delimiter.value.splice(index, 1)
}
const handleConfirm = () => {
  const arr = [props.curColumn, ...selectedColumns.value]
  if (!isCustomSplit.value) props.onConfirm(arr)
  else props.onConfirm(arr, delimiter.value)
}
onMounted(() => {
  console.log('合并栏目', props.columns)
  columnOptions.value = props.columns.map((column) => {
    return { label: column.field, value: column.field }
  })
})
</script>

<style scoped lang="less">
.merge-column {
  width: 460px;
  background-color: white;
  padding: 12px 16px;
  color: rgb(76, 87, 115);
  border-radius: 8px;
  box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.05);
}
</style>
