<template>
  <div class="data-preview">
    <a-row justify="start" align="top" type="flex" :wrap="true" class="data-preview-row">
      <a-col :span="24" class="data-preview-col">
        <div class="content">
          <!-- 搜索框 -->
          <div class="search-container">
            <a-input
              v-model:value="searchText"
              placeholder="请输入字段名搜索"
              style="width: 400px; margin-bottom: 12px"
              allow-clear
            />
          </div>
          <!-- 列的内容 -->
          <a-table
            :dataSource="filteredData"
            :columns="columns"
            :pagination="false"
            :scroll="{ y: 'calc(100vh - 350px)' }"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'columnName'">
                <div style="display: flex; align-items: center">
                  <a-tooltip placement="bottom" v-if="record.isPrimaryKey === 'YES'">
                    <template #title>
                      <span>主键</span>
                    </template>
                    <img
                      :src="Pri"
                      alt=""
                      style="width: 18px; height: 18px; margin-right: 5px; margin-top: 2px"
                    />
                  </a-tooltip>
                  <a-tooltip placement="bottom" v-else-if="record.isNullable === 'NO'">
                    <template #title>
                      <span>非空</span>
                    </template>
                    <img
                      :src="noNull"
                      alt=""
                      style="width: 18px; height: 18px; margin-right: 5px; margin-top: 2px"
                    />
                  </a-tooltip>
                  {{ record.columnName }}
                </div>
              </template>
            </template>
          </a-table>
        </div>
      </a-col>
    </a-row>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue'
import { useRoute } from 'vue-router'
import { getDataAssetTableDetail } from '@/api/assetmanager/dataassertmanage/dataassertmanage'
import noNull from '@/assets/data-base/nempty.png'
import Pri from '@/assets/data-base/pri.png'

const route = useRoute()
const searchText = ref('')

type ResponseData = Awaited<ReturnType<typeof getDataAssetTableDetail>>['data']
type TableDetail = ResponseData & { columnsList?: ResponseData['columnList'] }

const tableDetailRef = ref<TableDetail>()
const columns = [
  { title: '字段名', dataIndex: 'columnName', key: 'columnName' },
  { title: '字段序号', dataIndex: 'ordinalPosition', key: 'ordinalPosition' },
  { title: '默认值', dataIndex: 'columnDefault', key: 'columnDefault' },
  // { title: '可否为空', dataIndex: 'isNullable', key: 'isNullable' },
  { title: '数据类型', dataIndex: 'columnType', key: 'columnType' },
  { title: '字段注释', dataIndex: 'columnComment', key: 'columnComment' },
  {
    dataIndex: 'customComment',
    title: '自定义注释',
    key: 'customComment',
  },
]
const tableDataSource = computed(() => tableDetailRef.value?.columnsList ?? [])

// 添加过滤后的数据计算属性
const filteredData = computed(() => {
  if (!searchText.value) {
    return tableDataSource.value
  }
  return tableDataSource.value.filter((item) =>
    item.columnName.toLowerCase().includes(searchText.value.toLowerCase()),
  )
})

const fetchTableDetail = async (tableId: string) => {
  try {
    const response = await getDataAssetTableDetail({ tableId }, {})
    tableDetailRef.value = response.data
    console
  } catch (error) {
    console.error('Error fetching table detail:', error)
  }
}

onMounted(() => {
  const tableId = route.query.tableId as string
  fetchTableDetail(tableId)
})
</script>

<style lang="less" scoped>
.data-preview {
  width: 100%;
  height: 100%;
  overflow: auto;
}

.search-container {
  display: flex;
  justify-content: flex-start;
  margin-bottom: 10px;
}
</style>
