<template>
  <div class="page-type">
    <a-button @click="openMadol('table')">表格</a-button>
  </div>
</template>

<script setup lang="ts">
import { useAppStore } from '@/stores/app'

const appStore = useAppStore()

interface pageType {
  moduleName: string
}

const props: pageType = withDefaults(defineProps<pageType>(), {})

const emit = defineEmits(['generation:code'])

function openMadol(type: string) {
  appStore.setGenerationType({
    type,
    state: true,
    apiData: {
      project: 'gpt-api',
      module: props.moduleName,
    },
    fetchCode: true,
    fetchCallback: (data: string) => {
      emit('generation:code', { type: 'page-type', content: data })
    },
  })
}
</script>
<style scoped lang="less">
.page-type {
  margin-top: 6px;
}
</style>
