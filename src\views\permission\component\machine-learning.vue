<template>
  <a-modal
    v-model:visible="visible"
    title="分配机器学习文件"
    @ok="handleOk"
    @cancel="handleCancel"
    :width="800"
    :confirmLoading="confirmLoading"
  >
    <a-tabs v-model:activeKey="activeKey">
      <a-tab-pane :key="tabKeys.machineLearning" tab="机器学习文件">
        <a-table
          :columns="columns"
          :data-source="dataSource"
          :row-selection="{
            selectedRowKeys: selectedKeys,
            onChange: rowSelectionOnChange,
            type: 'checkbox',
          }"
          :pagination="false"
          style="margin-top: 10px"
          rowKey="autoId"
          v-if="visible2"
        >
        </a-table>
      </a-tab-pane>
      <a-tab-pane :key="tabKeys.kernel" tab="运行内核">
        <kernel-table v-model:table-data="kernelTableData" />
      </a-tab-pane>
    </a-tabs>
  </a-modal>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import type { TableColumnType } from 'ant-design-vue'
import { getJupyterProjectRole, updateJupyterByRole } from '@/views/rule-engine/service'
import { message } from 'ant-design-vue'
import KernelTable from './kernel-table.vue'
import { getKernelList, updateKernelList, type KernelInfo } from '@/api/services/kernel-allocation'

interface DataItem {
  key: string
  name: string
  description: string
}
const tabKeys = {
  machineLearning: '1',
  kernel: '2',
} as const

const visible = ref(false)
const visible2 = ref(false)
const selectedRows = ref<DataItem[]>([])
const confirmLoading = ref(false)
const roleData = ref<any>({})
const activeKey = ref('1')

// 表格列定义
const columns: TableColumnType[] = [
  {
    title: '名称',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '描述',
    dataIndex: 'description',
    key: 'description',
  },
  {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
  },
]
const kernelTableData = ref<KernelInfo[]>([])
// 运行内核表格数据
const getKernelTableData = async () => {
  const res = await getKernelList({ roleId: roleData.value.roleId })
  kernelTableData.value = res.data
}
watch(
  roleData,
  (newVal) => {
    if (newVal.roleId) {
      getKernelTableData()
    }
  },
  {
    immediate: true,
  },
)

// 模拟数据
const dataSource = ref<DataItem[]>([])
const selectedKeys = ref<any[]>([])

// 复选框配置
const rowSelectionOnChange = (selectedRowKeys: string[], selected: DataItem[]) => {
  selectedRows.value = selected
  console.log('selectedRowKeys:', selectedRowKeys)
  console.log('selected:', selected)
  selectedKeys.value = selectedRowKeys
}

// 打开modal的方法
const showModal = async (record: any) => {
  console.log('🚀 ~ showModal ~ record:', record)
  visible.value = true
  roleData.value = record
  visible2.value = false
  try {
    // getSelectedProject(record.autoId)
    const res = await getJupyterProjectRole({ roleId: record.roleId })
    selectedKeys.value = res.data
      .filter((item: any) => item.allowFlag)
      .map((item: any) => item.autoId)

    visible2.value = true
    dataSource.value = res.data || []
  } catch (error) {
    console.error('获取项目列表时发生错误:', error)
  }
}
const saveMachineLearning = async () => {
  dataSource.value.forEach((item: any) => {
    const val = selectedRows.value.find((row: any) => row.autoId === item.autoId)
    if (val) {
      item.allowFlag = true
    } else {
      item.allowFlag = false
    }
  })

  // 这里可以处理选中的数据
  console.log('确认选择的数据:', selectedRows.value)
  await updateJupyterByRole({ roleId: roleData.value.roleId, roleProjects: dataSource.value })
}
const saveKernel = async () => {
  console.log('运行内核列表:', kernelTableData.value)
  // 调用接口保存
  await updateKernelList({ roleId: roleData.value.roleId, roleKernels: kernelTableData.value })
}
// 确认按钮处理函数
const handleOk = async () => {
  try {
    confirmLoading.value = true
    const [res1, res2] = await Promise.allSettled([saveMachineLearning(), saveKernel()])
    if (res1.status === 'fulfilled' && res2.status === 'fulfilled') {
      message.success('操作成功')
      visible.value = false
    } else {
      if (res1.status === 'rejected') {
        console.error('机器学习文件操作失败:', res1.reason)
        message.error(res1.reason?.message || '机器学习文件操作失败')
      }
      if (res2.status === 'rejected') {
        console.error('运行内核操作失败:', res2.reason)
        message.error(res2.reason?.message || '运行内核操作失败')
      }
    }
  } catch (error) {
    console.error('发生错误:', error)
  } finally {
    confirmLoading.value = false
  }
}

// 取消按钮处理函数
const handleCancel = () => {
  visible.value = false
  selectedRows.value = []
}

// 导出方法供父组件使用
defineExpose({
  showModal,
})
</script>

<style scoped></style>
