<template>
  <div class="where-comp">
    <a-popover
      trigger="click"
      :arrow="false"
      v-model:open="visible"
      @openChange="handleVisibleChange"
    >
      <template #content>
        <div class="popover-content">
          <filter-main
            v-if="!column"
            :table-name="tableName"
            :columns="columns"
            @handleClick="handleClick"
          ></filter-main>
          <data-column-type
            v-else
            :data="operate"
            :column="column"
            @back="handleBack"
            @close="handleClose"
            @submit="handleSubmit"
          ></data-column-type>
        </div>
      </template>
      <slot>
        <div class="where-action">
          <div class="where-action-trigger">添加筛选器来缩小你的答案范围</div>
        </div>
      </slot>
    </a-popover>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import filterMain from './filter-main.vue'
import dataColumnType from '@/components/data-columns/type.vue'
import type { dataColumn, operateTypeValue, operateValue } from '@/components/data-columns/type'

const visible = ref(false)
const column = ref<dataColumn | null>(null)
const operate = ref<operateTypeValue>({
  operator: '',
  value: undefined,
})

const emits = defineEmits(['submit'])
defineProps<{
  tableName: string
  columns: dataColumn[]
}>()
const modelValue = defineModel<operateValue>({
  default: () => ({
    column: null,
    operateValue: undefined,
  }),
})
const selectedTableName = ref('')
function handleClick(value: dataColumn, tableName: string) {
  column.value = value
  selectedTableName.value = tableName
  console.log('where handle click event', value, tableName)
}

function handleBack() {
  column.value = null
}

function handleClose() {
  visible.value = false
}

function handleVisibleChange() {
  if (visible.value) {
    column.value = modelValue.value.column
    operate.value = modelValue.value.operateValue
  }
}

function handleSubmit(e: operateValue) {
  modelValue.value = e
  handleClose()
  emits('submit', e, selectedTableName.value)
}
</script>

<style lang="less" scoped>
.popover-content {
  max-width: 500px;
  width: 300px;
}
.where-comp {
  &-action {
    display: inline-flex;
    font-weight: bold;
    color: rgb(113, 114, 173);
    border-radius: 6px;
    border: 2px solid rgba(113, 114, 173, 0.25);
    cursor: pointer;
    pointer-events: auto;
    -webkit-box-align: stretch;
    align-items: stretch;
    transition: border 300ms linear 0s;
    cursor: default;

    &-trigger {
      display: flex;
      -webkit-box-align: center;
      align-items: center;
      padding: 10px;
      background-color: transparent;
      border-radius: 6px;
      transition: background 300ms linear 0s;
    }
  }
}
</style>
