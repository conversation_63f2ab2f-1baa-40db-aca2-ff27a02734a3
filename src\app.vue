<script setup lang="ts">
import { RouterView } from 'vue-router'
import { useAppStore } from '@/stores/app'
import { useUserStore } from '@/stores/user'
import { theme } from '@/config/antd-config-provider'
import zhCN from 'ant-design-vue/es/locale/zh_CN'
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'
import '@/utils/post-message'
import { useRouter } from 'vue-router'
import { useEventBus } from '@vueuse/core'
const router = useRouter()

dayjs.locale('zh-cn')

const appStore = useAppStore()
const userStore = useUserStore()

const handleMessage = (event: MessageEvent) => {
  // console.log('🚀 ~ handleMessage ~ event:', event)
  // console.log('🚀 ~ handleMessage ~ userStore:', userStore)
  switch (event.data.type) {
    case 'GET_TOKEN': {
      // 将 menuData 转换为 Map，以 menuUrl 为键
      let apiManageMenuData = JSON.parse(JSON.stringify(userStore.userMenu))

      // 深度遍历提取 menuType 为 1 或 2 的数据
      const extractMenuItems = (items: any[]): any[] => {
        const result: any[] = []

        const traverse = (menuItems: any[]) => {
          menuItems.forEach((item: any) => {
            // 如果 menuType 为 1 或 2，添加到结果中
            if ([1, 2, 4].includes(item.menuType)) {
              result.push({
                menuId: item.menuId,
                menuUrl: item.menuUrl,
                menuName: item.menuName,
                menuType: item.menuType,
              })
            }

            // 如果有子菜单，递归遍历
            if (item.children && item.children.length > 0) {
              traverse(item.children)
            }
            if (item.childrens && item.childrens.length > 0) {
              traverse(item.childrens)
            }
          })
        }

        traverse(items)
        return result
      }

      const arr = extractMenuItems(apiManageMenuData)
      const menuData = new Map()
      arr.forEach((item: any) => {
        menuData.set(item.menuUrl, item)
      })
      console.log('menuData', menuData)
      console.log('userInfo', userStore.userInfo)
      const userInfo = {
        ...userStore.userInfo,
      }
      ;(event.source as Window).postMessage(
        {
          type: 'SET_TOKEN',
          token: userStore.token,
          user: userInfo,
          menuData: menuData,
        },
        '*',
      )
      break
    }

    case 'NO_LOGIN': {
      // api-manage 没有拿到 token，跳转到登录页面
      console.log('api-manage 没有拿到 token, 跳转到登录页面')
      const redirectUrl = event.data.data.redirectUrl
      const prefixPath = '/api-manage'

      router.push({
        path: '/login',
        query: {
          redirectUrl: `${prefixPath}?api-manage-path=${redirectUrl}`,
        },
      })
      break
    }

    case 'pushRoute': {
      const { path, query } = event.data.payload
      router.push({
        path,
        query,
      })
      break
    }
    default:
      break
  }
}

window.addEventListener('message', handleMessage)
</script>

<template>
  <a-config-provider :theme="theme" :locale="zhCN">
    <a-spin :spinning="appStore.appLoading">
      <RouterView />
    </a-spin>
  </a-config-provider>
</template>

<style scoped></style>
