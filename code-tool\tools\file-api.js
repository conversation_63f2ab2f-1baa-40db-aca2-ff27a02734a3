import axios from 'axios'
import { MENU_CONFIG, PROJECT_MODULE, VIEWMENUDTL_CONFIG } from '../config/index.js'
import { getInterfaceInfos, getModuleName } from '../tools/index.js'

// 菜单数据映射
const dataMapping = {
  menuUrl: 'menuUrl',
  menuName: 'menuName',
  menuId: 'menuId',
  childrens: 'childrens',
}

// 白名单路由
const whiteArr = [
  'homePage',
  'getSsoAppList',
  'TebantSelect',
  'ParameterSelect',
  'SystemManagement',
  'positionManageList',
  'organizationalManagement',
  'RoleManagement',
  'positionManage',
  'userGroupList',
  'userManagementList',
  'BuSelect',
  'organizationType',
  'detailSystem',
]

/**
 * 获取API请求数据
 * @async
 * @function getApiRequest
 * @returns {Promise<Array>} 返回处理后的模块数据数组
 */
export async function getApiRequest(url, params) {
  try {
    const val = await axios({ method: 'get', url, params })
    let data = val?.data?.data
    if (!data) return
    let modules = data?.modules
    data.modules = modules.map((item) => {
      return {
        moduleName: getModuleName(item, data.modules),
        moduleId: item.moduleId,
        moduleNameCn: item.moduleName,
        interfaceInfos: getInterfaceInfos(item.interfaceInfos),
      }
    })

    return data
  } catch (err) {
    console.log(
      '%c 🍒 err: ',
      'font-size:12px;background-color: #E41A6A;color:#fff;',
      '获取swagger文档失败, 失败原因：' + err,
    )
  }
}

export async function getViewsRequest() {
  try {
    const { data } = await axios({ ...MENU_CONFIG })

    const menu = data?.data || []
    const newList = extractMenuType(menu)
    const list = flattenMenuData(newList)
    return {
      menu,
      list,
    }
  } catch (err) {
    console.log(
      '%c 🥠 获取views失败: ',
      'font-size:12px;background-color: #B03734;color:#fff;',
      err,
    )
  }
}

export async function getViewMenuDtl(params, headers = {}) {
  try {
    const { data } = await axios({
      ...VIEWMENUDTL_CONFIG,
      ...params,
      headers: { ...VIEWMENUDTL_CONFIG.headers, ...headers },
    })
    return data
  } catch (err) {
    console.log(
      '%c 🥠 获取ViewMenuDtl失败: ',
      'font-size:12px;background-color: #B03734;color:#fff;',
      err,
    )
  }
}

/**
 * 从给定的菜单数据中提取特定类型的菜单项。
 *
 * @param {Array} menu - 包含菜单项的数组。
 * @returns {Array} 返回符合条件的菜单项数组，每项包含 menuName 和 menuUrl。
 *
 * @description
 * 此函数遍历菜单数组，提取满足以下条件的菜单项：
 * 1. menuType 为 1
 * 2. 没有子菜单，或者所有子菜单的 menuType 都为 2
 * 函数使用递归方法遍历所有层级的菜单项。
 *
 * @example
 * const menu = [
 *   { menuType: 1, menuName: '菜单1', menuUrl: '/menu1', childrens: [] },
 *   { menuType: 1, menuName: '菜单2', menuUrl: '/menu2', childrens: [
 *     { menuType: 2, menuName: '子菜单2-1', menuUrl: '/submenu2-1' }
 *   ]}
 * ];
 * const result = extractMenuType(menu);
 * console.log(result);
 * // 输出: [
 * //   { menuName: '菜单1', menuUrl: '/menu1' },
 * //   { menuName: '菜单2', menuUrl: '/menu2' }
 * // ]
 */
export function extractMenuType(menu) {
  let result = []
  if (!menu || menu.length === 0) return []
  const traverseMenu = (items) => {
    items.forEach((item) => {
      if (
        (item.menuType === 1 || item.menuType === 3) &&
        (!item[dataMapping.childrens] ||
          item[dataMapping.childrens] === 0 ||
          (item[dataMapping.childrens] &&
            item[dataMapping.childrens].every((child) => child.menuType === 2)))
      ) {
        result.push({
          menuName: item[dataMapping.menuName],
          menuUrl: item[dataMapping.menuUrl],
          menuId: item[dataMapping.menuId],
        })
      }
      if (item.childrens && item.childrens.length > 0) {
        traverseMenu(item.childrens) // 递归遍历子菜单
      }
    })
  }

  traverseMenu(menu)
  return result
}

// 过滤权限中心菜单
export function flattenMenuData(menu) {
  const list = menu
  return list.filter((item) => !whiteArr.includes(item.menuUrl))
}

export async function testApiData() {
  try {
    const { data } = await axios({ ...PROJECT_MODULE })
    const records = data?.data?.records || []
    records.map(async (item) => {
      const val = await axios({
        url: 'http://localhost:8112/flow/rest/interfaceInfo',
        method: 'get',
        params: {
          projectId: PROJECT_MODULE.projectId,
          moduleId: item.moduleId,
          pageSize: 999,
          includeDeletedData: 0,
        },
      })
      item.interfaceId = val.interfaceId
      console.log(
        '%c 🍑 val: ',
        'font-size:12px;background-color: #B03734;color:#fff;',
        val.data.data,
      )
    })
  } catch (err) {
    console.log('%c 🦐 err: ', 'font-size:12px;background-color: #2EAFB0;color:#fff;', err)
  }
}

export async function getAxios(params) {
  return axios({
    url: params.url,
    method: params.methods,
    data: params.data,
    headers: params.headers,
  })
}
