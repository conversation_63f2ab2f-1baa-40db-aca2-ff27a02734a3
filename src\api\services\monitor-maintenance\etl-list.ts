import service from '@/api'
import type { Parameter } from '../data-asset/hbase-management'
import { SODATAFLINK } from '@/api/base'

import { orderBy } from 'lodash'

export class gridColumnDTO {
  /** ID */
  id!: number

  /** 源数据库类型 */
  srcDbType?: string

  /** 源数据库类型中文 */
  srcDbTypeZH?: string

  /** 源库 */
  srcDbname?: string

  /** 宽度 */
  srcTableName?: string

  /** 目标库类型 */
  desDbType?: string

  /** 源数据库类型中文 */
  desDbTypeZH?: string

  /** 目标库 */
  dbDesDbName?: string

  /** 目标表 */
  desTableName!: string

  /** 任务执行频率 */
  dataFreExpression?: string
  /** 任务执行开始时间 */
  dataStartTime?: string
  /** 更新时间 */
  updateTime?: string

  /** 是否增量 */
  srcIsIncrement?: boolean
  /** 增量字段 */
  srcIncrementCol?: string
  /** 源库配置信息表id */
  srcEsiId?: string
  /** 目标库配置信息表id */
  desEsiId?: string
  /** 是否有效 */
  isEnabled?: string
  /** 是否有效 */
  isEnabledBoo?: boolean
  /** 用于控制卡片的闭合 */
  isOpen?: boolean
  /** 用于控制卡片的选中效果 */
  isCheck?: boolean
}

export class gridobj {
  pageIndex?: number
  pageSize?: number
  records?: Array<gridColumnDTO>
  totalPages?: number
  totalRecords?: number
}

export class DbTypeDTO {
  value!: number
  label!: string
}

/**
 * 获取源库类型列表
 */
export function getDbTypeList() {
  const result = [
    {
      value: 1,
      label: 'MySQL',
    },
    {
      value: 2,
      label: 'Kudu',
    },
    {
      value: 3,
      label: 'TiDB',
    },
    {
      value: 4,
      label: 'Hive',
    },
    {
      value: 5,
      label: 'Oracle',
    },
    {
      value: 6,
      label: 'SQLServer',
    },
    {
      value: 7,
      label: 'Cache',
    },
    {
      value: 8,
      label: 'MongoDB',
    },
    {
      value: 9,
      label: 'File',
    },
    {
      value: 10,
      label: 'HTTP',
    },
    {
      value: 11,
      label: 'Impala',
    },
    {
      value: 12,
      label: 'Greenplum',
    },
    {
      value: 13,
      label: 'DM',
    },
    {
      value: 14,
      label: 'Druid',
    },
    {
      value: 15,
      label: 'Kafka',
    },
    {
      value: 16,
      label: 'PostgreSQL',
    },
    {
      value: 17,
      label: 'Socket',
    },
    {
      value: 18,
      label: 'Flink',
    },
    {
      value: 19,
      label: 'GBase',
    },
    {
      value: 21,
      label: 'HDFS',
    },
    {
      value: 22,
      label: 'OceanBase',
    },
    {
      value: 23,
      label: 'InfluxDB',
    },
    {
      value: 24,
      label: 'Spark',
    },
    {
      value: 25,
      label: 'Doris',
    },
    {
      value: 26,
      label: 'TDengine',
    },
    {
      value: 27,
      label: 'Elasticsearch',
    },
    {
      value: 28,
      label: 'ClickHouse',
    },
    {
      value: 29,
      label: 'Kingbase',
    },
    {
      value: 30,
      label: 'HBase',
    },
  ]
  // 以 `label` 升序排序
  return orderBy(result, ['label'], ['asc'])
}

export function deletePageList(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/config/TableInfo/etltableinfoDelete/${params.id}`,
    method: 'delete',
  })
}

/**
 * 自动获取库源名称
 * @param params
 */
export function getDatabaseFun(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/config/DbSchema/getDatabase`,
    method: 'post',
    data: params,
  })
}

/**
 * 自动获取库源名称-三层结构
 * @param params
 */
export function getDatabaseByIdFun(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/SchemaInfo/getNotRealTimeDatabaseById`,
    method: 'post',
    data: params,
  })
}

/**
 * 自动获取模式-三层结构
 * @param params
 */
export function selectSchemaByIdFun(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/SchemaInfo/getNotRealTimeSchemaById`,
    method: 'post',
    data: params,
  })
}

/**
 * 自动获取表名称-三层结构
 * @param params
 */
export function selectDbnameByIdFun(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/SchemaInfo/getTablesNotRealTimeById`,
    method: 'post',
    data: params,
  })
}

/**
 * 自动获取字段列名称-三层结构
 * @param params
 */
export function selectSrcTableNameByIdFun(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/SchemaInfo/getNotRealTimeColumnAndTypeByIdFun`,
    method: 'post',
    data: params,
  })
}

/**
 * 自动获取表名称
 * @param params
 */
export function selectDbnameFun(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/config/DbSchema/getTables`,
    method: 'post',
    data: params,
  })
}

/**
 * 自动获取字段列名称
 * @param params
 */
export function selectSrcTableNameFun(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/config/DbSchema/getColumnAndType`,
    method: 'post',
    data: params,
  })
}

/**
 * 附件上传
 * @param params
 */
export function uploadFile(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/config/TableInfo/uploadFile`,
    method: 'post',
    data: params,
  })
}

/**
 * 自动获取建表字段和字段类型
 * @param params
 */
export function getDesColumnAndType(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/config/DbSchema/getDesColumnAndType`,
    method: 'post',
    data: params,
  })
}
