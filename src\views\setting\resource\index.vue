<template>
  <div class="resource-page">
    <CardBox title="资源服务" subTitle="资源服务信息">
      <template #headerRight>
        <a-button type="primary" v-action:add @click="handelAddData"><PlusOutlined />新增</a-button>
      </template>

      <Table
        :columns="columns"
        :getData="getResourceList"
        ref="tableRef"
        :searchFormState="formMsg"
      >
        <template #search>
          <a-form-item label="IP地址">
            <a-input v-model:value="formMsg.serviceIp" placeholder="请输入IP地址" allowClear />
          </a-form-item>
          <a-form-item label="SSH端口">
            <a-input-number
              v-model:value="formMsg.sshPort"
              :min="0"
              placeholder="请输入SSH端口"
              allowClear
              style="width: 100%"
            />
          </a-form-item>
        </template>

        <template #bodyCell="{ column, record }: { column: any; record: ResourceDTO }">
          <template v-if="column.key === 'serviceType'">{{
            transformType(record.serviceType)
          }}</template>
          <template v-if="column.key === 'action'">
            <a-space>
              <a-button type="link" size="small" v-action:edit @click="handelTest(record)"
                >测试</a-button
              >
              <a-button type="link" size="small" v-action:edit @click="handelEdit(record)"
                >修改</a-button
              >
              <a-button type="link" size="small" v-action:delete @click="handelDelete(record)"
                >删除</a-button
              >
            </a-space>
          </template>
        </template>
      </Table>
    </CardBox>
    <EditResource ref="editResource" @success="loadData" />
  </div>
</template>

<script lang="ts" setup>
import CardBox from '@/components/card-box/card-box.vue'
import { Table } from '@fs/fs-components'
import { getResourceList, deleteResource, ResourceDTO, testResource } from '@/api/setting/resource'
import EditResource from './edit-resource.vue'
import { ref, reactive } from 'vue'
import { Modal } from 'ant-design-vue'
import { message } from 'ant-design-vue'
import { type ResouceAction } from './resource.d'
import { PlusOutlined } from '@ant-design/icons-vue'

const editResource = ref<ResouceAction>()
const tableRef = ref<typeof Table>()
const formMsg = reactive({
  serviceIp: '',
  sshPort: '',
})

const columns = [
  {
    title: '服务端口',
    dataIndex: 'servicePort',
    key: 'servicePort',
  },
  {
    title: '逻辑ID',
    dataIndex: 'logicId',
    key: 'logicId',
  },
  {
    title: '类型',
    dataIndex: 'serviceType',
    key: 'serviceType',
  },
  {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
    fixed: 'right',
    width: 250,
  },
]

function transformType(type: number) {
  let result = ''
  switch (type) {
    case 0:
      result = 'worker'
      break
    case 1:
      result = 'master'
      break
    case 2:
      result = '执行脚本节点'
      break
    case 3:
      result = 'flink'
      break
    case 4:
      result = 'spark'
      break
    case 5:
      result = '模型服务'
  }
  return result
}

function loadData() {
  tableRef.value?.getTableData()
}

// 添加
function handelAddData() {
  editResource.value?.showModal('add')
}

// 修改
function handelEdit(record: ResourceDTO) {
  editResource.value?.showModal('edit', record)
}

// 删除
function handelDelete(record: ResourceDTO) {
  Modal.confirm({
    title: '提示',
    content: `确定要删除【${record.serviceIp}】吗 ?`,
    onOk: () => {
      deleteResource({ id: record.id }).then((res) => {
        message.success('删除成功')
        // hackDeleteLastItem()
        tableRef.value?.getTableData()
      })
    },
    okText: '确认',
    cancelText: '取消',
  })
}

// 测试
function handelTest(record: ResourceDTO) {
  Modal.confirm({
    title: '提示',
    content: `确定要测试【${record.serviceIp}】吗 ?`,
    onOk: () => {
      testResource({ id: record.id }).then((res) => {
        if (res.data.code === 1) {
          message.success('测试成功')
        } else {
          message.error(res.data.message)
        }
      })
    },
    okText: '确认',
    cancelText: '取消',
  })
}
</script>

<style lang="less" scoped>
.resource-page {
  height: calc(100vh - 40px);

  :deep(.box-form-g) {
    margin-bottom: 0;
  }
}
</style>
