<template>
  <div class="user-center-container">
    <div class="header">
      <span style="height: 24px">个人中心</span>
      <button class="refresh-btn" @click="refreshSysmenu">刷新证书</button>
    </div>
    <div class="content">
      <div class="avatar-wrapper">
        <template v-if="blobUrl">
          <img :src="blobUrl" class="avatar" @click="uploadModalVisible = true" alt="avatar" />
        </template>
        <template v-else>
          <div class="avatar" @click="uploadModalVisible = true"></div>
        </template>
      </div>
      <div class="info-box">
        <div class="info-row">
          <span class="label">用户名：</span>
          <span class="value">{{ userName }}</span>
        </div>
        <div class="info-row">
          <span class="label">密码：</span>
          <span class="value">********</span>
        </div>
        <div class="divider"></div>
        <div class="info-row">
          <span class="label">所有者：</span>
          <span class="value">{{ licenseData.owner }}</span>
        </div>
        <div class="info-row">
          <span class="label">信息：</span>
          <span class="value">{{ licenseData.message }}</span>
          <a class="upload-link" href="#" @click="uploadModalVisible2 = true">上传licence</a>
        </div>
        <div class="info-row">
          <span class="label">过期时间：</span>
          <span class="value">{{ licenseData.expiredDate }}</span>
        </div>
        <div class="divider"></div>
      </div>
    </div>
    <UploadModal
      ref="uploadModalRef"
      v-model="uploadModalVisible"
      :uploadFunc="sysSystemFunUploadFile"
      @success="importHandleSuccess"
    />
    <UploadModal
      ref="uploadModalRef2"
      title="上传licence"
      v-model="uploadModalVisible2"
      fileName="licenseFile"
      :uploadFunc="upgradeLicense"
      @success="importHandleSuccess2"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import {
  checkLicense,
  sysSystemFunUploadFile,
  upgradeLicense,
  upadateUserAvatar,
  downLoadFile,
} from '@/api/services/user'
import { useUserStore } from '@/stores/user'
import UploadModal from '@/components/upload-modal/index.vue'
import { message } from 'ant-design-vue'

const userStore = useUserStore()
const licenseData = ref<any>({})
const uploadModalVisible = ref(false)
const uploadModalVisible2 = ref(false)
const { userName } = userStore.userInfo
const blobUrl = ref('')
const refreshSysmenu = () => {
  window.location.reload()
}

onMounted(() => {
  checkLicense({}).then((res) => {
    licenseData.value = res.data
  })
  downLoadFileHandle(userStore.userInfo.avatarUrl)
})

const downLoadFileHandle = async (fileUrl: string) => {
  if (!fileUrl) return
  const res = await downLoadFile({ fileUrl })
  // 1. 创建 Blob URL
  blobUrl.value = URL.createObjectURL(res)
}

const importHandleSuccess = async (res: any) => {
  if (res.code === '000000') {
    const data = await upadateUserAvatar({ avatarUrl: res.data, userId: userStore.userInfo.userId })
    if (data.code === '000000') {
      downLoadFileHandle(res.data)
      message.success(data.msg)
    }
    uploadModalVisible.value = false
  }
}

const importHandleSuccess2 = (res: any) => {
  if (res.code === '000000') {
    message.success(res.msg)
    uploadModalVisible2.value = false
    licenseData.value = res.data
  }
}
</script>

<style scoped lang="less">
.user-center-container {
  min-height: 600px;
  border-radius: 4px;
  box-shadow: 0 2px 8px #f0f1f2;
  padding: 0 0 40px 0;
}
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 40px;
  background: #fff;
  box-shadow: 0 8px 15px 0 rgba(149, 156, 182, 0.15);
  border-radius: 4px;
  border: 1px solid #eaebf0;
  margin-bottom: 16px;
  color: rgba(0, 0, 0, 0.85);
  font-size: 14px;
}
.refresh-btn {
  background: none;
  border: none;
  color: #409eff;
  cursor: pointer;
  font-size: 12px;
}
.content {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: 80px;
  background: #fff;
}
.avatar-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 32px;
}
.avatar {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background: #e6f0fa;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px #f0f1f2;
  background-image: url('@/assets/photo-bg.png');
  background-size: cover;
}
.info-box {
  width: 600px;
  background: #fff;
  border-radius: 4px;
  padding: 32px 48px;
}
.info-row {
  display: flex;
  align-items: center;
  margin-bottom: 18px;
}
.label {
  color: #999;
  width: 100px;
  text-align: left;
  margin-right: 16px;
}
.value {
  color: #333;
  flex: 1;
  text-align: left;
}
.upload-link {
  margin-left: 16px;
  color: #409eff;
  text-decoration: none;
  font-size: 14px;
}
.divider {
  border-bottom: 1px solid #eee;
  margin: 18px 0;
}
</style>
