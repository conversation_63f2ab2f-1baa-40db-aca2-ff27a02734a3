export interface VectorizerConfig {
  api_key: string
  vector_dimensions: string | number
  base_url: string
  source_type: string
  model: string
  type: string
}

export interface LLMConfig {
  creator: string
  default: boolean
  createTime: string
  api_key: string
  stream: string | boolean
  base_url: string
  temperature: number
  model: string
  type: string
  llm_id: string
  desc: string
}

export interface GraphStoreConfig {
  database: string
  password: string
  source_type: string
  uri: string
  user: string
}

export interface PromptConfig {
  biz_scene: string
  source_type: string
  language: string
}

export interface KnowledgeBaseConfig {
  vectorizer: VectorizerConfig
  llm_select: LLMConfig[]
  graph_store: GraphStoreConfig
  llm: LLMConfig
  prompt: PromptConfig
}
export type KnowledgeBase = {
  id: number
  /**
   * 中文名
   */
  name: string
  description: string
  /**
   * 英文名
   */
  namespace: string
  config: string
}
export type KnowledgeBaseList = {
  code: string
  list: KnowledgeBase[]
  page: number
  pageSize: number
  total: number
}

export interface ExtractConfig {
  llmType: string
  llmPrompt: string
  autoSchema: boolean
  autoWrite: boolean
}

export interface SplitConfig {
  splitLength: string
  semanticSplit: boolean
}

export interface Extension {
  extractConfig: ExtractConfig
  splitConfig: SplitConfig
}

export interface DataItem {
  id: number
  projectId: number
  gmtCreate: string
  gmtModified: string
  modifyUser: string
  createUser: string
  taskId: number
  jobName: string
  fileUrl: string
  status: 'FINISH' | string // 可以扩展其他可能的状态值
  type: 'FILE_EXTRACT' | string // 可以扩展其他可能的类型
  extension: Extension | string // 可能是字符串或解析后的对象
  version: string
}

export interface KnowledgeTaskList {
  total: number
  pageSize: number
  page: number
  data: DataItem[]
}

export interface GeneralConfig {
  database: string
  password: string
  uri: string
  user: string
  type: string
  model: string
  base_url: string
  api_key: string
  biz_scene: string
  lauguage: string
}
