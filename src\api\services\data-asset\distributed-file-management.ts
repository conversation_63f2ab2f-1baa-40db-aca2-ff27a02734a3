import service from '@/api'
import { SODATAFLINK } from '@/api/base'

export interface AssetResponse<T = any> {
  data: T
  code: string
  msg: string
  result: string
}

export interface IPageParams<T = any> {
  pageIndex: number
  pageSize: number
  totalPages: number
  totalRecords: number
  records: T
}

export interface IDirectoryParams {
  path: string // 文件夹全路径
  id: number // 数据库信息编号
}

// 创建文件夹
export function addCategoryItem(data: IDirectoryParams): Promise<AssetResponse<string>> {
  return service({
    method: 'post',
    url: `${SODATAFLINK}/hdfsSettings/createFolder`,
    data: data,
  })
}

// 获取文件目录列表
export async function getFileDirectoryById(data: IDirectoryParams): Promise<AssetResponse<any[]>> {
  return service({
    method: 'GET',
    url: `${SODATAFLINK}/hdfsSettings/getFileDirectory`,
    params: data,
  })
}

export interface IScameItemParams {
  pageIndex: number // 1
  pageSize: number // 9999
  dbType: number // 传21 筛选 hdfs数据
  ownerId: string // len@bigdata
}

export interface IScameInfoList {
  dbDesc: string // 展示名称
  id: number // 传值10187
  metaUrlInfo: string
  dbType: number
  updateTime: string
  dbOther: string
  authorizationList: string
  dbUsername: string
  createTime: string
  updatedPerosn: string
  dbUrl: string
  logicId: string
  dbPassword: string
}
// 获取数据源列表
export async function getScameInfoList(
  data: IScameItemParams,
): Promise<AssetResponse<IPageParams<IScameInfoList[]>>> {
  return service({
    method: 'GET',
    url: `${SODATAFLINK}/new2dataplatform/InfoSchema/PageListScameInfo`,
    params: data,
  })
}

export interface IEditCategoryNameParams {
  oldPath: string
  id: number
  newPath: string
}

// 重命名文件或文件夹名称
export function editCategoryName(data: IDirectoryParams): Promise<AssetResponse<string>> {
  return service({
    method: 'post',
    url: `${SODATAFLINK}/hdfsSettings/renameFolderOrFile`,
    data: data,
  })
}

// 删除文件或文件夹
export function deleteCategoryItem(data: IDirectoryParams): Promise<AssetResponse<string>> {
  return service({
    method: 'post',
    url: `${SODATAFLINK}/hdfsSettings/deleteFolderOrFile`,
    data,
  })
}

export interface IDownloadParams {
  localPath?: string // 本地下载存放路径(沟通过，不要该字段了)
  id: number // 数据库信息编号
  hdfsPath: string // hdfs文件存放路径
}
// 下载文件
export function downloadFileApi(data: IDownloadParams): Promise<AssetResponse<string>> {
  return service({
    method: 'post',
    url: `${SODATAFLINK}/hdfsSettings/downloadFile`,
    data,
    responseType: 'blob',
  })
}

// 上传文件
// export interface IUploadParams {
//   id: number // 数据库信息编号
//   file: any // 文件 类型要改
//   hdfsPath: string // hdfs文件存放路径
// }

export function uploadFileApi(data: any): Promise<AssetResponse<string>> {
  return service({
    method: 'post',
    url: `${SODATAFLINK}/hdfsSettings/uploadFile`,
    data,
  })
}
