import type { RouteRecordRaw } from 'vue-router'

const isSecondary = import.meta.env.VITE_APP_NEED_AUTH === 'false'

/**
 * 元数据管理
 */
const routes: RouteRecordRaw[] = [
  {
    path: '/meta-data-management',
    name: 'meta-data-management',
    component: () => import('@/views/meta-data/meta-data-management/index.vue'),
    meta: {
      isSecondary,
      title: '元数据管理',
    },
  },
  {
    path: '/meta-data-table',
    name: 'meta-data-table',
    component: () => import('@/views/meta-data/meta-data-table/index.vue'),
    meta: {
      isSecondary,
      title: '表详情',
    },
  },
]
export default routes
