<template>
  <a-modal :open="userOpen" :zIndex="1001" title="员工列表" width="1400px" @cancel="cancel">
    <template #footer> </template>
    <main>
      <a-row class="aRow">
        <a-col>
          <div class="left">
            <tree-node
              :treeData="treeData"
              @treeSelect="treeSelect"
              :isMenus="false"
              :fNames="fNames"
            ></tree-node>
          </div>
        </a-col>
        <a-col :span="18">
          <div class="right">
            <div class="example" v-show="spinModal">
              <a-spin />
            </div>
            <div class="addMenu">
              <div class="centent">
                <Table
                  :columns="columns"
                  :getData="getUserList"
                  ref="tableRef"
                  :searchFormState="formState"
                  :autoRequest="false"
                >
                  <template #search>
                    <a-form-item label="姓名" name="姓名" :rules="[{ message: '请输入姓名' }]">
                      <a-input v-model:value="form.userName" placeholder="请输入姓名" />
                    </a-form-item>
                    <a-form-item
                      label="手机号"
                      name="手机号"
                      :rules="[{ message: '请输入手机号' }]"
                    >
                      <a-input type="tel" v-model:value="form.mobile" placeholder="请输入手机号" />
                    </a-form-item>
                    <a-form-item
                      label="登录账号"
                      name="登录账号"
                      :rules="[{ message: '请输入登录账号' }]"
                    >
                      <a-input v-model:value="form.loginAccount" placeholder="请输入登录账号" />
                    </a-form-item>
                    <a-form-item label="查找范围">
                      <a-radio-group v-model:value="form.queryType" placeholder="请选择查找范围">
                        <a-radio value="1">本级组织</a-radio>
                        <a-radio value="2">本级及下级组织</a-radio>
                      </a-radio-group>
                    </a-form-item>
                  </template>
                </Table>
              </div>
            </div>
          </div>
        </a-col>
      </a-row>
    </main>
  </a-modal>
</template>
<script lang="ts" setup>
import { Table } from '@fs/fs-components'
import { useUserStore } from '@/stores/user'
import { h, onBeforeMount, onMounted, ref } from 'vue'
import { OrganizationalUserDTO } from '@/utils/column'
import { type FormInstance, Button } from 'ant-design-vue'
import { getUserList, getDeptList } from '@/api/services/permission'
import type { Column } from '@fs/fs-components/src/components/table/type'
interface FormState {
  userName: string
  mobile: string
  loginAccount: string
  queryType: string
  deptId: string
}
const form = ref<FormState>({
  userName: '',
  mobile: '',
  loginAccount: '',
  queryType: '',
  deptId: '',
})

const fNames = {
  children: 'deptList',
  title: 'deptName',
  key: 'deptId',
}

const formRef = ref<FormInstance>()
const treeData = ref<any>([])
const tableRef = ref<InstanceType<typeof Table> | null>(null)
const spinModal = ref<boolean>(false)
let columns = ref<Column[]>([])
const formState = ref({})
const userStore = useUserStore()
const emits = defineEmits(['selectUserHandle'])
const userOpen = ref<boolean>(false)

onMounted(() => {
  formState.value = Object.assign(form.value, {
    mobile: '',
    queryType: '',
  })
})

onBeforeMount(() => {
  initColumns()
})

function getTreeLists() {
  const params = {
    buId: userStore.userInfo.buId as string,
    isUse: 1,
  }
  getDeptList(params).then((result: any) => {
    if (result.code === '000000') {
      treeData.value = result.data
      // form.value.deptId = result.data[0].deptId
      tableRef.value && tableRef.value?.getTableData()
    }
  })
}

async function treeSelect(selectedKeys: string) {
  resetForm()
  if (selectedKeys) {
    spinModal.value = true
    form.value.deptId = selectedKeys
    await tableRef.value?.getTableData()
    spinModal.value = false
  }
}

const resetForm = () => {
  formRef.value && formRef.value.resetFields()
}

async function show() {
  userOpen.value = true
  await getTreeLists()
}

function cancel() {
  userOpen.value = false
}

async function initColumns() {
  columns.value = OrganizationalUserDTO.map((item) => {
    return {
      ...item,
      dataIndex: item.key,
    }
  })
  columns.value.push({
    key: 'action',
    title: '操作',
    customRender(data: Record<string, any>) {
      return [
        h(
          Button,
          {
            type: 'link',
            onClick() {
              emits('selectUserHandle', data.record)
              userOpen.value = false
            },
          },
          {
            default: () => '请选择',
          },
        ),
      ]
    },
  })
}
defineExpose({ show })
</script>
<style lang="less" scoped>
main {
  height: 100%;

  .left {
    height: 100%;
    border: 1px solid rgba(0, 0, 0, 0.04);
    border-radius: 4px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    min-width: 260px;

    .content {
      padding: 16px;
    }
  }

  .right {
    margin: 0 16px;
    position: relative;
    border: 1px solid rgba(0, 0, 0, 0.04);
    border-radius: 4px;
    text-align: center;
    height: 100%;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);

    .centent {
      padding: 16px;
    }
  }

  .aRow {
    height: 100%;
  }
}

.example {
  text-align: center;
  background: rgba(0, 0, 0, 0.01);
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 100;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
<style lang="less">
.popoverOption {
  width: 150px;
}
</style>
