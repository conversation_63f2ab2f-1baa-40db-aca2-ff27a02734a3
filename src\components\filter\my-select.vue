<template>
  <Select
    v-model:value="selectedItems"
    mode="tags"
    style="width: 100%"
    :options="filteredOptions"
    :allow-clear="true"
    @blur="onBlur"
  ></Select>
</template>
<script lang="ts" setup>
import { computed, nextTick } from 'vue'
import { Select, type SelectProps } from 'ant-design-vue'

const props = defineProps<{
  options?: SelectProps['options']
  isNumber?: boolean
}>()
const selectedItems = defineModel<string[]>('value', { required: true })

const onBlur = (e: any) => {
  if (!props.isNumber) return
  if (!/^\d+$/.test(e.target.value)) {
    console.log('请输入数字', e.target.value)
    nextTick(() => {
      console.log(' selectedItems.value', selectedItems.value)
      selectedItems.value.pop()
    })
  }
}

const filteredOptions = computed(() =>
  props.options?.filter((o) => {
    const isNumber = typeof o?.value === 'number' || !isNaN(Number(o?.value))
    return isNumber && !selectedItems.value.includes(String(o.value))
  }),
)
</script>
