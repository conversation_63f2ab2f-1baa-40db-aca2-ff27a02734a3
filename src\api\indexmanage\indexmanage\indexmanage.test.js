import { expect, test } from 'vitest'
import {
  addNewIndexGroup,
  getIndexGroupListByPage,
  modifyIndexGroupData,
  getSingleIndexGroupDataDtl,
  addNewIndexData,
  getIndexDataDtl,
  modifyIndexData,
  deployIndexData,
  getIndexHistoryDataListByPages,
  getIndexDataListByPages,
  getGroupTree,
  reviewByIndexId,
  getIndexNameList,
  getRealeaseIndexList,
} from './indexmanage'

test('addNewIndexGroup', async () => {
  const { data } = await addNewIndexGroup({
    parentId: 'If25P0',
    groupName: 'QuwEve',
    remarks: 'WMHK!K',
  })

  expect(data).toHaveProperty('code')
  expect(data).toHaveProperty('msg')
  expect(data.data[0]).toHaveProperty('userId'), expect(data.data[0]).toHaveProperty('userName')
})

test('getIndexGroupListByPage', async () => {
  const { data } = await getIndexGroupListByPage({
    groupName: 'H%mcI',
    status: 6910678303831201,
    pageIndex: 3860936013406440,
    pageSize: 7935553678333483,
  })

  expect(data).toHaveProperty('code')
  expect(data).toHaveProperty('msg')
  expect(data.data).toHaveProperty('records')
})

test('modifyIndexGroupData', async () => {
  const { data } = await modifyIndexGroupData({
    parentId: '6ckx1',
    groupId: 'byh3',
    groupName: 'X^8',
    status: 5960423081927246,
    remarks: 'W@E',
  })

  expect(data).toHaveProperty('code')
  expect(data).toHaveProperty('msg')
  expect(data).toHaveProperty('data')
})

test('getSingleIndexGroupDataDtl', async () => {
  const { data } = await getSingleIndexGroupDataDtl({ groupId: 'n3r9(y' })

  expect(data).toHaveProperty('code')
  expect(data).toHaveProperty('msg')
  expect(data.data).toHaveProperty('groupId'),
    expect(data.data).toHaveProperty('groupName'),
    expect(data.data).toHaveProperty('status'),
    expect(data.data).toHaveProperty('remarks'),
    expect(data.data).toHaveProperty('tenantId'),
    expect(data.data).toHaveProperty('createUser'),
    expect(data.data).toHaveProperty('createUsername'),
    expect(data.data).toHaveProperty('createTime'),
    expect(data.data).toHaveProperty('updateUser'),
    expect(data.data).toHaveProperty('updateUsername'),
    expect(data.data).toHaveProperty('updateTime')
})

test('addNewIndexData', async () => {
  const { data } = await addNewIndexData({
    formHistoryId: 'CFB',
    indexName: 'aZ9yF',
    groupId: '[L%CNd',
    parentId: '2*#(E',
    calFormula: 'vYSL',
    dataSourceId: 6315345319196774,
    formData: 'eeI0',
    remarks: 'eVPEe',
  })

  expect(data).toHaveProperty('code')
  expect(data).toHaveProperty('msg')
  expect(data).toHaveProperty('data')
})

test('getIndexDataDtl', async () => {
  const { data } = await getIndexDataDtl({ autoId: '3vm' })

  expect(data).toHaveProperty('code')
  expect(data).toHaveProperty('msg')
  expect(data.data).toHaveProperty('autoId'),
    expect(data.data).toHaveProperty('groupId'),
    expect(data.data).toHaveProperty('parentId'),
    expect(data.data).toHaveProperty('formDataId'),
    expect(data.data).toHaveProperty('indexName'),
    expect(data.data).toHaveProperty('calFormula'),
    expect(data.data).toHaveProperty('formData'),
    expect(data.data).toHaveProperty('formHistoryId'),
    expect(data.data).toHaveProperty('dataSourceId'),
    expect(data.data).toHaveProperty('remarks'),
    expect(data.data).toHaveProperty('status'),
    expect(data.data).toHaveProperty('revision'),
    expect(data.data).toHaveProperty('isDeleted'),
    expect(data.data).toHaveProperty('isRelease'),
    expect(data.data).toHaveProperty('currentVersion'),
    expect(data.data).toHaveProperty('tenantId'),
    expect(data.data).toHaveProperty('basicRuleList'),
    expect(data.data).toHaveProperty('createUser'),
    expect(data.data).toHaveProperty('createUsername'),
    expect(data.data).toHaveProperty('createTime'),
    expect(data.data).toHaveProperty('updateUser'),
    expect(data.data).toHaveProperty('updateUsername'),
    expect(data.data).toHaveProperty('updateTime')
})

test('modifyIndexData', async () => {
  const { data } = await modifyIndexData({
    autoId: 'Q!8qi',
    indexName: '%plC',
    groupId: 'jw4]V',
    parentId: 'tlj%',
    calFormula: '0@KfrB',
    formData: 'dTK',
    revision: 6366606475278258,
    remarks: ']#94ypd',
    isDeleted: 3098322167466191,
  })

  expect(data).toHaveProperty('code')
  expect(data).toHaveProperty('msg')
  expect(data).toHaveProperty('data')
})

test('deployIndexData', async () => {
  const { data } = await deployIndexData({ autoId: 'I$c(G[' })

  expect(data).toHaveProperty('code')
  expect(data).toHaveProperty('msg')
  expect(data).toHaveProperty('data')
})

test('getIndexHistoryDataListByPages', async () => {
  const { data } = await getIndexHistoryDataListByPages({
    basicDataId: '1rnt',
    startTime: 'Udqo',
    endTime: 'cF4k',
    pageIndex: 5321024722319610,
    pageSize: 2117884379443897,
  })

  expect(data).toHaveProperty('code')
  expect(data).toHaveProperty('msg')
  expect(data.data).toHaveProperty('records')
})

test('getIndexDataListByPages', async () => {
  const { data } = await getIndexDataListByPages({
    indexName: 'z#5',
    groupId: 'rSB#',
    parentId: 'HWo7',
    formDataId: '4U4DVY',
    businessType: 8046632958239183,
    formData: 'c03O',
    isDeleted: 3642027220703182,
    status: 1887535981416931,
    startTime: 'uVlhj2',
    endTime: 'sy#[Q',
    isRelease: 6386913641152464,
    pageIndex: 6444259757848394,
    pageSize: 4415964574328774,
  })

  expect(data).toHaveProperty('code')
  expect(data).toHaveProperty('msg')
  expect(data.data).toHaveProperty('records')
})

test('getGroupTree', async () => {
  const { data } = await getGroupTree({ groupName: '#QyW' })

  expect(data).toBeTruthy()
})

test('reviewByIndexId', async () => {
  const { data } = await reviewByIndexId({ indexId: '92VKMeh' })

  expect(data).toHaveProperty('code')
  expect(data).toHaveProperty('msg')
  expect(data.data).toHaveProperty('cost'), expect(data.data).toHaveProperty('dataList')
})

test('getIndexNameList', async () => {
  const { data } = await getIndexNameList({
    indexName: 'dr(*',
    pageIndex: 243626661672445,
    pageSize: 3753120250941738,
  })

  expect(data).toBeTruthy()
})

test('getRealeaseIndexList', async () => {
  const { data } = await getRealeaseIndexList({
    indexName: 'g25g',
    pageIndex: 4052987421336944,
    pageSize: 284587146803505,
  })

  expect(data).toHaveProperty('code')
  expect(data).toHaveProperty('msg')
  expect(data.data).toHaveProperty('records')
})
