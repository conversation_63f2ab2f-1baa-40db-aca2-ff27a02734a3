<template>
  <a-layout-header theme="light" class="header-menu">
    <div class="left">
      <div class="menu-tabs" ref="scrollRef" @wheel="onWheel">
        <Draggable
          v-model="appStore.menuTabs"
          :animation="200"
          item-key="menuId"
          ghost-class="tab-ghost"
          chosen-class="tab-chosen"
          @end="onDragEnd"
          style="display: flex"
        >
          <template #item="{ element: tab }">
            <div
              class="menu-tab"
              :class="{ active: tab.menuId === activeTabId }"
              :ref="setTabRef(tab.menuId)"
              @click="onTabClick(tab)"
              @contextmenu.prevent="onTabContextMenu($event, tab)"
            >
              {{ tab.menuName }}
              <CloseOutlined
                class="close-icon"
                style="margin-left: 5px; font-size: 12px"
                v-if="menuTabs.length > 1"
                @click.stop="onCloseTab(tab)"
              />
            </div>
          </template>
        </Draggable>
      </div>
    </div>
    <div class="right">
      <globalSearch></globalSearch>
      <a-tooltip placement="bottom">
        <template #title>
          <span>帮助文档</span>
        </template>
        <ReadFilled style="font-size: 18px; margin: 0 20px; padding-left: 7px" />
      </a-tooltip>
      <a-dropdown>
        <span class="span-icon">
          <i class="iconfont icon-yonghu" />
        </span>
        <template #overlay>
          <a-menu @click="onUserMenuClick">
            <a-menu-item key="1">个人中心</a-menu-item>
            <a-menu-item key="2">退出登录</a-menu-item>
          </a-menu>
        </template>
      </a-dropdown>
    </div>
    <!-- 右键菜单 -->
    <a-dropdown
      :visible="contextMenuVisible"
      :overlayStyle="{
        left: contextMenuX + 'px',
        top: contextMenuY + 'px',
        position: 'fixed',
        width: '120px',
        padding: '10px 0',
      }"
      @visibleChange="contextMenuVisible = false"
    >
      <template #overlay>
        <a-menu @click="onContextMenuClick">
          <a-menu-item key="refresh">刷新</a-menu-item>
          <a-menu-item key="refreshAll">刷新全部</a-menu-item>
          <a-menu-item key="close" :disabled="menuTabs.length === 1">关闭</a-menu-item>
          <a-menu-item key="closeLeft" :disabled="menuTabs.length === 1">关闭左侧</a-menu-item>
          <a-menu-item key="closeRight" :disabled="menuTabs.length === 1">关闭右侧</a-menu-item>
          <a-menu-item key="closeOthers" :disabled="menuTabs.length === 1">关闭其他</a-menu-item>
        </a-menu>
      </template>
    </a-dropdown>
  </a-layout-header>
</template>

<script setup lang="ts">
import { ref, watch, computed, nextTick } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useAppStore } from '@/stores/app'
import { useUserStore } from '@/stores/user'
import { jumpToUrl } from '@/layout/components/sidebars/helper'
import { CloseOutlined, ReadFilled } from '@ant-design/icons-vue'
import Draggable from 'vuedraggable'
import globalSearch from '@/layout/components/global-search/index.vue'

const router = useRouter()
const appStore = useAppStore()
const userStore = useUserStore()
const route = useRoute()

defineProps<{ menuItem: any | null }>()
const activeTabId = ref<string>('')
const scrollRef = ref<HTMLElement | null>(null)

// 右键菜单相关
const contextMenuVisible = ref(false)
const contextMenuX = ref(0)
const contextMenuY = ref(0)
const contextMenuTab = ref<any>(null)

const menuTabs = computed(() => {
  return appStore.menuTabs
})

const tabRefs = ref<Record<string, HTMLElement | null>>({})

function setTabRef(menuId: string): any {
  return (el: HTMLElement | null) => {
    tabRefs.value[menuId] = el
  }
}

watch(
  () => route,
  (val) => {
    if (val) {
      if (val?.meta) {
        const { menuId, isSecondary } = val.meta
        const { path } = val
        if (path === '/iframePage') {
          activeTabId.value = val.query.menuId as string
          return
        }
        if (!isSecondary) activeTabId.value = menuId as string
      }
    }
  },
  { immediate: true, deep: true },
)

// watch(activeTabId, (newId) => {
//   nextTick(() => {
//     const container = scrollRef.value
//     const tabEl = tabRefs.value[newId]
//     if (container && tabEl) {
//       // 计算 tab 相对 container 的左边距
//       const offsetLeft = tabEl.offsetLeft
//       const tabWidth = tabEl.offsetWidth
//       const containerWidth = container.clientWidth
//       // 让 tab 居中
//       const scrollTo = offsetLeft - (containerWidth - tabWidth) / 2
//       container.scrollTo({ left: scrollTo, behavior: 'smooth' })
//     }
//   })
// })
// 使用锚点定位特性自动滚动到对应tab
watch(
  activeTabId,
  (newId) => {
    nextTick(() => {
      const tabEl = tabRefs.value[newId]
      if (tabEl) {
        // 使用 scrollIntoView 锚点定位特性
        tabEl.scrollIntoView({
          behavior: 'smooth',
          block: 'nearest', // 垂直方向最近对齐
          inline: 'center', // 水平方向居中对齐
        })
      }
    })
  },
  { immediate: true },
)

const onTabClick = (tab: any) => {
  activeTabId.value = tab.menuId
  jumpToUrl(tab)
}

const onTabContextMenu = (e: MouseEvent, tab: any) => {
  contextMenuTab.value = tab
  contextMenuX.value = e.clientX
  contextMenuY.value = e.clientY
  contextMenuVisible.value = true
}

const onContextMenuClick = ({ key }: any) => {
  if (key === 'refresh') {
    // 刷新当前tab
    refreshTab(contextMenuTab.value)
    setTimeout(() => {
      router.replace({
        path: contextMenuTab.value.menuUrl,
      })
    }, 10)
  } else if (key === 'refreshAll') {
    // 刷新所有标签页
    appStore.setRouterShow(false)
    setTimeout(() => {
      appStore.setRouterShow(true)
    }, 200)
  } else if (key === 'close') {
    // 关闭当前tab
    const idx = menuTabs.value.findIndex((tab) => tab.menuId === contextMenuTab.value.menuId)
    if (idx !== -1) {
      menuTabs.value.splice(idx, 1)
      if (activeTabId.value === contextMenuTab.value.menuId && menuTabs.value.length) {
        onTabClick(menuTabs.value[0])
      }
    }
  } else if (key === 'closeLeft') {
    // 关闭左侧tab
    const idx = menuTabs.value.findIndex((tab) => tab.menuId === contextMenuTab.value.menuId)
    if (idx > 0) {
      menuTabs.value.splice(0, idx)
      if (!menuTabs.value.find((tab) => tab.menuId === activeTabId.value)) {
        onTabClick(menuTabs.value[0])
      }
    }
  } else if (key === 'closeRight') {
    // 关闭右侧tab
    const idx = menuTabs.value.findIndex((tab) => tab.menuId === contextMenuTab.value.menuId)
    if (idx !== -1 && idx < menuTabs.value.length - 1) {
      menuTabs.value.splice(idx + 1)
      if (!menuTabs.value.find((tab) => tab.menuId === activeTabId.value)) {
        onTabClick(menuTabs.value[menuTabs.value.length - 1])
      }
    }
  } else if (key === 'closeOthers') {
    // 关闭其他tab
    const idx = menuTabs.value.findIndex((tab) => tab.menuId === contextMenuTab.value.menuId)
    if (idx !== -1) {
      menuTabs.value.splice(0, idx)
      menuTabs.value.splice(1)
      activeTabId.value = contextMenuTab.value.menuId
    }
  }
  contextMenuVisible.value = false
}

// 添加刷新tab的方法
const refreshTab = async (tab: any) => {
  const route = tab.menuUrl
  appStore.removeCache(route)
  // 增加时间搓刷新路由缓存
  router.replace({
    path: contextMenuTab.value.menuUrl,
    query: {
      t: Date.now(),
    },
  })
  setTimeout(() => {
    appStore.addCache(route)
  }, 100)
}

const onUserMenuClick = (e: any) => {
  const { key } = e
  if (key === '1') {
    router.push('/userCenter')
  } else if (key === '2') {
    userStore.clearLoginData()
    router.push('/login')
  }
}

const onCloseTab = (tab: any) => {
  const idx = menuTabs.value.findIndex((t) => t.menuId === tab.menuId)
  if (idx !== -1) {
    menuTabs.value.splice(idx, 1)
    // 如果关闭的是当前激活tab，切换到第一个
    if (activeTabId.value === tab.menuId && menuTabs.value.length) {
      onTabClick(menuTabs.value[0])
    }
  }
}

const onDragEnd = (evt: any) => {
  // 这里 appStore.menuTabs 已自动更新，无需额外处理
  // 如需额外操作可在此添加
}

const onWheel = (e: WheelEvent) => {
  if (scrollRef.value) {
    e.preventDefault()
    const container = scrollRef.value
    const scrollAmount = e.deltaY * 0.5
    container.scrollLeft += scrollAmount
  }
}
</script>

<style scoped lang="less">
.header-menu {
  display: flex;
  align-items: center;
  background: #fff;
  height: 58px;
  border-bottom: 1px solid #eee;
  padding: 0 10px !important;
  line-height: normal;
  .iconfont {
    font-size: 16px;
  }
  .span-icon {
    padding: 0 7px;
    cursor: pointer;
  }
  .left {
    flex: 1;
    overflow: hidden;
    white-space: nowrap; /* 防止子元素换行 */
    .menu-tabs {
      display: flex; /* 让子元素不换行 */
      overflow-x: auto;
      .menu-tab {
        cursor: pointer;
        font-size: 14px;
        color: #344767;
        height: 35px;
        margin: 10.5px 5px;
        padding: 0 20px 0 13.5px; // 右侧预留关闭按钮空间
        transition: padding 0.3s;
        position: relative;
        display: flex;
        align-items: center;
        transition:
          background 0.3s,
          box-shadow 0.3s;

        &.active {
          background-color: #fff;
          box-shadow: 0 1px 5px 1px #ddd;
          border-radius: 10px;
        }
      }
    }
  }
  .right {
    flex: 1;
    height: 100%;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 0 38px;
  }
}
.menu-tab .close-icon {
  opacity: 0;
  overflow: hidden;
  transition: opacity 0.3s;
  margin-left: 5px;
  font-size: 12px;
  width: 13px;
}

.menu-tab:hover .close-icon,
.menu-tab.active .close-icon {
  opacity: 1;
}
</style>
