<template>
  <fc-designer ref="designer" height="100vh" :config="config" />
</template>
<script setup lang="ts">
import { ref } from 'vue'

const designer = ref()
const config = {
  showJsonPreview: false,
  showLanguage: false,
  hiddenMenu: ['subform', 'aide', 'layout'],
  hiddenItem: [
    'rate',
    'slider',
    'colorPicker',
    'tree',
    'elTransfer',
    'fcEditor',
    'stepForm',
    'fcValue',
    'tableForm',
    'nestedTableForm',
    'infiniteTableForm',
    'elAlert',
  ],
}
</script>
