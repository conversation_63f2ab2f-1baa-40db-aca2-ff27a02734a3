<template>
  <a-modal
    v-model:open="open"
    @ok="modalHandleOk"
    @cancel="handleCancel"
    title="已分配系统"
    width="800px"
    :confirm-loading="confirmLoading"
    class="assignedMenuModal"
  >
    <Table
      :columns="columns"
      :getData="getUserGroupSystemList"
      ref="tableRef"
      :searchFormState="formState"
    ></Table>
  </a-modal>
  <a-modal v-model:open="viewOpen" :title="title">
    <template #footer> </template>
    <tree-node :treeData="treeData" :fNames="fNames" :isMenus="false" :checkable="true"></tree-node>
  </a-modal>
</template>
<script lang="ts" setup>
import { Table } from '@fs/fs-components'
import { Button } from 'ant-design-vue'
import { assignedMenuDTO } from '@/utils/column'
import { defineProps, defineExpose, ref, watch, onBeforeMount, h } from 'vue'
import { getUserGroupSystemList, getUserGroupMenuList } from '@/api/services/permission'
const formRef = ref<HTMLFormElement | null>(null)
const confirmLoading = ref<boolean>(false)
const open = ref<boolean>(false)
const viewOpen = ref<boolean>(false)
const activeKey = ref('1')
const title = ref('')
let columns = ref<any[]>([])
const tableRef = ref<InstanceType<typeof Table> | null>(null)
const authorizationTableRef = ref<InstanceType<typeof Table> | null>(null)
const treeData = ref<any>([])
const props = defineProps({
  userGroupId: String,
})
const fNames = {
  children: 'datas',
  title: 'menuName',
  key: 'menuId',
}
const formState = ref<any>({
  userGroupId: undefined,
})

watch(
  () => props.userGroupId,
  (value) => {
    formState.value.userGroupId = value
    value && tableRef.value?.getTableData()
    value && authorizationTableRef.value?.getTableData()
  },
  { immediate: true },
)

onBeforeMount(() => {
  initColumns()
})

function getTreeLists(record: Record<string, any>) {
  const params = {
    userGroupId: record?.userGroupId,
    systemId: record?.systemId,
  }
  getUserGroupMenuList(params).then((result: any) => {
    if (result.code === '000000') {
      title.value = '分配可查询菜单'
      treeData.value = result.data
      viewOpen.value = true
    }
  })
  confirmLoading.value = false
}
function show() {
  formRef.value?.resetFields()
  activeKey.value = '1'
  open.value = true
}
const emits = defineEmits(['modalAssignedOk', 'modalAssignedCancel'])
function modalHandleOk() {
  confirmLoading.value = true
  open.value = false
  emits('modalAssignedOk')
  confirmLoading.value = false
}

function handleCancel() {
  emits('modalAssignedCancel')
  confirmLoading.value = false
}

async function initColumns() {
  columns.value = assignedMenuDTO.map((item) => {
    return {
      ...item,
      dataIndex: item.key,
    }
  })
  columns.value.push({
    key: 'action',
    title: '操作',
    customRender(data: Record<string, any>) {
      return [
        h(
          Button,
          {
            type: 'link',
            onClick() {
              confirmLoading.value = false
              operationHandle(data?.record)
            },
          },
          {
            default: () => '分配可查看菜单',
          },
        ),
      ]
    },
  })
}

function operationHandle(record: Record<string, any>) {
  getTreeLists(record)
}

defineExpose({ show })
</script>
<style lang="less">
.assignedMenuModal {
  .filter-box {
    .ant-space {
      .ant-space-item:last-child {
        display: none;
      }
    }
  }
}
</style>
