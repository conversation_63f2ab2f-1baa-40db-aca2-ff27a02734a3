<template>
  <a-modal
    v-model:open="open"
    :title="!isEdit ? '新增用户' : '编辑用户'"
    width="800px"
    :mask-closable="false"
    :confirm-loading="loading"
    @cancel="open = false"
    @ok="onSubmit"
  >
    <a-spin :spinning="spinning">
      <a-form
        class="section-form"
        ref="formRef"
        layout="vertical"
        name="form"
        :model="formState"
        :rules="rules"
      >
        <div class="form-row">
          <a-form-item v-if="isEdit" label="用户编号">
            <a-input :value="formState.userId" :disabled="isEdit"></a-input>
          </a-form-item>
          <a-form-item label="用户名称" name="userName">
            <a-input v-model:value.trim="formState.userName" placeholder="请输入"> </a-input>
          </a-form-item>
          <a-form-item v-if="!isEdit" label="用户BU" name="userBuId">
            <a-input v-model:value.trim="formState.userBuId" placeholder="请输入" disabled>
            </a-input>
          </a-form-item>
          <a-form-item label="登录账号" name="loginAccount">
            <a-input
              v-model:value.trim="formState.loginAccount"
              placeholder="请输入"
              :disabled="isEdit"
            >
            </a-input>
          </a-form-item>
          <a-form-item v-if="!isEdit" label="密码">
            <a-input
              value=""
              placeholder="登录默认密码在基本配置>系统配置管理页面查看"
              disabled
              title="登录默认密码在基本配置>系统配置管理页面查看"
            ></a-input>
          </a-form-item>
          <a-form-item label="用户状态" name="userStatus">
            <a-select
              v-model:value="formState.userStatus"
              :options="userStatusOptions"
              placeholder="请选择"
            ></a-select>
          </a-form-item>
          <a-form-item label="用户类型" name="userType">
            <a-select
              v-model:value="formState.userType"
              :options="userTypeOptions"
              placeholder="请选择"
              :disabled="isEdit"
            ></a-select>
          </a-form-item>
          <a-form-item label="用户手机号" name="mobile">
            <a-input v-model:value.trim="formState.mobile" placeholder="请输入"> </a-input>
          </a-form-item>
          <a-form-item label="分配角色" name="roleList">
            <a-select
              v-model:value="formState.roleList"
              :options="roleListOptions"
              :field-names="{ label: 'roleName', value: 'roleId' }"
              mode="multiple"
              placeholder="请选择"
            ></a-select>
          </a-form-item>
          <a-form-item label="可查看部门" name="viewDeptList" v-if="isHaveDept">
            <a-cascader
              v-model:value="formState.viewDeptList"
              :field-names="{ label: 'deptName', value: 'deptId', children: 'deptList' }"
              :options="viewDeptListOptions"
              multiple
              placeholder="请选择可查看部门"
              :show-checked-strategy="Cascader.SHOW_CHILD"
            ></a-cascader>
          </a-form-item>
          <a-form-item label="可查看用户组" name="userGroupList" v-if="isHaveUserGroup">
            <a-select
              v-model:value="formState.userGroupList"
              :options="userGroupListOptions"
              :field-names="{ label: 'userGroupName', value: 'userGroupId' }"
              mode="multiple"
              placeholder="请选择"
            ></a-select>
          </a-form-item>
          <a-form-item label="账号到期日" name="accountEndDate">
            <a-date-picker
              style="width: 100%"
              v-model:value="formState.accountEndDate"
              :format="dateFormat"
              :value-format="dateFormat"
            ></a-date-picker>
          </a-form-item>
          <a-form-item label="备注" name="remark">
            <a-input v-model:value.trim="formState.remark" placeholder="请输入"> </a-input>
          </a-form-item>
        </div>
        <div class="form-row" v-if="isHaveFileServer">
          <a-form-item label="头像" name="avatarUrl">
            <a-upload
              v-model:file-list="fileList"
              :headers="{
                Systemcode: AUTHORITYCENTER,
                Tenantid: userInfo.tenantId,
                Opuser: userInfo.userId,
                Authorization: token,
                Buid: userInfo.buId,
              }"
              name="file"
              list-type="picture-card"
              class="avatar-uploader"
              :show-upload-list="false"
              :action="uploadFile"
              :before-upload="beforeUpload"
              @change="handleUploadChange"
            >
              <div v-if="formState.avatarUrl" class="uploadImg">
                <img :src="formState.avatarUrl" alt="avatar" />
              </div>
              <div v-else>
                <LoadingOutlined v-if="uploadLoading" />
                <PlusOutlined v-else />
                <div class="ant-upload-text">上传</div>
              </div>
            </a-upload>
          </a-form-item>
        </div>
        <a-divider>员工信息</a-divider>
        <div class="form-row">
          <a-form-item label="工号" name="employeeCode">
            <a-input v-model:value.trim="formState.employeeCode" placeholder="请输入"> </a-input>
          </a-form-item>
          <a-form-item label="部门" name="deptId" v-if="isHaveDept">
            <a-cascader
              v-model:value="formState.deptId"
              :field-names="{ label: 'deptName', value: 'deptId', children: 'deptList' }"
              :options="viewDeptListOptions"
              expand-trigger="hover"
              change-on-select
              placeholder="请选择部门"
            ></a-cascader>
          </a-form-item>
          <a-form-item label="职务" name="positionId" v-if="isHavePost">
            <a-select
              v-model:value="formState.positionId"
              :options="positionListOptions"
              :field-names="{ label: 'positionName', value: 'positionId' }"
              placeholder="请选择"
            ></a-select>
          </a-form-item>
          <a-form-item label="员工状态" name="employeeStatus">
            <a-select
              v-model:value="formState.employeeStatus"
              :options="employeeStatusOptions"
              placeholder="请选择"
            ></a-select>
          </a-form-item>
          <a-form-item label="入职日期" name="entryDate">
            <a-date-picker
              style="width: 100%"
              v-model:value="formState.entryDate"
              :format="dateFormat"
              :value-format="dateFormat"
            ></a-date-picker>
          </a-form-item>
          <a-form-item label="离职日期" name="leaveDate">
            <a-date-picker
              style="width: 100%"
              v-model:value="formState.leaveDate"
              :format="dateFormat"
              :value-format="dateFormat"
            ></a-date-picker>
          </a-form-item>
          <a-form-item label="岗位" name="jobId" v-if="isHaveJob">
            <a-select
              v-model:value="formState.jobId"
              :field-names="{ label: 'jobName', value: 'jobId' }"
              :options="jobListOptions"
              expand-trigger="hover"
              change-on-select
              placeholder="请选择岗位"
            ></a-select>
          </a-form-item>
          <a-form-item label="安全等级" name="securityLevel">
            <a-input-number
              style="width: 100%"
              v-model:value="formState.securityLevel"
              :min="0"
              placeholder="请输入"
            ></a-input-number>
          </a-form-item>
        </div>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, onBeforeMount, computed } from 'vue'
import type { Rule } from 'ant-design-vue/es/form'
import { useUserStore } from '@/stores/user'
import { storeToRefs } from 'pinia'
import { AUTHORITYCENTER } from '@/api/base'

import {
  getRoleList,
  getUserGroupList,
  getJobList,
  uploadFile,
  getPositionList,
  addUser,
  viewUserDetail,
  updateUser,
  getSystemConfig,
} from '@/api/services/permission'
import {
  Cascader,
  message,
  type CascaderProps,
  type UploadProps,
  type UploadChangeParam,
} from 'ant-design-vue'
import { PlusOutlined, LoadingOutlined } from '@ant-design/icons-vue'
import { getDeptListFn } from './common'
export interface AddUserModalProps {
  type: 'add' | 'edit'
  userId: string
}

const dateFormat = 'YYYY-MM-DD'

/**
 * 用户状态
 */
const userStatusOptions = [
  { label: '正常', value: '1' },
  { label: '停用', value: '2' },
  { label: '锁定', value: '3' },
]

/**
 * 员工状态
 */
const employeeStatusOptions = [
  { label: '在职', value: '1' },
  { label: '离职', value: '2' },
]

/**
 * 用户类型
 */
const userTypeOptions = [
  { label: '系统管理员角色', value: '1', disabled: true },
  { label: '租户默认角色', value: '2', disabled: true },
  { label: 'BU管理', value: '3', disabled: true },
  { label: '普通角色', value: '4' },
]

const open = defineModel<boolean>({ default: false, required: true })
const props = withDefaults(defineProps<AddUserModalProps>(), {
  type: 'add',
  userId: '',
})
const emits = defineEmits(['submit'])
const isEdit = computed(() => props.type === 'edit')

const userStore = useUserStore()
const { userInfo, token } = storeToRefs(userStore)
const loading = ref(false)
const formRef = ref()
const formState = ref<Record<string, any>>({
  userName: '',
  userBuId: userInfo.value.buId,
  loginAccount: '',
  userStatus: null,
  userType: null,
  mobile: '',
  roleList: [],
  viewDeptList: [],
  userGroupList: [],
  accountEndDate: '',
  remark: '',
  avatarUrl: '',
  employeeCode: '',
  deptId: '',
  positionId: null,
  employeeStatus: null,
  entryDate: '',
  leaveDate: '',
  jobId: null,
  securityLevel: null,
})

const rules: Record<string, Rule[]> = {
  userName: [{ required: true, message: '请输入', trigger: 'change' }],
  userBuId: [{ required: true, message: '请输入', trigger: 'change' }],
  loginAccount: [{ required: true, message: '请输入', trigger: 'change' }],
  userStatus: [{ required: true, message: '请输入', trigger: 'change' }],
  // deptId: [{ required: true, message: '请选择', trigger: 'change' }],
}

const roleListOptions = ref([]) // 角色列表
const viewDeptListOptions = ref<CascaderProps['options']>([]) // 可查看部门
const jobListOptions = ref<any>([]) // 岗位列表
const userGroupListOptions = ref([]) // 用户组列表
const positionListOptions = ref([]) // 职务管理列表

const uploadLoading = ref(false)
const fileList = ref<UploadProps['fileList']>([])

const spinning = ref(false)
const userDetail = ref()

const isHaveDept = ref(0) //是否有组织
const isHavePost = ref(0) //是否有职位
const isHaveJob = ref(0) //是否有岗位
const isHaveUserGroup = ref(0) //是否有用户组
const isHaveFileServer = ref(0) //是否有Fftp上传

const viewDeptListOptionsMap = computed(() => {
  return isEdit.value
    ? buildOptionsMap(viewDeptListOptions.value as any, ['deptId', 'deptList'])
    : {}
})

onBeforeMount(() => {
  initOptions()
})

/**
 * 初始化选项数据
 */
async function initOptions() {
  try {
    spinning.value = true
    const configResponse = await getSystemConfig()
    isHaveDept.value = configResponse.data?.isHaveDept
    isHavePost.value = configResponse.data?.isHavePost
    isHaveJob.value = configResponse.data?.isHaveJob
    isHaveUserGroup.value = configResponse.data?.isHaveUserGroup
    isHaveFileServer.value = configResponse.data?.isHaveFileServer

    const requests = [
      getRoleListFn(),
      isHaveDept.value && getDeptLists(),
      isHaveUserGroup.value && getUserGroupListFn(),
      isHavePost.value && getPositionListFn(),
      isHaveJob.value && getJobListFn(),
    ]

    await Promise.all(requests)

    if (isEdit.value && props.userId) {
      await viewUserDetailFn()
    }
  } finally {
    spinning.value = false
  }
}

/**
 * 获取用户详情数据
 */
async function viewUserDetailFn() {
  try {
    const { data } = await viewUserDetail(props.userId)
    if (Array.isArray(data)) {
      userDetail.value = data[0]
      console.log(userDetail.value)
      initFormState(data[0])
    }
  } catch (error) {
    console.log(error)
  }
}

/**
 * 初始化编辑数据
 */
function initFormState({ roleList, viewDeptList, userGroupList, deptId, ...other }: any) {
  formState.value = { ...formState.value, ...other }
  formState.value.userBuId = userInfo.value.buId
  formState.value.roleList = roleList.map(({ roleId }: { roleId: string }) => roleId)
  formState.value.viewDeptList = viewDeptList.map(({ deptId }: { deptId: string }) => {
    return viewDeptListOptionsMap.value[deptId]
  })
  formState.value.userGroupList = userGroupList.map(
    ({ userGroupId }: { userGroupId: string }) => userGroupId,
  )
  formState.value.deptId = viewDeptListOptionsMap.value[deptId]
}

/**
 * 构建选项映射
 */
function buildOptionsMap(tree: any[], map: string[] = ['id', 'children']) {
  const [id, children] = map
  const pathsMap: Record<string, string[]> = {}
  const findPaths = (tree: any[], parentPath: string[] = []) => {
    tree.forEach((item) => {
      const newPath: string[] = [...parentPath, item[id]]
      pathsMap[item[id]] = newPath
      if (item[children]?.length) {
        findPaths(item[children], newPath)
      }
    })
  }
  findPaths(tree)
  return pathsMap
}

/**
 * 表单提交
 */
async function onSubmit() {
  formRef.value
    .validate()
    .then(() => {
      isEdit.value ? updateUserFn() : addUserFn()
    })
    .catch((error: any) => {
      console.log('error', error)
    })
}

/**
 * 获取角色列表
 */
async function getRoleListFn() {
  try {
    const { data }: { data: any } = await getRoleList({
      pageIndex: 1,
      pageSize: 999,
    })
    if (Array.isArray(data?.records)) {
      roleListOptions.value = data.records
    }
  } catch (error) {
    console.log(error)
  }
}

/**
 * 获取角色列表
 */
async function getJobListFn() {
  try {
    const { data }: { data: any } = await getJobList({
      isUse: 1,
      buId: userInfo.value.buId,
      pageIndex: 1,
      pageSize: 999,
    })
    if (Array.isArray(data?.records)) {
      jobListOptions.value = data.records
    }
  } catch (error) {
    console.log(error)
  }
}

/**
 * 获取部门数据
 */
async function getDeptLists() {
  getDeptListFn().then((res) => {
    viewDeptListOptions.value = res
  })
}

/**
 * 职务管理列表
 */
async function getPositionListFn() {
  try {
    const { data }: { data: any } = await getPositionList({
      isUse: 1,
      pageIndex: 1,
      pageSize: 999,
    })
    if (Array.isArray(data?.records)) {
      positionListOptions.value = data.records
    }
  } catch (error) {
    console.log(error)
  }
}

/**
 * 获取用户组列表
 */
async function getUserGroupListFn() {
  try {
    const { data } = await getUserGroupList({
      isUse: 1,
      buId: userInfo.value.buId,
      tenantId: userInfo.value.tenantId,
      userGroupName: '',
      pageSize: 999,
      pageIndex: 1,
    })
    if (Array.isArray(data?.records)) {
      userGroupListOptions.value = data.records
    }
  } catch (error) {
    console.log(error)
  }
}

/**
 * 新增用户
 */
async function addUserFn() {
  try {
    loading.value = true
    const { roleList, viewDeptList, userGroupList, deptId, loginAccount, ...other } =
      formState.value
    const { roleType } = userInfo.value
    const data = {
      ...other,
      loginAccount:
        roleType == 2
          ? `${loginAccount}@${userInfo.value?.loginAccount.split('@')[1]}`
          : loginAccount,
      roleList: roleList.map((roleId: string) => ({ roleId })),
      viewDeptList: viewDeptList.map((dept: string[]) => ({ deptId: dept[dept.length - 1] })),
      userGroupList: userGroupList.map((userGroupId: string) => ({ userGroupId })),
      deptId: deptId && deptId.length > 0 ? deptId[deptId.length - 1] : '',
    }
    await addUser(data)
    open.value = false
    emits('submit')
  } catch (error) {
    console.log(error)
  } finally {
    loading.value = false
  }
}

/**
 * 更新用户数据
 */
async function updateUserFn() {
  try {
    loading.value = true
    const { deptId, roleList, viewDeptList, userGroupList, ...other } = formState.value
    const deptIdToUse = deptId && deptId.length > 0 ? deptId[deptId.length - 1] : ''

    const data = {
      ...other,
      deptId: deptIdToUse,
      roleList: roleList.map((roleId: any) => ({
        roleId,
        autoId: generateAutoIdList(userDetail.value?.roleList, 'roleId', roleId),
      })),
      viewDeptList: viewDeptList.map((dept: string | any[]) => ({
        deptId: dept[dept.length - 1],
        autoId: generateAutoIdList(userDetail.value?.viewDeptList, 'deptId', dept[dept.length - 1]),
      })),
      userGroupList: userGroupList.map((userGroupId: any) => ({
        userGroupId,
        autoId: generateAutoIdList(userDetail.value?.userGroupList, 'userGroupId', userGroupId),
      })),
    }
    await updateUser(data)
    open.value = false
    emits('submit')
  } catch (error) {
    console.log(error)
  } finally {
    loading.value = false
  }
}

function generateAutoIdList(itemList: any[], id: string, targetId: any) {
  if (itemList && itemList.length > 0) {
    return itemList
      .map((item) => {
        if (item[id] === targetId) {
          return item.autoId
        }
      })
      .join('')
  } else {
    return generateRandomString(24)
  }
}

function generateRandomString(length: number) {
  var characters = 'abcdefghijklmnopqrstuvwxyz0123456789'
  var result = ''
  var charactersLength = characters.length
  for (var i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * charactersLength))
  }
  return result
}

const beforeUpload: UploadProps['beforeUpload'] = (file) => {
  const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png'
  if (!isJpgOrPng) {
    message.error('You can only upload JPG file!')
  }
  const isLt2M = file.size / 1024 / 1024 < 2
  if (!isLt2M) {
    message.error('Image must smaller than 2MB!')
  }
  return isJpgOrPng && isLt2M
}

const handleUploadChange = (info: UploadChangeParam) => {
  const { status, response } = info.file
  if (status === 'uploading') {
    uploadLoading.value = true
    return
  }
  if (status === 'done') {
    if (response.code === '000000') {
      formState.value.avatarUrl = response.data
    } else {
      response.msg && message.info(response.msg)
    }
    uploadLoading.value = false
  }
  if (status === 'error') {
    uploadLoading.value = false
    message.info('上传失败')
  }
}
</script>

<style scoped lang="less">
.section-form {
  padding: 20px 0;

  .form-row {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 10px;
    .uploadImg {
      width: 100px;
      height: 100px;
      img {
        width: 100%;
        height: 100%;
      }
    }
  }
}
</style>
