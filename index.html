<!doctype html>
<html lang="zh-<PERSON>">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" href="/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title></title>
    <script>
      function checkBrowserVersion() {
        const checkVersion = (browser, minVersion) => {
          var userAgent = navigator.userAgent
          if (userAgent.includes(browser)) {
            const regex = new RegExp(browser + '(\\d+)')
            var version = parseInt(userAgent.match(regex)[1])
            if (version < minVersion) {
              window.location.href = '/upgrade-browser.html'
            }
          }
        }
        checkVersion('Chrome/', 87)
        checkVersion('Firefox/', 78)
        checkVersion('Version/', 14)
        checkVersion('Edg/', 88)
      }
      checkBrowserVersion()
    </script>
  </head>
  <body>
    <div id="app"></div>
    <script type="module" src="/src/main.ts"></script>
    <script src="/env.js"></script>
  </body>
</html>
