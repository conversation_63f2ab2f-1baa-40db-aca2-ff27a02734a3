<template>
  <div class="data-lineage-diagram">
    <div id="container" class="g6-container"></div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { useRoute } from 'vue-router'
import { initG6, type DataType } from './graph'
import { getTableLineage } from '@/api/assetmanager/metadatamanage/metadatamanage'

const route = useRoute()
const loadingRef = ref<boolean>(false)

const fetchTableLineage = async (tableName: string, datasourceId: string, databaseName: string) => {
  try {
    const response = await getTableLineage({ tableName, datasourceId, databaseName }, {})
    const data = response.data as unknown as Array<DataType>

    return data
  } catch (error) {
    console.error('Error fetching table detail:', error)
  }
  return []
}

onMounted(async () => {
  loadingRef.value = true
  try {
    const tableName = route.query.tableName as string
    const datasourceId = route.query.datasourceId as string
    const databaseName = (route.query.databaseName as string) || ''
    const response = await fetchTableLineage(tableName, datasourceId, databaseName)

    initG6(response)
  } catch (error) {
    console.error('Error fetching lineage data:', error)
  } finally {
    loadingRef.value = false
  }
})
</script>

<style lang="less" scoped>
.data-lineage-diagram {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

#container {
  position: relative;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  max-height: 100%;
  overflow: hidden;
  background-color: #fff;
  border: 1px solid rgba(5, 5, 5, 0.06);
}

:deep(.g6-grid-container) {
  z-index: 0 !important;
}

:deep(canvas) {
  position: relative;
  z-index: 1;
}

:deep(.minimap) {
  position: absolute;
  top: 0;
  right: 0;
  z-index: 10;
  background: rgba(0, 0, 0, 0.04);
}
</style>
