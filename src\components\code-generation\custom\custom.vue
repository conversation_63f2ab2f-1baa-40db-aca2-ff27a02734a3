<template>
  <div class="content-custom">
    <div class="content-steps">
      <a-steps
        direction="vertical"
        :current="current"
        :items="tabList"
        style="height: 100%"
      ></a-steps>
    </div>
    <div v-if="current === 1" style="width: 100%">
      <v-ace-editor
        v-model:value="codeJson"
        lang="javascript"
        theme="chrome"
        style="width: 100%; height: 500px"
        :readonly="true"
      />
    </div>
    <template v-if="current === 0">
      <div>
        <v-ace-editor
          v-model:value="jsonInput"
          lang="json"
          theme="chrome"
          style="width: 400px; height: 500px"
          :options="{ useWorker: true }"
          :readonly="true"
        />
      </div>

      <div style="width: 100%; height: 500px; overflow-y: auto; overflow-x: hidden">
        <Form
          :items="items"
          ref="FormRefs"
          v-model="formData"
          :extral="{}"
          @submit="submitHandle"
          @change="changeHandle"
          :layout="layout"
          v-if="resetState"
        >
          <template v-slot:[val]="{ item }" v-for="(val, index) in slotList" :key="index">
            <a-form-item
              :label="item.label"
              :wrapperCol="{ span: 18 }"
              :labelCol="{ span: 6 }"
              v-if="item.type === 'requestApi'"
            >
              <requestApi
                :projectList="projectList"
                :defaultData="getDefaultData(item)"
                @request="requestHandle($event, item)"
              ></requestApi>
            </a-form-item>
            <a-form-item
              :wrapperCol="{ span: 18 }"
              :labelCol="{ span: 6 }"
              :label="item.label"
              v-else-if="item.type === 'columnsApi'"
            >
              <columnsApi :data="item.value" @change="columnsChange($event, item)"></columnsApi>
            </a-form-item>
          </template>
        </Form>
      </div>
    </template>

    <div class="content-footer">
      <a-button @click="curtHandle" v-show="current > 0" style="margin-right: 16px"
        >上一步</a-button
      >
      <a-button v-if="current === 0" @click="nextHandle" style="margin-right: 16px" type="primary"
        >下一步</a-button
      >
      <a-button @click="saveDraft" style="margin-right: 16px">保存草稿</a-button>
      <a-button v-if="current === 1" @click="generateCode" type="primary">生成代码</a-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, nextTick } from 'vue'
import { Form } from '@fs/fs-components'
import { debounce } from '@/utils'
import { getCustomTemplate, generationCoustomTemplate, getdraft, getData } from '../code-generation'

import { VAceEditor } from 'vue3-ace-editor'
import 'ace-builds/src-noconflict/mode-json' // Load the language definition file used below
import 'ace-builds/src-noconflict/theme-chrome' // Load the theme definition file used below
import ace from 'ace-builds'
import workerJavascript from 'ace-builds/src-noconflict/worker-json?url' // For vite
ace.config.setModuleUrl('ace/mode/json_worker', workerJavascript)

import requestApi from './request-api.vue'
import columnsApi from './columns-api.vue'

interface customProps {
  customType: string
  routerPath: string
}

const props: customProps = withDefaults(defineProps<customProps>(), {})

const jsonInput = ref('')
const items = ref<any[]>([])
const formData = ref<any>({})
const FormRefs = ref()
const dataList = ref<any[]>([])
const codeJson = ref('')
const tabList = ref([
  {
    title: '配置文件',
    value: 0,
  },
  {
    title: '生成代码',
    value: 2,
  },
])
const current = ref(0)
const layout = ref({
  layout: 'horizontal',
  labelCol: { span: 6 },
  wrapperCol: { span: 18 },
})
const resetState = ref(true)
const projectList = ref<any[]>([])
const slotList = ref<any>([])

function submitHandle(data: any) {
  console.log('%c 🍩 data: ', 'font-size:12px;background-color: #ED9EC7;color:#fff;', data)
}

const emit = defineEmits(['saveDraft', 'generateCode'])

function submit() {
  FormRefs.value.submit()
}

function reset() {
  formData.value = {}
  FormRefs.value.reset()
}

function blurHandle() {
  try {
    resetState.value = false
    // let result = eval(jsonInput.value)
    // const columnsData = result.find((item: { key: string }) => item.key === 'getColumns') || []
    // if (columnsData) {
    //   items.value = columnsData.value[0].value
    //   console.log(
    //     '%c 🌽 items.value: ',
    //     'font-size:12px;background-color: #93C0A4;color:#fff;',
    //     items.value,
    //   )
    // }
    nextTick(() => {
      resetState.value = true
    })
  } catch (error) {
    // this.jsonOutput = '无效的 JSON 格式'
    console.error('无效的 JSON 格式', error)
    resetState.value = true
  }
}

function saveDraft(state = true) {
  emit('saveDraft', { content: jsonInput.value, pageType: 'custom', type: props.customType }, state)
}

async function generateCode() {
  try {
    emit('generateCode', { content: codeJson.value })
    saveDraft(false)
  } catch (error) {
    console.log('%c 🍓 error: ', 'font-size:12px;background-color: #3F7CFF;color:#fff;', error)
  }
}

function getItemsHandle(list: any[]) {
  list.forEach((element: any) => {
    if (element.type) {
      const obj = {
        ...element,
        name: `${element.name}`,
        span: 20,
      }
      if (['requestApi', 'columnsApi'].includes(element.type)) {
        obj.slot = true
        slotList.value.push(element.name)
      }
      items.value.push(obj)
    }
    if (element.value) {
      element.value.forEach((val: any) => {
        if (val.type) {
          const obj2 = {
            ...val,
            name: `${element.name}-${val.name}`,
            span: 20,
          }
          if (['requestApi', 'columnsApi'].includes(val.type)) {
            obj2.slot = true
            slotList.value.push(`${element.name}-${val.name}`)
          }
          items.value.push(obj2)
          const value = val.valueType === 'array' ? JSON.stringify(val.value) : val.value
          formData.value[`${element.name}-${val.name}`] = value
        }
      })
    }
    console.log(
      '%c 🍎  items.value: ',
      'font-size:12px;background-color: #93C0A4;color:#fff;',
      items.value,
    )
  })
}

function changeHandle({ name, value }: { name: string; value: string }) {
  console.log('%c 🍔 { name, value }: ', 'font-size:12px;background-color: #B03734;color:#fff;', {
    name,
    value,
  })
  const arr = name.split('-')
  const [key1, key2] = arr
  const moudle = dataList.value.find((item: any) => item.name === key1)
  if (moudle) {
    const item = moudle.value.find((item: any) => item.name === key2)
    item.value = item.valueType === 'array' ? JSON.parse(value) : value
  }
  jsonInput.value = JSON.stringify(dataList.value, null, 2)
}

function requestHandle(data: any, item: any) {
  const interfaceValue = item.value.find((val: { name: string }) => val.name === 'interface')
  if (interfaceValue) {
    interfaceValue.value = data.interface
  }
  const interfaceImportValue = item.value.find(
    (val: { name: string }) => val.name === 'interfaceImport',
  )
  if (interfaceImportValue) {
    interfaceImportValue.value = `import { ${data.interface} } from '@/api/${data.project}/${data.module}'`
  }
  item.defaults = data
  jsonInput.value = JSON.stringify(dataList.value, null, 2)
}

function getDefaultData(item: any) {
  try {
    const interfaceValue = item.value.find((val: { name: string }) => val.name === 'interface')
    const interfaceImportValue = item.value.find(
      (val: { name: string }) => val.name === 'interfaceImport',
    )
    const pathSegments = extractPathSegments(interfaceImportValue.value)
    const obj = {
      project: pathSegments[0],
      module: pathSegments[1],
      interface: interfaceValue.value,
    }
    return obj
  } catch (error) {
    console.error('获取接口数据失败 ' + error)
    return {}
  }
}

function extractPathSegments(importString: string) {
  const regex = /from\s+['"]@\/api\/([^'"]+)['"]/
  const match = importString.match(regex)

  // 如果匹配成功，将路径拆分成数组
  if (match) {
    // 拆分路径并返回为数组
    return match[1].split('/')
  }
  return []
}

function curtHandle() {
  current.value--
}

async function nextHandle() {
  current.value++
  const { data } = await generationCoustomTemplate({ list: dataList.value, type: props.customType })
  codeJson.value = data.data
}

function columnsChange(data: any, item: any) {
  console.log('%c 🍱 item: ', 'font-size:12px;background-color: #F5CE50;color:#fff;', item)
  console.log('%c 🦀 data: ', 'font-size:12px;background-color: #42b983;color:#fff;', data)
  jsonInput.value = JSON.stringify(dataList.value, null, 2)
}

onMounted(async () => {
  const { data } = await getdraft({ name: props.routerPath })
  const { content = '', pageType = '', type = '' } = data.data || {}

  // 类型为自定义模板且类型相同时 启用草稿数据
  if (data?.data && pageType === 'custom' && props.customType === type) {
    // 草稿箱为自定义模板并且为表格时
    const list = JSON.parse(content)
    dataList.value = list
    jsonInput.value = content
    getItemsHandle(list)
  } else {
    const { data } = await getCustomTemplate({ type: props.customType })
    jsonInput.value = JSON.stringify(data.data, null, 2)
    const list = data.data
    dataList.value = list
    getItemsHandle(list)
  }
  const res = await getData()
  if (res?.data?.code === '000000') {
    projectList.value = res?.data?.data ?? []
  }
})
</script>

<style scoped lang="less">
/* Styles for 404 page */
.content-custom {
  position: relative;
  width: 100%;
  padding-top: 30px;
  display: flex;
  padding-bottom: 70px;
  .content-steps {
    height: 400px;
    width: 150px;
    flex-shrink: 0;
  }
  .button-group {
    text-align: right;
    .submit-button {
      margin-right: 20px;
    }
  }
  .content-footer {
    width: 100%;
    position: absolute;
    bottom: 15px;
    text-align: center;
    background: white;
    left: 0;
  }
}
</style>
