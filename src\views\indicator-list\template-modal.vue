<template>
  <div>
    <a-button type="primary" @click="showModal">新增</a-button>
    <a-modal
      v-model:open="visible"
      title="选择指标模板"
      @ok="handleOk"
      @cancel="handleCancel"
      width="800px"
    >
      <a-table
        :loading="loading"
        :columns="columns"
        :data-source="dataSource"
        :row-key="'formId'"
        :row-selection="{
          type: 'radio',
          selectedRowKeys,
          onChange: onSelectChange,
        }"
        :pagination="false"
        class="indicator-table"
        emptyText="暂无数据，请先创建指标模板"
      />
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, h } from 'vue'
import { useRouter } from 'vue-router'
import { message, type TableColumnsType, Tag } from 'ant-design-vue'
import { getIndexFormDataListByPage } from '@/api/indexmanage/indexform/indexform'

const router = useRouter()
const visible = ref<boolean>(false)
const loading = ref<boolean>(true)

// 表格列定义
const columns: TableColumnsType = [
  {
    title: '模板编号',
    dataIndex: 'formCode',
    key: 'formCode',
    fixed: 'left',
    width: 40,
  },
  {
    title: '模板名称',
    dataIndex: 'formName',
    key: 'formName',
    width: 80,
  },
  {
    title: '是否已发布',
    dataIndex: 'isRelease',
    key: 'isRelease',
    width: 100,
    customRender: ({ text }) =>
      h(Tag, { color: text === 1 ? 'green' : 'red' }, () => (text === 1 ? '是' : '否')),
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 40,
    customRender: ({ text }: any) => (text === 1 ? '已提交' : '编辑中'),
  },
  {
    title: '备注',
    dataIndex: 'remarks',
    key: 'remarks',
    width: 60,
  },

  {
    title: '当前版本号',
    dataIndex: 'currentVersion',
    key: 'currentVersion',
    width: 40,
  },
]

// 模拟数据
const dataSource = ref<any[]>([])

// 选择配置
const selectedRowKeys = ref<string[]>([])

// 选择变化处理
const onSelectChange = (keys: string[]) => {
  selectedRowKeys.value = keys
}

// 显示对话框
const showModal = () => {
  visible.value = true
}

watch(visible, (newVal) => {
  if (newVal) {
    fetchData()
  }
})

// 确认按钮处理
const handleOk = () => {
  if (!selectedRowKeys.value.length) return message.error('请选择指标模板')
  const dt = dataSource.value.find((item) => item.formId === selectedRowKeys.value[0])
  router.push(
    `/indicator-list/create?formId=${selectedRowKeys.value[0]}&businessType=${dt.businessType}`,
  )
  visible.value = false
}

// 取消按钮处理
const handleCancel = () => {
  visible.value = false
}

async function fetchData() {
  try {
    loading.value = true
    const {
      data: { records },
    } = await getIndexFormDataListByPage({
      pageIndex: 1,
      pageSize: 100,
      isRelease: 1,
    })
    dataSource.value = records
  } catch (error) {
    console.error('Error fetching data:', error)
    message.error('获取指标模板列表失败')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.ant-modal-body {
  max-height: 500px;
  overflow-y: auto;
}

.indicator-table {
  :deep(.ant-table-thead > tr > th) {
    white-space: nowrap;
  }

  :deep(.ant-table-tbody > tr > td) {
    white-space: nowrap;
  }
}
</style>
