[{"tableName": "user", "databaseName": "public", "fields": [{"fieldName": "ID", "alias": "用户ID", "selected": true}], "joins": [{"joinTableName": "user_info", "joinType": "left,inner,right", "joinFields": [{"fieldName": "ID", "alias": "用户ID", "selected": true}, {"fieldName": "name", "alias": "用户名称"}], "joinConditions": [{"mainTableName": "user", "mainFieldName": "user_id", "operator": "=,<,>,>=,<=,!=", "joinTableName": "user_info", "joinFieldName": "user_id"}]}], "customField": [{"expression": "CONCAT(user.user_id, '---', user.user_name)", "alias": "表达式别名"}], "where": [{"tableName": "user", "fieldName": "user_id", "operator": "=,!=,>,<,BETWEEN,>=,<=,IS_NULL,NOT_NULL,contains,not_contains,start_with,end_with,is_true,is_false", "minValue": "仅限于BETWEEN", "maxValue": "100", "value": "100", "fieldType": ""}], "summary": [{"tableName": "user", "fieldName": "user_id", "function": "count"}, {"tableName": "user", "fieldName": "user_id", "function": "count,sum,avg,max,min,distinct_count"}], "group": [{"tableName": "user", "fieldName": "user_id"}], "order": [{"tableName": "user", "fieldName": "user_id", "sort": "asc,desc"}], "limitValue": 100}]