<template>
  <a-table
    :dataSource="list"
    :columns="columns"
    :pagination="false"
    size="small"
    :scroll="{ x: 400, y: 300 }"
  >
    <template #bodyCell="{ column, record }">
      <template v-if="column.key === 'description'">
        {{ record[column.key] ? record[column.key] : '暂无' }}
      </template>
      <template v-if="column.key === 'required'">
        <span :style="{ color: record[column.key] ? 'red' : '' }">{{
          record[column.key] ? '是' : '否'
        }}</span>
      </template>
    </template>
  </a-table>
</template>

<script setup lang="ts">
import { computed } from 'vue'

const { dataSource } = defineProps({
  dataSource: {
    type: Array,
    default: () => [],
  },
})

const list = computed({
  get: () => {
    return dataSource
  },
  set: (value) => {
    list.value = value
  },
})

const columns = [
  {
    title: '字段名称',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '数据类型',
    dataIndex: 'type',
    key: 'type',
  },
  {
    title: '是否必填',
    dataIndex: 'required',
    key: 'required',
  },
  {
    title: '参数说明',
    dataIndex: 'description',
    key: 'description',
  },
]
</script>
<style scoped lang="less"></style>
