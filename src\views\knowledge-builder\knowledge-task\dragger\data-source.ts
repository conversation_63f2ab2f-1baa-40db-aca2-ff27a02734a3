export const columns = [
  {
    title: '属性(中文)',
    dataIndex: 'nameZh',
    key: 'nameZh',
  },
  {
    title: '属性(英文)',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '属性类型',
    key: 'rangeNameZh',
    dataIndex: 'rangeNameZh',
  },
  {
    title: '操作',
    key: 'action',
  },
]

export const mockData = [
  {
    key: '1',
    chinese: '描述',
    english: 'description',
    type: '文本',
  },
  {
    key: '2',
    chinese: '实体主键',
    english: 'id',
    type: '文本',
  },
  {
    key: '3',
    chinese: '名称',
    english: 'name',
    type: '文本',
  },
]

export const relationshipColumns = [
  {
    title: '关系名称(中文)',
    dataIndex: 'nameZh',
    key: 'nameZh',
  },
  {
    title: '关系名称(英文)',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '起点/终点',
    key: 'point',
    dataIndex: 'point',
  },
]

export const relationshipData = [
  // {
  //   key: '1',
  //   chinese: '描述',
  //   english: 'description',
  //   point: 'aaa'
  // },
]

export const sampleColumns = [
  {
    title: '属性',
    dataIndex: 'label',
    key: 'label',
  },
  {
    title: '属性值',
    dataIndex: 'name',
    key: 'name',
  },
]

export const relationMap = {
  CONCEPT_TYPE: '自环',
}
