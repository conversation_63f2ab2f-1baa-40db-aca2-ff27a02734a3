import { renderItemByCallCount } from './tools.js'
import prettier from 'prettier'
import parseBabel from 'prettier/parser-babel'
import parseHtml from 'prettier/parser-html'

export async function getFormTamplate(formExample) {
  const str =
    `<template>
  <Form
    :items="items"
    ref="FormRefs"
    v-model="formData"
    :extral="{}"
    @submit="submitHandle"
    :layout="layout"
    >
  </Form>
  <div class="button-group">
      <a-button class="submit-button" @click="submit" type="primary">提交</a-button>
      <a-button @click="reset">重置</a-button>
    </div>
</template>
` +
    '<' +
    `script setup lang='ts'>
import { ref } from 'vue'
import { Form } from '@fs/fs-components'
import { message } from "ant-design-vue/es/components";
${renderItemByCallCount('addData', formExample)}

const layout = ${renderItemByCallCount('layout', formExample)};

const FormRefs = ref();

const formData = ref<any>({});

const items = ${JSON.stringify(renderItemByCallCount('getColumns', formExample))}


async function submitHandle(data: any) {
  try{
    console.log('data' + data)
    await ${renderItemByCallCount('addData', formExample)}(data)
    message.success('提交成功')
  }catch(error){
    console.log('error：' + error)
  }
}

function submit() {
  FormRefs.value.submit()
}

function reset() {
  formData.value = {}
  FormRefs.value.reset()
}


    </` +
    `script>
<style scoped lang="less">
 .button-group {
    text-align: right;
    .submit-button {
      margin-right: 20px;
    }
  }
</style>  
  `
  const data = await prettier.format(str, { parser: 'vue', plugins: [parseHtml, parseBabel] })
  return data
}
