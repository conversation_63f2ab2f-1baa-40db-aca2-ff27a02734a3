import router from '@/router/core'
import { type ObjectDirective } from 'vue'
import type { RouteLocationNormalizedLoaded } from 'vue-router'
import { useUserStore } from '@/stores/user'

const actionDirective: ObjectDirective<any, any> = {
  mounted(el, binding) {
    const actionName = binding.arg as string
    const checkPermission = () => {
      const userStore: any = useUserStore()
      const interfaceData = userStore.interfaceData
      const currentRoute: RouteLocationNormalizedLoaded = router.currentRoute.value
      const val = interfaceData.find((item: any) => item.path.includes(currentRoute.path))
      let clonePermission: any[] = []
      if (val) {
        clonePermission = val?.meta?.clonePermission || []
      }

      console.log(
        '%c [ clonePermission ]-11',
        'font-size:13px; background:pink; color:#bf2c9f;',
        clonePermission,
      )
      const permissionId = Array.isArray(clonePermission) ? clonePermission : [clonePermission]
      if (!permissionId.includes(actionName)) {
        ;(el.parentNode && el.parentNode.removeChild(el)) || (el.style.display = 'none')
      }
    }
    checkPermission()
  },
}
export default actionDirective
