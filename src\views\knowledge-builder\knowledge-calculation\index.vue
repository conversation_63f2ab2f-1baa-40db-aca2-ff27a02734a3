<script lang="ts" setup>
import { ref, onMounted, reactive } from 'vue'
import { useRoute } from 'vue-router'
import { DownOutlined, QuestionCircleOutlined } from '@ant-design/icons-vue'

import { AlgorithmType } from '@/views/knowledge-builder/type'
import {
  reqGetAllRelationType,
  reqGetAllTags,
  reqGetCentrality,
  reqGetCommunityDetection,
  reqGetDag,
  reqGetFastRP,
  reqGetKnowledgeModel,
  reqGetPathDijkstra,
  reqGetSimilarity,
} from '@/api/services/knowledge'
import Centrality from './centrality.vue'
import Community from './community.vue'
import Similarity from './similarity.vue'
import Path from './path.vue'
import Dag from './dag.vue'
import Node from './node.vue'

const calcType = ref(AlgorithmType.Centrality)
const formState = reactive({
  targetType: undefined,
  nodeLabels: [],
  relationshipTypes: [],
  startNodes: [] as any,
  sourceNode: 0,
  topN: 0,
  dimension: 128,
})
const route = useRoute()
const formRef = ref<any>(null)
const searchLoading = ref(false)

const pageMap = {
  [AlgorithmType.Centrality]: Centrality,
  [AlgorithmType.Community]: Community,
  [AlgorithmType.Similarity]: Similarity,
  [AlgorithmType.Path]: Path,
  [AlgorithmType.Dag]: Dag,
  [AlgorithmType.Node]: Node,
}
const pageData = ref<any>([])

// 获取节点下拉数据
// #region
const nodeSelectList = ref([])
const relationSelectList = ref([])
const startList = ref<any[]>([])
const selectStartNodes = ref<any>({})
const limit = ref(1000)

onMounted(() => {
  getNodeSelectList()
  getRelationSelectList()
  getSchemaChartList()
})

const pushNum = computed(() => {
  if ([AlgorithmType.Similarity, AlgorithmType.Path, AlgorithmType.Node].includes(calcType.value)) {
    return 16
  } else if ([AlgorithmType.Centrality].includes(calcType.value)) {
    return 8
  }
  return 0
})

const getNodeSelectList = async () => {
  const res = await reqGetAllTags({ projectId: route.query?.projectId })
  console.log('%c [ res ]-113', 'font-size:13px; background:#539b71; color:#97dfb5;', res)
  nodeSelectList.value = res.map((item: any) => {
    return {
      label: item,
      value: item,
    }
  })
}

const getRelationSelectList = async () => {
  const res = await reqGetAllRelationType({ projectId: route.query?.projectId })
  console.log('%c [ relationRes ]-133', 'font-size:13px; background:#bb7a7d; color:#ffbec1;', res)
  relationSelectList.value = res.map((item: any) => {
    return {
      label: item,
      value: item,
    }
  })
}

const getSchemaChartList = async (config: any = {}) => {
  const res = await reqGetKnowledgeModel({
    projectId: route.query?.projectId,
    limit: limit.value,
    ...config,
  })
  startList.value = res.entityTypeDTOList
  console.log('%c [ res ]-58', 'font-size:13px; background:#8ef08b; color:#d2ffcf;', res)
}

const handleSelectStartNodes = (type: any, item: any) => {
  selectStartNodes.value = item
  formState.startNodes = [
    {
      id: item.properties.id,
      type: type,
    },
  ]

  console.log(
    '%c [  ]-79',
    'font-size:13px; background:#bad1ba; color:#fefffe;',
    type,
    item,
    formState,
  )
}

const filterOption = (input: string, option: any) => {
  return option.value.toLowerCase().indexOf(input.toLowerCase()) >= 0
}
const handleChange = (value: string) => {
  console.log(`selected ${value}`)
}

const handleClear = () => {
  formState.targetType = undefined
  formState.nodeLabels = []
  formState.relationshipTypes = []
  formState.startNodes = []
  formState.sourceNode = 0
  formState.topN = 0
  formState.dimension = 128
  pageData.value = []
}

const handleSearch = async () => {
  // 验证必填项
  const values = await formRef.value?.validate()
  const data = {
    projectId: Number(route.query?.projectId || 0),
    ...toRaw(values),
  }
  searchLoading.value = true
  // 根据不同推算算法调用请求
  if (calcType.value === AlgorithmType.Centrality) {
    pageData.value = await reqGetCentrality(data).finally(() => {
      searchLoading.value = false
    })
  } else if (calcType.value === AlgorithmType.Community) {
    pageData.value = await reqGetCommunityDetection(data).finally(() => {
      searchLoading.value = false
    })
  } else if (calcType.value === AlgorithmType.Similarity) {
    pageData.value = await reqGetSimilarity(data).finally(() => {
      searchLoading.value = false
    })
  } else if (calcType.value === AlgorithmType.Path) {
    pageData.value = await reqGetPathDijkstra(data).finally(() => {
      searchLoading.value = false
    })
  } else if (calcType.value === AlgorithmType.Dag) {
    pageData.value = await reqGetDag(data).finally(() => {
      searchLoading.value = false
    })
  } else if (calcType.value === AlgorithmType.Node) {
    pageData.value = await reqGetFastRP(data).finally(() => {
      searchLoading.value = false
    })
  }
  console.log('%c [  ]-169', 'font-size:13px; background:#1cbfa2; color:#60ffe6;', pageData.value)
}

// #endregion
</script>

<template>
  <div class="calculation-page">
    <a-radio-group @change="handleClear" v-model:value="calcType" style="margin-bottom: 16px">
      <a-radio-button :value="AlgorithmType.Centrality">中心性算法</a-radio-button>
      <a-radio-button :value="AlgorithmType.Community">社区检测算法</a-radio-button>
      <a-radio-button :value="AlgorithmType.Similarity">相似度算法</a-radio-button>
      <a-radio-button :value="AlgorithmType.Path">路径查找算法</a-radio-button>
      <a-radio-button :value="AlgorithmType.Dag">Dag算法</a-radio-button>
      <a-radio-button :value="AlgorithmType.Node">节点嵌入算法</a-radio-button>
    </a-radio-group>
    <div class="desc-container" v-show="calcType === AlgorithmType.Centrality">
      <div class="title">中心性（Centrality）</div>
      <div class="desc-item">
        <div class="label">定义：</div>
        <div class="value">
          <span>中心性用于衡量图中某个节点的重要性或影响力</span>
          <a-tooltip :overlayStyle="{ maxWidth: '700px', whiteSpace: 'normal' }">
            <template #title>
              <div>例子：</div>
              <div>在社交平台上找出最可能传播信息的用户。</div>
              <div>找出城市中交通流量最大的地铁站。</div>
              <div>在新冠疫情期间，通过社交接触网络找出可能的超级传播者。</div>
              <div>在蛋白质互作网络中，找出对癌症发生至关重要的中心蛋白。</div>
            </template>
            <QuestionCircleOutlined style="margin-left: 4px" />
          </a-tooltip>
        </div>
      </div>
    </div>
    <div class="desc-container" v-show="calcType === AlgorithmType.Community">
      <div class="title">社区检测（Community Detection）</div>
      <div class="desc-item">
        <div class="label">定义：</div>
        <div class="value">
          <span>用于识别图中紧密连接的节点群组，即“社区”，常用于发现结构化的子图</span>
          <a-tooltip :overlayStyle="{ maxWidth: '700px', whiteSpace: 'normal' }">
            <template #title>
              <div>例子：</div>
              <div>社交平台利用社区检测来推荐朋友或兴趣小组。</div>
              <div>电商识别经常购买特定类别的用户群体。</div>
              <div>分析糖尿病患者中常见的其他疾病组合（如高血压、高血脂）。</div>
              <div>在基因互作网络中识别与特定癌症相关的基因模块。</div>
            </template>
            <QuestionCircleOutlined style="margin-left: 4px" />
          </a-tooltip>
        </div>
      </div>
    </div>
    <div class="desc-container" v-show="calcType === AlgorithmType.Similarity">
      <div class="title">相似度（Similarity）</div>
      <div class="desc-item">
        <div class="label">定义：</div>
        <div class="value">
          <span>衡量两个节点（或结构）之间的相似程度</span>
          <a-tooltip :overlayStyle="{ maxWidth: '700px', whiteSpace: 'normal' }">
            <template #title>
              <div>例子：</div>
              <div>流媒体平台根据观影记录推荐相似的电影。</div>
              <div>搜索引擎中使用相似度算法返回最相关的结果。</div>
              <div>帮助医生快速找到类似病例，辅助诊断。</div>
              <div>发现某抗癌药的替代品或副作用较低的类似药物。</div>
            </template>
            <QuestionCircleOutlined style="margin-left: 4px" />
          </a-tooltip>
        </div>
      </div>
    </div>
    <div class="desc-container" v-show="calcType === AlgorithmType.Path">
      <div class="title">路径查找（Path Finding）</div>
      <div class="desc-item">
        <div class="label">定义：</div>
        <div class="value">
          <span>用于在图中寻找两个节点之间的最短路径或最佳路径</span>
          <a-tooltip :overlayStyle="{ maxWidth: '700px', whiteSpace: 'normal' }">
            <template #title>
              <div>例子：</div>
              <div>地图应用根据实时交通情况推荐最优路线。</div>
              <div>快递公司规划最短配送路线以节约成本。</div>
              <div>疫情期间，优化ICU病人的转院路径，减少感染风险。</div>
              <div>通过药物-靶点-信号通路图分析药物如何发挥作用。</div>
            </template>
            <QuestionCircleOutlined style="margin-left: 4px" />
          </a-tooltip>
        </div>
      </div>
    </div>
    <div class="desc-container" v-show="calcType === AlgorithmType.Dag">
      <div class="title">有向无环图（DAG, Directed Acyclic Graph）</div>
      <div class="desc-item">
        <div class="label">定义：</div>
        <div class="value">
          <span>一种图结构，所有边具有方向性且不含回路，常用于表示事件顺序或依赖关系</span>
          <a-tooltip :overlayStyle="{ maxWidth: '700px', whiteSpace: 'normal' }">
            <template #title>
              <div>例子：</div>
              <div>软件构建中的编译顺序依赖关系。</div>
              <div>建筑项目中，任务之间有依赖关系，使用DAG管理流程。</div>
              <div>制定标准化的患者治疗流程，如手术前、中、后步骤。</div>
              <div>构建疾病-症状-检查-诊断的因果路径，辅助医生决策。</div>
            </template>
            <QuestionCircleOutlined style="margin-left: 4px" />
          </a-tooltip>
        </div>
      </div>
    </div>
    <div class="desc-container" v-show="calcType === AlgorithmType.Node">
      <div class="title">节点嵌入（Node Embedding）</div>
      <div class="desc-item">
        <div class="label">定义：</div>
        <div class="value">
          <span>将图中的节点映射到低维向量空间中，保留图的结构信息和关系</span>
          <a-tooltip :overlayStyle="{ maxWidth: '700px', whiteSpace: 'normal' }">
            <template #title>
              <div>例子：</div>
              <div>将用户和物品嵌入到同一空间中，计算相似性，推荐用户可能喜欢的产品。</div>
              <div>社交网络分析利用节点嵌入来推荐可能认识的人或职位。</div>
              <div>将患者病历转化为向量，辅助预测疾病或治疗响应。</div>
              <div>将药物、靶点、疾病表示为嵌入向量，预测某种药物是否对某种疾病有效。</div>
            </template>
            <QuestionCircleOutlined style="margin-left: 4px" />
          </a-tooltip>
        </div>
      </div>
    </div>
    <div class="form-container">
      <a-form ref="formRef" :model="formState" name="basic">
        <a-row :gutter="24">
          <a-col :span="8" v-if="calcType === AlgorithmType.Centrality">
            <a-form-item
              name="targetType"
              :rules="[{ required: true, message: '目标节点标签不能为空' }]"
            >
              <a-select
                v-model:value="formState.targetType"
                show-search
                placeholder="目标节点标签"
                :options="nodeSelectList"
                :filter-option="filterOption"
                allowClear
              ></a-select>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item name="nodeLabels">
              <a-select
                v-model:value="formState.nodeLabels"
                show-search
                placeholder="节点标签"
                :options="nodeSelectList"
                :filter-option="filterOption"
                allowClear
                mode="multiple"
                :max-tag-count="1"
              ></a-select>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item name="relationshipTypes">
              <a-select
                v-model:value="formState.relationshipTypes"
                show-search
                placeholder="关系类型"
                :options="relationSelectList"
                :filter-option="filterOption"
                allowClear
                mode="multiple"
                :max-tag-count="1"
              ></a-select>
            </a-form-item>
          </a-col>
          <a-col :span="8" v-if="calcType === AlgorithmType.Centrality">
            <a-form-item name="startNodes">
              <a-dropdown :trigger="['click']">
                <a-button style="width: 100%" class="dropdown-link" @click.prevent>
                  <span :class="{ selected: formState.startNodes.length > 0 }">{{
                    formState.startNodes?.[0]?.type
                      ? `${selectStartNodes?.nameZh}-${formState.startNodes?.[0]?.type}`
                      : '请选择开始节点'
                  }}</span>
                  <DownOutlined />
                </a-button>
                <template #overlay>
                  <a-menu>
                    <a-sub-menu v-for="item in startList" :key="item.id" :title="item.nameZh">
                      <a-menu-item
                        @click="handleSelectStartNodes(subItem, item)"
                        v-for="subItem in item.labels.flat(Infinity)"
                        :key="subItem"
                        >{{ subItem }}</a-menu-item
                      >
                    </a-sub-menu>
                  </a-menu>
                </template>
              </a-dropdown>
            </a-form-item>
          </a-col>
          <a-col :span="8" v-if="calcType === AlgorithmType.Similarity">
            <a-form-item name="topN">
              <a-input-number
                style="width: 100%"
                id="inputNumber"
                placeholder="前N个(默认0无限制)"
                v-model:value="formState.topN"
                :min="0"
              />
            </a-form-item>
          </a-col>
          <a-col :span="8" v-if="calcType === AlgorithmType.Path">
            <a-form-item
              name="sourceNode"
              :rules="[{ required: true, message: '来源节点不能为空' }]"
            >
              <a-select
                v-model:value="formState.sourceNode"
                show-search
                placeholder="来源节点标签"
                :field-names="{ label: 'nameZh', value: 'id' }"
                :options="startList"
                :filter-option="filterOption"
                allowClear
              ></a-select>
            </a-form-item>
          </a-col>
          <a-col :span="8" v-if="calcType === AlgorithmType.Node">
            <a-form-item name="dimension">
              <a-input-number
                placeholder="维度"
                style="width: 100%"
                id="inputNumber"
                v-model:value="formState.dimension"
                :min="0"
                :max="128"
              />
            </a-form-item>
          </a-col>
          <a-col :span="8" :push="pushNum" style="text-align: right">
            <a-form-item>
              <a-button @click="handleClear">清除</a-button>
              <a-button
                :loading="searchLoading"
                style="margin-left: 8px"
                type="primary"
                @click="handleSearch"
                >查询</a-button
              >
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <keep-alive>
      <component :is="pageMap[calcType]" :pageData="pageData"></component>
    </keep-alive>
  </div>
</template>

<style lang="less" scoped>
.form-container {
  // box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.08);
  padding: 24px 0 0;
  margin-bottom: 24px;
}
.dropdown-link {
  color: #999;
  & .selected {
    color: #555;
  }
}
.desc-container {
  .title {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 8px;
  }
  .desc-item {
    display: flex;
    margin-bottom: 8px;
  }
}
</style>
