<template>
  <div class="role-management">
    <cardBox title="角色管理" subTitle="角色创建与权限分配管理">
      <template #headerRight>
        <a-button type="primary" @click="addTable" v-action:addRole>新增</a-button>
      </template>
      <Table :columns="columns" :getData="getRoleList" ref="tableRef" :searchFormState="formState">
        <template #search>
          <a-form-item label="" name="roleName" :rules="[{ message: '请输入角色名称' }]">
            <a-input v-model:value="formState.roleName" placeholder="请选择角色名称" />
          </a-form-item>
          <a-form-item label="">
            <a-select allow-clear v-model:value="formState.roleType" placeholder="请选择角色类别">
              <a-select-option :value="item.dataId" v-for="item in roleTypeList" :key="item">{{
                item.dataName
              }}</a-select-option>
            </a-select>
          </a-form-item>
        </template>
      </Table>
    </cardBox>
    <role-management-modal
      :open="modalOpen"
      :disabled="disabled"
      @modalCancel="modalCancel"
      @modalHandleOk="modalHandleOk"
      ref="modalRef"
    ></role-management-modal>
    <!-- 分配用户 -->
    <role-user-modal
      :requiredData="requiredData"
      @modalCancel="userModalCancel"
      @deleteUser="deleteUser"
      ref="userModalRef"
    ></role-user-modal>
    <!-- 分配数据源、分配配置项目 -->
    <DataSourceModel ref="dataSourceRef"></DataSourceModel>
    <RobatModel ref="robatRef"></RobatModel>
    <common-tree-node
      :exportModal="MenusModal"
      ref="Modalmenus"
      :title="title"
      :modalSystem="modalSystem"
      :checkedData="checkedData"
      @systemSelectEmitHandle="systemSelectEmitHandle"
      @exportModalHandleOk="exportModalHandleOk"
      @exportModalCancel="exportModalCancel"
      :exportTreeData="exportTreeData"
      :fNames="fNames"
      :MenusModalClear="MenusModalClear"
      :confirmLoading="confirmLoading"
    ></common-tree-node>
    <!-- 已分配菜单 -->
    <assigned-menu-modal
      ref="assignedModal"
      :roleId="roleId"
      @modalAssignedCancel="modalAssignedCancel"
      @modalAssignedOk="modalAssignedOk"
    ></assigned-menu-modal>
    <rule-model ref="ruleModelRef" />
    <data-assets ref="dataAssetsRef" />
    <!-- 机器学习文件 -->
    <machine-learning ref="machineLearningRef" />
    <ResourceServiceModal ref="resourceServiceRef" />
    <router-view></router-view>
  </div>
</template>
<script setup lang="ts">
import { storeToRefs } from 'pinia'
import { Table } from '@fs/fs-components'
import { useUserStore } from '@/stores/user'
import { RoleManagementDTO } from '@/utils/column'
import { DownOutlined } from '@ant-design/icons-vue'
import { ref, onBeforeMount, onMounted, h, withDirectives, resolveDirective } from 'vue'
import Dropdown from 'ant-design-vue/es/dropdown/dropdown'
import type { Column } from '@fs/fs-components/src/components/table/type'
import { Button, Menu, MenuItem, message } from 'ant-design-vue/es/components'
import {
  getRoleList,
  getUserSystem,
  getRoleMenuList,
  saveRoleMneu,
  deleteRoleUser,
} from '@/api/services/permission'
import cardBox from '@/components/card-box/card-box.vue'
import RuleModel from './component/rule-model.vue'
import DataAssets from './component/data-assets.vue'
import MachineLearning from './component/machine-learning.vue'
import DataSourceModel from './data-source-model.vue'
import RobatModel from './robat-model.vue'
import ResourceServiceModal from './component/resource-service-modal.vue'

let columns = ref<Column[]>([])
const tableRef = ref<InstanceType<typeof Table> | null>(null)
interface Data {
  dataId: string
  dataName: string
}
const modalRef = ref<any | null>(null)
const userModalRef = ref<any | null>(null)
const dataSourceRef = ref<any>(null)
const robatRef = ref<any>(null)
const modalOpen = ref<boolean>(false)
const disabled = ref<boolean>(false)
const roleTypeList = ref<Data[]>([])
const MenusModal = ref<boolean>(false)
const exportTreeData = ref<any>([])
const modalSystem = ref([])
const systemId = ref('')
const queryType = ref<number>(1)
const checkedData = ref<Record<string, any>>({})
const userStore = useUserStore()
const { userInfo } = storeToRefs(userStore)
const title = ref<string>('')
const MenusModalClear = ref<boolean>(false)
const confirmLoading = ref<boolean>(false)
const roleId = ref<string>('')
const requiredData = ref<any>({
  type: 'role',
  roleId: null,
})
const formState = ref<any>({
  roleName: '',
  roleTypeName: '',
  roleType: null,
})
const assignedModal = ref<any | null>(null)
const fNames = {
  children: 'datas',
  title: 'menuName',
  key: 'menuId',
}
const ruleModelRef = ref<any | null>(null)
const dataAssetsRef = ref<any | null>(null)
const machineLearningRef = ref<any | null>(null)
const resourceServiceRef = ref<any | null>(null)
onMounted(() => {
  getDictType()
})

function modalHandleOk() {
  tableRef.value?.getTableData()
  modalCancel()
}

function deleteUser(data: any, callback: () => any) {
  return new Promise((resolve, reject) => {
    deleteRoleUser({ userList: data })
      .then((res: any) => {
        if (res.code === '000000') {
          message.success('删除成功')
          resolve(true)
          callback()
        }
      })
      .catch(() => {
        reject()
      })
  })
}

function modalCancel() {
  modalOpen.value = false
}

onBeforeMount(() => {
  initColumns()
})

function getUserSystemModal() {
  const data = {
    userId: userInfo.value.userId,
    queryType: queryType.value,
  }
  getUserSystem(data).then((res: any) => {
    MenusModal.value = true
    modalSystem.value = res.data
  })
}

function systemSelectEmitHandle(id: string) {
  systemId.value = id
  const data = {
    roleId: checkedData.value?.roleId,
    systemId: systemId.value,
    queryType: queryType.value,
  }
  getRoleMenuList(data).then((res) => {
    exportTreeData.value = res.data
  })
}
async function exportModalHandleOk(data: Record<string, any>) {
  confirmLoading.value = true
  const menuList = data
  const { roleId } = checkedData.value
  MenusModal.value = false
  const params = {
    menuList,
    queryType: queryType.value,
    roleId,
    systemId: systemId.value,
  }
  saveRoleMneu(params)
    .then(() => {
      message.success('操作成功!')
      MenusModalClear.value = false
    })
    .finally(() => {
      confirmLoading.value = false
    })
}

function exportModalCancel() {
  MenusModal.value = false
  MenusModalClear.value = false
  confirmLoading.value = false
}

function getDictType() {
  // const params = { dictType: 'roleType' }
  // getDataDictList(params).then((res) => {
  //   if (res?.code === '000000') {
  //     roleTypeList.value = res?.data
  //   }
  // })
  roleTypeList.value = [{ dataId: '4', dataName: '自定义角色' }]
}

function addTable() {
  modalOpen.value = true
  disabled.value = false
  const data = {
    dictTypeOptions: roleTypeList.value,
  }
  modalRef.value?.show('add', data)
}

function clearModal() {
  MenusModalClear.value = true
  exportTreeData.value = []
  modalSystem.value = []
}

function userModalCancel() {
  requiredData.value.value = ''
}

function modalAssignedOk() {
  roleId.value = ''
}

function modalAssignedCancel() {
  roleId.value = ''
}

function operationHandle(key: string, record: Record<string, any>) {
  if (key === '0') {
    modalOpen.value = true
    disabled.value = true
    record['dictTypeOptions'] = roleTypeList.value
    modalRef.value?.show('edit', record)
  } else if (key === '1') {
    const { ...item } = record
    checkedData.value = item
    queryType.value = 1
    clearModal()
    title.value = '分配可查看菜单'
    getUserSystemModal()
  } else if (key === '2') {
    const { ...item } = record
    checkedData.value = item
    clearModal()
    queryType.value = 2
    title.value = '分配可授权菜单'
    getUserSystemModal()
  } else if (key === '3') {
    requiredData.value.roleId = record?.roleId
    userModalRef.value?.show('edit', record)
  } else if (key === '4') {
    roleId.value = record?.roleId
    assignedModal.value?.show('edit', record)
  } else if (key === '5') {
    // 打开规则模型弹窗
    ruleModelRef.value?.showModal(record)
  } else if (key === '6') {
    // 打开数据资产弹窗
    dataAssetsRef.value?.showModal(record)
  } else if (key === '7') {
    dataSourceRef.value?.show(key, record)
  } else if (key === '8') {
    dataSourceRef.value?.show(key, record)
  } else if (key === '9') {
    robatRef.value?.show(key, record)
  } else if (key === '10') {
    robatRef.value?.show(key, record)
  } else if (key === '11') {
    machineLearningRef.value?.showModal(record)
  } else if (key === '12') {
    resourceServiceRef.value?.showModal(record, record?.roleId)
  }
}

// 处理规则模型选择确认
function handleRuleConfirm(selectedData: any[]) {
  console.log('选中的规则模型:', selectedData)
  // 这里可以处理选中的规则模型数据
  message.success('规则模型分配成功')
}

// 处理数据资产选择确认
function handleDataAssetsConfirm(selectedData: any[]) {
  console.log('选中的数据资产:', selectedData)
  // 这里可以处理选中的数据资产数据
  message.success('数据资产分配成功')
}

async function initColumns() {
  columns.value = RoleManagementDTO.map((item) => {
    return {
      ...item,
      dataIndex: item.key,
    }
  })
  columns.value.unshift({
    key: 'index',
    title: '序号',
    width: 80,
    customRender: (data: Record<string, any>) => `${data.index + 1}`,
  })
  columns.value.push({
    key: 'action',
    title: '操作',
    customRender(data: Record<string, any>) {
      let menuItems = [
        { key: '0', name: '编辑', meta: 'updateRole' },
        { key: '3', name: '分配用户', meta: 'addRoleUser' },
        { key: '4', name: '已分配系统', meta: 'getRoleSystemList' },
        { key: '7', name: '分配数据源', meta: 'allocateDataSources' },
        { key: '8', name: '分配配置项目', meta: 'assignConfigurationItems' },
        { key: '6', name: '分配数据资产', meta: 'allocateDataAssets' },
        { key: '5', name: '分配规则模型', meta: 'allocationRuleModel' },
        { key: '9', name: '分配机器学习模型', meta: 'assignMachineLearningModels' },
        { key: '10', name: '分配机器学习实验', meta: 'assignMachineLearningExperiments' },
        { key: '11', name: '分配机器学习文件', meta: 'machineLearningEditFile' },
        { key: '12', name: '分配资源服务', meta: 'resourceService' },
      ]
      if (data?.record?.roleType !== 0) {
        menuItems.splice(1, 0, { key: '1', name: '分配可查看菜单', meta: 'viewMenu' })
        menuItems.splice(2, 0, { key: '2', name: '分配可授权菜单', meta: 'accreditMenu' })
      }
      const mappedMenuItems = menuItems.map((item) => {
        return h(MenuItem, { key: item.key }, () => {
          return withDirectives(
            h(
              Button,
              {
                type: 'text',
                style: { width: '100%' },
                onClick: () => {
                  operationHandle(item.key, data.record)
                },
              },
              {
                default: () => `${item.name}`,
              },
            ),
            [[resolveDirective('action'), '', item.meta]],
          )
        })
      })
      return [
        h(
          Dropdown,
          {
            placement: 'bottom',
          },
          {
            default: () => [
              h(
                Button,
                {
                  type: 'link',
                },
                {
                  default: () => ['更多', h(DownOutlined, {}, { default: () => '更多' })],
                },
              ),
            ],
            overlay: () => h(Menu, null, () => [mappedMenuItems]),
          },
        ),
      ]
    },
  })
}
</script>
<style lang="less">
.ant-dropdown-menu {
  .ant-dropdown-menu-item:hover {
    background-color: transparent !important;
  }
}
.role-management {
  height: 100%;
  background: white;
}
</style>
