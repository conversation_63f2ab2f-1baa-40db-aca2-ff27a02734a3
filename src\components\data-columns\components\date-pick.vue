<template>
  <a-tabs v-model:activeKey="activeKey" @change="handleTabChange()">
    <a-tab-pane key="BETWEEN" tab="在...之间">
      <a-range-picker
        style="width: 100%"
        v-model:value="dateValue"
        :show-time="{ format: 'HH:mm' }"
        format="YYYY-MM-DD HH:mm"
        valueFormat="YYYY-MM-DD HH:mm"
        :placeholder="['开始时间', '结束时间']"
        @change="handleDateChange()"
      />
    </a-tab-pane>
    <a-tab-pane key="IS BEFORE" tab="之前">
      <a-date-picker
        style="width: 100%"
        v-model:value="dateValue[0]"
        format="YYYY-MM-DD HH:mm:ss"
        valueFormat="YYYY-MM-DD HH:mm:ss"
        :show-time="{ format: 'HH:mm:ss' }"
        @change="handleDateChange()"
      />
    </a-tab-pane>
    <a-tab-pane key="IS ON" tab="当前">
      <a-date-picker
        style="width: 100%"
        v-model:value="dateValue[0]"
        format="YYYY-MM-DD HH:mm:ss"
        valueFormat="YYYY-MM-DD HH:mm:ss"
        :show-time="{ format: 'HH:mm:ss' }"
        @change="handleDateChange()"
      />
    </a-tab-pane>
    <a-tab-pane key="IS AFTER" tab="之后">
      <a-date-picker
        style="width: 100%"
        v-model:value="dateValue[0]"
        format="YYYY-MM-DD HH:mm:ss"
        valueFormat="YYYY-MM-DD HH:mm:ss"
        :show-time="{ format: 'HH:mm:ss' }"
        @change="handleDateChange()"
      />
    </a-tab-pane>
  </a-tabs>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'

const props = defineProps<{
  modelValue: any
}>()

const emits = defineEmits<{
  (e: 'update:modelValue', value: any): void
}>()

const activeKey = ref('BETWEEN')

const dateValue = ref<any[]>([null, null])

function handleTabChange() {
  dateValue.value = [null, null]
}

function handleDateChange() {
  emits('update:modelValue', {
    type: activeKey.value,
    value: dateValue.value,
  })
}

watch(
  () => props.modelValue,
  () => {
    if (props.modelValue) {
      activeKey.value = props.modelValue.type
      dateValue.value = props.modelValue.value
    }
  },
  {
    deep: true,
    immediate: true,
  },
)
</script>

<style lang="less" scoped></style>
