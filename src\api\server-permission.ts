import { Request } from '@fs/fs-components'
import { useUserStore } from '@/stores/user'
import { isDevMock } from '@/utils/index'
import router from '@/router/core'

export const newUserInfo = {
  systemcode: 'integratedSupervisionPortal',
  systemcode2: 'new-bigData',
}

const serviceAxios = new Request({
  handleRequestInterceptor,
  handleUnauthorized,
}).service

/**
 * 请求拦截
 */
function handleRequestInterceptor(config: any) {
  config.headers.Systemcode = config.headers.Systemcode
    ? config.headers.Systemcode
    : newUserInfo.systemcode2

  const userStore = useUserStore()
  const token = userStore.token
  const { userName, userId, tenantId } = userStore.userInfo
  if (token) {
    config.headers = {
      Authorization: token,
      opUser: userId,
      tenantId,
      opUserName: encodeURIComponent(userName),
      ...config.headers,
    }
  }
  return config
}

/**
 * 未授权回调
 */
function handleUnauthorized() {
  const userStore = useUserStore()
  userStore.clearLoginData()
  router.push('/login')
}

function service(data: any, mock?: any): any {
  if (mock && isDevMock()) {
    return Promise.resolve(mock)
  }
  return serviceAxios(data)
}

export default service
