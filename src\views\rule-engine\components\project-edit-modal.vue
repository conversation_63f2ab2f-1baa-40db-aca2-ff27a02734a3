<template>
  <Modal
    v-model:open="open"
    :title="title"
    @ok="handleSubmit"
    :confirmLoading="loading"
    @cancel="handleCancel"
    :destroyOnClose="true"
  >
    <Form :model="form" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }" ref="formRef">
      <FormItem
        label="名称"
        name="name"
        :rules="[{ required: true, type: 'string', message: '请输入项目名称', trigger: 'change' }]"
      >
        <Input v-model:value="form.name" placeholder="请输入项目名称" />
      </FormItem>
      <FormItem
        label="包名"
        name="packageName"
        :rules="[{ required: true, type: 'string', message: '请输入项目包名', trigger: 'change' }]"
      >
        <Input v-model:value="form.packageName" placeholder="请输入项目包名" />
      </FormItem>
      <FormItem label="标签" name="tag">
        <Input v-model:value="form.tag" placeholder="请输入项目标签" />
      </FormItem>
      <FormItem label="描述" name="description">
        <TextArea v-model:value="form.description" placeholder="请输入项目描述" :rows="4" />
      </FormItem>
    </Form>
  </Modal>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue'
import { Modal, Form, Input, message } from 'ant-design-vue'
import type { ProjectItem } from '../data'
import type { FormInstance } from 'ant-design-vue/es/form'
import { createProject, updateProject } from '../service'

const FormItem = Form.Item
const TextArea = Input.TextArea

const props = defineProps<{
  project?: ProjectItem
}>()
const open = defineModel<boolean>('open')
const title = computed(() => (props.project ? '编辑项目' : '创建项目'))

const emit = defineEmits<{
  (e: 'updateSuccess', project: ProjectItem): void
  (e: 'createSuccess', project: ProjectItem): void
  (e: 'cancel'): void
}>()

const loading = ref(false)
const formRef = ref<FormInstance | null>(null)

const getOriginalData = () => {
  return {
    autoId: undefined,
    name: '',
    tag: '',
    packageName: '',
    description: '',
  }
}
// 表单对象
const form = ref<ProjectItem>(getOriginalData())

// 监听项目数据变化
watch(
  () => props.project,
  (project) => {
    if (project) {
      // 复制对象到表单
      form.value = { ...project }
    } else {
      form.value = getOriginalData()
    }
  },
  { immediate: true },
)
const isEdit = computed(() => !!props.project)
const handleUpdate = async () => {
  const res = await updateProject(form.value)
  if (res.code === '000000') {
    // 提交成功，触发success事件
    emit('updateSuccess', { ...form.value })
    message.success('项目更新成功')
    open.value = false
  } else {
    message.error(res.msg || '项目更新失败')
  }
}
const handleCreate = async () => {
  const res = await createProject(form.value)
  if (res.code === '000000') {
    emit('createSuccess', { ...form.value })
    message.success('项目创建成功')
    open.value = false
  } else {
    message.error(res.msg || '项目创建失败')
  }
}
// 处理表单提交
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    loading.value = true
    if (isEdit.value) {
      await handleUpdate()
    } else {
      await handleCreate()
    }
    formRef.value?.resetFields()
    form.value = getOriginalData()
  } catch (error) {
    console.error('表单验证失败', error)
  } finally {
    loading.value = false
  }
}

// 取消编辑
const handleCancel = () => {
  open.value = false
  formRef.value?.resetFields()
  form.value = getOriginalData()
  emit('cancel')
}
</script>
