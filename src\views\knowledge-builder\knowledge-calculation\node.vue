<script lang="ts" setup>
import { defineProps } from 'vue'
import { nodeColumn } from './data-source'
import { CodeSandboxOutlined, CopyOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
const props = withDefaults(defineProps<{ pageData?: any }>(), {
  pageData: () => [], // 默认值为数组
})

const handleCopy = (embedding: string) => {
  if (!embedding) {
    message.warning('无可复制内容')
    return
  }

  console.log(
    '%c [  ]-16',
    'font-size:13px; background:#756bc7; color:#b9afff;',
    navigator.clipboard,
    embedding,
  )
  const textarea = document.createElement('textarea')
  textarea.value = JSON.stringify(toRaw(embedding))
  document.body.appendChild(textarea)
  textarea.select()
  document.execCommand('copy')
  document.body.removeChild(textarea)
  message.success('复制成功')
  // navigator.clipboard.writeText(embedding).then(
  //   () => {
  //     message.success('复制成功')
  //   },
  //   () => {
  //     message.error('复制失败')
  //   },
  // )
}
</script>

<template>
  <div class="centrality-page">
    <a-table :columns="nodeColumn" :data-source="pageData">
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'info'">
          <div><CodeSandboxOutlined /> {{ record.nodeInfo.name }}</div>
          <div>年龄：{{ record.nodeInfo.age }}</div>
          <div>部门：{{ record.nodeInfo.department }}</div>
        </template>
        <template v-if="column.dataIndex === 'operation'">
          <div>
            <div class="embedding-text">{{ record.embedding }}</div>
            <CopyOutlined @click="handleCopy(record.embedding)" />
          </div>
        </template>
      </template>
    </a-table>
  </div>
</template>

<style lang="less" scoped>
.embedding-text {
  max-width: 300px;
  word-break: break-all;
  display: -webkit-box;
  -webkit-line-clamp: 3; // 最多显示3行
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: normal;
}
</style>
