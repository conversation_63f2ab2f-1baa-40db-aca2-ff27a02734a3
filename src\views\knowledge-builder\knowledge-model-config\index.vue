<template>
  <div style="padding-left: 12px; background-color: #f7fafe; height: 100%">
    <div class="title-layout">
      <ArrowLeftOutlined @click="$router.go(-1)" />
      <h1 class="title-layout h1">模型配置</h1>
    </div>

    <modelConfig :data="props.data" @updateSuccess="handleClick" />
  </div>
</template>
<script lang="ts" setup>
import { onBeforeMount } from 'vue'
import { ArrowLeftOutlined } from '@ant-design/icons-vue'
// import generalConfig from '@/views/knowledge-builder/knowledge-model-config/general-configuration/index.vue'
import modelConfig from '@/views/knowledge-builder/knowledge-model-config/model-configuration/index.vue'
// import { getGloablConfig } from '@/api/services/home'
import type { KnowledgeBaseConfig } from '@/api/type'
const emits = defineEmits(['updateSuccess'])
const props = defineProps<{ data: KnowledgeBaseConfig }>()
// const activeKey = ref('1')
onBeforeMount(async () => {
  await getDataList()
  console.log(props.data)
})

const getDataList = async () => {
  // const res = await getGloablConfig({ version: 1, configId: 'KAG_CONFIG' })
  // formState.value = JSON.parse(res.config) as KnowledgeBaseConfig
  // formState.value = JSON.parse(props.data.config) as KnowledgeBaseConfig
  // console.log('%c 🍣 props.data: ', 'font-size:12px;background-color: #FFDD4D;color:#fff;', formState.value);
}

function handleClick() {
  console.log('==============执行了updateSuccess=========')
  emits('modalAddOk')
}

// const formState = ref<KnowledgeBaseConfig>({
//   graph_store: {
//     database: '',
//     password: '',
//     uri: '',
//     user: '',
//   },
//   vectorizer: {
//     type: '',
//     model: '',
//     base_url: '',
//     api_key: '',
//   },
//   prompt: {
//     biz_scene: '',
//     language: '',
//   },
//   llm_select: [],
//   llm: {},
// })
</script>
<style lang="less" scoped>
.title-layout {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 15px 0;
}
.title-layout h1 {
  margin: 0 12px;

  color: #333;
}
</style>
