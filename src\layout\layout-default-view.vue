<template>
  <a-layout class="layout">
    <sidebar-menu @menuItemChange="currentMenuItem = $event" />
    <a-layout>
      <header-menu :menu-item="currentMenuItem" />
      <a-layout-content class="layout-content">
        <router-view v-slot="{ Component, route }" v-if="isShow" :class="computedStyle">
          <transition name="fade" mode="out-in">
            <keep-alive :include="cacheList" :exclude="excludeCache">
              <component :is="Component" :key="route.fullPath" />
            </keep-alive>
          </transition>
        </router-view>
      </a-layout-content>
    </a-layout>
  </a-layout>
  <!-- 重置密码 -->
  <ResetPasswordModal
    v-if="resetPasswordModalOpen"
    v-model="resetPasswordModalOpen"
    :user-id="userInfo.userId"
    title="修改密码"
  />
  <!-- 生成代码弹窗 -->
  <codeGeneration v-if="isDev()" ref="codeGenerationRef" />
  <quickAccess v-if="isDev()" />
</template>

<script setup lang="ts">
import { storeToRefs } from 'pinia'
import { RouterView } from 'vue-router'
import { useAppStore } from '@/stores/app'
import { useUserStore } from '@/stores/user'
import { ref, onBeforeMount, watch, computed } from 'vue'
import { getUserInfo } from '@/api/services/permission'
import { isDev } from '@/utils'
import ResetPasswordModal from '@/components/permission/reset-password-modal.vue'
import codeGeneration from '@/components/code-generation/code-generation.vue'
import quickAccess from '@/components/quick-access/quick-access.vue'
import SidebarMenu from '@/layout/components/sidebars/sidebar-menu.vue'
import HeaderMenu from '@/layout/components/headers/header-menu.vue'

const route = useRoute()
const appStore = useAppStore()
const { isRouterShow } = storeToRefs(appStore)
const userStore = useUserStore()
const { isLoggedIn, token, userInfo } = storeToRefs(userStore)

const resetPasswordModalOpen = ref(false)

const codeGenerationRef = ref<any>(null)

const isShow = ref(true)

watch(
  () => isRouterShow.value,
  (val) => {
    isShow.value = val
  },
  {
    deep: true,
  },
)

const cacheList = computed(() => {
  return appStore.menuTabs.map((item) => {
    let url = item.path || ''
    if (url.startsWith('/')) {
      url = url.slice(1)
    }
    return url
  })
})

const computedStyle = computed(() => {
  const { path } = route
  // 外容器不加 padding 的页面
  const blackList = [
    'data-asset-management',
    'metadata-query',
    'distributed-file-management',
    'hbase-management',
    'tag-management',
    'meta-data-management',
    'iframePage',
    'apiconfig/api/apiList/apiList',
    'setting/template-manager',
  ]
  const isBlackList = blackList.some((item) => path.includes(item))
  if (isBlackList) {
    return 'no-padding'
  }
  return 'inner-warper'
})

const excludeCache = computed(() => {
  return appStore.excludeCache
})

const generationType = computed(() => {
  return appStore.generationType
})

const currentMenuItem = ref(null)

watch(
  generationType,
  (newValue) => {
    if (JSON.stringify(newValue) === '{}') return
    codeGenerationRef.value.show(newValue)
  },
  { deep: true, immediate: true },
)

onBeforeMount(() => {
  if (import.meta.env.VITE_APP_NEED_AUTH !== 'false') {
    isLoggedIn.value && getUserInfoFn()
  }
})

/**
 * 获取用户信息
 */
async function getUserInfoFn() {
  try {
    appStore.appLoading = true
    //@ts-ignore
    if (!userInfo?.userId) {
      const { data } = await getUserInfo({ userToken: token.value })
      userInfo.value = data
    }
  } catch (error) {
    console.log(error)
  } finally {
    appStore.appLoading = false
  }
}
</script>
<style scoped lang="less">
.layout {
  height: 100vh;
  .layout-content {
    overflow-y: auto;
    background: #fff;
  }
  .ant-layout-sider {
    background: #fff;
    overflow-y: auto;
  }
  .brand {
    width: 100%;
    overflow: hidden;
    display: flex;
    justify-content: center;
    margin-top: 15px;
    display: flex;
    color: rgba(0, 0, 0, 0.65);
    font-family:
      open sans,
      Helvetica,
      Arial,
      sans-serif;
    letter-spacing: -0.3px;
    font-variant: tabular-nums;
    font-weight: 600;
  }
  .ant-divider-horizontal {
    margin: 18px 0;
  }
  :deep(.rt-container) {
    height: 100%;
    .tabor-pages {
      height: calc(100% - 40px);
    }
  }
}
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s;
}
.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
.fade-enter-to,
.fade-leave-from {
  opacity: 1;
}

.inner-warper {
  padding: 24px;
  background-color: #fff;
  height: calc(100vh - 58px);
}
.no-padding {
  height: 100%;
  padding: 0;
}
</style>
