export const templatePagesData = [
  {
    isUse: 1,
    systemId: 'F00001',
    levelIds: '6537799c372bf736cc255216',
    isChildrenMenu: false,
    menuName: '工具页',
    isDefaultGrant: 0,
    parentId: '_top',
    childrens: [
      {
        isUse: 1,
        systemId: 'F00001',
        levelIds: '6537799c372bf736cc222116.',
        isChildrenMenu: false,
        menuName: '500',
        isDefaultGrant: 0,
        parentId: '_top',
        menuUrl: 'anomaly-page',
        childrens: [],
        sortId: 1,
        menuId: '6537799c372bf736cc222116',
        menuType: 1,
        menuTypeName: '500',
        actionName: '1',
      },
      {
        isUse: 1,
        systemId: 'F00001',
        levelIds: '6537799c372b23236cc222116.',
        isChildrenMenu: false,
        menuName: '高级表单',
        isDefaultGrant: 0,
        parentId: '_top',
        menuUrl: 'advanced-form',
        childrens: [],
        sortId: 1,
        menuId: '6537799c372b23236cc222116',
        menuType: 1,
        menuTypeName: '500',
        actionName: '1',
      },
      {
        isUse: 1,
        systemId: 'F00001',
        levelIds: '6537799cfd232323236cc222116.',
        isChildrenMenu: false,
        menuName: '步骤表单',
        isDefaultGrant: 0,
        parentId: '_top',
        menuUrl: 'step-form',
        childrens: [],
        sortId: 1,
        menuId: '6537799cfd232323236cc222116',
        menuType: 1,
        menuTypeName: '500',
        actionName: '1',
      },
      {
        isUse: 1,
        systemId: 'F00001',
        levelIds: '6537fsdada2372b23236cc222116.',
        isChildrenMenu: false,
        menuName: '查询表格',
        isDefaultGrant: 0,
        parentId: '_top',
        menuUrl: 'table-list',
        childrens: [
          {
            actionName: 'updateJob',
            menuType: 2,
          },
          {
            actionName: 'deleteJob',
            menuType: 2,
          },
          {
            actionName: 'addJob',
            menuType: 2,
          },
        ],
        sortId: 1,
        menuId: '6537fsdada2372b23236cc222116',
        menuType: 1,
        menuTypeName: '500',
        actionName: '1',
      },
      {
        isUse: 1,
        systemId: 'F00001',
        levelIds: '6537fsdaffff2b23236cc222116.',
        isChildrenMenu: false,
        menuName: 'echarts基础页面',
        isDefaultGrant: 0,
        parentId: '_top',
        menuUrl: 'echarts',
        childrens: [],
        sortId: 1,
        menuId: '6537fsdaffff2b23236cc222116',
        menuType: 1,
        menuTypeName: '500',
        actionName: '1',
      },
      {
        isUse: 1,
        systemId: 'F00001',
        levelIds: '6537f22gfhhjjjffff2b23236cc222116.',
        isChildrenMenu: false,
        menuName: 'echarts布局模板',
        isDefaultGrant: 0,
        parentId: '_top',
        menuUrl: 'page-layout',
        childrens: [],
        sortId: 1,
        menuId: '6537f22gfhhjjjffff2b23236cc222116',
        menuType: 1,
        menuTypeName: '500',
        actionName: '1',
      },
      {
        isUse: 1,
        systemId: 'F00001',
        levelIds: '6537799c372bf736cc255216.fhdhhadhadj2313123312.',
        isChildrenMenu: false,
        menuName: 'api管理',
        isDefaultGrant: 0,
        parentId: '_top',
        menuUrl: 'manage-api',
        childrens: [],
        sortId: 1,
        menuId: 'fhdhhadhadj2313123312',
        menuType: 1,
        menuTypeName: '500',
        actionName: '1',
      },
      {
        isUse: 1,
        systemId: 'F00001',
        levelIds: '6537799c372bf736cc255216.6537f22gfghhhhf236cc222116.',
        isChildrenMenu: false,
        menuName: '接口生成',
        isDefaultGrant: 0,
        parentId: '_top',
        menuUrl: 'port-manage',
        childrens: [],
        sortId: 1,
        menuId: '6537f22gfghhhhf236cc222116',
        menuType: 1,
        menuTypeName: '500',
        actionName: '1',
      },
      {
        isUse: 1,
        systemId: 'F00001',
        levelIds: '6537799c372bf736cc255216.ffjsjdjadjjfhuau23232.',
        isChildrenMenu: false,
        menuName: 'gpt接口管理',
        isDefaultGrant: 0,
        parentId: '_top',
        menuUrl: 'gpt-manage',
        childrens: [],
        sortId: 1,
        menuId: 'ffjsjdjadjjfhuau23232',
        menuType: 1,
        menuTypeName: '500',
        actionName: '1',
      },
    ],
    sortId: 1,
    menuId: '6537799c372bf736cc255216',
    menuType: 1,
    menuTypeName: '工具页',
    actionName: '1',
  },
]
