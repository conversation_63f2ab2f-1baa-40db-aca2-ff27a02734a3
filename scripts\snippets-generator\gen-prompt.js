import fs from 'fs-extra'
import { ANTD_COMP_LIST } from './const/index.js'

// 目标文件
const TARGET_FILE = './dist/gptcode.code-snippets'

function genContent(compName) {
  return {
    prefix: `fs-${compName}`,
    body: [
      `/**`,
      ` * 根据 <template> 模板中 <${compName}> 组件，生成其相关属性的数据声明、事件函数等`,
      `**/`,
    ],
    description: `<${compName}> 提示指令`,
  }
}

async function run() {
  const data = {}
  // 开始生成
  for (const compName of ANTD_COMP_LIST) {
    const name = compName.replace(/[<>]/g, '')
    data[name] = genContent(name)
  }

  console.log('data', data)

  fs.writeFileSync(TARGET_FILE, JSON.stringify(data, null, 2))

  console.log('写入完成')
}

run()
