export function getMessage(msg: string, params: any[] = [], response: any[] = []) {
  const str = `Javascript 编程环境，根据接口功能描述，用约定的 JSON 格式描述接口的输入和输出，并标识返回的数据为对象或者数组对象，当描述的数据中有数组类型嵌套时，统一用 children 作为字段名，params和response应该都是数组
    ${hasData(params, response) ? '现在提供部分输入输出的相关字段，请根据功能描述补全丰富。' : ''}
    要求只返回 JSON 代码不需要解释文字： 
  - 接口功能描述：${msg}
  - ${params && params.length > 0 ? `已知的输入参数：${JSON.stringify(params)}` : ''}
  - ${response && response.length > 0 ? `已知的返回字段：${JSON.stringify(response)}` : ''}
  - JSON格式示例： 
  {
    "params": [
      {
        "name": "page",
        "description": "页码",
        "type": "number",
        "default": "1",
        "required": true
      }
    ],
    "interfaceName": "getList"
    "httpMethodName": "POST",
    "interfaceDesc": "获取",
    "fullPath": "/student/school/login",
    "response": [
      {
        "name": "code",
        "type": "string",
        "description": "状态码（000000：成功；其他：失败）"
      },
      {
        "name": "msg",
        "type": "string",
        "description": "提示信息"
      },
      {
        "name": "data",
        "type": "array",
        "description": "响应的业务数据内容",
        "children": []
      }
    ]
  }
  
  JSON格式示例中各字段的含义： - \`params\`标识输入参数
  - \`returnType\`枚举response中name为data type为array表示返回数据是数组对象，而"object"表示对象
  - \`interfaceName\`表示接口名称
  - \`httpMethodName\`表示接口请求方式
  - \`interfaceDesc\`表示接口功能描述
  - \`fullPath\`表示接口请求路径
  - \`return\`表示接口返回数据的字段描述
  - \`name\`表示字段名
  - \`type\`表示字段类型
  - \`default\`表示默认值
  - \`required\`表示是否必填，枚举值是 \`true\`或者\`false\`
  - \`desc\`表示字段的中文名称
  - \`enum\`表示该字段的枚举值`

  return str
}

function hasData(params: any[], response: any[]) {
  if (params.length || response.length) {
    return true
  }
  return false
}

export function getCompletionsResponse(response: any) {
  const data = response.data
  const list = data.split('data:')
  let allData = ''
  list.forEach((item: any) => {
    if (item) {
      if (!item.includes('DONE')) {
        // 解析 JSON 字符串
        const data = JSON.parse(item)
        const content = data.choices[0].delta.content
        allData += content
      }
    }
  })
  const str = allData.replace(/^```json\n|\n```$/g, '')
  return JSON.parse(str)
}

export function getTypeName(returnType: string) {
  if (!returnType) {
    return ''
  } else if (returnType === 'object') {
    return '（对象）'
  } else if (returnType === 'array') {
    return '（列表）'
  }
}

// 递归处理数据
export function getNewResponse(list: any[]) {
  list.forEach((item: any) => {
    // 转换成api数据类型
    if (['object', 'array'].includes(item.type)) {
      item.isArray = item.type === 'array' ? true : false
      item.type = item.children
      if (Array.isArray(item.type) && item?.type.length > 0) {
        getNewResponse(item.type)
      }
    }
  })
}
