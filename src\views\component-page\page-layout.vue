<template>
  <div class="page-layout">
    <a-tabs v-model:activeKey="activeKey">
      <a-tab-pane key="1" tab="示例">
        <h2>横向多个</h2>
        <div style="background: #f5f5f5; padding: 16px">
          <flex-componnent :options="objData"></flex-componnent>
        </div>
        <h2>右侧纵向排列</h2>
        <div style="padding: 16px">
          <flex-componnent :options="objData2"></flex-componnent>
        </div>
        <h2>右侧多个排列</h2>
        <div style="background: #f5f5f5; padding: 16px">
          <flex-componnent :options="objData3"></flex-componnent>
        </div>
      </a-tab-pane>
      <a-tab-pane key="2" tab="代码编辑">
        <div class="echarts-box">
          <div class="left-box">
            <a-tabs v-model:activeKey="activeKey2" type="card" @change="handleTabChange">
              <a-tab-pane key="1" tab="代码编辑">
                <v-ace-editor
                  v-model:value="codeOptions"
                  lang="javascript"
                  theme="chrome"
                  :style="{ height: 'calc(100vh - 280px)' }"
                  :options="{ useWorker: true }"
                  @input="debouncedHandleInput"
                />
              </a-tab-pane>
              <a-tab-pane key="2" tab="完整代码">
                <v-ace-editor
                  v-model:value="integrityCode"
                  lang="javascript"
                  theme="chrome"
                  :style="{ height: 'calc(100vh - 280px)' }"
                  :readonly="true"
                />
              </a-tab-pane>
            </a-tabs>
          </div>
          <div class="right-box">
            <flex-componnent :options="compData"></flex-componnent>
          </div>
        </div>
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import flexComponnent from './component/flex-componnent.vue'
import {
  getOptionsBar,
  getOptionsLine,
  getOptionsPie,
  getOptionsRadar,
  getOptionsLineBg,
  getOptionsGauge,
} from '@/api/mock'
import { debounce } from '@/utils'
import { VAceEditor } from 'vue3-ace-editor'
import 'ace-builds/src-noconflict/mode-javascript' // Load the language definition file used below
import 'ace-builds/src-noconflict/theme-chrome' // Load the theme definition file used below

import ace from 'ace-builds'
import workerJavascript from 'ace-builds/src-noconflict/worker-javascript?url' // For vite
ace.config.setModuleUrl('ace/mode/javascript_worker', workerJavascript)

const activeKey = ref('2')
const activeKey2 = ref('1')

const objData = ref({
  compnentName: 'a-flex',
  comProps: {
    vertical: false,
    gap: 16,
  },
  children: [
    {
      compnentName: 'Echarts',
      comProps: {
        getOptions: getOptionsBar,
        title: '柱状图',
        items: [
          {
            type: 'radio-group',
            name: 'type',
            defaultValue: 'all',
            comProps: {
              options: [
                {
                  label: '全部',
                  value: 'all',
                },
                {
                  label: '今日',
                  value: 'today',
                },
                {
                  label: '本周',
                  value: 'week',
                },
                {
                  label: '本月',
                  value: 'month',
                },
                {
                  label: '本年',
                  value: 'year',
                },
              ],
              optionType: 'button',
            },
          },
        ],
        style: {
          width: 'calc((100% - (3 - 1) * 16px) / 3)',
        },
      },
    },
    {
      compnentName: 'Echarts',
      comProps: {
        getOptions: getOptionsLine,
        title: '折线图',
        items: [
          {
            type: 'select',
            name: 'name',
            defaultValue: [],
            comProps: {
              placeholder: '请选择类型',
              mode: 'multiple',
              maxTagCount: 2,
              style: {
                width: '200px',
              },
              options: [
                {
                  label: '全部',
                  value: 'all',
                },
                {
                  label: '开启',
                  value: 'start',
                },
                {
                  label: '关闭',
                  value: 'close',
                },
                {
                  label: '取消',
                  value: 'cancel',
                },
              ],
              optionType: 'button',
            },
          },
        ],
        style: {
          width: 'calc((100% - (3 - 1) * 16px) / 3)',
        },
      },
    },
    {
      compnentName: 'Echarts',
      comProps: {
        getOptions: getOptionsPie,
        title: '饼图',
        items: [
          {
            type: 'date-picker',
            name: 'date',
            comProps: {},
          },
        ],
        style: {
          width: 'calc((100% - (3 - 1) * 16px) / 3)',
        },
      },
    },
  ],
})

const objData2 = ref({
  compnentName: 'a-flex',
  comProps: {
    vertical: false,
    gap: 16,
  },
  children: [
    {
      compnentName: 'Echarts',
      comProps: {
        getOptions: getOptionsLineBg,
        title: '折线图',
        items: [
          {
            type: 'date-picker',
            name: 'date',
            comProps: {},
          },
        ],
        style: {
          width: 'calc((100% - (2 - 1) * 16px) / 2)',
        },
      },
    },
    {
      compnentName: 'a-flex',
      comProps: {
        vertical: false,
        gap: 16,
        style: {
          width: 'calc((100% - (2 - 1) * 16px) / 2)',
        },
      },
      children: [
        {
          compnentName: 'Echarts',
          comProps: {
            getOptions: getOptionsRadar,
            title: '饼图',
            style: {
              width: 'calc((100% - (2 - 1) * 16px) / 2)',
            },
          },
        },
        {
          compnentName: 'Echarts',
          comProps: {
            getOptions: getOptionsBar,
            title: '柱状图',
            style: {
              width: 'calc((100% - (2 - 1) * 16px) / 2)',
            },
          },
        },
      ],
    },
  ],
})

const objData3 = ref({
  compnentName: 'a-flex',
  comProps: {
    vertical: false,
    gap: 16,
  },
  children: [
    {
      compnentName: 'table-list',
      comProps: {
        getOptions: getOptionsLine,
        style: {
          width: 'calc((100% - (2 - 1) * 16px) / 2)',
        },
      },
    },
    {
      compnentName: 'a-flex',
      comProps: {
        vertical: true,
        gap: 16,
        align: 'center',
        style: {
          width: 'calc((100% - (2 - 1) * 16px) / 2)',
        },
      },
      children: [
        {
          compnentName: 'a-flex',
          comProps: {
            vertical: false,
            gap: 16,

            style: {
              width: '100%',
            },
          },
          children: [
            {
              compnentName: 'Echarts',
              comProps: {
                getOptions: getOptionsPie,
                title: '折线图',
                height: 350,
                style: {
                  width: 'calc((100% - (2 - 1) * 16px) / 2)',
                },
              },
            },
            {
              compnentName: 'Echarts',
              comProps: {
                getOptions: getOptionsBar,
                title: '柱状图',
                height: 350,
                style: {
                  width: 'calc((100% - (2 - 1) * 16px) / 2)',
                },
              },
            },
          ],
        },
        {
          compnentName: 'a-flex',
          comProps: {
            vertical: false,
            gap: 16,
            style: {
              width: '100%',
            },
          },
          children: [
            {
              compnentName: 'Echarts',
              comProps: {
                getOptions: getOptionsRadar,
                title: '雷达图',
                height: 350,
                style: {
                  width: 'calc((100% - (2 - 1) * 16px) / 2)',
                },
              },
            },
            {
              compnentName: 'Echarts',
              comProps: {
                getOptions: getOptionsLine,
                title: '折线图',
                height: 350,
                style: {
                  width: 'calc((100% - (2 - 1) * 16px) / 2)',
                },
              },
            },
          ],
        },
      ],
    },
  ],
})

const compData = ref<any>('')

const codeOptions = ref(`const compData = {
  compnentName: 'a-flex',
  comProps: {
    vertical: true,
    gap: 16,
  },
  children: [
    {
      compnentName: 'a-flex',
      comProps: {
        vertical: false,
        gap: 16,
      },
      children: [
        {
          compnentName: 'Echarts',
          comProps: {
            getOptions: getOptionsBar,
            title: '柱状图',
            width: '100%',
            items: [
              {
                type: 'radio-group',
                name: 'type',
                defaultValue: 'all',
                comProps: {
                  options: [
                    {
                      label: '全部',
                      value: 'all',
                    },
                    {
                      label: '今日',
                      value: 'today',
                    },
                    {
                      label: '本周',
                      value: 'week',
                    },
                    {
                      label: '本年',
                      value: 'year',
                    },
                  ],
                  optionType: 'button',
                },
              },
            ],
            style: {
              width: 'calc((100% - (3 - 1) * 16px) / 3)',
            },
          },
        },
        {
          compnentName: 'Echarts',
          comProps: {
            getOptions: getOptionsLine,
            title: '折线图',
            width: '100%',
            items: [
              {
                type: 'select',
                name: 'name',
                defaultValue: [],
                comProps: {
                  placeholder: '请选择类型',
                  mode: 'multiple',
                  maxTagCount: 2,
                  style: {
                    width: '200px',
                  },
                  options: [
                    {
                      label: '全部',
                      value: 'all',
                    },
                    {
                      label: '开启',
                      value: 'start',
                    },
                    {
                      label: '关闭',
                      value: 'close',
                    },
                    {
                      label: '取消',
                      value: 'cancel',
                    },
                  ],
                  optionType: 'button',
                },
              },
            ],
            style: {
              width: 'calc((100% - (3 - 1) * 16px) / 3)',
            },
          },
        },
        {
          compnentName: 'Echarts',
          comProps: {
            getOptions: getOptionsPie,
            title: '饼图',
            width: '100%',
            items: [
              {
                type: 'date-picker',
                name: 'date',
                comProps: { 
                  style: { width: '200px'},
                },
              },
            ],
            style: {
              width: 'calc((100% - (3 - 1) * 16px) / 3)',
            },
          },
        },
      ],
    },
    {
      compnentName: 'a-flex',
      comProps: {
        vertical: false,
        gap: 16,
      },
      children: [
        {
          compnentName: 'Echarts',
          comProps: {
            getOptions: getOptionsLineBg,
            title: '折线图',
            width: '100%',
            items: [
              {
                type: 'date-picker',
                name: 'date',
                comProps: {},
              },
            ],
            style: {
              width: 'calc((100% - (2 - 1) * 16px) / 2)',
            },
          },
        },
        {
          compnentName: 'a-flex',
          comProps: {
            vertical: false,
            gap: 16,
            style: {
              width: 'calc((100% - (2 - 1) * 16px) / 2)',
            },
          },
          children: [
            {
              compnentName: 'Echarts',
              comProps: {
                getOptions: getOptionsRadar,
                title: '饼图',
                width: '100%',
                style: {
                  width: 'calc((100% - (2 - 1) * 16px) / 2)',
                },
              },
            },
            {
              compnentName: 'Echarts',
              comProps: {
                getOptions: getOptionsBar,
                title: '柱状图',
                style: {
                  width: 'calc((100% - (2 - 1) * 16px) / 2)',
                },
              },
            },
          ],
        },
      ],
    },
    {
      compnentName: 'a-flex',
      comProps: {
        vertical: false,
        gap: 16,
      },
      children: [
        {
          compnentName: 'table-list',
          comProps: {
            getOptions: getOptionsLine,
            style: {
              width: 'calc((100% - (2 - 1) * 16px) / 2)',
            },
          },
        },
        {
          compnentName: 'a-flex',
          comProps: {
            vertical: true,
            gap: 16,
            align: 'center',
            style: {
              width: 'calc((100% - (2 - 1) * 16px) / 2)',
            },
          },
          children: [
            {
              compnentName: 'a-flex',
              comProps: {
                vertical: false,
                gap: 16,

                style: {
                  width: '100%',
                },
              },
              children: [
                {
                  compnentName: 'Echarts',
                  comProps: {
                    getOptions: getOptionsPie,
                    title: '折线图',
                    width: '100%',
                    height: 350,
                    style: {
                      width: 'calc((100% - (2 - 1) * 16px) / 2)',
                    },
                  },
                },
                {
                  compnentName: 'Echarts',
                  comProps: {
                    getOptions: getOptionsBar,
                    title: '柱状图',
                    width: '100%',
                    height: 350,
                    style: {
                      width: 'calc((100% - (2 - 1) * 16px) / 2)',
                    },
                  },
                },
              ],
            },
            {
              compnentName: 'a-flex',
              comProps: {
                vertical: false,
                gap: 16,
                style: {
                  width: '100%',
                },
              },
              children: [
                {
                  compnentName: 'Echarts',
                  comProps: {
                    getOptions: getOptionsRadar,
                    title: '雷达图',
                    width: '100%',
                    height: 350,
                    style: {
                      width: 'calc((100% - (2 - 1) * 16px) / 2)',
                    },
                  },
                },
                {
                  compnentName: 'Echarts',
                  comProps: {
                    getOptions: getOptionsLine,
                    title: '折线图',
                    width: '100%',
                    height: 350,
                    style: {
                      width: 'calc((100% - (2 - 1) * 16px) / 2)',
                    },
                  },
                },
              ],
            },
          ],
        },
      ],
    },
  ],
}
 
compData
`)
const integrityCode = ref('')

const inputHandle = () => {
  try {
    const data = eval(codeOptions.value as string)
    compData.value = data
  } catch (err: any) {
    console.error(err)
  }
}

const debouncedHandleInput = debounce(inputHandle, 300)

const handleTabChange = (val: string) => {
  if (val === '2') {
    integrityCode.value =
      `<template>
    <flex-componnent :options="compData"></flex-componnent>
</template>
` +
      '<' +
      `script setup>
import flexComponnent from './component/flex-componnent.vue'
import {
  getOptionsBar,
  getOptionsLine,
  getOptionsPie,
  getOptionsRadar,
  getOptionsLineBg,
  getOptionsGauge,
} from '@/api/mock'

 ${codeOptions.value}
    </` +
      `script>`
  }
}
</script>
<style scoped lang="less">
.flex-item {
  grid-template-columns: repeat(2, 1fr); /* 两列，每列占一半宽度 */
}

/* 针对所有滚动条 */
::-webkit-scrollbar {
  width: 4px; /* 更改滚动条的宽度 */
}

/* 滚动条的滑块 */
::-webkit-scrollbar-thumb {
  background-color: #888; /* 滑块颜色 */
  border-radius: 10px; /* 滑块圆角 */
}

/* 滚动条的轨道 */
::-webkit-scrollbar-track {
  background-color: #f1f1f1; /* 轨道颜色 */
  border-radius: 10px; /* 轨道圆角 */
}

/* 滚动条滑块悬停时的效果 */
::-webkit-scrollbar-thumb:hover {
  background-color: #555; /* 滑块悬停时的颜色 */
}

.echarts-box {
  display: flex;
  .left-box {
    width: 30%;
    margin-right: 20px;
  }
  .right-box {
    width: 70%;
    background: #f5f5f5;
    padding: 16px;
  }
}
</style>
