<template>
  <a-modal title="添加新模型" v-model:visible="visible" width="40%" @cancel="cancel" @ok="ok">
    <div style="margin-top: 20px">
      <a-form :model="formState" ref="form">
        <a-form-item label="model" name="model" required>
          <a-input v-model:value="formState.model" :disabled="isEdit" />
        </a-form-item>
        <a-form-item label="base_url" name="base_url" required>
          <a-input v-model:value="formState.base_url" />
        </a-form-item>
        <a-form-item label="desc" name="desc" required>
          <a-input
            v-model:value="formState.desc"
            placeholder="Please enter remarks for partitioning"
          />
        </a-form-item>
      </a-form>
    </div>
  </a-modal>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
const emits = defineEmits(['createOllamaOk', 'updateOllamaOk'])
const form = ref({})
const formState = ref({})
const visible = ref(false)

function cancel() {
  visible.value = false
}

function ok() {
  if (isEdit.value) {
    emits('updateOllamaOk', formState.value)
  } else {
    emits('createOllamaOk', formState.value)
  }

  cancel()
}
const isEdit = ref(false)
function show(detail: any) {
  if (detail) {
    isEdit.value = true
    formState.value = JSON.parse(JSON.stringify(detail))
  } else {
    isEdit.value = false
  }
  visible.value = true
}

defineExpose({
  show,
})
</script>

<style lang="less" scoped>
.header-operate {
  margin: 20px 0;
  text-align: right;
  .ant-select {
    width: 200px;
  }
}
::v-deep .custom-input.ant-input {
  border-radius: 4px;
}

/* 方式 2：直接修改所有输入框 */
::v-deep .ant-input {
  border-radius: 4px !important;
}
</style>
