<script lang="ts" setup>
import {
  reqGetModelList,
  reqGetStudyList,
  reqSaveExperiments,
  reqSaveModel,
} from '@/api/services/data-auth'
import { ref } from 'vue'
import { experimentColumn, robatColumn } from './data-source'
import type { Key } from 'ant-design-vue/es/_util/type'
import { message } from 'ant-design-vue'
const open = ref<boolean>(false)
const modelType = ref<string>('')
const dataSource = ref<any>([])
const roleId = ref<string | number>()
const state = reactive<{
  selectedRowKeys: Key[]
  loading: boolean
}>({
  selectedRowKeys: [],
  loading: false,
})

const getModelList = async (key: string) => {
  const role_id = roleId.value
  if (key === '9') {
    const res = await reqGetModelList({ role_id })
    dataSource.value = res
    const selectedRowKeys = res.filter((item: any) => item.is_checked).map((ii: any) => ii.name)
    state.selectedRowKeys = selectedRowKeys
  } else {
    const res = await reqGetStudyList({ role_id })
    dataSource.value = res
    const selectedRowKeys = res.filter((item: any) => item.is_checked).map((ii: any) => ii.name)
    state.selectedRowKeys = selectedRowKeys
  }
}

const onSelectChange = (selectedRowKeys: Key[]) => {
  console.log('selectedRowKeys changed: ', selectedRowKeys)
  state.selectedRowKeys = selectedRowKeys
}

const show = (key: string, record: any) => {
  roleId.value = record?.roleId
  getModelList(key)
  modelType.value = key
  open.value = true
}

const handleOk = async (e: MouseEvent) => {
  const data = {
    role_id: roleId.value,
    name_list: state.selectedRowKeys,
  }
  if (modelType.value === '9') {
    await reqSaveModel(data)
    message.success('保存成功')
    state.selectedRowKeys = []
  } else {
    await reqSaveExperiments(data)
    message.success('保存成功')
    state.selectedRowKeys = []
  }
  console.log(e)
  open.value = false
}

defineExpose({ show })
</script>

<template>
  <a-modal
    width="1200px"
    v-model:open="open"
    :title="modelType === '9' ? '分配机器学习模型' : '分配机器学习实验'"
    @ok="handleOk"
    destroyOnClose
  >
    <a-table
      size="small"
      rowKey="name"
      :row-selection="{ selectedRowKeys: state.selectedRowKeys, onChange: onSelectChange }"
      :columns="modelType === '9' ? robatColumn : experimentColumn"
      :data-source="dataSource"
    />
  </a-modal>
</template>

<style lang="less" scoped></style>
