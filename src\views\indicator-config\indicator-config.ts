export function tranlateDataToParams(data: any[]) {
  // 把编辑区的数据转成后端需要的参数格式
  const list: any[] = []
  let params: any = {}
  data.forEach((item) => {
    if (!Object.keys(params).length) {
      list.push(params)
      if (list.length > 1) {
        params.tableName = list[0].tableName
        params.tableId = list[0].tableId
        params.databaseName = list[0].databaseName
      }
    }
    if (item.type === 'dataSource') {
      Object.assign(params, item.value)
    } else if (item.type === 'group' && item.value) {
      params['group'] = translateGroup(item.value.group, item, data)
      params['summary'] = translateSummary(item.value.summary, item, data)
      params = {}
    } else if (item.type === 'joins') {
      if (!params[item.type]) params[item.type] = []
      params[item.type].push(item.value)
    } else {
      params[item.type] = translateValue(item, data)
    }
  })
  return list
}

const methodMap: Record<string, (value: any[], item: any, sourceData: any[]) => any> = {
  customField: translateCustomField,
  where: translateWhere,
}

function translateValue(item: any, data: any[]) {
  const { type, value } = item
  if (methodMap[type]) {
    return methodMap[type](value, item, data)
  }
  return value || ''
}

function translateCustomField(value: any[]) {
  return value?.map((it) => {
    return {
      alias: it.alias,
      expression: it.expression.replace(/[[\]]/g, ''),
    }
  })
}

function translateWhere(value: any[], item: any, data: any[]) {
  // const tableName = value[0].tableName || data[0].value.tableName
  return value?.map((it) => {
    const { column, operateValue } = it
    const tableName = it.tableName || data[0].value.tableName
    let isBetween = operateValue.operator === 'BETWEEN'
    let opV = operateValue.value
    let operator = operateValue.operator
    if (['DATE'].includes(column.columnType)) {
      if (opV.type === 'BETWEEN') {
        isBetween = true
        operator = 'BETWEEN'
        opV = opV.value.toString()
      } else if (opV.type === 'IS BEFORE') {
        operator = '<'
        opV = opV.value[0]
      } else if (opV.type === 'IS ON') {
        operator = '='
        opV = opV.value[0]
      } else {
        operator = '>'
        opV = opV.value[0]
      }
    }

    const [minValue, maxValue] = isBetween ? opV.split(',') : ['', '']
    const v = isBetween ? '' : opV instanceof Array ? opV.join(',') : opV
    return {
      tableName,
      fieldName: column.title,
      operator,
      value: v,
      minValue,
      maxValue,
      fieldType: column.columnType,
      sourceData: item.sourceData,
    }
  })
}

function translateGroup(value: any[], item: any, sourceData: any[]) {
  return value?.map((it) => {
    return {
      tableName: it.tableName,
      fieldName: it.title,
      sourceData: item.sourceData,
    }
  })
}

function translateSummary(value: any[], item: any, sourceData: any[]) {
  const tableName = sourceData[0].value.tableName
  return value?.map((it) => {
    return {
      tableName: tableName,
      fieldName: it.fieldName,
      function: it.value,
      sourceData: item.sourceData,
    }
  })
}

export function translateParamsToData(params: any[]) {
  // 把后端返回的参数转成编辑区需要的数据格式
  const specilKeys = ['group']
  const filterKeys = [
    'databaseName',
    'fields',
    'tableName',
    'dbColumns',
    'sourceData',
    'summary',
    'tableId',
  ]
  const data: any[] = []
  params.forEach((param: any, i: number) => {
    if (i === 0) {
      data.push({
        type: 'dataSource',
        value: {
          databaseName: param.databaseName,
          tableId: param.tableId,
          tableName: param.tableName,
          fields: param.fields,
          dbColumns: param.dbColumns,
        },
        preview: false,
      })
    }
    Object.keys(param)
      .filter((key) => !filterKeys.includes(key))
      .forEach((key) => {
        const value = param[key]
        if (specilKeys.includes(key)) {
          data.push({
            type: key,
            value: value[0].sourceData,
            sourceData: value[0].sourceData,
            preview: false,
          })
        } else if (['where'].includes(key)) {
          data.push({
            type: key,
            value: value[0].sourceData,
            sourceData: value[0].sourceData,
            preview: false,
          })
        } else if (['joins'].includes(key)) {
          value.forEach((join: any) => {
            data.push({
              type: 'joins',
              value: join,
            })
          })
        } else {
          data.push({
            type: key,
            value: value.sourceData || value,
            preview: false,
          })
        }
      })
  })
  return data
}

// {
//   tableName: '',
//   databaseName: '',
//   fields: [
//     {
//       fieldName: '',
//       alias: '',
//       selected: true,
//     },
//   ],
//   joins: [
//     {
//       joinTableName: 'user_info',
//       joinType: 'left,inner,right',
//       joinFields: [
//         {
//           fieldName: 'ID',
//           alias: '用户ID',
//           selected: true,
//         },
//         {
//           fieldName: 'name',
//           alias: '用户名称',
//         },
//       ],
//       joinConditions: [
//         {
//           mainTableName: 'user',
//           mainFieldName: 'user_id',
//           operator: '=',
//           joinTableName: 'user_info',
//           joinFieldName: 'user_id',
//         },
//       ],
//     },
//   ],
//   customField: [
//     {
//       expression: "CONCAT(user.user_id, '---', user.user_name)",
//       alias: '表达式别名',
//     },
//   ],
//   where: [
//     {
//       tableName: 'user',
//       fieldName: 'user_id',
//       operator:
//         '=,!=,>,<,BETWEEN,>=,<=,IS_NULL,NOT_NULL,contains,not_contains,start_with,end_with,is_true,is_false',
//       minValue: '仅限于BETWEEN',
//       maxValue: '100',
//       value: '100',
//       fieldType: '',
//     },
//   ],
//   summary: [
//     {
//       tableName: 'user',
//       fieldName: 'user_id',
//       function: 'count,sum,avg,max,min,distinct_count',
//     },
//   ],
//   group: [
//     {
//       tableName: 'user',
//       fieldName: 'user_id',
//     },
//   ],
//   order: [
//     {
//       tableName: 'user',
//       fieldName: 'user_id',
//       sort: 'asc,desc',
//     },
//   ],
//   limitValue: 100,
// }
