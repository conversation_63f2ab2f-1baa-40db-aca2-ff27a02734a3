<template>
  <div style="padding-right: 10px">
    <a-table :columns="columns" :data-source="props.data">
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <a @click="toVllmEdit(record)">编辑</a>
          <a-popconfirm
            title="是否确认删除"
            ok-text="是"
            cancel-text="否"
            @confirm="handleDelete(record)"
          >
            <a style="margin-left: 10px">删除</a>
          </a-popconfirm>
        </template>
      </template>
    </a-table>
  </div>
  <createVllm ref="createVllmRef" @updateVllmOk="handleUpdateVllmSuccess" />
</template>
<script lang="ts" setup>
import type { KnowledgeBaseConfig } from '@/api/type'
import createVllm from '@/views/knowledge-builder/knowledge-model-config/model-configuration/create-vllm.vue'
import { ref } from 'vue'
const props = defineProps<{ data: KnowledgeBaseConfig }>()
const createVllmRef = ref<InstanceType<typeof createVllm>>()
const emits = defineEmits(['deleteVllmOk', 'updateVllmSuccess'])
function toVllmEdit(record: any) {
  createVllmRef.value?.show(record)
}
function handleDelete(record: any) {
  emits('deleteVllmOk', record)
}
function handleUpdateVllmSuccess(vllm: any) {
  emits('updateVllmSuccess', vllm)
}
const columns = [
  {
    title: '模型名称',
    key: 'model',
    dataIndex: 'model',
    width: 200,
    align: 'center',
  },
  {
    title: '备注',
    key: 'desc',
    dataIndex: 'desc',
    width: 200,
    align: 'center',
  },
  {
    title: '创建人',
    key: 'creator',
    dataIndex: 'creator',
    width: 200,
    align: 'center',
  },
  {
    title: '修改时间',
    key: 'createTime',
    dataIndex: 'createTime',
    width: 200,
    align: 'center',
  },
  {
    title: '操作',
    key: 'action',
    dataIndex: 'action',
    width: 200,
    align: 'center',
  },
]
</script>
<style lang="less" scoped>
:deep(.filter-box) {
  display: none;
}
</style>
