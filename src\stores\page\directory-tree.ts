import { defineStore } from 'pinia'
import { useLocalStorage } from '@vueuse/core'

export const useDirectoryTreeStore = defineStore('directory-tree', () => {
  const treeData = useLocalStorage<any>('treeData', {
    expandedKeys: [], // 展开的节点
    selectedKeys: [], // 选中的节点
    treeDbData: [], // 树形数据
    dataRef: {}, // 当前选中的节点
  })
  /**
   * 保存页面数据
   */
  function setTreeDbData(key: string, data: any) {
    treeData.value[key] = data
  }

  function clearTreeDbData() {
    treeData.value = {
      expandedKeys: [],
      selectedKeys: [],
      treeDbData: [],
      dataRef: {},
    }
  }

  return {
    treeData,
    setTreeDbData,
    clearTreeDbData,
  }
})
