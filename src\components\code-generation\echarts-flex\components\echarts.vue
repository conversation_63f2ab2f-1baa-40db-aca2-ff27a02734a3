<template>
  <div class="echarts-item" :style="{ height: newItemData?.comProps?.height + 'px' || '220px' }">
    <span class="delete">
      <EditOutlined :style="{ fontSize: '16px' }" @click="editHandle" style="margin-right: 10px" />
      <a-popconfirm title="是否确认删除?" ok-text="是" cancel-text="否" @confirm="delHandle">
        <DeleteOutlined :style="{ fontSize: '16px' }" />
      </a-popconfirm>
    </span>

    <span class="desc">{{ newModelValue.title }}</span>
  </div>

  <a-modal v-model:open="open" title="编辑" @ok="submit" @cancel="open = false">
    <a-form
      :model="newModelValue"
      name="basic"
      :label-col="{ span: 4 }"
      :wrapper-col="{ span: 16 }"
      autocomplete="off"
      ref="formRef"
    >
      <a-tabs v-model:activeKey="activeKey">
        <a-tab-pane key="1" tab="图表">
          <a-form-item
            label="图表名称"
            name="title"
            :rules="[{ required: true, message: '请输入图表名称' }]"
          >
            <a-input v-model:value="newModelValue.title" placeholder="请输入图表名称" />
          </a-form-item>
          <a-form-item
            label="图表高度"
            name="height"
            :rules="[{ required: true, message: '请输入图表高度' }]"
          >
            <a-input-number
              style="width: 100%"
              :step="10"
              v-model:value="newModelValue.height"
              placeholder="请输入图表高度"
            />
          </a-form-item>
          <a-form-item
            label="项目名称"
            name="project"
            :rules="[{ required: true, message: '请选择项目' }]"
          >
            <a-select
              v-model:value="newModelValue.project"
              placeholder="请选择对应文件"
              @change="handleChange"
            >
              <a-select-option
                v-for="(item, index) in projectData"
                :key="index"
                :value="item.project"
                >{{ item.project }}</a-select-option
              >
            </a-select>
          </a-form-item>
          <a-form-item
            label="模块"
            name="module"
            v-if="newModelValue.project"
            :rules="[{ required: true, message: '请选择模块' }]"
          >
            <a-select
              v-model:value="newModelValue.module"
              placeholder="请选择模块"
              @change="modulesChange"
            >
              <a-select-option
                v-for="(item, index) in modulesList"
                :key="index"
                :value="item.moduleName"
                >{{ item.moduleNameCn }} {{ item.moduleName }}</a-select-option
              >
            </a-select>
          </a-form-item>
          <a-form-item
            label="函数"
            name="interface"
            v-if="newModelValue.module"
            :rules="[{ required: true, message: '请选择函数' }]"
          >
            <a-select v-model:value="newModelValue.interface" placeholder="请选择函数">
              <a-select-option
                v-for="(item, index) in interfaceList"
                :key="index"
                :value="item.interfaceName"
                >{{ item.interfaceNameCn }} {{ item.interfaceName }}</a-select-option
              >
            </a-select>
          </a-form-item>
          <a-form-item label="搜索字段" name="search">
            <a-select v-model:value="newModelValue.search" placeholder="请选择搜索字段">
              <a-select-option
                v-for="(item, index) in selectData"
                :key="index"
                :value="item.name"
                >{{ item.name }}</a-select-option
              >
            </a-select>
          </a-form-item></a-tab-pane
        >
        <a-tab-pane key="2" tab="图表样式" force-render>
          <v-ace-editor
            v-model:value="jsonInput"
            lang="json"
            theme="chrome"
            style="width: 400px; height: 500px"
            :options="{ useWorker: true }"
          />
        </a-tab-pane>
      </a-tabs>
    </a-form>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { DeleteOutlined, EditOutlined } from '@ant-design/icons-vue'
import { VAceEditor } from 'vue3-ace-editor'
import 'ace-builds/src-noconflict/mode-json' // Load the language definition file used below
import 'ace-builds/src-noconflict/theme-chrome' // Load the theme definition file used below

import ace from 'ace-builds'
import workerJavascript from 'ace-builds/src-noconflict/worker-json?url' // For vite
ace.config.setModuleUrl('ace/mode/json_worker', workerJavascript)

interface echatsProps {
  data: any
  columnData: any
  index: number
  projectData: any[]
  selectData: any[]
}

const props: echatsProps = withDefaults(defineProps<echatsProps>(), {})

const newColumnData = computed(() => props.columnData)
const newItemData = computed(() => props.data)

const newModelValue = ref<any>({
  title: '图表',
  height: '300px',
})

const modulesList = ref<any[]>([])

const interfaceList = ref<any[]>([])

const open = ref(false)

const activeKey = ref('1')

const emit = defineEmits(['update:import'])
const jsonInput = ref(``)

const interfaceData = computed(() => newModelValue.value.interface)

watch(interfaceData, (val) => {
  newItemData.value.comProps.getOptions = val
})

watch(
  newItemData,
  (val) => {
    if (val) {
      newModelValue.value.title = val.comProps.title
    }
  },
  { immediate: true },
)

const submit = () => {
  emit('update:import', {
    project: newModelValue.value.project,
    module: newModelValue.value.module,
    interface: newModelValue.value.interface,
  })
  open.value = false
  newItemData.value.comProps.title = newModelValue.value.title
  newItemData.value.comProps.height = newModelValue.value.height
  newItemData.value.history = newModelValue.value
  newItemData.value.comProps.options = JSON.parse(jsonInput.value)
  if (newModelValue.value.search) {
    newItemData.value.comProps.search = newModelValue.value.search
  }
}
const delHandle = () => {
  newColumnData.value.children.splice(props.index, 1)
}

const handleChange = (params: string) => {
  const item = props.projectData?.find((item) => item.project === params)
  if (item) {
    modulesList.value = item.modules
  }
}

const modulesChange = (params: string) => {
  const item = modulesList.value.find((item) => item.moduleName === params)
  if (item) {
    interfaceList.value = item.interfaceInfos
  }
}

const editHandle = () => {
  open.value = true
  newModelValue.value.height = newItemData.value.comProps.height
  newModelValue.value.title = newItemData.value.comProps.title
  jsonInput.value = JSON.stringify(newItemData.value.comProps.options, null, 2)
  if (newItemData.value.history) {
    newModelValue.value = newItemData.value.history
    if (newModelValue.value.project) {
      handleChange(newModelValue.value.project)
      modulesChange(newModelValue.value.module)
    }
  }
}
</script>
<style scoped lang="less">
.echarts-item {
  height: 300px;
  width: 100%;
  background: #ccc;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  .delete {
    position: absolute;
    right: 15px;
    top: 15px;
    cursor: pointer;
  }
}
</style>
