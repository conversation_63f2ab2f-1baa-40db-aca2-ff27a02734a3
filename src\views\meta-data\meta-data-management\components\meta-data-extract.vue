<template>
  <div class="meta-data-extract">
    <div class="header-div">
      <a-button type="primary" @click="addExtractTask" :icon="h(PlusOutlined)"
        >新增提取任务</a-button
      >
    </div>
    <div class="content-div">
      <a-table
        :scroll="{ x: 1080 }"
        :columns="columns"
        :data-source="tablesRef"
        :pagination="{ pageSize: 10 }"
        :loading="loadingRef"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'triggerType'">
            <a-space class="action-container">
              <span v-if="record.triggerType == 1">自动</span>
              <span v-if="record.triggerType == 2">手动</span>
              <span v-if="record.triggerType == 3">实时</span>
            </a-space>
          </template>
          <template v-if="column.key === 'pushType'">
            <a-space class="action-container">
              {{ fmtPushType(record.pushType) }}
            </a-space>
          </template>
          <template v-if="column.key === 'pushDate'">
            <a-space class="action-container">
              {{ fmtPushDate(record.pushType, record.pushDate) }}
            </a-space>
          </template>
          <template v-if="column.key === 'status'">
            <a-space class="action-container">
              <a-tag color="green" v-if="record.status == 1">启用</a-tag>
              <a-tag color="red" v-if="record.status == 2">暂停</a-tag>
            </a-space>
          </template>
          <template v-if="column.key === 'operate'">
            <a-space class="action-container" :align="'center'">
              <a-button @click.prevent="onEnableTask(record)">
                {{ record.status === 1 ? '暂停' : '启用' }}
              </a-button>
              <a-button @click.prevent="onShowLog(record)">日志</a-button>
              <a-dropdown>
                <MoreOutlined />
                <template #overlay>
                  <a-menu>
                    <a-menu-item @click="onStartTask(record)">
                      <CaretRightOutlined /> 运行
                    </a-menu-item>
                    <a-menu-item @click="onModifyTask(record)"><EditOutlined /> 修改 </a-menu-item>
                    <a-menu-item @click="onDeleteTask(record)">
                      <span style="color: red"> <DeleteOutlined /> 删除</span>
                    </a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
            </a-space>
          </template>
        </template>
      </a-table>
    </div>
  </div>
  <a-modal v-model:open="modalOpenRef" title="提取任务" width="900px" destroyOnClose :footer="null">
    <ExtractTask
      :datasource-id="datasourceId"
      :datasource-name="dataSourceName"
      :database-name="databaseData.key"
      :task-info="taskInfoRef"
      :table-list="props.tableList"
      @finished="onFinishedModal"
      @cancel="onCloseModal"
      :parentKey="databaseData?.parent?.key"
      :schemaName="databaseData?.parent ? databaseData?.title : undefined"
    />
  </a-modal>
  <ExtractTaskLog v-model:visible="logModalVisible" :logs="logData" />
</template>

<script lang="ts" setup>
import { ref, watch, h } from 'vue'
import ExtractTask from './extract-task.vue'
import {
  taskPageList,
  enableTask,
  deleteTask,
  startTask,
} from '@/api/assetmanager/metadatamanage/metadatamanage'
import type { queryTabledescFun } from '@/api/matedata/metadatamanagement/metadatamanagement'
import {
  CaretRightOutlined,
  PlusOutlined,
  MoreOutlined,
  EditOutlined,
  DeleteOutlined,
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import ExtractTaskLog from './extract-task-log.vue'

type TableData = Awaited<ReturnType<typeof queryTabledescFun>>['data']

type RecordData = Awaited<ReturnType<typeof taskPageList>>['data'] & { tableConfigs: string }

const props = defineProps<{
  databaseData: Record<string, any>
  tableList: TableData['obj']
}>()

const taskInfoRef = ref<RecordData>()
const modalOpenRef = ref(false)
const databaseData = computed(() => props.databaseData)
const datasourceId = computed(() => databaseData.value.datasourceId)
const dataSourceName = computed(() => databaseData.value.dataSourceName)

const fmtPushType = (pushType: number | undefined) => {
  if (pushType === undefined || pushType === null) {
    return ''
  }

  if (pushType === 1) {
    return '每日'
  }
  if (pushType === 2) {
    return '每周'
  }
  if (pushType === 3) {
    return '每月'
  }
}

const fmtPushDate = (pushType: number | undefined, pushDate: string) => {
  if (pushType === 1 || pushType === undefined || pushType === null) {
    return ''
  }
  if (pushType === 2) {
    const d = pushDate === '1' ? '一' : '三'
    return `周${d}`
  }
  if (pushType === 3) {
    return `${pushDate}号`
  }
}

const addExtractTask = () => {
  modalOpenRef.value = true
  taskInfoRef.value = undefined
}

const onCloseModal = () => {
  modalOpenRef.value = false
}

const onFinishedModal = () => {
  modalOpenRef.value = false
  fetchTableData()
}

const onEnableTask = async (record: any) => {
  try {
    loadingRef.value = true
    await enableTask({ taskId: record.taskId, status: record.status === 1 ? 2 : 1 })
    await fetchTableData()
    message.success(`任务${record.status === 1 ? '暂停' : '启用'}成功`)
  } catch (error) {
    message.error(`任务${record.status === 1 ? '暂停' : '启用'}失败`)
  } finally {
    loadingRef.value = false
  }
}

const onDeleteTask = async (record: any) => {
  try {
    loadingRef.value = true
    await deleteTask({ taskId: record.taskId })
    await fetchTableData()
    message.success('任务删除成功')
  } catch (error) {
    message.error('任务删除失败')
  } finally {
    loadingRef.value = false
  }
}

const onStartTask = async (record: any) => {
  try {
    loadingRef.value = true
    await startTask({ taskId: record.taskId })
    await fetchTableData()
    message.success('任务运行成功')
  } catch (error) {
    message.error('任务运行失败')
  } finally {
    loadingRef.value = false
  }
}

const onModifyTask = (record: any) => {
  modalOpenRef.value = true
  taskInfoRef.value = record
}

type Response = {
  pageIndex: number
  pageSize: number
  records: Array<RecordData>
  totalPages: number
  totalRecords: number
}
const tablesRef = ref<Response['records']>([])
const loadingRef = ref<boolean>(false)

const columns = ref<any[]>([
  {
    dataIndex: 'taskName',
    title: '任务名称',
    key: 'taskName',
    width: 150,
  },
  {
    dataIndex: 'triggerType',
    title: '触发方式',
    key: 'triggerType',
    width: 100,
  },
  {
    dataIndex: 'pushType',
    title: '推送方式',
    key: 'pushType',
    width: 100,
  },
  {
    dataIndex: 'pushDate',
    title: '推送日期',
    key: 'pushDate',
    width: 100,
  },
  {
    dataIndex: 'pushTime',
    title: '推送时间',
    key: 'pushTime',
    width: 100,
  },
  {
    dataIndex: 'status',
    title: '任务状态',
    key: 'status',
    width: 100,
  },
  {
    dataIndex: 'description',
    title: '任务描述',
    key: 'description',
    width: 200,
  },
  {
    dataIndex: 'operate',
    title: '操作',
    key: 'operate',
    width: 160,
    fixed: 'right',
  },
])

const searchFormData = computed(() => {
  return {
    datasourceId: databaseData.value.datasourceId,
    databseName: databaseData.value?.parent?.key || databaseData.value.key,
    pageIndex: 1,
    pageSize: 10,
  }
})

const fetchTableData = async () => {
  try {
    loadingRef.value = true
    await taskPageList(searchFormData.value).then((res) => {
      console.log('🚀 ~ fetchTableData ~ res:', res)
      if (res?.data) {
        tablesRef.value = (res.data as unknown as Response).records
      }
    })
  } catch (error) {
    console.error('获取数据表列表失败:', error)
  } finally {
    loadingRef.value = false
  }
}

watch(
  databaseData,
  (newVal) => {
    console.log('🚀 ~ watch ~ newVal:', newVal)
    fetchTableData()
  },
  { immediate: true, deep: true },
)

const logModalVisible = ref(false)
const logData = ref([])

const onShowLog = (record: any) => {
  // 这里可以根据 record.taskId 请求日志数据，示例用假数据
  logData.value = record
  logModalVisible.value = true
}
</script>

<style lang="less" scoped>
.meta-data-extract {
  height: 100%;
  .header-div {
    display: flex;
    align-items: center;
    height: 48px;
    justify-content: flex-end;
  }
  .content-div {
    padding: 12px 0;
    height: calc(100% - 48px);
  }
}
:deep(.fs-table .search-btn) {
  display: none;
}
:deep(.ant-btn:hover) {
  .clear-icon {
    opacity: 1 !important;
  }
}
</style>
