<script lang="ts" setup>
import { reqGetRuleList } from '@/api/services/knowledge'
import { useRouter, useRoute } from 'vue-router'
import { onMounted, ref } from 'vue'
import { columns } from './source-data'
const router = useRouter()
const route = useRoute()

const tableData = ref([])
const value = ref('')

const getRuleList = async () => {
  const res = await reqGetRuleList({
    projectId: route.query?.projectId,
  })
  tableData.value = res
  console.log('获取到数据了', res)
}

const onSearch = () => {
  console.log('search...')
}

onMounted(() => {
  getRuleList()
})
</script>

<template>
  <div class="rule-manager" style="margin-bottom: 16px">
    <a-page-header title="规则列表" @back="() => router.back()">
      <template #extra>
        <a-input-search
          v-model:value="value"
          placeholder="请输入关联实体、概念、关系"
          style="width: 200px"
          @search="onSearch"
        />
      </template>
    </a-page-header>
    <a-table :columns="columns" :dataSource="tableData" :pagination="false"> </a-table>
  </div>
</template>

<style lang="less" scoped>
:deep(.ant-page-header) {
  padding: 0;
}
</style>
