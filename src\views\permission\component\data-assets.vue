<template>
  <a-modal
    v-model:visible="visible"
    title="分配数据资产"
    @ok="handleOk"
    @cancel="handleCancel"
    :width="1200"
    :confirmLoading="confirmLoading"
    wrapClassName="data-assets-modal"
    :destroyOnClose="true"
  >
    <a-row class="content-row">
      <a-col flex="400px" class="left-col">
        <a-tree
          v-model:selectedKeys="selectedTreeKeys"
          v-model:checkedKeys="checkedTreeKeys"
          :tree-data="treeData"
          :checkable="true"
          @select="onTreeSelect"
          @check="onTreeCheck"
          :checkStrictly="true"
        >
          <template #title="{ title, dataRef }">
            <div class="tree-node-content" :title="title">
              <FolderOutlined
                style="font-size: 18px; margin-right: 10px; color: #677164"
                v-if="dataRef.nodeType === 1"
              />
              <type-icon
                v-else-if="dataRef.nodeType === 2"
                style="margin-right: 10px"
                :type="dataRef?.databaseType"
              />
              <TableOutlined
                style="font-size: 18px; margin-right: 10px; color: #677164"
                v-else-if="dataRef.nodeType === 3"
              />
              <span class="node-name">
                {{ title }}
              </span>
            </div>
          </template>
        </a-tree>
      </a-col>
      <a-col flex="auto" class="right-col">
        <div class="button-wrapper">
          <span class="table-name">{{ selectInfo?.tableName }}</span>
          <a-button
            type="primary"
            @click="openHandleColumn"
            v-if="selectInfo && selectInfo.nodeType === 3"
            >行控制</a-button
          >
        </div>
        <a-table
          :columns="columns"
          :data-source="dataSource"
          :row-selection="{
            selectedRowKeys: selectedColumns,
            onChange: onSelectionChange,
            type: 'checkbox',
          }"
          :pagination="false"
          rowKey="columnName"
          :scroll="{ y: 500 }"
        >
        </a-table>
      </a-col>
    </a-row>
  </a-modal>

  <a-modal
    v-model:open="open"
    title="行控制"
    destroyOnClose
    width="700px"
    @ok="handleOkColumn"
    @cancel="handleCancelColumn"
  >
    <a-form
      ref="formRef"
      :model="formState"
      name="basic"
      :label-col="{ span: 5 }"
      :wrapper-col="{ span: 16 }"
    >
      <a-form-item label="行配置" name="configValue">
        <a-space
          v-for="(user, index) in formState.configValue"
          :key="user.id"
          style="display: flex"
          align="baseline"
        >
          <a-form-item style="width: 120px" :name="['configValue', index, 'columnName']">
            <a-select
              placeholder="columnName"
              :options="paramList"
              :fieldNames="{ label: 'columnName', value: 'columnName' }"
              v-model:value="user.columnName"
            ></a-select>
          </a-form-item>
          <a-form-item style="width: 80px" :name="['configValue', index, 'operator']">
            <a-select
              placeholder="operator"
              :options="[
                { label: '大于', value: '>' },
                { label: '小于', value: '<' },
                { label: '等于', value: '=' },
                { label: '不等于', value: '!=' },
              ]"
              v-model:value="user.operator"
            ></a-select>
          </a-form-item>
          <a-form-item :name="['configValue', index, 'value']">
            <a-input v-model:value="user.value" placeholder="value" />
          </a-form-item>
          <MinusCircleOutlined @click="removeUser(user)" />
        </a-space>
        <a-button type="dashed" block @click="addUser">
          <PlusOutlined />
          添加行配置
        </a-button>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import type { TableColumnType } from 'ant-design-vue'
import { reqGetParamsList } from '@/views/data-asset/data-asset-management/auth-page/api/index'
import {
  MinusCircleOutlined,
  PlusOutlined,
  FolderOutlined,
  TableOutlined,
} from '@ant-design/icons-vue'
import {
  getDiretoryTree,
  getColumnsListByRoleId,
  saveDataPermissionRole,
} from '@/api/assetmanager/dataassertmanage/dataassertmanage'
import TypeIcon from '@/views/data-asset/metadata-query/components/type-icon.vue'
import { message } from 'ant-design-vue'

interface DataItem {
  key: string
  name: string
  description: string
}

interface FormState {
  userIdList: string | string[]
  columns: string[]
  configValue: ConfigValueType[]
}

interface ConfigValueType {
  columnName: string | undefined
  operator: string | undefined
  value: string | undefined
  id: number
}

interface TreeNode {
  title: string
  key: string
  tableId?: string
  columns?: string
  tableName?: string
  configValue?: ConfigValueType[]
  children?: TreeNode[]
}

const visible = ref(false)
const open = ref(false)
const selectedRows = ref<DataItem[]>([])
const confirmLoading = ref(false)
const selectInfo = ref<any>(null)
const tableList = ref<any[]>([])
const checkedAllData = ref<any[]>([])
const roleData = ref<any>({})
const emit = defineEmits(['success'])

const formState = reactive<FormState>({
  userIdList: [],
  columns: [],
  configValue: [
    {
      columnName: undefined,
      operator: undefined,
      value: undefined,
      id: Date.now(),
    },
  ],
})

// 表格列定义
const columns: TableColumnType[] = [
  {
    title: '列名',
    dataIndex: 'columnName',
    key: 'columnName',
  },
  {
    title: '注释',
    dataIndex: 'columnComment',
    key: 'columnComment',
  },
]

// 模拟数据
const dataSource = ref<any[]>([])
const paramList = ref<any[]>([])
const selectedColumns = ref<string[]>([])

const onSelectionChange = (selectedKeys: string[]) => {
  selectedColumns.value = selectedKeys
  const index = tableList.value.findIndex((item: any) => selectInfo.value.key === item.key)
  selectInfo.value.columns = selectedKeys
  if (index !== -1) {
    tableList.value[index].columns = selectedColumns.value
  }
}

// 树形数据
const treeData = ref<TreeNode[]>([])

const selectedTreeKeys = ref<string[]>([])
const checkedTreeKeys = ref<any>({})

// 获取所有子节点的key
const getAllChildrenKeys = (node: any): string[] => {
  const keys: string[] = []
  if (node.key) {
    keys.push(node.key)
  }
  if (node.children && node.children.length > 0) {
    node.children.forEach((child: any) => {
      keys.push(...getAllChildrenKeys(child))
    })
  }
  return keys
}

// 获取节点及其所有子节点
const getNodeAndChildren = (node: any): any[] => {
  const nodes: any[] = [node]
  if (node.children && node.children.length > 0) {
    node.children.forEach((child: any) => {
      nodes.push(...getNodeAndChildren(child))
    })
  }
  return nodes
}

// 在树中查找节点
const findNodeByKey = (key: string, nodes: TreeNode[]): TreeNode | null => {
  for (const node of nodes) {
    if (node.key === key) {
      return node
    }
    if (node.children) {
      const found = findNodeByKey(key, node.children)
      if (found) {
        return found
      }
    }
  }
  return null
}

// 在 script setup 部分添加获取父节点的方法
const getParentKey = (key: string, tree: TreeNode[]): string | null => {
  let parentKey = null
  for (const node of tree) {
    if (node.children) {
      if (node.children.some((item: TreeNode) => item.key === key)) {
        parentKey = node.key
      } else {
        const grandParentKey = getParentKey(key, node.children)
        if (grandParentKey) {
          parentKey = grandParentKey
        }
      }
    }
  }
  return parentKey
}

// 树节点选择事件处理
const onTreeSelect = async (selectedKeys: string[], info: any) => {
  const { dataRef, checked } = info.node
  if (dataRef.nodeType !== 3) return
  const res = await getColumnsListByRoleId({
    roleId: roleData.value.roleId,
    tableId: dataRef.tableId,
  })
  dataSource.value = res.data
  // dataRef.columns = res.data
  if (checked) {
    if (dataRef.columns) {
      selectedColumns.value = dataRef.columns
    } else {
      selectedColumns.value = res.data.map((item: any) => item.columnName)
    }
  }

  const index = tableList.value.findIndex((item: any) => dataRef.tableId === item.tableId)
  if (index === -1) {
    tableList.value.push({
      ...dataRef,
      selected: 'YES',
      configValue: '',
    })
    selectInfo.value = tableList.value[tableList.value.length - 1]
  } else {
    selectInfo.value = tableList.value[index]

    // selectInfo.value.columns = res.data.map((item: any) => item.columnName)
  }
}

// 树节点勾选事件处理
const onTreeCheck = (checkedKeys: any, info: any) => {
  const { checked, node } = info
  let newCheckedKeys = Array.isArray(checkedKeys) ? checkedKeys : checkedKeys.checked || []

  if (checked) {
    // 获取所有父节点的key
    let parentKey = getParentKey(node.key, treeData.value)
    while (parentKey) {
      if (!newCheckedKeys.includes(parentKey)) {
        newCheckedKeys.push(parentKey)
      }
      parentKey = getParentKey(parentKey, treeData.value)
    }
    node.dataRef.selected = 'YES'
    // 获取所有子节点的key
    const allChildrenKeys = getAllChildrenKeys(node)
    newCheckedKeys = Array.from(new Set([...newCheckedKeys, ...allChildrenKeys]))

    // 获取所有子节点数据
    const allNodes = getNodeAndChildren(node)
    allNodes.forEach((node) => {
      const index = tableList.value.findIndex((item: any) => node.tableId === item.tableId)
      if (index === -1 && node.nodeType === 3) {
        console.log('🚀 ~ allNodes.forEach ~ node:', node)
        const obj = node?.dataRef || node
        tableList.value.push({
          ...obj,
          selected: 'YES',
          configValue: null,
        })
      }
    })
    console.log('🚀 ~ allNodes.forEach ~ tableList.value:', tableList.value)
    // 表格选中
    if (selectInfo.value && node.dataRef.key === selectInfo.value.key) {
      selectInfo.value.columns = dataSource.value.map((item: any) => item.columnName)
      const val = tableList.value.find((item: any) => item.key === node.dataRef.key)
      if (val) {
        val.columns = selectInfo.value.columns
      }
      selectedColumns.value = dataSource.value.map((item: any) => item.columnName)
    }
  } else {
    // 取消勾选时的逻辑保持不变
    const allChildrenKeys = getAllChildrenKeys(node)
    newCheckedKeys = newCheckedKeys.filter((key: any) => !allChildrenKeys.includes(key))

    const allNodes = getNodeAndChildren(node)
    allNodes.forEach((node) => {
      const index = tableList.value.findIndex((item: any) => node.tableId === item.tableId)
      if (index !== -1) {
        tableList.value.splice(index, 1)
      }
    })
    node.dataRef.selected = 'NO'
    // 取消表格已选中
    if (selectInfo.value && node.dataRef.key === selectInfo.value.key) {
      selectInfo.value.columns = []
      selectedColumns.value = []
    }
  }

  checkedTreeKeys.value = { checked: newCheckedKeys, halfChecked: [] }
  checkedAllData.value = newCheckedKeys
    .map((key: any) => findNodeByKey(key, treeData.value))
    .filter(Boolean)
}

// 处理树形数据的函数
const handleTreeData = (data: any[]): TreeNode[] => {
  return data.map((item) => {
    const node: TreeNode = {
      ...item,
      title: getNodeTitle(item),
      key: getNodeKey(item),
      children: item.children ? handleTreeData(item.children) : undefined,
    }
    if (item?.columns) {
      node.columns = item.columns.split(',')
    }
    if (item?.configValue) {
      node.configValue = JSON.parse(item.configValue)
    }
    return node
  })
}

const getNodeTitle = (item: any) => {
  switch (item.nodeType) {
    case 1:
      return item.name
    case 2:
      return item.databaseName
    case 3:
      return item.tableName
    default:
      return ''
  }
}

const getNodeKey = (item: any) => {
  switch (item.nodeType) {
    case 1:
      return item.directoryId
    case 2:
      return item.databaseId
    case 3:
      return item.tableId
    default:
      return ''
  }
}

// 添加一个递归函数来获取所有选中的节点
const getSelectedKeys = (nodes: any[]): string[] => {
  let keys: string[] = []
  nodes.forEach((node) => {
    if (node.checked === 'YES') {
      keys.push(node.key)
      // 表格数据
      if (node.nodeType === 3) {
        tableList.value.push({
          ...node,
        })
        // 目录数据和数据库数据
      } else {
        checkedAllData.value.push({
          ...node,
        })
      }
    }
    if (node.children && node.children.length > 0) {
      keys = [...keys, ...getSelectedKeys(node.children)]
    }
  })
  return keys
}

// 修改 showModal 方法
const showModal = async (record: any) => {
  roleData.value = record
  selectedColumns.value = []
  dataSource.value = []
  tableList.value = []
  selectedTreeKeys.value = []
  selectInfo.value = null
  visible.value = true
  checkedAllData.value = []
  try {
    // 初始化加载所有数据
    const res = await getDiretoryTree({ roleId: record.roleId })
    treeData.value = handleTreeData(res.data)

    // 获取所有选中的节点的key
    checkedTreeKeys.value = getSelectedKeys(treeData.value)
  } catch (error) {
    console.error('获取项目列表时发生错误:', error)
  }
}

// 确认按钮处理函数
const handleOk = async () => {
  // 设置loading状态
  confirmLoading.value = true

  try {
    console.log('🚀 ~ tableList.value.forEach ~ tableList.value:', tableList.value)

    tableList.value.forEach((item: any) => {
      if (item?.columns) {
        item.selected = 'NO'
      } else {
        item.columns = ''
      }
      item.columns = (item?.columns && item.columns.join(',')) || ''
      item.configValue = item?.configValue
        ? JSON.stringify(
            item.configValue.map((item: ConfigValueType) => ({
              columnName: item.columnName,
              operator: item.operator,
              value: item.value,
            })),
          )
        : ''
    })
    const directoryList = checkedAllData.value

      .filter((item: any) => item.nodeType === 1)
      .map((item: any) => item.key)
    const databaseList = checkedAllData.value
      .filter((item: any) => item.nodeType === 2)
      .map((item: any) => {
        return {
          databaseId: item.key,
          databaseName: item.databaseName,
        }
      })
    const obj = {
      directoryList,
      databaseList,
      tableList: tableList.value,
      roleId: roleData.value.roleId,
      roleName: roleData.value.roleName,
    }

    await saveDataPermissionRole(obj)
    emit('success', obj)
    message.success('操作成功')
    visible.value = false
    confirmLoading.value = false
  } catch (error) {
    confirmLoading.value = false
  }
}

// 取消按钮处理函数
const handleCancel = () => {
  visible.value = false
  selectedRows.value = []
}

const addUser = () => {
  formState.configValue.push({
    columnName: undefined,
    operator: undefined,
    value: undefined,
    id: Date.now(),
  })
}

const handleOkColumn = () => {
  if (selectInfo.value) {
    selectInfo.value.configValue = formState.configValue
  }
  open.value = false
}

const handleCancelColumn = () => {
  open.value = false
}

const removeUser = (item: ConfigValueType) => {
  const index = formState.configValue.indexOf(item)
  if (index !== -1) {
    formState.configValue.splice(index, 1)
  }
}

const openHandleColumn = () => {
  open.value = true
  formState.configValue = selectInfo.value?.configValue || []
  paramList.value = dataSource.value
  console.log('🚀 ~ openHandleColumn ~ formState.configValue:', formState.configValue)
}

// 导出方法供父组件使用
defineExpose({
  showModal,
})
</script>

<style lang="less">
.data-assets-modal {
  .content-row {
    border: 1px solid #f0f0f0;
    min-height: 400px;
    display: flex;
    flex-wrap: nowrap;
  }

  .left-col {
    padding: 16px;
    border-right: 1px solid #f0f0f0;
    height: 600px;
    overflow-y: auto;
  }

  .right-col {
    padding: 16px;

    .button-wrapper {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      .table-name {
        font-size: 16px;
        font-weight: 600;
      }
    }
  }
  .tree-node-content {
    display: flex;
    align-items: center;
    white-space: nowrap; /* 禁止换行 */
    overflow: hidden; /* 隐藏溢出内容 */
    text-overflow: ellipsis; /* 显示省略号 */
  }
}
</style>
