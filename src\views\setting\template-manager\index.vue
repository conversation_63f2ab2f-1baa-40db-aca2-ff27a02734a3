<script setup lang="ts">
import { ref } from 'vue'
import CodeTemplate from '../code-template/index.vue'
import ScriptTemplate from '../script-template/index.vue'

const activeTabKey = ref('1')

const onTabChange = (key: string) => {
  activeTabKey.value = key
}
</script>

<template>
  <div class="service-config-container">
    <a-tabs :active-key="activeTabKey" @change="onTabChange">
      <a-tab-pane key="1" tab="Java模板">
        <CodeTemplate />
      </a-tab-pane>
      <a-tab-pane key="2" tab="Python/Shell模板">
        <ScriptTemplate />
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<style lang="less" scoped>
.service-config-container {
  width: 100%;
  height: 100%;
  background-color: #fff;
}
:deep(.ant-tabs) {
  .ant-tabs-tab {
    margin: 0 32px 0 0;
    padding: 12px 16px;
  }
  .ant-tabs-nav {
    margin-bottom: 0 !important;
  }
}
</style>
