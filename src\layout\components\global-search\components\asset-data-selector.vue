<script setup lang="ts">
import {
  type AssetTableInfo,
  AssetTreeData,
  type ColumnInfo,
  getDataAssetList,
  getTreeData,
  nodeTypeEnum,
  queryAssetTableInfoList,
  type AssetTableData,
} from '../search'
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import TypeIcon from '@/views/data-asset/metadata-query/components/type-icon.vue'

// 定义emit事件
const emit = defineEmits<{
  cancel: []
  confirm: [data: { databases: string[]; tables: string[]; schemas: string[] }]
}>()

// 响应式数据
const selectedRowKeys = ref<string[]>([])
const expandedRowKeys = ref<string[]>([])
const treeData = ref<AssetTreeData[]>([])
const checkedDatabaseIds = ref<string[]>([])
const treeLoading = ref(false)
const tableLoading = ref(false)
const curDatabaseName = ref('')
const curDatabaseId = ref('')

// 分页配置
const pagination = reactive({
  pageSize: 10,
  current: 1,
  total: 0,
  size: 'small' as const,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条`,
  pageSizeOptions: ['10', '50', '100', '200'],
})

// 表格列配置
const columns = [
  {
    title: '名称',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '注释',
    dataIndex: 'comment',
    key: 'comment',
  },
]

// 计算属性：当前数据库的表数据
const curTableData = computed((): AssetTableData[] => {
  const foundedTreeNode = findTreeNode(treeData.value, curDatabaseId.value)
  return foundedTreeNode ? foundedTreeNode.tableData || [] : []
})

// 初始化树数据
const initTreeData = async () => {
  try {
    treeLoading.value = true
    const res = await getTreeData()
    treeData.value = res
  } catch (error) {
    console.error('initTreeData error', error)
  } finally {
    treeLoading.value = false
  }
}

// 加载数据
const loadData = async (node: any) => {
  const nodeData = node.dataRef as AssetTreeData
  if (nodeData.nodeType === nodeTypeEnum.DIRECTORY) {
    if (
      nodeData.children &&
      nodeData.children.some((child) => child.nodeType === nodeTypeEnum.DATABASE)
    ) {
      // 如果目录节点有数据库子节点，则不加载
      return
    }
    // 目录节点，需要获取该目录下的数据库列表
    const res = await getDataAssetList({
      directoryId: Number(nodeData.directoryId),
    })
    if (res && res.code === '000000' && Array.isArray(res.data)) {
      let assetList =
        res.data.map((item: any) => ({
          key: item.databaseId,
          title: item.databaseName,
          nodeType: nodeTypeEnum.DATABASE,
          databaseId: item.databaseId,
          databaseName: item.databaseName,
          directoryId: String(item.directoryId),
          databaseType: item.databaseType,
          tableCount: item.tableCount,
          parentId: nodeData.directoryId,
        })) || []

      if (assetList.length > 0) {
        const newNode = {
          ...nodeData,
          children: [...(nodeData.children || []), ...assetList],
        } as AssetTreeData
        updateTreeData(newNode)
      }
    }
  }
}

// 选择树节点
const onSelectTreeNode = async (
  selectedKeys: string[],
  info: { selected: boolean; selectedNodes: any; node: any; event: any },
) => {
  const nodeData = info.node.dataRef as AssetTreeData
  const { nodeType, databaseName, databaseId } = nodeData
  if (nodeType === nodeTypeEnum.DATABASE) {
    if (databaseId && curDatabaseId.value !== databaseId) {
      curDatabaseName.value = databaseName || ''
      curDatabaseId.value = databaseId
      loadTableData(databaseId)
    }
  }
}

// 全选/取消全选数据库下的表
const checkAllTableRows = (nodeKey: string, checked: boolean) => {
  const foundedTreeNode = findTreeNode(treeData.value, nodeKey)
  if (foundedTreeNode && foundedTreeNode.tableData && foundedTreeNode.tableData.length > 0) {
    // 全选数据库下的表
    const newTableRowKeys = foundedTreeNode.tableData.map((item) => item.key)
    if (checked) {
      selectedRowKeys.value = Array.from(new Set([...selectedRowKeys.value, ...newTableRowKeys]))
    } else {
      selectedRowKeys.value = selectedRowKeys.value.filter((key) => !newTableRowKeys.includes(key))
    }
  }
}

// 检查树节点
const onCheckTreeNode = (
  checkedKeys: string[],
  e: { checked: boolean; checkedNodes: any; node: any; event: any },
) => {
  const prevCheckedKeys = [...checkedDatabaseIds.value]
  checkedDatabaseIds.value = checkedKeys
  if (checkedKeys.length > 0) {
    if (e.checked) {
      checkAllTableRows(checkedKeys[checkedKeys.length - 1], true)
    } else {
      // 比对 checkedKeys和this.checkedDatabaseIds, 筛选出取消选中的节点id
      const uncheckedIds = prevCheckedKeys.filter((id) => !checkedKeys.includes(id))
      // uncheckedIds 就是本次被取消选中的节点 id 数组
      uncheckedIds.forEach((id) => {
        checkAllTableRows(id, false)
      })
    }
  } else {
    // 清空 selectedKeys
    selectedRowKeys.value = []
  }
}

// 更新树节点数据
const updateTreeData = (node: AssetTreeData) => {
  const recur = (list: AssetTreeData[]) => {
    for (const item of list) {
      if (item.key === node.key) {
        item.children = node.children
        return
      }
      if (item.children) {
        recur(item.children)
      }
    }
  }
  const copyData = JSON.parse(JSON.stringify(treeData.value))
  recur(copyData)
  treeData.value = copyData
}

// 递归查找节点
const findTreeNode = (list: AssetTreeData[], key: string): AssetTreeData | null => {
  for (const item of list) {
    if (item.key === key) {
      return item
    }
    if (item.children) {
      const founded = findTreeNode(item.children, key)
      if (founded) {
        return founded
      }
    }
  }
  return null
}

// 加载表数据
const loadTableData = async (databaseId: string) => {
  try {
    tableLoading.value = true
    const res = await queryAssetTableInfoList(
      {
        databaseId,
        pageIndex: pagination.current,
        pageSize: pagination.pageSize,
      },
      false,
    )
    if (res && res.code === '000000' && Array.isArray(res.data.records)) {
      const tableData = convertTableData(res.data.records)
      const foundedTreeNode = findTreeNode(treeData.value, databaseId)
      if (foundedTreeNode) {
        foundedTreeNode.tableData = tableData
        const flag = !tableData || tableData.length === 0
        foundedTreeNode.disableCheckbox = flag
        updateTreeData(foundedTreeNode)
      }
      pagination.total = res.data.totalRecords
    }
  } catch (error) {
    console.error('loadTableData error', error)
  } finally {
    tableLoading.value = false
  }
}

// 表格分页变化
const onTableChange = (paginationInfo: any) => {
  const { current, pageSize } = paginationInfo
  pagination.current = current
  pagination.pageSize = pageSize
  loadTableData(curDatabaseId.value)
}

// 将后端数据转换为前端数据
const convertTableData = (backendData: AssetTableInfo[]): AssetTableData[] => {
  const result: AssetTableData[] = []
  for (const item of backendData) {
    const data: AssetTableData = {
      key: item.tableId,
      name: item.tableName,
      comment: item.tableComment || '',
      nodeType: nodeTypeEnum.TABLE,
      tableSchema: item.tableSchema || '',
      databaseName: item.databaseName || '',
    }
    if (item.columnList) {
      data.children = item.columnList.map((column: ColumnInfo, index: number) => ({
        key: `${column.columnId}_${column.columnName}_${index}`,
        name: column.columnName,
        comment: column.columnComment || '',
        nodeType: nodeTypeEnum.COLUMN,
        parentKey: item.tableId,
      }))
    }
    result.push(data)
  }
  return result
}

// 计算节点的选择状态
const getNodeSelectionState = (record: AssetTableData): 'none' | 'partial' | 'all' => {
  if (!record.children || record.children.length === 0) {
    return selectedRowKeys.value.includes(record.key) ? 'all' : 'none'
  }
  const childKeys = record.children.map((child) => child.key)
  const selectedChildKeys = childKeys.filter((key) => selectedRowKeys.value.includes(key))
  if (selectedChildKeys.length === 0) {
    return 'none'
  } else if (selectedChildKeys.length === childKeys.length) {
    return 'all'
  } else {
    return 'partial'
  }
}

// 处理列节点的选择
const handleColumnSelect = (columnRecord: AssetTableData, selected: boolean): void => {
  if (selected) {
    if (!selectedRowKeys.value.includes(columnRecord.key)) {
      selectedRowKeys.value.push(columnRecord.key)
    }
  } else {
    selectedRowKeys.value = selectedRowKeys.value.filter((key) => key !== columnRecord.key)
  }
  // updateParentTableState(columnRecord)
}

// 智能更新父表状态
const updateParentTableState = (columnRecord: AssetTableData): void => {
  const parentTable = curTableData.value.find(
    (table) => table.children && table.children.some((child) => child.key === columnRecord.key),
  )
  if (!parentTable) return
  const selectionState = getNodeSelectionState(parentTable)
  if (selectionState === 'all') {
    if (!selectedRowKeys.value.includes(parentTable.key)) {
      selectedRowKeys.value.push(parentTable.key)
    }
  } else {
    selectedRowKeys.value = selectedRowKeys.value.filter((key) => key !== parentTable.key)
  }
}

// 选中节点及其所有子节点
const selectWithChildren = (record: AssetTableData): void => {
  // 确保数组已初始化
  if (!Array.isArray(selectedRowKeys.value)) {
    selectedRowKeys.value = []
  }

  const keysToSelect: string[] = []

  // 如果是表节点，选中表本身和所有子节点
  if (record.nodeType === nodeTypeEnum.TABLE) {
    keysToSelect.push(record.key) // 表节点自身
    // 递归收集所有子节点的 key
    collectChildrenKeys(record, keysToSelect)
  } else {
    // 如果是列节点，只选中自身
    keysToSelect.push(record.key)
  }

  // 添加到选中列表（去重）
  const currentSelected = selectedRowKeys.value || []
  const newSelectedKeys = [...new Set([...currentSelected, ...keysToSelect])]
  selectedRowKeys.value = newSelectedKeys

  checkedDatabaseIds.value = [...new Set([...checkedDatabaseIds.value, curDatabaseId.value])]
}

// 取消选中节点及其所有子节点
const deselectWithChildren = (record: AssetTableData): void => {
  // 确保数组已初始化
  if (!Array.isArray(selectedRowKeys.value)) {
    selectedRowKeys.value = []
    return
  }

  const keysToDeselect: string[] = [record.key]

  // 递归收集所有子节点的 key
  collectChildrenKeys(record, keysToDeselect)

  // 从选中列表中移除
  selectedRowKeys.value = selectedRowKeys.value.filter(
    (key: string) => !keysToDeselect.includes(key),
  )
  const foundedTreeNode = findTreeNode(treeData.value, curDatabaseId.value)
  if (foundedTreeNode) {
    const tableData = foundedTreeNode.tableData || []
    const tableKeys = tableData.map((item) => item.key)
    // 如果数据库下的所有表都未选中，则取消选中数据库
    if (tableKeys.every((key) => !selectedRowKeys.value.includes(key))) {
      checkedDatabaseIds.value = checkedDatabaseIds.value.filter((id) => id !== curDatabaseId.value)
    }
  }
}

// 递归收集子节点的 key
const collectChildrenKeys = (node: AssetTableData, keysList: string[]): void => {
  if (node && node.children && Array.isArray(node.children) && node.children.length > 0) {
    for (const child of node.children) {
      if (child && child.key) {
        keysList.push(child.key)
        // 递归处理子节点的子节点
        collectChildrenKeys(child, keysList)
      }
    }
  }
}

// 取消操作
const handleCancel = (): void => {
  emit('cancel')
}

// 确认操作
const handleConfirm = (): void => {
  const databases: string[] = []
  const tables: string[] = []
  const schemas: string[] = []

  for (const databaseId of checkedDatabaseIds.value) {
    const foundedTreeNode = findTreeNode(treeData.value, databaseId)
    if (foundedTreeNode) {
      const tableData = foundedTreeNode.tableData || []
      const tableItems = tableData.filter((item) => selectedRowKeys.value.includes(item.key))
      if (tableItems.length > 0) {
        databases.push(...tableItems.map((item) => item.databaseName))
        tables.push(...tableItems.map((item) => item.name))
        schemas.push(...tableItems.map((item) => item.tableSchema || ''))
      }
    }
  }
  emit('confirm', {
    databases: [...new Set(databases)],
    tables: [...new Set(tables)],
    schemas: [...new Set(schemas)],
  })
}

// 表格行选择配置
const rowSelection = computed(() => ({
  selectedRowKeys: selectedRowKeys.value,
  getCheckboxProps: (record: AssetTableData) => {
    const selectionState = getNodeSelectionState(record)
    return {
      props: {
        indeterminate: selectionState === 'partial',
      },
    }
  },
  onChange: (selectedKeys: string[], selectedRows: AssetTableData[]) => {
    selectedRowKeys.value = selectedKeys || []
  },
  onSelect: (record: AssetTableData, selected: boolean, selectedRows: AssetTableData[]) => {
    nextTick(() => {
      if (record.nodeType === nodeTypeEnum.TABLE) {
        if (selected) {
          selectWithChildren(record)
        } else {
          deselectWithChildren(record)
        }
      } else {
        handleColumnSelect(record, selected)
      }
    })
  },
  onSelectAll: (
    selected: boolean,
    selectedRows: AssetTableData[],
    changeRows: AssetTableData[],
  ) => {
    // 可选：全选/全不选逻辑
    if (selected) {
      checkedDatabaseIds.value = [...new Set([...checkedDatabaseIds.value, curDatabaseId.value])]
    } else {
      checkedDatabaseIds.value = checkedDatabaseIds.value.filter((id) => id !== curDatabaseId.value)
    }
  },
}))

// 组件挂载时初始化
onMounted(() => {
  initTreeData()
})
</script>
<template>
  <div class="asset-data-selector-container">
    <div class="content">
      <!-- 左边面板，资产数据选择 -->
      <div class="left-panel">
        <div class="left-panel-header">
          <span>标题</span>
        </div>
        <div class="left-panel-content">
          <a-spin :spinning="treeLoading">
            <a-tree
              :tree-data="treeData"
              :checkable="true"
              :load-data="loadData"
              :checked-keys="checkedDatabaseIds"
              style="min-height: 200px"
              @select="onSelectTreeNode"
              @check="onCheckTreeNode"
            >
              <template #title="nodeData">
                <div v-if="nodeData.nodeType === nodeTypeEnum.DATABASE" class="tree-node-title">
                  <type-icon style="margin-right: 10px" :type="nodeData.databaseType" />
                  {{ nodeData.title }}
                </div>
                <div v-else class="tree-node-title">
                  {{ nodeData.title }}
                </div>
              </template>
            </a-tree>
          </a-spin>
        </div>
      </div>
      <!-- 右边面板，表格展示 -->
      <div class="right-panel">
        <a-table
          :columns="columns"
          :data-source="curTableData"
          :loading="tableLoading"
          :row-selection="rowSelection"
          v-model:expanded-row-keys="expandedRowKeys"
          :scroll="{ y: 340 }"
          :pagination="pagination"
          @change="onTableChange"
        />
      </div>
    </div>
    <a-divider style="margin: 10px 0" />
    <div class="selector-footer">
      <a-button @click="handleCancel">取消</a-button>
      <a-button type="primary" @click="handleConfirm">确定</a-button>
    </div>
  </div>
</template>
<style lang="less" scoped>
.asset-data-selector-container {
  width: 100%;
  height: 500px;
  display: flex;
  flex-direction: column;
  position: relative;

  & .content {
    display: flex;
    flex-direction: row;
    justify-content: space-evenly;
    width: 100%;
    height: calc(100% - 50px);

    & .left-panel {
      width: 300px;
      height: 100%;
      overflow-y: auto;
      border-right: 1px solid #e8e8e8;
      .tree-node-title {
        display: flex;
        align-items: center;
      }
    }

    & .right-panel {
      flex: 1;
      padding: 0px 10px;
    }
  }

  & .selector-footer {
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    gap: 10px;
  }
  .db-icon {
    width: 24px;
    height: 24px;
    object-fit: contain;
  }
}
</style>
