import type { OrderDTO } from '@/utils/column'

//员工列表
export const dataSourceColumn: OrderDTO = [
  {
    title: '数据源名称',
    dataIndex: 'dbDesc',
    key: 'dbDesc',
  },
  {
    title: '数据库地址',
    dataIndex: 'dbUrl',
    key: 'dbUrl',
  },
  {
    title: '数据库类型',
    dataIndex: 'dbType',
    key: 'dbType',
  },
]

export const projectColumn: OrderDTO = [
  {
    title: '配置项目名称',
    dataIndex: 'proName',
    key: 'proName',
  },
]

export const robatColumn: OrderDTO = [
  {
    title: '模型名称',
    dataIndex: 'name',
    key: 'name',
  },
]

export const experimentColumn: OrderDTO = [
  {
    title: '实验名称',
    dataIndex: 'name',
    key: 'name',
  },
]

export enum DbTypeName {
  MySQL = 1,
  Kudu = 2,
  TiDB = 3,
  Hive = 4,
  Oracle = 5,
  SQLServer = 6,
  Cache = 7,
  MongoDB = 8,
  File = 9,
  HTTP = 10,
  Impala = 11,
  Greenplum = 12,
  DM = 13,
  Druid = 14,
  Kafka = 15,
  PostgreSQL = 16,
  Socket = 17,
  Flink = 18,
  GBase = 19,
  HDFS = 21,
  OceanBase = 22,
  InfluxDB = 23,
  Spark = 24,
  Doris = 25,
  TDengine = 26,
  Elasticsearch = 27,
  ClickHouse = 28,
  Kingbase = 29,
  HBase = 30,
}
