<template>
  <div style="width: 100%; background-color: #ffffff; padding: 20px">
    <a-tabs v-model:activeKey="activeKey" tab-position="left">
      <a-tab-pane key="1" tab="知识库配置">
        <konwledgeConfig :data="formData" @modalAddOk="handleModalAddOk" />
      </a-tab-pane>
      <a-tab-pane key="2" tab="模型配置">
        <konwledgeModelConfig :data="formData" @modalAddOk="handleModalAddOk" />
      </a-tab-pane>
    </a-tabs>
  </div>
</template>
<script lang="ts" setup>
import { ref, onBeforeMount } from 'vue'

import konwledgeConfig from '@/views/knowledge-builder/knowledge-configuration/index.vue'
import konwledgeModelConfig from '@/views/knowledge-builder/knowledge-model-config/index.vue'
import { reqGetKnowledgeConfig } from '@/api/services/home'
import { useRoute } from 'vue-router'
const route = useRoute()

const activeKey = ref('1')
const projectId = ref('')
const formData = ref({})
onBeforeMount(async () => {
  // route.params.projectId && (await getData())
  console.log('================')
  console.log(route.params.projectId)
  projectId.value = route.params.projectId
  getData()
})
function handleModalAddOk() {
  console.log('======模型配置=========modalAddOk===============')
  getData()
}
async function getData() {
  const data = {
    projectId: route.params.projectId,
    // projectId: route.query?.projectId,
  }
  await reqGetKnowledgeConfig(data).then((res) => {
    console.log(res)

    formData.value = res
    // setFormState()
  })
}
</script>
