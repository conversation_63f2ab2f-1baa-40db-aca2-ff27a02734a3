import { defineConfig, presetWind3 } from 'unocss'
import presetAttributify from '@unocss/preset-attributify'

export default defineConfig({
  presets: [presetAttributify(), presetWind3()],
  shortcuts: {
    ellipsis: 'overflow-hidden whitespace-nowrap text-ellipsis',
    'flex-center': 'flex items-center justify-center',
    'scrollbar-hide':
      'scrollbar-none [&::-webkit-scrollbar]:hidden [-ms-overflow-style:none] [scrollbar-width:none]',
  },
  theme: {
    colors: {
      primary: '#65aae7', // 主色
    },
  },
  rules: [[/^text-b(\d+)$/, ([, d]) => ({ 'font-weight': d })]],
})
