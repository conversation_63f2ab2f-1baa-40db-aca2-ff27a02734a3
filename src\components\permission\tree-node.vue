<template>
  <div class="content">
    <a-tree
      v-model:selectedKeys="selectedKeys"
      :tree-data="gData"
      show-icon
      block-node
      :field-names="fieldNames"
      :draggable="draggable"
      @drop="onDrop"
      @select="handleTreeSelect"
      v-if="treeData.length > 0"
      :checkable="checkable"
      class="tree-node"
      v-model:checkedKeys="checkedKeys"
    >
      <template #title="{ ...record }">
        <span class="deptName">
          <div class="deptNameText">{{ record[fieldNames?.title] }}</div>
          <a-dropdown v-if="isMenus" placement="bottomLeft" class="tree-dropdown" @click.stop>
            <a class="ant-dropdown-link" @click.prevent>
              <MoreOutlined class="tree-title-custom" />
            </a>
            <template #overlay>
              <a-menu>
                <slot name="menus" :record="record"></slot>
              </a-menu>
            </template>
          </a-dropdown>
        </span>
      </template>
    </a-tree>
    <div v-else>
      <a-empty description="暂无数据" />
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref, watch } from 'vue'
import { type TreeProps } from 'ant-design-vue'
import { MoreOutlined } from '@ant-design/icons-vue'
import type { AntTreeNodeDropEvent, TreeDataItem } from 'ant-design-vue/es/tree'
const props = defineProps({
  treeData: {
    type: Array,
    default: () => [],
  },
  isMenus: {
    type: Boolean,
    default: true,
  },
  fNames: {
    type: Object,
    default: () => {},
  },
  checkable: {
    type: Boolean,
    default: () => false,
  },
  draggable: {
    type: Boolean,
    default: () => false,
  },
})
const emits = defineEmits(['treeSelect', 'draghandle'])
const checkedKeys = ref<string[]>([])
const selectedKeys = ref([])

const fieldNames = ref({
  children: 'menuList',
  title: 'menuName',
  key: 'menuId',
})!

watch(
  () => props.fNames,
  (value) => {
    if (value) {
      fieldNames.value = {
        children: value?.children,
        title: value?.title,
        key: value?.key,
      }
    }
  },
  { immediate: true },
)
const gData = ref<TreeProps['treeData']>([])
watch(
  () => props.treeData,
  (value: any) => {
    if (value && value.length > 0) {
      checkedKeys.value = []
      addCheckedKeys(value, checkedKeys.value)
      gData.value = value
    }
  },
  { immediate: true },
)

function handleTreeSelect(selectedKeys: string[]) {
  emits('treeSelect', selectedKeys[0])
}

function addCheckedKeys(data: any[], checkedKeys: any[]) {
  data.forEach((item) => {
    if (item.isHalfChecked === 0) {
      checkedKeys.push(item.menuId)
    }
    if (item.datas && item.datas.length > 0) {
      addCheckedKeys(item.datas, checkedKeys)
    }
  })
}

//树控件的拖拽事件
const onDrop = (info: AntTreeNodeDropEvent) => {
  if (info.node?.menuType === 2) {
    return false
  }
  const dropKey = info.node.key
  const dragKey = info.dragNode.key
  const dropPos = info.node.pos?.split('-') || []
  const dropPosition = info.dropPosition - Number(dropPos[dropPos.length - 1])
  const loop = (data: TreeProps['treeData'], key: string | number, callback: any) => {
    data &&
      data.forEach((item, index) => {
        if (item.deptId === key) {
          return callback(item, index, data)
        }
        if (item.deptList) {
          return loop(item.deptList, key, callback)
        }
      })
  }

  const data = [...(gData.value || [])]
  let dragObj: TreeDataItem
  loop(data, dragKey, (item: TreeDataItem, index: number, arr: TreeProps['treeData']) => {
    arr && arr.splice(index, 1)
    dragObj = item
  })

  if (!info.dropToGap) {
    loop(data, dropKey, (item: TreeDataItem) => {
      item.deptList = item.deptList || []
      item.deptList.unshift(dragObj)
    })
  } else if (
    (info.node.children || []).length > 0 && // Has children
    info.node.expanded && // Is expanded
    dropPosition === 1 // On the bottom gap
  ) {
    loop(data, dropKey, (item: TreeDataItem) => {
      item.deptList = item.deptList || []
      item.deptList.unshift(dragObj)
    })
  } else {
    let ar: TreeProps['treeData'] = []
    let i = 0
    loop(data, dropKey, (_item: TreeDataItem, index: number, arr: TreeProps['treeData']) => {
      ar = arr
      i = index
    })
    if (dropPosition === -1) {
      //@ts-ignore
      ar.splice(i, 0, dragObj)
    } else {
      //@ts-ignore
      ar.splice(i + 1, 0, dragObj)
    }
  }
  gData.value = data
  // 修改菜单
  emits('draghandle', info)
}
</script>
<style lang="less" scoped>
.content {
  padding: 16px;
  .tree-dropdown {
    position: absolute;
    right: 0px;
    top: 0px;
    padding-left: 20px;
    padding-right: 10px;
  }
  .deptName {
    .deptNameText {
      width: 138px;
      display: inline-block;
    }
  }
}
</style>
