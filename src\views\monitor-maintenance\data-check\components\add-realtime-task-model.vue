<template>
  <a-modal
    v-model:open="addMapModelVisible"
    :title="title || ''"
    width="70%"
    @ok="submit"
    @cancel="cancel"
    :destroyOnClose="true"
  >
    <a-form :model="formData" ref="ruleForm" :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }">
      <a-form-item
        v-if="type === 'task'"
        label="作业流"
        prop="flowId"
        :rules="{ required: true, message: '请选择作业流', trigger: 'change' }"
      >
        <a-select
          style="width: 260px"
          mode="multiple"
          :maxTagCount="1"
          v-model:value="formData.flowId"
          showSearch
          :filter-option="selectInput"
          placeholder="请选择作业流"
        >
          <a-select-option
            v-for="(option, optKey) in flowList"
            :key="optKey"
            :value="`${option.id}`"
            :label="option.name"
            >{{ option.name }}</a-select-option
          >
        </a-select>
      </a-form-item>

      <a-form-item v-if="type === 'task'" label="是否监控" prop="isMonitor">
        <a-switch v-model:checked="formData.isMonitor"></a-switch>
      </a-form-item>
      <a-form-item label="定时器" prop="cronExpression">
        <div style="display: flex">
          <a-input
            placeholder="请输入执行频率表达式"
            v-model:value="formData.cronExpression"
            style="width: 260px"
          ></a-input>
          <a-popover
            placement="bottom"
            :destroyTooltipOnHide="true"
            title="表达式生成器"
            v-model:open="visibleGePop"
            trigger="click"
          >
            <template #content>
              <a-form style="width: 540px">
                <a-form-item label="流程执行开始时间" prop="startTime">
                  <a-date-picker
                    transfer
                    :show-time="{ format: 'HH:mm:ss' }"
                    style="width: 100%"
                    format="YYYY-MM-DD HH:mm:ss"
                    valueFormat="YYYY-MM-DD HH:mm:ss"
                    v-model:value="ceForm.startTime"
                    placeholder="请选择流程执行开始时间"
                  />
                </a-form-item>
                <a-form-item label="流程执行间隔时间">
                  <a-input-number
                    style="width: 100%"
                    :min="0"
                    v-model:value="ceForm.intervalTime"
                    placeholder="请输流程执行间隔时间"
                  />
                </a-form-item>
                <a-form-item label="流程执行频率单位">
                  <a-select v-model:value="ceForm.dataFreExpressionUnit">
                    <a-select-option value="minutes">分钟</a-select-option>
                    <a-select-option value="hour">小时</a-select-option>
                    <a-select-option value="day">天</a-select-option>
                    <a-select-option value="month">月</a-select-option>
                    <a-select-option value="year">年</a-select-option>
                  </a-select>
                </a-form-item>
              </a-form>
              <div style="width: 100%; display: flex; justify-content: flex-end; margin-top: 30px">
                <a-button class="mr10" @click="visibleGePop = false">取消</a-button>
                <a-button type="primary" @click="generateExpressionFn()">确认</a-button>
              </div>
            </template>
            <a-button type="primary" style="margin-left: 10px">生成表达式</a-button>
          </a-popover>
          <a-tooltip trigger="hover" placement="top">
            <template #title> 例如：41 29 15 */1 * ? * 表明：每天的15:29:41秒 </template>
            <QuestionCircleOutlined style="cursor: pointer; line-height: 32px; margin-left: 5px" />
          </a-tooltip>
        </div>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup lang="ts">
import { generateExpression } from '@/api/services/monitor-maintenance/flow-definition'
import { message } from 'ant-design-vue'
import { ref } from 'vue'
import {
  getAllRealTimeFlow,
  addFlowRealTimeTask,
  realTimeUopdateCron,
} from '@/api/services/monitor-maintenance/data-quality'
import { useUserStore } from '@/stores/user'
import { formatTime } from '@/views/setting-page/utils'
import { QuestionCircleOutlined } from '@ant-design/icons-vue'

const userStore = useUserStore()

const ownerId = ref<string>(userStore.userInfo.loginAccount)
const emit = defineEmits(['callback'])
const props = defineProps<{
  title: string
  type: string
}>()
const ruleForm = ref()
const addMapModelVisible = ref(false)
const visibleGePop = ref(false)
let formData = reactive<any>({
  flowId: undefined,
  cronExpression: '',
  intervalTime: 60,
  isMonitor: true,
})
const dataSource = ref<any[]>(['<', '<=', '=', '>', '>=', '!='])
const lFieldTimeList = ref<any[]>(['hour', 'minute', 'second', 'nano_second'])
const lFieldDataList = ref<any[]>([
  'year',
  'month',
  'day',
  'hour',
  'minute',
  'second',
  'nano_second',
])
const flowList = ref<any[]>([])

const ceForm = ref<any>({
  startTime: formatTime(new Date()),
  dataFreExpressionUnit: 'day',
  intervalTime: 1,
})

const showModel = (record: any) => {
  if (record) {
    formData = reactive(record)
    formData.isMonitor = !!record.isMonitor
  }
  addMapModelVisible.value = true
  getAllRealTimeFlow({ ownerId: ownerId.value }).then((res) => {
    if (res.data.length) {
      flowList.value = res.data
    }
  })
}

//  生成表达式
const generateExpressionFn = () => {
  let params = {
    intervalTime: ceForm.value.intervalTime,
    dataStartTime: ceForm.value.startTime,
    dataFreExpressionUnit: ceForm.value.dataFreExpressionUnit,
  }
  generateExpression(params).then((res) => {
    if (res.data.code !== 1) {
      message.error(res.data.message)
      return
    }
    formData.cronExpression = res.data.obj
    visibleGePop.value = false
    ceForm.value = {
      startTime: formatTime(new Date()),
      dataFreExpressionUnit: 'day',
      intervalTime: 1,
    }
  })
}

// 筛选下拉框
const selectInput = (inputValue: string, option: any) => {
  return option.label.toLowerCase().indexOf(inputValue.toLowerCase()) >= 0
}

const submit = () => {
  ruleForm.value.validate().then(() => {
    if (props.type === 'task') {
      addFlowRealTimeTask({
        flowInfo: JSON.stringify({
          ...formData,
          isMonitor: +formData.isMonitor,
        }),
      }).then((res) => {
        if (res.data.code === 1) {
          message.success(res.msg)

          formData.flowId = undefined
          formData.intervalTime = 60
          formData.isMonitor = true
          cancel()
          emit('callback')
        } else {
          message.error(res.data.message)
        }
      })
    } else {
      realTimeUopdateCron({
        json: JSON.stringify({
          id: formData.id,
          cronExpression: formData.cronExpression,
        }),
      }).then((res) => {
        if (res.data.code === 1) {
          message.success(res.msg)
          formData.flowId = undefined
          formData.intervalTime = 60
          formData.isMonitor = true

          cancel()
          emit('callback')
        } else {
          message.error(res.data.message)
        }
      })
    }
  })
}
const cancel = () => {
  addMapModelVisible.value = false
}
defineExpose({
  showModel,
})
</script>
<style lang="less" scope></style>
