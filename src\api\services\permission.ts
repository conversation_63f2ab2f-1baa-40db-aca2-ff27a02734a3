import service from '../server-permission.ts'
import { PROJECT_AUTHORITYCENTER, QMUCENTER } from '../base'
import type { AxiosResponse } from 'axios'

// 白名单数据
const MOCK_WHITE_DATA: any = {
  accountLogout: {
    code: '000000',
    msg: '退出登录成功',
  },
}

// 获取用户信息
interface UserInfo {
  userId: string
  tenantId: string
  avatar: string
  isEnabled: number
  employeeId: string
  mobile: string
  userPetName: string
}
export function getUserInfo(
  data: { userToken: string },
  headers?: any,
): Promise<AxiosResponse<UserInfo>> {
  return service({
    method: 'GET',
    url: `${PROJECT_AUTHORITYCENTER}/loginManager/getUserInfo`,
    params: data,
    headers,
  })
}

// 获取用户信息
interface UserInfo {
  userId: string
  tenantId: string
  avatar: string
  isEnabled: number
  employeeId: string
  mobile: string
  userPetName: string
}
export function get301UcUserInfo(data: { userToken: string }): Promise<AxiosResponse<UserInfo>> {
  return service({
    method: 'GET',
    url: `/api/301UC/loginManager/getUserInfo`,
    params: data,
  })
}

// 获取用户菜单
export function getUserMenu(
  data: { userId: string; systemId?: string; menuType?: number },
  headers?: any,
) {
  return service({
    method: 'GET',
    url: `${PROJECT_AUTHORITYCENTER}/systemManage/getUserMenu`,
    params: data,
    headers,
  })
}

// 获取系统配置
export function getSystemConfig() {
  return service({
    method: 'GET',
    url: `${PROJECT_AUTHORITYCENTER}/systemConfig/viewSystemConfig`,
  })
}

// 获取登录验证码
export function getVerifyCode(data: { loginAccount: string }) {
  return service({
    method: 'GET',
    url: `${PROJECT_AUTHORITYCENTER}/loginManager/getVerifyCode`,
    params: data,
  })
}

// 账号密码登录
export function accountLogin(
  data: {
    loginAccount: string
    password: string
    VerifyCode?: string
  },
  Systemcode?: string,
) {
  const config: Record<string, any> = {
    method: 'POST',
    url: `${PROJECT_AUTHORITYCENTER}/loginManager/loginCheck`,
    data,
  }
  if (Systemcode) {
    config.headers = {
      Systemcode,
    }
  }
  return service(config)
}

// 账号退出
export function accountLogout(data: Record<string, any>) {
  return service(
    {
      method: 'GET',
      url: `${PROJECT_AUTHORITYCENTER}/loginManager/logout`,
      params: data,
    },
    MOCK_WHITE_DATA['accountLogout'],
  )
}

// 单点登录表格查询接口
export function getSsoAppList(data: {
  appCode?: string
  appName?: string
  isUse?: number
  pageSize: number
  pageIndezx: number
}): Promise<
  AxiosResponse<{
    tenantId: string
    appId: string
    appName: string
    isUse: number
    appCode: string
    isVerify: number
    createUserName: string
    createTime: string
    callbackUrl: string
  }>
> {
  return service({
    method: 'GET',
    url: `${PROJECT_AUTHORITYCENTER}/ssoManage/getSsoAppList`,
    params: data,
  })
}

// 单点登录编辑接口
export function updateSsoApp(data: {
  appId: string
  appName?: string
  callbackUrl?: string
  isUse?: number | string
  isVerify?: string
}): Promise<
  AxiosResponse<{
    tenantId: string
    appCode?: string
    appName?: string
    isUse?: number
    pageSize: number
    pageIndezx: number
  }>
> {
  return service({
    method: 'POST',
    url: `${PROJECT_AUTHORITYCENTER}/ssoManage/updateSsoApp`,
    data,
  })
}

// 单点登录新增接口
export function addSsoApp(data: {
  tenantId?: string
  appCode?: string
  appName: string
  callbackUrl: string
  isUse: number | string
  isVerify?: string
}) {
  return service({
    method: 'POST',
    url: `${PROJECT_AUTHORITYCENTER}/ssoManage/addSsoApp`,
    data,
  })
}

// 单点登录删除接口
export function deleteSsoApp(data: { appId: string }) {
  return service({
    method: 'POST',
    url: `${PROJECT_AUTHORITYCENTER}/ssoManage/deleteSsoApp`,
    data,
  })
}

// 租户表格查询
export function getTenantList(data: { pageSize?: number; pageIndex?: number }): Promise<
  AxiosResponse<{
    tenantId: string
    tenantCode: string
    manageUserAccount: string
    tenantName: string
    createUser: string
    createTime: string
  }>
> {
  return service({
    method: 'GET',
    url: `${PROJECT_AUTHORITYCENTER}/tenantManage/getTenantList`,
    params: data,
  })
}

// 新增租户列表
export function addTenant(data: {
  opUserName: string
  tenantCode: string
  tenantName: string
  remark?: string
  manageUserAccount: string
}) {
  return service({
    method: 'POST',
    url: `${PROJECT_AUTHORITYCENTER}/tenantManage/addTenant`,
    data,
  })
}

// 查询租户详情
export function viewTenantDetail(data: { tenantId: string }) {
  return service({
    method: 'GET',
    url: `${PROJECT_AUTHORITYCENTER}/tenantManage/viewTenantDetail`,
    params: data,
  })
}

// 租户表格编辑
export function updateTenant(data: {
  tenantId: string
  tenantCode: string
  tenantName: string
  remark?: string
}) {
  return service({
    method: 'POST',
    url: `${PROJECT_AUTHORITYCENTER}/tenantManage/updateTenant`,
    data,
  })
}

// 系统参数配置
export function updateSystemParams(data: {
  paramId: string
  paramRemark: string
  paramValue: string
}): Promise<
  AxiosResponse<{
    paramId: string
    paramRemark: string
    paramValue: string
    createUser: string
    createUserName: string
    createTime: string
  }>
> {
  return service({
    method: 'POST',
    url: `${PROJECT_AUTHORITYCENTER}/systemParamsManage/updateSystemParams`,
    data,
  })
}

// 系统参数表格
export function viewSystemParamsList(data: {
  paramRemark?: string
  pageSize?: number
  pageIndex?: number
}): Promise<
  AxiosResponse<{
    paramId: string
    paramRemark: string
    paramValue: string
    createUser: string
    createUserName: string
    createTime: string
  }>
> {
  return service({
    method: 'GET',
    url: `${PROJECT_AUTHORITYCENTER}/systemParamsManage/viewSystemParamsList`,
    params: data,
  })
}

interface SystemListResponse {
  records: {
    systemId: string
    systemCode: string
    systemName: string
    systemType: number
    systemTypeName: string
    sortId: number
    isUse: number
    createUser: string
    createUserName: string
    createTime: string
  }[]
}

// 系统管理表格
export function getSystemList(data: {
  systemName?: string
  isUse?: number
  systemType?: number
  pageSize: number
  pageIndex: number
}): Promise<AxiosResponse<SystemListResponse>> {
  return service({
    method: 'GET',
    url: `${PROJECT_AUTHORITYCENTER}/systemManage/getSystemList`,
    params: data,
  })
}

// 获取系统类别
export function getDataDictList(data: Record<string, any>): Promise<
  AxiosResponse<{
    systemId: string
    systemCode: string
    systemName: string
    systemType: number
    systemTypeName: string
    sortId: number
    isUse: number
    createUser: string
    createUserName: string
    createTime: string
  }>
> {
  return service({
    method: 'GET',
    url: `${PROJECT_AUTHORITYCENTER}/systemFun/sysSystemFun/getDataDictList`,
    params: data,
  })
}

// 编辑参数列表
export function updateSystem(data: {
  systemId?: string
  systemCode: string
  systemName: string
  systemType?: string | undefined
  sortId?: string
  isUse: any
}): Promise<
  AxiosResponse<{
    paramId: string
    paramRemark: string
    paramValue: string
    createUser: string
    createUserName: string
    createTime: string
  }>
> {
  return service({
    method: 'POST',
    url: `${PROJECT_AUTHORITYCENTER}/systemManage/updateSystem`,
    data,
  })
}

// 新增参数列表
export function addSystem(data: {
  systemCode: string
  systemName: string
  systemType?: string
  sortId?: string
  isUse: number | string
}) {
  return service({
    method: 'POST',
    url: `${PROJECT_AUTHORITYCENTER}/systemManage/addSystem`,
    data,
  })
}

// 获取树结构菜单
export function getMenuList(data: { systemId: string }): Promise<
  AxiosResponse<{
    menuId: string
    menuName: string
    menuType: number
    parentId: string
    parentMenuName: string
    menuList: {
      menuId: string
      menuName: string
      menuType: number
      parentId: string
      parentMenuName: string
    }[]
  }>
> {
  return service({
    method: 'GET',
    url: `${PROJECT_AUTHORITYCENTER}/systemManage/getMenuListNew`,
    params: data,
  })
}

// 新增菜单
export function addMenu(data: {
  systemId: string
  menuName: string
  menuIcon?: string
  parentId: string
  sortId?: string
  menuUrl?: string
  apiMethod?: string
  apiMode?: string
  isDefaultGrant?: boolean
  isUse: boolean
  menuType?: number
  actionName?: string
}) {
  return service({
    method: 'POST',
    url: `${PROJECT_AUTHORITYCENTER}/systemManage/addMenu`,
    data,
  })
}

// 删除菜单
export function deleteMenu(data: { systemId: string; menuId: string }) {
  return service({
    method: 'POST',
    url: `${PROJECT_AUTHORITYCENTER}/systemManage/deleteMenu`,
    data,
  })
}

// 查询菜单
export function viewMenuDtl(data: { menuId: string }): Promise<
  AxiosResponse<{
    menuId: string
    systemId: string
    menuName: string
    menuIcon: string
    parentId: string
    levelIds: string
    sortId: number
    menuUrl: string
    apiMethod: string
    apiMode: string
    isDefaultGrant: boolean
    isUse: boolean
    menuTypeName: string
    actionCode: string
    createUser: string
    createUserName: string
    createTime: string
  }>
> {
  return service({
    method: 'GET',
    url: `${PROJECT_AUTHORITYCENTER}/systemManage/viewMenuDtl`,
    params: data,
  })
}

// 修改菜单
export function updateMenu(data: {
  menuId: string
  systemId: string
  menuName: string
  menuIcon?: string
  parentId: string
  sortId?: number
  menuUrl?: string
  apiMethod?: string
  apiMode?: string
  isDefaultGrant?: boolean
  isUse?: boolean
  menuType?: number
  actionCode?: string
}) {
  return service({
    method: 'POST',
    url: `${PROJECT_AUTHORITYCENTER}/systemManage/updateMenu`,
    data,
  })
}

// 导出菜单
export function exportMenu(data: Record<string, any>): Promise<AxiosResponse<Blob, any>> {
  return service({
    method: 'POST',
    url: `${PROJECT_AUTHORITYCENTER}/systemManage/exportMenu`,
    data,
    responseType: 'blob',
  })
}

// 导入菜单
export function importMenu(data: Record<string, any>) {
  return service({
    method: 'POST',
    url: `${PROJECT_AUTHORITYCENTER}/systemManage/importMenu`,
    data,
    headers: {
      'Content-Type': 'multipart/form-data',
      Languagetype: 'en-US',
    },
  })
}

// 职务管理列表
export function getPositionList(data: {
  positionName?: string
  isUse: number
  pageSize: number
  pageIndex: number
}): Promise<
  AxiosResponse<{
    positionId: string
    positionName: string
    isUse: boolean
    tenantId: string
    buId: string
    remark: string
    createUserName: string
    createTime: string
  }>
> {
  return service({
    method: 'GET',
    url: `${PROJECT_AUTHORITYCENTER}/positionManage/getPositionList`,
    params: data,
  })
}

// 编辑职务管理
export function updatePosition(data: {
  positionId: string
  positionName?: string
  isUse: number | string
  remark?: string
}) {
  return service({
    method: 'POST',
    url: `${PROJECT_AUTHORITYCENTER}/positionManage/updatePosition`,
    data,
  })
}

// 新增职务管理
export function addPosition(data: {
  positionName: string
  isUse: boolean | string
  remark?: string
}) {
  return service({
    method: 'POST',
    url: `${PROJECT_AUTHORITYCENTER}/positionManage/addPosition`,
    data,
  })
}

// 删除职务管理
export function deletePosition(data: { positionId: string }) {
  return service({
    method: 'POST',
    url: `${PROJECT_AUTHORITYCENTER}/positionManage/deletePosition`,
    data,
  })
}

// 组织管理树结构数据
export function getDeptList(data: { buId: string; isUse?: number }): Promise<
  AxiosResponse<{
    parentId: string // 父级组织编号
    deptId: string // 组织编号
    deptName: string // 组织名称
    deptMangerId: string // 组织负责人用户编号
    deptMangerName: string // 组织负责人名称
    children: {
      parentId: string // 父级组织编号
      deptId: string // 组织编号
      deptName: string // 组织名称
      deptMangerId: string // 组织负责人用户编号
      deptMangerName: string // 组织负责人名称
    }[]
  }>
> {
  return service({
    method: 'GET',
    url: `${PROJECT_AUTHORITYCENTER}/deptManage/getDeptList`,
    params: data,
  })
}

// 获取组织树节点表格
export function getChildDeptList(data: {
  businessId?: string
  deptId: string
  deptName?: string
  pageSize: number
  pageIndezx: number
}): Promise<
  AxiosResponse<{
    deptId: string // 组织编号
    deptName: string // 组织名称
  }>
> {
  return service({
    method: 'GET',
    url: `${PROJECT_AUTHORITYCENTER}/deptManage/getChildDeptList`,
    params: data,
  })
}

// 新增组织表格数据
export function addDept(data: {
  deptName: string
  parentId: string
  deptType: string | undefined
  dutyUserId?: string
  sortId?: number | string
  remark?: string
}) {
  return service({
    method: 'POST',
    url: `${PROJECT_AUTHORITYCENTER}/deptManage/addDept`,
    data,
  })
}

// 删除组织表格数据
export function deleteDept(data: { deptId: string }) {
  return service({
    method: 'POST',
    url: `${PROJECT_AUTHORITYCENTER}/deptManage/deleteDept`,
    data,
  })
}

// 编辑组织数据
export function updateDept(data: {
  deptId: string
  deptName: string
  deptType: string | undefined
  dutyUserId: string
  sortId?: string | number
  remark?: string
  isUse?: boolean | string
}) {
  return service({
    method: 'POST',
    url: `${PROJECT_AUTHORITYCENTER}/deptManage/updateDept`,
    data,
  })
}

// 获取树节点详情
export function viewDeptDtl(data: { deptId: string }): Promise<AxiosResponse<any>> {
  return service({
    method: 'GET',
    url: `${PROJECT_AUTHORITYCENTER}/deptManage/viewDeptDtl`,
    params: data,
  })
}

// 获取用户列表
export function getUserList(data: {
  buId: string
  loginAccount?: string
  userName?: string
  deptId?: string
  mobile?: string
  queryType?: number
}): Promise<AxiosResponse<any>> {
  return service({
    method: 'GET',
    url: `${PROJECT_AUTHORITYCENTER}/userManage/getUserList`,
    params: data,
  })
}

// 获取角色列表
export function getRoleList(data: {
  businessId?: string
  roleName?: string
  pageSize: number
  pageIndex: number
}): Promise<AxiosResponse<any>> {
  return service({
    method: 'GET',
    url: `${PROJECT_AUTHORITYCENTER}/roleManage/getRoleList`,
    params: data,
  })
}

// 添加角色
export function addRole(data: {
  roleName: string
  isUse: boolean
  remark?: string
}): Promise<AxiosResponse<any>> {
  return service({
    method: 'POST',
    url: `${PROJECT_AUTHORITYCENTER}/roleManage/addRole`,
    data,
  })
}

// 添加角色
export function updateRole(data: {
  roleId?: string
  roleName: string
  isUse: boolean
  remark: string
}): Promise<AxiosResponse<any>> {
  return service({
    method: 'POST',
    url: `${PROJECT_AUTHORITYCENTER}/roleManage/updateRole`,
    data,
  })
}

// 分配菜单获取菜单
export function getUserSystem(data: {
  userId: string
  queryType: number
}): Promise<AxiosResponse<any>> {
  return service({
    method: 'GET',
    url: `${PROJECT_AUTHORITYCENTER}/systemManage/getUserSystem`,
    params: data,
  })
}

// 分配菜单获取菜单树结构
export function getRoleMenuList(data: {
  roleId: string
  systemId: string
  queryType: number
}): Promise<AxiosResponse<any>> {
  return service({
    method: 'GET',
    url: `${PROJECT_AUTHORITYCENTER}/roleManage/getRoleMenuList`,
    params: data,
  })
}

// 分配菜单选中
export function saveRoleMneu(data: {
  menuList?: any
  queryType: number
  roleId: string
  systemId: string
}): Promise<AxiosResponse<any>> {
  return service({
    method: 'POST',
    url: `${PROJECT_AUTHORITYCENTER}/roleManage/saveRoleMneu`,
    data,
  })
}

// 分配用户
export function getRoleUserList(data: {
  roleId: string
  pageSize: number
  pageIndex: number
}): Promise<AxiosResponse<any>> {
  return service({
    method: 'GET',
    url: `${PROJECT_AUTHORITYCENTER}/roleManage/getRoleUserList`,
    params: data,
  })
}

// 添加用户
export function addRoleUser(data: {
  roleId: string
  userList: any[]
}): Promise<AxiosResponse<any>> {
  return service({
    method: 'POST',
    url: `${PROJECT_AUTHORITYCENTER}/roleManage/addRoleUser`,
    data,
  })
}

// 删除用户
export function deleteRoleUser(data: { userList: [] }): Promise<AxiosResponse<any>> {
  return service({
    method: 'POST',
    url: `${PROJECT_AUTHORITYCENTER}/roleManage/deleteRoleUser`,
    data,
  })
}

// 查看菜单
export function getRoleSystemList(data: {
  roleId: string
  queryType: number
}): Promise<AxiosResponse<any>> {
  return service({
    method: 'GET',
    url: `${PROJECT_AUTHORITYCENTER}/roleManage/getRoleSystemList`,
    params: data,
  })
}

// 编辑岗位
export function updateJob(data: {
  jobId: string
  jobName?: string
  isUse?: boolean | string
  responsibility?: string
}): Promise<AxiosResponse<any>> {
  return service({
    method: 'POST',
    url: `${PROJECT_AUTHORITYCENTER}/positionManage/updateJob`,
    data,
  })
}

// 编辑岗位查询
export function getJobDetails(data: { jobId: string }): Promise<AxiosResponse<any>> {
  return service({
    method: 'GET',
    url: `${PROJECT_AUTHORITYCENTER}/positionManage/getJobDetails`,
    params: data,
  })
}

// 新增岗位查询
export function addJob(data: {
  jobName: string
  isUse: boolean | string
  responsibility?: string
}): Promise<AxiosResponse<any>> {
  return service({
    method: 'POST',
    url: `${PROJECT_AUTHORITYCENTER}/positionManage/addJob`,
    data,
  })
}

// 岗位删除
export function deleteJob(data: { jobId: string }): Promise<AxiosResponse<any>> {
  return service({
    method: 'POST',
    url: `${PROJECT_AUTHORITYCENTER}/positionManage/deleteJob`,
    data,
  })
}

// 用户组编辑
export function updateUserGroup(data: {
  isUse: string
  remark: string
  userGroupId: string
  userGroupName: string
}): Promise<AxiosResponse<any>> {
  return service({
    method: 'POST',
    url: `${PROJECT_AUTHORITYCENTER}/userGroupManage/updateUserGroup`,
    data,
  })
}

// 新增用户组
export function addUserGroup(data: {
  isUse: string
  remark: string
  userGroupName: string
}): Promise<AxiosResponse<any>> {
  return service({
    method: 'POST',
    url: `${PROJECT_AUTHORITYCENTER}/userGroupManage/addUserGroup`,
    data,
  })
}

// 新增用户组
export function deleteUserGroup(data: { userGroupId: string }): Promise<AxiosResponse<any>> {
  return service({
    method: 'POST',
    url: `${PROJECT_AUTHORITYCENTER}/userGroupManage/deleteUserGroup`,
    data,
  })
}

// 新增用户组用户组添加用户
export function getUserGroupUserList(data: {
  userGroupId: string
  pageSize: number
  pageIndex: number
}): Promise<AxiosResponse<any>> {
  return service({
    method: 'GET',
    url: `${PROJECT_AUTHORITYCENTER}/userGroupManage/getUserGroupUserList`,
    params: data,
  })
}

// 用户组分配可查看菜单
export function getUserGroupMenuList(data: {
  userGroupId: string
  systemId: string
}): Promise<AxiosResponse<any>> {
  return service({
    method: 'GET',
    url: `${PROJECT_AUTHORITYCENTER}/userGroupManage/getUserGroupMenuListNew`,
    params: data,
  })
}

// 新增用户组查询菜单
export function userGroupSetMenu(data: {
  menuList: Record<string, any>
  systemId: string
  userGroupId: string
}): Promise<AxiosResponse<any>> {
  return service({
    method: 'POST',
    url: `${PROJECT_AUTHORITYCENTER}/userGroupManage/userGroupSetMenu`,
    data,
  })
}

// 获取用户组列表
export function getUserGroupList(data: {
  tenantId: string
  buId: string
  userGroupName?: string
  isUse?: number
  pageSize: number
  pageIndex: number
}): Promise<AxiosResponse<any>> {
  return service({
    method: 'GET',
    url: `${PROJECT_AUTHORITYCENTER}/userGroupManage/getUserGroupList`,
    params: data,
  })
}

// 批量删除员工
export function batchDeleteUser(data: {
  userIdList: { userId: string | number }[]
}): Promise<AxiosResponse<any>> {
  return service({
    method: 'POST',
    url: `${PROJECT_AUTHORITYCENTER}/userManage/batchDeleteUser`,
    data,
  })
}

// 文件上传地址
export const uploadFile = `/api/fsAuthorityCenter/fileManage/uploadFile`

export function uploadFilePost(data: Record<string, any>): Promise<AxiosResponse<any>> {
  return service({
    method: 'POST',
    url: `${PROJECT_AUTHORITYCENTER}/fileManage/uploadFile`,
    data,
    headers: {
      'Content-Type': 'multipart/form-data',
      Languagetype: 'en-US',
    },
  })
}

// 获取岗位列表
export function getJobList(data: {
  buId: string
  isUse?: number
  pageIndex: number
  pageSize: number
}): Promise<AxiosResponse<any>> {
  return service({
    method: 'GET',
    url: `${PROJECT_AUTHORITYCENTER}/positionManage/getJobList`,
    params: data,
  })
}

// 用户管理 - 新增用户
export function addUser(data: Record<string, any>): Promise<AxiosResponse<any>> {
  return service({
    method: 'POST',
    url: `${PROJECT_AUTHORITYCENTER}/userManage/addUser`,
    data,
  })
}

// 用户管理 - 编辑用户
export function updateUser(data: Record<string, any>): Promise<AxiosResponse<any>> {
  return service({
    method: 'POST',
    url: `${PROJECT_AUTHORITYCENTER}/userManage/updateUser`,
    data,
  })
}

// 用户管理 - 更新用户状态 - 启用/禁用
export function updateUserStatus(data: {
  userStatus: number
  userId: string
}): Promise<AxiosResponse<any>> {
  return service({
    method: 'POST',
    url: `${PROJECT_AUTHORITYCENTER}/userManage/updateUserStatus`,
    data: { userStatus: data.userStatus },
    headers: {
      Opuser: data.userId,
    },
  })
}

// 用户管理 - 查看员工详情信息
export function viewUserDetail(userId: string): Promise<AxiosResponse<any>> {
  return service({
    method: 'POST',
    url: `${PROJECT_AUTHORITYCENTER}/userManage/viewUserDetail`,
    data: { userId },
  })
}

// 用户管理 - 重置密码
export function setUserPassword(data: {
  newPassword: string
  oldPassword: string
  userId: string
}): Promise<AxiosResponse<any>> {
  return service({
    method: 'POST',
    url: `${PROJECT_AUTHORITYCENTER}/userManage/setUserPassword`,
    data,
  })
}

// 用户管理 - 新增重置密码
export function adminResetUserPassword(data: {
  password: string
  userId: string
}): Promise<AxiosResponse<any>> {
  return service({
    method: 'POST',
    url: `${PROJECT_AUTHORITYCENTER}/userManage/adminResetUserPassword`,
    data,
  })
}

// BU管理 - BU列表查询
export function getBUList(data: {
  pageIndex: number
  pageSize: number
  isUse?: boolean
  buName?: string
}): Promise<AxiosResponse<any>> {
  return service({
    method: 'GET',
    url: `${PROJECT_AUTHORITYCENTER}/tenantManage/getBUList`,
    params: data,
  })
}

// BU管理 - 新增BU
export function addBu(data: {
  buName: string
  isUse: boolean
  manageUserAccount: string
  remark: string
}): Promise<AxiosResponse<any>> {
  return service({
    method: 'POST',
    url: `${PROJECT_AUTHORITYCENTER}/tenantManage/addBu`,
    data,
  })
}

// BU管理 - 更新BU
export function updateBU(data: {
  buName: string
  isUse: boolean
  businessId: string
  remark: string
}): Promise<AxiosResponse<any>> {
  return service({
    method: 'POST',
    url: `${PROJECT_AUTHORITYCENTER}/tenantManage/updateBU`,
    data,
  })
}

// 用户组 - 分配系统
export function getUserGroupSystemList(data: {
  pageIndex: number
  pageSize: number
  userGroupId?: string
}): Promise<AxiosResponse<any>> {
  return service({
    method: 'GET',
    url: `${PROJECT_AUTHORITYCENTER}/userGroupManage/getUserGroupSystemList`,
    params: data,
  })
}

// 用户组管理 - 分配用户
export function addUserGroupUser(data: {
  userGroupId: string
  userList: any[]
  userId?: string
}): Promise<AxiosResponse<any>> {
  return service({
    method: 'POST',
    url: `${PROJECT_AUTHORITYCENTER}/userGroupManage/addUserGroupUser`,
    data,
  })
}

// 用户组管理 - 批量删除用户组
export function deleteUserGroupUser(data: { userList: any[] }): Promise<AxiosResponse<any>> {
  return service({
    method: 'POST',
    url: `${PROJECT_AUTHORITYCENTER}/userGroupManage/deleteUserGroupUser`,
    data,
  })
}

// 组织类型 - 查询
export function getDeptTypeList(data: { isUse: number }): Promise<AxiosResponse<any>> {
  return service({
    method: 'GET',
    url: `${QMUCENTER}/deptManage/getDeptTypeList`,
    params: data,
  })
}

// 组织类型 - 删除
export function deleteDeptType(data: { deptId: string }): Promise<AxiosResponse<any>> {
  return service({
    method: 'POST',
    url: `${QMUCENTER}/deptManage/deleteDeptType`,
    data,
  })
}

// 组织类型 - 更新
export function updateDeptType(data: {
  deptTypeId: string
  levelId: number
  remark: string
  isDept: number | string
  deptTypeName: string
}): Promise<AxiosResponse<any>> {
  return service({
    method: 'POST',
    url: `${QMUCENTER}/deptManage/updateDeptType`,
    data,
  })
}

// 组织类型 - 新增
export function insertDeptType(data: {
  deptTypeName: string
  levelId: number
  remark: string
  isDept: number
}): Promise<AxiosResponse<any>> {
  return service({
    method: 'POST',
    url: `${QMUCENTER}/deptManage/insertDeptType`,
    data,
  })
}

// 组织类型 - 查询
export function getOrgJobList(): Promise<AxiosResponse<any>> {
  return service({
    method: 'GET',
    url: `${QMUCENTER}/deptManage/getOrgJobList`,
  })
}

// FOM004-保存角色隐藏表单
export function newRoleHiddenGrid(data: any): Promise<AxiosResponse<any>> {
  return service({
    method: 'POST',
    url: `${PROJECT_AUTHORITYCENTER}/formManage/newRoleHiddenGrid`,
    data,
  })
}

/**
 * FOM002-获取角色隐藏表单列表
 * @param roleId
 * @param gridId
 * @returns
 */
export function getRoleHiddenGridList(data: any): Promise<AxiosResponse<any>> {
  return service({
    method: 'GET',
    url: `${PROJECT_AUTHORITYCENTER}/formManage/getRoleHiddenGridList`,
    params: data,
  })
}

/**
 * FOM003-获取登录用户菜单下权限表单及列
 * @param  menuId  - 菜单id
 * @returns
 */
export function getGridList(data: any): Promise<AxiosResponse<any>> {
  return service({
    method: 'GET',
    url: `${PROJECT_AUTHORITYCENTER}/formManage/getGridList`,
    params: data,
  })
}
