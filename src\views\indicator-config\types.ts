export type DatabaseInfo = {
  /** 数据库ID */
  databaseId?: string
  /** 数据库名称 */
  databaseName?: string
  /** 数据库类型 */
  databaseType?: string
  /** 数据源ID */
  dataSourceId?: string
  /** 数据源名称 */
  dataSourceName?: string
  /** 等于数据库名称 */
  title?: string
}
export type TableInfo = {
  /** 数据库ID */
  databaseId: string
  /** 数据库名称 */
  databaseName: string
  /** 数据库类型 */
  databaseType: string
  /** 数据源ID */
  datasourceId: string
  /** 数据源名称 */
  datasourceName: string
  /** 主键 */
  key: string
  /** 节点类型 */
  nodeType: number
  /** 表注释 */
  tableComment: string
  /** 表ID */
  tableId: string
  /** 表名称 */
  tableName: string
  /** 表名称 */
  title: string
}
