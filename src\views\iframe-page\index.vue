<template>
  <iframe
    class="iframe-container"
    ref="iframe"
    :src="computedUrl"
    frameborder="0"
    @load="cacheIframeContent"
  ></iframe>
</template>

<script setup>
import { computed } from 'vue'
import { useRoute } from 'vue-router'

defineOptions({
  name: 'iframePage',
})

const route = useRoute()
const time = ref(new Date().getTime())
const iframe = ref()
const cacheObg = {}
console.log('🚀 ~ time:', time.value, '222222222222222222222222')

const cacheIframeContent = (event) => {
  cacheObg[computedUrl] = iframe.value.contentDocument.documentElement.outerHTML
  console.log(
    '🚀 ~ cacheIframeContent ~ iframe.value.contentDocument.documentElement.outerHTML:',
    iframe.value.contentDocument.documentElement.outerHTML,
  )
  console.log(iframe.value)
}

// 计算属性
const computedUrl = computed(() => {
  const { token = '', url = '', pageName } = route.query
  let path = `${url}${url.indexOf('?') === -1 ? '?' : '&'}`
  console.log(pageName, 'pageName')

  if (pageName === '模型开发训练') {
    path = `${url}${url.indexOf('?') === -1 ? '?' : '&'}token=${token}`
  }

  if (url.includes('/machine-learning-file')) {
    return path + '&url=' + window.ENV.IFRAME_FILE_CONFIG_URL
  }
  if (url.includes('/jobFlow/jobFlowInstance')) {
    const { id = '', type = '', name = '' } = route.query
    return (
      path +
      '&url=' +
      window.ENV.IFRAME_FILE_CONFIG_URL +
      '&id=' +
      id +
      '&type=' +
      type +
      '&name=' +
      name
    )
  }
  return path
})
console.log('🚀 ~ computedUrl:', computedUrl)
</script>

<style scoped>
.iframe-container {
  width: 100%;
  height: 100%;
}
</style>
