<template>
  <div class="db-icon">
    <img
      v-if="iconSrc"
      :src="iconSrc"
      alt="数据库图标"
      :style="{ width: width + 'px', height: height + 'px' }"
    />
  </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue'
import { getDbIcon } from '@/utils/utils'

const props = withDefaults(
  defineProps<{
    type: string | number
    width?: number
    height?: number
  }>(),
  {
    width: 25,
    height: 25,
  },
)

const iconSrc = computed(() => {
  return getDbIcon(props.type)
})
</script>

<style scoped>
.db-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.db-icon img {
  width: 100%;
  height: 100%;
  object-fit: contain; /* 保持比例，可选 */
}
</style>
