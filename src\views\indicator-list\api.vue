<template>
  <a-button type="link" @click="showDialog = true">配置API</a-button>

  <a-modal
    v-model:open="showDialog"
    title="配置API"
    okText="去配置API"
    @ok="handleOk"
    @cancel="showDialog = false"
  >
    <div style="padding: 20px">
      <a-alert
        description="请先复制API地址，点击“去配置API”按钮后会跳转到配置页面。"
        type="info"
        show-icon
      />
      <h5>指标名称：{{ indexName }}</h5>
      <a-input-group compact>
        <a-input :value="apiUrl" style="width: calc(100% - 70px)" />
        <a-button type="default" @click="copyUrl">复制</a-button>
      </a-input-group>
    </div>
  </a-modal>
</template>

<script lang="ts" setup>
import { ref, defineProps, onMounted } from 'vue'
import { message } from 'ant-design-vue'
// 引入 clipboard-copy 包
import copy from 'clipboard-copy'
import { postMsgPushRoute } from '@/utils/post-message'

const props = defineProps<{
  autoId: string
  indexName: string
}>()

const showDialog = ref(false)
const apiUrl = ref('')
const assignedUrl = ref('')
const hasCopied = ref(false)

function copyUrl() {
  const text = apiUrl.value || assignedUrl.value
  if (!text) {
    message.warning('没有可复制的URL')
    return
  }
  copy(text)
  hasCopied.value = true
  message.success('已复制到剪贴板')
}

function handleOk() {
  if (!hasCopied.value) {
    message.warning('请先复制API地址')
    return
  }
  showDialog.value = false
  hasCopied.value = false

  postMsgPushRoute('/apiconfig/api/apiList/apiList', {})
}

onMounted(() => {
  apiUrl.value = `http://${window.location.host}/indexManage/indexManage/reviewByIndexIdP/${props.autoId}`
})
</script>
