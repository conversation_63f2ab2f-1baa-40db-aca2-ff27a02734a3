import { expect, test } from 'vitest'
import {
  indexbusinessDelete,
  indexbusinessInsert,
  indexbusinessUpdate,
  getIndexbusinessList,
  getBusinessPageList,
} from './bussinesmanage'

test('indexbusinessDelete', async () => {
  const { data } = await indexbusinessDelete({ businessId: 5577138835266660 })

  expect(data).toBeTruthy()
})

test('indexbusinessInsert', async () => {
  const { data } = await indexbusinessInsert({
    businessName: 'kR[8u@',
    remark: 'cxFY',
  })

  expect(data).toBeTruthy()
})

test('indexbusinessUpdate', async () => {
  const { data } = await indexbusinessUpdate({
    businessName: 'QhAd&f',
    remark: ']GNfn0b',
    businessId: 4825542023482406,
  })

  expect(data).toBeTruthy()
})

test('getIndexbusinessList', async () => {
  const { data } = await getIndexbusinessList({
    businessName: 'hoB8]O',
    remark: '30s',
  })

  expect(data).toHaveProperty('code')
  expect(data).toHaveProperty('msg')
  expect(data.data).toHaveProperty('businessId'),
    expect(data.data).toHaveProperty('businessName'),
    expect(data.data).toHaveProperty('remark'),
    expect(data.data).toHaveProperty('createUser'),
    expect(data.data).toHaveProperty('createTime')
})

test('getBusinessPageList', async () => {
  const { data } = await getBusinessPageList({
    businessName: 'ojnoQ',
    remark: '8sJ3$5',
    pageIndex: 1491625027026679,
    pageSize: 3342397989044635,
  })

  expect(data).toHaveProperty('msg')
  expect(data).toHaveProperty('code')
  expect(data.data).toHaveProperty('totalRecords'),
    expect(data.data).toHaveProperty('pageIndex'),
    expect(data.data).toHaveProperty('pageSize'),
    expect(data.data).toHaveProperty('totalPages'),
    expect(data.data).toHaveProperty('records')
})
