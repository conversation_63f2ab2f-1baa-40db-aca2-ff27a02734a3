/* eslint-env node */
require('@rushstack/eslint-patch/modern-module-resolution')

module.exports = {
  root: true,
  extends: [
    'plugin:vue/vue3-essential',
    'eslint:recommended',
    '@vue/eslint-config-typescript',
    '@vue/eslint-config-prettier/skip-formatting',
  ],
  rules: {
    'vue/multi-word-component-names': 'off',
  },
  parserOptions: {
    ecmaVersion: 'latest',
  },

  // 配置忽略的文件规则 https://eslint.org/docs/latest/use/configure/ignore#ignorepatterns-in-config-files
  ignorePatterns: ['src/assets/**/*.js', 'scripts/**/*.js'],
}
