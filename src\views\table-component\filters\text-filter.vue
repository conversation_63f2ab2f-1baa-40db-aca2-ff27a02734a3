<template>
  <Card class="text-filter">
    <Space direction="vertical">
      <Select
        :options="stringConditionOptions"
        class="condiction-select"
        v-model:value="condition"
      />
      <Select mode="tags" class="text-select" v-model:value="data" />

      <div class="text-right">
        <Button type="primary" @click="handleConfirm">确定</Button>
      </div>
    </Space>
  </Card>
</template>

<script setup lang="ts">
import { Card, Space, Select, Button } from 'ant-design-vue'
import { ConditionEnum, stringConditionOptions } from '@/components/filter/constant'
import { ref } from 'vue'

const props = defineProps<{ onConfirm: (condition: ConditionEnum, value: string[]) => void }>()
const condition = ref(stringConditionOptions[0].value)
const data = ref<string[]>([])

const handleConfirm = () => {
  props.onConfirm(condition.value, data.value)
}
</script>

<style scoped lang="less">
.text-filter {
  width: 380px;
  background-color: white;
  font-size: 14px;
  border-radius: 8px;
  box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.05);
  :deep(.ant-space) {
    width: 100%;
  }
  .condiction-select {
    min-width: 104px;
  }
  .text-select {
    width: 100%;
  }
  .text-right {
    text-align: right;
  }
}
</style>
