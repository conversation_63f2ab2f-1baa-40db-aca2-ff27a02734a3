<template>
  <div class="page-full job-flow-service">
    <div class="content-div">
      <cardBox :title="'作业流服务配置'" :subTitle="'作业流服务配置信息'">
        <template #headerRight>
          <a-button type="primary" v-action:add @click="handleAddModal()">
            <template #icon><PlusOutlined /></template>
            新增</a-button
          >
          <!-- <a-button type="primary" @click="showMore = !showMore">
            <template #icon v-if="!showMore">
              <DownOutlined />
            </template>
            <template #icon v-else><UpOutlined /></template>
            {{ showMore ? '收起' : '搜索' }}
          </a-button> -->
        </template>
        <Table
          :columns="getColumns(1, { gridCode: 'jobFlowService' })"
          :getData="transgetJobFlowServiceList"
          ref="tableRef"
          :autoRequest="false"
          :searchFormState="params"
        >
          <template #search>
            <a-form-item :label="'IP地址'">
              <a-input v-model:value="params.serviceIp" placeholder="请输入IP地址" />
            </a-form-item>
            <a-form-item :label="'端口'">
              <a-input v-model:value="params.servicePort" placeholder="请输入端口" />
            </a-form-item>
            <a-form-item :label="'服务类型'">
              <a-select
                v-model:value="params.serviceType"
                allowClear
                :placeholder="'请选择服务类型'"
              >
                <a-select-option value="0">work</a-select-option>
                <a-select-option value="1">master</a-select-option>
                <a-select-option value="5">minwork</a-select-option>
              </a-select>
            </a-form-item>
          </template>
          <template #bodyCell="{ column, record, text }">
            <template v-if="column.dataIndex === 'serviceType'">
              {{ text || serviceTypeFilter(text) }}
            </template>
            <template v-if="column.dataIndex === 'hostPath'">
              <a class="table-cell" @click="logModal(text, '查看详情')">{{ text }}</a>
            </template>
            <!-- 原代码有两个dataPath -->
            <!-- <template v-if="column.dataIndex === 'dataPath'">
              <a class="table-cell" @click="logModal(text, '查看详情')">{{ text }}</a>
            </template> -->
            <template v-if="column.dataIndex === 'dataPath'">
              <a class="table-cell" @click="logModal(text, '查看详情')">{{ text }}</a>
            </template>
            <template v-if="column.dataIndex === 'execPath'">
              <a class="table-cell" @click="logModal(text, '查看详情')">{{ text }}</a>
            </template>
            <template v-if="column.dataIndex === 'logPath'">
              <a class="table-cell" @click="logModal(text, '查看详情')">{{ text }}</a>
            </template>
            <template v-if="column.dataIndex === 'action'">
              <div class="editable-row-operations">
                <a-button
                  type="link"
                  size="small"
                  v-action:edit
                  @click="() => handleEditModel(record)"
                  >修改</a-button
                >
                <a-button v-action:delete size="small" type="link" @click="handelDelete(record.id)"
                  >删除</a-button
                >
              </div>
            </template>
          </template>
        </Table>
      </cardBox>
    </div>

    <EditService
      :open="showModalRef"
      :currentItem="currentItemRef"
      :modalType="modalTypeRef"
      :workList="workList"
      @success="loadData"
    />
  </div>
</template>
<script lang="ts" setup>
import { ref } from 'vue'
import { PlusOutlined } from '@ant-design/icons-vue'
import { Table } from '@fs/fs-components'
import cardBox from '@/components/card-box/card-box.vue'
import {
  deleteJobFlowService,
  getJobFlowServiceList,
} from '@/api/services/monitor-maintenance/flow-config'
import { getMenuGrids } from '@/utils/menu-grids'
import type { GridCol } from '@/api/services/data-asset/config-manager'
import { useUserStore } from '@/stores/user'
import { storeToRefs } from 'pinia'
import { logModal } from '@/utils/log-modal'
import { message, Modal } from 'ant-design-vue'
import EditService from './edit-service.vue'
const route = useRoute()
const userStore = useUserStore()
const { userInfo } = storeToRefs(userStore)

const tableRef = ref<InstanceType<typeof Table> | null>(null)
const showModalRef = ref(false)
const params = ref({
  serviceIp: '',
  servicePort: '',
  serviceType: '',
})
const gridCol = ref<GridCol[]>([])
const actionWidth = 135
const initFormItem = () => {
  return {
    serviceType: 0,
    serviceIp: '',
    logicId: '',
    sshPort: 22,
    servicePort: 8080,
    filePort: 12346,
    hostUser: '',
    hostPwd: '',
    // threadNum: '',
    execPath: '',
    hostPath: '',
    dataPath: '',
    logPath: '',
    componentId: '',
    workerId: null,
  }
}
const currentItemRef = ref<{ [x: string]: any }>(initFormItem())

const modalTypeRef = ref('add')
const workList = ref<any[]>([])
// 打开弹出层
const handleAddModal = () => {
  currentItemRef.value = initFormItem()
  showModalRef.value = true
  modalTypeRef.value = 'add'
}

const handleEditModel = (record: any) => {
  currentItemRef.value = record
  // console.log('handleEditModel', record, currentItemRef.value)
  showModalRef.value = true
  modalTypeRef.value = 'edit'
}

const handelDelete = (id: string) => {
  // console.log('handelDelete', id, userStore.userInfo.loginAccount)
  Modal.confirm({
    title: '提示',
    content: '确定要删除吗 ?',
    cancelText: '取消',
    okText: '确认',
    onOk() {
      deleteJobFlowService({ id, ownerId: userStore.userInfo.loginAccount }).then(() => {
        tableRef.value?.getTableData()
        message.success('删除成功')
      })
    },
  })
}
/**
 * table列表展示数据用到的字段列表
 * @param index 表单下标
 * @param config 更多配置
 * config.orderIndex: 是否自增序号
 * config.hideactionable: 是否隐藏操作栏
 * config.fixedAction: 固定操作栏
 */
const getColumns = (index: number, config: { [x: string]: any } = {}) => {
  if (config.gridCode) {
    // 如果有配置 gridCode，优先使用 gridCode 查找表格列字段
    const gridCode = config.gridCode
    const grid = gridCol.value.find((i) => i.gridCode === gridCode)
    if (grid) {
      return grid.gridColumns
    }
  }
  let data = JSON.parse(
    JSON.stringify((gridCol.value[index] && gridCol.value[index].gridColumns) || []),
  )
  // console.log('终于获取到了', data, gridCol.value, index)
  // 是否隐藏操作栏
  if (config.hideactionable) {
    if (data.length > 1) data = data.slice(0, -1)
  }
  // console.log('data', data)
  return data
}

const serviceTypeFilter = (data: number) => {
  console.log('serviceTypeFilter', data)
  let result = ''
  switch (data) {
    case 0:
      result = 'work'
      break
    case 1:
      result = 'master'
      break
    case 5:
      result = 'minwork'
      break
    default:
      result = '--'
      break
  }
  return result
}

onBeforeMount(() => {
  const menuId = route.meta.menuId || ''
  const { userId, tenantId } = userInfo.value
  const header = {
    opUser: userId,
    tenantId,
  }
  if (!menuId) {
    throw new Error('该路由没有menuId')
  }
  getMenuGrids(menuId, header).then((res: any) => {
    gridCol.value = res.data.map((i: any) => {
      let width = 0 + actionWidth
      const gridColumns = (i.gridColumns || [])
        .map((ii: any) => {
          let colWidth = Number(ii.width) || 180
          width += colWidth
          let obj: any = {
            title: ii.columnCaption,
            dataIndex: ii.columnName,
            show: true,
            ellipsis: true,
            width: colWidth,
            align: 'center',
            scopedSlots: { customRender: ii.columnName },
          }
          if (ii.columnType === '7') {
            // eslint-disable-next-line no-new-func
            obj.columnInfo =
              ii.columnTypeParam && ii.columnTypeParam[0] === '{'
                ? new Function('return ' + ii.columnTypeParam)()
                : {}
          }
          return obj
        })
        .concat({
          // title: '操作',
          title: '操作',
          // @ts-ignore
          show: true,
          width: actionWidth,
          align: 'center',
          fixed: 'right',
          dataIndex: 'action',
          scopedSlots: { customRender: 'action' },
        })
      return {
        gridId: i.gridId,
        gridName: i.gridName,
        gridCode: i.gridCode,
        width,
        gridColumns,
      }
    })
  })
})

const transgetJobFlowServiceList = (params: any) =>
  getJobFlowServiceList(params).then((d: any) => {
    // 接口应该返回records 前端做处理
    workList.value = []
    d.data.records.forEach((item) => {
      const obj = {
        serviceType: 0,
        id: '',
        serviceIp: '',
      }
      if (item.serviceType === 0) {
        obj.id = item.id
        obj.serviceIp = item.serviceIp || ''
        workList.value.push(obj)
      }
    })
    d.data = d.data.records
    console.log('transgetJobFlowServiceList', workList.value)
    return d
  })
const fetchTableData = async () => {
  try {
    setTimeout(() => {
      tableRef.value?.getTableData()
    }, 20)
  } catch (error) {
    console.error('获取数据表列表失败:', error)
  }
}

// customRow(column: any) {
//   column.forEach((e: any, index: number) => {
//     column[index].className = e.key // 给数组中的每一列加上一个类名
//     column[index].id = e.key // 给数组中的每一列加上一个类名
//   })
// }

const loadData = (btnType: string) => {
  // console.log('loadData', btnType)
  if (btnType === 'formSubmit') {
    fetchTableData()
  }
  showModalRef.value = false
}

onMounted(() => {
  fetchTableData()
})
</script>
<style lang="less" scoped>
.job-flow-service {
  height: 100%;
  .header-div {
    display: flex;
    align-items: center;
    height: 48px;
    padding: 0px 12px 0 20px;
    border-bottom: 1px solid #0000001a;
  }
  .content-div {
    height: calc(100% - 48px);
    :deep(.box-main) {
      overflow: visible;
    }
  }
  :deep(.ant-btn) {
    margin-left: 8px;
  }
}
</style>
