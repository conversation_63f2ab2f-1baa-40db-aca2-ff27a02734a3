<script lang="ts" setup>
import { Badge, type TableProps } from 'ant-design-vue'
import Filter from '@/components/filter/filter.vue'
import Summary from '@/components/summary/summary.vue'
import HeaderField from './header-field.vue'
import fieldComponent from './field.vue'
import fieldDetails from './field-details.vue'
import fileterList from './filter-list.vue'
import type { FilterItem } from '@/components/filter/type'
import { TableOutlined, DotChartOutlined } from '@ant-design/icons-vue'
import type {
  PreviewData,
  CardData,
  IColumn,
  ServerFieldItem,
} from '@/api/services/indicator/type.ts'
import { ColumnType } from '@/api/services/indicator/type'
import { ConditionEnum, FormType } from '@/components/filter/constant'
import dayjs, { Dayjs } from 'dayjs'
import type { MergedColumnItem } from './type'
import { useStorage } from '@vueuse/core'

const props = withDefaults(
  defineProps<{
    data?: PreviewData
    loading?: boolean
    showHeader?: boolean
    requestData?: (filterList: FilterItem[]) => void
    filterData?: ServerFieldItem[]
  }>(),
  {
    showHeader: true,
  },
)
const summaryRef = ref()
const pageSize = ref('2000')
const contentType = ref('table')
const fieldDetailsRef = ref()
const designer = ref(null)
const visiblePageSize = ref(false)
const headerList = ref([
  {
    field: 'First Name',
    type: 'text',
    des: ' ',
    zone: 'GMT',
    earliestTime: '2022-01-01 00:00:00',
    endTime: '2022-01-01 00:00:00',
    titleShowType: 'text',
  },
  {
    field: 'created time',
    type: 'datetime',
    des: ' ',
    zone: 'GMT',
    earliestTime: '2022-01-01 00:00:00',
    endTime: '2022-01-01 00:00:00',
    titleShowType: 'text',
  },
])
const modalTitle = ref('字段详情')
const fieldList: any = ref([
  {
    field: 'First Name',
    showType: 'text',
    fieldType: 'boolean',
    value: 'Marchy',
  },
  {
    field: 'Created Date',
    showType: 'text',
    fieldType: 'datetime',
    value: '2022-01-01 00:00:00',
    backgroundColor: 'red',
  },
  {
    field: 'State',
    showType: 'text',
    fieldType: 'text',
    value: 78,
    showProgress: true,
  },
])
const fieldDetailsData = ref({})
const columns = ref<IColumn[]>([])
const filterColumns = computed<TableProps['columns']>(() => {
  console.log('🚀 ~ computed ~ columns.value', columns.value)

  const res = columns.value.filter((item) => item.isShow)
  return res
})
/**
 * 过滤器数据
 */
const filterCompData = ref<FilterItem[]>()
const dataSource = ref<CardData[]>([])
const openFilter = ref(false)
const filterCount = ref(0)

const mapColumnType2FormType = (colType: ColumnType) => {
  switch (colType) {
    case ColumnType.BOOLEAN:
      return FormType.checkbox
    case ColumnType.DATE:
      return FormType.date
    case ColumnType.EMAIL:
      return FormType.input
    case ColumnType.NUMBER:
      return FormType.range
    case ColumnType.TEXT:
      return FormType.input
    default:
      return FormType.input
  }
}
const getInitValue = (colType: ColumnType) => {
  switch (colType) {
    case ColumnType.BOOLEAN:
      return true
    case ColumnType.DATE:
      return null
    case ColumnType.EMAIL:
      return ''
    case ColumnType.NUMBER:
      return []
    case ColumnType.TEXT:
      return ''
    default:
      return ''
  }
}
const getInitOptions = (colType: ColumnType) => {
  switch (colType) {
    case ColumnType.BOOLEAN:
      return [
        {
          label: '是',
          value: true,
        },
        {
          label: '否',
          value: false,
        },
      ]
    default:
      return null
  }
}
const initFilterCompData = (): FilterItem[] => {
  const res =
    columns.value
      .filter((item) => item.key !== 'action')
      .map<FilterItem>((item) => {
        const obj: any = {
          field: item.field,
          columnType: item.type,
          comment: item.comment,
          originType: mapColumnType2FormType(item.type),
          value: getInitValue(item.type),
          componentProps: {
            isNumber: item.type === ColumnType.NUMBER,
            options: getInitOptions(item.type),
          },
          condition: item.type === ColumnType.NUMBER ? ConditionEnum.between : ConditionEnum.equal,
        }
        if (item.type === ColumnType.DATE) {
          obj.componentProps['onConditionChange'] = (
            condition?: ConditionEnum,
            filter?: FilterItem,
          ) => {
            if (filter) {
              filter.condition = condition
            }
            console.log('日期条件', filter)
          }
        }
        return obj
      }) ?? []
  return res
}
const clearFilter = () => {
  filterCompData.value = initFilterCompData()
  onFilterConfirm([])
  filterCount.value = 0
}
const handleClickFilter = () => {
  openFilter.value = true
}
const handleSummary = () => {
  summaryRef.value.show()
}
const handleDetalsField = (record: CardData) => {
  fieldDetailsData.value = record
  fieldDetailsRef.value.show()
}
const onFilterConfirm = (val: FilterItem[]) => {
  console.log('过滤器数据', val)
  filterCount.value = val.length
  props.requestData?.(val)
}
const handlePageSizeChange = () => {}

//  结合设置
const setColumns = (params: any) => {
  const _columns: any = JSON.parse(JSON.stringify(params))
  _columns.forEach((item: any) => {
    // console.log('🚀 ~ _columns.forEach ~ item:', item)
    item['titleShowType'] = 'text'
    item['showType'] = 'text'
    item['isShow'] = item.hasOwnProperty.call(item, 'isShow') ? item['isShow'] : 'show'
    item['showProgress'] = false
    // item['backgroundColor'] = '#e0eefa'
  })
  _columns.push({
    title: '操作',
    dataIndex: 'action',
    key: 'action',
    scopedSlots: { customRender: 'action' },
    width: 150,
    fixed: 'right',
  })
  columns.value = _columns
}
type MergedColData = { cols: MergedColumnItem[]; delimiter: string[] }
const mergedColData = useStorage<MergedColData[]>('mergedColData', [])
const scrollToRight = () => {
  const tableBody = document.querySelector('.ant-table-body')
  if (tableBody) {
    tableBody.scrollLeft = tableBody.scrollWidth
  }
}
const addMergeColumn = (data: MergedColData) => {
  const cols = data.cols
  const delimiter = data.delimiter
  // 构建列数据
  const fields = cols.map((item) => item.field)
  const dataIndex = fields.join('_')
  const hasProperty =
    dataSource.value.findIndex((item: Record<string, any>) => Reflect.has(item, dataIndex)) !== -1
  if (!hasProperty) {
    const title = '合并 ' + fields.join(',')
    const field = dataIndex
    const type = ColumnType.TEXT
    const key = Date.now().toString()
    const isShow = true
    columns.value.push({
      title,
      dataIndex,
      field,
      type,
      key,
      isShow,
      description: title,
    })
    dataSource.value.forEach((item: Record<string, any>) => {
      const valArr = fields.map((field) => item[field])
      if (delimiter.length) {
        const val = valArr.reduce((acc, curr, index) => {
          const res = acc + curr + (delimiter[index] || '')
          return res
        }, '')
        item[dataIndex] = val
      } else {
        item[dataIndex] = valArr.join(' ')
      }
    })
  }
}
const addMergeColumns = (data: MergedColData[]) => {
  data.forEach((item) => {
    addMergeColumn(item)
  })
}
watch(mergedColData, addMergeColumns)
const handleMergeColumn = (mergedCols: MergedColumnItem[], delimiter: string[] = []) => {
  console.log('合并栏目', mergedCols, delimiter)
  mergedColData.value = [...mergedColData.value, { cols: mergedCols, delimiter }]
  setTimeout(() => {
    scrollToRight()
  }, 200)
}
const setData = (data: any) => {
  console.log('🚀 ~ setData ~ data:', data)
  setColumns(data.titleList)
  dataSource.value = JSON.parse(JSON.stringify(data.dataList))
}

watch(
  () => props.data,
  (val) => {
    if (val) {
      setData(val)
      // initTableData()
    }
  },
)

// defineExpose({
//   setData,
// })
onMounted(() => {
  if (props.data) {
    props.data.titleList.forEach((item) => (item.isShow = true))
    setData(props.data)
    filterCompData.value = initFilterCompData()
    if (mergedColData.value.length) {
      addMergeColumns(mergedColData.value)
    }
  }
  // console.log('🚀 ~ onMounted ~ dataSource.value:', dataSource.value)
})
</script>
<template>
  <div class="table-container">
    <a-flex class="mb-4" justify="end" v-if="showHeader" :gap="10">
      <a-button type="default" @click="clearFilter">清空条件</a-button>
      <Badge :count="filterCount">
        <a-button type="primary" @click="handleClickFilter" :loading="loading">筛选器</a-button>
      </Badge>
    </a-flex>
    <!-- 筛选器选择列表 -->
    <!-- <fileterList /> -->
    <a-table
      :loading="loading"
      :columns="filterColumns"
      :data-source="dataSource"
      :scroll="{ y: 'calc(65vh - 200px)', x: columns.length * 150 + 'px' }"
    >
      <template #headerCell="{ column }: { column: IColumn }">
        <HeaderField
          v-if="column.dataIndex !== 'action'"
          :data="column"
          :columns="columns"
          :onMergeColumn="handleMergeColumn"
        ></HeaderField>
      </template>
      <template #bodyCell="{ column, record }: { column: IColumn; record: CardData }">
        <fieldComponent
          v-if="column.dataIndex !== 'action'"
          :props-item="record"
          :column="column"
        ></fieldComponent>
        <template v-else>
          <a-button class="ml-10" type="primary" @click="handleDetalsField(record)">
            字段详情
          </a-button>
        </template>
      </template>
    </a-table>
    <div class="pagination-box">
      <!-- <div class="pagination-center">
        <a-radio-group v-model:value="contentType">
          <a-radio-button value="chart">
            <DotChartOutlined />
          </a-radio-button>
          <a-radio-button value="table">
            <TableOutlined />
          </a-radio-button>
        </a-radio-group>
      </div> -->
      <div class="pagination-right">
        <a-popover v-model:open="visiblePageSize" trigger="click" placement="top">
          <template #content>
            <a-radio-group v-model:value="pageSize" style="display: flex; flex-direction: column">
              <a-radio value="2000" class="mb-10">显示最大值（ 2000 ）</a-radio>
              <a-radio value="">
                <a-input-number
                  v-model:value="pageSize"
                  style="width: 100px"
                  :max="2000"
                  :min="1"
                  @change="handlePageSizeChange"
                />
              </a-radio>
            </a-radio-group>
          </template>
        </a-popover>
      </div>
    </div>
    <Filter :onConfirm="onFilterConfirm" v-model:open="openFilter" :filters="filterCompData" />
    <Summary ref="summaryRef"></Summary>
    <fieldDetails
      ref="fieldDetailsRef"
      :modalTitle="modalTitle"
      :data="fieldDetailsData"
    ></fieldDetails>
  </div>
</template>
<style lang="less" scoped>
.table-container {
  :deep(.ant-table-cell) {
    padding: 2px !important;
  }
  :deep(.ant-table-cell):hover {
    background-color: #edf5fc !important;
  }
  .pagination-box {
    padding: 10px 10px;
    display: flex;
    line-height: 40px;
    justify-content: space-between;
    color: #696e7b;
    font-weight: 700;
    background-color: #fff;
    width: 100%;
  }
}
</style>
