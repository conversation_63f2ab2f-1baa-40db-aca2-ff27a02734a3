<template>
  <a-modal
    :open="removeMenuModal"
    width="400px"
    :confirm-loading="removeConfirmLoading"
    @ok="ModalhandleOk"
    @cancel="ModalCancel"
  >
    <template #title>
      <ExclamationCircleOutlined :style="{ fontSize: '16px', color: '#faad14' }" /> 提示
    </template>
    <p>是否确认删除</p>
  </a-modal>
</template>
<script lang="ts" setup>
import { ExclamationCircleOutlined } from '@ant-design/icons-vue'
defineProps({
  removeMenuModal: {
    type: Boolean,
    default: false,
  },
  removeConfirmLoading: Boolean,
})
const emits = defineEmits(['removeModalhandleOk', 'removeModalhandleCancel'])
function ModalhandleOk() {
  emits('removeModalhandleOk')
}
function ModalCancel() {
  emits('removeModalhandleCancel')
}
</script>
