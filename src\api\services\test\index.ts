export async function getDataHandle() {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        code: '000000',
        data: {
          totalRecords: 3,
          records: [
            {
              id: 1,
              name: '张三',
              age: 23,
            },
            {
              id: 2,
              name: '李四',
              age: 32,
            },
            {
              id: 3,
              name: '王五',
              age: 18,
            },
          ],
        },
      })
    }, 500)
  })
}

export async function addDataHandle(data: any) {
  return new Promise((resolve) => {
    setTimeout(() => {
      console.log(data)
      resolve({
        code: '000000',
        data: '添加成功',
      })
    }, 500)
  })
}

export async function editDataHanlde(data: any) {
  return new Promise((resolve) => {
    setTimeout(() => {
      console.log(data)
      resolve({
        code: '000000',
        data: '编辑成功',
      })
    }, 500)
  })
}

export async function delDataHanlde(data: any) {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        code: '000000',
        data: '删除成功',
      })
      console.log(data)
    }, 500)
  })
}
