<template>
  <div>
    <div class="add-box">
      <a-button type="primary" @click="addProject">新增项目</a-button>
    </div>
    <a-table :dataSource="dataSource" :columns="columns">
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <a-button type="link" @click="syncHandle(record)">同步</a-button>
          <a-button type="link" @click="editHandle(record)">编辑</a-button>
          <a-popconfirm
            title="是否确认删除"
            ok-text="是"
            cancel-text="否"
            @confirm="confirm(record)"
          >
            <a-button type="link">删除</a-button>
          </a-popconfirm>
        </template>
        <template v-else>
          {{ record[column.key] }}
        </template>
      </template>
    </a-table>

    <a-modal v-model:open="open" :title="type === 'add' ? '新增项目' : '编辑项目'" @ok="handleOk">
      <a-form
        :model="formState"
        name="basic"
        :label-col="{ span: 8 }"
        :wrapper-col="{ span: 16 }"
        ref="formRef"
      >
        <a-form-item
          label="swagger文档地址"
          name="url"
          :rules="[{ required: true, message: '请输入swagger文档地址' }]"
        >
          <a-textarea v-model:value="formState.url" placeholder="请输入swagger文档地址" />
        </a-form-item>
      </a-form>
      <template #footer>
        <a-button key="submit" @click="open = false">取消</a-button>
        <a-button key="submit" :loading="loading" type="primary" @click="handleOk">确定</a-button>
      </template>
    </a-modal>

    <a-modal
      v-model:open="open2"
      title="同步"
      width="900px"
      @ok="saveSync"
      okText="保存"
      :confirmLoading="spinning"
    >
      <a-spin tip="Loading..." :spinning="spinning">
        <div class="box">
          <div class="left-box">
            <div class="title">
              <a-checkbox v-model:checked="Allcheck" @change="checkAllHandle">新数据</a-checkbox>
            </div>
            <div class="content">
              <div v-for="(item, index) in treeData" :key="index" class="box-item">
                <a-checkbox v-model:checked="item.check" @change="handleCheck(item, index)"
                  >{{ item.moduleNameCn }} {{ item.moduleName }}</a-checkbox
                >
                <div
                  v-for="(item2, index2) in item.interfaceInfos"
                  :key="index2"
                  style="padding-left: 40px"
                  class="box-item"
                >
                  <a-checkbox v-model:checked="item2.check">
                    <a-tooltip placement="topLeft">
                      {{ item2.interfaceNameCn }} {{ item2.interfaceName }}
                      <template #title>
                        <div>
                          传入参数:
                          {{ getParam(item2.params) }}
                        </div>
                        <div>返回参数: {{ getRetrun(item2.response) }}</div>
                      </template>
                    </a-tooltip>
                    <a-tag color="green" v-if="item2.type === 'add'"> 新增</a-tag>
                    <a-tag color="cyan" v-if="item2.type === 'update'"> 修改</a-tag>
                  </a-checkbox>
                </div>
              </div>
            </div>
          </div>

          <div class="right-box">
            <div class="title">历史数据</div>
            <div class="content">
              <div v-for="(item, index) in selectData.modules" :key="index" class="box-item">
                {{ item.moduleNameCn }} {{ item.moduleName }}
                <div
                  v-for="(item2, index2) in item.interfaceInfos"
                  :key="index2"
                  style="padding-left: 40px"
                  class="box-item"
                >
                  {{ item2.interfaceNameCn }} {{ item2.interfaceName }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </a-spin>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import {
  getApiData,
  saveUrlData,
  getUrlDetail,
  synchronous,
  delUrl,
  editUrlData,
  generateDeploy,
} from '@/api/services/manage'
import { message } from 'ant-design-vue/es/components'
import { getParam, getRetrun } from '@/utils/tool'

const open = ref(false)
const open2 = ref(false)
const spinning = ref(false)
const Allcheck = ref(true)
const formState = ref({
  url: '',
})
const formRef = ref()
const type = ref('add')
const selectData = ref<any>({})
const dataSource = ref([])
const treeData = ref<any[]>([])
const loading = ref(false)
const columns = [
  {
    title: '项目路径',
    dataIndex: 'project',
    key: 'project',
  },
  {
    title: 'swagger地址',
    dataIndex: 'url',
    key: 'url',
  },
  {
    title: '时间',
    dataIndex: 'time',
    key: 'time',
  },
  {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
  },
]

async function getApiHandle() {
  const { data } = await getApiData()
  const list = data.filter((item: any) => item.project !== 'gpt-api')
  dataSource.value = list
}

function addProject() {
  type.value = 'add'
  open.value = true
  formState.value.url = ''
}

function handleOk() {
  formRef.value.validate().then(async () => {
    try {
      if (type.value === 'edit' && selectData.value.url === formState.value.url) {
        open.value = false
        // 编辑时未修改url
        return
      }
      loading.value = true
      const handle = type.value === 'edit' ? editUrlData : saveUrlData
      const { code } = await handle({ ...formState.value, path: selectData.value.project })
      if (code !== '000000') {
        message.error('获取swagger文档失败')
        return
      }
      open.value = false
      formState.value.url = ''
      const tips = type.value === 'add' ? '新增成功' : '编辑成功'
      message.success(tips)
      getApiHandle()
    } finally {
      loading.value = false
    }
  })
}

async function saveSync() {
  try {
    spinning.value = true
    let list = JSON.parse(JSON.stringify(treeData.value))
    list.map((item: any) => {
      item.interfaceInfos = item.interfaceInfos.filter((val: any) => val.check)
    })
    list = list.filter((val: any) => val?.interfaceInfos.length)
    await synchronous({ list: list, project: selectData.value.project })
    const obj = [
      {
        project: selectData.value.project,
        modules: list,
      },
    ]
    await generateDeploy({ project: obj })
    message.success('同步成功')
    open2.value = false
    spinning.value = false
    getApiHandle()
  } catch (error) {
    spinning.value = false
    console.error('同步错误：' + error)
  }
}

async function syncHandle(record: any) {
  open2.value = true
  spinning.value = true
  treeData.value = []
  selectData.value = record
  const { data } = await getUrlDetail({ url: record.url })
  // data.map((item: any) => {
  //   item.check = false
  //   item.interfaceInfos.map((val: any) => {
  //     val.check = false
  //   })
  // })
  const newList = compareAndMarkData(data, record.modules)
  treeData.value = newList
  spinning.value = false
  checkAllHandle({ target: { checked: true } })
}

function handleCheck(item: any, index: number) {
  item.interfaceInfos.map((val: any) => {
    val.check = item.check
  })
}

function checkAllHandle(val: any) {
  const check = val.target.checked
  treeData.value.map((item) => {
    item.check = check
    item.interfaceInfos.map((item: any) => {
      item.check = check
    })
  })
}

async function confirm(record: any) {
  await delUrl({ path: record.project })
  message.success('删除成功')
  getApiHandle()
}

onMounted(() => {
  getApiHandle()
})

// 对比两边数据 找出新增和修改函数
function compareAndMarkData(leftData: any, rightData: any) {
  const markedLeftData = leftData.map((leftModule: { moduleName: any; interfaceInfos: any[] }) => {
    const rightModule = rightData.find(
      (rightItem: { moduleName: any }) => rightItem.moduleName === leftModule.moduleName,
    )
    if (!rightModule) {
      leftModule.interfaceInfos.forEach((interfaceInfo: any) => {
        interfaceInfo.type = 'add'
      })
      return leftModule
    }
    const markedInterfaceInfos = leftModule.interfaceInfos.map((leftInterfaceInfo: any) => {
      const rightInterfaceInfo = rightModule.interfaceInfos.find(
        (rightItem: { interfaceName: any }) =>
          rightItem.interfaceName === leftInterfaceInfo.interfaceName,
      )
      if (!rightInterfaceInfo) {
        leftInterfaceInfo.type = 'add'
        return leftInterfaceInfo
      }
      if (compareParams(leftInterfaceInfo.params, rightInterfaceInfo.params)) {
        leftInterfaceInfo.type = 'update'
      }
      if (compareResponse(leftInterfaceInfo?.response || [], rightInterfaceInfo?.response || [])) {
        leftInterfaceInfo.type = 'update'
      }
      return leftInterfaceInfo
    })
    return {
      ...leftModule,
      interfaceInfos: markedInterfaceInfos,
    }
  })
  return markedLeftData
}

function compareParams(leftParams: string | any[], rightParams: string | any[]) {
  if (leftParams.length !== rightParams.length) {
    return true
  }
  for (let i = 0; i < leftParams.length; i++) {
    const leftParam = leftParams[i]
    const rightParam = rightParams[i]
    if (
      leftParam.name !== rightParam.name ||
      leftParam.type !== rightParam.type ||
      leftParam.description !== rightParam.description
    ) {
      return true
    }
  }
  return false
}

function compareResponse(leftParams: string | any[], rightParams: string | any[]) {
  if (leftParams.length !== rightParams.length) {
    return true
  }
  for (let i = 0; i < leftParams.length; i++) {
    const leftParam = leftParams[i]
    const rightParam = rightParams[i]
    if (Array.isArray(leftParam.type) && Array.isArray(rightParam.type)) {
      if (leftParam.type.length !== rightParam.type.length) {
        console.log('长度不一致')
        return true
      }
      if (JSON.stringify(leftParam.type) !== JSON.stringify(rightParam.type)) {
        console.log('内容不一致')
        return true
      }
    }
    if (leftParam.name !== rightParam.name) {
      console.log('字段内容不一致 顺序有变化')
      return true
    }
  }
  return false
}

function editHandle(record: any) {
  type.value = 'edit'
  selectData.value = record
  open.value = true
  formState.value.url = record.url
}
</script>
<style scoped lang="less">
.add-box {
  margin-bottom: 20px;
}
.box {
  display: flex;

  .left-box {
    width: 50%;
    border: 1px solid #ccc;
    margin-right: 20px;
    height: 600px;
    overflow: auto;
  }
  .right-box {
    width: 50%;
    border: 1px solid #ccc;
    height: 600px;
    overflow: auto;
  }
  .box-item {
    padding: 5px 0px;
    cursor: pointer;
  }
  .title {
    border-bottom: 1px solid #ccc;
    padding: 12px;
  }
  .content {
    padding: 12px;
  }
}
</style>
