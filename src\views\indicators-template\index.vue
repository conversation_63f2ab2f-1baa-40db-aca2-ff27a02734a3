<template>
  <div class="indicators-template-container">
    <cardBox
      title="指标计算模板管理"
      subTitle="指标计算管理，用于创建、编辑指标描述、分类、计算规则描述等属性，可视化设计。"
    >
      <template #headerRight>
        <a-space>
          <a-button type="primary" @click="addTableHandel" :loading="queryAddButtonStatus"
            >新增指标模板</a-button
          >
          <a-button type="primary" @click="showTemplateTypeModal">管理指标业务类型</a-button>
        </a-space>
      </template>
      <Table
        :columns="columns"
        :getData="getData"
        :pagination="pageEnterprisePagination"
        ref="tableRef"
        :searchFormState="form"
        :table-config="{
          scroll: { x: '1800px' },
        }"
      >
        <template #search>
          <a-form-item label="" name="formName">
            <a-input v-model:value="form.formName" placeholder="请输入指标配置模板名称" />
          </a-form-item>
          <a-form-item label="" name="businessName">
            <a-select
              v-model:value="form.businessName"
              :options="businessTypeOptions"
              placeholder="请选择业务类型"
            >
            </a-select>
          </a-form-item>
          <a-form-item label="" name="status">
            <a-select v-model:value="form.status" placeholder="请选择状态">
              <a-select-option value="">全部</a-select-option>
              <a-select-option value="1">已提交</a-select-option>
              <a-select-option value="0">编辑中</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="" name="startTime">
            <a-date-picker
              placeholder="请选择开始时间"
              v-model:value="form.startTime"
              format="YYYY-MM-DD HH:mm:ss"
            />
          </a-form-item>
          <a-form-item label="" name="endTime">
            <a-date-picker
              placeholder="请选择结束时间"
              v-model:value="form.endTime"
              format="YYYY-MM-DD HH:mm:ss"
            />
          </a-form-item>
          <a-form-item label="" name="isRelease">
            <a-select v-model:value="form.isRelease" placeholder="请选择是否已发布">
              <a-select-option value="">全部</a-select-option>
              <a-select-option value="1">已发布</a-select-option>
              <a-select-option value="0">未发布</a-select-option>
            </a-select>
          </a-form-item>
        </template>
        <template #bodyCell="data">
          <template v-if="data.column.dataIndex === 'isDeleted'">
            {{ data.record.isDeleted ? '是' : '否' }}
          </template>
          <template v-else-if="data.column.dataIndex === 'status'">
            {{ data.record.status ? '编辑中' : '已提交' }}
          </template>
          <template v-else-if="data.column.dataIndex === 'isRelease'">
            {{ data.record.isRelease ? '已发布' : '未发布' }}
          </template>
          <template v-else-if="data.column.dataIndex === 'action'">
            <a-button type="link" @click="createIndicator(data.record)">新增指标</a-button>
            <a-button type="link" @click="handleView(data.record)">查看</a-button>
            <a-button type="link" @click="editTableHandle(data.record)">编辑</a-button>

            <a-popconfirm
              title="是否确认发布"
              @confirm="released(data.record)"
              v-if="data.record.isRelease === 0"
            >
              <a-button type="link">发布</a-button>
            </a-popconfirm>

            <a-popconfirm title="是否确认删除" @confirm="confirm(data.record)">
              <a-button type="link">删除</a-button>
            </a-popconfirm>
          </template>
          <template v-else>
            {{ data.value }}
          </template>
        </template>
      </Table>
    </cardBox>
    <a-modal
      v-model:open="modalValue"
      @ok="modalHandleOk"
      :title="modalType === 'edit' ? '编辑' : '新增'"
      @cancel="cancel"
      width="800px"
      :confirm-loading="confirmLoading"
    >
      <a-form
        ref="formRef"
        :model="modalForm"
        :rules="rules"
        :label-col="labelCol"
        :wrapper-col="wrapperCol"
      >
        <a-form-item
          label="指标配置模板编号"
          name="formCode"
          :rules="[{ required: true, message: '请输入指标配置模板编号' }]"
        >
          <a-input v-model:value="modalForm.formCode" placeholder="请输入指标配置模板编号" />
        </a-form-item>

        <a-form-item
          label="指标配置模板名称"
          name="formName"
          :rules="[{ required: true, message: '请输入指标配置模板名称' }]"
        >
          <a-input v-model:value="modalForm.formName" placeholder="请输入指标配置模板名称" />
        </a-form-item>

        <a-form-item
          label=" 备注"
          name="remarks"
          :rules="[{ required: true, message: '请输入 备注' }]"
        >
          <a-input v-model:value="modalForm.remarks" placeholder="请输入 备注" />
        </a-form-item>

        <a-form-item
          label="字段数据"
          name="fieldsData"
          :rules="[{ required: false, message: '请输入字段数据' }]"
        >
          <a-input v-model:value="modalForm.fieldsData" placeholder="请输入字段数据" />
        </a-form-item>

        <a-form-item
          label="组件数据"
          name="compData"
          :rules="[{ required: false, message: '请输入组件数据' }]"
        >
          <a-input v-model:value="modalForm.compData" placeholder="请输入组件数据" />
        </a-form-item>
      </a-form>
    </a-modal>
    <a-drawer title="查看详情" v-model:open="showDetails">
      <form-create
        v-if="fcRule.length"
        v-model="formData"
        v-model:api="fApi"
        :rule="fcRule"
        :option="fcOption"
        @submit="submit"
      ></form-create>
    </a-drawer>

    <!-- 业务类型对话框 -->
    <templateTypeModal ref="templateTypeModalRef"></templateTypeModal>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Table } from '@fs/fs-components'
import type { Rule } from 'ant-design-vue/es/form'
import { Modal } from 'ant-design-vue'
import { message } from 'ant-design-vue/es/components'
import {
  getIndexFormDataListByPage,
  addIndexTemplate,
  delIndexTemplate,
  deployIndexFormData,
  getSingleIndexFormData,
} from '@/api/indexmanage/indexform/indexform'
import { getBusinessPageList } from '@/api/indexmanage/bussinesmanage/bussinesmanage'
import cardBox from '@/components/card-box/card-box.vue'

import { useRouter } from 'vue-router'
import FcDesigner from '@form-create/designer'
import templateTypeModal from './component/template-type-modal.vue'
import { COLUMS } from './const'
const fcRule = ref([])
// const fcOption = ref(FcDesigner.formCreate.parseJson('{"form":{"inline":false,"hideRequiredAsterisk":false,"labelPosition":"right","size":"default","labelWidth":"125px","showMessage":true},"resetBtn":{"show":false,"innerText":"重置"},"submitBtn":{"show":false,"innerText":"提交"}}'))
const fcOption = ref({})
const formData = ref({})
const fApi = ref(null)
const router = useRouter()
const labelCol = { span: 5 }
const wrapperCol = { span: 13 }
const form: Record<string, any> = ref({
  formName: '',
  isDeleted: '1',
  status: '',
  startTime: '',
  endTime: '',
  isRelease: '',
  businessName: null,
})
const pageEnterprisePagination = ref({
  pageIndex: 1,
  pageSize: 10,
})

const queryAddButtonStatus = ref(false)

const columns = COLUMS

const rules: Record<string, Rule[]> = {}
const modalForm: Record<string, any> = ref({})
const modalValue = ref(false)
const modalType = ref<string>('')
const formRef = ref<HTMLFormElement | null>(null)
const confirmLoading = ref<boolean>(false)
const tableRef = ref<InstanceType<typeof Table> | null>(null)
const showDetails = ref(false)

const templateTypeModalRef = ref<typeof templateTypeModal>()

const showTemplateTypeModal = () => {
  templateTypeModalRef.value?.showModal()
}

const businessTypeVal = ref(null)
const businessTypeOptions = ref<{ label: string; value: string }[]>([])

const getBusinessTypeOptions = async () => {
  try {
    const { data } = await getBusinessPageList({
      pageIndex: 1,
      pageSize: 1000,
    })
    if (data.records.length > 0) {
      businessTypeOptions.value = data.records.map((item: any) => ({
        label: item.businessName,
        value: item.businessName,
      }))
      // businessTypeVal.value = data.records[0].businessName
    }
  } catch (error) {
    console.error('获取业务类型失败', error)
  }
}

getBusinessTypeOptions()

const getData = () => {
  return getIndexFormDataListByPage({
    pageIndex: pageEnterprisePagination.value.pageIndex,
    pageSize: pageEnterprisePagination.value.pageSize,
    formName: form.value.formName,
    isDeleted: form.value.isDeleted,
    status: form.value.status,
    startTime: form.value.startTime,
    endTime: form.value.endTime,
    isRelease: form.value.isRelease,
    businessName: form.value.businessName,
  })
}

const submit = () => {}

async function addTableHandel() {
  try {
    queryAddButtonStatus.value = true
    const {
      data: { totalRecords },
    } = await getBusinessPageList({
      pageIndex: 1,
      pageSize: 1,
    })
    if (totalRecords === 0) {
      await Modal.confirm({
        title: '提示',
        content: '检查到模板业务类型为空，请先新增业务类型后，再新增指标模板。',
        onOk: () => {
          showTemplateTypeModal()
        },
        okText: '新增模板业务类型',
      })

      return
    }
    router.push('/indicators-template/create')
  } catch (error) {
    console.log(error)
    message.error('查询业务类型失败，请稍后重试')
  } finally {
    queryAddButtonStatus.value = false
  }
}

function editTableHandle(data: any) {
  router.push('/indicators-template/create?formId=' + data.formId + '&type=edit')
}

const released = async (record: any) => {
  const res = await deployIndexFormData({
    formId: record.formId,
  })
  if (res.code === '000000') {
    message.success('发布成功')
    tableRef.value?.getTableData()
  }
}

const createIndicator = (record: any) => {
  router.push(`/indicator-list/create?formId=${record.formId}&businessType=${record.businessType}`)
}

/**
 * Cancel the modal and reset the form
 */
function cancel() {
  modalValue.value = false
  formRef.value?.resetFields()
}

async function confirm(data: any) {
  try {
    await delIndexTemplate(data)
    message.success('删除成功')
    tableRef.value?.getTableData()
  } catch (error) {
    console.log(error)
  }
}

const handleView = (record: any) => {
  getSingleIndexFormData({
    formId: record.formId,
  }).then((res: any) => {
    if (res.code === '000000') {
      showDetails.value = true
      fcRule.value = FcDesigner.formCreate.parseJson(res.data.fieldsData)
      fcOption.value = JSON.parse(FcDesigner.formCreate.parseJson(res.data.compData))
      formData.value = res.data
    }
  })
}

async function modalHandleOk() {
  try {
    if (!formRef.value) {
      console.error('获取form表单失败')
      return
    }
    await formRef.value.validate()
    confirmLoading.value = true
    let handle
    if (modalType.value === 'edit') {
      // handle = updateIndexTemplate
    } else {
      handle = addIndexTemplate
    }
    // await handle(modalForm.value)
    tableRef.value?.getTableData()
    message.success('操作成功')
    modalValue.value = false
    confirmLoading.value = false
  } catch (error) {
    confirmLoading.value = false
    console.log(error)
  }
}
</script>
<style lang="less" scoped>
.indicators-template-container {
  background: white;

  height: 100%;
}
</style>
