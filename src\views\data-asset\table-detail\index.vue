<template>
  <div class="table-detail">
    <cardBox :title="tableName" :subTitle="tableComment ? tableComment : '暂无注释'">
      <a-tabs v-model:activeKey="activeKey" class="table-detail-tabs">
        <template #leftExtra>
          <span type="link" @click="goBack" class="go-back-btn">
            <ArrowLeftOutlined />
          </span>
        </template>
        <a-tab-pane key="1">
          <template #tab>
            <span>
              <UnorderedListOutlined />
              列
            </span>
          </template>
          <div class="mg-tp-12">
            <TableColumns></TableColumns>
          </div>
        </a-tab-pane>
        <a-tab-pane key="2" force-render>
          <template #tab>
            <span>
              <TableOutlined />
              数据预览
            </span>
          </template>
          <DataPreview class="mg-tp-12"></DataPreview>
        </a-tab-pane>
        <!-- <a-tab-pane key="3">
        <template #tab>
          <span>
            <BranchesOutlined />
            ER图
          </span>
        </template>
      </a-tab-pane> -->
        <a-tab-pane key="3">
          <template #tab>
            <span>
              <ApartmentOutlined />
              血缘关系
            </span>
          </template>
          <DataLineageDiagram class="mg-tp-12" />
        </a-tab-pane>
      </a-tabs>
    </cardBox>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import {
  UnorderedListOutlined,
  TableOutlined,
  ApartmentOutlined,
  ArrowLeftOutlined,
} from '@ant-design/icons-vue'
import DataPreview from './components/data-preview.vue'
import TableColumns from './components/table-columns.vue'
import DataLineageDiagram from './components/data-lineage-diagram.vue'
import { useRouter } from 'vue-router'
import cardBox from '@/components/card-box/card-box.vue'

const activeKey = ref('1')
const router = useRouter()
const route = useRoute()
const tableName = computed(() => (route.query.tableName as string) || '')
const tableComment = computed(() => (route.query.tableComment as string) || '')

const goBack = () => {
  router.back()
}
</script>

<style lang="less" scoped>
.table-detail {
  flex: auto;
  background: white;
  height: 100%;

  .header-div {
    display: flex;
  }
  .table-detail-row {
    height: 100%;
  }
  .table-detail-col {
    height: 100%;
    border-right: 1px solid #d7d7d7;
    padding: 12px 12px 0 12px;
    background: white;
  }
  .content {
    padding: 0px;
  }
  .table-detail-col:last-of-type {
    border-width: 0px;
  }
}

.table-detail-tabs {
  width: 100%;
  height: 100%;
  display: flex;

  :deep(.ant-tabs-content) {
    height: 100%;
  }
  :deep(.ant-tabs-tabpane) {
    flex: auto;
    overflow: hidden;
  }
}
.go-back-btn {
  color: var(--primary-color);
  // margin-left: 5px;
  margin-right: 10px;
  cursor: pointer;
}
.mg-tp-12 {
  margin-top: 12px;
}
</style>
