import { expect, test } from 'vitest'
import {
  getDbTypeList,
  getDbNameByType,
  getTableInfoList,
  previewData,
  updateDataAssets,
  dataAssetsMarking,
  addDataAssert,
  updateDataAssetTable,
  getDataAssetList,
  getDataAssetTableList,
  getDataAssetTableDetail,
  getTableLineage,
  databaseCount,
  tableCount,
  columnCount,
  deletedataPermission,
  saveDataPermission,
  dataPermissionPageList,
  updateDataPermission,
  getTableColumnsList,
  getDataPermissionDetail,
  getUserList,
} from './dataassertmanage'

test('getDbTypeList', async () => {
  const { data } = await getDbTypeList({})

  expect(data).toHaveProperty('code')
  expect(data).toHaveProperty('msg')
  expect(data.data[0]).toHaveProperty('dbType')
})

test('getDbNameByType', async () => {
  const { data } = await getDbNameByType({ dbType: '(sM' })

  expect(data).toHaveProperty('code')
  expect(data).toHaveProperty('msg')
  expect(data.data[0]).toHaveProperty('databaseName'),
    expect(data.data[0]).toHaveProperty('ip'),
    expect(data.data[0]).toHaveProperty('port'),
    expect(data.data[0]).toHaveProperty('engine'),
    expect(data.data[0]).toHaveProperty('datasourceId')
})

test('getTableInfoList', async () => {
  const { data } = await getTableInfoList({
    dbType: 'C#xa',
    databaseName: 'GQ@R',
    tableName: '%2M1Cf',
  })

  expect(data).toHaveProperty('code')
  expect(data).toHaveProperty('msg')
  expect(data.data[0]).toHaveProperty('databaseName'),
    expect(data.data[0]).toHaveProperty('tableComment'),
    expect(data.data[0]).toHaveProperty('tableCollation'),
    expect(data.data[0]).toHaveProperty('tableName'),
    expect(data.data[0]).toHaveProperty('columnList')
})

test('previewData', async () => {
  const { data } = await previewData({
    tableId: 'P*fLx4h',
    pageIndex: 7455773368932383,
    pageSize: 2960848036429119,
  })

  expect(data).toHaveProperty('code')
  expect(data).toHaveProperty('msg')
  expect(data.data[0]).toHaveProperty('columnList'), expect(data.data[0]).toHaveProperty('dataList')
})

test('updateDataAssets', async () => {
  const { data } = await updateDataAssets({
    tableId: 'IbcQ',
    status: 2955799505623261,
  })

  expect(data).toBeTruthy()
})

test('dataAssetsMarking', async () => {
  const { data } = await dataAssetsMarking({ tableId: 'CuV' })

  expect(data).toBeTruthy()
})

test('addDataAssert', async () => {
  const { data } = await addDataAssert({
    databaseName: 'BO&C',
    databaseType: '6(BlU',
    datasourceId: 1967452302330441,
    directoryId: 7296106132725203,
    ip: '5@Zg!',
    port: 6645855708293622,
    engine: 'B[%IP',
  })

  expect(data).toHaveProperty('code')
  expect(data).toHaveProperty('msg')
  expect(data).toHaveProperty('data')
})

test('updateDataAssetTable', async () => {
  const { data } = await updateDataAssetTable({
    tableId: 'ZaGmqXn',
    tableName: 'SWR&pl',
    tableType: '93O4]U',
    tableComment: '#9mmK%W',
    tableCollation: 'dLLK)J*',
  })

  expect(data).toHaveProperty('code')
  expect(data).toHaveProperty('msg')
  expect(data).toHaveProperty('data')
})

test('getDataAssetList', async () => {
  const { data } = await getDataAssetList({ directoryId: 2157596903999421 })

  expect(data).toHaveProperty('code')
  expect(data).toHaveProperty('msg')
  expect(data.data[0]).toHaveProperty('databaseId'),
    expect(data.data[0]).toHaveProperty('databaseName'),
    expect(data.data[0]).toHaveProperty('databaseType'),
    expect(data.data[0]).toHaveProperty('engine'),
    expect(data.data[0]).toHaveProperty('description'),
    expect(data.data[0]).toHaveProperty('tableCount')
})

test('getDataAssetTableList', async () => {
  const { data } = await getDataAssetTableList({
    databaseId: '!nI2ZX',
    pageIndex: 2716605825495925,
    pageSize: 2358491792417649,
  })

  expect(data).toHaveProperty('code')
  expect(data).toHaveProperty('msg')
  expect(data.data).toHaveProperty('pageIndex'),
    expect(data.data).toHaveProperty('pageSize'),
    expect(data.data).toHaveProperty('totalPages'),
    expect(data.data).toHaveProperty('totalRecords'),
    expect(data.data).toHaveProperty('records')
})

test('getDataAssetTableDetail', async () => {
  const { data } = await getDataAssetTableDetail({ tableId: '0nsW2' })

  expect(data).toHaveProperty('code')
  expect(data).toHaveProperty('msg')
  expect(data.data).toHaveProperty('tableId'),
    expect(data.data).toHaveProperty('tableName'),
    expect(data.data).toHaveProperty('status'),
    expect(data.data).toHaveProperty('tableType'),
    expect(data.data).toHaveProperty('labelId'),
    expect(data.data).toHaveProperty('tableComment'),
    expect(data.data).toHaveProperty('tableCollation'),
    expect(data.data).toHaveProperty('description'),
    expect(data.data).toHaveProperty('columnList')
})

test('getTableLineage', async () => {
  const { data } = await getTableLineage({ tableId: 'Scf@N)(' })

  expect(data).toHaveProperty('code')
  expect(data).toHaveProperty('msg')
  expect(data).toHaveProperty('data')
})

test('databaseCount', async () => {
  const { data } = await databaseCount({})

  expect(data).toBeTruthy()
})

test('tableCount', async () => {
  const { data } = await tableCount({})

  expect(data).toBeTruthy()
})

test('columnCount', async () => {
  const { data } = await columnCount({})

  expect(data).toBeTruthy()
})

test('deletedataPermission', async () => {
  const { data } = await deletedataPermission({ permissionId: 'ayzx' })

  expect(data).toBeTruthy()
})

test('saveDataPermission', async () => {
  const { data } = await saveDataPermission({
    tableId: 'kcIB[',
    configValue: '0[2#',
    columns: 'Up2',
  })

  expect(data).toHaveProperty('code')
  expect(data).toHaveProperty('msg')
  expect(data).toHaveProperty('data')
})

test('dataPermissionPageList', async () => {
  const { data } = await dataPermissionPageList({
    tableId: '@lghJH',
    userName: 'l8eM',
    pageIndex: 2147212759793477,
    pageSize: 2999040299305983,
  })

  expect(data).toHaveProperty('code')
  expect(data).toHaveProperty('msg')
  expect(data.data[0]).toHaveProperty('pageIndex'),
    expect(data.data[0]).toHaveProperty('pageSize'),
    expect(data.data[0]).toHaveProperty('totalPages'),
    expect(data.data[0]).toHaveProperty('totalRecords'),
    expect(data.data[0]).toHaveProperty('records')
})

test('updateDataPermission', async () => {
  const { data } = await updateDataPermission({
    permissionId: 'sZy',
    configValue: 'JqrgZi',
    columns: 'Q%S4tL',
  })

  expect(data).toHaveProperty('code')
  expect(data).toHaveProperty('msg')
  expect(data).toHaveProperty('data')
})

test('getTableColumnsList', async () => {
  const { data } = await getTableColumnsList({ tableId: '2g)W' })

  expect(data).toHaveProperty('code')
  expect(data).toHaveProperty('msg')
  expect(data.data[0]).toHaveProperty('databaseName'),
    expect(data.data[0]).toHaveProperty('tableComment'),
    expect(data.data[0]).toHaveProperty('tableCollation'),
    expect(data.data[0]).toHaveProperty('tableName'),
    expect(data.data[0]).toHaveProperty('columnList')
})

test('getDataPermissionDetail', async () => {
  const { data } = await getDataPermissionDetail({ permissionId: 'zKLgF' })

  expect(data).toHaveProperty('code')
  expect(data).toHaveProperty('msg')
  expect(data.data).toHaveProperty('permissionId'),
    expect(data.data).toHaveProperty('userId'),
    expect(data.data).toHaveProperty('userName'),
    expect(data.data).toHaveProperty('tableId'),
    expect(data.data).toHaveProperty('tableName'),
    expect(data.data).toHaveProperty('configValue'),
    expect(data.data).toHaveProperty('columnList')
})

test('getUserList', async () => {
  const { data } = await getUserList({
    userName: 'NGyXD',
    pageIndex: 8281165251512037,
    pageSize: 120739637895271,
    tableId: '5bFvi',
  })

  expect(data).toBeTruthy()
})
