<template>
  <div>
    <!-- <div class="emotion-box"> -->
    <div class="ds-btn">
      <div class="ds-btn-left" @click="openSelectSource">{{ showTableName }}</div>
      <div class="ds-btn-right">
        <a-popover v-model:open="visiblePopover" title="" placement="right" trigger="click">
          <template #content>
            <div class="field-list-warp">
              <a-checkbox
                v-model:checked="checkState.checkAll"
                :indeterminate="checkState.indeterminate"
                @change="onCheckAllChange"
              >
                {{ checkState.checkAll ? '取消所有' : '全选' }}
              </a-checkbox>
              <a-divider style="margin: 15px 0px 5px" />
              <div class="field-list">
                <a-checkbox-group
                  v-model:value="checkState.checkedList"
                  @change="onCheckedListChange"
                >
                  <div class="field-item" v-for="(item, key) in dataBaseTableField" :key="key">
                    <a-checkbox :value="item.title">
                      <span class="field-type-icon">{{ item.columnType }}</span>
                      <span class="filed_en">{{ item.title }}</span>
                      <a-tooltip placement="right">
                        <template #title>{{ item.comment }}</template>
                        <span class="filed_zh">{{ item.comment || '-' }}</span>
                      </a-tooltip>
                    </a-checkbox>
                  </div>
                </a-checkbox-group>
              </div>
            </div>
          </template>
          <DownOutlined @click="handleClickDownIcon" style="padding: 10px" />
        </a-popover>
      </div>
    </div>
    <!-- </div> -->
    <a-modal
      title="选择数据"
      v-model:open="showSelectSource"
      style="width: 80vw; max-width: 800px; max-height: 480px"
      :footer="null"
      @cancel="cancelSelectSource"
      @ok="okSelectSource"
    >
      <a-tabs v-model:activeKey="sourceType" v-if="showTabs" @change="getDataBase()">
        <a-tab-pane v-for="item in sourceTypeList" :key="item.value" :tab="item.label">
          <a-row :gutter="10" class="source-list-wrap">
            <a-col :span="12">
              <div class="source-list">
                <div v-if="queryDataBaseTableListStatus" class="loading-top">
                  <a-spin /> 加载...
                </div>
                <div
                  class="source-item"
                  v-else
                  v-for="(it, key) in dataBase"
                  @click="handleClickDataBase(it)"
                  :class="activeDataBase.databaseId === it.databaseId ? 'source-item-active' : ''"
                  :key="key"
                >
                  <DatabaseOutlined />{{ it.title }}
                </div>
              </div>
            </a-col>
            <a-col :span="12">
              <div class="source-list">
                <div v-if="queryTableFieldsListStatus" class="loading-top"><a-spin /> 加载...</div>
                <div
                  class="source-item table-item"
                  v-else
                  v-for="(it, key) in dataBaseTable"
                  @click="handleClickTable(it)"
                  :class="activeTable === it.title ? 'source-item-active' : ''"
                  :key="key"
                >
                  <a-tooltip placement="right">
                    <template #title>{{ it.comment || it.title }}</template>
                    <TableOutlined />
                    {{ it.comment }} {{ it.title }}
                  </a-tooltip>
                </div>
              </div>
            </a-col>
          </a-row>
        </a-tab-pane>
      </a-tabs>
      <a-row :gutter="10" v-else>
        <!-- 当不显示tabs时，直接展示数据源列表 -->
        <a-col :span="12">
          <div class="source-list">
            <div v-if="queryDataBaseTableListStatus" class="loading-top"><a-spin /> 加载...</div>
            <div class="source-item source-item-active" v-else>
              <DatabaseOutlined /> {{ activeDataBase.databaseName || activeDataBase.title }}
            </div>
          </div>
        </a-col>
        <a-col :span="12">
          <div class="source-list">
            <div v-if="queryTableFieldsListStatus" class="loading-top"><a-spin /> 加载...</div>
            <div
              class="source-item table-item"
              v-else
              v-for="(it, key) in dataBaseTable"
              @click="handleClickTable(it)"
              :class="activeTable === it.title ? 'source-item-active' : ''"
              :key="key"
            >
              <a-tooltip placement="right">
                <template #title>{{ it.comment || it.title }}</template>
                <TableOutlined />
                {{ it.comment }} {{ it.title }}
              </a-tooltip>
            </div>
          </div>
        </a-col>
      </a-row>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, watch, computed, inject } from 'vue'
import { DownOutlined, DatabaseOutlined, TableOutlined } from '@ant-design/icons-vue'
import {
  getDBTypeList,
  getDataAssertDBList,
  getDataAssetTableList,
  getDataAssetColumnList,
} from '@/api/indexmanage/datasourceoptions/datasourceoptions'
import type { ServerFieldItem } from '@/api/services/indicator/type'

/**
 * 字段说明：
 * 1，showTbas：是否显示tabs组件[数据源类型]
 * 2，dataSourceId：数据源id，非必传
 * 3，databaseName：数据库名称，非必传
 * 4，tableName：表名称，非必传
 * 5，callbackFn：点击弹窗确认按钮的回调事件
 * 6，isUnSelectData：是否未选择数据，如果为true，则显示选择数据源按钮，false则默认选择接口返回第一条
 */
const props = defineProps({
  showTabs: {
    type: Boolean,
    default: true,
    required: false,
  },
  dataSourceId: {
    type: [Number, String],
    required: false,
  },
  databaseName: {
    type: String,
    required: false,
  },
  tableName: {
    type: String,
    required: false,
  },
  tableId: {
    type: [String],
    required: false,
  },
  callbackFn: {
    type: Function,
    required: true,
  },
  selectData: {
    type: Array,
    default: () => [],
    required: false,
  },
  joinIndex: {
    type: Number,
    required: false,
  },
  // 新增：预设的表数据
  presetTableData: {
    type: Array,
    default: () => [],
    required: false,
  },
  // 新增：预设的激活数据库
  presetActiveDatabase: {
    type: Object,
    default: () => null,
    required: false,
  },
})

const checkState = ref<Record<string, any>>({
  checkAll: false,
  indeterminate: true,
  checkedList: [],
})
const sourceType = ref('1')
const activeSource = ref('')
const activeDataBase = ref() // 用于存储当前选中的数据库
const activeTable = ref('')
const showSelectSource = ref(false)
const sourceTypeList = ref<any[]>([])

const activeTableId = ref('') // 用于存储当前选中的表ID

const getDBTypeListReq = async () => {
  try {
    const { data = [] } = await getDBTypeList()
    sourceTypeList.value = data.map((item: any) => ({
      label: item.dbName,
      value: String(item.dbType),
    }))
  } catch (error) {
    // do nohting
  }
}

const dataBase: any = ref([])
const dataBaseTable: any = ref([])
const dataBaseTableField = ref<ServerFieldItem[]>([])
const showTableName = ref('选择初始数据')
const visiblePopover = ref(false)
const isFirst = ref(true)

// 从父组件注入表数据和数据库信息
const availableTablesInCurrentDatabase = inject<any>('availableTablesInCurrentDatabase', ref([]))
const currentActiveDatabase = inject<any>('currentActiveDatabase', ref(null))

const params: any = computed(() => ({
  id: activeTableId.value,
  tableId: activeTableId.value,
  tableName: activeTable.value,
  dbSchema: activeDataBase.value,
  dbName: activeDataBase.value?.title || '',
  dbColumns: dataBaseTableField.value,
  checkedColumns: checkState.value.checkedList,
  activeDataBase: activeDataBase.value, // 当前选中的库
}))

// 获取表列表状态
const queryDataBaseTableListStatus = ref(false)

// 获取表字段列表状态
const queryTableFieldsListStatus = ref(false)

watch(
  () => checkState.value.checkedList,
  (val: any) => {
    let plainOptions = dataBaseTableField.value.map((item: any) => item.title)
    checkState.value.indeterminate = !!val.length && val.length < plainOptions.length
    checkState.value.checkAll = val.length === plainOptions.length
  },
)

// 监听注入的表数据变化
watch(
  () => availableTablesInCurrentDatabase.value,
  (newVal) => {
    if (newVal && newVal.length > 0) {
      console.log('data-source: 接收到表数据更新:', newVal)
      dataBaseTable.value = newVal
    }
  },
  { immediate: true },
)

// 监听注入的数据库信息变化
watch(
  () => currentActiveDatabase.value,
  (newVal) => {
    if (newVal) {
      console.log('data-source: 接收到数据库信息更新:', newVal)
      activeDataBase.value = newVal
      // 当数据库信息更新时，重新获取表数据
      getDataBaseTable()
    }
  },
  { immediate: true },
)

const handleClickDownIcon = () => {}

const onCheckAllChange = (e: any) => {
  let plainOptions = dataBaseTableField.value.map((item: any) => item.title)
  let checkedList = e.target.checked ? plainOptions : [plainOptions[0]]
  if (checkedList.length === 0) {
    checkedList = [plainOptions[0]]
  }
  Object.assign(checkState.value, {
    checkedList,
    indeterminate: false,
  })

  checkState.value = {
    checkAll: e.target.checked,
    indeterminate: false,
    checkedList,
  }

  props.callbackFn(params.value, props.joinIndex)
}

function onCheckedListChange() {
  props.callbackFn(params.value, props.joinIndex)
}

//  请求库
const getDataBase = async () => {
  try {
    queryDataBaseTableListStatus.value = true

    dataBase.value = []
    dataBaseTable.value = []

    // 优先使用注入的当前激活数据库
    if (currentActiveDatabase.value) {
      console.log('使用注入的激活数据库:', currentActiveDatabase.value)
      activeDataBase.value = currentActiveDatabase.value
      // 如果有注入的数据库，直接获取表数据，不需要请求数据库列表
      getDataBaseTable()
      return
    }

    const { data } = await getDataAssertDBList({ dbType: sourceType.value })

    dataBase.value = data

    // 优先使用预设的激活数据库，否则使用第一个数据库
    if (props.presetActiveDatabase) {
      activeDataBase.value = props.presetActiveDatabase
      console.log('使用预设的激活数据库:', props.presetActiveDatabase)
    } else {
      activeDataBase.value = dataBase.value[0]
    }

    console.log('请求库', dataBase.value, activeDataBase.value)

    getDataBaseTable()
  } catch (error) {
    console.error(error)
  } finally {
    queryDataBaseTableListStatus.value = false
  }
}

//  请求表
const getDataBaseTable = async () => {
  if (!activeDataBase.value) return

  // 优先使用注入的表数据
  if (availableTablesInCurrentDatabase.value && availableTablesInCurrentDatabase.value.length > 0) {
    console.log('使用注入的表数据:', availableTablesInCurrentDatabase.value)
    dataBaseTable.value = availableTablesInCurrentDatabase.value
    return
  }

  // 如果有预设的表数据，直接使用
  if (props.presetTableData && props.presetTableData.length > 0) {
    console.log('使用预设的表数据:', props.presetTableData)
    dataBaseTable.value = props.presetTableData
    return
  }

  try {
    queryTableFieldsListStatus.value = true
    dataBaseTable.value = []
    const { data } = await getDataAssetTableList({
      databaseId: activeDataBase.value.databaseId,
    })
    dataBaseTable.value = data
  } catch (error) {
    console.error(error)
  } finally {
    queryTableFieldsListStatus.value = false
  }
}

//  请求字段
const getField = async () => {
  if (!activeTableId.value) {
    return
  }
  const { data } = await getDataAssetColumnList({
    tableId: activeTableId.value,
  })
  // @ts-ignore
  dataBaseTableField.value = data

  // 首次渲染，为了区分是否切换过表
  // props.selectData 区分是存量且有选择字段的情况
  // props.selectData.length < data.length 标识未全选
  if (isFirst.value && props.selectData.length > 0 && props.selectData.length < data.length) {
    return
  }

  // 默认全选
  checkState.value.checkAll = true
  checkState.value.checkedList = data.map((item: any) => item.title)
}

const handleClickDataBase = async (it: any) => {
  activeDataBase.value = it
  console.log('点击数据库', it)
  getDataBaseTable()
}

const handleClickTable = async (it: any) => {
  activeTableId.value = it.tableId
  activeTable.value = it.title
  showSelectSource.value = false

  // 重置选中的字段
  checkState.value.checkAll = false
  checkState.value.checkedList = []
  okSelectSource()
}

const openSelectSource = () => {
  showSelectSource.value = true
  isFirst.value = false
  if (isFirst.value) {
    getDataBase()
  }
}

const cancelSelectSource = () => {
  showSelectSource.value = false
}

const okSelectSource = async () => {
  showSelectSource.value = false
  showTableName.value = activeTable.value
  await getField()
  props.callbackFn(params.value, props.joinIndex)
}

defineExpose({
  openSelectSource,
})

onMounted(async () => {
  if (props.dataSourceId) {
    if (props.tableName) {
      showTableName.value = props.tableName
      activeTable.value = props.tableName
    }
    activeSource.value = props.dataSourceId + ''

    // 优先使用注入的激活数据库
    if (currentActiveDatabase.value) {
      activeDataBase.value = currentActiveDatabase.value
      console.log('使用注入的激活数据库:', currentActiveDatabase.value)
    } else if (props.presetActiveDatabase) {
      activeDataBase.value = props.presetActiveDatabase
      console.log('使用预设的激活数据库:', props.presetActiveDatabase)
    } else {
      activeDataBase.value = { databaseName: props.databaseName! }
    }

    console.log('activeDataBase.value', activeDataBase.value)
    activeTableId.value = props.tableId || ''
    // 修复 TypeScript 错误：为 selectData 添加类型断言
    checkState.value.checkedList = props.selectData.map((item: any) => item?.fieldName)
    getField()
  } else {
    // showSelectSource.value = true
  }
  await getDBTypeListReq()
  getDataBase()
})
</script>

<style lang="less" scoped>
.source-list-wrap {
  -webkit-user-select: none; /* Chrome, Safari */
  -moz-user-select: none; /* Firefox */
  -ms-user-select: none; /* IE/Edge */
  user-select: none; /* 标准写法 */
}
@default-text-color: #509ee3;
.title {
  color: @default-text-color;
  font-weight: bold;
  display: flex;
  margin-bottom: 0.5rem;
}
.emotion-box {
  display: flex;
  -webkit-box-align: center;
  align-items: center;
  flex-wrap: wrap;
  border-radius: 8px;
  background-color: rgba(80, 158, 227, 0.1);
  padding: 14px;
  color: rgb(80, 158, 227);
  .ds-btn {
    display: flex;
    // font-weight: bold;
    color: rgb(255, 255, 255);
    border-radius: 8px;
    border: 2px solid transparent;
    cursor: pointer;
    pointer-events: auto;
    -webkit-box-align: stretch;
    align-items: stretch;
    transition: border 300mslinear;

    .ds-btn-left {
      font-weight: bold;
      display: flex;
      -webkit-box-align: center;
      align-items: center;
      padding: 8px 10px;
      background-color: #509ee3;
      border-top-left-radius: 6px;
      border-bottom-left-radius: 6px;
      transition: background 300mslinear;
      font-size: 14px;
      &:hover {
        background-color: #6fafe7;
      }
    }
    .ds-btn-right {
      display: flex;
      -webkit-box-align: center;
      align-items: center;
      justify-content: center;
      background-color: rgb(80, 158, 227);
      border-left: 1px solid rgba(255, 255, 255, 0.25);
      border-top-right-radius: 6px;
      border-bottom-right-radius: 6px;
      transition: background 300ms linear;
      width: 37px;
      padding: 0px;
      &:hover {
        background-color: #6fafe7;
      }
    }
  }
}

.source-list {
  background-color: #fafbfc;
  margin: 10px;
  height: 380px;
  overflow-y: scroll;
  overflow-x: hidden;
  padding: 15px 25px 15px 15px;
  width: 100%;
  box-sizing: border-box;
  max-width: 100%;
  .source-item {
    border-radius: 10px;
    display: flex;
    // justify-content: space-between;
    padding: 8px;
    width: 100%;
    max-width: 100%;
    cursor: pointer;
    margin-bottom: 5px;
    .left-box {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      flex: 1;
      text-decoration: none;
      font-size: 0.875rem;
      color: #4c5773;
    }
    .right-box {
      width: 20px;
    }
  }
  .source-item-active.table-item {
    background-color: #509ee3;
    color: #fff;
  }
  .source-item-active.table-item:hover {
    background-color: #509ee3;
    color: #fff !important;
  }
  .table-item:hover {
    color: #4c5773 !important;
  }
  .source-item:hover {
    background-color: #eef5fc;
  }
  .source-item-active {
    background-color: #edf2f5;
  }
}

.field-list-warp {
  width: 360px;
}

.field-type-icon {
  display: inline-block;
  width: 46px;
  height: 12px;
  text-align: center;
  line-height: 12px;
  font-size: 10px;
  border: 1px solid #509ee3;
  border-radius: 4px;
  color: #509ee3;
  background-color: rgba(80, 158, 227, 0.1);
  padding: 1px;
  font-weight: bold;
  vertical-align: middle;
}
.filed_en {
  display: inline-block;
  width: 120px;
  margin: 0 6px;
  white-space: nowrap; /* 不换行 */
  overflow: hidden; /* 超出部分隐藏 */
  text-overflow: ellipsis; /* 显示省略号 */
  vertical-align: middle;
  font-weight: bold;
  color: #333;
}
.filed_zh {
  display: inline-block;
  width: 120px;
  margin: 0 6px;
  white-space: nowrap; /* 不换行 */
  overflow: hidden; /* 超出部分隐藏 */
  text-overflow: ellipsis; /* 显示省略号 */
  vertical-align: middle;
  color: #666;
}
.field-list {
  display: flex;
  flex-direction: column;
  max-height: 600px;
  overflow-y: scroll;
  overflow-x: hidden;
  padding: 0 4px;
  .field-item {
    display: flex;
    padding: 5px 6px;
    border-radius: 6px;
    max-width: 100%;
    overflow: hidden;
  }
  .field-item:hover {
    background-color: #edf2f5;
    border-radius: 4px;
  }
}
.source-list-wrap {
  height: 400px;
}
.source-modal {
  width: 80vw;
  max-width: 800px;
  max-height: 480px;
}

.loading-top {
  text-align: center;
  padding: 6px 0;
}
</style>
