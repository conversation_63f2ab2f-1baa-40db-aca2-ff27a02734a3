<template>
  <a-modal
    :open="open"
    @ok="modalHandleOk"
    :title="modalType === 'edit' ? '编辑' : '新增'"
    @cancel="cancel"
    width="800px"
    :confirm-loading="confirmLoading"
  >
    <a-form
      ref="formRef"
      :model="form"
      :rules="rules"
      :label-col="labelCol"
      :wrapper-col="wrapperCol"
    >
      <a-form-item v-if="modalType != 'add'" label="应用编号" name="applicationNo">
        <a-input :disabled="disabled" v-model:value="form.appId" placeholder="请输入应用编号" />
      </a-form-item>
      <a-form-item label="应用标识" name="appCode">
        <a-input v-model:value="form.appCode" :disabled="disabled" placeholder="请输入应用标识" />
      </a-form-item>
      <a-form-item label="应用名称" name="appName">
        <a-input v-model:value="form.appName" placeholder="请输入应用名称" />
      </a-form-item>
      <a-form-item label="应用回调地址" name="callbackUrl">
        <a-input v-model:value="form.callbackUrl" placeholder="请输入应用回调地址" />
      </a-form-item>
      <a-form-item label="是否启用" name="isUse">
        <a-radio-group v-model:value="form.isUse" placeholder="请选择是否启用">
          <a-radio :value="1">是</a-radio>
          <a-radio :value="0">否</a-radio>
        </a-radio-group>
      </a-form-item>
      <a-form-item label="是否需要验证码" name="isVerify">
        <a-radio-group v-model:value="form.isVerify" placeholder="请选择是否需要验证码">
          <a-radio :value="1">是</a-radio>
          <a-radio :value="0">否</a-radio>
        </a-radio-group>
      </a-form-item>
    </a-form>
  </a-modal>
</template>
<script lang="ts" setup>
import { defineEmits, defineProps, defineExpose, ref } from 'vue'
import type { Rule } from 'ant-design-vue/es/form'
import { addSsoApp, updateSsoApp } from '@/api/services/permission'
interface FormState {
  appId: string
  appCode: string
  appName: string
  callbackUrl: string
  isUse: string
  isVerify: string
}
defineProps({
  open: {
    type: Boolean,
    default: false,
  },
  disabled: Boolean,
})
const emits = defineEmits(['modalHandleOk', 'modalCancel', 'confirmLoadingHandle'])
const formRef = ref<HTMLFormElement | null>(null)
const labelCol = { span: 5 }
const wrapperCol = { span: 13 }
const modalType = ref<string>('')
const confirmLoading = ref<boolean>(false)
const form = ref<FormState>({
  appId: '',
  appCode: '',
  appName: '',
  callbackUrl: '',
  isUse: '',
  isVerify: '',
})

const rules: Record<string, Rule[]> = {
  appCode: [{ required: true, message: '请输入应用编号', trigger: 'blur' }],
  appName: [{ required: true, message: '请输入应用名称', trigger: 'blur' }],
  callbackUrl: [{ required: true, message: '请输入应用回调地址', trigger: 'blur' }],
  isUse: [{ required: true, message: '请选择是否启用', trigger: 'blur' }],
  isVerify: [{ required: true, message: '请选择是否需要验证码', trigger: 'blur' }],
}

function modalHandleOk() {
  formRef.value &&
    formRef.value
      .validate()
      .then(() => {
        const { appId, ...extra } = form.value
        const data = {
          ...extra,
        }
        const { appName, callbackUrl, isUse, isVerify } = form.value
        confirmLoading.value = true
        if (modalType.value === 'edit') {
          const update = {
            appId,
            appName,
            callbackUrl,
            isUse,
            isVerify,
          }
          updateSsoApp(update)
            .then(() => {
              emits('modalHandleOk', form.value)
            })
            .finally(() => {
              confirmLoading.value = false
            })
        } else {
          addSsoApp(data)
            .then(() => {
              emits('modalHandleOk', form.value)
            })
            .finally(() => {
              confirmLoading.value = false
            })
        }
      })
      .catch((error: any) => {
        console.log(error)
      })
}

function cancel() {
  emits('modalCancel')
}
function show(type: string, data?: Record<string, any>) {
  formRef.value?.resetFields()
  modalType.value = type
  if (type === 'edit') {
    editForm(data)
  } else {
    form.value = {
      appId: '',
      appCode: '',
      appName: '',
      callbackUrl: '',
      isUse: '',
      isVerify: '',
    }
  }
}

function editForm(data?: any) {
  if (data) {
    const copyData = JSON.parse(JSON.stringify(data))
    // copyData["isUse"] = copyData?.isUse === '是' ? 1 : 0
    form.value = copyData
  }
}
defineExpose({ show })
</script>
