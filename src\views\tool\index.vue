<script lang="ts" setup>
import { ref } from 'vue'
import { MinusCircleOutlined, PlusOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'

interface IQuery {
  id: string
  key: string
  value: string
}

interface LeftForm {
  host: string
  id: string
  query: IQuery[]
}

const leftFormState = ref<LeftForm>({
  host: '',
  id: '',
  query: [
    {
      id: Date.now().toString(),
      key: '',
      value: '',
    },
  ],
})

const leftFormRef = ref()
const completeText = ref('')

const removeQuery = (data: IQuery) => {
  // 如果只剩下一条数据，提示不能删除
  if (leftFormState.value.query.length <= 1) {
    message.warning('至少填写一条查询参数')
    return
  }
  const index = leftFormState.value.query.findIndex((item) => item.id === data.id)
  if (index !== -1) {
    leftFormState.value.query.splice(index, 1)
  }
}

const addQuery = () => {
  // 如果已经有3条数据，提示不能再添加
  if (leftFormState.value.query.length >= 3) {
    message.warning('最多只能添加3条查询参数')
    return
  }
  const newQuery: IQuery = {
    id: Date.now().toString(), // 使用时间戳作为唯一ID
    key: '',
    value: '',
  }
  leftFormState.value.query.push(newQuery)
}

const onFinish = (values: any) => {
  console.log('Received values of form:', values)
  const { host, id, query } = values
  const params = query.reduce((res: any, item: IQuery) => {
    res[item.key] = `[${item.value}]`
    return res
  }, {})
  const address = `${host}/#/middleware?id=${id}&queryString=` + JSON.stringify(params)
  console.log('🚀 ~ onFinish ~ address', address)
  completeText.value = address
}

const handleClear = () => {
  leftFormState.value = {
    host: '',
    id: '',
    query: [
      {
        id: Date.now().toString(),
        key: '',
        value: '',
      },
    ],
  }
  completeText.value = ''
  leftFormRef.value?.resetFields()
}

const handleCopy = () => {
  if (!completeText.value) {
    message.warning('请先生成地址')
    return
  }
  var textarea = document.createElement('textarea')
  textarea.value = completeText.value
  document.body.appendChild(textarea)
  textarea.select()
  document.execCommand('copy')
  document.body.removeChild(textarea)
  message.success('复制成功')
}

// #region
const rightFormState = ref<{ id: string }>({
  id: '',
})
const rightFormRef = ref()
const rightAddress = ref({
  text1: '',
  text2: '',
  text3: '',
})

const onRightFinish = (values: any) => {
  console.log('Received values of form:', values)
  const { id } = values
  const text1 = `iframePage?url=/#/de-link/${id}`
  const text2 = `iframePage?url=/#/middleware-landing?id=${id}`
  const text3 = `iframePage?url=/#/middleware-landing?id=${id}&attachParamsFlag=1`
  rightAddress.value = {
    text1,
    text2,
    text3,
  }
  // console.log('🚀 ~ onFinish ~ address', address)
  // completeText.value = address
}

const handleRightCopy = (value: 'text1' | 'text2' | 'text3') => {
  if (!rightAddress.value.text1) {
    message.warning('请先生成地址')
    return
  }
  var textarea = document.createElement('textarea')
  textarea.value = rightAddress.value[value]
  document.body.appendChild(textarea)
  textarea.select()
  document.execCommand('copy')
  document.body.removeChild(textarea)
  message.success('复制成功')
}

const handleRightClear = () => {
  rightFormState.value = { id: '' }
  rightAddress.value = { text1: '', text2: '', text3: '' }
  rightFormRef.value?.resetFields()
}

// #endregion
</script>

<template>
  <div class="tool-page">
    <div class="part-left">
      <h3>地址填写</h3>
      <a-form :label-col="{ span: 2 }" ref="leftFormRef" :model="leftFormState" @finish="onFinish">
        <a-form-item
          name="host"
          label="地址"
          :rules="[
            { required: true, message: 'Missing host' },
            {
              pattern: /^https?:\/\/[^\s]+$/i,
              message: '请输入合法的 http(s) 地址',
            },
          ]"
        >
          <a-input
            v-model:value="leftFormState.host"
            placeholder="示例：http://*************:8080"
            allowClear
          />
        </a-form-item>
        <a-form-item name="id" label="ID" :rules="[{ required: true, message: 'Missing ID' }]">
          <a-input v-model:value="leftFormState.id" placeholder="示例：6W48N58z" allowClear />
        </a-form-item>
        <a-space
          v-for="(data, index) in leftFormState.query"
          :key="data.id"
          class="query-space"
          align="baseline"
        >
          <a-form-item
            :name="['query', index, 'key']"
            style="flex: 1"
            :rules="{
              required: true,
              message: 'Missing key',
            }"
          >
            <a-input v-model:value="data.key" placeholder="示例：hospital" allowClear />
          </a-form-item>
          <a-form-item
            :name="['query', index, 'value']"
            :rules="{
              required: true,
              message: 'Missing value',
            }"
          >
            <a-input v-model:value="data.value" placeholder="示例：医院1" allowClear />
          </a-form-item>
          <MinusCircleOutlined @click="removeQuery(data)" />
        </a-space>
        <a-form-item>
          <a-button type="dashed" block @click="addQuery">
            <PlusOutlined />
            添加参数
          </a-button>
        </a-form-item>
        <a-form-item>
          <div style="display: flex; gap: 10px">
            <a-button style="flex: 1" type="primary" html-type="submit">生成地址</a-button>
            <a-button style="flex: 1" @click="handleClear">清空</a-button>
          </div>
        </a-form-item>
      </a-form>
      <div class="copy-container" @click="handleCopy">
        <div class="desc">点击复制</div>
        <div class="copy-text">
          完整地址：<span>{{ completeText }}</span>
        </div>
      </div>
    </div>
    <a-divider type="vertical" style="background-color: #7cb305; height: 100%"></a-divider>
    <div class="part-right">
      <h3>菜单填写</h3>
      <a-form
        :label-col="{ span: 2 }"
        ref="rightFormRef"
        :model="rightFormState"
        @finish="onRightFinish"
      >
        <a-form-item name="id" label="ID" :rules="[{ required: true, message: 'Missing ID' }]">
          <a-input v-model:value="rightFormState.id" placeholder="示例：6W48N58z" allowClear />
        </a-form-item>
        <a-form-item>
          <div style="display: flex; gap: 10px">
            <a-button style="flex: 1" type="primary" html-type="submit">生成地址</a-button>
            <a-button style="flex: 1" @click="handleRightClear">清空</a-button>
          </div>
        </a-form-item>
      </a-form>
      <h4>菜单地址1：</h4>
      <div class="copy-container" @click="handleRightCopy('text1')">
        <div class="desc">点击复制</div>
        <div class="copy-text">
          完整地址：<span>{{ rightAddress.text1 }}</span>
        </div>
      </div>
      <h4>菜单地址2：</h4>
      <div class="copy-container" @click="handleRightCopy('text2')">
        <div class="desc">点击复制</div>
        <div class="copy-text">
          完整地址：<span>{{ rightAddress.text2 }}</span>
        </div>
      </div>
      <h4>菜单地址3：</h4>
      <div class="copy-container" @click="handleRightCopy('text3')">
        <div class="desc">点击复制</div>
        <div class="copy-text">
          完整地址：<span>{{ rightAddress.text3 }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.tool-page {
  max-width: 1200px;
  background: #baabd641;
  margin: 0 auto;
  padding: 20px;
  height: 100%;
  display: flex;
  gap: 20px;
  .part-left,
  .part-right {
    flex: 1;
    overflow: auto;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE 10+ */
    &::-webkit-scrollbar {
      display: none; /* Chrome/Safari/Webkit */
    }
  }
  .query-space {
    display: flex;
    :deep(.ant-space-item:nth-child(1)) {
      flex: 1;
    }
    :deep(.ant-space-item:nth-child(2)) {
      flex: 1;
    }
  }
  .copy-container {
    border: 1px dashed rgba(0, 135, 238, 0.178);
    padding: 8px;
    cursor: pointer;
    &:hover {
      border-color: rgba(0, 135, 238, 1);
    }
    .desc {
      color: #ccc;
      font-size: 12px;
      margin-bottom: 8px;
    }
    .copy-text {
      font-size: 18px;
      color: #000000d9;
      span {
        color: #f00;
      }
    }
  }
}
</style>
