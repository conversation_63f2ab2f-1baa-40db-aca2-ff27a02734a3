<template>
  <div class="card-option" @click="handleClick">
    <div v-if="icon" class="card-option__icon">
      <img :src="icon" style="width: 100%" alt="" />
    </div>
    <div v-else class="card-option__icon" :style="{ background: iconColor }"></div>
    <div class="card-option__title">{{ title }}</div>
    <div class="card-option__desc">{{ desc }}</div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits } from 'vue'

const props = defineProps<{
  title: string
  desc: string
  iconColor?: string
  icon?: string
}>()

const emit = defineEmits(['click'])

function handleClick() {
  emit('click')
}
</script>

<style lang="less" scoped>
.card-option {
  display: flex;
  width: 100%;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 250px;
  background: #fff;
  border-radius: 12px;
  // box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.04);
  border: 1px solid #0000001a;
  cursor: pointer;
  transition: box-shadow 0.2s;
  padding: 24px 16px;
  box-sizing: border-box;
  margin: 0 auto;
  &:hover {
    border: 1px solid #ccc;
  }
  &__icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  &__title {
    font-size: 18px;
    font-weight: 600;
    color: #222;
    margin-bottom: 8px;
    text-align: center;
  }
  &__desc {
    font-size: 14px;
    color: #888;
    text-align: center;
    line-height: 1.5;
  }
}
</style>
