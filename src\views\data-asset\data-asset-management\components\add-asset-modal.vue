<template>
  <a-modal
    :visible="visible"
    :title="isAddTable ? '新增表' : '新增资产'"
    @ok="handleOk"
    @cancel="handleCancel"
    :maskClosable="false"
    :confirmLoading="loading"
    width="1000px"
    :destroyOnClose="true"
  >
    <a-row justify="start" align="top" type="flex">
      <a-col :span="8">
        <Tree
          :tree-data="treeData"
          :fieldNames="{
            children: 'children',
            title: 'name',
            key: 'id',
          }"
          class="custom-tree"
          showIcon
          @select="handleSelect"
          @expand="handleExpand"
          v-if="treeData"
          :defaultExpandAll="true"
          :expandedKeys="expandedKeys"
          :selectedKeys="selectedKeys"
        >
          <template #icon="{ dataRef }">
            <img
              :src="dataSource"
              alt=""
              v-if="!dataRef.isDataBase"
              style="width: 18px; height: 18px"
            />
          </template>
          <template #title="{ name, dataRef }">
            <div class="tree-node-content">
              <type-icon
                v-if="dataRef.isDataBase"
                style="margin-right: 10px"
                :type="dataRef?.dbType"
              />
              <span class="node-name" :title="name"
                >{{ name }}
                <template v-if="dataRef?.tableCount">（{{ dataRef?.tableCount }}）</template>
              </span>
            </div>
          </template>
        </Tree>
      </a-col>
      <a-col :span="16">
        <a-table
          :loading="loadingTableData"
          :columns="columns"
          :data-source="tableSource"
          :row-selection="{
            selectedRowKeys: selectedRowKeys,
            onChange: onSelectChange,
            checkStrictly: false,
          }"
          :pagination="false"
          row-key="key"
          :scroll="{ y: 500 }"
        >
          <template #expandIcon="{ expanded, onExpand, record }">
            <span @click="(e) => onExpand(record, e)" v-if="record.tableType">
              <DownOutlined v-if="expanded" style="margin-right: 5px; color: #6d6d6d" />
              <RightOutlined v-else style="margin-right: 5px; color: #6d6d6d" />
            </span>
          </template>
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'name'">
              <div class="table-name">
                <TableOutlined v-if="record.tableType" style="margin-right: 5px" />
                {{ record.name }}
              </div>
            </template>
          </template>
        </a-table>
      </a-col>
    </a-row>
  </a-modal>
</template>

<script lang="ts" setup>
import { ref, defineProps, defineEmits, watch, computed } from 'vue'
import {
  getSourceAndDBList,
  getTableInfoList,
  addDataAssert,
} from '@/api/assetmanager/dataassertmanage/dataassertmanage'
import { message, type TreeProps } from 'ant-design-vue'
import TypeIcon from '@/views/data-asset/metadata-query/components/type-icon.vue'
import dataSource from '@/assets/data-base/data-source.png'
import { TableOutlined, DownOutlined, RightOutlined } from '@ant-design/icons-vue'
import { Tree } from 'ant-design-vue'

interface databaseName {
  value: string
  label: string
  databaseName: string
  ip: string
  port: string
  engine: string
  datasourceId: string
  key?: string
}

interface Column {
  columnType: string
  isNullable: string
  dataType: string
  columnComment: string
  ordinalPosition: string
  columnKey: string
  tableName: string
  columnName: string
  name?: string
  value?: string
  key?: string
  parentValue?: string
}

interface TableInfo {
  databaseName: string
  tableCollation: string
  tableName: string
  columnList: Column[]
  name?: string
  value?: string
  key?: string
  description?: string
}

const props = defineProps<{
  visible: boolean
  parentData: Record<string, any>
}>()

const emit = defineEmits(['update:visible', 'success'])

const formRef = ref()
const formState = ref({
  name: '',
  description: '',
  dbType: undefined as string | undefined,
  databaseName: undefined as string | undefined,
  tableList: [] as any[],
})
const treeData = ref<TreeProps['treeData']>([])
const tableSource = ref<any>([])
const selectedData = ref<any>({})
const selectedKeys = ref<string[]>([])

const isAddTable = computed(() => props.parentData?.isDataBase)

const allDbNameList = ref<databaseName[]>([])
const allTableList = ref<TableInfo[]>([])
const loading = ref(false)
const expandedKeys = ref<string[]>([])
const loadingTableData = ref(false)

const handleOk = async () => {
  try {
    loading.value = true

    const list = fitterDataHandle(selectedRow.value)

    const obj: any = {
      databaseType: selectedData.value.dbType,
      databaseName: selectedData.value.databaseName || selectedData.value.tableDbname,
      datasourceId: selectedData.value.datasourceId,
      ip: selectedData.value.ip,
      port: selectedData.value.port,
      tableList: JSON.stringify(list),
      directoryId: props?.parentData?.directoryId,
    }

    const { data } = await addDataAssert(obj, {})
    message.success('创建成功')
    emit('success', data)
    handleCancel()
  } catch (error) {
    message.error('创建失败')
  } finally {
    loading.value = false
  }
}

const handleExpand: TreeProps['onExpand'] = (keys) => {
  expandedKeys.value = keys as string[]
}

const handleCancel = () => {
  treeData.value = []
  tableSource.value = []
  selectedRow.value = []
  selectedRowKeys.value = []
  selectedData.value = {}
  expandedKeys.value = []
  emit('update:visible', false)
}

watch(
  () => props.visible,
  async (newVal) => {
    if (!newVal) {
      formRef.value?.resetFields()
    } else {
      await getSourceAndDBListHandle()
    }
  },
)

const getSourceAndDBListHandle = async () => {
  const res = await getSourceAndDBList({ keyword: props.parentData.databaseName })
  const list = processTreeData(res.data)
  const hasChildrenList = list.filter((item) => item.children && item.children.length > 0)
  console.log('%c [ list ]-220', 'font-size:13px; background:#a69415; color:#ead859;', list)
  treeData.value = hasChildrenList
  if (isAddTable.value) {
    // 设置展开的节点
    expandedKeys.value = treeData.value.map((item: any) => item.id)
  }
}

// 处理树形数据，递归设置 isLeaf
const processTreeData = (data: any[], isSchemaNode = false) => {
  return data
    .map((item) => {
      const processedItem = { ...item, name: `${item.dbDesc}` }
      if (!processedItem?.id) {
        processedItem.id = processedItem.key + '_' + (isSchemaNode ? item.tableSchema : '')
        const list = ['sqlserver', 'oracle', 'postgresql', 'greemplum', 'gbase', 'kingbase']
        if (isSchemaNode) {
          processedItem.name = processedItem.tableSchema
        } else {
          processedItem.name = processedItem.databaseName + '（' + processedItem.ip + '）'
        }
        processedItem.isDataBase = true
        processedItem.isSchemaNode = isSchemaNode
        processedItem.isLeaf = true
      }
      if (processedItem?.databaseList?.length) {
        processedItem.children = processedItem?.databaseList
        delete processedItem.databaseList
      }
      if (processedItem?.schemaList?.length) {
        isSchemaNode = true
        processedItem.children = processedItem?.schemaList
        delete processedItem.schemaList
      } else {
        isSchemaNode = false
      }
      if (processedItem.children && processedItem.children.length > 0) {
        const childrenList = processTreeData(processedItem.children, isSchemaNode)
        processedItem.isLeaf = !childrenList.length
        processedItem.children = childrenList
        if (isSchemaNode) {
          processedItem.children = processedItem.children.map((ele: any) => ({
            ...ele,
            datasourceId: processedItem.datasourceId,
          }))
        }
      }
      return processedItem
    })
    .filter((item: any) => {
      // 如果是新增表的场景
      if (isAddTable.value) {
        // 如果是数据库节点，检查是否匹配当前选中的数据库
        if (item.isDataBase) {
          return (
            item.databaseName === props.parentData.databaseName && item.ip === props.parentData.ip
          )
        }
        // 如果是数据源节点，检查其子节点中是否包含目标数据库
        if (item.children) {
          item.children = item.children.filter(
            (child: any) =>
              child.databaseName === props.parentData.databaseName &&
              child.ip === props.parentData.ip,
          )

          if (item.children.length > 0) {
            handleSelect([item.children[0].key], { node: item.children[0] })
          }
          return item.children.length > 0
        }
      }
      // 非新增表场景或其他情况，过滤掉已选择的项
      return !item.selected
    })
}

const handleSelect = async (keys: any, info: any) => {
  if (!info.node.isDataBase || !info.node.isLeaf) {
    return
  } else {
    selectedKeys.value = keys
  }
  selectedData.value = info.node
  const { key, dbType } = info.node.dataRef
  const params: any = {
    dbType,
    databaseName: info.node.databaseName,
    key,
  }
  if (info.node.isSchemaNode) {
    params.databaseName = info.node.tableDbname
    params.tableSchema = info.node.tableSchema
  }
  try {
    loadingTableData.value = true
    const res = await getTableInfoList(params)
    res.data.forEach((item: any) => {
      item.name = item.tableName
      item.description = item.tableComment
      item.children = item.columnList
      item.children.forEach((column: any) => {
        column.key = column.tableName + '-' + column.columnName
        column.name = column.columnName
        column.description = column.columnComment
      })
      delete item.columnList
    })
    tableSource.value = res.data.filter((item: any) => !item.selected)
  } catch (error) {
    message.error('获取表信息失败')
  } finally {
    loadingTableData.value = false
  }
}

const columns = [
  {
    title: '名称',
    key: 'name',
    dataIndex: 'name',
  },
  {
    title: '注释',
    key: 'description',
    dataIndex: 'description',
  },
]

const selectedRowKeys = ref<string[]>([])
const selectedRow = ref<string[]>([])

const onSelectChange = (keys: string[], selectedRows: any[]) => {
  selectedRowKeys.value = keys
  selectedRow.value = selectedRows
}

const fitterDataHandle = (selectedRows: any[]) => {
  // 创建一个Map来存储表和其对应的列
  const tableMap = new Map()

  // 首先处理所有选中的表
  selectedRows.forEach((item: any) => {
    if (item.tableType) {
      // 如果是表，创建一个新对象并初始化columnList
      const tableData = {
        ...item,
        columnList: [],
      }
      delete tableData.children // 删除children属性
      tableMap.set(item.key, tableData)
    }
  })

  // 如果没有直接选中的表，从列数据中提取表信息
  selectedRows.forEach((item: any) => {
    if (!item.tableType && item.tableKey) {
      if (!tableMap.has(item.tableKey)) {
        // 从原始数据源中查找对应的表
        const parentTable = tableSource.value.find((table: any) => table.key === item.tableKey)
        if (parentTable) {
          const tableData = {
            ...parentTable,
            columnList: [],
          }
          delete tableData.children
          tableMap.set(item.tableKey, tableData)
        }
      }
    }
  })

  // 处理列数据
  selectedRows.forEach((item: any) => {
    if (!item.tableType && item.tableKey) {
      const table = tableMap.get(item.tableKey)
      if (table) {
        table.columnList.push(item)
      }
    }
  })

  // 转换Map为数组并返回
  return Array.from(tableMap.values())
}
</script>

<style lang="less" scoped>
:deep(.ant-tree) {
  .ant-tree-treenode {
    width: 100%;
    height: 40px;
    display: flex;
    align-items: center;
    .ant-tree-switcher {
      line-height: 40px;
    }
    .ant-tree-iconEle {
      width: 18px;
      height: 18px;
      line-height: 18px;
      margin-right: 5px;
    }
    .ant-tree-title {
      width: 100%;
    }
    .ant-tree-node-content-wrapper {
      width: 100%;
      height: 40px;
      display: flex;
      align-items: center;
    }
  }
}
.custom-tree {
  margin-top: 10px;
  :deep(.ant-tree-node-content-wrapper) {
    width: 100%;
    &:hover {
      background-color: #f5f5f5;
    }
  }
  :deep(.ant-tree-node-selected) {
    background-color: #e6f7ff;
  }
  :deep(.ant-tree-title) {
    width: 100%;
  }
  .tree-node-content {
    width: 100%;
    padding-right: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .node-name {
      flex: 1;
      white-space: nowrap;
    }

    .node-actions {
      display: flex;
      align-items: center;
      gap: 8px;

      .action-icon {
        font-size: 16px;
        color: #666;
        cursor: pointer;

        &:hover {
          color: #1890ff;
        }
      }
    }
  }
}
.table-name {
  display: flex;
  flex-wrap: nowrap;
}
:deep(.ant-table-cell-with-append) {
  display: flex;
}
</style>
