<script lang="ts" setup>
import { ref, defineExpose, onMounted, watch } from 'vue'
import { columns, type ResQualityList } from './data-source'
import { useRouter, useRoute } from 'vue-router'
import cardBox from '@/components/card-box/card-box.vue'
import { QuestionCircleOutlined } from '@ant-design/icons-vue'
import { reqGetDataQualityList } from '@/api/services/dataquality'

const router = useRouter()
const route = useRoute()

const dataSource = ref<ResQualityList[]>([])
const filterDataSource = ref<ResQualityList[]>([])
const inputValue = ref('')

// Pagination state
const current = ref(1)
const pageSize = ref(10)
const total = ref(0)

const handleEnterKeyDown = () => {
  current.value = 1 // Reset to first page when searching
  getQualityData()
}

const handleGoDetail = (record: any) => {
  console.log(record)
  const query = {
    compId: record.compId,
    flowInstanceId: record.flowInstanceId,
    compDesc: record.compDesc,
  }
  router.push({ path: '/reporterDetail', query })
}

const getQualityData = async () => {
  const id = Array.isArray(route.query.id) ? route.query.id[0] : route.query.id
  const res = await reqGetDataQualityList({
    flowInstanceId: id || undefined,
    pageIndex: current.value,
    pageSize: pageSize.value,
    name: inputValue.value || undefined,
  })
  dataSource.value = res.records || []
  filterDataSource.value = res.records || []
  total.value = res.total || 0
}

// Handle pagination change
const handleTableChange = (pagination: any) => {
  current.value = pagination.current
  pageSize.value = pagination.pageSize
  getQualityData()
}

onMounted(() => {
  console.log('mounted')
  getQualityData()
})
</script>

<template>
  <div class="router-page">
    <cardBox title="数据质量" subTitle="通过引入数据质量组件提升数据的可信任度, 分析数据质量报告">
      <div style="display: flex; justify-content: space-between; margin-bottom: 24px">
        <a-input
          style="width: 400px"
          v-model:value="inputValue"
          placeholder="请输入名称"
          allowClear
        >
        </a-input>
        <a-button type="primary" @click="handleEnterKeyDown"> 搜索 </a-button>
      </div>
      <a-table
        :columns="columns"
        :data-source="filterDataSource"
        :pagination="{
          current: current,
          pageSize: pageSize,
          total: total,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total: number) => `共 ${total} 条`,
        }"
        @change="handleTableChange"
      >
        <template #headerCell="{ title, column }">
          <div v-if="column.key === 'time'">
            {{ title }}
            <a-tooltip title="任务开始时间和结束时间">
              <QuestionCircleOutlined />
            </a-tooltip>
          </div>
        </template>
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'time'">
            <div>{{ record.startTime }} -</div>
            {{ record.endTime }}
          </template>
          <template v-if="column.key === 'operation'">
            <a-button style="padding: 0" type="link" @click="handleGoDetail(record)">
              查看详情
            </a-button>
          </template>
        </template>
      </a-table>
    </cardBox>
  </div>
</template>

<style lang="less" scoped>
.router-page {
  height: 100%;

  > header {
    margin-bottom: 24px;
  }
}
</style>
