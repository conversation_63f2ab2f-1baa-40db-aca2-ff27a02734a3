<template>
  <div>
    <a-popover title="" trigger="click" v-model:open="showMenu" :overlayStyle="{ padding: 0 }">
      <a-button type="link" @click="showMenu = !showMenu"> {{ active }} <DownOutlined /> </a-button>
      <template #content>
        <a-menu @click="handleClick" style="border: none">
          <a-menu-item v-for="item in menuMap" :key="item.key">{{ item.label }}</a-menu-item>
        </a-menu>
      </template>
    </a-popover>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { DownOutlined } from '@ant-design/icons-vue'

const props = defineProps<{
  callback?: (value: string) => void
}>()
const showMenu = ref(false)
const active = ref('等于')
const menuMap = ref([
  { key: '1', label: '等于' },
  { key: '2', label: '不等于' },
  { key: '3', label: '包含' },
  { key: '4', label: '不包含' },
  { key: '5', label: '以...开始' },
  { key: '6', label: '以...结束' },
  { key: '7', label: '为空' },
])

const handleClick = (e: any) => {
  showMenu.value = false
  active.value = menuMap.value[e.key - 1].label
  props.callback && props.callback(menuMap.value[e.key - 1].label)
}
</script>

<style scoped lang="less"></style>
