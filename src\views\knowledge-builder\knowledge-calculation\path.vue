<script lang="ts" setup>
import { defineProps } from 'vue'
import { pathColumn } from './data-source'
import { CodeSandboxOutlined } from '@ant-design/icons-vue'
const props = withDefaults(defineProps<{ pageData?: any }>(), {
  pageData: () => [], // 默认值为数组
})
</script>

<template>
  <div class="centrality-page">
    <a-table :columns="pathColumn" :data-source="pageData">
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'sourceInfo'">
          <div><CodeSandboxOutlined /> {{ record.sourceInfo.name }}</div>
          <div>年龄：{{ record.sourceInfo.age }}</div>
          <div>部门：{{ record.sourceInfo.department }}</div>
        </template>
        <template v-if="column.dataIndex === 'targetInfo'">
          <div><CodeSandboxOutlined /> {{ record.targetInfo.name }}</div>
          <div>年龄：{{ record.targetInfo.age }}</div>
          <div>部门：{{ record.targetInfo.department }}</div>
        </template>
      </template>
    </a-table>
  </div>
</template>

<style lang="less" scoped></style>
