<template>
  <a-radio-group v-model:value="value">
    <a-radio-button value="table">表格字段</a-radio-button>
    <a-radio-button value="search">搜索字段</a-radio-button>
    <a-radio-button value="form">表单字段</a-radio-button>
  </a-radio-group>
  <tableColumns v-model="dataSourceData" v-if="value === 'table'"></tableColumns>
  <searchColumns v-model="searchData" type="search" v-else-if="value === 'search'"></searchColumns>
  <searchColumns v-model="formData" type="form" v-else-if="value === 'form'"></searchColumns>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import tableColumns from './table-columns.vue'
import searchColumns from './search-columns.vue'

interface Columns {
  dataSource: any[]
  search: any[]
  form: any[]
}

const value = ref('table')

const props: Columns = withDefaults(defineProps<Columns>(), {
  dataSource: () => [],
  search: () => [],
})

const dataSourceData = computed(() => props.dataSource)

const searchData = computed(() => props.search)

const formData = computed(() => props.form)
</script>
<style scoped lang="less"></style>
