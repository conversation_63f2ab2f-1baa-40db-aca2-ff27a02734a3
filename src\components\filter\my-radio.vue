<template>
  <RadioGroup v-model:value="data" option-type="button" :options="options" button-style="solid" />
</template>

<script setup lang="ts">
import { RadioGroup } from 'ant-design-vue'
import type { RadioGroupChildOption } from 'ant-design-vue/es/radio/Group'
const data = defineModel<string>('value', { required: true })
defineProps<{
  options: RadioGroupChildOption[]
}>()
</script>

<style scoped></style>
