<template>
  <div class="directory-tree">
    <div class="title">
      <span>元数据列表</span>
      <span>
        <a-tooltip placement="top">
          <template #title>
            <span>刷新</span>
          </template>
          <RedoOutlined style="font-size: 19px" @click="refresh" />
        </a-tooltip>
      </span>
    </div>
    <a-input
      v-model:value="searchName"
      placeholder="请输入内容"
      :bordered="true"
      @change="handleSearch"
      style="margin-bottom: 10px"
    />
    <a-tree
      v-if="reloadTree"
      :tree-data="treeData"
      v-model:expandedKeys="expandedKeys"
      v-model:selectedKeys="selectedKeys"
      :fieldNames="{
        children: 'children',
        title: 'name',
        key: 'id',
      }"
      class="custom-tree"
      showIcon
      :load-data="onLoadData"
      @select="handleSelect"
      @expand="handleExpand"
    >
      <template #icon="{ dataRef }">
        <img
          :src="dataSource"
          alt=""
          v-if="!dataRef.isDataBase && !dataRef.isSchema"
          style="width: 18px; height: 18px"
        />
      </template>
      <template #title="{ name, dataRef }">
        <div
          class="tree-node-content"
          @mouseenter="handleMouseEnter(dataRef)"
          @mouseleave="handleMouseLeave(dataRef)"
        >
          <type-icon
            v-if="(dataRef.isDataBase && !dataRef?.isModule) || dataRef.isSchema"
            style="margin-right: 10px"
            :type="dataRef?.dbType"
          />
          <span class="node-name" :title="name"
            >{{ name }}
            <template v-if="dataRef?.tableCount && !dataRef?.isSchema"
              >（{{ dataRef?.tableCount }}）</template
            >
          </span>
        </div>
      </template>
    </a-tree>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { RedoOutlined } from '@ant-design/icons-vue'
import { storeToRefs } from 'pinia'
import { useUserStore } from '@/stores/page/data-asset'
import {
  getDbListBySourceId,
  getSrouceList,
} from '@/api/assetmanager/metadatamanage/metadatamanage'
import TypeIcon from '@/views/data-asset/metadata-query/components/type-icon.vue'
import dataSource from '@/assets/data-base/data-source.png'

const emit = defineEmits(['add-asset'])

const userStore = useUserStore()
const { metadata } = storeToRefs(userStore)
const { setMetadata } = userStore

const expandedKeys = ref<string[]>([])
const selectedKeys = ref<string[]>([])
const treeData = ref<any>([])
const searchName = ref('')
const route = useRoute()
const reloadTree = ref(true)
// 存储原始树形数据
const originalTreeData = ref<any>([])
const dbName = computed(() => (route.query.dbName as string) || '')

onMounted(() => {
  if (metadata.value.expandedKeys?.length) {
    expandedKeys.value = metadata.value.expandedKeys
  }
  if (metadata.value.selectedKeys?.length) {
    selectedKeys.value = metadata.value.selectedKeys
    emit('add-asset', { ...metadata.value.dataRef })
  }
  if (metadata.value.originalTreeData?.length) {
    originalTreeData.value = metadata.value.originalTreeData
  }
  if (metadata.value.treeData?.length) {
    // 未搜索时，设置 treeData 为 originalTreeData
    if (!dbName.value) {
      treeData.value = metadata.value.originalTreeData
    } else {
      treeData.value = metadata.value.treeData
    }
    if (dbName.value) {
      searchName.value = dbName.value
      handleSearch()
    }
  } else {
    fetchData()
  }
})

// 处理树形数据，递归设置 isLeaf
const processTreeData = (data: any[]) => {
  expandedKeys.value = []
  return data.map((item) => {
    const processedItem = { ...item, name: `${item.dbDesc}` }
    // if (processedItem.assertCount <= 0 && !processedItem?.children?.length) {
    processedItem.isLeaf = false
    // }
    if (processedItem.children && processedItem.children.length > 0) {
      processedItem.children = processTreeData(processedItem.children)
    }
    return processedItem
  })
}

const handleSelect = (selectedKeysArr: string[], info: any) => {
  setMetadata('selectedKeys', selectedKeysArr)
  setMetadata('expandedKeys', expandedKeys.value)
  setMetadata('treeData', treeData.value)

  const dataRef = info.node.dataRef
  setMetadata('dataRef', dataRef)
  if (dataRef?.isDataBase) {
    emit('add-asset', dataRef)
  }
}

const handleExpand = (expandedKeysArr: string[]) => {
  expandedKeys.value = expandedKeysArr
  setMetadata('expandedKeys', expandedKeysArr)
  setMetadata('treeData', treeData.value)
}

const onLoadData: any = (treeNode: any) => {
  return new Promise<void>((resolve) => {
    if (treeNode.dataRef.assertCount <= 0 || treeNode.dataRef.isSchema) {
      resolve()
      return
    }
    getDbListBySourceId({ datasourceId: treeNode.dataRef.id }, {})
      .then(({ data }) => {
        const list = data.map((item: any) => {
          const list = ['sqlserver', 'oracle', 'postgresql', 'greemplum', 'gbase', 'kingbase']
          const obj = {
            ...item,
            id: treeNode.dataRef.id + '_' + item.tableSchema,
            name: item.tableSchema,
            isLeaf: list.includes(item.dbType) ? false : true,
            isDataBase: list.includes(item.dbType) ? false : true,
            isSchema: list.includes(item.dbType) ? true : false,
          }
          if (item?.schemaList && item?.schemaList.length) {
            obj.children = item.schemaList.map((schema: any) => ({
              ...schema,
              id: treeNode.dataRef.id + '_' + schema.tableSchema,
              name: schema.tableSchema,
              isLeaf: true,
              isDataBase: true,
              isModule: true,
            }))
          }
          return obj
        })
        if (!treeNode?.dataRef?.children) treeNode.dataRef.children = []
        const fileList = treeNode.dataRef.children.filter((item: any) => !item.isDataBase)
        treeNode.dataRef.children = [...fileList, ...list]
        treeData.value = [...treeData.value]
        setMetadata('treeData', treeData.value)
        updateOriginalTreeNode(originalTreeData.value, treeNode.dataRef.id, treeNode.dataRef)
        resolve()
      })
      .catch((error) => {
        console.error('加载数据失败:', error)
        resolve()
      })
  })
}

// 更新 originalTreeData 中对应节点的数据
const updateOriginalTreeNode = (nodes: any[], nodeId: string, dataRef: any): boolean => {
  for (let i = 0; i < nodes.length; i++) {
    if (nodes[i].id === nodeId) {
      nodes[i] = JSON.parse(JSON.stringify(dataRef))
      return true
    }
    if (nodes[i].children && nodes[i].children.length > 0) {
      if (updateOriginalTreeNode(nodes[i].children, nodeId, dataRef)) return true
    }
  }

  return false
}

const fetchData = async () => {
  try {
    const params = {
      pageIndex: 1,
      pageSize: 10000,
    }
    const res = await getSrouceList(params)
    if (res?.data?.records) {
      treeData.value = processTreeData(res.data.records)
      if (metadata.value?.originalTreeData.length <= 0) {
        originalTreeData.value = JSON.parse(JSON.stringify(treeData.value)) // 保存原始数据的副本
        setMetadata('originalTreeData', originalTreeData.value)
      }

      setMetadata('treeData', treeData.value)
      reloadTree.value = true
      if (dbName.value) {
        searchName.value = dbName.value
        handleSearch()
      } else {
        // 如果树形数据不为空，则展开第一个节点，并加载子节点
        if (treeData.value.length > 0) {
          const firstNode = treeData.value[0]
          expandedKeys.value = [firstNode.id]
          setTimeout(() => {
            const childrenData = firstNode.children
            if (childrenData && childrenData.length > 0) {
              const firstChildNode = childrenData[0]
              selectedKeys.value = [firstChildNode.id]
              handleSelect([firstChildNode.key], {
                selected: true,
                selectedNodes: [firstChildNode],
                node: { dataRef: firstChildNode },
              })
            }
          }, 200)
        }
      }
    }
  } catch (error) {
    console.error('获取数据失败:', error)
  }
}

const handleSearch = () => {
  if (!searchName.value) {
    // 如果搜索框为空，恢复原始数据
    treeData.value = JSON.parse(JSON.stringify(originalTreeData.value))
  } else {
    // 使用本地过滤
    treeData.value = filterTreeData(originalTreeData.value, searchName.value)
  }
  reloadTree.value = false
  setTimeout(() => {
    reloadTree.value = true
  }, 0)
}

// 本地过滤树形数据
const filterTreeData = (data: any[], keyword: string): any[] => {
  if (!keyword) return data

  // 递归搜索匹配节点
  const filterNodes = (nodes: any[]): any[] => {
    return nodes.filter((node) => {
      // 检查当前节点是否匹配
      const isMatch = node.name.toLowerCase().includes(keyword.toLowerCase())

      // 递归检查子节点
      let filteredChildren: any[] = []
      if (node.children && node.children.length > 0) {
        filteredChildren = filterNodes(node.children)
      }

      // 如果子节点有匹配，保留这些子节点
      if (filteredChildren.length > 0) {
        node.children = filteredChildren
        return true
      }

      // 如果当前节点匹配，则保留
      return isMatch
    })
  }

  return filterNodes(JSON.parse(JSON.stringify(data)))
}

const handleMouseEnter = (dataRef: any) => {
  dataRef.isHovered = true
}

const handleMouseLeave = (dataRef: any) => {
  dataRef.isHovered = false
}

const refresh = () => {
  useUserStore().clearMetadata()
  reloadTree.value = false
  fetchData()
}

defineExpose({
  fetchData,
})
</script>

<style lang="less" scoped>
.directory-tree {
  height: 100%;

  .custom-tree {
    margin-top: 10px;
    :deep(.ant-tree-node-content-wrapper) {
      width: 100%;
      &:hover {
        background-color: #f5f5f5;
      }
    }
    :deep(.ant-tree-node-selected) {
      background-color: #e6f7ff;
    }
    :deep(.ant-tree-title) {
      width: 100%;
    }
    .tree-node-content {
      width: 100%;
      padding-right: 10px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .node-name {
        flex: 1;
        white-space: nowrap;
      }

      .node-actions {
        display: flex;
        align-items: center;
        gap: 8px;

        .action-icon {
          font-size: 16px;
          color: #666;
          cursor: pointer;

          &:hover {
            color: #1890ff;
          }
        }
      }
    }
  }

  :deep(.ant-tree) {
    .ant-tree-treenode {
      width: 100%;
      height: 40px;
      display: flex;
      align-items: center;
      .ant-tree-switcher {
        line-height: 40px;
      }
      .ant-tree-iconEle {
        width: 18px;
        height: 18px;
        line-height: 18px;
        margin-right: 5px;
      }
      .ant-tree-title {
        width: 100%;
      }
      .ant-tree-node-content-wrapper {
        width: 100%;
        height: 40px;
        display: flex;
        align-items: center;
      }
    }
  }

  .title {
    margin-right: auto;
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 24px;
    padding-bottom: 16px;
    display: flex;
    justify-content: space-between;
  }
}
</style>
