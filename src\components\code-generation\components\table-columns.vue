<template>
  <div class="table-columns-add">
    <a-button type="primary" @click="addHandle">新增</a-button>
  </div>
  <a-table :dataSource="newModelValue" :columns="columns">
    <template #bodyCell="{ column, record, index }">
      <template v-if="column.key === 'operate'">
        <a-button type="link" @click="del(index)">删除</a-button>
      </template>
      <a-input
        type="text"
        v-model:value="record[column.key]"
        :placeholder="'请输入' + column.title"
        v-else
      />
    </template>
  </a-table>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { message } from 'ant-design-vue/es/components'

interface Columns {
  modelValue: any[]
}

const emit = defineEmits(['update:modelValue'])

const props: Columns = withDefaults(defineProps<Columns>(), {
  modelValue: () => [],
})

const columns = ref([
  {
    title: '字段名',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '字段描述',
    dataIndex: 'description',
    key: 'description',
  },
  {
    title: '操作',
    dataIndex: 'operate',
    key: 'operate',
  },
])

const newModelValue = computed({
  get: () => props.modelValue,
  set: (value) => {
    emit('update:modelValue', value)
  },
})

const addHandle = () => {
  newModelValue.value.push({
    name: '',
    desc: '',
  })
}

const del = (index: number) => {
  if (newModelValue.value.length > 1) {
    newModelValue.value.splice(index, 1)
  } else {
    message.warning('当前为最后一个表头字段')
  }
}

const upData = () => {
  emit('update:modelValue', newModelValue.value)
}

// 导出 show 方法
defineExpose({
  upData,
})
</script>

<style scoped lang="less">
.table-columns-add {
  text-align: right;
  margin-bottom: 16px;
}
</style>
