<template>
  <div class="database-table">
    <div class="header-div">
      <a-cascader
        v-model:value="searchForm.labelId"
        placeholder="Please select"
        :options="assetsLabelTree"
        popupClassName="database-table-cascader"
        :fieldNames="{
          label: 'labelName',
          value: 'labelId',
          children: 'children',
        }"
        @change="changeHandle"
      >
        <a-button type="text" @click.prevent style="display: flex; align-items: center"
          >标签
          <template v-if="selectLabel.labelName">：</template>
          <span
            :title="selectLabel.labelName"
            style="
              display: inline-block;
              max-width: 100px;
              white-space: nowrap; /* 禁止换行 */
              overflow: hidden; /* 隐藏溢出内容 */
              text-overflow: ellipsis; /* 显示省略号 */
            "
          >
            {{ selectLabel.labelName }}</span
          >
          <CloseCircleOutlined
            v-if="selectLabel.labelName"
            style="font-size: 12px; opacity: 0; transition: opacity 0.2s"
            class="clear-icon"
            @click.stop="handleIconClick"
          />
          <DownOutlined style="font-size: 12px" />
        </a-button>
      </a-cascader>
    </div>
    <div class="content-div">
      <cardBox subTitle=" ">
        <template #title>
          <div style="display: flex; align-items: center" class="content-rl content-top">
            <type-icon :width="48" :height="36" :type="databaseData?.databaseType" />
            <span>{{ databaseData?.databaseName }}</span>
          </div>
        </template>
        <template #subTitle>
          <div class="content-rl">{{ databaseDescription }}</div>
        </template>
        <template #headerRight>
          <!-- <a-button
            type="primary"
          >
            新增资产
          </a-button> -->
        </template>
        <a-tabs v-model:activeKey="activeKey">
          <template #leftExtra>
            <!-- 左侧占位符 -->
            <span style="color: transparent"> 22 </span>
          </template>
          <a-tab-pane key="1" tab="数据列表">
            <div style="padding: 0 12px">
              <div
                style="
                  margin-bottom: 10px;
                  display: flex;
                  align-items: center;
                  padding-left: 12px;
                  gap: 16px;
                  flex-wrap: wrap;
                "
              >
                <div style="display: flex; align-items: center; gap: 8px">
                  <span style="white-space: nowrap">表名称：</span>
                  <a-input
                    allow-clear
                    v-model:value="tableName"
                    placeholder="请输入表名称"
                    style="width: 200px"
                    @pressEnter="handleSearch"
                  />
                </div>
                <div style="display: flex; align-items: center; gap: 8px">
                  <span style="white-space: nowrap">发布状态：</span>
                  <a-select
                    v-model:value="publishStatus"
                    placeholder="请选择发布状态"
                    allow-clear
                    style="width: 150px"
                  >
                    <a-select-option :value="1">已发布</a-select-option>
                    <a-select-option :value="0">未发布</a-select-option>
                  </a-select>
                </div>
                <div style="display: flex; align-items: center; gap: 8px">
                  <a-button type="primary" @click="handleSearch" :loading="searchLoading">
                    查询
                  </a-button>
                  <a-button @click="handleReset"> 重置 </a-button>
                  <a-divider type="vertical" />
                  <a-button
                    type="primary"
                    @click="handleBatchRelease"
                    :disabled="selectedRowKeys.length === 0"
                    :loading="batchLoading"
                  >
                    批量发布
                  </a-button>
                  <a-button
                    @click="handleBatchOffline"
                    :disabled="selectedRowKeys.length === 0"
                    :loading="batchLoading"
                  >
                    批量下架
                  </a-button>
                </div>
              </div>
              <a-table
                :columns="columns"
                :data-source="tableData"
                :loading="tableLoading"
                :pagination="pagination"
                :row-selection="rowSelection"
                row-key="tableId"
                @change="handleTableChange"
              >
                <template #bodyCell="{ column, record }">
                  <template v-if="column.key === 'operate'">
                    <a-space class="action-container">
                      <a @click.prevent="handleEdit(record)">编辑</a>
                      <a-popconfirm
                        title="是否确认删除"
                        ok-text="是"
                        cancel-text="否"
                        @confirm="handleDel(record)"
                      >
                        <a>删除</a>
                      </a-popconfirm>
                      <a @click.prevent="toDetail(record)">详情</a>
                      <!-- <a @click.prevent="handleAuth(record)">权限</a> -->
                      <a @click.prevent="handleMarking(record)">打标</a>
                      <a-popconfirm
                        v-if="record.status === 1"
                        title="是否确认下架"
                        ok-text="是"
                        cancel-text="否"
                        @confirm="handleStatusChange(record, 0)"
                      >
                        <a>下架</a>
                      </a-popconfirm>
                      <a-popconfirm
                        v-if="record.status === 0"
                        title="是否确认发布"
                        ok-text="是"
                        cancel-text="否"
                        @confirm="handleStatusChange(record, 1)"
                      >
                        <a>发布</a>
                      </a-popconfirm>
                    </a-space>
                  </template>
                </template>
              </a-table>
            </div>
          </a-tab-pane>
        </a-tabs>
      </cardBox>
    </div>

    <!-- Edit Table Modal -->
    <edit-table-modal
      v-model:visible="editModalVisible"
      :table-data="currentTable"
      :database-data="databaseData"
      @success="handleEditSuccess"
    />

    <!-- Marking Modal -->
    <a-modal
      :visible="markingModalVisible"
      title="打标"
      @ok="handleMarkingOk"
      @cancel="handleMarkingCancel"
      :maskClosable="false"
      :confirmLoading="markingSubmitLoading"
    >
      <a-form :model="markingForm" :rules="markingRules" ref="markingFormRef">
        <a-form-item label="标签" name="labelId">
          <a-select
            v-model:value="markingForm.labelId"
            placeholder="请选择标签"
            :options="labelOptions"
            :fieldNames="{ label: 'labelName', value: 'labelId' }"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch, onMounted, computed } from 'vue'
import cardBox from '@/components/card-box/card-box.vue'
import {
  getDataAssetTableList,
  deleteDataAssets,
  updateDataAssets,
  dataAssetsMarking,
  releaseDataAssets,
} from '@/api/assetmanager/dataassertmanage/dataassertmanage'
import {
  getDataAssetsLabelTree,
  getDataAssetsLabel,
} from '@/api/assetmanager/datalabelmodule/datalabelmodule'
import EditTableModal from './edit-table-modal.vue'
import { message } from 'ant-design-vue'
import { DownOutlined, CloseCircleOutlined } from '@ant-design/icons-vue'
import { useRouter } from 'vue-router'
import TypeIcon from '@/views/data-asset/metadata-query/components/type-icon.vue'

const activeKey = ref('1')

const router = useRouter()

const props = defineProps<{
  databaseId?: string
  databaseData?: Record<string, any>
  description?: string
}>()

const emit = defineEmits(['refresh'])

const searchForm = ref<any>({
  labelId: null,
})
const assetsLabelTree = ref<any[]>([])
const tableName = ref('')
const publishStatus = ref(null)
const searchLoading = ref(false)
const selectedRowKeys = ref<string[]>([])
const batchLoading = ref(false)

// 表格数据
const tableData = ref<any[]>([])
const tableLoading = ref(false)
const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条`,
})

const databaseDescription = computed(() => {
  return props.description
})
const tableSchemaCol = {
  dataIndex: 'tableSchema',
  title: '模式名',
  key: 'tableSchema',
}
const columns = ref<any[]>([
  {
    dataIndex: 'tableName',
    title: '表名',
    key: 'tableName',
  },
  {
    dataIndex: 'tableComment',
    title: '表注释',
    key: 'tableComment',
  },
  {
    dataIndex: 'description',
    title: '表描述',
    key: 'description',
  },
  {
    dataIndex: 'status',
    title: '资产状态',
    key: 'status',
    customRender: ({ text }: any) => {
      const mapObj = {
        1: '已发布',
        0: '未发布',
      }
      return mapObj[text as keyof typeof mapObj]
    },
  },
  {
    dataIndex: 'operate',
    title: '操作',
    key: 'operate',
    width: 260,
  },
])

const editModalVisible = ref(false)
const currentTable = ref<Record<string, any>>({})
const selectLabel = ref<Record<string, any>>({})
const markingModalVisible = ref(false)
const markingFormRef = ref()
const labelOptions = ref<any[]>([])
const markingSubmitLoading = ref(false)

const markingForm = ref<Record<string, any>>({
  labelId: null,
})

const markingRules = {
  labelId: [{ required: true, message: '请选择标签' }],
}

const changeHandle = (value: string, selectedOptions: any) => {
  console.log('🚀 ~ changeHandle ~ value:', value)
  if (selectedOptions.length === 2) {
    selectLabel.value = selectedOptions[1]
  }
}

const fetchTableData = async (page?: number, pageSize?: number) => {
  if (!props?.databaseId) return
  try {
    tableLoading.value = true
    const params = {
      databaseId: props.databaseId,
      labelId: searchForm.value.labelId ? searchForm.value.labelId[1] : undefined,
      tableName: tableName.value || undefined,
      status: publishStatus.value,
      pageIndex: page || pagination.value.current,
      pageSize: pageSize || pagination.value.pageSize,
    }

    const res = await getDataAssetTableList(params, {})
    tableData.value = res.data.records || []
    pagination.value = {
      ...pagination.value,
      current: res.data.pageIndex || 1,
      pageSize: res.data.pageSize || 10,
      total: res.data.totalRecords || 0,
    }
  } catch (error) {
    console.error('获取数据表列表失败:', error)
    message.error('获取数据表列表失败')
  } finally {
    tableLoading.value = false
  }
}

const toDetail = (record: any) => {
  router.push({
    name: 'table-detail',
    query: {
      tableId: record.tableId,
      tableName: record.tableName,
      tableComment: record.tableComment,
    },
  })
}

const handleStatusChange = async (record: any, status: number) => {
  try {
    await updateDataAssets(
      {
        tableId: record.tableId,
        status,
      },
      {},
    )
    message.success(status === 1 ? '发布成功' : '停用成功')
    fetchTableData()
    emit('refresh')
  } catch (error) {
    console.log(error)
  }
}

// const handleAuth = (record: any) => {
//   console.log('🚀 ~ handleAuth ~ record:', record)
//   const tableId = record.tableId
//   router.push(`/data-asset-management/auth/${tableId}?tableName=${record.tableName}`)
// }

const handleDel = async (record: any) => {
  try {
    await deleteDataAssets(
      {
        tableId: record.tableId,
      },
      {},
    )
    message.success('删除成功')
    fetchTableData()
    emit('refresh')
  } catch (error) {
    console.log(error)
  }
}

const handleEdit = (record: Record<string, any>) => {
  currentTable.value = record
  editModalVisible.value = true
}

const handleEditSuccess = () => {
  fetchTableData()
  emit('refresh')
}

const handleIconClick = () => {
  selectLabel.value = {}
  searchForm.value.labelId = null
  handleSearch()
}

const handleMarking = (record: Record<string, any>) => {
  currentTable.value = record
  markingModalVisible.value = true
  markingForm.value.labelId = record?.labelId
  fetchLabels()
}

const fetchLabels = async () => {
  try {
    const res = await getDataAssetsLabel({}, {})
    labelOptions.value = res.data
  } catch (error) {
    console.error('获取标签列表失败:', error)
  }
}

const handleMarkingOk = async () => {
  try {
    markingSubmitLoading.value = true
    await markingFormRef.value.validate()
    await dataAssetsMarking(
      {
        tableId: currentTable.value.tableId,
        labelId: markingForm.value.labelId,
      },
      {},
    )
    message.success('打标成功')
    fetchTableData()
    emit('refresh')
    handleMarkingCancel()
  } catch (error) {
    console.error('打标失败:', error)
  } finally {
    markingSubmitLoading.value = false
  }
}

const handleMarkingCancel = () => {
  markingForm.value.labelId = undefined
  markingModalVisible.value = false
}

// 分页变化处理
const handleTableChange = (pag: any, filters: any, sorter: any) => {
  pagination.value.current = pag.current
  pagination.value.pageSize = pag.pageSize
  fetchTableData(pag.current, pag.pageSize)
}

// 查询按钮点击事件
const handleSearch = () => {
  pagination.value.current = 1
  fetchTableData()
}

// 重置按钮点击事件
const handleReset = () => {
  tableName.value = ''
  publishStatus.value = null
  searchForm.value.labelId = null
  selectLabel.value = {}
  selectedRowKeys.value = []
  pagination.value.current = 1
  fetchTableData()
}

// 表格行选择配置
const rowSelection = computed(() => ({
  selectedRowKeys: selectedRowKeys.value,
  onChange: (keys: string[]) => {
    selectedRowKeys.value = keys
  },
  getCheckboxProps: (record: any) => ({
    name: record.tableId,
  }),
}))

// 批量发布
const handleBatchRelease = async () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请选择要发布的数据表')
    return
  }

  try {
    batchLoading.value = true
    await releaseDataAssets({
      idList: selectedRowKeys.value,
      status: 1,
    })
    message.success('批量发布成功')
    selectedRowKeys.value = []
    handleSearch()
    emit('refresh')
  } catch (error) {
    console.error('批量发布失败:', error)
    message.error('批量发布失败')
  } finally {
    batchLoading.value = false
  }
}

// 批量下架
const handleBatchOffline = async () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请选择要下架的数据表')
    return
  }

  try {
    batchLoading.value = true
    await releaseDataAssets({
      idList: selectedRowKeys.value,
      status: 0,
    })
    message.success('批量下架成功')
    selectedRowKeys.value = []
    handleSearch()
    emit('refresh')
  } catch (error) {
    console.error('批量下架失败:', error)
    message.error('批量下架失败')
  } finally {
    batchLoading.value = false
  }
}

onMounted(() => {
  getDataAssetsLabelTree({}, {}).then((res) => {
    res.data.forEach((item: any) => {
      item.labelId = item.categoryId + '_' + item.categoryName
      item.labelName = item.categoryName
      if (!item?.children || item.children.length === 0) item.disabled = true
    })
    assetsLabelTree.value = res.data
  })
})

watch(
  () => props.databaseData,
  (newVal) => {
    if (newVal) {
      searchForm.value.labelId = null
      selectLabel.value = {}
      tableName.value = ''
      publishStatus.value = null
      const list = ['sqlserver', 'oracle', 'postgresql', 'greemplum', 'gbase', 'kingbase']
      if (list.includes(newVal.databaseType)) {
        if (!columns.value.find((item) => item.key === 'tableSchema')) {
          columns.value.unshift(tableSchemaCol)
        }
      } else {
        columns.value = columns.value.filter((item) => item.key !== 'tableSchema')
      }
      handleSearch()
    }
  },
  { immediate: true },
)
</script>

<style lang="less" scoped>
.database-table {
  height: 100%;
  .header-div {
    display: flex;
    align-items: center;
    height: 48px;
    padding: 0px 12px 0 20px;
    border-bottom: 1px solid #0000001a;
  }
  .content-div {
    height: calc(100% - 48px);
  }
}
.content-rl {
  padding: 0 12px;
}
.content-top {
  padding-top: 12px;
}
:deep(.fs-table .search-btn) {
  display: none;
}
:deep(.ant-btn:hover) {
  .clear-icon {
    opacity: 1 !important;
  }
}
</style>
