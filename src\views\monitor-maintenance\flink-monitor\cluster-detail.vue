<template>
  <div class="page-full">
    <a-flex justify="space-between" style="padding-bottom: 10px">
      <div class="title mb-20px" text="20px #000000d9">集群详情信息</div>
      <a-space :size="8">
        <a-button type="primary" @click="setProcessMonitorFn('start')">启动</a-button>
        <a-button type="primary" @click="setProcessMonitorFn('stop')">停止</a-button>
        <a-button type="primary" @click="setProcessMonitorFn('restart')">重启</a-button>
      </a-space>
    </a-flex>
    <div class="page-main" ref="pageMain">
      <div class="list-content">
        <a-descriptions layout="vertical" bordered size="small">
          <a-descriptions-item
            :label="key"
            :span="1"
            v-for="(value, key) of clusterlInfo"
            :key="key"
          >
            {{ value }}
          </a-descriptions-item>
        </a-descriptions>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref } from 'vue'
import {
  getClusterDetail,
  setProcessMonitor,
} from '@/api/services/monitor-maintenance/flink-monitor'
import { message } from 'ant-design-vue'

const clusterlInfo = ref<any>({})

// 获取列表数据
const getList = () => {
  getClusterDetail({}).then((res) => {
    clusterlInfo.value = res.data.obj
  })
}

// 设置集群状态
const setProcessMonitorFn = (type: string) => {
  setProcessMonitor({ type, params: { serverId: 'flink' } }).then((res: any) => {
    if (res.code === '000000') {
      message.success('操作成功')
      getList()
    } else {
      message.error('操作失败')
    }
  })
}

onMounted(() => {
  getList()
})
</script>

<style lang="less" scoped></style>
