<template>
  <div ref="container" />
</template>

<script setup lang="ts">
import { ref, watch, onMounted, onBeforeUnmount } from 'vue'
import * as monaco from 'monaco-editor'
import SQLSnippets from './core/snippets'
// @ts-ignore
import { language as sqlLanguage } from 'monaco-editor/esm/vs/basic-languages/sql/sql.js'
// @ts-ignore
import { language as shellLanguage } from 'monaco-editor/esm/vs/basic-languages/shell/shell.js'
// @ts-ignore
import { language as pythonLanguage } from 'monaco-editor/esm/vs/basic-languages/python/python.js'
import { toRaw } from 'vue'

// 1. Props
const props = withDefaults(
  defineProps<{
    options?: any
    onInputField?: Function
    onInputTableAlia?: Function
    onBlur?: Function
    customKeywords?: any[]
    dbs?: any[]
    triggerCharacters?: any
    value?: string
    theme?: string
    readonly?: boolean
    overviewBorder?: boolean
    minimap?: boolean
    isShowDb?: boolean
  }>(),
  {
    options: () => ({}),
    onInputField: () => {},
    onInputTableAlia: () => {},
    onBlur: () => {},
    customKeywords: () => [],
    dbs: () => [],
    triggerCharacters: () => [],
    value: '',
    theme: '',
    readonly: false,
    overviewBorder: true,
    minimap: true,
    isShowDb: false,
  },
)

// 2. Emits
const emit = defineEmits(['input'])

// 3. Refs
const container = ref<HTMLElement | null>(null)
const monacoEditor = ref<any>(null)
const sqlSnippets = ref<any>(null)

// 4. Watchers
watch(
  () => props.dbs,
  (val) => {
    sqlSnippets.value?.setDbSchema(val)
  },
  { deep: true },
)

watch(
  () => props.theme,
  () => {
    if (monacoEditor.value && props.theme) {
      monaco.editor.setTheme(props.theme)
    }
  },
  { immediate: true },
)

watch(
  () => props.readonly,
  () => {
    toRaw(monacoEditor.value).updateOptions({ readOnly: props.readonly })
  },
)

watch(
  () => props.options,
  (options) => {
    toRaw(monacoEditor.value).updateOptions(options)
  },
)

// 5. Methods
function setData(val: string) {
  toRaw(monacoEditor.value).setValue(val)
}

async function initEditor() {
  sqlSnippets.value = new SQLSnippets(
    monaco,
    props.customKeywords,
    props.onInputField,
    props.onInputTableAlia,
    props.dbs,
    props.isShowDb,
  )
  monacoEditor.value = monaco.editor.create(container.value as HTMLElement, {
    value: props.value,
    theme: props.theme || 'vs',
    contextmenu: false,
    automaticLayout: true,
    readOnly: props.readonly,
    suggestOnTriggerCharacters: true,
    overviewRulerBorder: props.overviewBorder,
    minimap: { enabled: props.minimap },
    fontSize: '16px',
    ...props.options,
  })
  monaco.languages.registerCompletionItemProvider(props.options.language, {
    triggerCharacters: [...props.triggerCharacters],
    provideCompletionItems: (model: any, position: any) =>
      sqlSnippets.value.provideCompletionItems(model, position, props.options.language),
  })
  monacoEditor.value.onDidChangeModelContent(() => {
    emit('input', toRaw(monacoEditor.value).getValue())
  })
}

// 6. Lifecycle
onMounted(async () => {
  await initEditor()
})

onBeforeUnmount(() => {
  // 销毁编辑器
  // monacoEditor.value?.dispose()
  // ...如有其它资源释放
})

defineExpose({
  setData,
})
</script>
