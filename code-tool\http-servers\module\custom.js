import { tableExample, formExample } from '../../custom-template/page-template-data.js'
import { getTableTamplate } from '../../custom-template/table-template.js'
import { getFormTamplate } from '../../custom-template/form-template.js'

const obj = {
  table: tableExample,
  form: formExample,
}

const templateObj = {
  table: getTableTamplate,
  form: getFormTamplate,
}

export function handleGetCoustomTemplate(req) {
  const { type = '' } = req.body
  const str = obj[type] || ''
  return { code: '000000', data: str }
}

export async function generationCoustomTemplate(req) {
  const { list = [], type = '' } = req.body
  const str = await templateObj[type](list)
  return { code: '000000', data: str }
}
