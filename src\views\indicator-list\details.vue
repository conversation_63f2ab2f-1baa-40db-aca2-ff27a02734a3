<script setup lang="ts">
import TableComponent from '@/views/table-component/table-component.vue'
import { getIndexDataDtl } from '@/api/indexmanage/indexmanage/indexmanage'
import { VAceEditor } from 'vue3-ace-editor'
import {
  getDataReview,
  getGenerateSql,
  getSqlDateBaseColumnParamsInfoList,
} from '@/api/indexmanage/datasourceoptions/datasourceoptions'
import { logModal } from '@/utils/log-modal'
import { message } from 'ant-design-vue'
import { tranlateDataToParams, translateParamsToData } from '../indicator-config/indicator-config'
import type { PreviewData, IndexListItem, ServerFieldItem } from '@/api/services/indicator/type.ts'
import { ColumnType } from '@/api/services/indicator/type'
import type { FilterItem } from '@/components/filter/type'
import type { WhereItem } from '@/api/services/indicator/type.ts'
import type { Dayjs } from 'dayjs'
import dayjs from 'dayjs'

const activeKey = ref('1')
const details: any = ref({})
const tableComponentRef = ref()
const tableLoading = ref(true)
const previewData = ref<PreviewData>()
const ruleText: any = ref()
const props = defineProps<{
  formId: string
  detailData?: IndexListItem
  previewType: number
  previewSql?: string
  databaseName?: string
}>()

export type FieldType =
  | 'linkUrl'
  | 'linkText'
  | 'field'
  | 'type'
  | 'selectedDateFormate'
  | 'displayTime'
  | 'timeFormate'
  | 'numberStyle'
  | 'decimalStyle'
  | 'decimalNum'
  | 'multiple'
  | 'startStr'
  | 'endStr'

//  设置字段排序
const fieldSort = (fieldName: string, sort: string) => {
  let rule = JSON.parse(ruleText.value)
  console.log('==========fieldName', fieldName, sort, rule)
  // 取最后一个字段进行操作
  const newRule = rule.map((item: any, index: number) => {
    if (index === rule.length - 1) {
      // 取最后一个对象操作
      const obj = {
        tableName: item.tableName,
        fieldName: fieldName,
        sort: sort,
      }
      if (item.order && item.order.length) {
        const existingIndex = item.order.findIndex((item: any) => item.fieldName === fieldName)
        if (existingIndex !== -1) {
          item.order[existingIndex].sort = sort
        } else {
          item.order.push({
            tableName: item.tableName,
            fieldName: fieldName,
            sort: sort,
          })
        }
      } else {
        item.order = [obj]
      }
    }
    return item
  })
  console.log('newRule', newRule)

  ruleText.value = JSON.stringify(newRule)
  getData()
}
provide('fieldSort', fieldSort)

// 设置文本、链接、按钮
const setField = (field: FieldType, fieldName: string, fieldValue: string) => {
  const _preview = JSON.parse(JSON.stringify(previewData.value))
  // 判断是要改数字还是日期的展示形式
  // console.log('preview.value 设置字段', field, fieldName, fieldValue)
  _preview.titleList.forEach((item: any) => {
    if (item.dataIndex === fieldName) {
      item[field] = fieldValue
    }
  })
  previewData.value = _preview
}

provide('setField', setField)

//  隐藏字段
const hideField = (fieldName: string) => {
  let _preview = JSON.parse(JSON.stringify(previewData.value))
  _preview.titleList.forEach((item: any) => {
    if (item.dataIndex === fieldName) {
      item.isShow = false
    }
  })
  console.log('preview.value 隐藏字段', previewData.value, tableComponentRef.value)
  previewData.value = _preview
  // tableComponentRef.value?.setData(_preview)
}

provide('hideField', hideField)

const getData = async () => {
  try {
    if (!props.detailData) return
    tableLoading.value = true
    const res = await getDataReview({
      dataSourceId: Number.parseInt(props.detailData!.dataSourceId!),
      dynamicParam: props.detailData!.dynamic === 1 ? props.detailData!.dynamicParam : void 0,
      queryJson: ruleText.value,
      previewType: props.previewType,
      previewSql: props.previewSql,
      databaseName: props.databaseName,
    }).catch((err) => {
      const errMsg = `${err.data.msg.slice(0, 100)}...`
      message.destroy()
      message.error(errMsg)
    })
    if (res && res.code === '000000') {
      console.log('preview.value 接口getData', res.data)
      previewData.value = res.data
    }
  } catch (error) {
    console.error(error)
  } finally {
    tableLoading.value = false
  }
}
const formatDate2Str = (data: any, tar: WhereItem) => {
  const formatter = (dayjs: Dayjs) => {
    return dayjs.format('YYYY-MM-DD HH:mm:ss')
  }
  let res = data.value
  if (Array.isArray(res)) {
    if (dayjs.isDayjs(res[0])) {
      res = [formatter(res[0]), formatter(res[1])]
      tar.minValue = res[0]
      tar.maxValue = res[1]
    }
  } else if (dayjs.isDayjs(res)) {
    res = formatter(res)
    tar.value = res
  }
  tar.value = res
}
const onFilterConfirm = async (list: FilterItem[]) => {
  const rules = JSON.parse(ruleText.value) as Array<any>
  const rule = rules[rules.length - 1]
  // 将 list 转为 WhereItem[]
  const whereItems: WhereItem[] = list.map((item: FilterItem) => {
    const obj: WhereItem = {
      tableName: rule.tableName,
      fieldName: item.field,
      operator: item.condition,
      value: item.value,
      fieldType: item.columnType,
    }
    if (item.columnType === ColumnType.DATE) {
      formatDate2Str(item, obj)
    }
    return obj
  })
  console.log('WHERE', whereItems)
  rule.where = whereItems
  ruleText.value = JSON.stringify(rules)
  await getData()
}
const filterData = ref<ServerFieldItem[]>([])

onMounted(async () => {
  const basicRuleList = props.detailData?.basicRuleList
  if (!basicRuleList || basicRuleList.length === 0) return
  // console.log('basicRuleList', basicRuleList[0].ruleText)
  ruleText.value = genQueryJson(basicRuleList[0].ruleText)
  // queryFilterData()
  getData()
  if (props.detailData?.autoId) {
    const { data } = await getIndexDataDtl({ autoId: props.detailData?.autoId })
    details.value = data
  }
})
const genQueryJson = (ruleText: any): string => {
  const list = JSON.parse(ruleText)
  if (!Array.isArray(list)) return ruleText
  // const editRuleList = list.map((it: any) => {
  //   const ruleText = JSON.parse(it.ruleText)
  //   return ruleText
  // })
  const editDataList = translateParamsToData(list)
  const params = tranlateDataToParams(editDataList)
  return JSON.stringify(params)
}

const loadinSQLContent = ref(false)
const viewSql = () => {
  if (props.previewType === 2) {
    // 自定义SQL模式下，直接显示SQL
    showSqlEditor.value = true
    content.value = props.previewSql || ''
    return
  }
  loadinSQLContent.value = true
  getGenerateSql({
    dataSourceId: Number.parseInt(props.detailData!.dataSourceId!),
    queryJson: ruleText.value,
  })
    .then((res: any) => {
      if (res.code === '000000') {
        // logModal(res.data.generateSql, '生成SQL')
        content.value = res.data.generateSql
        showSqlEditor.value = true
      }
    })
    .catch((err) => {
      console.error(err)
      message.error(err.data.msg)
    })
    .finally(() => {
      loadinSQLContent.value = false
    })
}

const getLabel = (key: string, options?: any[]) => {
  if (options?.length) {
    return options.find((item) => item.value === details.value[key])?.label || '-'
  }
  return details.value[key] || ''
}

const handleTabChange = (val: any) => {
  // if (val === '2') {
  //   setTimeout(() => {
  //     tableComponentRef.value?.setData(preview.value)
  //   }, 0)
  // }
}

const showSqlEditor = ref(false)
const content = ref('')
const language = ref('sql')
const theme = ref('github')
const editorOptions = ref({
  fontSize: '14px', // 设置字体大小
  showPrintMargin: false, // 是否显示打印边距
  enableLiveAutocompletion: true, // 启用实时自动补全功能
})
const closeSQLEditor = () => {
  showSqlEditor.value = false
}
</script>

<template>
  <a-tabs v-model:activeKey="activeKey" @change="handleTabChange">
    <a-tab-pane key="1" tab="指标详情">
      <div style="max-height: 500px; overflow: auto">
        <a-form
          name="basic"
          :label-col="{ span: 3 }"
          :wrapper-col="{ span: 16 }"
          autocomplete="off"
          labelAlign="left"
        >
          <a-form-item label="指标名称" name="indexName">
            {{ getLabel('indexName') }}
          </a-form-item>
          <!-- <a-form-item label="计算公式/规则逻辑" name="calFormula">
            {{ getLabel('calFormula') }}
          </a-form-item> -->
          <a-form-item label="指标配置数据" name="formData" v-if="getLabel('formData')">
            <!-- <a href="javascript:void(0)" @click="handlePreview(getLabel('formData'))">{{
                getLabel('formData')
              }}</a> -->
            <div v-for="(item, key) in JSON.parse(getLabel('formData'))" :key="key">
              <span v-if="item.type !== 'select' || typeof item.value === 'string'"
                >{{ item.title }}：{{ item.value }}</span
              >
              <span v-else>
                {{ item.title }}：
                {{
                  item.value
                    ?.map(
                      (val: any) => item.options.find((option: any) => option.value === val)?.label,
                    )
                    .join('，')
                }}
              </span>
            </div>
          </a-form-item>
          <a-form-item label="指标计算规则数据表" name="basicRuleList">
            <!-- <a class="log-text" href="javascript:void(0)" @click="viewSql()">查看SQL</a> -->
            <a-button type="link" @click="viewSql()" :loading="loadinSQLContent">查看SQL</a-button>
          </a-form-item>
          <a-form-item label="指标配置模板版本id" name="formHistoryId">
            {{ getLabel('formHistoryId') }}
          </a-form-item>
          <a-form-item label="数据源编号" name="dataSourceId">
            {{ getLabel('dataSourceId') }}
          </a-form-item>
          <a-form-item label="是否启用" name="isDeleted">
            {{
              getLabel('isDeleted', [
                { label: '是', value: 1 },
                { label: '否', value: 0 },
              ])
            }}
          </a-form-item>
          <a-form-item label="是否已发布" name="isRelease">
            {{
              getLabel('isDelisReleaseted', [
                { label: '已发布', value: 1 },
                { label: '未发布', value: 0 },
              ])
            }}
          </a-form-item>
          <a-form-item label="备注" name="remarks">
            {{ getLabel('remarks') }}
          </a-form-item>
          <a-form-item label="当前版本号" name="currentVersion">
            {{ getLabel('currentVersion') }}
          </a-form-item>
          <a-form-item label="创建人名称" name="remarks">
            {{ getLabel('createUsername') }}
          </a-form-item>
          <a-form-item label="创建时间" name="remarks">
            {{ getLabel('createTime') }}
          </a-form-item>
        </a-form>
      </div>
    </a-tab-pane>
    <a-tab-pane key="2" tab="预览指标结果">
      <TableComponent
        ref="tableComponentRef"
        :data="previewData"
        :request-data="onFilterConfirm"
        :loading="tableLoading"
        :filter-data="filterData"
      />
    </a-tab-pane>
  </a-tabs>
  <a-modal
    v-model:visible="showSqlEditor"
    title="查看SQL"
    :width="800"
    :bodyStyle="{ height: '60vh' }"
    @ok="closeSQLEditor"
    @cancel="closeSQLEditor"
  >
    <VAceEditor
      :value="content"
      :lang="language"
      :theme="theme"
      :options="editorOptions"
      readonly
      style="height: 100%; width: 100%"
    />
  </a-modal>
</template>
<style lang="less">
// .full-modal {
//   .ant-modal {
//     max-width: 100%;
//     top: 0;
//     padding-bottom: 0;
//     margin: 0;
//   }
//   .ant-modal-content {
//     display: flex;
//     flex-direction: column;
//     height: calc(100vh);
//   }
//   .ant-modal-body {
//     flex: 1;
//   }
// }
</style>
@/utils/log-modal @/api/services/indicator/type
