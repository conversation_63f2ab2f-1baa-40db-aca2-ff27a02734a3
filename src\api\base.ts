export const PREFIX = 'api'
export const AUTHORITYCENTER = localStorage.getItem('SYSTEM_CODE') || 'new-bigData'
export const PROJECT_AUTHORITYCENTER = `/api/301UC`
export const LOGIN_PROJECT_AUTHORITYCENTER = `/api/301UC/loginManager`
export const SYSTEM_CODE = `${AUTHORITYCENTER}`
export const QMUCENTER = `/qmUcenter`
export const PUBLICKEY = `MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDg3/t1twW8BvmdWXMvDyaKot2L eb5BX75mBi6cxkxBDUx5SCnARdWPUkdqpBIBMD6xgkwA3LuWFGWHQl4nou+p2no4 dC0wAJB3w9sznvaah3Lf9z3iB9+zis8mT4N57aK7zHBQ4J725MJDtLrepi8MSa7f poDEB5BZpeXSw6a4FQIDAQAB`
export const ASSETMANAGER = `/assetManager`
export const ctoken = `bigfish_ctoken_19h4g5f99b`
export const SODATAFLINK = `/api/sodataFlink`
export const SODATAFLINK_HBASE = `/sodataFlink`
