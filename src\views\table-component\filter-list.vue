<template>
  <div class="filter-list" v-if="filterList.length">
    <div class="filter-item" color="#e3e3ef" v-for="(item, index) in filterList" :key="index">
      {{ item.label }}
      <CloseOutlined style="font-size: 12px; margin-top: -5px" @click="deleteItem(index)" />
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref } from 'vue'
import { CloseOutlined } from '@ant-design/icons-vue'
const filterList: any = ref([])

const deleteItem = (index: number) => {
  filterList.value.splice(index, 1)
}
</script>

<style lang="less" scoped>
.filter-list {
  width: 100%;
  height: fit-content;
  padding: 10px 0;
  background-color: #fff;
  border-bottom: 1px solid #eeecec;
  display: flex;
  margin-top: 10px;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
  .filter-item {
    background-color: #e3e3ef;
    color: #747cb7;
    padding: 0px 16px;
    border-radius: 10px;
    height: 24px;
    line-height: 24px;
    cursor: pointer;
    font-weight: 700;
  }
}
</style>
