<script lang="ts" setup>
import { ref } from 'vue'
import ResourceManage from './resource/index.vue'
import JobFlowService from './job-flow-service/index.vue'

const activeTabKey = ref('1')
function onTabChange(key: string) {
  activeTabKey.value = key
}
</script>

<template>
  <div class="service-config-container">
    <a-tabs :active-key="activeTabKey" @change="onTabChange">
      <a-tab-pane key="1" tab="资源服务">
        <ResourceManage />
      </a-tab-pane>
      <a-tab-pane key="2" tab="作业流服务">
        <JobFlowService />
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<style lang="less" scoped>
.service-config-container {
  width: 100%;
  height: 100%;
  background-color: #fff;
}
</style>
