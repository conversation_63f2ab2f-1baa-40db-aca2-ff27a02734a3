const sql = [
  `CREATE FUNCTION GetPatientFinanceInfo()
RETURNS TABLE 
AS
RETURN
(
    SELECT 
        HIS_PATIENT_INFO.HIS_PATIENT_INFO AS PatientInfo,
        FIN_PAYMENT_RECORD.FIN_PAYMENT_RECORD AS PaymentRecord,
        FIN_CHARGE_DETAIL.FIN_CHARGE_DETAIL AS ChargeDetail,
        BANK_CARD_INFO.BANK_CARD_INFO AS BankCardInfo
    FROM HIS_PATIENT_INFO
    LEFT JOIN FIN_PAYMENT_RECORD ON HIS_PATIENT_INFO.HIS_PATIENT_INFO = FIN_PAYMENT_RECORD.FIN_PAYMENT_RECORD
    LEFT JOIN FIN_CHARGE_DETAIL ON HIS_PATIENT_INFO.HIS_PATIENT_INFO = FIN_CHARGE_DETAIL.FIN_CHARGE_DETAIL
    LEFT JOIN BANK_CARD_INFO ON HIS_PATIENT_INFO.HIS_PATIENT_INFO = BANK_CARD_INFO.BANK_CARD_INFO
);`,
  `CREATE FUNCTION GetPatientFinanceInfo()
RETURNS TABLE 
AS
RETURN
(
    SELECT 
        HIS_PATIENT_INFO.HIS_PATIENT_INFO AS PatientInfo,
        FIN_PAYMENT_RECORD.FIN_PAYMENT_RECORD AS PaymentRecord,
        FIN_CHARGE_DETAIL.FIN_CHARGE_DETAIL AS ChargeDetail,
        BANK_CARD_INFO.BANK_CARD_INFO AS BankCardInfo
    FROM HIS_PATIENT_INFO
    LEFT JOIN FIN_PAYMENT_RECORD ON HIS_PATIENT_INFO.HIS_PATIENT_INFO = FIN_PAYMENT_RECORD.FIN_PAYMENT_RECORD
    LEFT JOIN FIN_CHARGE_DETAIL ON HIS_PATIENT_INFO.HIS_PATIENT_INFO = FIN_CHARGE_DETAIL.FIN_CHARGE_DETAIL
    LEFT JOIN BANK_CARD_INFO ON HIS_PATIENT_INFO.HIS_PATIENT_INFO = BANK_CARD_INFO.BANK_CARD_INFO
);
`,
  `CREATE FUNCTION GetMedicalTestsAndReports()
RETURNS TABLE 
AS
RETURN
(
    SELECT 
        LIS_TEST_ITEM.LIS_TEST_ITEM AS TestItem,
        LIS_TEST_RESULT.LIS_TEST_RESULT AS TestResult,
        PACS_IMAGE_REPORT.PACS_IMAGE_REPORT AS ImageReport,
        EMR_BASIC_RECORD.EMR_BASIC_RECORD AS EMRRecord
    FROM LIS_TEST_ITEM
    LEFT JOIN LIS_TEST_RESULT ON LIS_TEST_ITEM.LIS_TEST_ITEM = LIS_TEST_RESULT.LIS_TEST_RESULT
    LEFT JOIN PACS_IMAGE_REPORT ON LIS_TEST_ITEM.LIS_TEST_ITEM = PACS_IMAGE_REPORT.PACS_IMAGE_REPORT
    LEFT JOIN EMR_BASIC_RECORD ON LIS_TEST_ITEM.LIS_TEST_ITEM = EMR_BASIC_RECORD.EMR_BASIC_RECORD
);

`,
]
export default function () {
  // 随机 3以内的数字
  return sql[Math.floor(Math.random() * 3)]
}

export const fsOptionsDefault = {
  submitBtn: { show: false, innerText: '提交' },
  form: {
    inline: false,
    hideRequiredAsterisk: false,
    labelPosition: 'right',
    size: 'default',
    labelWidth: '125px',
    showMessage: false,
  },
  language: {},
  resetBtn: { show: false, innerText: '重置' },
}
