import { DatabaseType } from '@/enums/database-type'
import { message } from 'ant-design-vue'

/**
 * 创建uuid
 * @param n
 */
export const createUuid = (n = 36) => {
  const str = 'abcdefghijklmnopqrstuvwxyz0123456789'
  let result = ''
  for (let i = 0; i < n; i++) {
    result += str[parseInt(Math.random() * str.length + '')]
  }
  return result
}

// eslint-disable-next-line no-unused-vars
export function useDebounce<T extends (...args: any[]) => any>(callback: T, delay: number) {
  let timeoutId: number

  return function (this: ThisParameterType<T>, ...args: Parameters<T>) {
    clearTimeout(timeoutId)
    //@ts-ignore
    timeoutId = setTimeout(() => {
      callback.apply(this, args)
    }, delay)
  }
}

export const loadImage = (imageName: string) => {
  return new URL(`../assets/image/${imageName}`, import.meta.url).href
}

export const loadImagePath = (imageName: string, path: string) => {
  return new URL(`../assets${path ? '/' + path : ''}/${imageName}`, import.meta.url).href
}

export function getRegexVal(modelValue: string, varRegex: RegExp) {
  const res = modelValue.match(varRegex)?.map((it) => it.trim())
  return res?.length ? [...new Set(res)] : []
}

export function extractFuncNames(jsonString: string): string[] {
  const regex = /"func_name"\s*:\s*"([^"]+)"/g
  const funcNames: string[] = []
  let match

  while ((match = regex.exec(jsonString)) !== null) {
    funcNames.push(match[1])
  }

  return funcNames
}

export function parseVarNameAndType(
  v: string,
  names: string[],
  restOption = true,
  varType?: string,
) {
  let regStr = ''
  names.forEach((it, i) => {
    regStr +=
      i == 0
        ? `${it}([\\w]*)`
        : `(${restOption ? '?:' : ''},${it}([\\w\\<\\>]*))${restOption ? '?' : ''}`
  })
  const res = getRegexVal(v, new RegExp(regStr, 'g'))
  return parseNameAndType1(res, varType, ...names.map((it) => it.length))
}

export function parseNameAndType1(list: string[], varTpye?: string, ...names: number[]) {
  return list.map((it) => {
    const [name, type] = it.split(',').filter((it) => it)
    const text = name.slice(names[0])
    const label = `${text}${varTpye ? '-' + varTpye : ''}${type ? `(${type.replace('=', ':')})` : ''}`
    const dataType = type && type.slice(names[1])
    return { label, insertText: text, dataType, type: varTpye === 'sql' ? 'normal' : 'specil' }
  })
}
/**
 * 提取类似 compareAndSet(String name,long expect, long update) 这样的字符串中的参数信息
 * @param inputString
 * @returns
 */
export function extractRedisParams(inputString: string): { type: string; name: string }[] {
  const parenthesesPattern = /\(([^)]+)\)/g

  let match
  let content = ''

  while ((match = parenthesesPattern.exec(inputString)) !== null) {
    content = match[1]
  }
  const params = content.split(',').map((v) => {
    const temp = v.trim().split(' ')
    return { name: temp[1], type: temp[0] }
  })

  return params
}
export function parseParamsWithName(modelValue: string, name: string) {
  const regexp = new RegExp(`${name}\\(([.\\s\\S]*?)\\)`, 'g')
  const repRegexp = new RegExp(`${name}\\(`, 'g')
  return getRegexVal(modelValue, regexp)
    .map((it) =>
      it
        .replace(repRegexp, '')
        .replace(/\)/g, '')
        .split(/[\r\n]/g)
        .map((it) => it.trim())
        .filter((it) => it),
    )
    .flat()
}

export function parseNameAndType(list: string[], num1 = 5, num2 = 5) {
  return list.map((it) => {
    const [name, type] = it.split(',').filter((it) => it)
    return { label: name.slice(num1), dataType: type && type.slice(num2), type: 'specil' }
  })
}

// eslint-disable-next-line no-unused-vars
export function uniqueArray<T>(array: T[], findCallback: (item: T, current: T) => boolean) {
  return array.reduce((acc: T[], current) => {
    const x = acc.find((item: T) => findCallback(item, current))
    if (!x) {
      return acc.concat(current)
    } else {
      return acc
    }
  }, [])
}

export function getDuplicates(arr: any[], key: string) {
  const repeatMatch: Record<string, any> = {}
  arr.forEach((item) => {
    repeatMatch[item[key]] = (repeatMatch[item[key]] || 0) + 1
  })
  return arr.filter((obj) => repeatMatch[obj[key]] > 1)
}

export function parseParamsWithResult(
  modelValue: string,
  name: string,
  str: string,
  isId: boolean,
) {
  let regexPattern = new RegExp(`${name}="([^"]+)${str}"|${name}=([^]+)${str}`)
  if (isId) regexPattern = new RegExp(`${name}="([^,"]+)"${str}|${name}=([^,]+)${str}`)
  const matches = modelValue.match(regexPattern)
  if (matches) {
    const startingLine = matches[1] || matches[2]
    return {
      isString: matches[1] ? true : false,
      value: startingLine,
    }
  }
  return {}
}

export function isNullOrUndefined(value: any): boolean {
  return value === null || value === undefined
}

export function escapeRegExp(text: string) {
  if (text) {
    return text.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&')
  }
  return ''
}

/**
 * URL 参数转换
 */
export function objectToUrlParams(obj: Record<string, any>) {
  return Object.keys(obj)
    .map((key) => {
      return encodeURIComponent(key) + '=' + encodeURIComponent(obj[key])
    })
    .join('&')
}

export function newreplaceExceptInsideQuotes(input: string, search: any, replacement: any) {
  // 使用正则表达式进行替换
  const regex = new RegExp(`("\\b[^"]*\\b")|\\b(${search})\\b`, 'g')
  // 执行替换操作
  return input.replace(regex, (match: any, quoted: any) => {
    // 如果匹配到了被双引号包裹的字符串，则不进行替换
    if (quoted) {
      return quoted
    } else {
      return replacement
    }
  })
}

// 生成一定数量的随机数
export function generateUniqueRandomNumbers(min: number, max: number, count: number) {
  const uniqueNumbers = new Set()
  while (uniqueNumbers.size < count) {
    const randomNumber = Math.floor(Math.random() * (max - min + 1)) + min
    uniqueNumbers.add(randomNumber)
  }
  return Array.from(uniqueNumbers)
}

// exportFile公共方法
/**
 * js导出文件
 * @param content
 * @param filename
 */
export function exportFile(content: string | ArrayBuffer | Blob, filename: string) {
  // 字符内容转变成blob地址
  const blob = new Blob([content])
  if ('msSaveOrOpenBlob' in navigator) {
    // IE导出
    ;(window.navigator as any).msSaveOrOpenBlob(blob, filename)
  } else {
    // 创建隐藏的可下载链接
    const eleLink = document.createElement('a')
    eleLink.download = filename
    eleLink.style.display = 'none'
    eleLink.href = URL.createObjectURL(blob)
    // 触发点击
    document.body.appendChild(eleLink)
    eleLink.click()
    // 然后移除
    document.body.removeChild(eleLink)
  }
}

/**
 * 获取文件名称
 * @param contentPosition
 */
export function getFileName(contentPosition: string) {
  let fileName = contentPosition.split(';')[1].split('filename=')[1]
  fileName = decodeURIComponent(fileName)
  return fileName
}

/**
 * 获取文件类型
 * @param contentPosition
 */
export function getFileType(fileName: string) {
  const parts = fileName.split('.')
  const extension: string = parts[parts.length - 1] // 获取最后一个点后面的部分
  return extension.toLowerCase()
}

// 支持预览的文件类型
export const supportFileTypes = ['png', 'jpg', 'txt', 'docx', 'pdf', 'mov', 'mp4', 'xlsx', 'xls']

export function getTopDomain() {
  const domain = document.domain
  return /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/.test(
    domain,
  )
    ? domain
    : domain.split('.').slice(-2).join('.')
}

/**
 * 展平树结构数据
 * @param tree
 * @param childKey
 * @param list
 */
export function flattenTree<T extends { [x: string]: any } = any>(
  tree: T[],
  childKey = 'children',
  list: any = [],
): T[] {
  tree.forEach((i) => {
    list.push(i)
    if (i[childKey]) {
      flattenTree(i[childKey], childKey, list)
    }
  })
  return list
}

/**
 * 计算百分数，防止出现两位小数 * 100出现小数失真的问题（例如：0.58 * 100 = 57.9999999）
 * @param num 数值
 * @param fixed 小数位数
 * return 百分数
 */
export function formatPercent(num: number, fixed = 2) {
  if (num) {
    return parseFloat((num * 100).toFixed(fixed))
  } else {
    return 0
  }
}

/**
 * 根据数据库类型获取图标
 * @param dbType 数据库类型（字符串或数字）
 * @returns 图标路径或undefined
 */
export function getDbIcon(dbType: string | number | undefined): string | undefined {
  if (!dbType) return undefined

  // 统一处理字符串和数字类型
  const key = typeof dbType === 'string' ? dbType.toLowerCase() : dbType
  const path = DatabaseType.getBy(key)?.iconPath
  return path
}

export async function copyText(text?: string) {
  if (!text) return
  try {
    await navigator.clipboard.writeText(text)
    message.success('复制成功')
  } catch (error) {
    logPlus.red('复制失败', error)
    message.error('复制失败')
  }
}
