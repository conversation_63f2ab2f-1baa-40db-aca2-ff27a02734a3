import Mock from 'mockjs'
import moment from 'moment'

// js保留字和关键字
const reservedWords = [
  'break',
  'case',
  'catch',
  'class',
  'const',
  'continue',
  'debugger',
  'default',
  'delete',
  'do',
  'else',
  'export',
  'extends',
  'finally',
  'for',
  'function',
  'if',
  'import',
  'in',
  'instanceof',
  'let',
  'new',
  'return',
  'super',
  'switch',
  'this',
  'throw',
  'try',
  'typeof',
  'var',
  'void',
  'while',
  'with',
  'yield',
]

/**
 * 防抖函数，用于限制函数的执行频率
 * @param {Function} func - 要执行的函数
 * @param {number} wait - 等待时间，单位为毫秒
 * @param {boolean} [immediate=false] - 是否立即执行函数
 * @returns {Function} - 经过防抖处理后的函数
 */
export function debounce(func, wait, immediate = false) {
  let timeout = null

  return function (...args) {
    const context = this

    const later = function () {
      timeout = null
      if (!immediate) func.apply(context, args)
    }

    const callNow = immediate && !timeout

    if (timeout) clearTimeout(timeout)
    timeout = setTimeout(later, wait)

    if (callNow) func.apply(context, args)
  }
}

/**
 * 根据给定的值确定请求类型
 * @param {string} val - 输入的请求参数类型
 * @returns {string} 返回对应的请求类型
 *
 * @description
 * 该函数接受一个字符串参数,根据不同的输入值返回对应的请求类型:
 * - 'queryParam': 返回 'query'
 * - 'bodyParam': 返回 'body'
 * - 'formParam': 返回 'form'
 * - 'headerParam': 返回 'header'
 * - 'pathParam': 返回 'path'
 * - 'binaryMessage': 返回 'binary'
 * - 'textMessage': 返回 'text'
 * - 其他情况: 返回 'unknown'
 */
function determineRequestType(val) {
  switch (val) {
    case 'queryParam':
      return 'query'
    case 'bodyParam':
      return 'body'
    case 'formParam':
      return 'form'
    case 'headerParam':
      return 'header'
    case 'pathParam':
      return 'path'
    case 'binaryMessage':
      return 'binary'
    case 'textMessage':
      return 'text'
    default:
      return 'unknown'
  }
}

/**
 * 根据给定的值返回对应的参数类型名称。
 *
 * @param {string} val - 表示参数类型的字符串值。
 * @returns {string} 返回对应的参数类型名称。
 *
 * @description
 * 此函数接受一个字符串值，并根据预定义的映射关系返回相应的参数类型名称。
 * 如果输入值不在预定义的映射中，函数将返回输入值本身。
 *
 * @example
 * console.log(paramTypeName('1')); // 输出: "number"
 * console.log(paramTypeName('3')); // 输出: "boolean"
 * console.log(paramTypeName('unknown')); // 输出: "unknown"
 */
function paramTypeName(val) {
  const typeMap = {
    0: 'string',
    1: 'number',
    2: 'Float',
    3: 'boolean',
    4: 'Object',
    5: 'array',
    6: 'number',
    7: 'number',
    9: 'string',
    10: 'string',
    11: 'string',
    12: 'string',
    15: 'File',
    16: 'Object',
  }

  return typeMap[val] || val // 如果没有匹配的值，返回原始值
}

/**
 * 从对象数组中移除具有重复键属性值的对象，保留第一个出现的对象。
 *
 * @param {Array} arr - 要处理的对象数组。
 * @param {string} keyProperty - 用于确定唯一性的对象属性名。
 * @returns {Array} 返回一个新数组，包含唯一对象。
 *
 * @description
 * 此函数遍历输入数组，使用指定的键属性来确定对象的唯一性。
 * 它会保留每个唯一键值的第一个对象，丢弃后续具有相同键值的对象。
 *
 * @example
 * const arr = [
 *   { id: 1, name: '张三' },
 *   { id: 2, name: '李四' },
 *   { id: 1, name: '王五' }
 * ];
 * const result = uniqueArrayObjects(arr, 'id');
 * console.log(result);
 * // 输出: [{ id: 1, name: '张三' }, { id: 2, name: '李四' }]
 */
function uniqueArrayObjects(arr, keyProperty) {
  const uniqueSet = new Set()
  const result = []
  for (const item of arr) {
    const keyValue = item[keyProperty]
    if (!uniqueSet.has(keyValue)) {
      uniqueSet.add(keyValue)
      result.push(item)
    }
  }
  return result
}

/**
 * 将嵌套的数据结构扁平化
 * @param {Object} data - 包含嵌套数组的数据对象
 * @returns {Array} 扁平化后的数据数组
 **/
export function flattenData(data) {
  const flattened = []
  for (const key in data) {
    if (Array.isArray(data[key]) && key !== 'headerParam') {
      const type = determineRequestType(key)
      data[key].forEach((item) => {
        if (item.children && item.children.length > 0) {
          getChildrenData(item, key)
          flattened.push({ ...item })
        } else {
          flattened.push({ ...item, queryType: type, paramTypeName: paramTypeName(item.paramType) })
        }
      })
    }
  }
  return uniqueArrayObjects(flattened, 'paramName')
}

function getChildrenData(item, key) {
  const type = determineRequestType(key)
  item.queryType = type
  item.paramTypeName = paramTypeName(item.paramType)
  if (item.children && item.children.length > 0) {
    item.children.forEach((val) => {
      getChildrenData(val, key)
    })
  }
}

/**
 * 将嵌套的数据结构扁平化
 * @param {Object} data - 包含嵌套数组的数据对象
 * @returns {Array} 扁平化后的数据数组
 **/
export function flattenDataHeader(data) {
  const flattened = []
  for (const key in data) {
    if (Array.isArray(data[key]) && key === 'headerParam') {
      const type = determineRequestType(key)
      data[key].forEach((item) => {
        if (item.children && item.children.length > 0) {
          const flattenedChildren = flattenData({ children: item.children })
          flattened.push(...flattenedChildren)
        } else {
          flattened.push({ ...item, queryType: type, paramTypeName: paramTypeName(item.paramType) })
        }
      })
    }
  }
  return uniqueArrayObjects(flattened, 'paramName')
}

/**
 * 获取字符串中最后一个点号后的字符
 * @param {string} str - 输入的字符串
 * @returns {string|null} 最后一个点号后的字符，如果没有点号或点号后没有字符则返回null
 */

/**
 * 获取字符串中最后一个点号后的所有字符。
 *
 * @param {string} str - 要处理的输入字符串。
 * @returns {string|null} 如果找到点号且点号后有字符，则返回点号后的所有字符；否则返回 null。
 *
 * @example
 * // 返回 "js"
 * getLastCharAfterDot("file.name.js");
 *
 * @example
 * // 返回 null
 * getLastCharAfterDot("no-dot-string");
 */
export function getLastCharAfterDot(str) {
  if (!str) return 'unknown'
  const dotIndex = str.lastIndexOf('.')
  if (dotIndex !== -1 && dotIndex < str.length - 1) {
    let typeName = str.substring(dotIndex + 1).toLocaleLowerCase()
    if (['integer', 'double', 'long'].includes(typeName)) {
      typeName = 'number'
    }
    if (['date', 'bigdecimal'].includes(typeName)) {
      typeName = 'string'
    }
    return typeName
  }
  return null
}

/**
 * 根据输入的列表过滤出需要得接口信息。
 * @param {Array} list - 输入的列表
 * @returns {Array} - 包含接口信息的数组
 */
export function getInterfaceInfos(list) {
  if (list.length === 0) return []
  // 使用 map 方法遍历输入的列表
  return list.map((val) => {
    const interfaceName = extractAfterSlash(val.fullPath)
    // 对每个元素进行转换，返回一个新的对象
    return {
      interfaceName: interfaceName,
      interfaceNameCn: val.interfaceName,
      description: val.description,
      fullPath: val.fullPath, // 获取 val 对象的 fullPath 属性
      httpMethodName: val.httpMethodName, // 获取 val 对象的 httpMethodName 属性
      // outResults: val.outResults, // 获取 val 对象的 outResults 属性
      // inParamModelData: val.inParamModelData, // 获取 val 对象的 inParamModelData 属性
      params: getParamFunc(val.inParamModelData),
      headers: getHeaderFunc(val.inParamModelData),
      response: getReturnFunc(val?.mockReturnResultExample),
    }
  })
}

/**
 * 从给定的输入参数模型数据中提取请求头参数信息。
 *
 * @param {Object} inParamModelData - 包含请求头参数信息的输入参数模型数据。
 * @returns {Array} - 包含请求头参数信息的数组。
 *
 * @description
 * 这个函数用于从给定的输入参数模型数据中提取请求头参数信息。它首先将输入数据扁平化，然后遍历扁平化后的数据，提取每个请求头参数的名称、类型、描述和是否必需的信息。最后，它将这些信息组成一个数组并返回。
 *
 * @example
 * const inParamModelData = {
 *   headerParam: [
 *     { paramName: 'Authorization', paramTypeName: 'string', description: '认证令牌', checkType: true },
 *     { paramName: 'Content-Type', paramTypeName: 'string', description: '内容类型', checkType: true }
 *   ]
 * };
 * const headers = getHeaderFunc(inParamModelData);
 * console.log(headers); // 输出: [{ name: 'Authorization', type: 'string', description: '认证令牌', required: true }, { name: 'Content-Type', type: 'string', description: '内容类型', required: true }]
 */
function getHeaderFunc(inParamModelData) {
  // 解析参数数据
  const headerData = flattenDataHeader(inParamModelData)
  // 解析参数信息
  const header = getDataList(headerData)
  return header
}

/**
 * 从给定的输入参数模型数据中提取请求参数信息。
 *
 * @param {Object} inParamModelData - 包含请求参数信息的输入参数模型数据。
 * @returns {Array} - 包含请求参数信息的数组。
 *
 * @description
 * 这个函数用于从给定的输入参数模型数据中提取请求参数信息。它首先将输入数据扁平化，然后遍历扁平化后的数据，提取每个请求参数的名称、类型、描述和是否必需的信息。最后，它将这些信息组成一个数组并返回。
 *
 * @example
 * const inParamModelData = {
 *   queryParam: [
 *     { paramName: 'param1', paramTypeName: 'string', description: '参数1描述', checkType: true },
 *     { paramName: 'param2', paramTypeName: 'number', description: '参数2描述', checkType: false }
 *   ]
 * };
 * const params = getParamFunc(inParamModelData);
 * console.log(params); // 输出: [{ name: 'param1', type: 'string', description: '参数1描述', required: true }, { name: 'param2', type: 'number', description: '参数2描述', required: false }]
 */
function getParamFunc(inParamModelData) {
  // 解析参数数据
  const paramData = flattenData(inParamModelData)
  // 解析参数信息
  const params = getDataList(paramData)
  return params
}

/**
 * 从给定的列表中提取参数信息并返回一个新的列表。
 *
 * @param {Array} list - 包含参数信息的数组。
 * @returns {Array} - 包含提取的参数信息的新数组。
 *
 * @description
 * 这个函数用于从给定的列表中提取参数信息。它遍历输入的列表，对每个元素进行处理，提取出 paramName、paramTypeName、description 和 checkType 等信息，并将这些信息组成一个新的对象。如果 checkType 存在，则将其转换为布尔值。最后，它将这些新对象组成一个数组并返回。
 *
 * @example
 * const list = [
 *   { paramName: 'param1', paramTypeName: 'string', description: '参数1描述', checkType: 'true' },
 *   { paramName: 'param2', paramTypeName: 'number', description: '参数2描述', checkType: 'false' }
 * ];
 * const newList = getDataList(list);
 * console.log(newList); // 输出: [{ name: 'param1', type: 'string', description: '参数1描述', required: true }, { name: 'param2', type: 'number', description: '参数2描述', required: false }]
 */
function getDataList(list) {
  // 解析返回值信息
  const returns = list.map((el) => {
    const obj = {
      name: el?.paramName,
      type: el?.paramTypeName,
      description: el?.description,
      required: Boolean(el?.checkType),
    }
    if (el?.children && el.children.length > 0) {
      obj.children = getDataList(el.children)
      obj.isList = true
    }
    return obj
  })
  return returns
}

function getReturnFunc(mockReturnResultExample) {
  try {
    // 解析返回值信息
    const returns =
      mockReturnResultExample &&
      mockReturnResultExample.map((el) => {
        return {
          name: el?.name,
          isArray: el?.isList,
          description: el?.description,
          type:
            el?.isList || (el?.children && el.children.length > 0)
              ? getReturnFunc(el?.children)
              : getLastCharAfterDot(el.type),
        }
      })
    return returns
  } catch (err) {
    console.log(
      '%c 🍉转换return 返回参数失败: ',
      'font-size:12px;background-color: #E41A6A;color:#fff;',
      err,
    )
  }
}

/**
 * 将数据进行转换处理。
 * @param {Object} data - 要转换的数据对象
 * @returns {Array} - 转换后的数据数组
 */
export function transformData(data) {
  // 使用 Object.entries 方法将对象转换为键值对数组，并使用 map 方法遍历数组
  return Object.entries(data).map(([key, value]) => {
    // 如果值的类型为对象，则使用 Object.entries 方法将对象转换为键值对数组，并使用 map 方法遍历数组
    return {
      name: key,
      isArray: Array.isArray(value),
      type: Array.isArray(value)
        ? transformData(value[0])
        : typeof value === 'object'
          ? transformData(value)
          : typeof value,
    }
  })
}

/**
 * 从给定的列表中生成随机数据。
 *
 * @param {Array} list - 包含数据项的数组，每个数据项包含 name 和 type 属性。
 * @param {string} list.name - 数据项的名称。
 * @param {string} list.type - 数据项的数据类型，可以是 'string' 或 'number'。
 * @returns {Object} - 包含生成的随机数据的对象，其中键是数据项的名称，值是相应类型的随机值。
 *
 * @example
 * const list = [
 *   { name: 'username', type: 'string' },
 *   { name: 'age', type: 'number' }
 * ];
 * const data = generateDataFromList(list);
 * console.log(data); // 可能输出: { username: '随机字符串', age: 随机整数 }
 */
export function generateDataFromList(list) {
  const data = {}
  list.forEach((item) => {
    let value
    if (item.type === 'string') {
      value = Mock.Random.string()
    } else if (item.type === 'number') {
      // 生成大于 0 的整数
      value = Mock.Random.integer(1, Number.MAX_SAFE_INTEGER)
    }
    data[item.name] = value
  })
  return data
}

/**
 * 获取当前时间并格式化为字符串
 * @returns {string} - 当前时间的字符串表示，格式为 'YYYY-MM-DD HH:mm:ss'
 */
export function getCurrentTime() {
  const momentCool = moment()
  return momentCool.format('YYYY-MM-DD HH:mm:ss')
}

/**
 * 从给定的字符串中提取最后一个斜杠（/）之后的部分
 * @param {string} str - 输入的字符串
 * @returns {string} - 提取出的字符串，如果输入的字符串中没有斜杠，则返回原字符串
 */
export function extractAfterSlash(str) {
  const lastSlashIndex = str.lastIndexOf('/')
  if (lastSlashIndex === -1) return str
  return addActionIfReserved(str.substring(lastSlashIndex + 1))
}

//  符合js关键字 添加 "Action" 后缀
function addActionIfReserved(word) {
  if (reservedWords.includes(word)) {
    return word + 'Action'
  }
  return word
}

function isHasMore(ids, module) {
  const obj = {}
  const list = JSON.parse(JSON.stringify(module))
  list.forEach((item) => {
    if (item.interfaceInfos && item.interfaceInfos.length) {
      const fullPath = item.interfaceInfos[0].fullPath
      const parts = fullPath.split('/')
      // 已存在表示false
      if (obj[parts[2]]) {
        obj[parts[2]] = false
      } else {
        obj[parts[2]] = true
      }
    }
  })
  return !!obj[ids]
}

/**
 * 获取模块名称
 * @param {Object} item - 包含模块信息的对象
 * @returns {string} - 提取出的模块名称
 */
export function getModuleName(item, module) {
  try {
    let moduleNameNew = item.moduleName
    if (item.interfaceInfos && item.interfaceInfos.length) {
      const fullPath = item.interfaceInfos[0].fullPath
      const parts = fullPath.split('/')
      const moduleId = item.moduleId
      moduleNameNew =
        parts[2] + (!isHasMore(parts[2], module) ? moduleId.slice(moduleId.length - 5) : '')
    }
    return moduleNameNew
  } catch (err) {
    console.log('获取moduleName失败：' + err)
  }
}

/**
 * 解析 URL 并提取协议、IP 地址、端口和 UID
 * @param {string} url - 要解析的 URL
 * @returns {Object} - 包含协议、IP 地址、端口和 UID 的对象
 */
export function parseUrl(url) {
  // 使用 URL 类解析传入的 URL 字符串
  const urlObj = new URL(url)

  // 获取协议、IP 地址和端口，并格式化为字符串
  const ipWithPort = `${urlObj.protocol}//${urlObj.hostname}:${urlObj.port}`

  // 获取 URL 的哈希部分
  const hash = urlObj.hash

  // 如果哈希部分存在，则解析它以获取 UID 参数
  if (hash) {
    // 将哈希部分分割成键值对字符串
    const hashParams = new URLSearchParams(hash.split('?')[1])

    // 从键值对字符串中获取 UID 参数的值
    const uid = hashParams.get('uid')

    // 返回包含协议、IP 地址、端口和 UID 的对象
    return { ipWithPort, uid }
  }

  // 如果哈希部分不存在，则只返回协议、IP 地址和端口
  return { ipWithPort }
}

export function getToLocaleLowerCase(value) {
  return value.toLocaleLowerCase()
}
