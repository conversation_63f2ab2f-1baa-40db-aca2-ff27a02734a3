<template>
  <div class="home-page">
    <div class="home-top">
      <img :src="blobUrl" alt="" class="avatar" />
      <div class="home-welcome">
        <p>您好，开始您一天的工作吧！</p>
        <span>{{ date }}</span>
      </div>
      <div class="task-status">
        <div class="running status-div">
          <p v-if="statisticsData[0]">{{ statisticsData[0].type }}</p>
          <span v-if="statisticsData[0]">{{ statisticsData[0].cnt }}</span>
        </div>
        <a-divider type="vertical" />
        <div class="wait status-div">
          <p v-if="statisticsData[1]">{{ statisticsData[1].type }}</p>
          <span v-if="statisticsData[1]">{{ statisticsData[1].cnt }}</span>
        </div>
        <a-divider type="vertical" />
        <div class="fail status-div">
          <p>失败实例</p>
          <span>{{ failWarning.cnt }}</span>
        </div>
      </div>
    </div>
    <div class="home-content">
      <div class="dashboard-container">
        <div class="left-column">
          <div class="section task-types">
            <div class="section-header">
              <h3>任务类型</h3>
            </div>
            <div class="cards-container" v-if="stats1.length">
              <div class="card real-time-tasks" v-for="item in stats1" :key="item.title">
                <div class="card-content">
                  <h4>{{ item.title }}</h4>
                  <div class="number">{{ item.value }}</div>
                  <div class="icon">
                    <img :src="item.icon" alt="" />
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="section real-time-status">
            <div class="section-header">
              <h3>实时状态</h3>
              <div class="refresh-icon">
                <RedoOutlined style="font-size: 16px; color: #1890ff" @click="getStatusInfo" />
              </div>
            </div>
            <div class="cards-container">
              <div class="card spark" v-for="item in stats2" :key="item.title">
                <div class="card-content">
                  <h4>{{ item.title }}</h4>
                  <div class="status-row">
                    <div class="number">{{ item.value }}</div>
                    <a-tag :closable="false" color="#87d068" v-if="item.status === 1">
                      运行中
                    </a-tag>
                    <a-tag class="tag-btn" v-else-if="item.status === 0">未启动</a-tag>
                    <a-tag color="#f50" v-else-if="item.status === -1" class="tag-btn">异常</a-tag>
                    <div class="actions">
                      <a-button
                        type="link"
                        :disabled="item.status === 1"
                        class="action-link"
                        @click.stop="setProcessMonitorHanlde('start', item.serverId)"
                        >启动</a-button
                      >
                      <a-button
                        type="link"
                        class="action-link"
                        :disabled="item.status === 0"
                        @click.stop="setProcessMonitorHanlde('stop', item.serverId)"
                        >停止</a-button
                      >
                      <a-button
                        type="link"
                        class="action-link"
                        @click.stop="setProcessMonitorHanlde('restart', item.serverId)"
                        >重启</a-button
                      >
                    </div>
                  </div>
                  <div class="icon">
                    <img :src="item.icon" alt="" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="right-column">
          <div class="section job-flow-instances">
            <div class="section-header">
              <h3>作业流实例</h3>
              <div class="refresh-icon">
                <RedoOutlined
                  style="font-size: 16px; color: #1890ff"
                  @click="getJobFlowInstanceStatisticsHanlde"
                />
              </div>
            </div>
            <div class="table-container">
              <a-table
                :columns="columns"
                :data-source="tableData"
                :pagination="false"
                :show-header="true"
                size="middle"
                class="instances-table"
              >
                <template #bodyCell="{ column, record }">
                  <template v-if="column.key === 'status'">
                    <div class="status-cell">
                      <div class="status-icon">
                        <img :src="getImgIcon(record.state)" />
                      </div>
                      <span>{{ record.type }}</span>
                    </div>
                  </template>
                  <template v-else-if="column.key === 'percentage'">
                    <div class="progress-container">
                      <span>{{ record?.taskPercent * 100 || 0 }}%</span>
                      <div class="progress-bar">
                        <div
                          class="progress-fill"
                          :style="{ width: record?.taskPercent ?? 0 + '%' }"
                        ></div>
                      </div>
                    </div>
                  </template>
                </template>
              </a-table>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="home-bottom">
      <a-row :gutter="24" justify="space-between" type="flex">
        <a-col :span="14">
          <section
            class="section-container section-card"
            style="border: 1px solid #0000001a; border-radius: 10px"
          >
            <RedoOutlined
              style="font-size: 16px; color: #1890ff"
              class="section-icon"
              @click="getComponentStatisticsHanlde"
            />
            <div class="module-header-btn-box">
              <div
                v-for="item in moduleList"
                :key="item.value"
                :class="checkModule === item.value ? 'mhd-btn-check' : ''"
                class="mhd-btn"
                @click="handleChangeModule(item.value)"
              >
                {{ item.label }}
              </div>
            </div>
            <div class="module-chart-box">
              <div class="mc-left-box">
                <div
                  v-for="(item, index) in lineData[checkModule - 1]"
                  :key="index"
                  class="line-item"
                >
                  <div class="percent-text">{{ formatPercent(item.percent) }}%</div>
                  <div class="progress-box">
                    <div
                      v-if="item.percent"
                      :style="{ height: item.percent * 120 + 'px' }"
                      class="progress-color-box"
                    ></div>
                  </div>
                  <div class="line-text">{{ item.type }}</div>
                </div>
              </div>
              <div class="mc-right-box">
                <div v-show="checkModule === 4" id="realTimeSync"></div>
                <div v-show="checkModule === 5" id="realTimeCompute"></div>
                <div v-show="checkModule === 1" id="dbImport"></div>
                <div v-show="checkModule === 2" id="sql"></div>
                <div v-show="checkModule === 3" id="sparkSql"></div>
              </div>
            </div>
            <div class="module-data-box">
              <div
                v-for="(item, index) in lineData[checkModule - 1]"
                :key="index"
                class="md-number-item"
              >
                <p>{{ item.type }}</p>
                <p>{{ item.value }}</p>
              </div>
            </div>
          </section>
        </a-col>
        <a-col :span="10">
          <section
            class="section-container section-card"
            style="padding: 0; border: 1px solid #0000001a"
          >
            <div class="section-top section-title-box" style="padding: 5px 10px 0 20px">
              <h1 class="section-title">正在执行的实例</h1>
              <RedoOutlined style="font-size: 16px; color: #1890ff" @click="toLink" />
            </div>
            <div class="section-content run-list-content" style="height: 485px">
              <template v-if="runingJobFlowList.length">
                <p
                  v-for="(item, key) in runingJobFlowList"
                  :key="key"
                  @click="toLink(item)"
                  class="task-to"
                >
                  {{ item.name }}
                </p>
              </template>
              <a-empty v-else style="padding-top: 100px" />
            </div>
          </section>
        </a-col>
      </a-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useUserStore } from '@/stores/user'
import { storeToRefs } from 'pinia'
import { downLoadFile } from '@/api/services/user'
import { formatPercent } from '@/utils/utils'
import { RedoOutlined } from '@ant-design/icons-vue'
import {
  getTotalDataStatistics,
  getJobFlowInstanceStatistics,
  getHomePageRealTime,
  getjudgeInfo,
  setProcessMonitor,
  getComponentStatistics,
  getRuningJobFlow,
} from '@/api/home'
import { message } from 'ant-design-vue'
import dayjs from 'dayjs'
import waiting from '@/assets/svg/waiting.svg'
import success from '@/assets/svg/success.svg'
import running from '@/assets/svg/running.svg'
import failure from '@/assets/svg/failure.svg'
import shishi from '@/assets/svg/shishi.svg'
import feishishi from '@/assets/svg/feishishi.svg'
import sparkIcon from '@/assets/svg/spark-icon.svg'
import flinkIcon from '@/assets/svg/flink-icon.svg'
import * as echarts from 'echarts'
import { useRouter } from 'vue-router'

defineOptions({
  name: 'home',
})

class StatisticsDTO {
  cnt?: number
  type?: string
  count?: number
  state?: string
  moduletype?: string
  value?: string
}

class InstanceInfoDTO {
  cnt?: number
  total?: number
  taskPercent?: number
  state?: string
  type?: string
}

const router = useRouter()
const userStore = useUserStore()
const { userInfo } = storeToRefs(userStore)
const blobUrl = ref('')
const checkModule = ref(4)
const lineData = ref<any[]>([])
const stats1 = ref<any[]>([])
const stats2 = ref<any[]>([])
const runingJobFlowList = ref<any[]>([])
const statisticsData = ref<StatisticsDTO[]>([])
const failWarning = ref<any>({})
const lang = 'zh-CN'
const date = dayjs().format('YYYY-MM-DD')
// 表格列配置
const columns = [
  {
    title: '实例状态',
    dataIndex: 'status',
    key: 'status',
    width: 150,
  },
  {
    title: '实例数量',
    dataIndex: 'cnt',
    key: 'cnt',
    width: 100,
  },
  {
    title: '实例占比',
    dataIndex: 'percentage',
    key: 'percentage',
    width: 200,
  },
  {
    title: '实例总数',
    dataIndex: 'total',
    key: 'total',
    width: 100,
  },
]

// 表格数据
const tableData = ref<any>([])

const moduleList = [
  { label: '实时同步组件', value: 4 },
  { label: '实时计算组件', value: 5 },
  { label: '批量同步组件', value: 1 },
  { label: 'SQL组件', value: 2 },
  { label: 'SparkSQL组件', value: 3 },
]

onMounted(() => {
  downLoadFileHandle(userInfo.value.avatarUrl)
  getTotalDataStatisticsHanlde()
  getJobFlowInstanceStatisticsHanlde()
  getTaskInfo()
  getStatusInfo()
  getComponentStatisticsHanlde()
  getRuningJobFlowHandle()
})

const getImgIcon = function (state: string) {
  switch (state) {
    case '1':
      return running
    case '2':
      return success
    case '3':
      return waiting
    case '-1':
      return failure
  }
  return ''
}

// 获取作业流数据统计
const getTotalDataStatisticsHanlde = function () {
  getTotalDataStatistics({ lang }).then((res: any) => {
    statisticsData.value = res.data || []
  })
}

// 获取作业流实例统计
const getJobFlowInstanceStatisticsHanlde = function () {
  getJobFlowInstanceStatistics({
    createTime: date,
    ownerId: userStore.userInfo.loginAccount,
    lang,
  }).then((res) => {
    tableData.value = res.data || []
    failWarning.value = res.data && res.data.find((i: StatisticsDTO) => i.state === '-1')
  })
}

// 设置集群状态
const setProcessMonitorHanlde = async (type: string, serverId: any) => {
  try {
    await setProcessMonitor({ type, params: { serverId } })
    message.success('操作成功')
    getStatusInfo()
  } catch (error) {
    console.log(error)
  }
}

//  获取首页任务类型
const getTaskInfo = function () {
  getHomePageRealTime({ lang: lang }).then((res: any) => {
    if (res.data.length) {
      stats1.value = [
        {
          title: res.data[1].type,
          value: res.data[1].count,
          icon: shishi,
        },
        {
          title: res.data[0].type,
          value: res.data[0].count,
          icon: feishishi,
        },
      ]
    }
  })
}

//  获取首页实时状态
const getStatusInfo = function () {
  getjudgeInfo({}).then((res: any) => {
    if (res.data.code === 1) {
      stats2.value = [
        {
          title: res.data.obj[1].type.toUpperCase(),
          value: res.data.obj[1].count,
          type: res.data.obj[1].type,
          serverId: res.data.obj[1].serverId,
          status: res.data.obj[1].status,
          link: res.data.obj[1].link,
          icon: sparkIcon,
        },
        {
          title: res.data.obj[0].type.toUpperCase(),
          value: res.data.obj[0].count,
          type: res.data.obj[0].type,
          serverId: res.data.obj[0].serverId,
          status: res.data.obj[0].status,
          link: res.data.obj[0].link,
          icon: flinkIcon,
        },
      ]
    }
  })
}

const downLoadFileHandle = async (fileUrl: string) => {
  if (!fileUrl) return
  const res = await downLoadFile({ fileUrl })
  // 1. 创建 Blob URL
  blobUrl.value = URL.createObjectURL(res)
}

//  改变显示组件
const handleChangeModule = (val: number) => {
  checkModule.value = val
}

// 获取组件统计信息
const getComponentStatisticsHanlde = () => {
  getComponentStatistics({ submitTime: date, lang: lang }).then((res: any) => {
    let dbImport =
      res.data && res.data.filter((item: StatisticsDTO) => item.moduletype === 'DBIMPORT')
    let dbImportTotal = dbImport.reduce((total: number, item: any) => total + item.value, 0)
    let sql = res.data && res.data.filter((item: StatisticsDTO) => item.moduletype === 'SQL')
    let sqlTotal = sql.reduce((total: number, item: any) => total + item.value, 0)
    let sparkSql =
      res.data && res.data.filter((item: StatisticsDTO) => item.moduletype === 'SPARKSQL')
    let sparkSqlTotal = sparkSql.reduce((total: number, item: any) => total + item.value, 0)
    let shell =
      res.data && res.data.filter((item: StatisticsDTO) => item.moduletype === 'REALTIMESYNC')
    let shellTotal = shell.reduce((total: number, item: any) => total + item.value, 0)
    let python =
      res.data && res.data.filter((item: StatisticsDTO) => item.moduletype === 'REALTIMECOMPUTE')
    let pythonTotal = python.reduce((total: number, item: any) => total + item.value, 0)
    canvasPie(dbImport, dbImportTotal, 'dbImport')
    canvasPie(sql, sqlTotal, 'sql')
    canvasPie(sparkSql, sparkSqlTotal, 'sparkSql')
    canvasPie(shell, shellTotal, 'realTimeSync')
    canvasPie(python, pythonTotal, 'realTimeCompute')
  })
}

const canvasPie = (data: any, total: any, dom: any) => {
  lineData.value.push(data)
  echarts.init(document.getElementById(dom)).setOption({
    title: {
      text: '组件总数' + total,
      left: 'center',
      bottom: 'left',
      textStyle: {
        color: '#fff',
        fontStyle: 'normal',
        fontWeight: 500,
        fontFamily: 'PingFangSC-Medium, PingFang SC',
        fontSize: 18,
      },
    },
    tooltip: {
      trigger: 'item',
    },
    color: ['#F9DDB9', '#58CFFF', '#FFB0E3', '#BEE461'],
    series: [
      {
        name: dom,
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center',
        },
        emphasis: {
          label: {
            show: false,
          },
        },
        labelLine: {
          show: false,
        },
        data: data.map((item: any) => {
          return {
            name: item.type,
            value: item.value,
          }
        }),
      },
    ],
  })
}

// 获取正在运行的作业流
const getRuningJobFlowHandle = () => {
  getRuningJobFlow({
    pageIndex: 1,
    pageSize: 10,
    ownerId: userStore.userInfo.loginAccount,
  }).then((res) => {
    runingJobFlowList.value = res.data.records
  })
}

const toLink = (item: any) => {
  router.push(
    `/iframePage?pageName=作业流示例&url=${window.ENV.IFRAME_PAGE_URL_数据资产目录}/jobFlow/jobFlowInstance&id=${item.id}&type=instance&name=${item.name}`,
  )
}
</script>
<style lang="less" scoped>
@import './home.less';
</style>
