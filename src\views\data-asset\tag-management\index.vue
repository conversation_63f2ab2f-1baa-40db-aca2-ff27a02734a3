<template>
  <div class="tag-management">
    <a-row class="tag-container">
      <a-col :span="6" class="category-box">
        <div class="category-inner">
          <h4 class="category-header">分类</h4>
          <a-button class="add-category" @click="addCategory">+ 添加分类</a-button>
          <a-menu v-model:selectedKeys="currentKey" class="category-menu">
            <a-menu-item v-for="item in categoryList" :key="item.key">
              <div class="menu-item">
                <span class="item-title" :title="item.label">{{ item.label }}</span>
                <a-dropdown>
                  <a class="item-icon" @click.prevent>
                    <EllipsisOutlined />
                  </a>
                  <template #overlay>
                    <a-menu @click="(i: ITagCategoryMenu) => onClickFn(i.key, item)">
                      <a-menu-item key="edit"><EditOutlined class="icon-btn" />编辑</a-menu-item>
                      <a-menu-item key="delete"
                        ><DeleteOutlined class="icon-btn" />删除</a-menu-item
                      >
                    </a-menu>
                  </template>
                </a-dropdown>
              </div>
            </a-menu-item>
          </a-menu>
        </div>
      </a-col>
      <a-col :span="18" class="table-box">
        <div class="table-inner">
          <CardBox :title="currentCategory.categoryName" :subTitle="currentCategory.description">
            <template v-slot:headerRight
              ><a-button type="primary" class="add-tag" @click="addTagClick"
                >+ 添加标签</a-button
              ></template
            >
            <div class="table-wrap">
              <a-table
                :dataSource="tagList"
                :columns="columns"
                :pagination="{ pageSize: 20 }"
                :scroll="{ y: 550 }"
              >
                <template #bodyCell="{ column, record }">
                  <template v-if="column.key === 'operate'">
                    <a-button type="link" @click="editTagClick(record)">编辑</a-button>
                    <a-button type="link" @click="delTagClick(record)">删除</a-button>
                  </template>
                  <span v-else>{{ record[column.key] }}</span>
                </template>
              </a-table>
            </div>
          </CardBox>
        </div>
      </a-col>
    </a-row>
  </div>

  <EditCategory
    :visibleDialog="showCategoryDialog"
    :dialogType="categoryType"
    :currentItem="categoryForm"
    @okHandle="saveCategory"
    @canceHandle="canceCategory"
  ></EditCategory>
  <EditTag
    :visibleDialog="showTagDialog"
    :dialogType="tagType"
    :categoryId="currentCategory.categoryId"
    :currentItem="tagItem"
    @okHandle="saveTag"
    @canceHandle="canceTag"
  ></EditTag>
</template>

<script lang="ts" setup>
import {
  getTagCategoryList,
  deleteTagCategoryItem,
  type AssetResponse,
  type ITag,
  type ITagCategory,
  type ITagCategoryMenu,
  getTagList,
  type ITagParams,
  deleteTagItem,
} from '@/api/services/data-asset/tag-management'
import {
  MenuUnfoldOutlined,
  EllipsisOutlined,
  EditOutlined,
  DeleteOutlined,
} from '@ant-design/icons-vue'
import { message, Modal } from 'ant-design-vue'
import CardBox from '@/components/card-box/card-box.vue'
import EditTag from './edit-tag.vue'
import EditCategory from './edit-category.vue'

defineOptions({
  name: 'tag-management',
})

const initCategoryForm = () => {
  return {
    categoryName: '',
    description: '',
  }
}

const initTagForm = () => {
  return {
    categoryId: 0,
    labelName: '',
    description: '',
  }
}
const currentKey = ref([''])
const currentCategory = ref<ITagCategory>(initCategoryForm())

const showCategoryDialog = ref(false)
const categoryList = ref<ITagCategoryMenu[]>([])
const categoryType = ref('add')
const categoryForm = ref<ITagCategory>(initCategoryForm())

const tagList = ref<ITag[]>([])
const showTagDialog = ref(false)
const tagType = ref('add')
const tagItem = ref<ITag>(initTagForm())

const columns = ref([
  {
    title: '标签名称',
    dataIndex: 'labelName',
    key: 'labelName',
    width: 160,
  },
  {
    title: '标签描述',
    dataIndex: 'description',
    key: 'description',
    width: 300,
  },
  {
    title: '操作',
    dataIndex: 'operate',
    key: 'operate',
    width: 160,
  },
])

watch(
  () => currentKey.value,
  () => {
    const item = categoryList.value.find((item) => item.key === currentKey.value[0])
    if (item) {
      currentCategory.value = {
        categoryId: Number(item.key),
        categoryName: item.label,
        description: item.desc,
      }
      initTagList()
    } else {
      currentCategory.value = initCategoryForm()
    }
  },
  { immediate: true },
)
const onClickFn = (key: string, item: ITagCategoryMenu) => {
  if (key === 'edit') {
    categoryType.value = 'edit'
    categoryForm.value = {
      categoryId: Number(item.key),
      categoryName: item.label,
      description: item.desc,
    }
    showCategoryDialog.value = true
  } else if (key === 'delete') {
    delCategoryFn(item.key)
  }
}

// 删除分类
const delCategoryFn = async (id: string) => {
  Modal.confirm({
    title: '确定要删除吗？',
    onOk: async () => {
      const res = await deleteTagCategoryItem(Number(id))
      if (res.code === '000000') {
        message.success('删除成功')
        initCategory()
      } else {
        message.error(res.msg)
      }
    },
  })
}

// 分类列表
const initCategory = async () => {
  const res: AssetResponse<ITagCategory[]> = await getTagCategoryList()
  if (res.code === '000000') {
    categoryList.value = res.data.map((item: ITagCategory) => {
      return {
        key: `${item.categoryId}`,
        icon: () => h(MenuUnfoldOutlined),
        label: item.categoryName,
        desc: item.description,
      }
    })
    currentKey.value = [`${res.data[0]?.categoryId ?? -1}`]
    currentCategory.value = res.data[0] ?? initCategoryForm()
  }
}

// 添加分类
const addCategory = () => {
  categoryType.value = 'add'
  categoryForm.value = initCategoryForm()
  showCategoryDialog.value = true
}

// 分类保存
const saveCategory = () => {
  showCategoryDialog.value = false
  initCategory()
}

// 删除tag
const delTagClick = async (tag: ITag) => {
  Modal.confirm({
    title: '确定要删除吗？',
    onOk: async () => {
      const res = await deleteTagItem(Number(tag.labelId))
      if (res.code === '000000') {
        message.success('删除成功')
        initTagList()
      } else {
        message.error(res.msg)
      }
    },
  })
}

// 取消操作
const canceCategory = () => {
  showCategoryDialog.value = false
  categoryForm.value = initCategoryForm()
}

// 获取标签列表
const initTagList = async () => {
  if (!currentCategory.value.categoryId) {
    return
  }
  const params: ITagParams = {
    categoryId: currentCategory.value.categoryId ?? -1,
  }
  const res: AssetResponse<ITag[]> = await getTagList(params)
  if (res.code === '000000') {
    tagList.value = res.data
  }
}

const editTagClick = (item: ITag) => {
  tagItem.value = item
  tagType.value = 'edit'
  showTagDialog.value = true
}

// 添加标签
const addTagClick = () => {
  tagItem.value = initTagForm()
  tagType.value = 'add'
  showTagDialog.value = true
}
// 保存标签
const saveTag = () => {
  canceTag()
  initTagList()
}
// 取消操作
const canceTag = () => {
  showTagDialog.value = false
  tagItem.value = initTagForm()
}

onMounted(async () => {
  await initCategory()
})
</script>
<style scoped lang="less">
.tag-management,
.tag-container {
  width: 100%;
  height: 100%;
  background: white;
}
.category-box,
.table-box {
  height: 100%;
}
.tag-container {
  border-right: 1px solid #eee;
}
.category-inner,
.table-inner {
  width: 100%;
  height: 100%;
  padding: 12px;
  display: flex;
  flex-direction: column;
}
.category-inner {
  border-right: 1px solid #eee;
}
.category-menu {
  overflow: auto;
  height: 0;
  flex: auto;
}
.add-category {
  width: 100%;
  margin-bottom: 16px;
}
.category-header {
  margin-block-start: 0;
  margin-block-end: 0;
  font-size: 16px;
  margin-bottom: 16px;
}
.menu-item {
  display: flex;
  flex-direction: row;
}
.item-title {
  flex: auto;
  width: 0;
  display: inline-block;
  white-space: nowrap; /* 防止文本自动换行 */
  overflow: hidden; /* 隐藏超出部分 */
  text-overflow: ellipsis; /* 显示省略号 */
}
.item-icon {
  font-size: 20px;
  transform: rotate(90deg);
}
.icon-btn {
  margin-right: 5px;
}
:deep(.ant-menu-light.ant-menu-root.ant-menu-vertical) {
  border-inline-end: none;
}
</style>
