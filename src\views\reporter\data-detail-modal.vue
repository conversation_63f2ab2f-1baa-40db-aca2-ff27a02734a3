<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { VAceEditor } from 'vue3-ace-editor'
import 'ace-builds/src-noconflict/mode-json' // 导入文本模式
import 'ace-builds/src-noconflict/theme-github' // 导入github主题
import { getQuailtyDataDtl, getQuailtyDataLog } from '@/api/services/dataquality'

interface TableRecord {
  key: string
  name: string
  status: string
  time: string
  type: string
}

const visible = ref(false)
const logModalVisible = ref(false)
const selectedRecord = ref<any>(null)
const highlightedCode = ref<any>(null)
const loading = ref(false)

// Pagination
const current = ref(1)
const pageSize = ref(10)
const total = ref(0)

// Parameters passed from parent
const modalParams = ref<any>({})

// Table data
const dataSource = ref<TableRecord[]>([])

const columns = [
  {
    title: '组件描述 ',
    dataIndex: 'compDesc',
    key: 'compDesc',
  },
  {
    title: '状态',
    dataIndex: 'stateName',
    key: 'stateName',
  },
  {
    title: '任务开始时间',
    dataIndex: 'startTime',
    key: '任务开始时间',
  },
  {
    title: '任务结束时间',
    dataIndex: 'endTime',
    key: 'endTime',
  },
  {
    title: '规则名称',
    dataIndex: 'ruleName',
    key: 'ruleName',
  },
  {
    title: '文件路径',
    dataIndex: 'dataFile',
    key: 'dataFile',
  },
  {
    title: '操作',
    key: 'action',
    width: 120,
    slots: {
      customRender: 'action',
    },
    fixed: 'right',
  },
]

const editorOptions = ref({
  fontSize: '14px', // 设置字体大小
  showPrintMargin: false, // 是否显示打印边距
  readOnly: true, // 设置为只读
})

// Fetch data from API
const fetchData = async () => {
  if (!modalParams.value.flowInstanceId) return

  loading.value = true
  try {
    // Adjust parameters based on your actual API requirements
    const params = {
      flowInstanceId: modalParams.value.flowInstanceId,
      pageIndex: current.value,
      pageSize: pageSize.value,
      startTime: modalParams.value.startTime || '',
      endTime: modalParams.value.endTime || '',
    }

    const res = await getQuailtyDataDtl(params)
    // Mock response structure - adjust based on your actual API response
    dataSource.value = res?.records || []
    total.value = res.data?.total || 0
  } catch (error) {
    console.error('Failed to fetch data:', error)
  } finally {
    loading.value = false
  }
}

// Handle pagination change
const handleTableChange = (pagination: any) => {
  current.value = pagination.current
  pageSize.value = pagination.pageSize
  fetchData()
}

const showModal = (params: any) => {
  modalParams.value = params
  visible.value = true
  current.value = 1 // Reset to first page
  fetchData()
}

const handleCancel = () => {
  visible.value = false
  modalParams.value = {}
  dataSource.value = []
}

const showLogModal = async (record: any) => {
  selectedRecord.value = record
  logModalVisible.value = true
  const res = await getQuailtyDataLog({
    taskInstanceId: record.taskInstanceId,
    dataFile: record.dataFile,
  })
  highlightedCode.value = res.data.log
}

const handleLogCancel = () => {
  logModalVisible.value = false
}

defineExpose({
  showModal,
})
</script>

<template>
  <a-modal
    v-model:visible="visible"
    title="查询质量报告详情"
    width="1000px"
    @cancel="handleCancel"
    :footer="null"
  >
    <a-table
      :columns="columns"
      :data-source="dataSource"
      :loading="loading"
      :pagination="{
        current: current,
        pageSize: pageSize,
        total: total,
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: (total: number, range: [number, number]) => `共 ${total} 条`,
      }"
      @change="handleTableChange"
      :scroll="{ x: 1500 }"
    >
      <template #action="{ record }">
        <a-button type="link" @click="showLogModal(record)">明细数据</a-button>
      </template>
    </a-table>

    <!-- 日志弹窗 -->
    <a-modal
      v-model:visible="logModalVisible"
      title="明细数据"
      width="600px"
      @cancel="handleLogCancel"
      :footer="null"
    >
      <VAceEditor
        v-model:value="highlightedCode"
        lang="json"
        theme="github"
        :options="editorOptions"
        style="height: 300px; width: 100%"
      />
    </a-modal>
  </a-modal>
</template>

<style scoped>
.ant-timeline {
  margin: 16px;
}
</style>
