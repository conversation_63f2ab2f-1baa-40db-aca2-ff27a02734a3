import { color } from 'echarts'
import demoData from './data.json'

console.log(1111, demoData)
export const demoDataList = demoData.result.entityTypeDTOList.map((item) => {
  return {
    ...item,
    label: { show: true, position: 'bottom' },
    itemStyle: { color: getRandomColor() },
  }
})
export const initLinkList = demoData.result.relationTypeDTOList.map((item) => {
  return {
    ...item,
    source: String(item.startEntity.id), // 一定要确保source、target字段是字符串类型
    target: String(item.endEntity.id),
    level: item.originBelongToProject,
    lineStyle: { curveness: 0.3, color: '#2596FF', width: 2 },
  }
})

console.log('获取到initLink了', initLinkList)

export const categories = [
  {
    name: 'center',
    label: {
      show: true,
      position: 'inside',
      fontSize: 34,
      fontFamily: 'NUM-TITLE',
    },
    // symbol: 'image://' + 'bg',
    symbolSize: 150,
    itemStyle: {
      color: '#f90',
      shadowColor: '#2596FF',
      shadowBlur: 10,
    },
  },
  {
    name: 'up2',
    // symbol: 'image://' + 'nodebg1',
    symbolSize: 100,
    itemStyle: {
      color: '#f90',
      shadowColor: '#2596FF',
      shadowBlur: 10,
    },
  },
  {
    name: 'mid2',
    // symbol: 'image://' + 'nodebg2',
    symbolSize: 80,
    label: {
      position: 'inside',
      fontSize: 23,
      fontFamily: 'NUM-TITLE',
    },
    itemStyle: {
      color: '#f90',
      shadowColor: '#2596FF',
      shadowBlur: 10,
    },
  },
  {
    name: 'down2',
    // symbol: 'image://' + 'nodebg3',
    symbolSize: 100,
    label: {
      position: 'inside',
      fontSize: 23,
      fontFamily: 'NUM-TITLE',
    },
    itemStyle: {
      color: '#f90',
      shadowColor: '#2596FF',
      shadowBlur: 10,
    },
  },
  {
    name: 'up3',
    symbolSize: 28,
    itemStyle: {
      color: '#2596FF',
      shadowColor: '#2596FF',
      shadowBlur: 10,
    },
    label: {
      show: true,
      position: 'bottom',
    },
  },
  {
    name: 'mid3',
    symbolSize: 28,
    itemStyle: {
      color: '#00FFF4',
      shadowColor: '#00FFF4',
      shadowBlur: 10,
    },
  },
  {
    name: 'down3',
    symbolSize: 28,
    itemStyle: {
      color: '#FF9500',
      shadowColor: '#FF9500',
      shadowBlur: 10,
    },
    label: {
      show: true,
      position: 'bottom',
    },
  },
]

export const nodesList = [
  {
    id: '1',
    name: '中心节点',
    stream: 'center',
    level: 1,
    category: 'center',
    label: {
      show: true,
      position: 'inside',
      overflow: 'break',
      fontFamily: 'DOUYU',
      lineHeight: 20,
      fontSize: 18,
      width: 100,
      color: '#f00',
    },
  },
  {
    id: '2',
    name: '节点1',
    level: 2,
    stream: 'up',
    category: 'up2',
    label: {
      show: true,
      position: 'inside',
      overflow: 'break',
      fontFamily: 'DOUYU',
      lineHeight: 20,
      fontSize: 16,
      width: 70,
      color: '#f00',
    },
  },
  {
    id: '2-1',
    name: '2-111111',
    level: 3,
    stream: 'up',
    category: 'up3',
  },
  {
    id: '2-2',
    name: '2222',
    stream: 'up',
    level: 3,
    category: 'up3',
  },
  {
    id: '2-3',
    name: '2-333333',
    stream: 'up',
    level: 3,
    category: 'up3',
  },
  {
    id: '2-4',
    name: '2-4444444',
    stream: 'up',
    level: 3,
    category: 'up3',
  },
  {
    id: '3',
    name: '材料',
    stream: 'up',
    level: 2,
    category: 'up2',
    label: {
      show: true,
      position: 'inside',
      overflow: 'break',
      fontFamily: 'DOUYU',
      lineHeight: 20,
      fontSize: 16,
      width: 70,
      color: '#f00',
    },
  },
  {
    id: '4',
    name: '服务',
    stream: 'mid',
    level: 2,
    category: 'mid2',
    label: {
      show: true,
      position: 'inside',
      overflow: 'break',
      fontFamily: 'DOUYU',
      lineHeight: 20,
      fontSize: 16,
      width: 70,
      color: '#f00',
    },
  },
  {
    id: '4-1',
    name: '技术服务',
    stream: 'mid',
    level: 3,
    category: 'mid3',
  },
  {
    id: '5',
    name: '工程',
    stream: 'mid',
    level: 2,
    category: 'mid2',
    label: {
      show: true,
      position: 'inside',
      overflow: 'break',
      fontFamily: 'DOUYU',
      lineHeight: 20,
      fontSize: 16,
      width: 70,
      color: '#f00',
    },
  },
  {
    id: '6',
    name: '施工',
    stream: 'down',
    level: 2,
    category: 'down2',
    label: {
      show: true,
      position: 'inside',
      overflow: 'break',
      fontFamily: 'DOUYU',
      lineHeight: 20,
      fontSize: 16,
      width: 70,
      color: '#f00',
    },
  },
  {
    id: '7',
    name: '管理',
    stream: 'down',
    level: 2,
    category: 'down2',
    label: {
      show: true,
      position: 'inside',
      overflow: 'break',
      fontFamily: 'DOUYU',
      lineHeight: 20,
      fontSize: 16,
      width: 70,
      color: '#f00',
    },
  },
  {
    id: '7-1',
    name: '智能',
    stream: 'down',
    level: 3,
    category: 'down3',
    label: {
      show: true,
      position: 'bottom',
      color: '#f00',
      fontSize: 15,
    },
  },
]

const linksList = [
  {
    source: '1',
    target: '2',
    lineStyle: {
      width: 2,
      color: {
        type: 'linear',
        x: 0,
        y: 0,
        x2: 1,
        y2: 1,
        colorStops: [
          {
            offset: 0,
            color: '#0f3362', // 0% 处的颜色
          },
          {
            offset: 1,
            color: '#2596FF', // 100% 处的颜色
          },
        ],
        global: false, // 缺省为 false
      },
      curveness: 0.2,
    },
  },
  {
    source: '2',
    target: '2-1',
    lineStyle: {
      width: 2,
      color: {
        type: 'linear',
        x: 0,
        y: 0,
        x2: 1,
        y2: 1,
        colorStops: [
          {
            offset: 0,
            color: '#0f3362', // 0% 处的颜色
          },
          {
            offset: 1,
            color: '#2596FF', // 100% 处的颜色
          },
        ],
        global: false, // 缺省为 false
      },
      curveness: 0.2,
    },
  },
  {
    source: '2',
    target: '2-2',
    lineStyle: {
      width: 2,
      color: {
        type: 'linear',
        x: 0,
        y: 0,
        x2: 1,
        y2: 1,
        colorStops: [
          {
            offset: 0,
            color: '#0f3362', // 0% 处的颜色
          },
          {
            offset: 1,
            color: '#2596FF', // 100% 处的颜色
          },
        ],
        global: false, // 缺省为 false
      },
      curveness: 0.2,
    },
  },
  {
    source: '2',
    target: '2-3',
    lineStyle: {
      width: 2,
      color: {
        type: 'linear',
        x: 0,
        y: 0,
        x2: 1,
        y2: 1,
        colorStops: [
          {
            offset: 0,
            color: '#0f3362', // 0% 处的颜色
          },
          {
            offset: 1,
            color: '#2596FF', // 100% 处的颜色
          },
        ],
        global: false, // 缺省为 false
      },
      curveness: 0.2,
    },
  },
  {
    source: '2',
    target: '2-4',
    lineStyle: {
      width: 2,
      color: {
        type: 'linear',
        x: 0,
        y: 0,
        x2: 1,
        y2: 1,
        colorStops: [
          {
            offset: 0,
            color: '#0f3362', // 0% 处的颜色
          },
          {
            offset: 1,
            color: '#2596FF', // 100% 处的颜色
          },
        ],
        global: false, // 缺省为 false
      },
      curveness: 0.2,
    },
  },
  {
    source: '1',
    target: '3',
    lineStyle: {
      width: 2,
      color: {
        type: 'linear',
        x: 0,
        y: 0,
        x2: 1,
        y2: 1,
        colorStops: [
          {
            offset: 0,
            color: '#0f3362', // 0% 处的颜色
          },
          {
            offset: 1,
            color: '#2596FF', // 100% 处的颜色
          },
        ],
        global: false, // 缺省为 false
      },
      curveness: 0.2,
    },
  },
  {
    source: '1',
    target: '4',
  },
  {
    source: '4',
    target: '4-1',
  },
  {
    source: '1',
    target: '5',
  },
  {
    source: '1',
    target: '6',
    lineStyle: {
      width: 2,
      color: {
        type: 'linear',
        x: 0,
        y: 0,
        x2: 1,
        y2: 1,
        colorStops: [
          {
            offset: 0,
            color: 'rgba(255, 149, 0, 0.1)', // 0% 处的颜色
          },
          {
            offset: 1,
            color: 'rgba(255, 149, 0, 1)', // 100% 处的颜色
          },
        ],
        global: false, // 缺省为 false
      },
      curveness: 0.2,
    },
  },
  {
    source: '1',
    target: '7',
    lineStyle: {
      width: 2,
      color: {
        type: 'linear',
        x: 0,
        y: 0,
        x2: 1,
        y2: 1,
        colorStops: [
          {
            offset: 0,
            color: 'rgba(255, 149, 0, 0.1)', // 0% 处的颜色
          },
          {
            offset: 1,
            color: 'rgba(255, 149, 0, 1)', // 100% 处的颜色
          },
        ],
        global: false, // 缺省为 false
      },
      curveness: 0.2,
    },
  },
  {
    source: '7-1',
    target: '7',
    lineStyle: {
      width: 2,
      color: {
        type: 'linear',
        x: 0,
        y: 0,
        x2: 1,
        y2: 1,
        colorStops: [
          {
            offset: 0,
            color: 'rgba(255, 149, 0, 0.1)', // 0% 处的颜色
          },
          {
            offset: 1,
            color: 'rgba(255, 149, 0, 1)', // 100% 处的颜色
          },
        ],
        global: false, // 缺省为 false
      },
      curveness: 0.2,
    },
  },
]

export const series = [
  {
    type: 'graph', // 图形类别-graph关系图
    layout: 'force', // 布局方式，force为力引导布局
    draggable: true, // 指示节点是否可以拖动
    symbolSize: 20, // 节点的大小-圆圈
    autoMultLines: true, // 允许多条线连接
    autoSelf: true, // 允许节点指向自身
    roam: true, // 允许用户拖动和缩放图表
    label: {
      // 图形上的文本标签-圆圈里边展示的内容
      show: true,
      formatter: function (param) {
        // 标签内容-category
        return param.data.nameZh
      },
    },
    edgeSymbol: ['none', 'arrow'], // 连接线两端的形状,此为双向箭头
    edgeSymbolSize: [10, 10], //连接线两端形状的大小
    edgeLabel: {
      show: true,
      fontSize: 14,
      position: 'middle',
      formatter: function (param) {
        // 标签内容-category
        console.log('aaaaa', param)

        return param.data.nameZh
      },
    },
    data: demoDataList,
    links: initLinkList,
    // data: [
    //   { id: '0', nameZh: '张三', level: '0', parent: '', itemStyle: { color: '#ee6666' } },
    //   { id: '1', name: '李四', level: '1', parent: '张三', itemStyle: { color: '#9fe080' } },
    //   { id: '2', name: '王五', level: '1', parent: '张三', itemStyle: { color: '#9fe080' } },
    //   { id: '3', name: '赵六', level: '2', parent: '李四', itemStyle: { color: '#fc8452' } },
    //   { id: '4', name: '孙七', level: '2', parent: '王五', itemStyle: { color: '#fc8452' } },
    //   { id: '5', name: '孙七', level: '2', parent: '张三', itemStyle: { color: '#fc8452' } },
    //   { id: '6', name: '周八', level: '2', parent: '王五', itemStyle: { color: '#fc8452' } },
    // ],
    // links: [
    //   { source: 0, target: 1, level: '1', category: '夫妻' },
    //   { source: 0, target: 2, level: '1', category: '邻居' },
    //   { source: 1, target: 0, level: '1', category: '夫妻' },
    //   { source: 1, target: 3, level: '2', category: '堂兄弟' },
    //   { source: 2, target: 4, level: '2', category: '夫妻' },
    //   { source: 4, target: 0, level: '2', category: '祖孙' },
    //   { source: 2, target: 5, level: '2', category: '姐妹' },
    //   { source: 2, target: 6, level: '2', category: '姐妹' },
    // ],
    emphasis: {
      // 高亮状态的图形样式。
      focus: 'adjacency',
      lineStyle: {
        color: '#FF6B6B', // 自环颜色
        width: 3,
        curveness: 0.3,
      },
    },
    force: {
      // 力引导布局相关的配置项
      repulsion: 100, //节点之间的斥力因子,即值越大节点间的连线越长
      edgeLength: 90, //引力因子,值越大越往中心靠拢
      friction: 0.6,
      layoutAnimation: true,
      // gravity: 0.05, //节点受到的向中心的引力因子。该值越大节点越往中心点靠拢。
    },
    zoom: 2, // 默认图标放大的倍数
    scaleLimit: {
      // 限制放大缩小的倍数
      min: 1,
      max: 5,
    },
    //组件离容器上下左右侧的距离。
    left: 10,
    right: 10,
    top: 10,
    bottom: 10,
  },
]

export const data = {
  nodesList,
  linksList,
}

export const series2 = [
  {
    type: 'graph', // 类型:关系图
    layout: 'force', // 图的布局，类型为力导图
    draggable: true, // 指示节点是否可以拖动
    roam: true, // 是否开启鼠标缩放和平移漫游。默认不开启。如果只想要开启缩放或者平移,可以设置成 'scale' 或者 'move'。设置成 true 为都开启
    zoom: 0.9, //初始化展示大小
    edgeSymbol: ['circle', 'circle'], // 边两端的标记大小，可以是一个数组分别指定两端，也可以是单个统一指定。（因节点背景是透明，不设置端点的时候是从节点中心开始连接，设置的时候是从边开始）
    edgeSymbolSize: [1, 1], //设置端点大小和线差不多
    force: {
      //力引导图基本配置
      friction: 0.6,
      layoutAnimation: true,
      gravity: 0.05, //节点受到的向中心的引力因子。该值越大节点越往中心点靠拢。
      edgeLength: 360, //边的两个节点之间的距离
      repulsion: 4000, //节点之间的斥力因子。支持数组表达斥力范围，值越大斥力越大。
    },
    lineStyle: {
      width: 2,
      color: {
        type: 'linear',
        x: 0,
        y: 0,
        x2: 1,
        y2: 1,
        colorStops: [
          {
            offset: 0,
            color: 'rgba(0, 255, 244, 0.01)', // 0% 处的颜色
          },
          {
            offset: 1,
            color: 'rgba(0, 255, 244, 1)', // 100% 处的颜色
          },
        ],
        global: false,
      },
      curveness: 0.2, //弧度
    },
    emphasis: {
      focus: 'adjacency',
      lineStyle: {
        width: 10,
      },
    },
    label: {
      show: true,
      position: 'bottom',
      color: '#f00',
      fontSize: 15,
    },
    // 数据
    data: data.nodesList,
    links: data.linksList,
    categories: categories,
  },
]

export function getRandomColor() {
  const r = Math.floor(Math.random() * 256)
  const g = Math.floor(Math.random() * 256)
  const b = Math.floor(Math.random() * 256)
  return 'rgb(' + r + ',' + g + ',' + b + ')'
}
