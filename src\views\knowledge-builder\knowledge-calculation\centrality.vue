<script lang="ts" setup>
import { defineProps } from 'vue'
import { centralityColumn } from './data-source'
const props = withDefaults(defineProps<{ pageData?: any }>(), {
  pageData: () => [], // 默认值为数组
})
</script>

<template>
  <div class="centrality-page">
    <a-table :columns="centralityColumn" :data-source="pageData">
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'name'">
          <span>{{ record.nodeInfo.name }}</span>
        </template>
      </template>
    </a-table>
  </div>
</template>

<style lang="less" scoped></style>
