<template>
  <div class="database-table">
    <div class="content-div">
      <cardBox :title="databaseData?.tableSchema" subTitle="数据表列表">
        <template #title>
          <div style="display: flex; align-items: center" class="content-rl content-top">
            <type-icon :width="48" :height="36" :type="databaseData?.dbType" />
            <span>{{ databaseData?.tableSchema }}</span>
          </div>
        </template>
        <template #subTitle>
          <div class="content-rl">数据表列表</div>
        </template>
        <template #headerRight>
          <!-- <a-button
            type="primary"
          >
            新增资产
          </a-button> -->
        </template>
        <a-tabs v-model:activeKey="activeKey">
          <template #leftExtra>
            <!-- 左侧占位符 -->
            <span style="color: transparent"> 22 </span>
          </template>
          <a-tab-pane key="1" tab="数据模式">
            <div
              style="margin-bottom: 10px; display: flex; align-items: center; padding-left: 12px"
            >
              <span>
                <a-input
                  @change="handleSwitchChange"
                  allow-clear
                  v-model:value="tableName"
                  placeholder="在数据表中查找"
                  style="width: 400px; margin-right: 20px"
                />
              </span>
              <a-switch
                v-model:checked="state.inuse"
                checked-children="有效"
                un-checked-children="无效"
                checkedValue="Y"
                unCheckedValue="N"
                @change="handleSwitchChange"
              />
            </div>
            <Table
              :columns="columns"
              :getData="getTableListByDb"
              :autoRequest="false"
              ref="tableRef"
              :searchFormState="searchFormData"
              style="padding: 0 12px"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'tableName'">
                  <span :style="{ color: record?.inuse === 'Y' ? 'black' : 'red' }"
                    >{{ record?.tableName }}{{ record?.inuse === 'Y' ? '' : '（已删除）' }}</span
                  >
                </template>
                <template v-if="column.key === 'customComment'">
                  <EditOutlined
                    @click.prevent="openEditCommentModal(record)"
                    v-if="record.inuse === 'Y'"
                  />
                  {{ record?.customComment }}
                </template>
                <template v-if="column.key === 'operate'">
                  <a-space class="action-container">
                    <a @click.prevent="toDetail(record)">查看详情</a>
                  </a-space>
                </template>
              </template>
            </Table></a-tab-pane
          >
        </a-tabs>
      </cardBox>
    </div>

    <!-- Edit Table Modal -->
    <edit-table-modal
      v-model:visible="editModalVisible"
      :table-data="currentTable"
      :database-data="databaseData"
      @success="handleEditSuccess"
    />

    <!-- Edit Comment Modal -->
    <a-modal
      v-model:visible="commentModalVisible"
      title="编辑注释"
      @ok="handleCommentSubmit"
      :confirmLoading="submitLoading"
      @cancel="commentModalVisible = false"
    >
      <a-form :model="commentForm" :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }">
        <a-form-item label="表名" name="customComment">
          {{ currentRecord.tableName }}
        </a-form-item>
        <a-form-item label="自定义注释" name="customComment">
          <a-input v-model:value="commentForm.customComment" placeholder="请输入自定义注释" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch, computed } from 'vue'
import { Table } from '@fs/fs-components'
import cardBox from '@/components/card-box/card-box.vue'
import { getTableListByDb } from '@/api/assetmanager/metadatamanage/metadatamanage'
import { tableinfoUpdate } from '@/api/services/data-asset/hbase-management'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import TypeIcon from '@/views/data-asset/metadata-query/components/type-icon.vue'
import { EditOutlined } from '@ant-design/icons-vue'

const router = useRouter()

const props = defineProps<{
  databaseId?: string
  databaseData?: Record<string, any>
}>()

const emit = defineEmits(['refresh'])

const searchForm = ref<any>({
  labelId: null,
})
const tableRef = ref<any>(null)
const state = ref({
  inuse: 'Y',
})
const activeKey = ref('1')
const tableName = ref('')

const columns = ref<any[]>([
  {
    dataIndex: 'tableName',
    title: '表名',
    key: 'tableName',
  },
  {
    dataIndex: 'tableComment',
    title: '原表注释',
    key: 'tableComment',
  },
  {
    dataIndex: 'customComment',
    title: '自定义注释',
    key: 'customComment',
  },
  {
    dataIndex: 'engine',
    title: '数据库引擎',
    key: 'engine',
  },
  {
    dataIndex: 'ip',
    title: 'ip',
    key: 'ip',
  },
  {
    dataIndex: 'port',
    title: '端口',
    key: 'port',
  },
  {
    dataIndex: 'operate',
    title: '操作',
    key: 'operate',
    width: 120,
  },
])

const editModalVisible = ref(false)
const currentTable = ref<Record<string, any>>({})
const selectLabel = ref<Record<string, any>>({})

// 编辑注释相关变量
const commentModalVisible = ref(false)
const commentForm = ref({
  customComment: '',
})
const currentRecord = ref<Record<string, any>>({})
const submitLoading = ref(false)

const searchFormData = computed(() => {
  const ids = props?.databaseId?.split('_') || []
  return {
    datasourceId: ids[0] ? ids[0] : null,
    databaseName: props?.databaseData?.tableDbname
      ? props?.databaseData?.tableDbname
      : props?.databaseData?.tableSchema,
    tableSchema: props?.databaseData?.tableSchema ? props?.databaseData?.tableSchema : null,
    inuse: state.value.inuse,
    tableName: tableName.value,
  }
})

const fetchTableData = async () => {
  if (!props?.databaseId) return
  try {
    searchForm.value.databaseId = props?.databaseId
    setTimeout(() => {
      tableRef.value?.getTableData()
    }, 20)
  } catch (error) {
    console.error('获取数据表列表失败:', error)
  }
}

const handleSwitchChange = () => {
  fetchTableData()
}

const toDetail = (record: any) => {
  router.push({
    name: 'metadata-detail',
    query: {
      ...searchFormData.value,
      tableName: record.tableName,
      tableComment: record.tableComment,
    },
  })
}

const handleEditSuccess = () => {
  fetchTableData()
  emit('refresh')
}

// 打开编辑注释弹窗
const openEditCommentModal = (record: any) => {
  currentRecord.value = record
  commentForm.value.customComment = record.customComment || ''
  commentModalVisible.value = true
}

// 提交编辑注释
const handleCommentSubmit = async () => {
  submitLoading.value = true
  try {
    const ids = props?.databaseId?.split('_') || []
    // TODO: 这里应该添加保存注释的API调用
    await tableinfoUpdate({
      customComment: commentForm.value.customComment,
      tableName: currentRecord.value.tableName,
      tableDbname: props?.databaseData?.tableSchema,
      tableSchema: props?.databaseData?.tableSchema,
      dbid: ids[0] ? ids[0] : null,
    })
    message.success('更新注释成功')
    commentModalVisible.value = false
    fetchTableData() // 刷新表格数据
  } catch (error) {
    console.error('更新注释失败:', error)
  } finally {
    submitLoading.value = false
  }
}

watch(
  () => props.databaseData,
  (newVal) => {
    if (newVal) {
      searchForm.value.labelId = null
      selectLabel.value = {}
      fetchTableData()
    }
  },
  { immediate: true },
)
</script>

<style lang="less" scoped>
.database-table {
  height: 100%;
  .header-div {
    display: flex;
    align-items: center;
    height: 48px;
    padding: 0px 12px 0 20px;
    border-bottom: 1px solid #0000001a;
  }
  .content-div {
    height: calc(100% - 48px);
  }
}
.content-rl {
  padding: 0 12px;
}
.content-top {
  padding-top: 12px;
}
:deep(.fs-table .search-btn) {
  display: none;
}
:deep(.ant-btn:hover) {
  .clear-icon {
    opacity: 1 !important;
  }
}
</style>
