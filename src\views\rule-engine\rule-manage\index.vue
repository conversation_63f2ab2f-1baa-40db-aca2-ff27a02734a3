<template>
  <main class="rule-manage-container">
    <Title title="数据模型规则管理" desc="编辑、创建数据模型的规则，查询规则列表">
      <template #right>
        <Space>
          <Button type="primary" @click="handleAdd">新增</Button>
          <Button type="primary" @click="fetchData" :loading="loading">刷新</Button>
        </Space>
      </template>
    </Title>
    <div flex="~ justify-between">
      <Space>
        <Button type="text" @click="handleBack">
          <template #icon>
            <ArrowLeftOutlined />
          </template>
        </Button>
        <h1>规则列表</h1>
      </Space>
    </div>
    <Table :columns="columns" :dataSource="dataSource" :loading="loading">
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <div>
            <Button type="link" size="small" @click="handleEdit(record)">编辑</Button>
            <Button type="link" size="small" @click="handleApply(record)">应用</Button>
            <Button type="link" size="small" @click="handleTest(record)">测试</Button>
            <Popconfirm title="确定删除吗？" @confirm="handleDelete(record)">
              <Button type="link" size="small" danger>删除</Button>
            </Popconfirm>
          </div>
        </template>
      </template>
    </Table>
    <AddRuleModal
      v-model:open="addModalVisible"
      :data="currentRule"
      :title="!!currentRule ? '编辑规则' : '新增规则'"
      @ok="handleAddOk"
      @cancel="handleCancel"
    />
    <ApplyModal
      v-model:open="applyModalVisible"
      :data="currentRule"
      @ok="handleApplyOk"
      @cancel="handleApplyCancel"
    />
    <Modal
      v-model:open="testModalVisible"
      title="测试规则"
      :confirmLoading="testConfirmLoading"
      @cancel="handleTestCancel"
    >
      <Form
        ref="testFormRef"
        :model="testFormState"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 20 }"
        :rules="testRules"
      >
        <FormItem
          v-for="param in currentRule?.paramsInfo"
          :key="param.keyName"
          :label="param.name"
          :name="[param.key]"
          :rules="[{ required: true, message: `请输入${param.name}` }]"
        >
          <Select
            v-if="param.valueType === 'BOOLEAN'"
            v-model:value="testFormState[param.key]"
            :placeholder="`请选择${param.name}`"
          >
            <SelectOption :value="true">true</SelectOption>
            <SelectOption :value="false">false</SelectOption>
          </Select>
          <DatePicker
            v-else-if="param.valueType === 'DATE'"
            v-model:value="testFormState[param.key]"
            :placeholder="`请选择${param.name}`"
            show-time
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 100%"
          />
          <InputNumber
            v-else-if="param.valueType === 'NUMBER'"
            v-model:value="testFormState[param.key]"
            :placeholder="`请输入${param.name}`"
            style="width: 100%"
          />
          <Input
            v-else
            v-model:value="testFormState[param.key]"
            :placeholder="`请输入${param.name}`"
          />
        </FormItem>
        <FormItem label="测试结果" v-if="testResult">
          {{ testResult?.result }}
        </FormItem>
      </Form>
      <template #footer>
        <div style="text-align: right">
          <Button @click="handleTestCancel">取消</Button>
          <Button
            type="primary"
            :loading="testConfirmLoading"
            @click="handleTestOk"
            style="margin-left: 8px"
          >
            测试
          </Button>
        </div>
      </template>
    </Modal>
  </main>
</template>

<script setup lang="ts">
import {
  Button,
  message,
  Popconfirm,
  Space,
  Table,
  Modal,
  Form,
  Input,
  Select,
  DatePicker,
  InputNumber,
} from 'ant-design-vue'
import type { FormInstance } from 'ant-design-vue'
import { useRoute } from 'vue-router'
import type { RuleItem } from '../data'
import { deleteRule, getRuleList, applyRule } from '../service'
import { columns } from './columns'
import AddRuleModal from '../components/add-rule-modal.vue'
import { ArrowLeftOutlined } from '@ant-design/icons-vue'
import ApplyModal from '../components/apply-modal.vue'

const route = useRoute()
const projectId = route.query.projectId
const dataSource = ref<RuleItem[]>([])
const loading = ref(false)
const addModalVisible = ref(false)
const applyModalVisible = ref(false)
const currentRule = ref<RuleItem>()
const testResult = ref<any>()
const handleAdd = () => {
  addModalVisible.value = true
}
const router = useRouter()
const handleBack = () => {
  router.back()
}
const handleEdit = (record: RuleItem) => {
  currentRule.value = record
  if (currentRule.value?.ruleType === undefined) currentRule.value.ruleType = 0
  addModalVisible.value = true
}
const handleApply = (record: RuleItem) => {
  currentRule.value = record
  applyModalVisible.value = true
}
const handleAddOk = async () => {
  addModalVisible.value = false
  currentRule.value = void 0
  await fetchData()
}

const handleTest = (record: RuleItem) => {
  currentRule.value = record
  testModalVisible.value = true
  testFormState.value = {}
}

const handleCancel = () => {
  currentRule.value = void 0
  addModalVisible.value = false
}
const handleDelete = async (record: RuleItem) => {
  try {
    const res = await deleteRule(record.autoId!)
    if (res.code === '000000') {
      message.success('删除成功')
      await fetchData()
    } else {
      message.error(res.msg || '删除失败')
    }
  } catch (error) {
    console.error('删除失败:', error)
  }
}
const handleApplyOk = () => {
  applyModalVisible.value = false
  currentRule.value = void 0
}
const handleApplyCancel = () => {
  applyModalVisible.value = false
  currentRule.value = void 0
}
const fetchData = async () => {
  if (!projectId) {
    message.warning('请先选择项目')
    return
  }
  try {
    loading.value = true
    const res = await getRuleList(projectId as string)
    dataSource.value = res.data || []
    loading.value = false
  } catch (error) {
    console.error('获取规则列表失败:', error)
  } finally {
    loading.value = false
  }
}
onMounted(() => {
  fetchData()
})

const FormItem = Form.Item
const testModalVisible = ref(false)
const testConfirmLoading = ref(false)
const testFormRef = ref<FormInstance>()
const testFormState = ref<Record<string, any>>({})
const SelectOption = Select.Option

const testRules = computed(() => {
  const rules: Record<string, any> = {}
  currentRule.value?.paramsInfo?.forEach((param) => {
    rules[param.key] = [{ required: true, message: `请输入${param.name}` }]
  })
  return rules
})

const handleTestOk = async () => {
  try {
    await testFormRef.value?.validate()
    testConfirmLoading.value = true
    const res = await applyRule({
      ruleId: currentRule.value?.autoId,
      projectId: currentRule.value?.projectId,
      args: testFormState.value,
    })

    if (res.code === '000000') {
      message.success('测试成功')
      testResult.value = res.data
    } else {
      message.error(res.msg || '测试失败')
      testResult.value = null
    }
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    testConfirmLoading.value = false
  }
}

const handleTestCancel = () => {
  testFormRef.value?.resetFields()
  testFormState.value = {}
  testModalVisible.value = false
  currentRule.value = void 0
  testResult.value = null
}
</script>

<style scoped>
.rule-manage-container {
}
</style>
