import router, { routeMap } from '@/router/core'
import { type RouteRecordRaw, type RouteRecordNormalized } from 'vue-router'
import type { UserMenu } from '@fs/fs-components/src/components/layout/layout'
import defaultRoutes from '@/router/core/base'
/**
 * 初始化菜单配置
 */
const MenuData = function initMenu(
  menu: UserMenu[],
  userMenu: Record<string, any>,
  userStore: any,
  callback?: { (): void; (): void; (): any },
) {
  const userMenuMap: Record<string, UserMenu> = {}
  const generateRoutes = (menus: UserMenu[] = []): RouteRecordRaw[] => {
    return menus.map((item) => {
      const { menuUrl, childrens, menuName, menuId, levelIds, menuIcon, actionName, menuType } =
        item
      if (menuUrl) {
        userMenuMap[item.menuUrl] = item
      }
      return {
        path: `${menuUrl || menuId}`,
        name: menuUrl || menuId,
        component: routeMap[item.menuUrl]?.component,
        children: childrens?.length ? generateRoutes(childrens) : [],
        actionName: actionName,
        menuType: menuType,
        meta: {
          title: menuName,
          menuId: menuId,
          menuKeyPath: levelIds?.slice(0, -1).split('.') || [],
          icon: menuIcon || routeMap[item.menuUrl]?.meta?.icon,
          clonePermission: childrens?.length
            ? generateRoutes(childrens)
                .map((item: any) => {
                  return item.actionName
                })
                .filter((item) => item !== undefined)
            : [],
        },
      }
    })
  }

  // 动态生成路由
  const routes = generateRoutes(menu)
  const interfaceData = flattenTree(routes) // 铺平的接口返回菜单
  console.log('🚀 ~ interfaceData:', interfaceData)
  userStore.interfaceData = interfaceData

  const sourceObject = router.getRoutes() // 本地路由菜单

  // const pathData: RouteRecordNormalized[] = []

  // const interfaceMap = new Map()
  // interfaceData.forEach((obj: { name: string; menuType: number }) => {
  //   ;(obj.menuType === 1 || obj.menuType === 3 || obj.menuType === 4) &&
  //     interfaceMap.set(obj.name, obj)
  // })

  sourceObject.forEach((obj) => {
    const val = interfaceData.find((item) => item.path === obj.path)
    if (val) {
      obj.meta = {
        ...val.meta,
        isSecondary: obj.meta?.isSecondary,
        title: val.meta?.title || obj.meta?.title,
      }
    }
  })

  // 对比路由表完的数据,筛选保留所有菜单和页签数据, 本地路由表没有的数据, 不追加路由表
  // const filteredData = pathData
  //   .map((root: any) => filterMenuTypeOne(root))
  //   .filter((node) => {
  //     return node !== null
  //   })

  // const namesArray = defaultRoutes.map((route) => route.name)
  // 清除所有现有的路由, 除layout页面注册路由
  // router.getRoutes().forEach((route) => {
  //   if (route.name && route.name != 'layout' && !namesArray.includes(route.name)) {
  //     router.removeRoute(route.name)
  //   }
  // })

  // filteredData.forEach((item: any) => {
  //   const foundObject = sourceObject.find((obj) => obj.name === item.name)
  //   // Update path
  //   item.path = foundObject?.path || item.path
  //   // Merge meta information
  //   item.meta = { ...item.meta, ...foundObject?.meta }
  //   // Clear children array to prevent duplication
  //   item.children = []

  //   // Add route to router
  //   router.addRoute('layout', item)
  // })

  // 保存菜单数据
  userMenu.value = menu
  // 跳转对应路由
  callback && callback()
}

const flattenTree = (nodes: any[]) => {
  return nodes.reduce((acc: any[], node: any) => {
    if (
      node.menuType === 1 ||
      node.menuType === 3 ||
      node.menuType === 4 ||
      node.isChildrenMenu === true
    ) {
      acc.push(node)
    }
    if (node.children) {
      acc.push(...flattenTree(node.children))
    }
    return acc
  }, [])
}

// 递归函数来筛选所有 menuType 等于 1 的菜单节点及其直接子节点   1为菜单 2 是权限 3 页签
function filterMenuTypeOne(node: UserMenu) {
  // 遍历数组类型为菜单和为子菜单数据返回
  if (
    node.menuType === 1 ||
    node.menuType === 3 ||
    node.menuType === 4 ||
    node.isChildrenMenu === true
  ) {
    const filteredChildren =
      node.children?.filter((child: { component: undefined; menuType: number }) => {
        return (
          (child.menuType === 1 ||
            child.menuType === 3 ||
            node.menuType === 4 ||
            node.isChildrenMenu === true) &&
          child.component != undefined
        )
      }) ?? []
    return { ...node, children: filteredChildren }
  }
  if (node.children) {
    const filteredChildren: UserMenu[] = []
    node.children.forEach((child: UserMenu) => {
      const filteredChild = filterMenuTypeOne(child)
      if (filteredChild) {
        filteredChildren.push(filteredChild)
      }
    })
    return filteredChildren.length > 0 ? { ...node, children: filteredChildren } : null
  }

  return null
}

export default MenuData
