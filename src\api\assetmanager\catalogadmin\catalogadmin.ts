import service from '@/api'
import { MOCK_DATA } from './catalogadmin-mock'

/**
 *  - CA001-新增资产目录
 * @param {boolean} parentId - 父级目录ID
 * @param {string} name - 目录名称
 * @param {string} description - 目录描述
 * @param {string} opUser -
 * @param {string} opUserName -
 * @returns {any} - 返回一个解析为响应数据的Promise
 */

export async function addDataAssetsDirectory(
  data: {
    parentId?: boolean
    name: string
    description?: string
  },
  headers: {
    opUser?: string
    opUserName?: string
  },
): Promise<any> {
  return service(
    {
      method: 'POST',
      url: '/assetManager/catalogAdmin/addDataAssetsDirectory',
      data,
      headers,
    },
    MOCK_DATA.addDataAssetsDirectory,
  )
}

/**
 *  - CA002-修改资产目录
 * @param {boolean} id - 自增主键
 * @param {string} parentId - 父级目录ID
 * @param {string} name - 目录名称
 * @param {string} description - 目录描述
 * @param {string} opUser -
 * @param {string} opUserName -
 * @returns {any} - 返回一个解析为响应数据的Promise
 */

export async function updateDataAssetsDirectory(
  data: {
    id: boolean
    parentId?: string
    name: string
    description?: string
  },
  headers: {
    opUser?: string
    opUserName?: string
  },
): Promise<any> {
  return service(
    {
      method: 'POST',
      url: '/assetManager/catalogAdmin/updateDataAssetsDirectory',
      data,
      headers,
    },
    MOCK_DATA.updateDataAssetsDirectory,
  )
}

/**
 *  - CA003-删除资产目录
 * @param {boolean} id - 主键
 * @param {string} opUser -
 * @param {string} opUserName -
 * @returns {any} - 返回一个解析为响应数据的Promise
 */

export async function delDataAssetsDirectory(
  data: {
    id: number
  },
  headers: {
    opUser?: string
    opUserName?: string
  },
): Promise<any> {
  return service(
    {
      method: 'POST',
      url: '/assetManager/catalogAdmin/delDataAssetsDirectory',
      data,
      headers,
    },
    MOCK_DATA.delDataAssetsDirectory,
  )
}

/**
 *  - CA004-查询目录树形结构
 * @param {boolean} id - undefined
 * @param {boolean} parentId - undefined
 * @param {string} name - undefined
 * @param {string} opUser - undefined
 * @param {string} opUserName - undefined
 * @returns {any} - 返回一个解析为响应数据的Promise
 */

export async function getListDataAssetsDirectory(
  data: {
    id?: boolean
    parentId?: boolean
    name?: string
  },
  headers: {
    opUser?: string
    opUserName?: string
  },
): Promise<{
  code: string
  msg: string
  data: Array<{
    id: number
    parentId: number
    name: string
    description: string
    createdTime: string
    createdUser: string
    updatedTime: string
    updatedUser: string
  }>
}> {
  return service(
    {
      method: 'GET',
      url: '/assetManager/catalogAdmin/getListDataAssetsDirectory',
      params: data,
      headers,
    },
    MOCK_DATA.getListDataAssetsDirectory,
  )
}
