<template>
  <a-input v-model:value="filterValue" placeholder="搜索列表" allow-clear style="margin-top: 10px">
    <template #prefix>
      <SearchOutlined />
    </template>
  </a-input>
  <div style="margin: 10px 0">
    <a-checkbox
      v-model:checked="state.checkAll"
      :indeterminate="state.indeterminate"
      @change="onCheckAllChange"
    >
      全部选中
    </a-checkbox>
  </div>
  <a-checkbox-group v-model:value="modelValue" style="width: 100%">
    <a-row>
      <a-col :span="24" v-for="item in filterOptions" :key="item.value" style="margin-bottom: 10px">
        <a-checkbox :value="item.value">{{ item.label }}</a-checkbox>
      </a-col>
    </a-row>
  </a-checkbox-group>
</template>

<script setup lang="ts">
import { computed, reactive, ref, watch } from 'vue'
import { SearchOutlined } from '@ant-design/icons-vue'

const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
})

const modelValue = defineModel({
  default: () => [],
})
const filterValue = ref('')
const state = reactive({
  indeterminate: false,
  checkAll: false,
})

const filterOptions = computed(() => {
  return props.data.options.filter((item: any) => {
    return item.label.toLowerCase().includes(filterValue.value.toLowerCase())
  })
})

watch(
  () => modelValue.value,
  (val: string[]) => {
    state.indeterminate = !!val.length && val.length < filterOptions.value.length
    state.checkAll = val.length === filterOptions.value.length
  },
)

const onCheckAllChange = (e: any) => {
  modelValue.value = e.target.checked ? filterOptions.value.map((item: any) => item.value) : []
  Object.assign(state, {
    indeterminate: false,
  })
}
</script>

<style lang="less" scoped></style>
