import service from '@/api'
import { MOCK_DATA } from './indexmanage-mock'

/**
 * 新增接口 - 新增指标分组
 * @param {string} parentId - 父节点ID
 * @param {string} groupName - 分组名称
 * @param {string} remarks - 备注
 * @param {string} opUser - 操作人
 * @param {string} opUserName - 操作人名称
 * @param {string} tenantId - 租户编号
 * @returns {any} - 返回一个解析为响应数据的Promise
 */

export async function addNewIndexGroup(
  data: {
    parentId?: string
    groupName: string
    remarks?: string
  },
  headers?: {
    opUser?: string
    opUserName?: string
    tenantId?: string
  },
): Promise<{
  code: string
  msg: string
  data: Array<{ userId: string; userName: string }>
}> {
  return service(
    {
      method: 'POST',
      url: '/indexManage/indexManage/addNewIndexGroup',
      data,
      headers,
    },
    MOCK_DATA.addNewIndexGroup,
  )
}

/**
 * 新增接口 - 分页查询指标分组
 * @param {string} groupName - 分组名称
 * @param {number} status - 状态:1有效0无效
 * @param {number} pageIndex - 页码
 * @param {number} pageSize - 页大小
 * @param {string} opUser - 操作人
 * @param {string} opUserName - 操作人名称
 * @param {string} tenantId - 租户编号
 * @returns {any} - 返回一个解析为响应数据的Promise
 */

export async function getIndexGroupListByPage(
  data: {
    groupName?: string
    status?: number
    pageIndex: number
    pageSize: number
  },
  headers?: {
    opUser?: string
    opUserName?: string
    tenantId?: string
  },
): Promise<{
  code: string
  msg: string
  data: {
    records: Array<{
      groupId: string
      groupName: string
      status: number
      remarks: string
      tenantId: string
      createUser: string
      createUsername: string
      createTime: string
      updateUser: string
      updateUsername: string
      updateTime: string
    }>
  }
}> {
  return service(
    {
      method: 'GET',
      url: '/indexManage/indexManage/getIndexGroupListByPage',
      params: data,
      headers,
    },
    MOCK_DATA.getIndexGroupListByPage,
  )
}

/**
 * 新增接口 - 修改指标分组
 * @param {string} parentId - 父节点ID
 * @param {string} groupId - 指标分组主键
 * @param {string} groupName - 分组名称
 * @param {number} status - 状态:1有效0无效
 * @param {string} remarks - 备注
 * @param {string} opUser - 操作人
 * @param {string} opUserName - 操作人名称
 * @param {string} tenantId - 租户编号
 * @returns {any} - 返回一个解析为响应数据的Promise
 */

export async function modifyIndexGroupData(
  data: {
    parentId?: string
    groupId: string
    groupName?: string
    status?: number
    remarks?: string
  },
  headers?: {
    opUser?: string
    opUserName?: string
    tenantId?: string
  },
): Promise<{
  code: string
  msg: string
  data: undefined
}> {
  return service(
    {
      method: 'POST',
      url: '/indexManage/indexManage/modifyIndexGroupData',
      data,
      headers,
    },
    MOCK_DATA.modifyIndexGroupData,
  )
}

/**
 * 新增接口 - 获取单个指标分组数据
 * @param {string} groupId - 指标分组主键
 * @param {string} opUser - 操作人
 * @param {string} opUserName - 操作人名称
 * @param {string} tenantId - 租户编号
 * @returns {any} - 返回一个解析为响应数据的Promise
 */

export async function getSingleIndexGroupDataDtl(
  data: {
    groupId: string
  },
  headers?: {
    opUser?: string
    opUserName?: string
    tenantId?: string
  },
): Promise<{
  code: string
  msg: string
  data: {
    groupId: string
    groupName: string
    status: number
    remarks: string
    tenantId: string
    createUser: string
    createUsername: string
    createTime: string
    updateUser: string
    updateUsername: string
    updateTime: string
  }
}> {
  return service(
    {
      method: 'GET',
      url: '/indexManage/indexManage/getSingleIndexGroupDataDtl',
      params: data,
      headers,
    },
    MOCK_DATA.getSingleIndexGroupDataDtl,
  )
}

/**
 * 新增接口 - 新增指标数据
 * @param {string} formHistoryId - 指标配置模板版本编号
 * @param {string} indexName - 指标名称
 * @param {string} groupId - 分组编号
 * @param {string} parentId - 指标父id
 * @param {string} calFormula - 计算公式/规则逻辑
 * @param {number} dataSourceId - 数据源编号
 * @param {string} formData - 指标配置数据
 * @param {Object} basicRuleList - 指标计算规则列表
 * @param {string} remarks - 描述
 * @param {string} opUser - 操作人
 * @param {string} opUserName - 操作人名称
 * @param {string} tenantId - 租户编号
 * @returns {any} - 返回一个解析为响应数据的Promise
 */

export async function addNewIndexData(
  data: {
    formHistoryId: string
    indexName: string
    groupId?: string
    parentId?: string
    calFormula?: string
    dataSourceId?: number
    formData?: string
    basicRuleList?: Array<{
      ruleType: number
      ruleText: string
      ruleScript?: string
    }>
    remarks?: string
  },
  headers?: {
    opUser?: string
    opUserName?: string
    tenantId?: string
  },
): Promise<{
  code: string
  msg: string
  data: undefined
}> {
  return service(
    {
      method: 'POST',
      url: '/indexManage/indexManage/addNewIndexData',
      data,
      headers,
    },
    MOCK_DATA.addNewIndexData,
  )
}

/**
 * 新增接口 - 获取指标数据详情
 * @param {string} autoId - 指标基础数据主键
 * @param {string} opUser - 操作人
 * @param {string} opUserName - 操作人名称
 * @param {string} tenantId - 租户编号
 * @returns {any} - 返回一个解析为响应数据的Promise
 */

export async function getIndexDataDtl(
  data: {
    autoId: string
  },
  headers?: {
    opUser?: string
    opUserName?: string
    tenantId?: string
  },
): Promise<{
  code: string
  msg: string
  data: {
    autoId: string
    groupId: string
    parentId: string
    formDataId: string
    indexName: string
    calFormula: string
    formData: string
    formHistoryId: string
    dataSourceId: string
    remarks: string
    status: number
    revision: number
    isDeleted: number
    isRelease: number
    currentVersion: number
    tenantId: string
    basicRuleList: Array<{
      basicRuleId: string
      basicDataId: string
      ruleType: number
      ruleText: string
      ruleScript: string
      databaseName: string
      createTime: string
    }>
    createUser: string
    createUsername: string
    createTime: string
    updateUser: string
    updateUsername: string
    updateTime: string
    dynamic: number
    dynamicParam: string
  }
}> {
  return service(
    {
      method: 'GET',
      url: '/indexManage/indexManage/getIndexDataDtl',
      params: data,
      headers,
    },
    MOCK_DATA.getIndexDataDtl,
  )
}

/**
 * 新增接口 - 编辑指标数据
 * @param {string} autoId - 指标基础数据主键
 * @param {string} indexName - 指标名称
 * @param {string} groupId - 分组编号
 * @param {string} parentId - 指标父id
 * @param {string} calFormula - 计算公式/规则逻辑
 * @param {string} formData - 指标配置数据
 * @param {number} revision - 乐观锁
 * @param {Object} basicRuleList - 指标计算规则列表
 * @param {string} remarks - 描述
 * @param {number} isDeleted - 是否启用:1是0否
 * @param {string} opUser - 操作人
 * @param {string} opUserName - 操作人名称
 * @param {string} tenantId - 租户编号
 * @returns {any} - 返回一个解析为响应数据的Promise
 */

export async function modifyIndexData(
  data: {
    autoId: string
    indexName?: string
    groupId?: string
    parentId?: string
    calFormula?: string
    formData?: string
    revision: number
    basicRuleList?: Array<{
      ruleType?: number
      ruleText: string
      ruleScript?: string
    }>
    remarks?: string
    isDeleted?: number
  },
  headers?: {
    opUser?: string
    opUserName?: string
    tenantId?: string
  },
): Promise<{
  code: string
  msg: string
  data: undefined
}> {
  return service(
    {
      method: 'POST',
      url: '/indexManage/indexManage/modifyIndexData',
      data,
      headers,
    },
    MOCK_DATA.modifyIndexData,
  )
}

/**
 * 新增接口 - 发布指标数据
 * @param {string} autoId - 指标基础数据主键
 * @param {string} opUser - 操作人
 * @param {string} opUserName - 操作人名称
 * @param {string} tenantId - 租户编号
 * @returns {any} - 返回一个解析为响应数据的Promise
 */

export async function deployIndexData(
  data: {
    autoId: string
  },
  headers?: {
    opUser?: string
    opUserName?: string
    tenantId?: string
  },
): Promise<{
  code: string
  msg: string
  data: undefined
}> {
  return service(
    {
      method: 'POST',
      url: '/indexManage/indexManage/deployIndexData',
      data,
      headers,
    },
    MOCK_DATA.deployIndexData,
  )
}

/**
 * 新增接口 - 获取指标数据历史版本数据列表
 * @param {string} basicDataId - 指标基础数据编号
 * @param {string} startTime - 开始时间
 * @param {string} endTime - 结束时间
 * @param {number} pageIndex - 页码
 * @param {number} pageSize - 页大小
 * @param {string} opUser - 操作人
 * @param {string} opUserName - 操作人名称
 * @param {string} tenantId - 租户编号
 * @returns {any} - 返回一个解析为响应数据的Promise
 */

export async function getIndexHistoryDataListByPages(
  data: {
    basicDataId: string
    startTime?: string
    endTime?: string
    pageIndex: number
    pageSize: number
  },
  headers?: {
    opUser?: string
    opUserName?: string
    tenantId?: string
  },
): Promise<{
  code: string
  msg: string
  data: {
    records: Array<{
      historyId: string
      basicDataId: string
      groupId: string
      parentId: string
      formDataId: string
      indexName: string
      formHistoryId: string
      formData: string
      calFormula: string
      dataSourceId: string
      remarks: string
      version: number
      revision: number
      isDeleted: number
      tenantId: string
      createUser: string
      createUsername: string
      createTime: string
      updateUser: string
      updateUsername: string
      updateTime: string
    }>
  }
}> {
  return service(
    {
      method: 'GET',
      url: '/indexManage/indexManage/getIndexHistoryDataListByPages',
      params: data,
      headers,
    },
    MOCK_DATA.getIndexHistoryDataListByPages,
  )
}

/**
 * 新增接口 - 分页查询指标数据列表
 * @param {string} indexName - 指标名称
 * @param {string} groupId - 分组编号
 * @param {string} parentId - 父指标id
 * @param {string} formDataId - 指标配置数据id
 * @param {number} businessType - 业务类型
 * @param {string} formData - 指标配置数据query
 * @param {number} isDeleted - 是否启用:1是0否
 * @param {number} status - 状态:0=编辑中1=已提交
 * @param {string} startTime - 开始时间
 * @param {string} endTime - 结束时间
 * @param {number} isRelease - 是否已发布:1是0否
 * @param {number} pageIndex - 页码
 * @param {number} pageSize - 页大小
 * @param {string} opUser - 操作人
 * @param {string} opUserName - 操作人名称
 * @param {string} tenantId - 租户编号
 * @returns {any} - 返回一个解析为响应数据的Promise
 */

export async function getIndexDataListByPages(
  data: {
    indexName?: string
    groupId?: string
    parentId?: string
    formDataId?: string
    businessType?: number
    formData?: string
    isDeleted?: number
    status?: number
    startTime?: string
    endTime?: string
    isRelease?: number
    pageIndex: number
    pageSize: number
  },
  headers?: {
    opUser?: string
    opUserName?: string
    tenantId?: string
  },
): Promise<{
  code: string
  msg: string
  data: {
    records: Array<{
      autoId: string
      groupId: string
      parentId: string
      formDataId: string
      dataSourceId: number
      indexName: string
      calFormula: string
      remarks: string
      status: number
      revision: number
      isDeleted: number
      isRelease: number
      currentVersion: number
      tenantId: string
      createUser: string
      createUsername: string
      createTime: string
      updateUser: string
      updateUsername: string
      updateTime: string
    }>
  }
}> {
  return service(
    {
      method: 'GET',
      url: '/indexManage/indexManage/getIndexDataListByPages',
      params: data,
      headers,
    },
    MOCK_DATA.getIndexDataListByPages,
  )
}

/**
 *  - 查询分组树形结构
 * @param {string} groupName -
 * @param {string} opUser -
 * @param {string} opUserName -
 * @returns {any} - 返回一个解析为响应数据的Promise
 */

export async function getGroupTree(
  data: {
    groupName?: string
  },
  headers?: {
    opUser?: string
    opUserName?: string
  },
): Promise<any> {
  return service(
    {
      method: 'GET',
      url: '/indexManage/indexManage/getGroupTree',
      params: data,
      headers,
    },
    MOCK_DATA.getGroupTree,
  )
}

/**
 * 根据指标ID数据预览 - 根据指标ID数据预览
 * @param {string} indexId - 指标ID
 * @returns {any} - 返回一个解析为响应数据的Promise
 */

export async function reviewByIndexId(data: { indexId: string }): Promise<{
  code: string
  msg: string
  data: { cost: number; dataList: undefined }
}> {
  return service(
    {
      method: 'GET',
      url: '/indexManage/indexManage/reviewByIndexId',
      params: data,
    },
    MOCK_DATA.reviewByIndexId,
  )
}

/**
 *  - 查询指标下拉列表
 * @param {string} indexName -
 * @param {number} pageIndex -
 * @param {number} pageSize -
 * @returns {any} - 返回一个解析为响应数据的Promise
 */

export async function getIndexNameList(data: {
  indexName?: string
  pageIndex: number
  pageSize: number
}): Promise<any> {
  return service(
    {
      method: 'GET',
      url: '/indexManage/indexManage/getIndexNameList',
      params: data,
    },
    MOCK_DATA.getIndexNameList,
  )
}

/**
 * 新增接口 - 分页查询已发布指标数据列表
 * @param {string} indexName - 指标名称
 * @param {number} pageIndex - 页码
 * @param {number} pageSize - 页大小
 * @returns {any} - 返回一个解析为响应数据的Promise
 */

export async function getRealeaseIndexList(data: {
  indexName?: string
  pageIndex: number
  pageSize: number
}): Promise<{
  code: string
  msg: string
  data: {
    records: Array<{
      autoId: string
      groupId: string
      parentId: string
      formDataId: string
      dataSourceId: number
      indexName: string
      remarks: string
      status: number
      currentVersion: number
    }>
  }
}> {
  return service(
    {
      method: 'GET',
      url: '/indexManage/indexManage/getRealeaseIndexList',
      params: data,
    },
    MOCK_DATA.getRealeaseIndexList,
  )
}

/**
 * 新增接口 - 获取指标分组树列表
 * @returns {any} - 返回一个解析为响应数据的Promise
 */
export async function getIndexGroupTreeList() {
  return service({
    method: 'GET',
    url: '/indexManage/indexManage/getGroupTree',
  })
}

/**
 * 目录树响应数据结构
 */
export interface DirectoryTreeResponse {
  /** 响应状态码 */
  code: string
  /** 目录树数据数组 */
  data: DirectoryTreeNode[]
  /** 响应消息 */
  msg: string
  /** 处理结果状态 */
  result: string
  [property: string]: any
}

/**
 * 目录树根节点数据结构
 */
export interface DirectoryTreeNode {
  /** 资产数量 */
  assertCount: number
  /** 选中状态 */
  checked: string
  /** 子节点数组（数据库节点） */
  children: DatabaseNode[]
  /** 目录描述信息 */
  description: null | string
  /** 目录唯一标识ID */
  directoryId: string
  /** 目录名称 */
  name: string
  /** 节点类型（用于区分不同层级的节点） */
  nodeType: number
  /** 父节点ID（根节点为null） */
  parentId: null
  [property: string]: any
}

/**
 * 数据库节点数据结构
 */
export interface DatabaseNode {
  /** 资产数量 */
  assertCount: number
  /** 选中状态 */
  checked: string
  /** 子节点数组（表节点） */
  children: TableNode[]
  /** 数据库ID */
  databaseId: string
  /** 数据库名称 */
  databaseName: string
  /** 数据库类型（如MySQL、PostgreSQL等） */
  databaseType: string
  /** 数据源ID */
  datasourceId: string
  /** 数据源名称 */
  datasourceName: string
  /** 数据库描述信息 */
  description: null | string
  /** 所属目录ID */
  directoryId: string
  /** 数据库引擎类型 */
  engine: null | string
  /** 显示名称 */
  name: string
  /** 节点类型标识 */
  nodeType: number
  /** 父节点ID */
  parentId: string
  /** 数据表数量 */
  tableCount: number
  [property: string]: any
}

/**
 * 数据表节点数据结构
 */
export interface TableNode {
  /** 资产数量 */
  assertCount: number
  /** 选中状态 */
  checked: string
  /** 子节点数组（子表节点） */
  children: SubTableNode[]
  /** 表字段信息 */
  columns: null
  /** 配置值 */
  configValue: null
  /** 所属数据库ID */
  databaseId: string
  /** 所属数据库名称 */
  databaseName: string
  /** 数据库类型 */
  databaseType: string
  /** 表描述信息 */
  description: null | string
  /** 所属目录ID */
  directoryId: string
  /** 数据库引擎类型 */
  engine: null | string
  /** 显示名称 */
  name: string
  /** 节点类型标识 */
  nodeType: number
  /** 父节点ID */
  parentId: string
  /** 是否被选中 */
  selected: null
  /** 表注释说明 */
  tableComment: null | string
  /** 子表数量 */
  tableCount: number
  /** 表唯一标识ID */
  tableId: string
  /** 表名称 */
  tableName: string
  [property: string]: any
}

/**
 * 子表节点数据结构（第三层表节点）
 */
export interface SubTableNode {
  /** 资产数量 */
  assertCount: number
  /** 选中状态 */
  checked: string
  /** 子节点数组（最底层表节点） */
  children: LeafTableNode[]
  /** 表字段信息 */
  columns: null
  /** 配置值 */
  configValue: null
  /** 所属数据库ID */
  databaseId: string
  /** 所属数据库名称 */
  databaseName: string
  /** 数据库类型 */
  databaseType: string
  /** 表描述信息 */
  description: null | string
  /** 所属目录ID */
  directoryId: string
  /** 数据库引擎类型 */
  engine: null | string
  /** 显示名称 */
  name: string
  /** 节点类型标识 */
  nodeType: number
  /** 父节点ID */
  parentId: string
  /** 是否被选中 */
  selected: null
  /** 表注释说明 */
  tableComment: null | string
  /** 子表数量 */
  tableCount: number
  /** 表唯一标识ID */
  tableId: string
  /** 表名称 */
  tableName: string
  [property: string]: any
}

/**
 * 叶子表节点数据结构（最底层的表节点）
 */
export interface LeafTableNode {
  /** 选中状态 */
  checked: string
  /** 表字段信息 */
  columns: null
  /** 配置值 */
  configValue: null
  /** 所属数据库ID */
  databaseId: string
  /** 节点类型标识 */
  nodeType: number
  /** 是否被选中 */
  selected: null
  /** 表注释说明 */
  tableComment: null
  /** 表唯一标识ID */
  tableId: string
  /** 表名称 */
  tableName: string
  [property: string]: any
}

/**
 * 获取目录树数据
 * @param params - 请求参数
 * @param params.roleId - 角色ID，用于权限控制
 * @param headers - 请求头信息
 * @param headers.opUser - 操作用户ID
 * @param headers.opUserName - 操作用户名称
 * @returns {Promise<DirectoryTreeResponse>} 返回目录树数据的Promise
 */
export async function getDiretoryTree(headers?: {
  /** 操作用户ID */
  opUser?: string
  /** 操作用户名称 */
  opUserName?: string
}): Promise<DirectoryTreeResponse> {
  return service({
    method: 'GET',
    url: '/indexManage/dataSourceOptions/getDirectoryTree',
    headers,
  })
}
