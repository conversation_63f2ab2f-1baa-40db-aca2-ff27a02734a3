import axios from 'axios'
import { transformData } from '../tools/index.js'
import process from 'node:process'
import { getApi<PERSON>son, setApiJson } from '../http-servers/module/index.js'
import { generateDataFromList } from '../tools/index.js'

const args = process.argv.slice(2)
const moduleName = args[0]
const funcsName = args[1]
const funcsList = funcsName ? funcsName.split(' ') : []

/**
 * 异步函数，用于获取并处理数据。
 * @returns {Promise<void>} - 返回一个 Promise，表示函数执行完成
 * @throws {Error} - 如果获取或处理数据过程中发生错误，将抛出错误
 */
async function fetchAndProcessData() {
  try {
    let apiData = []
    apiData = await getApiJson()

    // 过滤对应模块
    if (moduleName) {
      apiData = apiData.filter((item) => item.moduleName === moduleName)
    }

    // 过滤模块中对应函数
    if (funcsName) {
      apiData[0].interfaceInfos = apiData[0].interfaceInfos.map((item) => {
        if (funcsList.includes(item.interfaceName)) {
          // upState 为 true 表示此函数需要更新
          item.upState = true
        }
        return item
      })
    }

    const promisesArray = []
    apiData.forEach((item) => {
      item.interfaceInfos.forEach((func) => {
        // 赋值默认请求参数
        const paramData = generateDataFromList(func.params)
        // 如果含有函数名称则只更新当前函数
        if ((funcsName && func.upState) || !funcsName) {
          promisesArray.push(requestAndProcessFunc(func, paramData))
        }
      })
    })

    // 等待所有 Promise 完成
    await Promise.all(promisesArray)
    // 保存更新后的 apiData 数据
    await setApiJson(apiData)
  } catch (err) {
    // 捕获错误，并打印错误信息
    console.error(
      '%c 🍪 生成record 失败: ',
      'font-size:12px;background-color: #6EC1C2;color:#fff;',
      err,
    )
  }
}

/**
 * 异步函数，用于发送请求并处理函数的响应数据。
 * @param {Object} func - 函数对象，包含函数的相关信息
 * @param {string} func.fullPath - 函数的完整路径
 * @param {string} func.httpMethodName - 函数的 HTTP 请求方法名
 * @returns {Promise<Array>} - 返回一个 Promise，解析为处理后的响应数据数组
 * @throws {Error} - 如果请求失败，将抛出错误
 */
async function requestAndProcessFunc(func, paramData) {
  try {
    // 发起请求，使用 axios 库发送 HTTP 请求
    const response = await axios({
      url: 'http://172.16.101.134:8112' + func.fullPath,
      method: func.httpMethodName,
      data: paramData,
    })

    // 调用 transformData 函数，将响应数据进行转换处理
    const result = transformData(response.data)

    // 将处理后的响应数据赋值给函数对象的 response 属性
    func.response = result
    // 返回处理后的响应数据数组
    return result
  } catch (error) {
    // msg：  ${error?.response?.data?.error}
    // 捕获错误，并打印错误信息
    console.error(`%c 🥝 函数${func.interfaceName}请求失败: http状态码 ${error.status} `)
  }
}

fetchAndProcessData()
