import moment from 'moment'

export const StatusList = {
  0: '试用',
  1: '正式',
  2: '离职',
  3: '开除',
}

/**
 * 获取用户状态
 * @param status
 */
export function getEmployeeStatus(status: keyof typeof StatusList) {
  return StatusList[status]
}

/**
 * 获取用户账户是否启用
 * @param enabled
 */
export function getIsEnabled(enabled: number) {
  return enabled ? '是' : '否'
}

/**
 * 获取标准时间格式 YYYY-MM-DD hh:mm:ss
 * @param time
 */
export function getStandardTime(time: string) {
  return moment(time).format('YYYY-MM-DD hh:mm:ss')
}

export const AreaList = {
  1: '省',
  2: '市',
  3: '区',
  4: '街道',
}

export function getAreaLevelName(level: keyof typeof AreaList) {
  return AreaList[level]
}

export const OAuthClientStatus = {
  0: '未开通',
  1: '正常使用',
  2: '禁用',
}

export function getOAuthClientStatus(status: keyof typeof OAuthClientStatus) {
  return OAuthClientStatus[status]
}

/**
 * 判断是否权限菜单
 * @param menuType
 */
export function isAuthMenu(menuType: string) {
  return menuType === '5' || menuType === '4'
}

/**
 * 判断是否根菜单
 * @param menuType
 */
export function isRootMenu(menuType: string) {
  return menuType === '0'
}

/**
 * 判断是否一级菜单
 * @param menuType
 */
export function isFirstMenu(menuType: string) {
  return menuType === '1'
}

/**
 * 判断是否下级菜单
 * @param menuType
 */
export function isSubMenu(menuType: string) {
  return menuType === '2'
}

/** * 获取显示文本，空文本或者null会被转换成 ”--“ */
export function getDisplayText(val: string | null | undefined) {
  if (val === '' || val == null) {
    return '--'
  } else {
    return val
  }
}
