<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Browser Version Check</title>
    <style>
      body {
        margin: 0;
        padding: 0;
        font-family: Arial, sans-serif;
        background-color: #f5f5f5;
        font-size: 14px;
        color: #111;
      }
      code {
        font-family: 'Courier New', Courier, monospace;
        color: #666;
        font-size: 12px;
      }
      .container {
        max-width: 600px;
        margin: 200px auto;
        background-color: #fff;
        border-radius: 5px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        padding: 20px;
      }
      h1 {
        color: #333;
        text-align: center;
      }
      .browser-info {
        margin-top: 20px;
      }
      .upgrade-link {
        display: block;
        text-align: center;
        margin-top: 20px;
      }
      .upgrade-link a {
        display: inline-block;
        padding: 10px 20px;
        background-color: #007bff;
        color: #fff;
        text-decoration: none;
        border-radius: 5px;
      }
      .upgrade-link a:hover {
        background-color: #0056b3;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>请升级浏览器</h1>

      <div class="browser-info">
        <p id="msg"></p>
        <p>当前浏览器版本：<code id="version"></code></p>
      </div>
      <div class="upgrade-link" id="upgrade-button" style="display: none">
        <a href="https://www.google.com/chrome/" id="upgrade-link"
          >立即升级至最新版 <span id="upgrade-version"></span
        ></a>
      </div>
    </div>
    <script>
      var UPGRADE_URL = {
        chrome: 'https://www.google.com/chrome/',
        firefox: 'http://www.firefox.com.cn/',
        edg: 'https://www.microsoft.com/zh-cn/edge',
        version: 'https://www.apple.com/hk/en/safari/',
      }
      document.addEventListener('DOMContentLoaded', function () {
        function checkBrowserVersion() {
          const userAgent = navigator.userAgent
          let version = 0
          let message = document.getElementById('msg')

          const checkVersion = (browser, minVersion) => {
            if (userAgent.includes(browser)) {
              document.getElementById('version').innerText = '' + userAgent
              document.getElementById('upgrade-version').innerText = browser.replace('/', '')

              const key = browser.replace('/', '').toLowerCase()

              document.getElementById('upgrade-link').href = UPGRADE_URL[key]

              const regex = new RegExp(browser + '(\\d+)')
              version = parseInt(userAgent.match(regex)[1])

              if (version < minVersion) {
                document.getElementById('upgrade-button').style.display = ''
                message.innerHTML = '您的浏览器版本过旧，可能影响网页的正常显示和安全性。'
              }
            }
          }

          checkVersion('Chrome/', 87)
          checkVersion('Firefox/', 78)
          checkVersion('Version/', 14)
          checkVersion('Edg/', 88)
        }

        checkBrowserVersion()
      })
    </script>
  </body>
</html>
