<template>
  <div class="tree-menu">
    <div class="tree-header">
      <h4>数据目录</h4>
      <a-button size="small" type="text" @click="refreshTree">
        <ReloadOutlined />
      </a-button>
    </div>

    <div class="tree-content">
      <a-spin :spinning="loading">
        <a-tree
          v-if="treeData.length > 0"
          :tree-data="processedTreeData"
          :field-names="fieldNames"
          :expanded-keys="expandedKeys"
          :selected-keys="selectedKeys"
          show-icon
          @select="onSelect"
          @expand="onExpand"
        >
          <template #icon="{ nodeType }">
            <FolderOutlined v-if="nodeType === 1" />
            <DatabaseOutlined v-else-if="nodeType === 2" />
            <TableOutlined v-else-if="nodeType === 3" />
            <span v-else-if="nodeType === 4" class="field-icon">📄</span>
          </template>

          <template #title="{ title, nodeType, tableCount, assertCount, originalData }">
            <div
              class="tree-node-title"
              :data-node-type="nodeType"
              @dblclick="handleDoubleClick(originalData)"
            >
              <span class="node-name" :title="title">{{ title }}</span>
              <span v-if="nodeType === 1 && assertCount > 0" class="node-count"
                >({{ assertCount }})</span
              >
              <span v-else-if="nodeType === 2 && tableCount > 0" class="node-count"
                >({{ tableCount }})</span
              >
            </div>
          </template>
        </a-tree>

        <div v-else class="empty-state">
          <a-empty description="暂无数据" />
        </div>
      </a-spin>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, nextTick } from 'vue'
import { message } from 'ant-design-vue'
import {
  FolderOutlined,
  DatabaseOutlined,
  TableOutlined,
  ReloadOutlined,
} from '@ant-design/icons-vue'
import {
  getDiretoryTree,
  type DirectoryTreeResponse,
  type DirectoryTreeNode,
} from '@/api/indexmanage/indexmanage/indexmanage'

// Emits
const emit = defineEmits<{
  /** 节点选择事件 */
  select: [node: any, selectedKeys: string[]]
  /** 表节点双击事件，用于插入SQL */
  tableDoubleClick: [
    tableInfo: {
      tableName: string
      databaseName: string
      tableId: string
    },
  ]
  /** 字段节点双击事件，用于插入SQL */
  fieldDoubleClick: [
    fieldInfo: {
      fieldName: string
      tableName: string
      databaseName: string
      tableId: string
      fieldType: string
      fieldComment: string
    },
  ]
  /** 树数据加载完成事件 */
  treeLoaded: []
}>()

// 响应式数据
const loading = ref(false)
const treeData = ref<DirectoryTreeNode[]>([])
const expandedKeys = ref<string[]>([]) // 展开的节点keys
const selectedKeys = ref<string[]>([]) // 选中的节点keys

// 树形组件配置
const fieldNames = {
  children: 'children',
  title: 'title',
  key: 'key',
}

// 处理树形数据，统一字段格式
const processedTreeData = computed(() => {
  const processNode = (node: any): any => {
    const processed: any = {
      ...node,
      originalData: node, // 保存原始数据
      key: getNodeKey(node),
      title: getNodeTitle(node),
      disabled: node.nodeType === 4, // 字段节点不可编辑
    }

    if (node.children && node.children.length > 0) {
      processed.children = node.children.map(processNode)
    }

    return processed
  }

  return treeData.value.map(processNode)
})

// 获取节点的唯一标识
const getNodeKey = (node: any): string => {
  if (node.nodeType === 1) {
    return `dir_${node.directoryId}`
  } else if (node.nodeType === 2) {
    return `db_${node.databaseId}`
  } else if (node.nodeType === 3) {
    return `table_${node.tableId}`
  }
  return `unknown_${Math.random()}`
}

// 获取节点显示名称
const getNodeTitle = (node: any): string => {
  if (node.nodeType === 1) {
    return node.name || '未命名目录'
  } else if (node.nodeType === 2) {
    return node.databaseName || '未命名数据库'
  } else if (node.nodeType === 3) {
    return node.tableName || '未命名表'
  } else if (node.nodeType === 4) {
    return node.fieldName || '未命名字段'
  }
  return '未知节点'
}

// 获取目录树数据
const fetchTreeData = async () => {
  try {
    loading.value = true
    const response: DirectoryTreeResponse = await getDiretoryTree()

    if (response.code === '000000') {
      treeData.value = response.data || []

      // 数据加载完成后，发射treeLoaded事件
      nextTick(() => {
        console.log('TreeMenu: 数据加载完成，发射treeLoaded事件')
        emit('treeLoaded')
      })
    } else {
      message.error(response.msg || '获取目录树失败')
      treeData.value = []
    }
  } catch (error) {
    console.error('获取目录树失败:', error)
    message.error('获取目录树失败')
    treeData.value = []
  } finally {
    loading.value = false
  }
}

// 节点选择事件
const onSelect = (keys: string[], info: any) => {
  selectedKeys.value = keys
  const selectedNode = info.node
  emit('select', selectedNode.originalData, keys)
}

// 节点展开事件
const onExpand = (keys: string[], info: any) => {
  expandedKeys.value = keys
  console.log('展开节点:', keys, info)
}

// 处理双击事件
const handleDoubleClick = (nodeData: any) => {
  // 表节点双击插入表名
  if (nodeData.nodeType === 3 && nodeData.tableName) {
    // 查找对应的数据库名称
    const databaseName = findDatabaseName(nodeData)

    const tableInfo = {
      tableName: nodeData.tableName,
      databaseName: databaseName,
      tableId: nodeData.tableId,
    }
    emit('tableDoubleClick', tableInfo)
  }

  // 字段节点双击插入字段名
  if (nodeData.nodeType === 4 && nodeData.fieldName) {
    // 查找对应的数据库名称和表名
    const databaseName = findDatabaseName(nodeData)
    const tableName = findTableName(nodeData)

    const fieldInfo = {
      fieldName: nodeData.fieldName,
      tableName: tableName,
      databaseName: databaseName,
      tableId: nodeData.tableId,
      fieldType: nodeData.fieldType || nodeData.columnType,
      fieldComment: nodeData.fieldComment || nodeData.comment,
    }
    emit('fieldDoubleClick', fieldInfo)
  }
}

// 查找表节点对应的数据库名称
const findDatabaseName = (node: any): string => {
  // 从原始数据中查找对应的数据库
  const findDatabase = (nodes: any[], targetTableId: string): string => {
    for (const node of nodes) {
      if (node.nodeType === 2 && node.children) {
        // 检查这个数据库下是否有目标表
        const hasTable = node.children.some(
          (child: any) => child.nodeType === 3 && child.tableId === targetTableId,
        )
        if (hasTable) {
          return node.databaseName
        }
      }

      // 递归查找子节点
      if (node.children) {
        const result = findDatabase(node.children, targetTableId)
        if (result) return result
      }
    }
    return ''
  }
  if (node.nodeType === 2) return node.databaseName
  return findDatabase(treeData.value, node.tableId) || '未知数据库'
}

// 查找字段节点对应的表名称
const findTableName = (node: any): string => {
  // 从原始数据中查找对应的表
  const findTable = (nodes: any[], targetTableId: string): string => {
    for (const dbNode of nodes) {
      if (dbNode.nodeType === 2 && dbNode.children) {
        // 在数据库节点下查找表节点
        for (const tableNode of dbNode.children) {
          if (tableNode.nodeType === 3 && tableNode.tableId === targetTableId) {
            return tableNode.tableName
          }
        }
      }

      // 递归查找子节点
      if (dbNode.children) {
        const result = findTable(dbNode.children, targetTableId)
        if (result) return result
      }
    }
    return ''
  }

  if (node.nodeType === 3) return node.tableName
  return findTable(treeData.value, node.tableId) || '未知表'
}

// 查找节点对应的数据源ID
const findDatasourceId = (node: any): string => {
  // 如果当前节点就是数据库节点，直接返回datasourceId
  if (node.nodeType === 2 && node.datasourceId) {
    return node.datasourceId
  }

  // 如果是表节点或字段节点，需要向上查找所属的数据库节点
  const findDatabaseNode = (nodes: any[], targetNode: any): any => {
    for (const dbNode of nodes) {
      if (dbNode.nodeType === 2 && dbNode.children) {
        // 检查这个数据库下是否有目标表
        const hasTable = dbNode.children.some((child: any) => {
          if (targetNode.nodeType === 3) {
            // 查找表节点
            return child.nodeType === 3 && child.tableId === targetNode.tableId
          } else if (targetNode.nodeType === 4) {
            // 查找字段节点（通过表ID关联）
            return child.nodeType === 3 && child.tableId === targetNode.tableId
          }
          return false
        })

        if (hasTable) {
          return dbNode
        }
      }

      // 递归查找子节点
      if (dbNode.children) {
        const result = findDatabaseNode(dbNode.children, targetNode)
        if (result) return result
      }
    }
    return null
  }

  const databaseNode = findDatabaseNode(treeData.value, node)
  return databaseNode?.datasourceId || ''
}

// 查找节点对应的表ID
const findTableId = (node: any): string => {
  // 如果当前节点就是表节点，直接返回tableId
  if (node.nodeType === 3 && node.tableId) {
    return node.tableId
  }

  // 如果是字段节点，返回关联的tableId
  if (node.nodeType === 4 && node.tableId) {
    return node.tableId
  }

  // 如果是数据库节点或目录节点，返回空字符串
  return ''
}

// 刷新树形数据
const refreshTree = () => {
  fetchTreeData()
}

// 更新表节点的列字段信息
const updateNodeColumns = (tableId: string, columnData: any[]) => {
  const updateTreeNode = (nodes: any[]): any[] => {
    return nodes.map((node) => {
      if (node.nodeType === 3 && node.tableId === tableId) {
        // 找到对应的表节点，添加列字段作为子节点
        return {
          ...node,
          children: columnData.map((column) => ({
            ...column,
            key: `column_${column.key || column.fieldName}`,
            title: column.fieldName,
            fieldName: column.fieldName,
            fieldTypeName: column.fieldTypeName,
            fieldComment: column.fieldComment,
            nodeType: 4, // 字段节点类型
            isLeaf: true,
          })),
        }
      } else if (node.children) {
        // 递归处理子节点
        return {
          ...node,
          children: updateTreeNode(node.children),
        }
      }
      return node
    })
  }

  // 更新原始树数据
  treeData.value = updateTreeNode(treeData.value)
}

// 根据数据源ID和数据库名称自动展开并选中对应的数据库节点，可选择性展开到表节点
const expandAndSelectDatabase = (datasourceId: string, databaseName: string, tableId?: string) => {
  console.log('尝试展开和选中数据库:', { datasourceId, databaseName, tableId })

  // 查找匹配的数据库节点和表节点
  const findAndExpandDatabase = (nodes: any[], parentKeys: string[] = []): boolean => {
    for (const node of nodes) {
      if (node.nodeType === 1 && node.children) {
        // 目录节点，检查其子节点（数据库节点）
        const currentKeys = [...parentKeys, getNodeKey(node)]

        for (const dbNode of node.children) {
          if (
            dbNode.nodeType === 2 &&
            dbNode.datasourceId === String(datasourceId) &&
            dbNode.databaseName === databaseName
          ) {
            // 找到匹配的数据库节点
            const dbKey = getNodeKey(dbNode)
            const keysToExpand = [...currentKeys, dbKey]

            // 如果提供了tableId，尝试查找并选中对应的表节点
            if (tableId && dbNode.children) {
              for (const tableNode of dbNode.children) {
                if (tableNode.nodeType === 3 && tableNode.tableId === tableId) {
                  // 找到匹配的表节点
                  const tableKey = getNodeKey(tableNode)

                  // 展开到表节点的父级（数据库节点）
                  expandedKeys.value = [...new Set([...expandedKeys.value, ...keysToExpand])]

                  // 选中表节点
                  selectedKeys.value = [tableKey]

                  console.log('找到并选中表节点:', {
                    databaseName: dbNode.databaseName,
                    tableName: tableNode.tableName,
                    tableId: tableNode.tableId,
                    expandedKeys: expandedKeys.value,
                    selectedKeys: selectedKeys.value,
                  })

                  return true
                }
              }

              // 如果提供了tableId但未找到对应表，记录警告但仍展开数据库节点
              console.warn('未找到指定的表节点，将展开数据库节点:', { tableId, databaseName })
            }

            // 如果没有提供tableId或未找到对应表，只展开和选中数据库节点
            expandedKeys.value = [...new Set([...expandedKeys.value, ...currentKeys])]
            selectedKeys.value = [dbKey]

            console.log('找到并选中数据库节点:', {
              databaseName: dbNode.databaseName,
              datasourceId: dbNode.datasourceId,
              expandedKeys: expandedKeys.value,
              selectedKeys: selectedKeys.value,
            })

            return true
          }
        }

        // 递归查找子节点
        if (findAndExpandDatabase(node.children, currentKeys)) {
          return true
        }
      }
    }
    return false
  }

  // 等待树数据加载完成后再查找
  if (treeData.value.length > 0) {
    const found = findAndExpandDatabase(treeData.value)
    if (!found) {
      console.warn('未找到匹配的数据库节点:', { datasourceId, databaseName, tableId })
    }
    return found
  } else {
    console.warn('树数据尚未加载完成')
    return false
  }
}

// 获取所有数据库列表
const getAllDatabases = () => {
  const databases: Array<{
    databaseName: string
    datasourceId: string
  }> = []

  const extractDatabases = (nodes: any[]) => {
    for (const node of nodes) {
      if (node.nodeType === 2) {
        // 数据库节点
        databases.push({
          databaseName: node.databaseName,
          datasourceId: node.datasourceId,
        })
      }
      if (node.children) {
        extractDatabases(node.children)
      }
    }
  }

  extractDatabases(treeData.value)
  return databases
}

// 获取所有表列表
const getAllTables = () => {
  const tables: Array<{
    tableName: string
    databaseName: string
    tableId: string
    comment?: string
  }> = []

  const extractTables = (nodes: any[], currentDatabaseName?: string) => {
    for (const node of nodes) {
      if (node.nodeType === 2) {
        // 数据库节点，更新当前数据库名称
        if (node.children) {
          extractTables(node.children, node.databaseName)
        }
      } else if (node.nodeType === 3 && currentDatabaseName) {
        // 表节点
        tables.push({
          tableName: node.tableName,
          databaseName: currentDatabaseName,
          tableId: node.tableId,
          comment: node.comment || '',
        })
      }
      if (node.children && node.nodeType !== 2) {
        extractTables(node.children, currentDatabaseName)
      }
    }
  }

  extractTables(treeData.value)
  return tables
}

// 对外暴露的方法
defineExpose({
  refreshTree,
  updateNodeColumns,
  findDatabaseName,
  findDatasourceId,
  findTableId, // 新增：查找表ID
  expandAndSelectDatabase, // 新增：自动展开和选中数据库节点
  getAllDatabases, // 新增：获取所有数据库列表
  getAllTables, // 新增：获取所有表列表
  getSelectedNodes: () => {
    // 返回当前选中的节点
    return []
  },
})

// 初始化
onMounted(() => {
  fetchTreeData()
})
</script>

<style scoped lang="less">
.tree-menu {
  height: 100%;
  display: flex;
  flex-direction: column;
  border-right: 1px solid #f0f0f0;
  background: #fafafa;
}

.tree-header {
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #fff;

  h4 {
    margin: 0;
    font-size: 14px;
    font-weight: 500;
    color: #262626;
  }
}

.tree-content {
  flex: 1;
  padding: 8px;
  overflow-y: auto;

  :deep(.ant-tree) {
    background: transparent;

    .ant-tree-treenode {
      padding: 2px 0;
    }

    .ant-tree-node-content-wrapper {
      padding: 2px 4px;
      border-radius: 4px;
      transition: all 0.2s;
      display: flex;
      align-items: center;
      min-height: 24px;
      width: 100%;

      &:hover {
        background-color: #e6f7ff;
      }

      &.ant-tree-node-selected {
        background-color: #bae7ff;
      }
    }

    .ant-tree-iconEle {
      margin-right: 6px;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      width: 16px;
      height: 16px;
      flex-shrink: 0;

      .anticon {
        font-size: 14px;
        line-height: 1;
      }
    }

    .ant-tree-title {
      display: flex;
      align-items: center;
      flex: 1;
      min-height: 20px;
      min-width: 0; // 确保可以收缩
    }

    .ant-tree-switcher {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 24px;
      height: 24px;
      flex-shrink: 0;
    }
  }
}

.tree-node-title {
  display: flex;
  align-items: center;
  gap: 4px;
  cursor: pointer;
  width: 100%;
  line-height: 1.4;

  .node-name {
    flex: 1;
    font-size: 13px;
    color: #262626;
    line-height: 1.4;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    min-width: 0;
  }

  .node-count {
    font-size: 12px;
    color: #8c8c8c;
    background: #f5f5f5;
    padding: 1px 6px;
    border-radius: 10px;
    line-height: 1.2;
    white-space: nowrap;
    flex-shrink: 0;
  }

  // 目录节点（nodeType: 1）- 最大宽度 200px
  &[data-node-type='1'] {
    .node-name {
      max-width: 200px;
    }
  }

  // 数据库节点（nodeType: 2）- 最大宽度 180px (考虑缩进)
  &[data-node-type='2'] {
    .node-name {
      max-width: 180px;
    }
  }

  // 表节点（nodeType: 3）- 最大宽度 160px (考虑更深的缩进)
  &[data-node-type='3'] {
    .node-name {
      max-width: 160px;
    }

    &:hover {
      background: rgba(24, 144, 255, 0.1);
      border-radius: 4px;
      padding: 2px 4px;
      margin: -2px -4px;
    }

    &:hover::after {
      content: '双击插入';
      font-size: 11px;
      color: #1890ff;
      margin-left: 8px;
      line-height: 1;
      white-space: nowrap;
      flex-shrink: 0;
    }
  }

  // 字段节点（nodeType: 4）- 最大宽度 140px (考虑最深的缩进)
  &[data-node-type='4'] {
    .node-name {
      max-width: 140px;
      font-size: 12px;
      color: #595959;
    }

    &:hover {
      background: rgba(82, 196, 26, 0.1);
      border-radius: 4px;
      padding: 2px 4px;
      margin: -2px -4px;
    }
  }
}

.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
}

// 滚动条样式
.tree-content::-webkit-scrollbar {
  width: 6px;
}

.tree-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.tree-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;

  &:hover {
    background: #a8a8a8;
  }
}

// 字段图标样式
.field-icon {
  font-size: 12px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}
</style>
