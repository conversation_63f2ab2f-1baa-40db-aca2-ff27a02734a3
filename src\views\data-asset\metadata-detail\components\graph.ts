import {
  Graph,
  Grid,
  Minimap,
  registerBehavior,
  registerEdge,
  registerNode,
  type EdgeConfig,
  type GraphData,
  type IBBox,
  type IEdge,
  type IGroup,
  type IPoint,
  type IShape,
  type Item,
  type ModelConfig,
  type NodeConfig,
} from '@antv/g6'

export type DataType = {
  id: string
  label: string
  attrs: Array<{
    key: string
    type?: string
    relation?: Array<{
      nodeId: string
      key: string
      label?: string
    }>
  }>
}

export function initG6(dataSource: Array<DataType>) {
  let graph: Graph | undefined = undefined
  const rawData = dataSource
  const isInBBox = (point: IPoint, bbox: IBBox) => {
    const { x, y } = point
    const { minX, minY, maxX, maxY } = bbox
    return x < maxX && x > minX && y > minY && y < maxY
  }

  const itemHeight = 30 // 设置字段 的高
  registerBehavior('dice-er-sccoll', {
    getDefaultCfg() {
      return {
        multiple: true,
      }
    },
    // 获取事件
    getEvents() {
      return {
        wheel: 'scorll',
        click: 'click',
      }
    },
    // 滚动条
    scorll(e: any) {
      e.preventDefault()
      if (graph === undefined || graph === null) {
        return
      }
      const nodes = graph.getNodes().filter((n) => {
        const bbox = n.getBBox()
        return isInBBox(graph.getPointByClient(e.clientX, e.clientY), bbox)
      })

      const x = e.deltaX || e.movementX
      let y = e.deltaY || e.movementY
      if (!y && navigator.userAgent.indexOf('Firefox') > -1) y = (-e.wheelDelta * 125) / 3

      if (nodes) {
        for (const node of nodes) {
          ;((node) => {
            const model = node.getModel() as ModelConfig & {
              attrs: Array<{
                key: string
              }>
              startIndex?: number
              startX?: number
            }
            if (model.attrs.length < 9) {
              return
            }
            const idx = model.startIndex || 0
            let startX = model.startX || 0.5
            let startIndex = idx + y * 0.02
            startX -= x
            if (startIndex < 0) {
              startIndex = 0
            }
            if (startX > 0) {
              startX = 0
            }
            if (startIndex > model.attrs.length - 1) {
              startIndex = model.attrs.length - 1
            }
            graph?.update(node, {
              startIndex,
              startX,
            })
            const edges = node.getEdges()
            edges &&
              edges.forEach((edge) => {
                edge.refresh()
              })
          })(node)
        }
      }
    },
    // 底部缩放按钮
    click(e) {
      const item = e.item
      const shape = e.shape
      if (!item) return
      const model = item.getModel()
      if (shape.get('name') === 'collapse') {
        graph?.updateItem(item, {
          collapsed: true,
          size: [300, 300],
        })
        setTimeout(() => graph?.layout(), 100)
      } else if (shape.get('name') === 'expand') {
        graph?.updateItem(item, {
          collapsed: false,
          size: [300, 300],
        })
        setTimeout(() => graph?.layout(), 100)
      } else if (shape.get('name') === 'title') {
        console.log('点击了表名', model.label)
      }
    },
  })
  // lineDash 的差值，可以在后面提供 util 方法自动计算
  const dashArray = [
    [0, 1],
    [0, 2],
    [1, 2],
    [0, 1, 1, 2],
    [0, 2, 1, 2],
    [1, 2, 1, 2],
    [2, 2, 1, 2],
    [3, 2, 1, 2],
    [4, 2, 1, 2],
  ]
  const lineDash = [4, 2, 1, 2]
  const interval = 9
  registerEdge('dice-er-edge', {
    setState(name?: string, value?: string | boolean, item?: Item) {
      const shape = item?.get('keyShape')
      if (name === 'running') {
        if (value) {
          const length = shape.getTotalLength()
          let totalArray: number[] = []
          for (let i = 0; i < length; i += interval) {
            totalArray = totalArray.concat(lineDash)
          }
          let index = 0
          shape.animate(
            {
              onFrame() {
                const cfg = {
                  lineDash: dashArray[index].concat(totalArray),
                }
                index = (index + 1) % interval
                return cfg
              },
              repeat: true,
            },
            3000,
          )
        } else {
          shape.stopAnimate()
          shape.attr('lineDash', null)
          shape.attr('stroke', '#5B8FF9') // 恢复默认颜色
        }
      } else if (name === 'highlight') {
        // 加粗，高亮
        if (value) {
          shape.attr('lineWidth', 2)
          shape.attr('stroke', 'red')
        } else {
          shape.attr('lineWidth', 1)
        }
      }
    },
    draw(cfg, group): IShape {
      // 绘制图形
      const edge = group.cfg.item
      const sourceNode = edge.getSource().getModel()
      const targetNode = edge.getTarget().getModel()
      const sourceIndex = sourceNode.attrs.findIndex((e) => e.key === cfg.sourceKey)
      const sourceStartIndex = sourceNode.startIndex || 0
      // const colors = {
      //   0: '#EEBC20',
      //   1: '#5B8FF9',
      //   2: '#5BD8A6'
      // }
      let sourceY = 15
      if (!sourceNode.collapsed && sourceIndex > sourceStartIndex - 1) {
        sourceY = 30 + (sourceIndex - sourceStartIndex + 0.5) * 30
        sourceY = Math.min(sourceY, 300)
      }
      const targetIndex = targetNode.attrs.findIndex((e) => e.key === cfg.targetKey)
      const targetStartIndex = targetNode.startIndex || 0
      let targetY = 15
      if (!targetNode.collapsed && targetIndex > targetStartIndex - 1) {
        targetY = (targetIndex - targetStartIndex + 0.5) * 30 + 30
        targetY = Math.min(targetY, 300)
      }
      const startPoint = { ...(cfg.startPoint ?? { x: 0, y: 0, anchorIndex: 0 }) }
      const endPoint = { ...(cfg.endPoint ?? { x: 0, y: 0 }) }
      startPoint.y = startPoint.y + sourceY
      endPoint.y = endPoint.y + targetY

      let shape: IShape
      if (sourceNode.id !== targetNode.id) {
        shape = group.addShape('path', {
          attrs: {
            stroke: '#5B8FF9', // 边的颜色
            path: [
              ['M', startPoint.x, startPoint.y],
              [
                'C',
                endPoint.x / 3 + (2 / 3) * startPoint.x,
                startPoint.y,
                endPoint.x / 3 + (2 / 3) * startPoint.x,
                endPoint.y,
                endPoint.x,
                endPoint.y,
              ],
            ],
            endArrow: true, // 末端是箭头
          },
          sourceKey: cfg.sourceKey,
          targetKey: cfg.targetKey,
          name: 'path-shape',
        })
      } else if (!sourceNode.collapsed) {
        let gap = Math.abs((startPoint.y - endPoint.y) / 3)
        if (startPoint['index'] === 1) {
          gap = -gap
        }
        shape = group.addShape('path', {
          attrs: {
            stroke: '#5B8FF9',
            path: [
              ['M', startPoint.x, startPoint.y],
              [
                'C',
                startPoint.x - gap,
                startPoint.y,
                startPoint.x - gap,
                endPoint.y,
                startPoint.x,
                endPoint.y,
              ],
            ],
            endArrow: true,
          },
          sourceKey: cfg.sourceKey,
          targetKey: cfg.targetKey,
          name: 'path-shape',
        })
      }
      return shape
    },
  })
  // 获取需要高亮显示的节点 element ,key 为字段，获取key字段前后连接的线条，需要做递归，获取整条血缘线
  const getHighLightElement = (key: string, id: string, elements: IEdge[], direct?: string) => {
    if (!direct || direct === 'source') {
      // 根据字段key，获取前连接线
      const sourceEdges = graph?.findAll('edge', (edge) => {
        const model = edge._cfg.model
        const targetNode = edge._cfg.targetNode
        return model.targetKey === key && targetNode._cfg.id === id
      })

      sourceEdges.forEach((edge) => {
        if (elements.indexOf(edge) < 0) {
          elements.push(edge)
          const model = edge._cfg.model
          const sourceKey = model.sourceKey
          const sourceNode = edge._cfg.sourceNode
          const sourceNodeId = sourceNode._cfg.id
          getHighLightElement(sourceKey, sourceNodeId, elements, 'source')
        }
      })
    }
    if (!direct || direct === 'target') {
      // 根据字段key，获取后连接线
      const targetEdges = graph?.findAll('edge', (edge) => {
        const model = edge._cfg.model
        const sourceNode = edge._cfg.sourceNode
        return model.sourceKey === key && sourceNode._cfg.id === id
      })
      targetEdges.forEach((edge) => {
        if (elements.indexOf(edge) < 0) {
          elements.push(edge)
          const model = edge._cfg.model
          const targetKey = model.targetKey
          const targetNode = edge._cfg.targetNode
          const targetNodeId = targetNode._cfg.id
          getHighLightElement(targetKey, targetNodeId, elements, 'target')
        }
      })
    }
  }
  // 注册节点
  registerNode('dice-er-box', {
    draw(cfg, group) {
      const width = 250
      const height = 316
      const itemCount = 10
      const boxStyle = {
        stroke: '#EDF2F3', // 设置盒子边框颜色
        radius: 4,
      }

      const { attrs = [], selectedIndex, collapsed } = cfg
      const startIndex = (cfg.startIndex as number) || 0
      const list = attrs as Array<{
        key: string
        type: string
        relation?: Array<{ nodeId: string; key: string }>
      }>
      const afterList = list.slice(Math.floor(startIndex), Math.floor(startIndex + itemCount - 1))
      const offsetY = (0.5 - (startIndex % 1)) * itemHeight + 30
      // 表名部分设置
      group.addShape('rect', {
        attrs: {
          fill: boxStyle.stroke,
          height: 30,
          width,
          radius: [boxStyle.radius, boxStyle.radius, 0, 0],
        },
        draggable: true,
      })

      const fontLeft = 12
      // let fontLeft = 12

      // if (icon && icon.show !== false) {
      //   group.addShape('image', {
      //     attrs: {
      //       x: 8,
      //       y: 8,
      //       height: 16,
      //       width: 16,
      //       ...icon,
      //     },
      //   })
      //   fontLeft += 18
      // }
      // 表名字样式
      group.addShape('text', {
        attrs: {
          y: 22,
          x: fontLeft,
          fill: '#000',
          text: cfg.label,
          fontSize: 13,
          fontWeight: 500,
          cursor: 'pointer',
        },
        name: 'title',
      })

      // 添加一个隐藏的tooltip
      const tooltip = group.addShape('rect', {
        attrs: {
          x: 0,
          y: -60,
          width,
          height: 60,
          fill: '#000',
          opacity: 0,
          radius: 4,
          visible: false,
          text: '',
        },
        name: 'tooltip',
      })

      const tooltipText = group.addShape('text', {
        attrs: {
          x: 10,
          y: -10,
          text: '',
          fill: '#fff',
          fontSize: 12,
          visible: false,
        },
        name: 'tooltip-text',
      })

      // 添加hover事件处理
      const titleShape = group.find((element) => element.get('name') === 'title')
      if (titleShape) {
        titleShape.on('mouseover', () => {
          console.log('鼠标移入')
          const firstField = cfg
          const value: any = dataSource.findLast((item) => item.id === firstField.id)
          console.log('🚀 ~ titleShape.on ~ dataSource:', dataSource)
          console.log('🚀 ~ titleShape.on ~ value:', value)
          if (value && (value.dbName || value.addrInfo)) {
            tooltip.attr('opacity', 0.75)
            tooltipText.attr({
              visible: true,
              text: ` 数据库地址：${value.addrInfo}
              \n 数据库名称：${value.dbName}`,
            })
          }
        })

        titleShape.on('mouseout', () => {
          console.log('鼠标移出')
          tooltip.attr('opacity', 0)
          tooltipText.attr('visible', false)
        })
      }

      // 底部灰色的可设置表的打开与关闭的按钮
      group.addShape('rect', {
        attrs: {
          x: 0,
          y: collapsed ? 30 : 300,
          height: 15,
          width,
          fill: '#eee',
          radius: [0, 0, boxStyle.radius, boxStyle.radius],
          cursor: 'pointer',
        },
        name: collapsed ? 'expand' : 'collapse',
      })
      // 缩放文字
      group.addShape('text', {
        attrs: {
          x: width / 2 - 6,
          y: (collapsed ? 30 : 300) + 12,
          text: collapsed ? '+' : '-',
          width,
          fill: '#000',
          radius: [0, 0, boxStyle.radius, boxStyle.radius],
          cursor: 'pointer',
        },
        name: collapsed ? 'expand' : 'collapse',
      })
      // 设置左右两边的边
      const keyshape = group.addShape('rect', {
        attrs: {
          x: 0,
          y: 0,
          width,
          height: collapsed ? 45 : height,
          ...boxStyle,
          cursor: 'pointer',
        },
        draggable: true,
      })

      if (collapsed) {
        return keyshape
      }

      const listContainer = group.addGroup({})
      listContainer.setClip({
        type: 'rect',
        attrs: {
          x: -8,
          y: 30,
          width: width + 16,
          height: 300 - 30,
        },
      })
      listContainer.addShape({
        type: 'rect',
        attrs: {
          x: 1,
          y: 30,
          width: width - 2,
          height: 300 - 30,
          fill: '#fff',
        },
        draggable: true,
      })

      if (list.length > itemCount) {
        const barStyle = {
          width: 8,
          padding: 0,
          boxStyle: {
            stroke: '#00000022',
          },
          innerStyle: {
            fill: '#00000022',
          },
        }

        listContainer.addShape('rect', {
          attrs: {
            y: 30,
            x: width - barStyle.padding - barStyle.width,
            width: barStyle.width,
            height: height - 30,
            ...barStyle.boxStyle,
          },
        })

        const indexHeight =
          afterList.length > itemCount ? (afterList.length / list.length) * height : 10
        listContainer.addShape('rect', {
          attrs: {
            y: 30 + barStyle.padding + (startIndex / list.length) * (height - 30),
            x: width - barStyle.padding - barStyle.width,
            width: barStyle.width,
            height: Math.min(height, indexHeight),
            ...barStyle.innerStyle,
          },
        })
      }
      if (afterList) {
        afterList.forEach((e, i) => {
          const isSelected = Math.floor(startIndex) + i === Number(selectedIndex)
          let { key = '' } = e
          if (e.type) {
            key += ` - ${e.type}`
          }
          const label = key.length > 26 ? `${key.slice(0, 24)}...` : key

          listContainer.addShape('rect', {
            attrs: {
              x: 1,
              y: i * itemHeight - itemHeight / 2 + offsetY,
              width: width - 4,
              height: itemHeight,
              radius: 2,
              lineWidth: 1,
              cursor: 'pointer',
            },
            name: `item-${Math.floor(startIndex) + i}-content`,
            draggable: true,
          })
          listContainer.addShape('rect', {
            // 字段容器 样式
            attrs: {
              x: 0,
              y: i * itemHeight + offsetY + 6,
              width: 250 - 4,
              height: itemHeight,
              radius: 2,
              lineWidth: 1,
              cursor: 'pointer',
            },
            name: `item-${Math.floor(startIndex) + i}`,
            draggable: true,
          })

          listContainer.addShape('text', {
            // 字段文字 样式
            attrs: {
              x: 6,
              y: i * itemHeight + offsetY + 6,
              text: label,
              fontSize: 12,
              fill: '#000',
              full: e,
              fontWeight: isSelected ? 500 : 100,
              cursor: 'pointer',
            },
            className: 'attr-text',
            name: `item-${Math.floor(startIndex) + i}`,
          })
        })
      }
      return keyshape
    },
    afterDraw(cfg?: ModelConfig, group?: IGroup) {
      const attrKeyRecord: string[] = []
      const attrNodes = group?.findAll((element) => element.get('className') === 'attr-text') ?? []
      for (const attrNode of attrNodes) {
        ;((attrNode) => {
          attrNode.on('click', () => {
            const attrKey = attrNode.attrs.full.key
            const arrtKeyIndex = attrKeyRecord.indexOf(attrKey)
            const edges: IEdge[] = []
            getHighLightElement(attrKey, group?.cfg.id, edges)
            if (arrtKeyIndex === -1) {
              attrKeyRecord.push(attrKey)
              edges.forEach((edge) => {
                graph?.setItemState(edge, 'highlight', true)
                graph?.setItemState(edge, 'running', true)
              })
            } else {
              attrKeyRecord.splice(arrtKeyIndex, 1)
              edges.forEach((edge) => {
                graph?.setItemState(edge, 'highlight', false)
                graph?.setItemState(edge, 'running', false)
              })
            }
          })
        })(attrNode)
      }
    },
    getAnchorPoints() {
      // 边的位置
      return [
        [0, 0],
        [1, 0],
      ]
    },
  })

  // 转化配置数据为可用数据
  const dataTransform = (data: Array<DataType>): GraphData => {
    const nodes: Array<NodeConfig> = []
    const edges: Array<EdgeConfig> = []
    data.map((node) => {
      nodes.push({
        ...node,
      })
      if (node.attrs) {
        for (const attr of node.attrs) {
          if (attr.relation) {
            for (const relation of attr.relation) {
              // 边配置
              edges.push({
                source: node.id,
                target: relation.nodeId,
                sourceKey: attr.key,
                targetKey: relation.key,
                label: relation.label,
              })
            }
          }
        }
      }
    })
    return { nodes, edges }
  }

  const container = document.getElementById('container') as HTMLDivElement

  const width = container.scrollWidth
  const height = container.scrollHeight - 20
  // 网格
  const grid = new Grid()
  // 缩略图
  const minimap = new Minimap({
    size: [120, 120],
    className: 'minimap',
    type: 'delegate',
  })
  // 工具栏
  const graphInstance = new Graph({
    container: 'container',
    fitCenter: true, // 自动适应容器大小
    defaultNode: {
      size: [300, 300], // 表之间的间隔
      type: 'dice-er-box',
      color: '#5B8FF9',
      style: {
        fill: '#9EC9FF',
        lineWidth: 3,
      },
      labelCfg: {
        style: {
          fill: 'black',
          fontSize: 20,
        },
      },
    },
    defaultEdge: {
      type: 'dice-er-edge',
    },
    modes: {
      default: [
        'drag-node',
        'drag-canvas',
        'dice-er-sccoll',
        {
          type: 'zoom-canvas',
          sensitivity: 0.8,
        },
      ],
      select: ['drag-node', 'drag-canvas', 'dice-er-sccoll'],
    },
    layout: {
      width,
      height,
      type: 'dagre',
      rankdir: 'LR',
      align: 'UL',
      contcolPoints: true,
      nodesepFunc: () => 0.2,
      ranksepFunc: () => 0.5,
    },
    animate: true,
    minZoom: 0.6, // 最小缩放比例
    maxZoom: 2, // 最大缩放比例
    plugins: [grid, minimap], // 将 minimap 实例配置到图上
  })
  graphInstance.data(dataTransform(rawData))
  graphInstance.render()
  // 切换滚动模式
  graphInstance.on('node:mouseover', () => {
    if (graphInstance.getCurrentMode() === 'default') {
      graphInstance.setMode('select')
    }
  })
  graphInstance.on('node:mouseleave', () => {
    if (graphInstance.getCurrentMode() === 'select') {
      graphInstance.setMode('default')
    }
  })
  // 监听画布缩放，缩小到一定程度，节点显示缩略样式
  // const currentLevel = 'expand'
  // const briefZoomThreshold = Math.max(graphInstance.getZoom(), 0.5)
  // graphInstance.on('viewportchange', (e) => {
  //   if (e.action !== 'zoom') return
  //   const currentZoom = graphInstance.getZoom()
  //   let toLevel = currentLevel
  //   if (currentZoom < briefZoomThreshold) {
  //     toLevel = 'collapse'
  //   } else {
  //     toLevel = 'expand'
  //   }
  //   if (toLevel !== currentLevel) {
  //     currentLevel = toLevel

  //     for (const node of graphInstance.getNodes()) {
  //       graphInstance.updateItem(node, {
  //         collapsed: toLevel === 'collapse',
  //         size: [300, 300],
  //       })
  //       setTimeout(() => graphInstance.layout(), 100)
  //     }
  //   }
  // })
  graph = graphInstance
}
