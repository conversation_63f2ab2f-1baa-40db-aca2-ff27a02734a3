<template>
  <a-drawer v-model:open="visible" :title="null" width="25%" class="filter-modal">
    <div class="title-text">汇总方式</div>
    <div class="tag-list">
      <a-tag
        color="#96c763"
        :closable="true"
        @close="onClose"
        v-for="(text, key) in checkTag"
        :key="key"
      >
        <span style="padding: 4px 4px 4px 8px; display: inline-block">{{ text.label }}</span>
      </a-tag>
      <a-popover
        v-model:visible="methodsVisible"
        title="基本功能"
        trigger="click"
        placement="bottom"
      >
        <a-button
          type="default"
          shape="default"
          :loading="false"
          :disabled="false"
          @click="handleClickADD"
        >
          + {{ checkTag.length ? '' : '添加一个方法或指标' }}
        </a-button>
        <template #content>
          <a-menu style="border: none">
            <a-menu-item
              v-for="item in methodList"
              @click="handleClickAddMthod(item)"
              :key="item.value"
            >
              {{ item.label }}
            </a-menu-item>
          </a-menu>
        </template>
      </a-popover>
    </div>
    <a-divider type="horizontal"> </a-divider>
    <div class="field-list">
      <div v-for="(item, key) in fieldList" :key="key">
        <h2 class="field-title">{{ item.label }}</h2>
        <div
          v-for="(it, key) in item.children"
          :key="key"
          class="field-item"
          :class="activeField === it.key ? 'field-item-active' : ''"
          @click="handleClickField(it)"
        >
          {{ it.field }}
        </div>
      </div>
    </div>
  </a-drawer>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const visible = ref<boolean>(false)
const methodsVisible = ref(false)
const checkTag: any = ref([])
const activeField = ref('')

const methodList = ref([
  { value: 'sum', label: '求和' },
  { value: 'avg', label: '平均值' },
  { value: 'distinct_count', label: '去重计算' },
  { value: 'count', label: '累计行数' },
  { value: 'min', label: '最小值' },
  { value: 'max', label: '最大值' },
])

const fieldList = ref<any[]>([
  {
    label: '基本信息',
    value: '1',
    key: 'basic',
    children: [
      {
        field: 'Created At',
        type: 'date',
        value: '',
        time: [],
        selectOptions: [],
        key: 'createdAt',
      },
      {
        field: 'State',
        type: 'select',
        value: '',
        selectOptions: [
          {
            value: 'Active',
            label: 'Active',
            key: 'active',
          },
          {
            value: 'Inactive',
            label: 'Inactive',
            key: 'inactive',
          },
          {
            value: 'Pending',
            label: 'Pending',
            key: 'pending',
          },
          {
            value: 'Suspended',
            label: 'Suspended',
            key: 'suspended',
          },
          {
            value: 'Deleted',
            label: 'Deleted',
            key: 'deleted',
          },
        ],
        key: 'state',
      },
      {
        field: '自定义列1',
        type: 'input',
        value: '',
        key: 'custom1',
      },
      {
        field: '自定义列2',
        type: 'limit',
        minValue: '',
        maxValue: '',
        key: 'custom2',
      },
      {
        field: '自定义列3',
        type: 'checkbox',
        value: [],
        selectOptions: [
          {
            value: 'AAffilate',
            label: 'AAffilate',
            key: 'aAffiliate',
          },
          {
            value: 'BFFilate',
            label: 'BFFilate',
            key: 'bAffiliate',
          },
          {
            value: 'CFFilate',
            label: 'CFFilate',
            key: 'cAffiliate',
          },
          {
            value: 'DFFilate',
            label: 'DFFilate',
            key: 'dAffiliate',
          },
          {
            value: 'EFFilate',
            key: 'eAffiliate',
          },
        ],
        key: 'custom3',
      },
    ],
  },
  {
    label: '高级信息',
    value: '2',
    key: 'advance',
    children: [
      {
        field: 'Created At',
        type: 'date',
        value: '',
        time: [],
        selectOptions: [],
        key: 'createdAtAdvance',
      },
      {
        field: 'State',
        type: 'select',
        value: '',
        selectOptions: [
          {
            value: 'Active',
            label: 'Active',
            key: 'activeAdvance',
          },
          {
            value: 'Inactive',
            label: 'Inactive',
            key: 'inactiveAdvance',
          },
          {
            value: 'Pending',
            label: 'Pending',
            key: 'pendingAdvance',
          },
          {
            value: 'Suspended',
            key: 'suspendedAdvance',
          },
        ],
        key: 'stateAdvance',
      },
    ],
  },
])

const show = () => {
  visible.value = true
}

const onClose = (e: any) => {
  console.log('🚀 ~ onClose ~ e:', e)
  // visible.value = false
}

const handleClickADD = () => {}

const handleClickAddMthod = (item: any) => {
  checkTag.value.push(item)
  methodsVisible.value = false
}

const handleClickField = (it: any) => {
  activeField.value = it.key
}

defineExpose({
  show,
})
</script>

<style lang="less" scoped>
@tab-color: rgb(76, 87, 115);
@bg-color: #eef5fc;
@ac-color: rgb(80, 158, 227);
@mb-color-border: #eeecec;
@title-color: #8ec257;

.filter-modal {
  .title-text {
    display: flex;
    -webkit-box-align: center;
    align-items: center;
    font-size: 1.17em;
    font-weight: bold;
    margin-top: 0px;
    margin-bottom: 15px;
  }
  .field-title {
    margin: 1rem 0px 0.5rem;
    text-transform: uppercase;
    font-weight: 700;
    font-size: 0.75rem;
    padding: 0px 0.5rem;
    color: @title-color;
  }
  .field-item {
    display: flex;
    -webkit-box-align: center;
    align-items: center;
    margin-left: 0.5rem;
    padding: 0.5rem 10px;
    -webkit-box-flex: 1;
    flex-grow: 1;
    cursor: pointer;
    font-weight: 600;
    border-radius: 4px;
  }
  .field-item:hover {
    background-color: #fafbfc;
    color: @title-color;
  }
  .field-item-active {
    background-color: @title-color;
    color: #fff;
  }
}
</style>
