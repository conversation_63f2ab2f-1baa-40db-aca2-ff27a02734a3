<template>
  <div class="custom-olumn">
    <div class="title">自定义列</div>
    <div class="emotion">
      <div v-for="(item, i) in modelValue" :key="i">
        <a-popover
          :trigger="['click']"
          overlayClassName="expression-editor"
          v-model:open="item.open"
        >
          <template #content>
            <div>
              <p>表达式</p>
              <customInput v-model="item.expression" :fieldList="withJoinColumns"></customInput>
              <p>命名</p>
              <a-input v-model:value="item.alias" placeholder="请输入命名" />
              <div style="text-align: right; margin-top: 30px">
                <a-button type="primary" @click="submitHandle(item)">确定</a-button>
              </div>
            </div>
          </template>
          <div class="emotion-item">
            <div class="expression-title" style="margin-right: 10px">{{ item.alias }}</div>
            <CloseCircleOutlined @click.stop="handleRemove(i)" class="close-btn" />
          </div>
        </a-popover>
      </div>
      <a-popover :trigger="['click']" overlayClassName="expression-editor" :open="open">
        <PlusSquareFilled class="icon" @click="open = true" />
        <template #content>
          <div>
            <p>表达式</p>
            <customInput v-model="expression" :fieldList="withJoinColumns"></customInput>
            <p>命名</p>
            <a-input v-model:value="alias" placeholder="请输入命名" />
            <div style="text-align: right; margin-top: 30px">
              <a-button @click="open = false" style="margin-right: 15px">取消</a-button>
              <a-button type="primary" @click="submitHandle()">确定</a-button>
            </div>
          </div>
        </template>
      </a-popover>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { PlusSquareFilled, CloseCircleOutlined } from '@ant-design/icons-vue'
import customInput from './custom-input.vue'

const open = ref(false)
const alias = ref('')
const expression = ref('')
const modelValue = defineModel<any[]>({
  default: () => [],
})
const props = defineProps({
  columns: {
    type: Array,
    default: () => [],
  },
  dataList: {
    type: Array,
    default: () => [],
  },
})

const joinsColumns = computed(() => {
  const allJoins = props.dataList.filter((item: any) => item.type === 'joins')

  return allJoins.map((it: any) => {
    return it.value.columns.map((col: any) => {
      return {
        ...col,
        title: `${it.value.joinTableName}.${col.title}`,
      }
    })
  })
})

const withJoinColumns = computed(() => {
  return props.columns.concat(...(joinsColumns.value || []).flat())
})

function handleRemove(index: number) {
  modelValue.value.splice(index, 1)
}

const submitHandle = (item?: any) => {
  if (!item) {
    const obj = {
      open: false,
      alias: alias.value,
      expression: expression.value,
    }
    modelValue.value = [...(modelValue.value || []), obj]
    open.value = false
    alias.value = ''
    expression.value = ''
  } else {
    item.open = false
  }
}
</script>
<style scoped lang="less">
.custom-olumn {
  .title {
    margin-bottom: 8px;
    font-size: 14px;
    color: rgb(147, 161, 171);
  }
  .emotion {
    display: flex;
    -webkit-box-align: center;
    align-items: center;
    flex-wrap: wrap;
    border-radius: 8px;
    background-color: rgba(147, 161, 171, 0.1);
    padding: 14px;
    color: rgb(147, 161, 171);
    gap: 10px;
    &-item {
      display: flex;
      -webkit-box-align: center;
      align-items: center;
      padding: 7px;
      background-color: rgb(147, 161, 171);
      border-radius: 6px;
      transition: background 300ms linear 0s;
      color: #fff;
      cursor: pointer;

      &:hover {
        background-color: #5387ac;
      }
    }
    .icon {
      font-size: 40px;
      cursor: pointer;

      &:hover {
        color: rgb(83, 135, 172);
      }
    }
  }
  .close-btn {
    border-radius: 50%;
    &:hover {
      background-color: #fff;
      color: #5387ac;
    }
  }
  .rt-15 {
    margin-right: 15px;
  }
}
</style>
