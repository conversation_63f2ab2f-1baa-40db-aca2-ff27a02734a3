<template>
  <a-layout-sider
    class="layout-sider"
    :trigger="null"
    :width="60"
    @mouseenter="handleMouseEnter"
    @mouseleave="handleMouseLeave"
  >
    <div
      class="sider-content"
      :class="{ expanded: !collapsed, collapsed: collapsed }"
      :style="{ width: !collapsed ? '230px' : '60px' }"
    >
      <!-- 这里放原本的内容 -->
      <div class="brand"><span v-if="!collapsed">大数据开发管理平台</span></div>
      <a-divider style="margin: 19px 0" />
      <div class="section-sider">
        <a-menu
          class="menu scrollbar-style"
          mode="inline"
          :selectedKeys="menuSelectedKeys"
          :openKeys="expandedKeys"
          @select="onSelect"
          @expand="onExpand"
          theme="light"
        >
          <template v-for="menu in transformMenuData(MenuData)" :key="menu.menuId">
            <a-menu-item
              v-if="!menu.childrens || menu.childrens.length === 0"
              :key="menu.menuId"
              :value="menu.menuUrl"
            >
              <a>
                <span class="icon">
                  <i :class="['iconfont', menu.menuIcon]"></i>
                </span>
                <span class="label" v-if="!collapsed">{{ menu.menuName }}</span>
              </a>
            </a-menu-item>
            <a-sub-menu v-if="menu.childrens && menu.childrens.length > 0" :key="menu.menuId">
              <template #title>
                <span class="icon">
                  <i :class="['iconfont', menu.menuIcon]"></i>
                </span>
                <span class="label" v-if="!collapsed">{{ menu.menuName }}</span>
              </template>
              <a-menu-item-group v-if="!collapsed">
                <template v-for="subMenu in menu.childrens" :key="subMenu.menuId">
                  <a-menu-item
                    v-if="!subMenu.childrens || subMenu.childrens.length === 0"
                    :key="subMenu.menuId"
                    :value="subMenu.menuUrl"
                  >
                    <a
                      ><span class="label" v-if="!collapsed">{{ subMenu.menuName }}</span></a
                    >
                  </a-menu-item>
                  <a-sub-menu
                    v-if="subMenu.childrens && subMenu.childrens.length > 0"
                    :key="subMenu.menuId"
                    :title="subMenu.menuName"
                  >
                    <template #title>
                      <span class="label" v-if="!collapsed">{{ subMenu.menuName }}</span>
                    </template>
                    <a-menu-item-group>
                      <a-menu-item
                        v-for="lastMenu in subMenu.childrens"
                        :key="lastMenu.menuId"
                        :value="menu.menuUrl"
                        style="font-size: 12px; margin-left: 10px"
                      >
                        <a
                          ><span class="label">{{ lastMenu.menuName }}</span></a
                        >
                      </a-menu-item>
                    </a-menu-item-group>
                  </a-sub-menu>
                </template>
              </a-menu-item-group>
            </a-sub-menu>
          </template>
        </a-menu>
        <a-menu theme="light" class="bottom-menu" :selectedKeys="menuSelectedKeys">
          <a-menu-item
            v-if="configMenu"
            :key="configMenu.menuId"
            class="logout-menu"
            @click="handleGoSetting"
          >
            <a>
              <span class="icon">
                <i :class="['iconfont', configMenu.menuIcon]"></i>
              </span>
              <span class="label" v-if="!collapsed">{{ configMenu.menuName }}</span>
            </a>
          </a-menu-item>
          <a-menu-item @click="handleLogout" class="logout-menu">
            <a>
              <span class="icon">
                <!-- <a-icon type="logout"/> -->
                <i :class="['iconfont', 'icon-guanliyuanqiehuan']"></i>
              </span>
              <span class="label" v-if="!collapsed">退出登录</span>
            </a>
          </a-menu-item>
        </a-menu>
      </div>
    </div>
  </a-layout-sider>
</template>

<script setup lang="ts">
import { computed, createVNode, ref, watch } from 'vue'
import { defineEmits } from 'vue'
import { Modal } from 'ant-design-vue'
import { ExclamationCircleOutlined } from '@ant-design/icons-vue'
import { logoutFn } from '@/utils/user'
import { useUserStore } from '@/stores/user'
import { storeToRefs } from 'pinia'
import { flattenTree } from '@/utils/utils'
import { useRoute, useRouter } from 'vue-router'
import type { UserMenu } from '@fs/fs-components/src/components/layout/layout'
import { parseUrlWithVaribles } from '@/layout/components/sidebars/helper'
import { jumpToUrl } from '@/layout/components/sidebars/helper'

const route = useRoute()
const router = useRouter()
const MenuData = ref<UserMenu[]>([])
const userStore = useUserStore()
const collapsed = ref(true)
const { userMenu, interfaceData } = storeToRefs(userStore)
const expandedKeys = ref<string[]>([])
const menuSelectedKeys = ref<string[]>([])
function onSelect(e: any) {
  handleClickMenuItem(e)
}

function onExpand(e: any) {
  expandedKeys.value = e.expandedKeys
}

watch(
  userMenu,
  (newValue) => {
    if (newValue) {
      const data = filterAuthMenu(filterTree(JSON.parse(JSON.stringify(newValue))))
      MenuData.value = data
    }
  },
  { deep: true, immediate: true },
)

function handleClickMenuItem(e: any) {
  const { selectedKeys } = e
  const currentItem = flattenTree(MenuData.value, 'childrens').find(
    (item: any) => item.menuId === e.key,
  )
  jumpToUrl(currentItem)
  menuSelectedKeys.value = selectedKeys
}

const configMenu = computed(() => {
  const { userMenu } = userStore
  return userMenu.find((item: any) => {
    return item.actionName === 'config'
  })
})

function transformMenuData(data: any[] = []): any[] {
  return data.map((item) => {
    return {
      ...item,
      key: item.menuId,
      label: item.menuName,
      title: item.menuName,
      icon: item.menuIcon,
      childrens:
        Array.isArray(item.childrens) &&
        item.childrens.length &&
        !(item.childrens as any[]).every((child: any) => child.menuType === 5)
          ? transformMenuData(item.childrens)
          : null,
    }
  })
}

function handleGoSetting() {
  jumpToUrl(configMenu.value)
  emit('menuItemChange', configMenu.value) // 新增：通知父组件
}

function filterAuthMenu(menu: any[]) {
  return menu.filter((item) => {
    if (item.childrens && item.childrens.length > 0) {
      item.childrens = filterAuthMenu(item.childrens)
    }

    // 这里解析 menuUrl 中 有 `${xxx}` 的变量，并尝试从 window.ENV 中匹配替换
    item.menuUrl = parseUrlWithVaribles(item.menuUrl)
    return item.menuType !== 2 && item.menuType !== 5 && !item.isChildrenMenu
  })
}

// 递归函数来筛选所有 menuType 等于 1 的菜单   1为菜单 2 是权限 3 页签
function filterTree(tree: UserMenu[]): UserMenu[] {
  const filterNode = (node: UserMenu): UserMenu | null => {
    if (node.isChildrenMenu === true || node.menuType === 2 || node.menuType === 3) {
      return null
    }
    let filteredChildren: UserMenu[] | undefined
    if (node.childrens) {
      filteredChildren = node.childrens
        .map(filterNode) // 递归过滤子节点
        .filter((childNode): childNode is UserMenu => childNode !== null) // 去掉值为 null 的节点
    }
    //@ts-ignore
    return { ...node, childrens: filteredChildren }
  }
  return tree.map(filterNode).filter((node): node is UserMenu => node !== null) // 最终去掉值为 null 的节点
}

function handleLogout() {
  Modal.confirm({
    title: '提示',
    icon: createVNode(ExclamationCircleOutlined),
    content: '真的要注销登录吗 ?',
    okText: '确认',
    cancelText: '取消',
    onOk() {
      logoutFn()
    },
  })
}

function handleMouseEnter() {
  collapsed.value = false
}
function handleMouseLeave() {
  collapsed.value = true
}

function findMenuKeyByPath(menuList: UserMenu[], path: string): any {
  for (const item of menuList) {
    if (item.path === path) return item
    if (item.childrens && item.childrens.length) {
      const found = findMenuKeyByPath(item.childrens, path)
      if (found) return found
    }
  }
  return null
}

// 查找菜单项的父级路径
function findMenuParentKeys(menuList: UserMenu[], targetMenuId: string): string[] {
  const findMenuItem = (menus: UserMenu[]): UserMenu | null => {
    for (const menu of menus) {
      if (menu.menuId === targetMenuId) {
        return menu
      }
      if (menu.childrens && menu.childrens.length > 0) {
        const found = findMenuItem(menu.childrens)
        if (found) return found
      }
    }
    return null
  }
  const targetMenu = findMenuItem(menuList)
  if (targetMenu?.meta?.menuKeyPath) {
    return targetMenu.meta?.menuKeyPath?.slice(0, -1) || []
  } else if (targetMenu?.levelIds) {
    const arr = targetMenu.levelIds.split('.').slice(0, -1)
    return arr
  }
  return []

  // function findParents(menus: UserMenu[], targetId: string, currentPath: string[] = []): boolean {
  //   for (const menu of menus) {
  //     const newPath = [...currentPath, menu.menuId]

  //     if (menu.menuId === targetId) {
  //       // 找到目标菜单，将路径中除了目标菜单本身的所有父级添加到parentKeys
  //       parentKeys.push(...newPath.slice(0, -1))
  //       return true
  //     }

  //     if (menu.childrens && menu.childrens.length > 0) {
  //       if (findParents(menu.childrens, targetId, newPath)) {
  //         return true
  //       }
  //     }
  //   }
  //   return false
  // }

  // findParents(menuList, targetMenuId)
}

// 新增：根据当前路由设置expandedKeys
// function setExpandedKeysByCurrentRoute() {
//   if (!MenuData.value.length) return

//   const { path, query } = route
//   let targetMenuId: string | null = null

//   if (path === '/iframePage') {
//     targetMenuId = query.menuId as string
//   } else {
//     const item = findMenuKeyByPath(interfaceData.value, path)
//     if (item) {
//       targetMenuId = item.meta?.menuId
//     }
//   }

//   if (targetMenuId) {
//     const parentKeys = findMenuParentKeys(MenuData.value, targetMenuId)
//     expandedKeys.value = parentKeys
//   }
//   console.trace('%c 设置 expandedKeys', 'color: red;', expandedKeys.value)
// }

// 监听菜单数据变化和路由变化 同步菜单选中状态
const emit = defineEmits(['menuItemChange'])
watch(
  [MenuData, () => route],
  ([menuList, routeData]) => {
    if (menuList && menuList.length) {
      const { path, query } = routeData
      if (path === '/iframePage') {
        menuSelectedKeys.value = [query.menuId as string]
        // 设置expandedKeys
        expandedKeys.value = findMenuParentKeys(MenuData.value, query.menuId as string)
        emit('menuItemChange', query) // 新增：通知父组件
        return
      }
      const item = findMenuKeyByPath(interfaceData.value, path)
      if (item) {
        menuSelectedKeys.value = [item.meta?.menuId]
        // 设置expandedKeys
        expandedKeys.value = item.meta?.menuKeyPath || []
        emit('menuItemChange', item) // 新增：通知父组件
      }
    }
  },
  { immediate: true, deep: true },
)
</script>

<style scoped lang="less">
.layout-sider {
  position: relative;
  width: 60px !important;
  min-width: 60px !important;
  max-width: 60px !important;
  overflow: visible !important; // 允许内容溢出
  background: #fff;
  height: 100%;
}

.sider-content {
  position: absolute;
  left: 0;
  top: 0;
  height: 100vh;
  width: 60px;
  background: #fff;
  transition: width 0.2s;
  z-index: 10;
  overflow: hidden;
  border-right: 1px solid #eee;
}
.sider-content.expanded {
  width: 230px;
}
.sider-content.collapsed :deep(.ant-menu-submenu-arrow) {
  display: none !important;
}
.brand {
  width: 100%;
  overflow: hidden;
  display: flex;
  justify-content: center;
  margin-top: 17px;
  height: 20px;
  color: rgba(0, 0, 0, 0.65);
  font-family:
    open sans,
    Helvetica,
    Arial,
    sans-serif;
  letter-spacing: -0.3px;
  font-variant: tabular-nums;
  font-weight: 600;
  font-size: 14px;
}
.ant-divider-horizontal {
  margin: 18px 0;
}
.ant-menu-light .ant-menu-item-selected {
  background-color: #e6f5ff !important;
}
:deep(.ant-menu-item-selected) .ant-menu-title-content {
  color: #141414 !important;
  flex: none;
  .label {
    font-weight: 600 !important;
  }
}
:deep(.ant-menu-submenu-title .iconfont) {
  font-size: 30px;
}
:deep(.ant-menu-sub .ant-menu-submenu-title) {
  padding: 0px 9px 0px 56px !important;
}
:deep(.ant-menu-item) {
  height: 54px !important;
  width: 100%;
  margin: 0;
  display: flex;
  align-items: center;
  border-radius: 0 !important;
  padding: 0 !important;
  a {
    padding: 10px 12px !important;
    display: flex;
  }
  .icon {
    display: flex;
    // width: 30px !important;
    // height: 30px !important;
    margin-right: 11px;
  }
  .iconfont {
    font-size: 30px;
  }
  .ant-menu-title-content {
    text-align: left;
  }
}
:deep(.ant-menu-submenu-title) {
  padding: 10px 12px !important;
  margin: 0 !important;
  height: 54px !important;
  text-align: left;
  width: 100%;
  border-radius: 0 !important;
  .icon {
    display: flex;
    margin-right: 11px;
  }
  .ant-menu-title-content {
    display: flex;
  }
}
:deep(.ant-menu-item-group) {
  .ant-menu-item a {
    padding: 0px 9px 0px 56px !important;
  }
  .ant-menu-item-group-title {
    padding: 0;
  }
  .ant-menu-item {
    height: auto !important;
    width: 100%;
  }
}
:deep(.ant-menu-light .ant-menu-submenu-selected > .ant-menu-submenu-title) {
  color: inherit;
}
:deep(.ant-menu-item:hover) {
  background-color: #e6f5ff !important;
}
.section-sider {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: calc(100% - 75px);
}
.fixed-sider {
  position: fixed;
  z-index: 1000;
  height: 100%;
}
.sider-placeholder {
  display: block;
  background: transparent;
  // 可加 border 或 background 方便调试
}
.scrollbar-style {
  overflow-y: auto;
  overflow-x: hidden;
}
:deep(.ant-menu) {
  border-inline-end: 0px !important;
}
</style>
