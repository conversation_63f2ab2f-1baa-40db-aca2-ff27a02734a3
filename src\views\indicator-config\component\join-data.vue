<template>
  <!-- <a-button v-if="showBtnIcon" type="primary" @click="handleClickAddJoin" class="mt-10">
    <BlockOutlined />Join
  </a-button> -->
  <div>
    <div class="title">联合（Jo<PERSON>）数据</div>
    <div class="join-box">
      <div class="emotion-box">
        <div class="ds-btn">
          <a-button
            type="primary"
            size="large"
            style="font-size: 14px; font-weight: bold"
            v-if="mode === 'edit'"
            >{{ joinItem.leftTableName }}</a-button
          >

          <!-- 新增模式 -->
          <a-button type="primary" size="large" style="font-size: 14px; font-weight: bold" v-else>{{
            leftTableName
          }}</a-button>

          <popoverMenu :menuList="JoinList" :handle-click-callback="handeClickJoin">
            <template #trigger>
              <a-button type="link" style="font-weight: bold; color: #509ee3">
                {{ joinItem?.joinType.toUpperCase() }}
              </a-button>
            </template>
          </popoverMenu>

          <!-- 编辑回显模式 -->
          <template v-if="mode === 'edit'">
            <dataSourceComponent
              ref="dataSourceRef"
              :databaseName="joinItem.databaseName"
              :dataSourceId="dataSourceId"
              :tableId="joinItem.tableId"
              :tableName="joinItem.joinTableName"
              :callback-fn="handleClickTable"
              :showTabs="false"
              :selectData="joinItem.joinFields"
            />
          </template>

          <!-- 新增模式 -->
          <template v-else>
            <dataSourceComponent
              ref="dataSourceRef"
              :databaseName="leftDatabaseName"
              :dataSourceId="dataSourceId"
              :tableId="leftTableId"
              :callback-fn="handleClickTable"
              :isUnSelectData="true"
              :showTabs="false"
              :presetTableData="availableTablesInCurrentDatabase"
              :presetActiveDatabase="currentActiveDatabase"
            />
          </template>
        </div>
      </div>
      <div
        style="width: 40px; text-align: center; font-weight: bold; color: #509ee3; cursor: default"
      >
        BY
      </div>
      <div class="emotion-box" v-if="joinItem?.joinConditions?.length">
        <div
          class="emotion-item select-field-btn"
          v-for="(item, idx) in joinItem.joinConditions"
          :key="idx"
          style="line-height: 1"
        >
          <Group
            :end-index="findIndexInJoinData(item.joinTableName)"
            @handleClick="(e) => confirmLeftTableField(e, idx)"
          >
            <div v-if="item.mainFieldName">
              <div text="12px lh-12px white b400">{{ item.mainTableName }}</div>
              <span text="14px lh-14px b700 white">{{ item.mainFieldName }}</span>
            </div>
            <span v-else text="14px b700" class="w-80px px-10px py-8px"> 选择字段 </span>
          </Group>
          <!-- <popoverMenu
            :menuList="byLeftFieldList"
            :handle-click-callback="handleClickLeftField"
            :index="index"
            mode="table"
          >
            <template #trigger>
              <div type="primary" v-if="item.mainFieldName">
                <span style="font-size: 12px; display: block; line-height: 1">{{
                  item.mainTableName
                }}</span>
                <span style="font-size: 14px; line-height: 1; font-weight: bold">{{
                  item.mainFieldName
                }}</span>
              </div>
              <div type="primary" v-else>
                <span style="font-weight: bold; font-size: 14px; padding: 8px 10px; width: 80px"
                  >选择字段</span
                >
              </div>
            </template>
          </popoverMenu> -->
          <div style="align-self: center">
            <popoverMenu
              :menuList="gxMenuList"
              :handle-click-callback="handleClickGxBack"
              :index="idx"
            >
              <template #trigger>
                <span class="operator-icon">{{ item.operator }}</span>
              </template>
            </popoverMenu>
          </div>
          <popoverMenu
            :menuList="byRightFieldList"
            :handle-click-callback="handleClickRightField"
            :index="idx"
            mode="table"
          >
            <template #trigger>
              <div type="primary" v-if="item.joinFieldName">
                <span style="font-size: 12px; display: block; line-height: 1">{{
                  item.joinTableName
                }}</span>
                <span style="font-size: 14px; line-height: 1; font-weight: bold">{{
                  item.joinFieldName
                }}</span>
              </div>
              <div type="primary" v-else>
                <span
                  style="
                    font-weight: bold;
                    font-size: 14px;
                    font-weight: bold;
                    padding: 8px 10px;
                    width: 80px;
                  "
                  >选择字段</span
                >
              </div>
            </template>
          </popoverMenu>

          <CloseOutlined
            @click="handleDeleteJoin(idx)"
            v-if="joinItem.joinConditions.length > 1"
            class="close-icon-button"
          />
          <!-- <span class="ml-10" v-if="joinConditions.length > 1 && index >= 0 && index !== joinConditions.length - 1">and</span> -->
        </div>
        <a-button type="primary" v-if="!showAddRight()" @click="addJoinConditions()" size="large">
          <PlusOutlined />
        </a-button>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, watch, nextTick, inject } from 'vue'
import { PlusOutlined, CloseOutlined } from '@ant-design/icons-vue'
import { getDataAssetColumnList } from '@/api/indexmanage/datasourceoptions/datasourceoptions'
import dataSourceComponent from './data-source.vue'
import popoverMenu from '@/components/popover-menu/popover-menu.vue'
import Group from '@/components/group/index.vue'
import type { ServerFieldItem } from '@/api/services/indicator/type'
import { JoinTableColumnsKey, type JoinTableColumns } from '../provide-keys'

//  关于：by
const props = defineProps<{
  // 查询 BY 左侧字段列表的默认数据
  leftTableId: string
  dataSourceId?: number | string
  leftDatabaseName?: string
  leftTableName?: string
}>()
watch(
  () => props.leftDatabaseName,
  (newVal) => {
    console.log('leftDatabaseName', newVal)
  },
  { immediate: true },
)

// 监听注入的表数据变化
watch(
  () => availableTablesInCurrentDatabase.value,
  (newVal) => {
    console.log('join-data: 接收到表数据更新:', newVal)
  },
  { immediate: true },
)

// 监听注入的数据库信息变化
watch(
  () => currentActiveDatabase.value,
  (newVal) => {
    console.log('join-data: 接收到数据库信息更新:', newVal)
  },
  { immediate: true },
)
const mode = ref('add') // 模式：add 或 edit

const joinItem = defineModel<Record<string, any>>({
  default: () => ({
    joinTableName: '',
    joinConditions: [],
  }),
})
const joinTableColumns = inject<Ref<JoinTableColumns>>(JoinTableColumnsKey)
const findIndexInJoinData = (tableName: string) => {
  return joinTableColumns?.value.findIndex((item) => item.tableName === tableName) ?? 0
}

const gxMenuList = [
  {
    value: '=',
    label: '=',
  },
  {
    value: '>',
    label: '>',
  },
  {
    value: '>=',
    label: '>=',
  },
  {
    value: '<',
    label: '<',
  },
  {
    value: '<=',
    label: '<=',
  },
  {
    value: '!=',
    label: '!=',
  },
]
const JoinList = ref([
  {
    value: 'left',
    label: 'Left Join',
  },
  {
    value: 'right',
    label: 'Right Join',
  },
  {
    value: 'inner',
    label: 'Inner Join',
  },
])
const dataSourceRef = ref<any>(null)

const byLeftFieldList = ref([])
const byRightFieldList = ref([])

// 从父组件注入表数据和数据库信息
const availableTablesInCurrentDatabase = inject<any>('availableTablesInCurrentDatabase', ref([]))
const currentActiveDatabase = inject<any>('currentActiveDatabase', ref(null))

const handeClickJoin = (key: any) => {
  joinItem.value.joinType = key
}

//  请求字段
const getField = async (type: string) => {
  let tableId = ''
  if (type === 'left') {
    if (mode.value === 'add') {
      tableId = props.leftTableId
    } else {
      tableId = joinItem.value.leftTableId
    }
    const { data } = await getDataAssetColumnList({ tableId })

    // @ts-ignore
    byLeftFieldList.value = data.map((item: any) => {
      return {
        ...item,
        value: item.title,
        label: item.title,
      }
    })
  } else {
    tableId = joinItem.value.tableId
    const { data } = await getDataAssetColumnList({ tableId })

    // @ts-ignore
    byRightFieldList.value = data.map((item: any) => {
      return {
        ...item,
        value: item.title,
        label: item.title,
      }
    })
  }
}

const handleClickTable = async (it: any) => {
  joinItem.value.tableId = it.tableId
  joinItem.value.joinTableName = it.tableName

  joinItem.value.joinConditions = [
    {
      mainTableName: props.leftTableName,
      mainFieldName: '',
      operator: '=',
      joinTableName: it.tableName,
      joinFieldName: '',
    },
  ]
  joinItem.value.joinFields = it.checkedColumns.map((fieldName: string) => ({
    fieldName,
    alias: fieldName,
    selected: true,
  }))
  joinItem.value.columns = it.dbColumns
  byRightFieldList.value = it.dbColumns.map((item: any) => {
    return {
      ...item,
      value: item.title,
      label: item.title,
    }
  })
}

const handleClickGxBack = (value: any, index: number) => {
  joinItem.value.joinConditions[index].operator = value
}

const handleClickLeftField = (key: string, index: number) => {
  joinItem.value.joinConditions[index].mainFieldName = key
}
const confirmLeftTableField = (e: ServerFieldItem, index: number) => {
  joinItem.value.joinConditions[index].mainFieldName = e.title
  joinItem.value.joinConditions[index].mainTableName = e.tableName
}

const handleClickRightField = (key: string, index: number) => {
  joinItem.value.joinConditions[index].joinFieldName = key
}

const addJoinConditions = () => {
  joinItem.value.joinConditions.push({
    mainTableName: props.leftTableName,
    mainFieldName: '',
    operator: '=',
    joinTableName: joinItem.value.joinTableName,
    joinFieldName: '',
  })
}

const showAddRight = () => {
  let isUnSet = false
  if (joinItem.value.joinConditions?.length) {
    isUnSet = joinItem.value.joinConditions.some(
      (it: any) => it.joinFieldName === '' || it.mainFieldName === '',
    )
  }
  return isUnSet
}

const handleDeleteJoin = (index: number) => {
  joinItem.value.joinConditions.splice(index, 1)
}

// 只有新增模式调用
const handleClickAddJoin = () => {
  joinItem.value = {
    leftTableId: props.leftTableId, // 做为左侧表的 ID
    leftTableName: props.leftTableName, // 做为左侧表的名称
    joinTableName: '',
    joinType: 'left',
    joinFields: [],
    columns: [],
    joinConditions: [],
  }
  // 查询 BY 左侧字段列表
  getField('left')

  nextTick(() => {
    dataSourceRef.value?.openSelectSource()
  })
}

watch(
  () => props.leftTableName,
  () => {
    getField('left')
  },
)

defineExpose({
  handleClickAddJoin,
})

onMounted(() => {
  // console.log('joinItem ', joinItem.value.joinTableName, joinItem.value.tableId)

  // console.log('joinItem props', props)

  if (joinItem.value?.joinTableName && joinItem.value?.tableId) {
    // 编辑模式
    mode.value = 'edit'
    getField('left')
    getField('right')
  } else {
    // 新增模式
    handleClickAddJoin()
  }
})
</script>

<style lang="less" scoped>
@default-text-color: #509ee3;
.title {
  color: @default-text-color;
  font-weight: bold;
  display: flex;
  margin-bottom: 0.5rem;
}
.join-box {
  display: flex;
  justify-content: center;
  align-items: center;
}

.emotion-item {
  display: flex;
  -webkit-box-align: center;
  align-items: center;
  justify-content: space-around;
  flex-wrap: wrap;
  // margin-top: 5px;
}
.emotion-box {
  // height: fit-content;
  border-radius: 8px;
  background-color: rgba(80, 158, 227, 0.1);
  padding: 14px;
  color: #509ee3;
  flex: 1;
  display: flex;
  align-items: center;
  gap: 10px;
  .ds-btn {
    display: flex;
    font-weight: bold;
    font-weight: 12px;
    color: rgb(255, 255, 255);
    // border-radius: 6px;
    border: 2px solid transparent;
    cursor: pointer;
    pointer-events: auto;
    -webkit-box-align: stretch;
    align-items: center;
    transition: border 300mslinear;
    .ds-btn-left {
      display: flex;
      -webkit-box-align: center;
      align-items: center;
      padding: 10px;
      background-color: rgb(80, 158, 227);
      border-top-left-radius: 6px;
      border-bottom-left-radius: 6px;
      transition: background 300mslinear;
    }
    .ds-btn-right {
      display: flex;
      -webkit-box-align: center;
      align-items: center;
      justify-content: center;
      background-color: rgb(80, 158, 227);
      border-left: 1px solid rgba(255, 255, 255, 0.25);
      border-top-right-radius: 6px;
      border-bottom-right-radius: 6px;
      transition: background 300ms linear;
      width: 37px;
      padding: 0px;
    }
  }
}

.source-list {
  background-color: #fafbfc;
  margin: 10px;
  max-height: calc(100vh - 500px);
  overflow-y: scroll;
  overflow-x: hidden;
  padding: 15px 25px 15px 15px;
  width: 100%;
  box-sizing: border-box;
  max-width: 100%;
  .source-item {
    // border-radius: 10px;
    display: flex;
    justify-content: space-between;
    padding: 8px;
    width: 100%;
    max-width: 100%;
    cursor: pointer;
    margin-bottom: 5px;
    .left-box {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      flex: 1;
      text-decoration: none;
      font-size: 0.875rem;
      color: #4c5773;
    }
    .right-box {
      width: 20px;
    }
  }
  .source-item-active.table-item {
    background-color: #509ee3;
    color: #fff;
  }
  .source-item-active.table-item:hover {
    background-color: #509ee3;
    color: #fff !important;
  }
  .table-item:hover {
    color: #4c5773 !important;
  }
  .source-item:hover {
    background-color: #eef5fc;
  }
  .source-item-active {
    background-color: #edf2f5;
  }
}

.select-field-btn {
  background-color: #509ee3;
  border-radius: 8px;
  color: #fff;
  padding: 4px 16px;
  cursor: pointer;
  min-height: 40px;
}

.select-field-btn:hover {
  // background-color: #7abdf0;
}

.field-list {
  display: flex;
  flex-direction: column;
  max-height: 600px;
  overflow-y: scroll;
  overflow-x: hidden;
  padding: 0 4px;
  .field-item {
    display: flex;
    width: 100%;
    padding: 5px 6px;
    // border-radius: 6px;
  }
  .field-item:hover {
    background-color: #edf2f5;
  }
}

.operator-icon {
  display: inline-block;
  width: 30px;
  color: #fff;
  font-weight: bold;
  border: 1px solid #f9f9f9;
  padding: 2px 0;
  margin: 0 20px;
  border-radius: 4px;
  text-align: center;
  &:hover {
    background-color: #f9f9f9;
    color: #000;
  }
}

.close-icon-button {
  position: relative;
  right: -10px;
  padding: 2px;
  &:hover {
    // background-color: #333;
    color: #509ee3;
    background-color: #fff;
    border-radius: 50%;
  }
}
</style>
