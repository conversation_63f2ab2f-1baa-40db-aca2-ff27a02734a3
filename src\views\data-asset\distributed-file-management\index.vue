<template>
  <div class="distributed-file-management" :style="{ '--left-width': leftWidth + '%' }">
    <a-row justify="start" align="top" type="flex" :wrap="true" class="management-row">
      <a-col class="management-col" :style="{ width: leftWidth + '%' }">
        <directory-tree
          ref="directoryTreeRef"
          :params="params"
          :refreshCount="refreshCount"
          @add-directory-tree="handleScameInfoSelect"
        />
      </a-col>
      <div class="resize-handle" @mousedown="startResize">
        <div class="panel-grabber-vertical">
          <div class="handle-icon handle-icon-vertical"></div>
        </div>
      </div>
      <a-col
        class="management-col content right-border"
        :style="{ width: 'calc(100% - ' + leftWidth + '% - 4px)', paddingLeft: '14px' }"
      >
        <filed-table
          ref="filedTableRef"
          :filed-table-data="filedTableData"
          :params="params"
          v-if="params.id && params.fullPath.length > 0"
          @refresh="handleRefresh"
          @refreshFiledTable="refreshFiledTable"
        />
        <a-empty v-else style="margin-top: 100px">
          <template #description>请在左侧选择一个目录文件进行查看 </template>
        </a-empty>
      </a-col>
    </a-row>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import DirectoryTree from './components/directory-tree.vue'
import filedTable from './components/filed-table.vue'

defineOptions({ name: 'distributed-file-management' })
const directoryTreeRef = ref()
const filedTableRef = ref()
const filedTableData = ref<Record<string, any>>()

// 拖拽相关的状态
const leftWidth = ref(20) // 初始宽度设为20%
const isResizing = ref(false)
const startX = ref(0)
const startLeftWidth = ref(0)
// 记录右侧文件列表刷新次数
const refreshCount = ref(0)
// 当前选择的数据源id和文件夹全路径
const initParams = () => {
  return {
    id: undefined,
    list: [],
    fullPath: '', // 用来存放全路径
    treeNode: null,
  }
}

const params = ref<any>(initParams())
// 开始拖拽
const startResize = (e: MouseEvent) => {
  isResizing.value = true
  startX.value = e.clientX
  startLeftWidth.value = leftWidth.value
  document.addEventListener('mousemove', handleMouseMove)
  document.addEventListener('mouseup', stopResize)
}

// 文件列表和相关参数刷新
const handleScameInfoSelect = (data: any) => {
  filedTableData.value = [...data.list]
  params.value = { ...data }
  handleRefresh()
}

const handleRefresh = () => {
  filedTableRef.value?.refresh()
}

// 上传成功刷新文件列表
const refreshFiledTable = () => {
  refreshCount.value += 1
}
// 处理拖拽移动
const handleMouseMove = (e: MouseEvent) => {
  if (!isResizing.value) return

  const containerWidth = document.querySelector('.management-row')?.clientWidth || 0
  const deltaX = e.clientX - startX.value
  const deltaPercent = (deltaX / containerWidth) * 100

  const newWidth = Math.max(15, Math.min(60, startLeftWidth.value + deltaPercent))
  leftWidth.value = newWidth
}

// 停止拖拽
const stopResize = () => {
  isResizing.value = false
  document.removeEventListener('mousemove', handleMouseMove)
  document.removeEventListener('mouseup', stopResize)
}

onMounted(async () => {
  handleScameInfoSelect(params.value)
})
</script>

<style src="./style.less" lang="less" scoped></style>
