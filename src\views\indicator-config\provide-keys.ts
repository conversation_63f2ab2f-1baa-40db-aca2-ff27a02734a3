import type { ServerFieldItem } from '@/api/services/indicator/type'
import type { ParamItem } from './component/dynamic-param.vue'
/**
 * 联合表字段
 */
export type JoinTableColumns = { fields: ServerFieldItem[]; tableName: string }[]
export const JoinTableColumnsKey = Symbol() as InjectionKey<Ref<JoinTableColumns>>
export type DynamicParam = ParamItem[]
export const DynamicParamKey = Symbol() as InjectionKey<Ref<DynamicParam>>
export const NeedDynamicParamKey = Symbol() as InjectionKey<Ref<boolean>>
