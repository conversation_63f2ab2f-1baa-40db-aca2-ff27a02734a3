import { getApiData } from '@/api/services/manage'
import { generateDataFromList } from '@/utils/tool'

export const getAllCode = async (pageData: any) => {
  const form = pageData.form || []
  const { importState } = pageData
  const str =
    `
      ${getTamplate(pageData)}
    
    ` +
    '<' +
    `script setup lang='ts'>
    import { ref, computed, onMounted, watch } from 'vue'
    import { Form, getPattern } from '@fs/fs-components'
    import { message } from 'ant-design-vue/es/components'
    import { ${getImportName(pageData?.request)} } from '@/api/${getToLocaleLowerCase(pageData?.project)}/${getToLocaleLowerCase(pageData?.module)}/${getToLocaleLowerCase(pageData?.module)}'
  
    ${!pageData?.modal && isDetail(pageData) && importState ? `import modalComponent from "./component/modal-component.vue"` : ''}

    
    const layout = {
      layout: 'horizontal',
      labelCol: { span: 4 },
      wrapperCol: { span: 18 }
    }
  
    const FormRefs = ref()
      ${
        !pageData?.modal && isDetail(pageData) && importState
          ? ` const open = ref(false)
       `
          : ''
      }
    const formData = ref<any>({})
       ${
         !pageData?.modal && isDetail(pageData)
           ? ` const editState = ref(false)
       `
           : ''
       }
  
    const items = ref<any[]>(${getItems(form)})
  
    const ConfirmLoading = ref(false)
  
     ${getVariable(pageData)}
  
     ${
       isDetail(pageData)
         ? ` const detailData = ref<any>({})
const isDetail = ref(true)
${
  !pageData?.modal && importState
    ? `const editHandle = () => {
open.value = true
}`
    : ''
}

${
  !pageData?.modal && !importState
    ? `const editHandle = () => {
isDetail.value = false
editState.value = true
}`
    : ''
}

  
   const getDetail = async () => {
      const { data } = await  ${isDetail(pageData)}(${await getParamDetail(pageData)})
      detailData.value = data
      formData.value = data
   }
  
  
  ${
    pageData?.modal
      ? `watch(
    () => newModelValue.value,
    (val) => {
      if (val) {
        getDetail()
      }
    },
  )`
      : ` onMounted(async () =>{
     getDetail()
   })`
  }
   `
         : ''
     }
  
   async function submitHandle(data: any) {
    ${getSubmit(pageData)}
    }
   
    </` +
    `script>
      <style scoped lang="less">
      .button-group{
        padding-left: 16.7%;
        .submit-button{
            margin-right: 20px;
          }
      }
      </style>
        `
  return str
}

const getTamplate = (pageData: any) => {
  const { importState } = pageData
  if (pageData?.modal) {
    return `
      <template>
         <a-modal
        :open="newModelValue"
        title="编辑"
        width="${pageData?.width ?? 600}px"
        :confirm-loading="ConfirmLoading"
        @cancel="cancel"
      >
          <h2>${pageData.title}</h2>
           <Form
            :items="items"
            ref="FormRefs"
            v-model="formData"
            :extral="{}"
            @submit="submitHandle"
            :layout="layout"
          >
            
        </Form>
         <template #footer>
              <a-button key="back" @click="cancel">取消</a-button>
              <a-button key="submit" type="primary" :loading="ConfirmLoading" @click="modalHandleOk"
                >确定</a-button
              >
            </template>
      </a-modal>    
      </template>
  
      <!-- 
      页面使用方法 引入控件
      import modalComponent from "./component/modal-component.vue"
      绑定一个boolean值 通过改变值控制弹窗显隐 
      const open = ref(false)
      <modalComponent}Modal v-model="open"></modalComponent>
      -->
      `
  }
  return `<template>
    <div style="width: ${pageData?.width ?? 600}px">
        <h2>${pageData.title}</h2>
        <Form
            :items="items"
            ref="FormRefs"
            v-model="formData"
            :extral="{}"
            @submit="submitHandle"
            :layout="layout"
            ${isDetail(pageData) ? ':isDetail="isDetail"' : ''}
            ${isDetail(pageData) ? ':detailData="detailData"' : ''}
          >
        </Form>
         <div  class="button-group">
          ${isDetail(pageData) ? '<a-button v-if="isDetail === true"  @click="editHandle" class="submit-button">编辑</a-button>' : ''}
            <div ${isDetail(pageData) ? 'v-if="!isDetail"' : ''} >
             ${isDetail(pageData) ? ` <a-button class="submit-button" @click="editState = false,isDetail=true" v-if="editState">取消</a-button>` : ''} 
                <a-button class="submit-button" :loading="ConfirmLoading" @click="submit" type="primary">提交</a-button>
                <a-button @click="reset">重置</a-button>
            </div> 
        </div>
    </div>
          ${isDetail(pageData) && importState ? ` <modalComponent v-model="open" @update:formData="getDetail"></modalComponent>` : ''}
    </template>`
}

const getImportName = (list: any[] | undefined) => {
  if (list) {
    // 去重并获取到存在的接口名称
    const arr: any[] = list.reduce((acc, item) => {
      if (item.api && !acc.some((t: { api: any }) => t.api === item.api)) {
        acc.push(item)
      }
      return acc
    }, [])
    return arr.reduce((cur, item, index) => {
      return cur + item?.api + (index === list.length - 1 ? '' : ', ')
    }, '')
  }
  return ''
}

/**
 * 根据给定的物品数组生成格式化后的物品字符串数组
 * 此函数用于将原始物品数据转换为具有特定格式的字符串数组，以便在Vue组件中使用
 * 每个物品被转换为一个包含名称、标签、类型和规则的对象形式的字符串，其中规则包括验证要求
 * 如果物品数组为空，则返回空数组
 *
 * @param {any[]} items - 待处理的物品数组
 * @returns {string | []} - 格式化后的物品字符串数组，如果输入为空数组则返回空数组
 */
const getItems = (items: any[]) => {
  // 检查物品数组是否不为空
  if (items.length > 0) {
    // 使用reduce方法将物品数组转换为单个字符串
    const str = items.reduce((cur, item) => {
      // 构建每个物品的详细信息字符串，包括名称、标签、类型和规则
      // 标签优先使用物品的描述，如果没有描述，则使用名称
      // 规则部分包括是否为必填项，以及相应的验证消息
      return (
        cur +
        `{
          name: '${item.name}',
          label: '${item?.description ?? item.name}',
          type: '${item.type}',
          span: '${item.span}',
          rules: [
            { required: ${item?.required ?? 'false'}, message: '${item?.description ?? item.name}不能为空' },
            ${getPattern(item)}
          ],
          ${getCompProps(item)}
        },`
      )
    }, '')
    // 返回格式化后的物品字符串数组
    return `[${str}]`
  }
  // 如果物品数组为空，返回空数组
  return '[]'
}

const getVariable = (pageData: any) => {
  if (pageData?.modal) {
    return `
      interface Modal {
        modelValue: boolean
      }
  
      const props: Modal = withDefaults(defineProps<Modal>(), {
        modelValue: false,
      })
  
      const emit = defineEmits(['update:modelValue', 'update:formData'])
  
      const newModelValue = computed({
        get: () => props.modelValue,
        set: (value) => {
          emit('update:modelValue', value)
        },
      })
  
      const cancel = () => {
          ${
            isDetail(pageData)
              ? `  if(!isDetail.value){
          isDetail.value = true
          return 
        }`
              : ''
          }
        newModelValue.value = false
        formData.value = {};
        FormRefs.value.reset();
      }
  
      const modalHandleOk = () => {
        FormRefs.value.submit()
      }
      `
  }
  return `  
  
    function submit() {
      FormRefs.value.submit()
    }
  
    function reset() {
      formData.value = {}
      FormRefs.value.reset()
    }`
}

const getParamDetail = async (pageData: any) => {
  const { data } = await getApiData({ interfaceName: isDetail(pageData) })
  const obj: any = {}
  data.params.forEach((item: any) => {
    obj[item.name] = generateDataFromList(item.type)
  })
  return JSON.stringify(obj)
}

const getSubmit = (pageData: any) => {
  const { importState } = pageData
  let content = `
    try{
    ConfirmLoading.value = true
    ${getOperation('add', pageData) ? 'await ' + getOperation('add', pageData) + '(data)' : ''}
     message.success('新增成功')
     ConfirmLoading.value =  false
     ${isDetail(pageData) ? `getDetail()` : ''}
     ${
       isDetail(pageData) && !importState
         ? `
     isDetail.value = true
    editState.value = false`
         : ''
     }
    } catch(error: any){
     ConfirmLoading.value =  false
      // message.error(error?.msg || '新增失败')
    } 
    `
  if (pageData?.modal) {
    content = `
      try{
        ConfirmLoading.value = true
        ${getOperation('add', pageData) ? 'await ' + getOperation('add', pageData) + '(data)' : ''}
        message.success('新增成功')
        ConfirmLoading.value =  false
        newModelValue.value = false
        formData.value = {};
        FormRefs.value.reset();
           ${pageData?.modal ? `emit('update:formData')` : ''}
    ${isDetail(pageData) ? `isDetail.value = true` : ''}
      } catch(error){
        ConfirmLoading.value =  false
         //  message.error(error?.msg || '新增失败')
      }
    `
  }
  return content
}

const isDetail = (pageData: any) => {
  const item = pageData.request.find((item: { name: string }) => item.name === '详情')
  if (item?.api) {
    return item?.api
  } else {
    return false
  }
}

/**
 * 根据提供的项目列表生成验证模式字符串
 * 该函数用于根据每个项目中定义的模式，生成一个用于表单验证的模式字符串
 * 主要用于动态地为表单中的不同字段添加正则表达式验证规则
 *
 * @param items 一个对象数组，每个对象包含需要生成验证规则的信息，如pattern和description/name字段
 * @returns 返回一个字符串，包含根据items生成的验证模式规则如果items为空，则返回空字符串
 */
const getPattern = (item: any) => {
  if (item?.pattern) {
    return `{ pattern:  getPattern('${item.pattern}'), message: '${item?.description ?? item.name}格式不正确' }`
  }
  // 如果items数组为空，返回空字符串
  return ``
}

const getCompProps = (item: any) => {
  const obj: any = {}
  if (item?.description ?? item.name) {
    if (!obj.compProps) obj.compProps = {}
    obj.compProps.placeholder = `请输入${item?.description ?? item.name}`
  }
  if (item?.options) {
    if (!obj.compProps) obj.compProps = {}
    obj.compProps.options = item.options
  }

  if (JSON.stringify(obj) === '{}') return ''
  return 'compProps: ' + JSON.stringify(obj.compProps, null, 2)
}

// 获取api函数名称
export const getOperation = (type: string, pageData: any) => {
  let label = ''
  switch (type) {
    case 'add':
      label = '新增'
      break
    case 'edit':
      label = '编辑'
      break
    case 'del':
      label = '删除'
      break
    default:
      ''
      break
  }
  const data = pageData.request?.find((item: { name: string }) => item.name === label)
  return data?.api ?? ''
}

export const getTableAllCode = async (pageData: any, interfaceInfos: any[]) => {
  const request = pageData?.request || []
  const search = pageData?.search || []
  const str =
    `<template>
    <Table :columns="columns" ${getResquestData(request)} ref="tableRef" :searchFormState="form">
      <template #operate>
        <a-button type="primary" @click="addTableHandel">新增</a-button>
      </template>
      <template #search>
        ${getSearch(pageData?.search)}
      </template>
      <template #bodyCell="data">
        <template v-if="data.column.dataIndex === 'action'">
          <a-button type="link" @click="editTableHandle(data.record)">编辑</a-button>
        <a-popconfirm title="是否确认删除" @confirm="confirm(data.record)">
          <a-button type="link">删除</a-button>
        </a-popconfirm>
        </template>
        <template v-else>
          {{ data.value }}
        </template>
      </template>
</Table>

<a-modal
    v-model:open="modalValue"
    @ok="modalHandleOk"
    :title="modalType === 'edit' ? '编辑' : '新增'"
    @cancel="cancel"
    width="800px"
    :confirm-loading="confirmLoading"
  >
    <a-form
      ref="formRef"
      :model="modalForm"
      :rules="rules"
      :label-col="labelCol"
      :wrapper-col="wrapperCol"
    >
      ${getModalItem(pageData)}
    </a-form>
  </a-modal>
</template>

` +
    '<' +
    `script setup lang='ts'>
import { ref } from 'vue'
import { Table, getPattern } from '@fs/fs-components'
import type { Rule } from 'ant-design-vue/es/form'
import { message } from 'ant-design-vue/es/components'
import { ${getImportName(pageData?.request)} } from '@/api/${getToLocaleLowerCase(pageData?.project)}/${getToLocaleLowerCase(pageData?.module)}/${getToLocaleLowerCase(pageData?.module)}'

const labelCol = { span: 5 }
const wrapperCol = { span: 13 }
const form: Record<string, any> = ref({
  ${search.reduce((cur: string, item: { name: any; type: string }) => {
    return cur + `${item.name}: ${item.type === 'select' ? 'undefined' : "''"},`
  }, '')}
})
const columns = ${getCulumns(pageData?.dataSource)}
const rules: Record<string, Rule[]> = {}
const modalForm: Record<string, any> = ref({})
const modalValue = ref(false)
const modalType = ref<string>('')
const formRef = ref<HTMLFormElement | null>(null)
const confirmLoading = ref<boolean>(false)
const tableRef = ref<InstanceType<typeof Table> | null>(null)

function addTableHandel() {
  modalValue.value = true
  modalType.value = 'add'
}

function editTableHandle(data: any) {
  modalForm.value = data
  modalType.value = 'edit'
  modalValue.value = true
}

function cancel() {
  modalValue.value = false
  formRef.value?.resetFields()
}

async function confirm(data: any){
  try {
    ${getOperation('del', pageData) ? 'await ' + getOperation('del', pageData) + '(data)' : ''}
    message.success('删除成功')
  } catch (error) {
  console.log(error)
  }
}

async function modalHandleOk() {
  try{
     if(!formRef.value) {
        console.error('获取form表单失败')
        return
      }
     await formRef.value.validate()
      confirmLoading.value = true
      let handle
      if (modalType.value === 'edit') {
        handle = ${getOperation('edit', pageData) ? getOperation('edit', pageData) : '()=>{}'}
      } else {
        handle = ${getOperation('add', pageData) ? getOperation('add', pageData) : '()=>{}'}
      }
     await handle(modalForm.value)
      tableRef.value?.getTableData()
      message.success('操作成功')
      modalValue.value = false
      confirmLoading.value = false
    } catch(error){
       confirmLoading.value = false
       console.log(error)
    }
}
</` +
    `script>`
  return str
}

const getResquestData = (request: any) => {
  const data = request.find((item: { name: string }) => item.name === '查询')
  if (!data?.api) return ''
  return `:getData='${data.api}'`
}

const getSearch = (list: any[] | undefined) => {
  if (!list) return ''
  let str = ''
  str = list.reduce((cur, item) => {
    return (
      cur +
      `<a-form-item label="${item?.description ?? item.name}" name="${item.name}">
  ${getItem(item)}
</a-form-item> \n`
    )
  }, '')
  return str
}

const getModalItem = (pageData: any) => {
  const list = pageData.form
  let formItems = ''
  for (let i = 0; i < list.length; i++) {
    const item = list[i]
    formItems += `
              <a-form-item label="${item?.description ?? item.name}" name="${item.name}"  :rules="[{ required: ${item.required}, message: '请输入${item?.description ?? item.name}' },${getPattern(item)}]">
        <a-input v-model:value="modalForm.${item.name}" placeholder="请输入${item?.description ?? item.name}" />
      </a-form-item>
              `
  }
  return formItems
}

const getCulumns = (list: any[] | undefined) => {
  if (!list) return '[]'
  const arr = list.map((item) => {
    const title = item?.description !== '' ? item?.description : item.name
    return {
      title,
      dataIndex: item.name,
      key: item.name,
    }
  })
  arr.push({
    title: '操作',
    key: 'action',
    dataIndex: 'action',
  })
  return JSON.stringify(arr, null, 2)
}

const getItem = (item: any) => {
  let str = ''
  switch (item.type) {
    case 'input':
      str = `<a-input v-model:value="form.${item.name}" placeholder="请输入${item?.description ?? item.name}" />`
      break
    case 'date':
      str = `<a-date-picker v-model:value="form.${item.name}" placeholder="请输入${item?.description ?? item.name}" />`
      break
    case 'select':
      str = ` <a-select v-model:value="form.${item.name}" placeholder="请选择${item?.description ?? item.name}">
${item.options && item.options.map((item: any) => `  <a-select-option value="${item.value}">${item.label}</a-select-option>`)}
</a-select>`
      break
  }
  return str
}

function getToLocaleLowerCase(value: string) {
  return value.toLocaleLowerCase()
}
