<template>
  <div class="header-box">
    <div class="left">知识模型</div>
    <div class="right">
      <a-select
        v-model:value="nodeValue"
        show-search
        placeholder="选择标签筛选"
        style="width: 250px"
        :options="nodeSelectList"
        :filter-option="filterOption"
        allowClear
        mode="multiple"
        size="small"
        :max-tag-count="3"
      ></a-select>
      <a-select
        v-model:value="relValue"
        show-search
        placeholder="选择关系筛选"
        style="width: 250px"
        :options="relationSelectList"
        :filter-option="filterOption"
        mode="multiple"
        :max-tag-count="2"
        size="small"
        allowClear
      ></a-select>
      查询限制：
      <a-input-number v-model:value="limit" :min="1" :max="10000" size="small" />
      <!-- <a-button v-if="!isEdit" type="primary" @click="handleEdit"> 编辑schema </a-button>
      <a-button v-if="isEdit" @click="handleCancel"> 取消 </a-button>
      <a-button v-if="isEdit" type="primary" @click="handleSave" :loading="saveLoading">
        保存
      </a-button>
      <a-button v-if="!isEdit" @click="handleConcept"> 概念模型 </a-button>
      <a-button v-if="!isEdit" @click="handleRule"> 规则管理 </a-button> -->
    </div>
  </div>
  <div class="editor-wrap" v-show="isEdit">
    <Editor ref="editorRef" />
  </div>
  <div class="content" v-show="!isEdit">
    <!-- <a-radio-group class="radio-select" v-model:value="modelType">
      <a-radio-button :value="ShowChart.SCHEMA">点状模式</a-radio-button>
      <a-radio-button :value="ShowChart.TREE">树状模式</a-radio-button>
    </a-radio-group> -->
    <keep-alive>
      <component :is="pageMap[modelType]" :schemaData="schemaData" :treeData="treeData"></component>
    </keep-alive>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onBeforeMount } from 'vue'
import TreeChart from './tree-chart.vue'
import Editor from './editor.vue'
import Schema from '../knowledge-task/schema.vue'
import {
  reqGetAllRelationType,
  reqGetAllTags,
  reqGetChartData,
  reqGetKnowledgeModel,
  reqGetTreeData,
  reqSaveSchemaScript,
} from '@/api/services/knowledge'
import { getRandomColor } from './assets-data'
import { message } from 'ant-design-vue'
import { ShowChart } from '../type'
import { useRoute, useRouter } from 'vue-router'

const schemaData = reactive({
  demoDataListData: [],
  initLinkListData: [],
})
const pageMap = {
  [ShowChart.SCHEMA]: Schema,
  [ShowChart.TREE]: TreeChart,
}
const treeData = ref({})
const route = useRoute()
const router = useRouter()
const isEdit = ref(false)
const modelType = ref(ShowChart.SCHEMA)
const editorRef = ref()
const saveLoading = ref()

const getSchemaChartList = async (config: any = {}) => {
  const res = await reqGetKnowledgeModel({
    projectId: route.query?.projectId,
    limit: limit.value,
    nodeLabels: nodeValue.value,
    relTypes: relValue.value,
    ...config,
  })
  schemaData.demoDataListData = res.entityTypeDTOList.map((item: any) => {
    return {
      ...item,
      label: { show: true, position: 'bottom' },
      itemStyle: { color: getRandomColor() },
    }
  })
  schemaData.initLinkListData = res.relationTypeDTOList.map((item: any) => {
    return {
      ...item,
      source: String(item.startEntity.id), // 一定要确保source、target字段是字符串类型
      target: String(item.endEntity.id),
      level: item.originBelongToProject,
      lineStyle: { curveness: 0.3, color: '#2596FF', width: 2 },
    }
  })
  console.log('schema图表数据', schemaData)
}

/* ------------节点下拉、数字输入框数据----------- */
// #region
const nodeSelectList = ref([])
const relationSelectList = ref([])
const nodeValue = ref(undefined)
const relValue = ref(undefined)
const limit = ref(1000)
const filterOption = (input: string, option: any) => {
  return option.value.toLowerCase().indexOf(input.toLowerCase()) >= 0
}
const handleChange = (value: string) => {
  console.log(`selected ${value}`)
}

watch(
  () => [nodeValue.value, relValue.value, limit.value],
  () => {
    console.log('nodeValue', nodeValue.value)
    console.log('relValue', relValue.value)
    getSchemaChartList()
  },
)

const getNodeSelectList = async () => {
  const res = await reqGetAllTags({ projectId: route.query?.projectId })
  console.log('%c [ res ]-113', 'font-size:13px; background:#539b71; color:#97dfb5;', res)
  nodeSelectList.value = res.map((item: any) => {
    return {
      label: item,
      value: item,
    }
  })
}

const getRelationSelectList = async () => {
  const res = await reqGetAllRelationType({ projectId: route.query?.projectId })
  console.log('%c [ relationRes ]-133', 'font-size:13px; background:#bb7a7d; color:#ffbec1;', res)
  relationSelectList.value = res.map((item: any) => {
    return {
      label: item,
      value: item,
    }
  })
}

onMounted(() => {
  getNodeSelectList()
  getRelationSelectList()
})

// #endregion

const getTreeChartList = async () => {
  const res = await reqGetTreeData({
    projectId: route.query?.projectId,
  })
  treeData.value = res
  console.log(222, res)
}

const handleSave = async () => {
  saveLoading.value = true
  await reqSaveSchemaScript({
    data: editorRef.value?.content,
  })
  getSchemaChartList()
  saveLoading.value = false
  message.success('保存成功')
  // 刷新数据
  isEdit.value = !isEdit.value
}
const handleCancel = () => {
  console.log('取消编辑')
  isEdit.value = !isEdit.value
}

const handleConcept = () => {
  router.push(`/knowledgeBuilder/knowledgeModel/conceptual?projectId=${route.query?.projectId}`)
}
const handleRule = () => {
  router.push(`/knowledgeBuilder/knowledgeModel/ruleManager?projectId=${route.query?.projectId}`)
}

const handleEdit = () => {
  // 编辑schema
  isEdit.value = !isEdit.value
  editorRef.value?.getSchemaScript()
  console.log('编辑')
}

onBeforeMount(() => {
  // 获取schema图表数据
  getSchemaChartList()
  // 获取树状图数据
  // getTreeChartList()
})
</script>
<style scoped lang="less">
.header-box {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 50px;
  .left {
    font-size: 20px;
    line-height: 28px;
    font-weight: 500;
    color: #000;
  }
  .right {
    display: flex;
    align-items: center;
    gap: 8px;
  }
}
.editor-wrap {
  width: 100%;
  height: 100%;
  background: #fff;
  position: relative;
  z-index: 111;
  border-radius: 4px;
}
.content {
  width: 100%;
  height: calc(100% - 112px);
  background: #fff;
  position: relative;
  overflow: hidden;
  z-index: 111;
  // border: 1px solid #eee;
  border-radius: 4px;
  // /deep/ .ant-drawer {
  //   position: absolute;
  // }
  // /deep/ .ant-drawer-mask {
  //   background: transparent;
  // }
  .radio-select {
    position: absolute;
    top: 20px;
    left: 20px;
    z-index: 2;
  }
  .model-container {
    z-index: 1;
    width: 100%;
    height: 100%;
  }
}
</style>
