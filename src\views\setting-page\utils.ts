import { orderBy } from 'lodash'
/**
 * 获取源库类型列表
 */
export function getDbTypeList() {
  const result = [
    {
      value: 1,
      label: 'MySQL',
    },
    {
      value: 2,
      label: 'Kudu',
    },
    {
      value: 3,
      label: 'TiDB',
    },
    {
      value: 4,
      label: 'Hive',
    },
    {
      value: 5,
      label: 'Oracle',
    },
    {
      value: 6,
      label: 'SQLServer',
    },
    {
      value: 7,
      label: 'Cache',
    },
    {
      value: 8,
      label: 'MongoDB',
    },
    {
      value: 9,
      label: 'File',
    },
    {
      value: 10,
      label: 'HTTP',
    },
    {
      value: 11,
      label: 'Impala',
    },
    {
      value: 12,
      label: 'Greenplum',
    },
    {
      value: 13,
      label: 'DM',
    },
    {
      value: 14,
      label: 'Druid',
    },
    {
      value: 15,
      label: 'Kafka',
    },
    {
      value: 16,
      label: 'PostgreSQL',
    },
    {
      value: 17,
      label: 'Socket',
    },
    {
      value: 18,
      label: 'Flink',
    },
    {
      value: 19,
      label: 'GBase',
    },
    {
      value: 21,
      label: 'HDFS',
    },
    {
      value: 22,
      label: 'OceanBase',
    },
    {
      value: 23,
      label: 'InfluxDB',
    },
    {
      value: 24,
      label: 'Spark',
    },
    {
      value: 25,
      label: 'Doris',
    },
    {
      value: 26,
      label: 'TDengine',
    },
    {
      value: 27,
      label: 'Elasticsearch',
    },
    {
      value: 28,
      label: 'ClickHouse',
    },
    {
      value: 29,
      label: 'Kingbase',
    },
    {
      value: 30,
      label: 'HBase',
    },
  ]
  // 以 `label` 升序排序
  return orderBy(result, ['label'], ['asc'])
}

export const columns = [
  {
    title: '数据库类型',
    dataIndex: 'dbType',
    key: 'dbType',
  },
  {
    title: '名称',
    dataIndex: 'dbDesc',
    key: 'dbDesc',
  },
  {
    title: '连接地址',
    dataIndex: 'dbUrl',
    key: 'dbUrl',
    width: 350,
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
  },
  {
    title: '创建人',
    dataIndex: 'authorizationList',
    key: 'authorizationList',
  },
  {
    title: '操作',
    dataIndex: 'operation',
    key: 'operation',
    fixed: 'right',
    width: 250,
  },
]

/**
 * 时间类-->创建时间，格式化时间
 * @param date
 * @param format
 */
export function formatTime(date: Date, format = 'yyyy-MM-dd HH:mm:ss') {
  const o: any = {
    'M+': date.getMonth() + 1,
    'd+': date.getDate(),
    'H+': date.getHours(),
    'm+': date.getMinutes(),
    's+': date.getSeconds(),
    'q+': Math.floor((date.getMonth() + 3) / 3),
    S: date.getMilliseconds(),
  }

  if (/(y+)/.test(format)) {
    format = format.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length))
  }
  for (const k in o) {
    if (new RegExp(`(${k})`).test(format)) {
      format = format.replace(
        RegExp.$1,
        RegExp.$1.length === 1 ? o[k] : ('00' + o[k]).substr(('' + o[k]).length),
      )
    }
  }
  return format
}
