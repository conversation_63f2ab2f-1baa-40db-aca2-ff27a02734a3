<template>
  <div class="spark-monitor">
    <div class="page-head" v-if="useInfo">
      <img alt="" src="@/assets/monitor/icon_11.png" />
      <div class="mi-info-box">
        <p>
          SPARK内存使用率：<strong>已用{{ useInfo.used }}</strong
          ><strong style="margin-left: 15px">总计{{ useInfo.total }}</strong>
        </p>
        <a-progress
          :percent="useInfo.percent * 100"
          strokeColor="#1791FF"
          :format="(percent: number) => `${percent.toFixed(2)}%`"
        />
      </div>
      <a-button type="primary" class="refresh-btn" @click="fetchSparkMemoryUse">刷新</a-button>
    </div>
    <div class="page-wrap">
      <a-tabs
        v-model="activeKey"
        destroyInactiveTabPane
        :default-active-key="TabType.Tab1"
        style="height: 100%"
      >
        <a-tab-pane :key="TabType.Tab1" tab="字典表进度">
          <RunTable ref="tab1" />
        </a-tab-pane>
        <a-tab-pane :key="TabType.Tab2" tab="执行器">
          <ExecutorsTable ref="tab2" />
        </a-tab-pane>
        <a-tab-pane :key="TabType.Tab3" tab="RDD">
          <RddTable ref="tab3" />
        </a-tab-pane>
        <a-tab-pane :key="TabType.Tab4" tab="SQL执行状态">
          <QueryMemorySqlTable ref="tab4" />
        </a-tab-pane>
      </a-tabs>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import RddTable from './rdd-table.vue'
import ExecutorsTable from './executors-table.vue'
import RunTable from './running-dic-table.vue'
import QueryMemorySqlTable from './query-memory-sql-table.vue'
import { getSparkMemoryUse } from '@/api/services/monitor-maintenance/spark-monitor'
import { type Action } from './spark-monitor'

enum TabType {
  Tab1 = '1',
  Tab2 = '2',
  Tab3 = '3',
  Tab4 = '4',
}

const activeKey = ref(TabType.Tab1)
const useInfo = ref<{ used: string; total: string; percent: number }>()
const tab1 = ref<Action>()
const tab2 = ref<Action>()
const tab3 = ref<Action>()
const tab4 = ref<Action>()

function getList(key: TabType) {
  switch (key) {
    case TabType.Tab1:
      tab1.value?.getList()
      break
    case TabType.Tab2:
      tab2.value?.getList()
      break
    case TabType.Tab3:
      tab3.value?.getList()
      break
    case TabType.Tab4:
      tab4.value?.getList()
      break
  }
}

//  获取spark内存使用率
async function fetchSparkMemoryUse() {
  const resp = await getSparkMemoryUse({})
  if (resp.data.obj) {
    useInfo.value = resp.data.obj
  }
  getList(activeKey.value)
}

onMounted(fetchSparkMemoryUse)
</script>
<style lang="less" scoped>
.page-head {
  width: 100%;
  height: fit-content;
  border-radius: 12px;
  background-color: #fff;
  margin-bottom: 24px;
  display: flex;
  position: relative;

  img {
    width: 68px;
    height: 68px;
    margin-right: 15px;
  }

  .mi-info-box {
    display: flex;
    flex-direction: column;
    justify-content: center;
    width: 30%;
  }

  p {
    line-height: 22px;
    margin: -16px 0 8px;
  }

  .refresh-btn {
    position: absolute;
    right: 20px;
    top: 50%;
    margin-top: -16px;
  }
}

.page-wrap {
  height: 100%;
  width: 100%;
  border-radius: 12px;
  background-color: #fff;
}
.spark-monitor {
  padding: 24px;
}
:deep(.ant-tabs .ant-tabs-bar) {
  margin: 0 0 24px 0;
}
</style>
