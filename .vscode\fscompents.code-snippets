{
  "vuetemplate": {
    "prefix": "vuetemplate",
    "description": "Vue3 页面模板代码片段",
    "body": [
      "<template>",
      "  <div>",
      "    <h1>Hello, Vue3!</h1>",
      "    <p>Welcome to your Vue3 project.</p>",
      "  </div>",
      "</template>",
      "",
      "<script setup lang=\"ts\">",
      "import { ref } from 'vue'",
      "",
      "const message = ref('Hello, Vue3!')",
      "</script>",
      "",
      "<style scoped>",
      "h1 {",
      "  color: blue;",
      "}",
      "</style>"
    ]
  },
	"fsc-table": {
    "prefix": "fsc-table",
    "description": "<Table />组件模板",
    "body": [
      "<Table",
      "  :columns=\"${1:columns}\"",
      "  :getData=\"${2:getData}\"",
      "  ref=\"${3:tableRef}\"",
      "  :searchFormState=\"${4:form}\"",
      ">",
      "  <template #operate>",
      "    <a-button type=\"primary\" @click=\"${5:addTableHandel}\">新增</a-button>",
      "  </template>",
      "  <template #search>",
      "    <a-form-item label=\"页码\" name=\"page\">",
      "      <a-input v-model:value=\"${6:form.page}\" placeholder=\"请输入页码\" />",
      "    </a-form-item>",
      "  </template>",
      "  <template #bodyCell=\"data\">",
      "    <template v-if=\"data.column.dataIndex === 'action'\">",
      "      <a-button type=\"link\" @click=\"${7:editTableHandle(data.record)}\">编辑</a-button>",
      "      <a-popconfirm title=\"是否确认删除\" @confirm=\"${8:confirm(data.record)}\">",
      "        <a-button type=\"link\">删除</a-button>",
      "      </a-popconfirm>",
      "    </template>",
      "    <template v-else>",
      "      {{ data.value }}",
      "    </template>",
      "  </template>",
      "</Table>",
      "",
      "<a-modal",
      "  v-model:open=\"${9:modalValue}\"",
      "  @ok=\"${10:modalHandleOk}\"",
      "  :title=\"${11:modalType} === 'edit' ? '编辑' : '新增'\"",
      "  @cancel=\"${12:cancel}\"",
      "  width=\"${13:800px}\"",
      "  :confirm-loading=\"${14:confirmLoading}\"",
      ">",
      "  <a-form",
      "    ref=\"${15:formRef}\"",
      "    :model=\"${16:modalForm}\"",
      "    :rules=\"${17:rules}\"",
      "    :label-col=\"${18:labelCol}\"",
      "    :wrapper-col=\"${19:wrapperCol}\"",
      "  >",
      "  </a-form>",
      "</a-modal>"
    ]
  },
  "fsc-table-script": {
    "prefix": "fsc-table-script",
    "description": "<Table /> 组件脚本模板",
    "body": [
      "import { reactive, ref } from 'vue';",
      "import { Table } from '@fs/fs-components';",
      "import type { TableColumnType } from 'ant-design-vue';",
      "",
      "// 定义表格列配置",
      "const columns = ref<TableColumnType[]>([",
      "  { title: '名称', dataIndex: 'name', width: 200 },",
      "  { title: '年龄', dataIndex: 'age', width: 100 },",
      "  { title: '地址', dataIndex: 'address' },",
      "  { title: '操作', width: 150, slots: { customRender: 'operate' } }",
      "]);",
      "",
      "// 定义表格配置",
      "const tableConfig = reactive({ rowKey: 'id', bordered: true });",
      "",
      "// 定义搜索表单状态",
      "const searchForm = reactive({ name: '', age: undefined, address: '' });",
      "",
      "// 添加分页配置",
      "const pagination = reactive({",
      "  pageIndex: 1,",
      "  pageSize: 10,",
      "  total: 0,",
      "  showSizeChanger: true,",
      "  showQuickJumper: true,",
      "  showTotal: (total: number) => `总计 ${total} 条`,",
      "  onChange: (page: number, pageSize: number) => {",
      "    pagination.pageIndex = page;",
      "    pagination.pageSize = pageSize;",
      "    refreshData();",
      "  },",
      "  onShowSizeChange: (current: number, size: number) => {",
      "    pagination.pageIndex = 1;",
      "    pagination.pageSize = size;",
      "    refreshData();",
      "  }",
      "});",
      "",
      "// 获取表格数据的方法",
      "const fetchData = async (params: any) => {",
      "  try {",
      "    const queryParams = { ...params, pageIndex: pagination.pageIndex, pageSize: pagination.pageSize };",
      "    const response = await fetch('/api/data', { method: 'POST', body: JSON.stringify(queryParams) });",
      "    const data = await response.json();",
      "    pagination.total = data.total;",
      "    return { data: data.list, total: data.total };",
      "  } catch (error) {",
      "    console.error('获取数据失败：', error);",
      "  }",
      "};",
      "",
      "// 刷新数据的方法",
      "const refreshData = () => { fetchData(searchForm); };",
      "",
      "// 处理表单重置",
      "const handleReset = async () => {",
      "  try {",
      "    pagination.pageIndex = 1;",
      "    await refreshData();",
      "    console.log('表单已重置');",
      "  } catch (error) {",
      "    console.error('重置失败：', error);",
      "  }",
      "};",
      "",
      "// 处理表单提交",
      "const handleFinish = async (values: any) => {",
      "  try {",
      "    pagination.pageIndex = 1;",
      "    await fetchData(values);",
      "    console.log('表单提交的值：', values);",
      "  } catch (error) {",
      "    console.error('提交失败：', error);",
      "  }",
      "};",
      "",
      "// 处理新增操作",
      "const handleAdd = async () => {",
      "  try {",
      "    console.log('点击了新增按钮');",
      "  } catch (error) {",
      "    console.error('新增失败：', error);",
      "  }",
      "};",
      "",
      "// 处理编辑操作",
      "const handleEdit = async (record: any) => {",
      "  try {",
      "    console.log('编辑数据：', record);",
      "  } catch (error) {",
      "    console.error('编辑失败：', error);",
      "  }",
      "};",
      "",
      "// 处理删除操作",
      "const handleDelete = async (record: any) => {",
      "  try {",
      "    console.log('删除数据：', record);",
      "  } catch (error) {",
      "    console.error('删除失败：', error);",
      "  }",
      "};",
    ]
  }
}
