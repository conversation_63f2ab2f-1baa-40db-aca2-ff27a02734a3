<template>
  <div class="subscription-settings">
    <CardBox
      :title="'订阅配置管理'"
      :subTitle="'用于新增、编辑，管理配置状态，以及查询管理配置列表'"
    >
      <template v-slot:headerRight
        ><a-button type="primary" class="add-tag" @click="addItem"
          >+ 新增订阅配置</a-button
        ></template
      >
      <Table
        :columns="columns"
        :getData="getSubscripConfigListByPages"
        :autoRequest="false"
        ref="tableRef"
        :searchFormState="searchFormData"
      >
        <template #search>
          <a-form-item label="" name="配置名称" :rules="[{ message: '请输入配置名称' }]">
            <a-input v-model:value="searchFormData.configName" placeholder="请输入配置名称" />
          </a-form-item>
        </template>
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'pushType'">
            <span>{{ `${pushTypeObj[record.pushType] ?? '--'}` }}</span>
          </template>
          <template v-else-if="column.key === 'pushDate'">
            <span>{{ handelPushDate(record) }}</span>
          </template>
          <template v-if="column.key === 'operate'">
            <a-space class="action-container">
              <a @click.prevent="editItem(record)">编辑</a>
              <a-popconfirm
                title="是否确认删除"
                ok-text="是"
                cancel-text="否"
                @confirm="deleteItem(record)"
              >
                <a>删除</a>
              </a-popconfirm>
              <a-popconfirm
                :title="`是否确认${record.status === 1 ? '禁用' : '启用'}`"
                ok-text="是"
                cancel-text="否"
                @confirm="handleStatusChange(record)"
              >
                <a>{{ record.status === 1 ? '禁用' : '启用' }}</a>
              </a-popconfirm>
              <a @click.prevent="openHistoryRecords(record)">推送历史记录</a>
            </a-space>
          </template>
        </template>
      </Table>
    </CardBox>
    <a-modal
      v-model:open="modalVisible"
      :title="modalTtitle"
      @ok="confirmBtn"
      @cancel="canceBtn"
      destroyOnClose
    >
      <a-form :model="formState" :label-col="labelCol" ref="subscriptFormRef">
        <a-form-item
          label="配置名称"
          name="configName"
          :rules="[{ required: true, message: '请输入配置名称' }]"
        >
          <a-input v-model:value="formState.configName" placeholder="请输入配置名称" />
        </a-form-item>
        <a-form-item
          label="订阅指标名称"
          name="indexDataId"
          :rules="[{ required: true, message: '请选择订阅指标名称' }]"
        >
          <a-button type="link" @click.prevent="openIndexModal(formState)">{{
            formState.indexName
          }}</a-button>
        </a-form-item>
        <a-form-item
          label="订阅者数据表名称"
          name="serverId"
          :rules="[{ required: true, message: '请选择订阅者数据表名称' }]"
        >
          <a-select
            v-model:value="formState.serverId"
            show-search
            placeholder="请选择订阅者数据表ID"
            :options="serverIdListRef"
            :field-names="{ label: 'serverName', value: 'serverId' }"
            :filter-option="filterServeIdOption"
            allowClear
          ></a-select>
        </a-form-item>
        <a-form-item
          label="推送方式"
          name="pushType"
          :rules="[{ required: true, message: '请选择推送方式' }]"
        >
          <a-radio-group
            name="radioGroup"
            v-model:value="formState.pushType"
            @change="pushTypeChanged"
          >
            <a-radio value="1">每日</a-radio>
            <a-radio value="2">每周</a-radio>
            <a-radio value="3">每月</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item
          label="推送日期"
          v-if="`${formState.pushType}` !== '1'"
          name="pushDateArray"
          :rules="[{ required: true, message: '请选择推送方式' }]"
        >
          <a-select
            v-if="`${formState.pushType}` === '2'"
            v-model:value="formState.pushDateArray"
            :options="weekOptions"
            mode="tags"
            placeholder="请选择推送日期"
          />
          <a-select
            v-if="`${formState.pushType}` === '3'"
            v-model:value="formState.pushDateArray"
            :options="monthOptions"
            mode="tags"
            placeholder="请选择推送日期"
          />
        </a-form-item>
        <a-form-item label="推送时间">
          <a-time-picker v-model:value="formState.pushTime" valueFormat="HH:mm:ss" />
        </a-form-item>
        <a-form-item label="配置描述" name="description">
          <a-textarea
            v-model:value="formState.description"
            showCount
            placeholder="请输入配置描述"
          />
        </a-form-item>
      </a-form>
    </a-modal>
    <!-- 查询订阅推送记录modal -->
    <a-modal
      v-model:open="pushrecordModalVisible"
      width="1200px"
      :title="'查询订阅推送记录'"
      :footer="null"
      destroyOnClose
      :bodyStyle="modalBodyStyle"
    >
      <div class="pushrecord-modal-content">
        <Table
          :columns="pushrecordColumns"
          :getData="getPushrecordListByPages"
          :autoRequest="false"
          ref="pushrecordTableRef"
          :searchFormState="searchPushrecordList"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'pushType'">
              <span>{{ `${subscribePushType[record.pushType] ?? '--'}` }}</span>
            </template>
            <template v-else-if="column.key === 'status'">
              <span>{{ `${subscribeStatus[record.status] ?? '--'}` }}</span>
            </template>
            <template v-else-if="column.key === 'datas'">
              <span
                style="cursor: pointer; color: var(--new-primary-color)"
                @click.prevent="openPreCode(record)"
                >{{ record.datas }}</span
              >
            </template>
          </template>
        </Table>
      </div>
    </a-modal>
    <!-- 推送数据json modal -->
    <a-modal
      v-model:open="datasModalVisible"
      width="1000px"
      :title="'查看详情'"
      destroyOnClose
      class="datas-modal"
    >
      <VAceEditor
        v-model:value="highlightedCode"
        lang="json"
        theme="github"
        :options="editorOptions"
        style="height: 300px; width: 100%"
      />
      <!-- <pre class="a11y-light">
        <span class="hljs">
          <code class="json" v-html="highlightedCode" style="word-wrap: break-word;white-space: pre-wrap;"></code>
        </span>
      </pre> -->
      <template #footer>
        <a-button type="primary" @click="datasModalVisible = false">关闭</a-button>
      </template>
    </a-modal>
    <!-- 指标下拉 modal -->
    <a-modal
      v-model:open="indexModalVisible"
      width="1000px"
      :title="'指标下拉列表详情'"
      :footer="null"
      destroyOnClose
      class="index-modal"
      :bodyStyle="modalBodyStyle"
      @cancel="handleIndexModalClose"
    >
      <div class="index-modal-content">
        <Table
          :columns="indexDataColumns"
          :getData="getIndexDataListByPages"
          :autoRequest="false"
          ref="indexDataTableRef"
          :searchFormState="searchIndexData"
        >
          <template #search>
            <a-form-item label="指标名称" name="指标名称" :rules="[{ message: '请输入指标名称' }]">
              <a-input v-model:value="searchIndexData.indexName" placeholder="请输入指标名称" />
            </a-form-item>
          </template>
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'operate'">
              <a-space class="action-container">
                <a-popconfirm
                  :title="`订阅指标必须且最多选择一项，是否确认${record.indexId === formState.indexDataId ? '取消选中' : '选中'}该订阅指标`"
                  ok-text="是"
                  cancel-text="否"
                  @confirm="selectItem(record)"
                >
                  <a>{{ record.indexId === formState.indexDataId ? '取消选中' : '选中' }}</a>
                </a-popconfirm>
              </a-space>
            </template>
          </template>
        </Table>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import {
  addSubscriptSetItem,
  changeSubStatusApi,
  deleteSubscriberSetItem,
  editSubscriberSetItem,
  getIndexDataListByPages,
  fetchServerIdByName,
  getPushrecordListByPages,
  getSubscripConfigListByPages,
  type IPushrecordList,
  type IServerIdItem,
  type ISubscriptIteSet,
  type ISubStatusItem,
  type IIndexItem,
} from '@/api/services/data-asset/subscription-settings'
import type { AssetResponse } from '@/api/services/data-asset/tag-management'
import { ref, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import CardBox from '@/components/card-box/card-box.vue'
import { Table } from '@fs/fs-components'
import { VAceEditor } from 'vue3-ace-editor'
import 'ace-builds/src-noconflict/mode-json' // 导入文本模式
import 'ace-builds/src-noconflict/theme-github' // 导入github主题

import hljs from 'highlight.js/lib/core'
import json from 'highlight.js/lib/languages/json'
import 'highlight.js/styles/a11y-light.min.css'
hljs.registerLanguage('json', json)

type FlexibleType = {
  [key: string]: string
}

const initForm = () => {
  return {
    configName: '',
    indexDataId: '',
    serverId: null,
    pushType: '1',
    pushDate: '',
    pushDateArray: [], // 存在下拉框里需要的格式
    pushTime: '09:00:00',
    description: '',
    configId: '',
    indexName: '请选择订阅指标名称', // 仅展示名称用
  }
}

const modalVisible = ref<boolean>(false)
const modalTtitle = ref('')
const modeType = ref('add')
const editorOptions = ref({
  fontSize: '14px', // 设置字体大小
  showPrintMargin: false, // 是否显示打印边距
  readOnly: true, // 设置为只读
})

const subscriptFormRef = ref()
const formState = ref<ISubscriptIteSet>(initForm())

const labelCol = { style: { width: '160px', textAlign: 'left' } }
// 订阅者数据表List
const serverIdListRef = ref<IServerIdItem[]>([])
// 根据推送方式显示不同的options
const weekOptions = [
  {
    value: '1',
    label: '周一',
  },
  {
    value: '2',
    label: '周二',
  },
  {
    value: '3',
    label: '周三',
  },
  {
    value: '4',
    label: '周四',
  },
  {
    value: '5',
    label: '周五',
  },
  {
    value: '6',
    label: '周六',
  },
  {
    value: '7',
    label: '周天',
  },
]
const monthOptions = [
  {
    value: '1',
    label: '1号',
  },
  {
    value: '2',
    label: '2号',
  },
  {
    value: '3',
    label: '3号',
  },
  {
    value: '4',
    label: '4号',
  },
  {
    value: '5',
    label: '5号',
  },
  {
    value: '6',
    label: '6号',
  },
  {
    value: '7',
    label: '7号',
  },
  {
    value: '8',
    label: '8号',
  },
  {
    value: '9',
    label: '9号',
  },
  {
    value: '10',
    label: '10号',
  },
  {
    value: '11',
    label: '11号',
  },
  {
    value: '12',
    label: '12号',
  },
  {
    value: '13',
    label: '13号',
  },
  {
    value: '14',
    label: '14号',
  },
  {
    value: '15',
    label: '15号',
  },
  {
    value: '16',
    label: '16号',
  },
  {
    value: '17',
    label: '17号',
  },
  {
    value: '18',
    label: '18号',
  },
  {
    value: '19',
    label: '19号',
  },
  {
    value: '20',
    label: '20号',
  },
  {
    value: '21',
    label: '21号',
  },
  {
    value: '22',
    label: '22号',
  },
  {
    value: '23',
    label: '23号',
  },
  {
    value: '24',
    label: '24号',
  },
  {
    value: '25',
    label: '25号',
  },
  {
    value: '26',
    label: '26号',
  },
  {
    value: '27',
    label: '27号',
  },
  {
    value: '28',
    label: '28号',
  },
]

const pushTypeObj: FlexibleType = {
  '1': '每日',
  '2': '每周',
  '3': '每月',
}

const subscribePushType: FlexibleType = {
  1: '自动',
  2: '手动',
}

const subscribeStatus: FlexibleType = {
  0: '执行中',
  1: '成功',
  2: '失败',
}

const columns = ref<any[]>([
  {
    title: '配置名称',
    dataIndex: 'configName',
    key: 'configName',
  },
  {
    title: '推送方式',
    dataIndex: 'pushType',
    key: 'pushType',
  },
  {
    title: '推送日期',
    dataIndex: 'pushDate',
    key: 'pushDate',
  },
  {
    title: '推送时间',
    dataIndex: 'pushTime',
    key: 'pushTime',
  },
  {
    title: '配置描述',
    dataIndex: 'description',
    key: 'description',
  },
  {
    title: '操作',
    dataIndex: 'operate',
    key: 'operate',
    width: 260,
  },
])

const tableRef = ref<any>(null)
const searchFormData = ref<any>({
  configName: '',
})

const pushrecordModalVisible = ref<boolean>(false)
const pushrecordColumns = ref<any[]>([
  {
    title: '自增主键',
    dataIndex: 'autoId',
    key: 'autoId',
  },
  {
    title: '订阅配置主键',
    dataIndex: 'configId',
    key: 'configId',
  },
  {
    title: '开始时间',
    dataIndex: 'startTime',
    key: 'startTime',
  },
  {
    title: '完成时间',
    dataIndex: 'finishTime',
    key: 'finishTime',
  },
  {
    title: '推送任务状态', // 0执行中1成功2失败
    dataIndex: 'status',
    key: 'status',
  },
  {
    title: '推送方式', // 1自动2手动
    dataIndex: 'pushType',
    key: 'pushType',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
  },
  {
    title: '更新时间',
    dataIndex: 'updateTime',
    key: 'updateTime',
  },
  {
    title: '推送数据json',
    dataIndex: 'datas',
    key: 'datas',
  },
])

const pushrecordTableRef = ref<any>(null)
const searchPushrecordList = ref<any>({
  configId: '',
})

// 推送数据json
const highlightedCode = ref<any>(null)
const datasModalVisible = ref<boolean>(false)

// 指标下拉Modal
const indexDataTableRef = ref<any>(null)
const indexModalVisible = ref<boolean>(false)
const indexDataColumns = ref<any[]>([
  {
    title: '指标名称',
    dataIndex: 'indexName',
    key: 'indexName',
  },
  {
    title: '指标ID',
    dataIndex: 'indexId',
    key: 'indexId',
  },
  {
    title: '操作',
    dataIndex: 'operate',
    key: 'operate',
    width: 100,
  },
])
const searchIndexData = ref<any>({
  indexName: '',
})
const modalBodyStyle = {
  maxHeight: 'calc(100vh - 200px)', // 留出顶部/底部空间
  overflowY: 'auto',
  padding: '16px 24px',
}
// 新增订阅配置
const addItem = () => {
  modeType.value = 'add'
  modalTtitle.value = '新增订阅配置'
  modalVisible.value = true
}
// 编辑订阅配置
const editItem = (item: ISubscriptIteSet) => {
  // console.log('编辑订阅配置:', item)
  formState.value = {
    ...item,
    pushType: `${item.pushType}`,
    pushDateArray: `${item.pushDate}`?.split(','),
  }

  modeType.value = 'edit'
  modalTtitle.value = '编辑订阅配置'
  modalVisible.value = true
}

// 删除数据订阅服务配置
const deleteItem = async (item: ISubscriptIteSet) => {
  try {
    await deleteSubscriberSetItem(`${item.configId}`)
    message.success('删除成功')
    fetchTableData()
  } catch (error) {
    console.log(error)
  }
}
// 禁用/启用订阅配置 item.status === 1 当前是启用状态 需要禁用
const handleStatusChange = async (item: ISubscriptIteSet) => {
  // console.log('删除订阅配置:', item)
  const params: ISubStatusItem = {
    configId: `${item.configId}`,
    changeType: item.status === 1 ? 2 : 1,
  }
  try {
    await changeSubStatusApi(params)
    message.success('操作成功')
    fetchTableData()
  } catch (error) {
    console.log(error)
  }
}
// 查询订阅推送记录
const openHistoryRecords = (item: ISubscriptIteSet) => {
  pushrecordModalVisible.value = true
  searchPushrecordList.value.configId = item.configId
  if (item.configId?.length === 0) return
  fetchPushrecordTableData()
  // console.log('查询订阅推送记录:', item, searchPushrecordList.value.configId)
}
// 查询订阅推送记录里的推送数据json
const openPreCode = (item: IPushrecordList) => {
  datasModalVisible.value = true

  try {
    // 尝试解析并格式化JSON数据
    const parsedData = JSON.parse(item.datas)
    highlightedCode.value = JSON.stringify(parsedData, null, 2)
  } catch (error) {
    // 如果解析失败，则使用原始数据
    console.error('JSON解析失败:', error)
    highlightedCode.value = item.datas
  }
}
// 确认按钮
const confirmBtn = () => {
  subscriptFormRef.value.validate().then(async () => {
    const params = {
      ...formState.value,
      pushDate: formState.value.pushDateArray?.join(','),
    }
    // console.log('formState:', formState)
    let res: AssetResponse<string>
    if (modeType.value === 'add') {
      res = await addSubscriptSetItem(params)
    } else {
      res = await editSubscriberSetItem(params)
    }
    if (res.code === '000000') {
      message.success('操作成功')
      fetchTableData()
      canceBtn()
    } else {
      message.error(res.msg)
    }
  })
}

// 取消按钮
const canceBtn = () => {
  modalVisible.value = false
  formState.value = initForm()
}

const filterServeIdOption = (input: string, option: any) => {
  return option.serverName.toLowerCase().indexOf(input.toLowerCase()) >= 0
}

// 处理推送日期
const handelPushDate = (item: ISubscriptIteSet) => {
  let dateStr = ''
  const dateArr = item.pushDate?.split(',')
  if (`${item.pushType}` === '2') {
    dateStr = weekOptions
      .filter((w) => dateArr?.includes(w.value))
      ?.map((w) => w.label)
      ?.join(',')
  }
  if (`${item.pushType}` === '3') {
    dateStr = monthOptions
      .filter((m) => dateArr?.includes(m.value))
      ?.map((m) => m.label)
      ?.join(',')
  }
  return dateStr.length === 0 ? '--' : dateStr
}
// 推送方式选项变化时
const pushTypeChanged = (_e: Event) => {
  formState.value.pushDateArray = []
}
// 打开指标下拉Modal
const openIndexModal = (form: ISubscriptIteSet) => {
  indexModalVisible.value = true
  fetchIndexDatadByName()
  // console.log('form:',form)
}
// 选中当前指标名称
const selectItem = (item: IIndexItem) => {
  if (formState.value.indexDataId === item.indexId) {
    // 说明是取消
    formState.value.indexDataId = ''
    formState.value.indexName = '请选择订阅指标名称'
  } else {
    formState.value.indexDataId = item.indexId
    formState.value.indexName = item.indexName
  }
}
// 关闭指标下拉列表modal时，重置查询条件
const handleIndexModalClose = () => {
  searchIndexData.value.indexName = ''
}
// 订阅配置管理列表
const fetchTableData = async () => {
  try {
    setTimeout(() => {
      tableRef.value?.getTableData()
    }, 20)
  } catch (error) {
    console.error('获取数据表列表失败:', error)
  }
}

// 数据订阅服务列表
const getServerIdByName = async (serverName = '') => {
  const res: AssetResponse<IServerIdItem[]> = await fetchServerIdByName(serverName)
  if (res.code !== '000000') return
  serverIdListRef.value = [...res.data]
}

// 指标下拉列表
const fetchIndexDatadByName = async () => {
  try {
    setTimeout(() => {
      indexDataTableRef.value?.getTableData()
    }, 20)
  } catch (error) {
    console.error('获取指标下拉列表失败:', error)
  }
}

// 订阅推送记录列表
const fetchPushrecordTableData = async () => {
  try {
    setTimeout(() => {
      pushrecordTableRef.value?.getTableData()
    }, 20)
  } catch (error) {
    console.error('获取订阅推送记录列表失败:', error)
  }
}

onMounted(async () => {
  await fetchTableData()
  getServerIdByName()
})
</script>

<style src="./style.less" lang="less" scoped></style>
