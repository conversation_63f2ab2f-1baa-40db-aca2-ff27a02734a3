<template>
  <div ref="buttonRef" class="floating-button" @mousedown="startDrag">
    <a-dropdown :trigger="['click']">
      <a class="ant-dropdown-link" @click.prevent> 快捷 </a>
      <template #overlay>
        <a-menu @click="goPage">
          <a-menu-item key="/manage-api">
            <a href="javascript:;">API项目管理</a>
          </a-menu-item>
          <a-menu-item key="/port-manage">
            <a href="javascript:;">API接口管理</a>
          </a-menu-item>
          <a-menu-item key="/SystemManagement">
            <a href="javascript:;">路由代码生成</a>
          </a-menu-item>
          <a-menu-item key="/RoleManagement">
            <a href="javascript:;">角色菜单授权</a>
          </a-menu-item>
          <a-menu-item key="/refresh">
            <a href="javascript:;">刷新权限菜单</a>
          </a-menu-item>
          <a-menu-item key="/edit">
            <a href="javascript:;">编辑页面代码</a>
          </a-menu-item>
          <a-menu-item key="/layout">
            <a href="javascript:;">生成布局代码</a>
          </a-menu-item>
          <a-menu-item key="/chat">
            <a href="javascript:;">AI快速生成</a>
          </a-menu-item>
        </a-menu>
      </template>
    </a-dropdown>
  </div>

  <a-modal v-model:open="open" title="提示" @ok="handleOk" @cancel="cancelHandle">
    <div class="tip-box">
      <div v-if="isHasType">
        检测到上次生成的页面为
        <span class="type"> {{ getTypeName(type) }} </span
        >，是否需要生成，如需生成其它类型页面，请切换类型（切换类型会清空上次缓存）
      </div>
      <template v-else> 未检测到上次生成的页面类型，请选择需要生成的页面类型 </template>
    </div>
    <div class="type-box">
      <span class="title">页面类型：</span>
      <a-select ref="select" v-model:value="type" style="width: 100%">
        <a-select-option value="table">table</a-select-option>
        <a-select-option value="form">form</a-select-option>
        <a-select-option value="echarts">echarts</a-select-option>
        <a-select-option value="grid">grid</a-select-option>
        <a-select-option value="custom">自定义模板</a-select-option>
      </a-select>
    </div>
    <div class="type-box" v-if="type === 'custom'">
      <span class="title">模板：</span>
      <a-select v-model:value="customType" style="width: 100%">
        <a-select-option :value="item.value" v-for="item in customList" :key="item.value">{{
          item.label
        }}</a-select-option>
      </a-select>
    </div>
  </a-modal>

  <layoutModal v-model="layoutOpen" v-if="layoutOpen"></layoutModal>

  <!-- <chatDialog v-model="chatOpen"></chatDialog> -->
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
const router = useRouter()
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { debounce } from '@/utils'
import { getdraft } from '@/components/code-generation/code-generation'
import { useRoute } from 'vue-router'
import { useAppStore } from '@/stores/app'
import ResizeObserver from 'resize-observer-polyfill'
import layoutModal from '../layout-modal/layout-modal.vue'
import chatDialog from '../chat-dialog/chat-dialog.vue'

const userStore = useUserStore()

const fastPosition = ref({
  right: 40,
  bottom: 40,
  left: null,
  top: null,
})
const type = ref('table')
const customType = ref('table')
const open = ref(false)
const route = useRoute()
const appStore = useAppStore()
const isHasType = ref(false)
const newWidth = ref(0)
const newHeight = ref(0)
const layoutOpen = ref(false)
const chatOpen = ref(false)
const customList = ref([
  {
    label: '表格',
    value: 'table',
  },
  {
    label: '表单',
    value: 'form',
  },
])
const handleOk = () => {
  isHasType.value = false
  open.value = false
  appStore.setGenerationType({ type: type.value, customType: customType.value, state: true })
}

const getTypeName = (type: string) => {
  if (type === 'table') {
    return '表格'
  } else if (type === 'form') {
    return '表单'
  } else if (type === 'echarts') {
    return '图表'
  } else if (type === 'grid') {
    return '布局'
  } else if (type === 'custom') {
    return '自定义模板'
  }
  return '未知'
}

const cancelHandle = () => {
  isHasType.value = false
  open.value = false
}

async function goPage(data: any) {
  if (data.key === '/chat') {
    chatOpen.value = true
    return
  }
  if (data.key === '/layout') {
    layoutOpen.value = true
    return
  }
  if (data.key === '/edit') {
    let routePath = ''
    if (route.path) {
      routePath = route.path
    }
    if (!routePath) return
    const { data } = await getdraft({ name: routePath })
    open.value = true
    const { pageType = '', type: custom = 'table' } = data?.data ?? {}
    if (pageType) {
      type.value = pageType
      isHasType.value = true
      customType.value = custom
    }
    return
  }
  if (data.key === '/refresh') {
    // 动态引入文件 防止引入pinia 报错
    const { getUserMenuFn } = await import('@/utils/user')
    await getUserMenuFn(false)
    return
  }
  const url = data.key
  router.push(url)
  const levelIds = findLevelIdsByMenuUrl(userStore.userMenu, url)
  if (levelIds) userStore.currentMenuKeyPath = levelIds.split('.')
}

// 获取菜单ids 匹配左侧菜单
function findLevelIdsByMenuUrl(data: any, targetUrl: string) {
  for (let item of data) {
    if ('/' + item.menuUrl === targetUrl) {
      return item.levelIds
    }
    if (item.childrens && item.childrens.length > 0) {
      let result: any = findLevelIdsByMenuUrl(item.childrens, targetUrl)
      if (result) {
        return result
      }
    }
  }
  return null
}

// 按钮的 DOM 引用
const buttonRef = ref<HTMLElement | null>(null)
let isDragging = false
let offsetX = 0
let offsetY = 0

const startDrag = (e: MouseEvent) => {
  if (buttonRef.value) {
    isDragging = true
    offsetX = e.clientX - buttonRef.value.offsetLeft
    offsetY = e.clientY - buttonRef.value.offsetTop
    document.addEventListener('mousemove', drag)
    document.addEventListener('mouseup', stopDrag)
  }
}

const drag = (e: MouseEvent) => {
  if (isDragging && buttonRef.value) {
    const left = e.clientX - offsetX
    const top = e.clientY - offsetY

    // 假设按钮所在容器的宽度和高度已知
    const containerWidth = document.body.clientWidth // 这里可根据实际容器替换
    const containerHeight = document.body.clientHeight // 这里可根据实际容器替换
    // 计算right值
    const right = containerWidth - left - buttonRef.value.offsetWidth
    buttonRef.value.style.right = `${right}px`

    // 计算bottom值
    const bottom = containerHeight - top - buttonRef.value.offsetHeight
    buttonRef.value.style.bottom = `${bottom}px`
    debouncedHandleInput(right, bottom)
  }
}

const debouncedHandleInput = debounce(dragHandle, 100)

function dragHandle(right: number, bottom: number) {
  const { left, top } = fastPosition.value
  localStorage.setItem('fastPosition', JSON.stringify({ left, right, top, bottom }))
}

const stopDrag = () => {
  isDragging = false
  document.removeEventListener('mousemove', drag)
  document.removeEventListener('mouseup', stopDrag)
}

// 在组件挂载时，设置按钮初始位置（这里可以根据需求调整）
onMounted(() => {
  setPosition()
})

const setPosition = () => {
  if (buttonRef.value) {
    fastPosition.value = Object.assign(
      fastPosition.value,
      JSON.parse(localStorage.getItem('fastPosition') || '{}'),
    )
    let { right, bottom } = fastPosition.value

    if (right + 50 > newWidth.value) {
      right = newWidth.value - 50
    }

    if (bottom + 50 > newHeight.value) {
      bottom = newHeight.value - 50
    }

    buttonRef.value.style.position = 'fixed'
    buttonRef.value.style.right = right + 'px'
    buttonRef.value.style.bottom = bottom + 'px'
  }
}

// 在组件卸载时，移除可能存在的事件监听器
onUnmounted(() => {
  document.removeEventListener('mousemove', drag)
  document.removeEventListener('mouseup', stopDrag)
  observer.observe(document.body)
})

const observer = new ResizeObserver(() => {
  newWidth.value = window.innerWidth
  newHeight.value = window.innerHeight
  setPosition()
})

onMounted(() => {
  observer.observe(document.body)
})
</script>
<style scoped lang="less">
.floating-button {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: white;
  color: white;
  text-align: center;
  line-height: 50px;
  cursor: move;
  user-select: none;
  z-index: 1000;
  box-shadow:
    0 6px 16px 0 rgba(0, 0, 0, 0.08),
    0 3px 6px -4px rgba(0, 0, 0, 0.12),
    0 9px 28px 8px rgba(0, 0, 0, 0.05);
}
.type-box {
  margin-top: 15px;
  display: flex;
  align-items: center;
  .title {
    display: inline-block;
    width: 100px;
  }
}
.tip-box {
  .type {
    color: #1677ff;
  }
}
</style>
