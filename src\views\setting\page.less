.nav-page-title {
    display: flex;
    align-content: center;
    justify-content: space-between;
    width: 100%;
    background: #FFFFFF;
    // box-shadow: @shadow-1;
    // border-radius: 12px;
  
  
    .title-left {
      > p {
        font-size: 20px;
        font-weight: bold;
        color: #141414;
        word-break: break-all;
        margin-bottom: 0;
        margin-top: 0;
      }
      .desc {
        font-size: 14px;
        color: #999;
        margin-top: 5px;
        word-break: break-all;
      }
    }
  
    .go-back {
      font-size: 14px;
      margin-right: 36px;
      cursor: pointer;
      display: flex;
      align-items: center;
  
      i {
        margin-right: 8px;
      }
    }
  
    .list-top-left {
      flex: 1;
      margin: 0 10px;
  
      .ant-btn-group {
        button {
          width: 58px;
        }
      }
  
      .active-list {
        color: #1890FF;
      }
    }
  }
  