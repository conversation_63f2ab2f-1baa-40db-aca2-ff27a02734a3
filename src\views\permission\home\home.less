.home-page {
  .home-top {
    background-color: #fff;
    border: 1px solid rgba(0, 0, 0, 0.10196078431372549);
    border-radius: 12px;
    padding: 22px 0 22px 40px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    box-sizing: border-box;
    .avatar {
      width: 56px;
      height: 56px;
    }
    .home-welcome {
      flex: 1;
      margin: 0 25px;
      p {
        font-size: 18px;
        font-weight: 600;
        color: rgba(0, 0, 0, 0.85);
        margin-bottom: 8px;
        margin-top: 0;
        font-family:
          PingFangSC-Medium,
          PingFang SC;
      }
      span {
        font-size: 14px;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.65);
      }
    }
    .task-status {
      display: flex;
      justify-content: center;
      align-items: center;
      .status-div {
        padding: 0 40px;
        position: relative;
        text-align: center;
        p {
          height: 22px;
          font-size: 16px;
          font-weight: 500;
          color: rgba(0, 0, 0, 0.45);
          line-height: 22px;
          margin-bottom: 2px;
          margin-top: 0;
          font-family:
            PingFangSC-Medium,
            PingFang SC;
        }
        span {
          font-size: 24px;
          font-family: DINAlternate-Bold, DINAlternate;
          font-weight: 700;
          color: #252525;
          line-height: 28px;
        }
      }
    }
  }
}

.home-content {
  margin-top: 24px;

  .dashboard-container {
    display: flex;
    gap: 24px;

    .left-column {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 24px;
    }

    .right-column {
      flex: 1;
    }

    .section {
      background: #fff;
      border-radius: 12px;

      .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        padding-right: 20px;

        h3 {
          font-size: 21px;
          font-weight: 700;
          color: #000;
          margin: 0;
          font-family:
            PingFangSC-Medium,
            PingFang SC;
        }

        .refresh-icon {
          width: 20px;
          height: 20px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;

          :deep(.anticon) {
            font-size: 12px;
            color: #666;
          }
        }
      }

      .cards-container {
        display: flex;
        gap: 16px;

        .card {
          flex: 1;
          background: #fff;
          border: 1px solid rgba(0, 0, 0, 0.08);
          border-radius: 8px;
          padding: 16px;
          position: relative;

          .card-content {
            position: relative;

            h4 {
              font-size: 14px;
              color: rgba(0, 0, 0, 0.85);
              font-family:
                PingFangSC-Medium,
                PingFang SC;
              margin-bottom: 4px;
              margin-top: 0;
              font-weight: 600;
              color: #8c8c8c;
            }

            .number {
              font-weight: 700;
              color: #252525;
              font-family: DINAlternate-Bold, DINAlternate;
              margin-bottom: 8px;
              font-size: 30px;
              line-height: 30px;
            }

            .status-row {
              display: flex;
              align-items: center;
              gap: 12px;
              margin-bottom: 8px;

              .number {
                font-size: 32px;
                font-weight: 700;
                color: #252525;
                font-family: DINAlternate-Bold, DINAlternate;
                margin-bottom: 0;
              }

              .status-badge {
                padding: 4px 12px;
                border-radius: 12px;
                font-size: 12px;
                font-weight: 500;

                // &.running {
                //   background: #f6ffed;
                //   color: #52c41a;
                //   border: 1px solid #b7eb8f;
                // }
              }

              .actions {
                display: flex;
                gap: 8px;

                .ant-btn-link {
                  padding: 0 7px;
                }
              }
            }

            .icon {
              position: absolute;
              bottom: 0;
              right: 0;
              width: 48px;
              height: 48px;
              background: #1890ff;
              border-radius: 6px;
              display: flex;
              align-items: center;
              justify-content: center;

              :deep(.anticon) {
                color: #fff;
                font-size: 16px;
              }
            }
          }
        }
      }

      .table-container {
        .instances-table {
          :deep(.ant-table) {
            background: transparent;

            .ant-table-thead > tr > th {
              background: transparent;
              font-size: 14px;
              font-weight: 500;
              color: rgba(0, 0, 0, 0.85);
              font-family:
                PingFangSC-Medium,
                PingFang SC;
              padding: 12px 16px;
            }

            .ant-table-tbody > tr > td {
              padding: 16px;
              font-size: 14px;
              color: rgba(0, 0, 0, 0.85);
              border-width: 0;
            }

            .ant-table-tbody > tr:hover > td {
              background: transparent;
            }
          }

          .status-cell {
            display: flex;
            align-items: center;
            gap: 8px;

            .status-icon {
              width: 20px;
              height: 20px;
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;
              img {
                width: 100%;
                height: 100%;
              }

              :deep(.anticon) {
                font-size: 12px;
              }
            }
          }

          .progress-container {
            display: flex;
            align-items: center;
            gap: 8px;

            span {
              min-width: 30px;
              font-size: 12px;
              color: rgba(0, 0, 0, 0.65);
            }

            .progress-bar {
              flex: 1;
              height: 4px;
              background: rgba(0, 0, 0, 0.06);
              border-radius: 2px;
              overflow: hidden;

              .progress-fill {
                height: 100%;
                background: #1890ff;
                border-radius: 2px;
                transition: width 0.3s ease;
              }
            }
          }
        }
      }
    }
  }
}
.home-bottom {
  margin-top: 30px;
  .section-container {
    position: relative;
    width: 100%;
    background: #fff;
    padding: 0 20px 24px;
    margin-bottom: 16px;
    color: rgba(0, 0, 0, 0.65);
    .section-icon {
      position: absolute;
      right: 10px;
      top: 10px;
    }
    .module-header-btn-box {
      display: flex;
      justify-content: space-between;
      margin-top: 35px;
      .mhd-btn {
        padding: 8px 25px 10px 22px;
        height: 41px;
        border-radius: 6px;
        border: 1px solid rgba(135, 139, 146, 0.15);
        background: #f1f2f4;
        cursor: pointer;
        transition: all 0.25s;
      }
      .mhd-btn-check {
        border: 1px solid #1890ff;
        background: #1890ff;
        color: #fff;
      }
    }
    .module-chart-box {
      width: 100%;
      height: 254px;
      background: linear-gradient(270deg, #551aff 0%, #1d4dff 36%, #5adeff 100%);
      border-radius: 12px;
      color: #fff;
      margin-top: 26px;
      display: flex;
      .mc-left-box {
        width: 60%;
        position: relative;
        display: flex;
        justify-content: space-around;
        padding: 35px 0;
        .line-item {
          flex: 1;
          display: flex;
          flex-direction: column;
          text-align: center;
          color: #fff;
          justify-content: flex-end;
          .progress-box {
            width: 20px;
            margin: 8px auto;
            .progress-color-box {
              width: 100%;
              background-color: #fff;
              transition: all 0.5s;
            }
          }
          .line-text {
            margin-top: 8px;
          }
        }
      }
      .mc-right-box {
        width: 40%;

        div {
          width: 150px;
          height: 180px;
          margin: 25px auto;
        }
      }
      .mc-left-box::before {
        content: '';
        position: absolute;
        right: 0;
        top: 50%;
        margin-top: -114px;
        width: 1px;
        height: 228px;
        background-color: #fff;
        opacity: 0.65;
        background: linear-gradient(244deg, hsla(0, 0%, 100%, 0), #fff 50%, hsla(0, 0%, 100%, 0));
      }
    }
  }
  .run-list-content {
    overflow-y: scroll;
    padding: 0;
  }

  .run-list-content::-webkit-scrollbar-track {
    border-radius: 10px;
    background-color: rgba(255, 255, 255, 1); //设置背景透明
  }
  .section-top {
    display: flex;
    align-content: center;
    -ms-flex-pack: justify;
    justify-content: space-between;
    align-items: center;
  }
  .task-to {
    font-size: 16px;
    font-weight: 400;
    color: #999;
    line-height: 20px;
    // padding-top: 10px;
    position: relative;
    padding-left: 40px;
    height: 50px;
    line-height: 50px;
    border-radius: 12px;
    // background-color: #fff;
    margin-bottom: 5px;
    cursor: pointer;
    transition: all 0.5s;
    margin: 5px 20px;
  }

  .task-to:hover {
    box-shadow: 0px 10px 30px 8px #f4f4f4;
  }

  .task-to::before {
    content: '';
    position: absolute;
    left: 16px;
    width: 14px;
    height: 14px;
    border-radius: 50%;
    background-color: #1890ff;
    top: 50%;
    margin-top: -7px;
  }
}
.module-data-box {
  margin-top: 20px;
  display: flex;
  justify-content: space-around;
  // border: 1px solid #0000001a;
  padding-top: 16px;
  border-radius: 10px;

  .md-number-item {
    flex: 1;
    text-align: center;
    p{
      margin: 0;
    }
    p:nth-child(1) {
      line-height: 22px;
      color: rgba(0, 0, 0, 0.65);
      font-size: 16px;
    }

    p:nth-child(2) {
      font-size: 36px;
      font-family: JCHEadA;
      color: rgba(0, 0, 0, 0.85);
      line-height: 50px;
      letter-spacing: 1px;
      font-weight: 600;
    }
  }
}
