<script setup lang="ts">
import SqlTreeComp from '@/components/sql-tree-comp/index.vue'
import type { SqlDataNode } from '@/components/sql-tree-comp/types'
import { useRequest } from 'vue-hooks-plus'
import api from '@/api/sql-edit-sys'
import { logPlus } from '@/utils/log-plus'
import { NodeType } from '@/api/models/sql-tree'
import { Tabs, TabPane, Dropdown, Menu, MenuItem, message } from 'ant-design-vue'
import { CopyOutlined, TableOutlined, EllipsisOutlined, EditOutlined } from '@ant-design/icons-vue'
import type { Key } from 'ant-design-vue/es/vc-table/interface'
import { copyText } from '@/utils/utils'
import Splitter from '@/components/splitter/index.vue'
import DBTablesView from './db-tables-view/index.vue'
import { activeDbKey } from './use-sql-editing'
import { getDbIcon } from '@/utils/utils'
import { h } from 'vue'
import DBDesigner from './db-designer/index.vue'
import CopyTableModal from './components/copy-table-modal.vue'
import { useEventBus } from '@vueuse/core'

const menuKey = {
  refresh: 'refresh',
  viewData: 'viewData',
  copyTable: 'copyTable',
  generateSql: 'generateSql',
}
defineOptions({
  name: 'SqlEditing',
})
const copyTableModalRef = ref<any>(null)
const isEditingName = ref(false)
const editingName = ref('')
/** 当前选中的树节点 */
const activeNode = ref<SqlDataNode>()
const isShowEmpty = computed(() => {
  const activeDb = activeNode.value
  return !(activeDb?.dbName || activeDb?.dbSchema)
})
const isTableNode = computed(() => {
  return activeNode.value?.nodeType === NodeType.table.value
})
const isDBNode = computed(() => {
  return (
    activeNode.value?.nodeType === NodeType.database.value ||
    activeNode.value?.nodeType === NodeType.schema.value
  )
})
const expandedKeys = ref<Key[]>([])
const treeData = ref<SqlDataNode[]>([])
/** 加载数据连接列表 */
const { loading: loadingSqlTree } = useRequest(api.getDBConnections, {
  onSuccess: (res) => {
    treeData.value = res
  },
})
/** 加载数据库实例列表 */
const { runAsync: getDbInstances } = useRequest(api.getDbInstances, {
  manual: true,
})
/** 加载表列表 */
const { runAsync: getDbTables } = useRequest(api.getDbTables, {
  manual: true,
})

const onSelect = (nodeData: SqlDataNode) => {
  logPlus.red('选中节点', nodeData)
  activeNode.value = nodeData
}

const onLoadData = async (nodeData: SqlDataNode) => {
  logPlus.red('加载子节点', nodeData)
  switch (nodeData.nodeType) {
    case NodeType.connection.value: {
      /** 加载数据连接下的数据库实例列表 */
      const res = await getDbInstances(nodeData.key)
      nodeData.children = res.map((item) => ({
        ...item,
        connectionId: nodeData.key,
        dbConnection: nodeData.dbConnection,
        key: nodeData.key + '-' + item.key, // 兼容不同数据连接下出现相同key的情况
        isLeaf: false,
      }))
      break
    }
    case NodeType.database.value: {
      /** 加载数据库下的表 */
      const res = await getDbTables({ id: nodeData.connectionId!, dbName: nodeData.title })
      nodeData.children = res.map((item) => ({
        ...item,
        connectionId: nodeData.connectionId,
        dbName: nodeData.title,
        dbConnection: nodeData.dbConnection,
        isLeaf: item.nodeType === NodeType.table.value,
      }))
      break
    }
    case NodeType.schema.value: {
      /** 加载 schema 下的表 */
      const res = await getDbTables({ id: nodeData.connectionId!, dbSchema: nodeData.title })
      nodeData.children = res.map((item) => ({
        ...item,
        connectionId: nodeData.connectionId,
        dbName: nodeData.dbName,
        dbSchema: nodeData.title,
        dbConnection: nodeData.dbConnection,
        isLeaf: item.nodeType === NodeType.table.value,
      }))
      break
    }
  }
}
/** 刷新节点 */
const refreshNode = async (record: SqlDataNode) => {
  await onLoadData(record)
  // 展开节点
  expandedKeys.value = Array.from(new Set([...expandedKeys.value, record.key]))
}
/** 点击右键菜单 */
const handleMenuClick = async (key: Key, record: SqlDataNode) => {
  logPlus.red('点击右键菜单', key, record)
  if (key === menuKey.refresh) {
    await refreshNode(record)
  }
  if (key === menuKey.viewData) {
    // 查看数据
  }
  if (key === menuKey.copyTable) {
    // 复制表名
    copyText(record.title)
  }
  if (key === menuKey.generateSql) {
    // 生成SQL
  }
}
const findParent = (node: SqlDataNode): SqlDataNode | undefined => {
  if (!node) return undefined
  const targetKey = node.key as any
  const dfs = (nodes: SqlDataNode[] = []): SqlDataNode | undefined => {
    for (const cur of nodes) {
      const children = (cur.children || []) as SqlDataNode[]
      if (children.some((c) => c.key === targetKey)) {
        return cur
      }
      const found = dfs(children)
      if (found) return found
    }
    return undefined
  }
  return dfs(treeData.value)
}
const onCopyTable = async (isCopyData = false) => {
  if (!activeNode.value || !copyTableModalRef.value) return
  const desName = await copyTableModalRef.value.open(activeNode.value.title)
  if (!desName) return
  const isSuccess = await api.copyTable({
    id: activeNode.value.connectionId!,
    dbName: activeNode.value.dbName,
    schema: activeNode.value.dbSchema,
    srcName: activeNode.value.title,
    desName,
    isCopyData,
  })
  if (isSuccess) {
    message.success('复制成功')
    // 刷新左侧目录树
    const res = await getDbTables({
      id: activeNode.value.connectionId!,
      dbName: activeNode.value.dbName,
      dbSchema: activeNode.value.dbSchema,
    })
    updateNodeParent(activeNode.value, res)
  } else {
    message.error('复制失败')
  }
}
const updateNodeParent = (node: SqlDataNode, children: SqlDataNode[]) => {
  const parent = findParent(node)
  if (!parent) return
  parent.children = children.map((item) => ({
    ...item,
    connectionId: activeNode.value?.connectionId,
    dbName: activeNode.value?.dbName,
    dbSchema: activeNode.value?.dbSchema,
    dbConnection: activeNode.value?.dbConnection,
    isLeaf: item.nodeType === NodeType.table.value,
  }))
}
const onEditTableName = () => {
  isEditingName.value = true
  editingName.value = activeNode.value?.title || ''
}
const { loading: loadingRenameTable, runAsync: renameTable } = useRequest(api.renameTable, {
  manual: true,
})
const handleRenameTable = async () => {
  isEditingName.value = false
  if (!activeNode.value) return
  if (editingName.value.trim() === activeNode.value.title) return
  const isSuccess = await renameTable({
    id: activeNode.value.connectionId!,
    dbName: activeNode.value.dbName,
    schema: activeNode.value.dbSchema,
    srcName: activeNode.value.title,
    desName: editingName.value.trim(),
  })
  if (isSuccess) {
    message.success('重命名成功')
    activeNode.value.title = editingName.value.trim()
  } else message.error('重命名失败')
}
const backToDbView = () => {
  if (!activeNode.value) return
  const evtBus = useEventBus<string, SqlDataNode>('sql-editing')
  const parent = findParent(activeNode.value)
  if (!parent) return
  evtBus.emit('update-selected-key', parent)
}
/** 提供当前选中的数据库节点 */
provide(activeDbKey, activeNode)
</script>

<template>
  <div class="sql-editing-container p-0! overflow-hidden" flex="~">
    <Splitter :min-size="240" initial-split="280px">
      <template #left>
        <div class="left size-full overflow-y-auto p-4 scrollbar-hide">
          <SqlTreeComp
            :treeData="treeData"
            :loading="loadingSqlTree"
            :loadData="onLoadData"
            v-model:expandedKeys="expandedKeys"
            @select="onSelect"
          >
            <template #title="{ record }">
              <!-- 默认为刷新按钮 -->
              <Dropdown :trigger="['contextmenu']">
                <span>{{ record.title }}</span>
                <template #overlay>
                  <Menu
                    v-if="record.nodeType !== NodeType.table.value"
                    @click="({ key }) => handleMenuClick(key, record)"
                  >
                    <MenuItem :key="menuKey.refresh"> 刷新 </MenuItem>
                  </Menu>
                  <Menu v-else @click="({ key }) => handleMenuClick(key, record)">
                    <MenuItem :key="menuKey.viewData"> 查看数据 </MenuItem>
                    <MenuItem :key="menuKey.copyTable"> 复制表名 </MenuItem>
                    <MenuItem :key="menuKey.generateSql"> 生成SQL </MenuItem>
                  </Menu>
                </template>
              </Dropdown>
            </template>
          </SqlTreeComp>
        </div>
      </template>
      <template #right>
        <div class="right h-full p-4" flex="~ col auto gap-4">
          <a-empty v-if="isShowEmpty" description="请先选择数据库"></a-empty>
          <!-- 左侧选中的是数据库 -->
          <template v-else-if="isDBNode">
            <div class="flex items-center gap-2">
              <img :src="getDbIcon(activeNode?.dbConnection?.dbType)" class="size-10" />
              <strong text="20px">{{ activeNode?.dbName || '' }}</strong>
            </div>
            <Tabs class="h-full flex-gap-4">
              <TabPane tab="数据列表" key="1">
                <DBTablesView />
              </TabPane>
              <TabPane tab="可视化设计" key="2">
                <DBDesigner />
              </TabPane>
              <TabPane tab="详情" key="3">
                <div class="sql-editing-right-header-title">详情</div>
              </TabPane>
            </Tabs>
          </template>
          <!-- 左侧选中的是表 -->
          <template v-else-if="isTableNode">
            <!-- 面包屑 -->
            <a-breadcrumb separator=">" text="primary">
              <a-breadcrumb-item>{{ activeNode?.dbConnection?.name || '-' }}</a-breadcrumb-item>
              <a-breadcrumb-item>
                <a @click="backToDbView" style="color: #65aae7">{{ activeNode?.dbName || '-' }}</a>
              </a-breadcrumb-item>
              <a-breadcrumb-item text="primary">
                {{ activeNode?.title || '-' }}
              </a-breadcrumb-item>
            </a-breadcrumb>
            <div class="flex items-end gap-2 justify-between">
              <!-- 头部左侧 -->
              <a-space>
                <TableOutlined text="6" />
                <strong v-if="!isEditingName" text="20px">{{ activeNode?.title || '' }}</strong>
                <div v-else flex="~ gap-0 items-center">
                  <a-input
                    v-model:value="editingName"
                    text="b700 14px"
                    @press-enter="handleRenameTable"
                  />
                  <a-button type="link" size="small" @click="isEditingName = false">取消</a-button>
                  <a-button type="link" size="small" @click="handleRenameTable">确定</a-button>
                </div>
                <a-button
                  type="text"
                  :icon="h(EditOutlined)"
                  :loading="loadingRenameTable"
                  @click="onEditTableName"
                />
                <a-dropdown>
                  <CopyOutlined text="14px" />
                  <template #overlay>
                    <Menu>
                      <MenuItem @click="onCopyTable()"> 复制结构 </MenuItem>
                      <MenuItem @click="onCopyTable(true)"> 复制结构和数据 </MenuItem>
                    </Menu>
                  </template>
                </a-dropdown>
              </a-space>
              <!-- 头部右侧 -->
              <a-space>
                <a-dropdown>
                  <a-button
                    type="text"
                    :icon="
                      h(EllipsisOutlined, {
                        style: { transform: 'rotate(90deg)', fontSize: '18px' },
                      })
                    "
                  />
                  <template #overlay>
                    <Menu>
                      <MenuItem>编辑</MenuItem>
                      <MenuItem>删除</MenuItem>
                    </Menu>
                  </template>
                </a-dropdown>
                <a-button type="primary">创建查询</a-button>
              </a-space>
            </div>
            <Tabs class="h-full flex-gap-4">
              <TabPane tab="字段" key="4"> 字段 </TabPane>
              <TabPane tab="详情" key="5"> 详情 </TabPane>
              <TabPane tab="数据预览" key="6">
                <div class="sql-editing-right-header-title">数据预览</div>
              </TabPane>
            </Tabs>
          </template>
        </div>
      </template>
    </Splitter>
    <CopyTableModal ref="copyTableModalRef" />
  </div>
</template>

<style scoped lang="less">
:deep(.ant-tree-treenode) {
  white-space: nowrap;
  width: 100%;
}
:deep(.ant-tabs .ant-tabs-content) {
  height: 100%;
  overflow: hidden;
}
:deep(.ant-tabs-nav) {
  margin: 0;
}
</style>
