<script lang="ts" setup>
import { ref } from 'vue'

const open = ref(false)
const currentData = ref()

const showDrawer = (record: any) => {
  console.log('当前record', record)
  currentData.value = record
  open.value = true
}

const onClose = () => {
  open.value = false
}

defineExpose({
  showDrawer,
})
</script>

<template>
  <a-drawer
    title="高级配置"
    placement="right"
    :closable="false"
    :open="open"
    :get-container="false"
    :style="{ position: 'absolute' }"
    @close="onClose"
    :push="false"
    :mask-closable="true"
    :maskStyle="{ background: 'transparent' }"
  >
    <a-descriptions :column="1">
      <a-descriptions-item label="属性名称（中文）">{{
        currentData?.nameZh ?? '-'
      }}</a-descriptions-item>
      <a-descriptions-item label="属性名称（英文）">{{
        currentData?.name ?? '-'
      }}</a-descriptions-item>
      <a-descriptions-item label="属性类型"
        >{{ currentData?.rangeName ?? '-' }}({{
          currentData?.rangeNameZh ?? '-'
        }})</a-descriptions-item
      >
      <a-descriptions-item label="描述">{{ currentData?.description ?? '-' }}</a-descriptions-item>
      <a-descriptions-item label="约束类型">
        {{ currentData?.constraints?.[0]?.nameZh }}
      </a-descriptions-item>
    </a-descriptions>
  </a-drawer>
</template>

<style lang="less" scoped></style>
