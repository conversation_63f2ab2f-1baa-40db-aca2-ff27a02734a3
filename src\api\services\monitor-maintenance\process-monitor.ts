import service from '@/api'
import { SODATAFLINK } from '@/api/base'

export interface ProcessMonitorDTO {
  serverId: string
  info: {
    type: string
    avgTime: string
    lastTime: string
    status: string
    tips: string
    env: {
      jvmEnv: any
      osEvn: any
    }
    serviceInfo: {
      id: string
      serviceIp: string
      servicePort: string
    }
  }
}

export interface EnvDTO {
  osEvn: {
    name?: string // 操作系统
    version?: string // 操作系统版本
    arch?: string // 处理器类型
    availableProcessors?: string // 核数
    totalPhysicalMemorySize?: string // 内存
    freePhysicalMemorySize?: string // 剩余内存
    freeSwapSpaceSize?: string // 剩余交换分区
    totalSwapSpaceSize?: string // 交换分区
  }
  jvmEvn: {
    threadCount?: string // 线程总数
    maxMemory?: string // jvm最大可用内存
    totalMemory?: string // jvm内存
    freeMemory?: string // Jvm剩余内存
    gcs?: {
      name?: string // Gc区域
      count?: string // Gc次数
      time?: string // Gc用时
    }[]
  }
}

export interface ProcessMonitorDataDTO {
  serverId: string
  avgTime: string
  lastTime: string
  status: string
  tips: string
  id: string
  serviceIp: string
  servicePort: string
}

export interface Parameter {
  [prop: string]: any
}
/**
 * 获取服务进程监控信息
 * @param params
 */
export function getProcessMonitor(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/new2dataplatform/serviceMonitor/health`,
    method: 'post',
    data: params,
    extraParams: {
      showLoading: false,
    },
  })
}

/**
 * 服务启停
 * @param params
 */
export function setProcessMonitor(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/new2dataplatform/serviceMonitor/health`,
    method: 'post',
    data: params,
  })
}

/**
 * 日志服务
 * @param params
 */
export function tailLogFun(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/LogMonitor/tailLogFun`,
    method: 'post',
    data: params,
  })
}

/**
 * 获取下载日志参数
 * @param params
 */
export function getDownLogParam(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/LogMonitor/downloadServiceLogFun`,
    method: 'post',
    data: params,
  })
}
