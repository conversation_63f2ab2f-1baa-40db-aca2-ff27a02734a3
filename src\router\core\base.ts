import type { RouteRecordRaw } from 'vue-router'

/**
 * 默认路由
 */
const defaultRoutes: RouteRecordRaw[] = [
  {
    path: '/login',
    name: 'login',
    meta: {
      title: '登录',
    },
    component: () => import('@/layout/login/login-view.vue'),
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'empty',
    meta: {
      title: '404页面',
    },
    component: () => import('@/views/permission/not-found.vue'),
  },
  {
    path: '/500',
    meta: {
      title: '500页面',
    },
    name: 'error',
    component: () => import('@/views/permission/server-error.vue'),
  },
]

export default defaultRoutes
