import scpClient from 'scp2'

// 本地上传文件夹
const sourceDirPath = './dist'

// 上传 options
const options = {
  host: '*************',
  username: 'root',
  password: 'nmoa29GhIy',
  path: '/www/new-data-platform-app/dist',
}

async function upload() {
  console.log('[准备上传本地目录]: ', sourceDirPath)
  return new Promise((resolve, reject) => {
    scpClient.scp(sourceDirPath, options, (error) => {
      if (error) {
        console.log('[上传失败]: ', error)
        reject(error)
      }

      console.log('上传成功')
      resolve()
    })
  })
}

upload()
