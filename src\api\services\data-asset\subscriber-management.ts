export interface AssetResponse<T = any> {
  data: T
  code: string
  msg: string
  result: string
}

export interface ISubscriber {
  serverId?: string
  serverName: string
  password: string
  apiHeaders: string
  recEmail: string
  apiHost: string
  apiPath: string
  host: string
  fromAddress: string
  userName: string
  apiType: string | null
}

export type ISubscriberForm = Omit<ISubscriber, 'serverId'>

export interface ISubscriberParams {
  serverName?: string
}
