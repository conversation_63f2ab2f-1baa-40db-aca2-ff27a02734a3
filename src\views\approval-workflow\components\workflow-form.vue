<template>
  <div class="workflow-form">
    <div class="workflow-form__steps">
      <a-steps :current="currentStep" :items="steps" />
    </div>

    <div class="workflow-form__content">
      <!-- 第一步：基本信息 -->
      <div v-show="currentStep === 0" class="step-content">
        <a-form
          :label-col="{ span: 4 }"
          :wrapper-col="{ span: 17 }"
          :model="formState"
          ref="formRef"
          :loading="loading"
          layout="horizontal"
        >
          <a-form-item
            label="名称"
            name="name"
            :rules="[{ required: true, message: '请输入名称' }]"
          >
            <a-input v-model:value="formState.name" placeholder="请输入名称" />
          </a-form-item>
          <a-form-item
            label="分类"
            name="category"
            :rules="[{ required: true, message: '请选择分类' }]"
          >
            <a-select
              v-model:value="formState.category"
              placeholder="请选择分类"
              :options="workflowTypeList"
              :loading="optionsLoading"
            />
          </a-form-item>
          <a-form-item label="通知地址" name="pushResultUrl">
            <a-input disabled :value="formState.pushResultUrl" />
          </a-form-item>
          <a-form-item label="描述" name="description">
            <a-textarea v-model:value="formState.description" placeholder="请输入描述" />
          </a-form-item>
        </a-form>
      </div>

      <!-- 第二步：审批设置 -->
      <div v-show="currentStep === 1" class="step-content">
        <div class="workflow-form__node-container">
          <FlowNode
            :node-data="{
              id: 'start',
              label: '开始',
              enabled: false,
              isStart: true,
            }"
            @addNode="addNode"
            @minusNode="minusNode"
          />
          <FlowNode
            v-for="(node, index) in flowNodes"
            :key="node.label + index"
            :node-data="node"
            @addNode="addNode"
            @changeEnabled="changeEnabled"
            @minusNode="minusNode"
            @selectUser="selectUser"
          />
          <FlowNode
            :node-data="{
              id: 'end',
              label: '结束',
              enabled: false,
              isEnd: true,
            }"
          />
        </div>
      </div>
    </div>

    <div class="workflow-form__footer">
      <!-- 第一步：只有下一步 -->
      <div v-if="currentStep === 0" class="step-buttons">
        <a-button @click="handleCancel">取消</a-button>
        <a-button type="primary" @click="nextStep">下一步</a-button>
      </div>

      <!-- 第二步：上一步和保存 -->
      <div v-if="currentStep === 1" class="step-buttons">
        <a-button @click="prevStep">上一步</a-button>
        <a-button type="primary" :loading="saveLoading" @click="handleSave">保存</a-button>
      </div>
    </div>

    <a-modal :open="showModal" title="选择审批人" :footer="null" @cancel="closeModal">
      <ApproverSelector :dept-list="deptList" @cancel="closeModal" @confirm="handleConfirm" />
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted, nextTick } from 'vue'
import { message, Form } from 'ant-design-vue'
import FlowNode from './flow-node.vue'
import { type FlowNodeData, type ListItem } from '../type'
import ApproverSelector from './approver-selector.vue'
import { WorkflowTypeOptions } from '../constant'
import {
  getApprovalWorkflowTypeList,
  getDeptList,
  queryWorkflowDetail,
  type WorkflowTypeItem,
} from '@/api/approval-workflow'
import { listItem2WorkflowItem, workflowItem2ListItem } from '../transform-util'

// Props
interface Props {
  data?: ListItem
}

const props = withDefaults(defineProps<Props>(), {
  data: undefined,
})

// Emits
const emit = defineEmits<{
  save: [data: any, callback: () => void]
  cancel: []
}>()

// Refs
const formRef = ref<any>()

// 步骤相关
const currentStep = ref(0)
const steps = [
  {
    title: '基本信息',
  },
  {
    title: '审批设置',
  },
]

// Reactive state
const optionsLoading = ref(true)
const workflowTypeList = ref<WorkflowTypeItem[]>([])
const deptList = ref<any[]>([])
const idCount = ref(1)
const loading = ref(false)
const saveLoading = ref(false)
const showModal = ref(false)
const selectedIndex = ref<number | null>(null)

const flowNodes = ref<FlowNodeData[]>([
  {
    id: '1',
    label: '审批人',
    enabled: true,
  },
])

const formState = reactive<Partial<ListItem>>({
  name: '',
  description: '',
  category: undefined,
  pushResultUrl: '',
  flowNodes: [],
})

// Computed
const isEditing = computed(() => !!props.data)

// Watchers
watch(
  () => props.data,
  (newVal: any) => {
    console.log('data', newVal)
    if (newVal) {
      if (!newVal.flowNodes || !newVal.flowNodes.length) {
        try {
          loading.value = true
          queryWorkflowDetail(newVal.id).then((res: any) => {
            if (res.code === '000000' && res.data) {
              Object.assign(formState, workflowItem2ListItem(res.data) as ListItem)
              flowNodes.value = formState.flowNodes || []
            } else {
              message.error(res.msg || '获取审批流详情失败')
            }
          })
        } catch (error) {
          message.error('获取审批流详情失败')
        } finally {
          loading.value = false
        }
      } else {
        flowNodes.value = [...newVal.flowNodes]
        Object.assign(formState, { ...newVal })
      }
    }
  },
  { immediate: true },
)

watch(
  () => formState.category,
  (newValue: string | undefined) => {
    if (!newValue) return
    const target = workflowTypeList.value.find((item: WorkflowTypeItem) => item.value === newValue)
    if (!target) return
    formState.pushResultUrl = target.address
  },
)

// Methods
/**
 * 下一步
 */
const nextStep = async () => {
  await formRef.value.validateFields()
  console.log('第一步验证成功')
  currentStep.value = 1
}

/**
 * 上一步
 */
const prevStep = () => {
  currentStep.value = 0
}

/**
 * 新增节点
 * @param node 当前节点
 */
const addNode = (node: FlowNodeData) => {
  let index = flowNodes.value.findIndex((item) => item.id === node.id)
  if (index !== -1) index = index + 1
  else index = 0
  const id = String(++idCount.value)
  console.log(`新增节点: 索引-${index}, id-${id}`)
  flowNodes.value.splice(index, 0, {
    id,
    label: '审批人',
    enabled: true,
  })
  console.log('节点列表', flowNodes.value)
}

/**
 * 更新节点 checkbox
 * @param node 当前节点
 * @param e 事件对象
 */
const changeEnabled = (node: FlowNodeData, e: any) => {
  let index = flowNodes.value.findIndex((item) => item.id === node.id)
  if (index === -1) return
  const checked = e.target.checked
  console.log('更新节点 checkbox', index, checked)
  flowNodes.value[index].enabled = checked
}

/**
 * 删除节点
 * @param node 当前节点
 */
const minusNode = (node: FlowNodeData) => {
  let index = flowNodes.value.findIndex((item) => item.id === node.id)
  if (index === -1) return
  console.log('删除节点', index)
  flowNodes.value.splice(index, 1)
}

/**
 * 打开审批人选择器
 * @param node 当前节点
 */
const selectUser = (node: FlowNodeData) => {
  let index = flowNodes.value.findIndex((item) => item.id === node.id)
  if (index === -1) return
  console.log('打开审批人选择器', index)
  showModal.value = true
  selectedIndex.value = index
}

const closeModal = () => {
  showModal.value = false
  selectedIndex.value = null
}

/**
 * 确定选择的审批人
 * @param data 选择的审批人
 */
const handleConfirm = (data: Partial<{ label: string; value: any }>) => {
  console.log('确定选择的审批人', data)
  showModal.value = false
  if (selectedIndex.value !== null && data.label && data.value) {
    flowNodes.value[selectedIndex.value].label = data.label
    flowNodes.value[selectedIndex.value].value = data.value
  }
}

const handleSave = async () => {
  if (flowNodes.value.length === 0) {
    message.warning('请至少添加一个审批节点')
    return
  } else if (flowNodes.value.some((item) => !item.value)) {
    message.warning('请选择审批人')
    return
  }

  console.log('表单验证成功')
  saveLoading.value = true
  const data = {
    ...formState,
    flowNodes: flowNodes.value,
  }
  emit('save', data, () => {
    saveLoading.value = false
  })
}

const handleCancel = () => {
  emit('cancel')
}

/**
 * 查询部门列表
 */
const queryDeptList = async () => {
  try {
    const res = await getDeptList()
    if (res.code === '000000' && res.data) {
      deptList.value = res.data
    } else {
      message.error(res.msg || '获取部门列表失败')
    }
  } catch (error) {
    message.error('获取部门列表失败')
  }
}

/**
 * 获取审批流类型列表
 */
const queryWorkflowTypeList = async () => {
  try {
    optionsLoading.value = true
    const res = await getApprovalWorkflowTypeList()
    if (res.code === '000000' && res.data) {
      workflowTypeList.value = res.data
    } else {
      message.error(res.msg || '获取审批流类型列表失败')
    }
  } catch (error) {
    message.error('获取审批流类型列表失败')
  } finally {
    optionsLoading.value = false
  }
}

// Lifecycle
onMounted(() => {
  queryDeptList()
  queryWorkflowTypeList()
})
</script>

<style lang="less" scoped>
.workflow-form {
  background-color: #fff;
  height: 100%;
  display: flex;
  flex-direction: column;

  &__steps {
    margin: 0 auto;
    margin-bottom: 20px;
    padding: 24px 24px 0 24px;
    width: 500px;
  }

  &__content {
    flex: 1;
    display: flex;
    flex-direction: column;
    width: 100%;
    overflow-y: auto;
  }

  &__node-container {
    display: flex;
    align-items: flex-start;
    flex-direction: row;
    flex-wrap: wrap;
    width: 95%;
    gap: 16px;
  }

  &__footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    padding: 0 24px 16px 24px;
  }
}

.step-content {
  padding: 24px;
  flex: 1;
}

.step-buttons {
  display: flex;
  gap: 10px;
}

:deep(.ant-steps-item-title) {
  font-weight: 500;
}

:deep(.ant-steps-item-description) {
  color: #666;
}
</style>
