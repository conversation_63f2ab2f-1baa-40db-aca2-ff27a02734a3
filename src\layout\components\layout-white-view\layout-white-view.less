.fs-layout {
  --content-mg-w: 16px;
  --content-pd-w: 20px;
  --header-h: 52px;
  height: 100vh;
  .layout-sider {
    background-color: #fff;
    height: 100%;
    .section-sider {
      display: flex;
      flex-direction: column;
      height: 100%;
      .menu {
        flex: 1;
        overflow: hidden;
        &:hover {
          overflow: auto;
        }
      }
      :deep(.ant-menu) {
        background-color: #fff;
        .ant-menu-submenu .ant-menu-submenu-title,
        .ant-menu-inline .ant-menu-item,
        .ant-menu-item {
          height: 36px;
          border-radius: 4px;
          line-height: 36px;
          font-weight: 700;
          color: rgb(79, 82, 92);
          padding: 10px 20px !important;
          &:hover {
            background-color: #c2ddf5;
            color: rgb(80, 158, 227);
            font-weight: 700;
          }
        }
        .ant-menu-item-selected {
          background-color: #dcecf9;
          color: rgb(80, 158, 227);
          font-weight: 700;
        }
        .ant-menu-submenu {
          margin-top: 10px;
        }
        .ant-menu-submenu-title {
          height: 15px !important;
          font-size: 12px;
          color: #696e7b !important;
        }
        .ant-menu-submenu-title:hover {
          background-color: #fff !important;
          color: #696e7b !important;
        }
      }
      .collapsed {
        border-top: 1px solid #e5e6eb;
        height: 56px;
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 0 26px;
        .collapsed-icon {
          font-size: 18px;
          transform-origin: center center;
          transform: rotate(-90deg);
          &.close {
            transform: rotate(90deg);
          }
        }
      }
    }
  }
  .layout-internal {
    height: calc(100vh - var(--header-h));
    display: flex;
    .layout-section-right {
      overflow: auto;
    }
  }
  .layout-header {
    position: sticky;
    top: 0;
    padding-left: 16px;
    padding-right: 16px;
    background-color: #fff;
    display: flex;
    justify-content: space-between;
    align-items: center;
    line-height: initial;
    border-bottom: 1px solid #e5e6eb;
    height: var(--header-h);
    .logo {
      display: flex;
      align-items: center;
      gap: 8px;
      cursor: pointer;
      .img {
        width: 32px;
      }
      .tit {
        font-weight: bold;
        font-size: 18px;
      }
    }
    .header-bar {
      display: flex;
      align-items: center;
      gap: 10px;
      .user {
        display: flex;
        align-items: center;
        cursor: pointer;
        .name {
          font-size: 14px;
          margin-left: 8px;
          max-width: 100px;
          overflow: hidden; /* 溢出内容隐藏 */
          text-overflow: ellipsis; /* 溢出文本显示省略号 */
        }
      }
    }
  }

  .menu-slot {
    margin: 10px 5px 15px;
  }
  :deep(.ant-layout) {
    background-color: #f9fbfc !important;
  }
  .layout-content {
    background-color: #f9fbfc !important;
    .breadcrumb {
      margin-bottom: var(--content-mg-w);
    }
    &.secondary {
      border-radius: 0;
      margin: 0;
      padding: 0;
    }
    margin: var(--content-mg-w);
    padding: var(--content-pd-w);
    background-color: #fff !important;
    min-height: auto;
    display: flex;
    flex-direction: column;
    border-radius: 8px;
  }
  .layout-footer {
    text-align: center;
    font-size: 12px;
    color: #999;
  }
}

// 配置滚动条样式
.scrollbar-style::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}
.scrollbar-style::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.1);
}
