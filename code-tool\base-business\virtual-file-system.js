import { promises as fs } from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'
import { dirname } from 'path'
import { GENERATE_PATH, CATCH_PATH } from '../config/index.js'
import { debounce, getToLocaleLowerCase } from '../tools/index.js'
import prettier from 'prettier'
import { generateTemplateApi } from '../page-template/generate-api.js'
import { getApiJson, setApiJson } from '../http-servers/module/index.js'
import { generateTemplateMockApi } from '../page-template/mock-api.js'
import fsExtra from 'fs-extra'
const { ensureFile } = fsExtra

// 模拟虚拟文件系统的结构
let virtualFileSystem = await readSystemFile()

/**
 * 创建文件并将其内容写入虚拟文件系统中的指定位置。
 * @param {string} key - 文件所属的键值，用于标识文件所在的文件夹或模块
 * @param {string} name - 文件的名称
 * @param {string} content - 文件的内容
 * @returns {void}
 */
export async function createFile(key, name, content) {
  // 如果虚拟文件系统中不存在对应的键值，则创建一个空对象
  if (!virtualFileSystem[key]) {
    virtualFileSystem[key] = {}
  }

  // 将文件内容写入虚拟文件系统中的指定位置
  virtualFileSystem[key][name] = content

  // 将虚拟文件系统的内容写入磁盘
  setFileToDisk()
}

export async function createApiFile(key, name, content, projectName) {
  // 如果虚拟文件系统中不存在对应的键值，则创建一个空对象
  if (!virtualFileSystem[key]) {
    virtualFileSystem[key] = {}
  }
  if (!virtualFileSystem[key][projectName]) {
    virtualFileSystem[key][projectName] = {}
  }
  virtualFileSystem[key][projectName][name] = content
  if (name === 'system-config-detail') {
    // 即使写入虚拟文件系统
    writeVirtualJson(virtualFileSystem)
  } else {
    // 将虚拟文件系统的内容写入磁盘
    setFileToDisk()
  }
}

/**
 * 更新虚拟文件系统中指定位置的文件内容，并将其写入磁盘。
 * @async
 * @function updateFile
 * @param {string} key - 文件所属的键值，用于标识文件所在的文件夹或模块
 * @param {string} name - 文件的名称
 * @param {string} data - 更新的数据
 * @param {string} projectName - 项目名称 api需要区分项目
 * @returns {Promise<void>} - 返回一个解析为undefined的Promise
 * @throws {Error} - 如果更新文件时发生错误，则抛出错误
 */
export async function updateFile(key, name, data, projectName) {
  console.log('%c 🍲 key: ', 'font-size:12px;background-color: #3F7CFF;color:#fff;', key)
  try {
    const virtual = await readSystemFile()

    // 如果虚拟文件系统中不存在对应的键值，则创建一个空对象
    if (!virtual[key]) {
      if (key === 'api') {
        createApiFile(key, name, data, projectName)
      } else {
        createFile(key, name, data)
      }

      return
    }

    if (key === 'api') {
      // 更新指定位置的文件内容
      const selectData =
        virtual[key][projectName][name] && JSON.parse(virtual[key][projectName][name])
      if (selectData) {
        selectData.interfaceInfos = selectData.interfaceInfos.map((item) => {
          const selectItem = JSON.parse(data).interfaceInfos.find(
            (i) => i.interfaceName === item.interfaceName,
          )
          return selectItem || item
        })

        virtual[key][projectName][name] = JSON.stringify(selectData)
      }
    } else {
      virtual[key][name] = data
    }
    // 更新虚拟文件系统
    virtualFileSystem = virtual
    // 将虚拟文件系统的内容写入磁盘
    await setFileToDisk()
  } catch (error) {
    console.error('Error updating file:', error)
    throw error
  }
}

// 读取虚拟文件系统中的文件
export async function readSystemFile() {
  try {
    const virtualFileSystemDirPath = getJsonPath().cacheDirPath
    const virtualFileSystemPath = path.join(virtualFileSystemDirPath, 'virtual-file-system.json')
    // 读取虚拟文件系统的内容
    const content = await fs.readFile(virtualFileSystemPath, 'utf8')
    return JSON.parse(content)
  } catch (err) {
    return {}
  }
}

/**
 * 将文件写入dist磁盘
 *
 * @param {string} fileName - 要写入的文件名
 * @param {string} type - 文件类型,用于确定存储路径
 * @param {Object} virtual - 文件系统数据 - 虚拟文件系统数据
 * @param {string} [pathType='dist'] - 路径类型，默认为 'dist'
 * @throws {Error} 如果虚拟文件系统中不存在指定的文件
 *
 * @description
 * 该函数执行以下操作:
 * 1. 获取dist目录路径
 * 2. 根据文件类型拼接模块路径
 * 3. 检查虚拟文件系统中是否存在对应的文件
 * 4. 拼接完整的文件路径
 * 5. 从虚拟文件系统获取文件内容
 * 6. 将文件内容写入磁盘
 *
 * @example
 * writeFileToDist('example.js', 'scripts');
 */
export async function writeRouterViewsToDist(fileName, type, virtual) {
  // 获取生成目录路径
  const { distDirPath } = getJsonPath()

  // 拼接模块路径
  const moudlePath = path.join(distDirPath, type)

  // 检查虚拟文件系统中是否存在对应的文件
  if (!virtual[type] || !virtual[type][fileName]) {
    console.error(`虚拟文件系统中不存在文件: ${fileName}`)
    return
  }
  const newfileName = type === 'views' ? `${fileName}/index.vue` : fileName + '.ts'

  // 获取文件内容
  let content = virtual[type][fileName]
  const filePath = path.join(moudlePath, newfileName)
  await ensureFile(filePath)
  // 将文件内容写入磁盘
  fs.writeFile(filePath, content)
}

/**
 * 将测试用例写入到指定的文件中
 *
 * @async
 * @function writeTextFile
 * @param {Object} data - 包含文件内容和文件名的对象
 * @param {string} data.moduleName - 文件名
 * @param {string} data.content - 文件内容
 * @throws {Error} 如果文件写入失败
 *
 * @description
 * 该函数执行以下操作：
 * 1. 获取文件路径
 * 2. 创建文件目录（如果不存在）
 * 3. 将文本内容写入到文件中
 *
 * @example
 * const data = {
 *   moduleName: 'example',
 *   content: 'This is an example text.'
 * };
 * writeTextFile(data);
 */
export async function writeTextFile(data, project) {
  const { srcApiDirPath } = getJsonPath()
  const textfilefolder = path.join(srcApiDirPath, `${project.project}/${data.moduleName}`)
  await fs.mkdir(textfilefolder, { recursive: true })
  const textfilePath = path.join(textfilefolder, `${getToLocaleLowerCase(data.moduleName)}.test.js`)
  await fs.writeFile(textfilePath, data.content)
}

/**
 * 将虚拟文件系统中的文件夹结构写入dist磁盘
 *
 * @async
 * @function writeFolderToDisk
 * @throws {Error} 如果清理目标文件夹或写入文件时发生错误
 *
 * @description
 * 该函数执行以下操作：
 * 1. 获取目标目录路径
 * 2. 清空目标文件夹
 * 3. 遍历虚拟文件系统中的所有文件夹
 * 4. 在目标目录中创建对应的文件夹结构
 * 5. 将每个文件夹中的文件写入磁盘
 *
 * @example
 * await writeFolderToDisk();
 */
export async function writeFolderToDisk(virtual = virtualFileSystem) {
  try {
    // 外部调用时重置virtualFileSystem
    virtualFileSystem = virtual
    const { distDirPath } = getJsonPath()
    await clearFolder(distDirPath)

    for (const key in virtual) {
      if (key === 'api') {
        writeApiFolderToDisk()
      } else {
        for (const val in virtual[key]) {
          writeRouterViewsToDist(`${val}`, key, virtual, 'dist')
        }
      }
    }
    setFileToDisk()
  } catch (err) {
    console.log(
      '%c 🥜 写入dist文件失败: ',
      'font-size:12px;background-color: #EA7E5C;color:#fff;',
      err,
    )
  }
}

export async function writeApiFolderToDisk() {
  try {
    // 外部调用时重置virtualFileSystem
    const virtual = virtualFileSystem
    const { apiDirPath } = getJsonPath()
    fs.mkdir(apiDirPath, { recursive: true })
    await clearFolder(apiDirPath)
    fs.mkdir(apiDirPath, { recursive: true })
    for (const key in virtual) {
      if (key === 'api') {
        for (const project in virtual[key]) {
          for (const module in virtual[key][project]) {
            if (module !== 'system-config-detail')
              writeApiFile(project, `${module}`, key, virtual['api'], 'dist')
          }
        }
      }
    }
    setFileToDisk()
  } catch (err) {
    console.log(
      '%c 🥜 写入dist文件失败: ',
      'font-size:12px;background-color: #EA7E5C;color:#fff;',
      err,
    )
  }
}

async function writeApiFile(project, module, funcName, apiData, pathType, other) {
  try {
    // 获取生成目录路径
    const { apiDirPath, srcApiDirPath } = getJsonPath()
    // 拼接模块路径
    const projectPath = path.join(
      pathType === 'dist' ? apiDirPath : srcApiDirPath,
      getToLocaleLowerCase(project),
    )
    fs.mkdir(projectPath, { recursive: true })
    const modulePath = path.join(projectPath, getToLocaleLowerCase(module))
    fs.mkdir(modulePath, { recursive: true })
    const moduleName = module + '.ts'

    let content = apiData[project][module]
    const list = []
    for (const key in apiData[project]) {
      list.push(JSON.parse(apiData[project][key]))
    }
    content = await generateTemplateApi(JSON.parse(content), list, other ? module : '')
    if (!content) return
    const filePath = path.join(modulePath, getToLocaleLowerCase(moduleName))
    // 将文件内容写入磁盘
    fs.writeFile(filePath, content)

    // 生成mock.ts文件
    const mockModuleName = module + '-mock.ts'
    const mockModulePath = path.join(modulePath, getToLocaleLowerCase(mockModuleName))
    // 写入api-mock文件
    await fs.writeFile(mockModulePath, generateTemplateMockApi())
  } catch (error) {
    console.error('写入api文件失败:', error)
  }
}

/**
 * 将 API 数据、路由数据和视图数据写入到文件系统中
 * @async
 * @param {Object} apiData - API 数据对象
 * @param {string} type - 写入的类型，可以是 'all'、'api' 或 'router'
 * @returns {Promise} - 当所有写入操作完成时解决的 Promise
 */
export async function writeFolderToFile(apiData, type = 'all', virtualFileSystem, other = '') {
  // 从系统文件中读取虚拟文件系统数据，如果没有则初始化为空对象
  let virtual = (await readSystemFile()) || {}

  // 如果类型是 'router'，则直接使用虚拟文件系统中的数据
  if (type === 'router') {
    virtual = virtualFileSystem
  }
  // 初始化新的 API 数据对象
  let newApiData
  // 如果有传入的 API 数据，则使用传入的数据，否则使用虚拟文件系统中的 API 数据
  newApiData = apiData ? apiData : virtual['api']
  // 获取虚拟文件系统中的路由数据和视图数据
  const routerData = virtual['router']
  const viewsData = virtual['views']

  // 遍历新的 API 数据对象，将每个项目和模块的 API 文件写入到文件系统中
  if (newApiData && type !== 'router') {
    for (const project in newApiData) {
      for (const module in newApiData[project]) {
        // 过滤掉 swagger 配置文件
        if (module !== 'system-config-detail') {
          writeApiFile(project, module, 'api', newApiData, 'src', other)
        }
      }
    }
  }

  // 如果有路由数据，并且类型是 'all' 或 'router'，则将路由数据写入到文件系统中
  if (routerData && (type === 'all' || type === 'router')) {
    writeFilesRouter(routerData)
  }

  // 如果有视图数据，并且类型是 'all' 或 'router'，则将视图数据写入到文件系统中
  if (viewsData && (type === 'all' || type === 'router')) {
    writeFilesViews(viewsData)
  }
}

/**
 * @param {Object} routerData  路由配置数据
 * 部署路由文件
 */
async function writeFilesRouter(routerData) {
  try {
    const { srcRouterDirPath } = getJsonPath()
    const generatePagePath = path.join(srcRouterDirPath, 'generate.ts')
    await ensureFile(generatePagePath)
    await fs.writeFile(generatePagePath, routerData.generate)
  } catch (error) {
    console.log('写入路由generate文件失败:', error)
  }
}

/**
 * @param {Object} viewsData  页面配置数据
 * 部署views文件
 */
async function writeFilesViews(viewsData) {
  try {
    const { srcViewsDirPath } = getJsonPath()
    for (const view in viewsData) {
      const viewPath = path.join(srcViewsDirPath, view)
      const viewPagePath = path.join(viewPath, `index.vue`)
      await ensureFile(viewPagePath)
      await fs.writeFile(viewPagePath, viewsData[view])
    }
  } catch (error) {
    console.log('写入views文件失败:', error)
  }
}

function setFileToDisk() {
  debouncedWriteFileJson(virtualFileSystem)
}

// debounce 包装后的函数
const debouncedWriteFileJson = debounce(writeVirtualJson, 300)

/**
 * 将虚拟文件系统的内容写入到JSON文件中
 */
export async function writeVirtualJson(virtual) {
  try {
    virtualFileSystem = virtual
    // 获取JSON文件的路径
    const { cacheDirPath } = getJsonPath()
    await fs.mkdir(cacheDirPath, { recursive: true })
    const CATCH_PATH = path.join(cacheDirPath, 'virtual-file-system.json')
    // 将虚拟文件系统的内容以JSON格式写入到指定的JSON文件中
    await fs.writeFile(CATCH_PATH, JSON.stringify(virtual, null, 2))
  } catch (error) {
    console.log('写入virtual-file-system.json文件失败:', error)
  }
}

export function getJsonPath() {
  const __filename = fileURLToPath(import.meta.url) // 获取当前文件的路径
  const __dirname = dirname(__filename) // 获取当前文件所在的目录
  // 计算上级目录路径
  const parentDirPath = dirname(__dirname)
  // 创建dist目录
  const distDirPath = path.join(parentDirPath, GENERATE_PATH)
  // 获取dist Api目录
  const apiDirPath = path.join(distDirPath, '/api')
  // 获取项目根目录
  const projectRootDirectory = dirname(parentDirPath)
  // 获取项目src目录
  const srcDirPath = path.join(projectRootDirectory, '/src')
  // 获取项目src/views目录
  const srcViewsDirPath = path.join(srcDirPath, '/views')
  // 获取项目src/api目录
  const srcApiDirPath = path.join(srcDirPath, '/api')
  // 获取项目src/api目录
  const srcRouterDirPath = path.join(srcDirPath, '/router/biz')

  fs.mkdir(distDirPath, { recursive: true })
  // 创建虚拟文件系统json路径
  const cacheDirPath = path.join(parentDirPath, CATCH_PATH)
  return {
    distDirPath,
    cacheDirPath,
    parentDirPath,
    srcViewsDirPath,
    srcApiDirPath,
    apiDirPath,
    projectRootDirectory,
    srcDirPath,
    srcRouterDirPath,
  }
}

/**
 * 从磁盘读取虚拟文件系统的数据
 *
 * @async
 * @function getFileToDisk
 * @returns {Promise<Object>} 返回虚拟文件系统对象
 * @throws {Error} 如果读取文件时发生错误
 *
 * @description
 * 该函数执行以下操作：
 * 1. 检查虚拟文件系统是否为空
 * 2. 如果为空，尝试从磁盘读取 JSON 文件
 * 3. 解析 JSON 内容并更新虚拟文件系统
 * 4. 如果文件不存在或读取失败，会在控制台输出提示信息
 * 5. 返回更新后的虚拟文件系统对象
 *
 * @example
 * const fileSystem = await getFileToDisk();
 */
export async function getFileToDisk() {
  if (Object.keys(virtualFileSystem).length === 0) {
    try {
      // 获取JSON文件的路径
      const { virtualJsonPath } = getJsonPath()
      // 检查文件是否存在并读取内容
      const content = await fs.readFile(virtualJsonPath, 'utf8')
      if (content) {
        virtualFileSystem = JSON.parse(content)
      }
    } catch (err) {
      console.log('virtualFileSystem.json文件不存在.')
    }
  }
  return virtualFileSystem
}

/**
 * 清空指定文件夹
 * @param {string} folderPath - 要清空的文件夹路径
 */
export async function clearFolder(folderPath) {
  try {
    await fs.access(folderPath)
    const files = await fs.readdir(folderPath)
    for (const file of files) {
      const curPath = path.join(folderPath, file)
      const stat = await fs.lstat(curPath)
      if (stat.isDirectory()) {
        // 如果是子文件夹，递归清空
        await clearFolder(curPath)
      } else {
        // 如果是文件，直接删除
        await fs.unlink(curPath)
      }
    }
  } catch (error) {
    if (error.code === 'ENOENT') {
      // 文件或目录不存在，无需处理
      return
    } else {
      throw error
    }
  }
}

export async function writeFileMockPageData(list) {
  try {
    const { srcDirPath } = getJsonPath()
    const mockPagePath = path.join(srcDirPath, 'utils/mock-page.ts')
    // 将文件内容写入磁盘
    const content = await prettier.format(
      `
      export const mockPagesData =  ${JSON.stringify(list)}
      `,
      { parser: 'typescript' },
    )
    fs.writeFile(mockPagePath, content)
  } catch (err) {
    console.log('写入mock-page.ts失败' + err)
  }
}

/**
 * 删除虚拟文件系统中指定项目的API数据
 * @param {string} project - 要删除的项目名称
 * @returns {Promise<void>} - 当删除操作完成时解析的 Promise
 */
export async function delVirtualData(project) {
  try {
    // 从虚拟文件系统中获取API数据
    const data = await getApiJson()
    // 删除指定项目的数据
    delete data[project]
    // 更新虚拟文件系统中的API数据
    setApiJson(data)
  } catch (error) {
    console.log(`删除${project}模块失败`, error)
  }
}

/**
 * 清除指定类型的 JSON 数据
 * @param {string} type - 要清除的 JSON 数据类型
 * @returns {Promise<void>} - 无返回值的 Promise 对象
 */
export async function clearTypeJson(type) {
  try {
    // 获取虚拟文件系统的 JSON 数据
    const virtualFileSystem = await readSystemFile()
    virtualFileSystem[type] = {}

    // 将更新后的虚拟文件系统写入 JSON 文件
    writeVirtualJson(virtualFileSystem)
  } catch (err) {
    console.log('%c 🍦 err: ', 'font-size:12px;background-color: #7F2B82;color:#fff;', err)
  }
}

/**
 * 检查文件是否存在
 * @async
 * @param {string} filePath - 要检查的文件路径
 * @returns {Promise<boolean>} 如果文件存在返回 true，否则返回 false
 */
export async function checkFileExistence(filePath) {
  try {
    // 使用 fs.access 方法检查文件是否存在，如果文件存在不会抛出异常
    await fs.access(filePath)
    return true
  } catch (error) {
    // 如果文件不存在，fs.access 会抛出异常，此时返回 false
    return false
  }
}

export async function writeMockTestTs(virtualData, project, moduleName) {
  try {
    const projectData = virtualData[project]
    const moduleData = JSON.parse(projectData[moduleName])
    const val = generateTemplateMockApi(moduleData['mock-data'])
    // 获取生成目录路径
    const { srcApiDirPath } = getJsonPath()
    const mockPath = path.join(srcApiDirPath, `${project}/${moduleName}/${moduleName}-mock.ts`)
    const state = await checkFileExistence(mockPath)
    if (!state) {
      throw new Error('mock文件不存在，请先生成代码')
    }
    ensureFile(mockPath)
    // 将文件内容写入磁盘
    const content = await prettier.format(val, { parser: 'typescript' })
    fs.writeFile(mockPath, content)
    return { state: true }
  } catch (error) {
    console.log('写入mock-api.ts失败' + error)
    return { state: false, message: error.message }
  }
}
