<script lang="ts" setup>
import * as echarts from 'echarts'

const chartRef = ref(null)
const option = {
  title: {
    text: '各医院投入产出和预测效率分析对比图',
    left: 'center',
    bottom: 0,
    textStyle: {
      fontSize: 14,
      fontWeight: 'normal',
    },
  },
  tooltip: {
    trigger: 'axis',
    formatter: (params: any) => {
      const actual = params[0]?.data || '-'
      const predicted = params[1]?.data || '-'
      return `${params[0]?.axisValue}<br/>实际值: ${actual}<br/>预测值: ${predicted}`
    },
  },
  legend: {
    data: ['实际值', '预测值', '标准线'],
    bottom: '20px',
    textStyle: {
      color: '#666',
    },
  },
  xAxis: {
    type: 'category',
    data: [
      '医院1',
      '医院2',
      '医院3',
      '医院4',
      '医院5',
      '医院6',
      '医院7',
      '医院8',
      '医院9',
      '医院10',
    ],
    axisLine: {
      lineStyle: {
        color: '#ccc',
      },
    },
    axisLabel: {
      color: '#666',
    },
  },
  yAxis: {
    type: 'value',
    min: 0.6,
    max: 1.0,
    splitLine: {
      lineStyle: {
        type: 'dashed',
        color: '#ddd',
      },
    },
    axisLine: {
      show: false,
    },
    axisLabel: {
      color: '#666',
    },
  },
  series: [
    {
      name: '实际值',
      type: 'line',
      data: [0.7, 0.8, 0.85, 0.88, 0.9, 0.87, 0.89, 0.92, 0.91, 0.93],
      lineStyle: {
        color: '#73C0DE',
      },
      symbol: 'circle',
      symbolSize: 6,
      itemStyle: {
        color: '#73C0DE',
      },
    },
    {
      name: '预测值',
      type: 'line',
      data: [0.68, 0.78, 0.83, 0.86, 0.88, 0.85, 0.87, 0.9, 0.89, 0.91],
      lineStyle: {
        color: '#FAC858',
      },
      symbol: 'circle',
      symbolSize: 6,
      itemStyle: {
        color: '#FAC858',
      },
    },
    {
      name: '标准线',
      type: 'line',
      data: Array(10).fill(0.9), // 标准线固定值
      lineStyle: {
        type: 'dashed',
        color: '#F56C6C',
      },
      symbol: 'none',
    },
  ],
}

onMounted(() => {
  const chart = echarts.init(chartRef.value, null, { renderer: 'canvas', height: 320 })
  chart.setOption(option)
})
</script>

<template>
  <div class="download-chart" ref="chartRef"></div>
</template>

<style lang="scss" scoped></style>
