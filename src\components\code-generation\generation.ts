export interface Project {
  project: string
  modules: Moudle[]
}

export interface Moudle {
  moduleName: string
  moduleNameCn: string
  moduleId: string
  interfaceInfos: item[]
}

export interface item {
  interfaceName: string
}

export interface generation {
  request: any[]
  interfaceInfos: item[]
  form: any[]
  dataSource: any[]
  search: any[]
  project?: string
  module?: string
}

export interface generationForm {
  request: any[]
  form: any[]
  layout: string
  modal: boolean
  arrange: number
  title: string
  width: number
  project?: string
  module?: string
}
