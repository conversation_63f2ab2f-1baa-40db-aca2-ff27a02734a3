<template>
  <div class="packet-management-container">
    <a-spin :spinning="loading">
      <Title title="指标分组管理" desc="用于在目录层级中管理指标" />

      <div style="border: 1px solid #ddd; border-radius: 6px; padding: 10px">
        <a-tree
          v-model:expandedKeys="expandedKeys"
          v-model:selectedKeys="selectedKeys"
          :tree-data="treeData"
        >
          <template #title="{ title, key }">
            <a-popover placement="right" trigger="hover">
              <template #content>
                <div class="popover-buttons">
                  <a @click="handleAddNode(key, title)"><PlusCircleOutlined /> 添加分组</a>
                  <a @click="handleEdit(key)"><EditOutlined /> 修改</a>
                  <a-popconfirm
                    title="确定要删除该节点吗？"
                    ok-text="确定"
                    cancel-text="取消"
                    @confirm="handleDeleteConfirm(key)"
                  >
                    <a><DeleteOutlined /> 删除</a>
                  </a-popconfirm>
                </div>
              </template>
              <span>{{ title }}</span>
            </a-popover>
          </template>
        </a-tree>
      </div>

      <AddNodeModal
        v-if="showAddNodeModal"
        :parentNodeTitle="currentNode.title"
        :parentNodeKey="currentNode.key"
        @confirm="handleAddNodeConfirm"
        @cancel="handleAddNodeCancel"
      />

      <EditNodeModal
        v-if="showEditNodeModal"
        :currentNode="currentEditNode"
        :parentNodes="treeData"
        :currentParentId="currentEditNodeParentId"
        @confirm="handleEditNodeConfirm"
        @cancel="handleEditNodeCancel"
      />
    </a-spin>
  </div>
</template>
<script lang="ts" setup>
import usePacketManagementState from './state'
import AddNodeModal from './components/add-node-modal.vue'
import EditNodeModal from './components/edit-node-modal.vue'
import {} from '@ant-design/icons-vue'
import { PlusCircleOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons-vue'
import Title from '@/components/title/title.vue'

const {
  treeData,
  expandedKeys,
  selectedKeys,
  showAddNodeModal,
  currentNode,
  handleAddNode,
  handleAddNodeConfirm,
  handleAddNodeCancel,
  showEditNodeModal,
  currentEditNode,
  currentEditNodeParentId,
  handleEdit,
  handleEditNodeConfirm,
  handleEditNodeCancel,
  handleDeleteConfirm,
  availableParentNodes,
  handleAddRootNode,
  loading,
} = usePacketManagementState()

// 默认展开一级节点
import { watch } from 'vue'
watch(
  treeData,
  (val) => {
    if (val && val.length > 0) {
      expandedKeys.value = val.map((item: any) => item.key)
    }
  },
  { immediate: true },
)
</script>

<style scoped>
.popover-buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.popover-buttons a {
  cursor: pointer;
  color: #1890ff;
}

.popover-buttons a:hover {
  color: #40a9ff;
}
.packet-management-container {
  background: white;
  height: 100%;
}
</style>
