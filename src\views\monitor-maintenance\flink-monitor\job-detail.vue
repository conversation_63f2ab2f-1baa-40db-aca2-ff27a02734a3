<template>
  <div class="page-full">
    <a-flex justify="space-between" style="padding-bottom: 10px">
      <div class="title mb-20px" text="20px #000000d9">job详情信息</div>
      <a-space :size="8">
        <a-button type="primary" @click="getList()"
          ><template #icon><ReloadOutlined /></template>刷新</a-button
        >
        <a-button type="primary" @click="getConfiList()">Flink任务管理</a-button>
      </a-space>
    </a-flex>

    <div class="page-main" ref="pageMain">
      <div class="list-content">
        <a-table
          ref="table"
          row-key="jid"
          :indentSize="40"
          :pagination="false"
          :data-source="jobDetailList"
          :columns="jobDetailColumns"
          :scroll="{ y: 'calc(100vh - 250px)' }"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'action'">
              <a-space>
                <a-button type="link" size="small" @click="handleStop(record)">停止</a-button>
                <a-dropdown>
                  <a class="ant-dropdown-link" @click="(e) => e.preventDefault()">
                    <span>更多</span>
                    <DownOutlined />
                  </a>
                  <template #overlay>
                    <a-menu>
                      <a-menu-item @click.stop="handleErrorInfo(record)">异常信息</a-menu-item>
                      <a-menu-item @click.stop="handleExecutionPlan(record)">执行计划</a-menu-item>
                      <a-menu-item @click.stop="handleCheckPointHistory(record)"
                        >CheckPoint历史</a-menu-item
                      >
                    </a-menu>
                  </template>
                </a-dropdown>
              </a-space>
            </template>
          </template>
        </a-table>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref } from 'vue'
import {
  getCheckPointHistoryById,
  getExecutionPlanById,
  getJobDetail,
  getJobErrorInfoById,
  stopJobById,
  getFlinkConfiList,
} from '@/api/services/monitor-maintenance/flink-monitor'
import { Modal } from 'ant-design-vue'
import { message, notification } from 'ant-design-vue'
import { logModal } from '@/utils/log-modal'
import { ReloadOutlined, DownOutlined } from '@ant-design/icons-vue'

const jobDetailList = ref<any[]>([])
const jobDetailColumns = [
  {
    show: true,
    title: 'ID',
    ellipsis: true,
    width: 180,
    dataIndex: 'jid',
    align: 'center',
    scopedSlots: { customRender: 'jid' },
  },
  {
    show: true,
    title: '名称',
    ellipsis: true,
    width: 180,
    dataIndex: 'name',
    align: 'center',
    scopedSlots: { customRender: 'name' },
  },
  {
    show: true,
    title: '状态',
    ellipsis: true,
    width: 180,
    dataIndex: 'state',
    align: 'center',
    scopedSlots: { customRender: 'state' },
  },
  {
    show: true,
    title: '操作',
    width: 240,
    align: 'center',
    fixed: 'right',
    dataIndex: 'action',
    scopedSlots: { customRender: 'action' },
  },
]

/* 获取列表数据 */
const getList = () => {
  getJobDetail({}).then((res) => {
    jobDetailList.value = res.data.obj
  })
}

// 停止
const handleStop = (record: any) => {
  Modal.confirm({
    title: '提示',
    content: `确定要停止【${record.name}】吗 ?`,
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      let params = {
        jobId: record.jid,
        targetDirr: '',
        drain: false,
      }
      stopJobById(params).then((res) => {
        if (res.data.code === 1) {
          message.success('停止成功')
        }
      })
    },
  })
}

// 异常信息
const handleErrorInfo = (record: any) => {
  getJobErrorInfoById({ jobId: record.jid }).then((res) => {
    if (res.data.code === -1) {
      message.error(res.data.message)
    } else {
      logModal(res.data.obj, '异常信息', true)
    }
  })
}

// 执行计划
const handleExecutionPlan = (record: any) => {
  getExecutionPlanById({ jobId: record.jid }).then((res) => {
    if (res.data.code === -1) {
      message.error(res.data.message)
    } else {
      logModal(res.data.obj, '执行计划', true)
    }
  })
}

// checkPoint历史
const handleCheckPointHistory = (record: any) => {
  getCheckPointHistoryById({ jobId: record.jid }).then((res) => {
    if (res.data.code === -1) {
      message.error(res.data.message)
    } else {
      logModal(res.data.obj, 'checkPoint历史', true)
    }
  })
}
const getConfiList = () => {
  getFlinkConfiList({}).then((res) => {
    if (res.data) {
      window.open(res.data, '_blank')
    } else {
      notification.warning({
        message: '打开Flink任务管理失败',
        description: '请在全局配置里添加Flink地址',
      })
    }
  })
}

onMounted(() => {
  getList()
})
</script>

<style lang="less" scoped></style>
