<template>
  <div class="set-config">
    <div class="nav-page-title">
      <div class="title-left">
        <p>团队和用户管理</p>
        <div class="desc">配置平台设计用户和部门的管理</div>
      </div>
    </div>
    <div class="set-config__card-list">
      <CardOption
        v-for="item in cardList"
        :key="item.title"
        :title="item.title"
        :desc="item.desc"
        :icon-color="item.iconColor"
        :icon="item.icon"
        @click="handleCardClick(item)"
        v-action:[item.actionName]
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import CardOption from './card-option.vue'
import user from '@/assets/icons/user.png'
import workFlow from '@/assets/icons/work-flow.svg'
import service from '@/assets/icons/service.svg'
import permission from '@/assets/icons/permission.png'

interface CardItem {
  title: string
  desc: string
  iconColor?: string
  icon?: string
  key: string
  path: string
  actionName?: string
}

const router = useRouter()

const cardList = ref<CardItem[]>([
  {
    title: '用户管理',
    desc: '配置平台设计用户和部门的管理',
    iconColor: '#ff7c50',
    icon: user,
    key: 'userManagementList',
    path: '/userManagementList',
    actionName: 'userManagementList',
  },
  {
    title: '组织管理',
    desc: '配置平台涉及工作流审核节点功能',
    iconColor: '#ff7c50',
    icon: workFlow,
    key: 'organizationalManagement',
    path: '/organizationalManagement',
    actionName: 'organizationalManagement',
  },
  {
    title: '职务管理',
    desc: '配置获取不同来源的数据源连接，提取数据资源',
    iconColor: '#5b8ff9',
    icon: service,
    key: 'positionManageList',
    path: '/positionManageList',
    actionName: 'positionManageList',
  },
  {
    title: '岗位管理',
    desc: '配置平台设计用户和部门的管理',
    iconColor: '#ff7c50',
    icon: permission,
    key: 'positionManage',
    path: '/positionManage',
    actionName: 'positionManage',
  },
  {
    title: '租户管理',
    desc: '配置平台设计用户和部门的管理',
    iconColor: '#ff7c50',
    icon: permission,
    key: 'TebantSelect',
    path: '/TebantSelect',
    actionName: 'TebantSelect',
  },
  {
    title: 'BU管理',
    desc: '配置平台设计用户和部门的管理',
    iconColor: '#ff7c50',
    icon: permission,
    key: 'BuSelect',
    path: '/BuSelect',
    actionName: 'BuSelect',
  },
])

function handleCardClick(item: CardItem) {
  router.push(item.path)
}
</script>

<style lang="less" scoped>
@import '@/assets/main.less';
@import './page.less';

.set-config {
  width: 100%;
  height: ~'calc(100vh - @{layout-header-height})';
  overflow-y: auto;
  // background: #fafbfc;
  box-sizing: border-box;
  &__title {
    font-size: 18px;
    font-weight: 600;
    color: #222;
    margin-bottom: 8px;
  }
  &__card-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 430px));
    gap: 16px 24px;
    width: 100%;
    margin: 24px auto;
  }
}
</style>
