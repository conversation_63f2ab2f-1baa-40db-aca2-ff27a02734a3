<script lang="ts" setup>
import type { ResDetailData } from './data-source'

const props = defineProps<{
  detailData: ResDetailData
}>()
</script>

<template>
  <div class="statistics-component">
    <div class="card-item">
      <div class="part-left">
        <div class="title">所有执行</div>
        <div class="count">{{ detailData.totalExecuteCnt }}次</div>
      </div>
      <div class="part-right"></div>
    </div>
    <div class="card-item">
      <div class="part-left">
        <div class="title">成功</div>
        <div class="count">{{ detailData.executeSuccessCnt }}次</div>
      </div>
      <div class="part-right">
        <a-progress
          class="success-progress"
          type="circle"
          :percent="detailData.successPct"
          :size="60"
          strokeColor="#4cca9f"
          :format="(percent: number) => `${percent}%`"
        />
      </div>
    </div>
    <!-- <div class="card-item">
      <div class="part-left">
        <div class="title">已终止</div>
        <div class="count">0</div>
      </div>
      <div class="part-right">
        <a-progress
          class="warning-progress"
          :format="(percent: number) => `${percent}%`"
          type="circle"
          :percent="30"
          :size="60"
          strokeColor="#febf47"
        />
      </div>
    </div> -->
    <div class="card-item">
      <div class="part-left">
        <div class="title">失败</div>
        <div class="count">{{ detailData.executeFailedCnt }}次</div>
      </div>
      <div class="part-right">
        <a-progress
          class="error-progress"
          type="circle"
          :format="(percent: number) => `${percent}%`"
          :percent="detailData.failedPct"
          :size="60"
          strokeColor="#f0462c"
        />
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.card-item {
  border-radius: 10px;
  border: 1px solid #0000001a;
  padding: 16px;
  display: flex;
  justify-content: space-between;
  .title {
    color: #757575;
    margin-bottom: 4px;
  }
  .count {
    font-size: 18px;
    color: #292929;
  }
}
.statistics-component {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24px;

  // @media (min-width: 901px) {
  //   grid-template-columns: repeat(4, 1fr);
  // }
  @media (max-width: 601px) {
    grid-template-columns: repeat(1, 1fr);
  }
}
.success-progress {
  /deep/ .ant-progress-inner .ant-progress-text {
    color: #4cca9f; // 你需要的颜色
  }
}
.warning-progress {
  /deep/ .ant-progress-inner .ant-progress-text {
    color: #febf47; // 你需要的颜色
  }
}
.error-progress {
  /deep/ .ant-progress-inner .ant-progress-text {
    color: #f0462c; // 你需要的颜色
  }
}
</style>
