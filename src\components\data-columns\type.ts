export type normalType = string | number | boolean | null | undefined
export type columnType = 'TEXT' | 'NUMBER' | 'DATE' | 'BOOLEAN' | 'ENUM'
export interface dataColumn {
  columnType: columnType
  title: string
  desc: string
  key: string
  options?: { label: string; value: normalType }[]
  [key: string]: any
}
export interface operateValue {
  column: dataColumn | null
  operateValue: operateTypeValue
  tableName?: string
}

export interface operateTypeValue {
  operator: string
  value: any
}
