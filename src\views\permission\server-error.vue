<template>
  <div class="content">
    <div class="img">
      <img src="../../assets/500.png" alt="" />
    </div>
    <!-- <p>您正在寻找的页面不存在</p> -->
    <div class="btn">
      <a-button @click="goHome"> 返回首页</a-button>
      <a-button @click="setState" style="margin-left: 20px"> 更改状态</a-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
const router = useRouter()
import { useRouter } from 'vue-router'
import { useAppStore } from '@/stores/app'
const appStore = useAppStore()
function goHome() {
  appStore.setServerType('200')
  router.push('/')
}

function setState() {
  appStore.setServerType('200')
}
</script>

<style scoped lang="less">
/* Styles for 404 page */
.content {
  position: relative;
  width: 100%;

  .img {
    width: 900px;
    margin: 0 auto;

    img {
      width: 100%;
      height: 100%;
    }
  }

  p {
    width: 100%;
    text-align: center;
    font-size: 30px;
    position: absolute;
    bottom: 160px;
  }

  .btn {
    position: absolute;
    bottom: 50px;
    z-index: 1;
    right: 0;
    left: 0;
    margin: 0 auto;
    text-align: center;
  }
}
</style>
