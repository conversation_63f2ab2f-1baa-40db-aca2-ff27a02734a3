<template>
  <div class="group">
    <a-popover trigger="click" v-model:open="open" @openChange="handleVisibleChange">
      <template #content>
        <div class="popover-content">
          <div class="filter-main">
            <a-input v-model:value="filterValue" placeholder="查找..." allow-clear>
              <template #prefix>
                <SearchOutlined />
              </template>
            </a-input>
            <a-divider style="margin: 10px 0"></a-divider>
            <div class="h-40vh overflow-y-auto hide-scrollbar">
              <div class="data-source" v-for="item in filterColumns" :key="item.tableName">
                <a-collapse
                  v-model:activeKey="activeKeys"
                  collapsible="header"
                  :expandIconPosition="'end'"
                  ghost
                >
                  <a-collapse-panel :key="item.tableName" :showArrow="false">
                    <template #header>
                      <div flex="~ gap-2" class="overflow-hidden" text="20px b700">
                        <TableOutlined />
                        <Tooltip :title="item.tableName">
                          <span class="ellipsis">{{ item.tableName }}</span>
                        </Tooltip>
                        <RightOutlined
                          text="14px"
                          :rotate="!activeKeys.includes(item.tableName) ? 0 : 90"
                        />
                      </div>
                    </template>
                    <data-columns :data="dataColumnData(item)" @handleClick="handleClick($event)">
                      <template #extal="{ item }">
                        <div class="extal">
                          <a-tooltip
                            placement="top"
                            :title="'按' + (item.extalName || '月')"
                            v-if="item.columnType === 'DATE'"
                          >
                            <a-dropdown trigger="click" @click.stop>
                              <div class="ant-dropdown-link" @click.prevent>
                                按{{ item.extalName || '月' }}
                                <DownOutlined />
                              </div>
                              <template #overlay>
                                <a-menu @click="selectMenu($event, item)">
                                  <a-menu-item key="分">
                                    <div href="javascript:;">分</div>
                                  </a-menu-item>
                                  <a-menu-item key="小时">
                                    <div href="javascript:;">小时</div>
                                  </a-menu-item>
                                  <a-menu-item key="天">
                                    <div href="javascript:;">天</div>
                                  </a-menu-item>
                                  <a-menu-item key="周">
                                    <div href="javascript:;">周</div>
                                  </a-menu-item>
                                  <a-menu-item key="月">
                                    <div href="javascript:;">月</div>
                                  </a-menu-item>
                                  <a-menu-item key="年">
                                    <div href="javascript:;">年</div>
                                  </a-menu-item>
                                </a-menu>
                              </template>
                            </a-dropdown>
                          </a-tooltip>
                        </div>
                      </template>
                    </data-columns>
                  </a-collapse-panel>
                </a-collapse>
              </div>
            </div>
          </div>
        </div>
      </template>
      <div class="group-action">
        <slot>
          <div class="group-action-trigger">选择分组的列</div>
        </slot>
      </div>
    </a-popover>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { SearchOutlined, TableOutlined, DownOutlined, RightOutlined } from '@ant-design/icons-vue'
import type { dataColumn } from '../data-columns/type'
import { JoinTableColumnsKey, type JoinTableColumns } from '@/views/indicator-config/provide-keys'
import type { ServerFieldItem } from '@/api/services/indicator/type'
import { Tooltip } from 'ant-design-vue'
const props = defineProps<{
  tableName?: string
  columns?: dataColumn[]
  endIndex?: number
}>()
const emits = defineEmits(['handleClick'])

const open = ref(false)

const handleVisibleChange = (status: boolean) => {
  open.value = status
}

const filterValue = ref('')
const activeKeys = ref<string[]>([])
const joinTableColumns = inject<Ref<JoinTableColumns>>(JoinTableColumnsKey)
const filterColumns = computed<JoinTableColumns>(() => {
  if (!joinTableColumns?.value) return []
  let newArr = JSON.parse(JSON.stringify(joinTableColumns.value))
  if (typeof props.endIndex === 'number') {
    console.log('%c [ 表长度 ]: ', 'color: #007acc;', props.endIndex)
    newArr = newArr.slice(0, props.endIndex)
  }
  return newArr
})
const dataColumnData = (item: any) => {
  return item.fields.filter(
    (field: ServerFieldItem) =>
      !filterValue.value || field.title.toLowerCase().includes(filterValue.value.toLowerCase()),
  )
}
function selectMenu(e: any, item: dataColumn) {
  item.extalName = e.key
  handleClick(item)
}

function handleClick(e: dataColumn | ServerFieldItem) {
  open.value = false
  emits('handleClick', e)
}
watch(filterValue, (newValue) => {
  if (!newValue) {
    activeKeys.value = []
    return
  }
  const matchedTables =
    filterColumns.value?.filter((table) =>
      table.fields.some((field) => field.title.toLowerCase().includes(newValue.toLowerCase())),
    ) ?? []
  activeKeys.value = matchedTables.map((table) => table.tableName)
})
</script>

<style lang="less" scoped>
:deep(.ant-collapse-header-text) {
  max-width: 100%;
}
.popover-content {
  max-width: 500px;
  width: 300px;
}
.group {
  &-action {
    display: inline-flex;
    font-weight: bold;

    border-radius: 6px;
    cursor: pointer;
    pointer-events: auto;
    -webkit-box-align: stretch;
    align-items: stretch;
    transition: border 300ms linear;
    :deep(&-trigger) {
      color: rgb(136, 191, 77);
      display: flex;
      border: 2px solid rgba(136, 191, 77, 0.25);
      -webkit-box-align: center;
      align-items: center;
      padding: 10px;
      background-color: transparent;
      border-radius: 6px;
      transition: background 300ms linear 0s;
    }
  }
}
.data-source {
  &-title {
    display: flex;
    align-items: center;
    padding: 10px 0;
    font-size: 20px;
    font-weight: bold;
  }
}
</style>
