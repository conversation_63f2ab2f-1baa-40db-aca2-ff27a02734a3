<template>
  <div class="column" v-if="column">
    <div class="column-header">
      <div class="column-title" @click="emits('back')">
        <LeftOutlined />
        <span style="margin-top: -3px">{{ column.title }}</span>
      </div>
      <div class="column-extra">
        <operate
          v-model="operateValue.operator"
          :type="column.columnType"
          v-if="!noOperate.includes(column.columnType)"
          @selected="handleSelected"
        ></operate>
      </div>
    </div>
    <div class="column-content">
      <component
        :is="component"
        v-model:modelValue="operateValue.value"
        :data="column"
        v-if="component"
      ></component>
    </div>
    <div class="column-footer">
      <a-button type="primary" @click="handleAdd" :disabled="disabled">
        {{ data?.operator ? '更新' : '添加' }}至筛选器
      </a-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import { LeftOutlined } from '@ant-design/icons-vue'
import type { dataColumn, operateTypeValue } from './type'
import Operate from './components/operate.vue'
import inputTag from './components/input-tag.vue'
import inputNumber from './components/input-number.vue'
import inputBetween from './components/input-between.vue'
import radioBoolean from './components/radio-boolean.vue'
import checkboxEnum from './components/checkbox-enum.vue'
import datePick from './components/date-pick.vue'

const emits = defineEmits(['back', 'close', 'submit'])
const props = defineProps<{
  column: dataColumn | null
  data: operateTypeValue
}>()
const noOperate = ['DATE', 'BOOLEAN']
const specilType = [...noOperate, 'ENUM']

const componentMap: any = {
  '=': inputTag,
  '!=': inputTag,
  '>': inputNumber,
  '<': inputNumber,
  BETWEEN: inputBetween,
  contains: inputTag,
  not_contains: inputTag,
  start_with: inputTag,
  end_with: inputTag,
  '>=': inputNumber,
  '<=': inputNumber,
  isnull: '',
  notnull: '',
  DATE: datePick,
  BOOLEAN: radioBoolean,
  ENUM: checkboxEnum,
}

const operateValue = ref<operateTypeValue>({
  operator: '',
  value: undefined,
})

const component = computed(() => {
  const { operator } = operateValue.value
  let type = operator
  if (props.column && specilType.includes(props.column.columnType)) {
    if (['=', '!='].includes(operator) || !['ENUM'].includes(props.column.columnType)) {
      type = props.column.columnType
    }
  }
  return componentMap[type]
})

const disabled = computed(() => {
  if (!props.column) {
    return true
  }
  if (['IS_NULL', 'NOT_NULL'].includes(operateValue.value.operator)) return false
  if ([null, undefined].includes(operateValue.value.value)) {
    return true
  }
  return false
})

watch(
  () => props.column,
  () => {
    operateValue.value.operator = ''
    operateValue.value.value = undefined
  },
)

watch(
  () => props.data,
  () => {
    operateValue.value = {
      operator: props.data?.operator || '',
      value: props.data?.value || undefined,
    }
  },
  {
    deep: true,
    immediate: true,
  },
)

function handleSelected(value: string) {
  operateValue.value.operator = value
  if (['IS_NULL', 'NOT_NULL'].includes(value)) {
    operateValue.value.value = value
  } else {
    operateValue.value.value = value === 'BETWEEN' ? [undefined, undefined] : undefined
  }
}

function handleAdd() {
  const copyVaule = operateValue.value
  const value = copyVaule.value instanceof Array ? copyVaule.value.join(',') : copyVaule.value
  emits('submit', {
    column: { ...props.column },
    operateValue: { ...copyVaule, value: value ?? '' },
  })
}
</script>

<style lang="less" scoped>
.column {
  &-title {
    cursor: pointer;
    display: flex;
    align-items: center;
    font-size: 16px;
    font-weight: 500;
  }
  &-title:hover {
    span {
      color: rgba(80, 158, 227, 0.88);
    }
  }
  &-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  &-content {
    padding: 10px 0;
  }
  &-footer {
    border-top: 1px solid #f0f0f0;
    padding: 10px 0;
    text-align: right;
  }
}
</style>
