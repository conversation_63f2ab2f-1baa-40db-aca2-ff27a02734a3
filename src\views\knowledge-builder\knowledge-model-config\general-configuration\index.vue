<template>
  <div>
    <h1>图存储配置：</h1>
    <a-spin :spinning="loading" />
    <a-row class="layout-container">
      <a-col :span="6">
        <div class="input-title-layout">database</div>
      </a-col>
      <a-col :span="6">
        <div class="input-title-layout">password</div>
      </a-col>
      <a-col :span="6"> <div class="input-title-layout">uri</div></a-col>
      <a-col :span="6"> user</a-col>
    </a-row>
    <a-row class="edit-layout-container" :wrap="false">
      <a-col :span="6"
        ><a-input
          style="width: 90%"
          v-model:value="formState.graph_store.database"
          :bordered="isEdit"
          placeholder="Borderless"
          :disabled="!isEdit"
      /></a-col>
      <a-col :span="6"
        ><a-input
          style="width: 90%"
          v-model:value="formState.graph_store.password"
          :bordered="isEdit"
          placeholder="Borderless"
          :disabled="!isEdit"
      /></a-col>
      <a-col :span="6"
        ><a-input
          style="width: 90%"
          v-model:value="formState.graph_store.uri"
          :bordered="isEdit"
          placeholder="Borderless"
          :disabled="!isEdit"
      /></a-col>
      <a-col :span="6"
        ><a-input
          style="width: 90%"
          v-model:value="formState.graph_store.user"
          :bordered="isEdit"
          placeholder="Borderless"
          :disabled="!isEdit"
      /></a-col>
    </a-row>
    <h1 style="margin-top: 40px">向量配置：</h1>

    <a-row class="layout-container">
      <a-col :span="6"> <div class="input-title-layout">type</div></a-col>
      <a-col :span="6"><div class="input-title-layout">model</div></a-col>
      <a-col :span="6"><div class="input-title-layout">base_url</div> </a-col>
      <a-col :span="6">api_key</a-col>
    </a-row>

    <a-row class="edit-layout-container" :wrap="false">
      <a-col :span="6"
        ><a-input
          style="width: 90%"
          v-model:value="formState.vectorizer.type"
          :bordered="isEdit"
          placeholder="Borderless"
          :disabled="!isEdit"
      /></a-col>
      <a-col :span="6"
        ><a-input
          style="width: 90%"
          v-model:value="formState.vectorizer.model"
          :bordered="isEdit"
          placeholder="Borderless"
          :disabled="!isEdit"
      /></a-col>
      <a-col :span="6"
        ><a-input
          style="width: 90%"
          v-model:value="formState.vectorizer.base_url"
          :bordered="isEdit"
          placeholder="Borderless"
          :disabled="!isEdit"
      /></a-col>
      <a-col :span="6"
        ><a-input
          style="width: 90%"
          v-model:value="formState.vectorizer.api_key"
          :bordered="isEdit"
          placeholder="Borderless"
          :disabled="!isEdit"
      /></a-col>
    </a-row>
    <h1 style="margin-top: 40px">提示词中英文配置：</h1>

    <a-row class="layout-container">
      <a-col :span="12"><div class="input-title-layout">biz_scene</div></a-col>
      <a-col :span="12">language</a-col>
    </a-row>

    <a-row class="edit-layout-container" :wrap="false">
      <a-col :span="12"
        ><a-input
          style="width: 90%"
          v-model:value="formState.prompt.biz_scene"
          :bordered="isEdit"
          placeholder="Borderless"
          :disabled="!isEdit"
      /></a-col>
      <a-col :span="12"
        ><a-input
          style="width: 90%"
          v-model:value="formState.prompt.language"
          :bordered="isEdit"
          placeholder="Borderless"
          :disabled="!isEdit"
      /></a-col>
    </a-row>

    <a-button
      v-if="!isEdit"
      type="primary"
      @click="isEdit = !isEdit"
      style="margin-top: 60px; margin-bottom: 20px"
    >
      编辑
    </a-button>
    <div v-else style="margin-top: 60px; margin-bottom: 20px">
      <a-button type="primary" @click="saveConfig"> 保存 </a-button>
      <a-button style="margin-left: 20px" @click="isEdit = !isEdit"> 取消 </a-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { updateGloablConfig } from '@/api/services/knowledge'
import type { KnowledgeBaseConfig } from '@/api/type'
import { ref, watch } from 'vue'
import { message } from 'ant-design-vue'
const emits = defineEmits(['modalAddOk'])
const props = defineProps<{ data: KnowledgeBaseConfig }>()
const loading = ref(false)
watch(
  () => props.data,
  (newVal, oldVal) => {
    console.log('%c 🥛 newVal: ', 'font-size:12px;background-color: #6EC1C2;color:#fff;', newVal)
    console.log('%c 🥛 oldVal: ', 'font-size:12px;background-color: #6EC1C2;color:#fff;', oldVal)
    formState.value = props.data
  },
)

const isEdit = ref(false)
const formState = ref<KnowledgeBaseConfig>({
  graph_store: {
    database: '',
    password: '',
    uri: '',
    user: '',
  },
  vectorizer: {
    type: '',
    model: '',
    base_url: '',
    api_key: '',
  },
  prompt: {
    biz_scene: '',
    language: '',
  },
  llm_select: [],
  llm: {},
})

const saveConfig = async () => {
  loading.value = true
  const res = await updateGloablConfig(
    { id: 2 },
    {
      id: 2,
      configName: 'Global Configuration',
      configId: 'KAG_CONFIG',
      version: '1',
      config: JSON.stringify(formState.value),
    },
  )

  if (res === 1) {
    loading.value = false
    message.success('保存成功')
    isEdit.value = false
    emits('modalAddOk')
  } else {
    loading.value = false
    message.error('保存失败')
  }
}
</script>

<style lang="less" scoped>
.layout-container {
  background-color: #fafafa;
  height: 40px;
  align-items: center;
  padding-left: 20px;
  margin-right: 10px;
  border: 1px solid #f5f5f5;
}
.edit-layout-container {
  height: 40px;
  align-items: center;
  padding: 10px;
  margin-right: 10px;
  background-color: #ffffff;
}
.input-title-layout {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-right: 25px;
  border-right: 1px solid #999999;
}
::v-deep .ant-input[disabled] {
  color: #333333; /* 自定义禁用时的字体颜色 */
}
::v-deep .custom-input.ant-input {
  border-radius: 4px;
}

/* 方式 2：直接修改所有输入框 */
::v-deep .ant-input {
  border-radius: 4px !important;
}
</style>
