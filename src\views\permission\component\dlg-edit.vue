<template>
  <a-modal
    v-model:open="open"
    :title="actionMap[actionType]"
    @ok="handleOk"
    destroyOnClose
    width="700px"
    :confirmLoading="loading"
  >
    <a-form
      ref="formRef"
      :model="formState"
      name="basic"
      :label-col="{ span: 5 }"
      :wrapper-col="{ span: 16 }"
    >
      <a-form-item
        label="用户"
        name="userIdList"
        :rules="[{ required: true, message: '请选择用户' }]"
      >
        <a-select
          mode="multiple"
          style="width: 100%"
          placeholder="请选择用户"
          :options="userList"
          :fieldNames="{ label: 'userName', value: 'userId' }"
          v-model:value="formState.userIdList"
        ></a-select>
      </a-form-item>
      <a-form-item
        label="列配置"
        name="columns"
        :rules="[{ required: true, message: '列权限配置不能为空' }]"
      >
        <ColumnSelector
          v-model="formState.columns"
          :table-id="tableId"
          @select-columns="handleSelectColumns"
          v-if="true"
        />
      </a-form-item>
      <a-form-item label="行配置" name="configValue">
        <a-space
          v-for="(user, index) in formState.configValue"
          :key="user.id"
          style="display: flex"
          align="baseline"
        >
          <a-form-item style="width: 120px" :name="['configValue', index, 'columnName']">
            <a-select
              placeholder="columnName"
              :options="paramList"
              :fieldNames="{ label: 'columnName', value: 'columnName' }"
              v-model:value="user.columnName"
            ></a-select>
          </a-form-item>
          <a-form-item style="width: 80px" :name="['configValue', index, 'operator']">
            <a-select
              placeholder="operator"
              :options="[
                { label: '大于', value: '>' },
                { label: '小于', value: '<' },
                { label: '等于', value: '=' },
                { label: '不等于', value: '!=' },
              ]"
              v-model:value="user.operator"
            ></a-select>
          </a-form-item>
          <a-form-item :name="['configValue', index, 'value']">
            <a-input v-model:value="user.value" placeholder="value" />
          </a-form-item>
          <MinusCircleOutlined @click="removeUser(user)" />
        </a-space>
        <a-button type="dashed" block @click="addUser">
          <PlusOutlined />
          添加行配置
        </a-button>
      </a-form-item>
    </a-form>
  </a-modal>
</template>
<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { ActionType, type AuthItem, type ParamItem, type UserItem } from './datas'
import { reqAddDataList, reqEditDataList, reqGetParamsList, reqGetUserList } from './api'
import { useRoute } from 'vue-router'
import { MinusCircleOutlined, PlusOutlined } from '@ant-design/icons-vue'
import { type FormInstance, Form, message } from 'ant-design-vue'
import ColumnSelector from '../../data-asset-management/components/column-selector.vue'

interface FormState {
  userIdList: string | string[]
  columns: string[]
  configValue: ConfigValueType[]
}

const route = useRoute()
const tableId = route.params.authId as string

const props = defineProps<{
  actionType: ActionType
  getAuthList: () => void
}>()

const actionMap = {
  [ActionType.ADD]: '新增用户',
  [ActionType.EDIT]: '编辑用户',
}

const useForm = Form.useForm
const formRef = ref<FormInstance>()
const open = ref<boolean>(false)
const userList = ref<UserItem[]>([])
const paramList = ref<ParamItem[]>([])
const currentAuthItem = ref<AuthItem | null>(null)
const formState = reactive<FormState>({
  userIdList: [],
  columns: [],
  configValue: [
    {
      columnName: undefined,
      operator: undefined,
      value: undefined,
      id: Date.now(),
    },
  ],
})
const selectedColumns = ref<any[]>([])
const loading = ref<boolean>(false)

onMounted(() => {
  getParamList()
  getUserList()
})

const getParamList = async () => {
  const res = await reqGetParamsList(tableId)
  paramList.value = res.data
  console.log('%c [ res ]-26', 'font-size:13px; background:#318a15; color:#75ce59;', res)
}
const getUserList = async () => {
  const res = await reqGetUserList(tableId)
  userList.value = res.data.records
  console.log('%c [ res ]-26', 'font-size:13px; background:#318a15; color:#75ce59;', res)
}

const showModal = (data?: AuthItem) => {
  if (data) {
    currentAuthItem.value = data
    console.log('%c [ data ]-168', 'font-size:13px; background:#9c2617; color:#e06a5b;', data)
    formState.userIdList = [data.userId]
    formState.columns = data.columns.split(',')
    console.log('sss', JSON.parse(data.configValue))
    formState.configValue = JSON.parse(data.configValue)
  } else {
    formState.userIdList = []
    formState.columns = []
    formState.configValue = []
    formRef.value?.resetFields()
  }
  open.value = true
}

const handleOk = async () => {
  const values = await formRef.value?.validateFields()
  if (!values) {
    return
  }
  loading.value = true
  try {
    const userIdList = toRaw(values.userIdList).map((item: string) => {
      const currentItem = userList.value.find((user) => user.userId === item)
      return {
        userId: currentItem?.userId,
        userName: currentItem?.userName,
      }
    })
    console.log('Form values:', values, userIdList)
    let configValue: any = JSON.stringify(
      values.configValue.map((item: ConfigValueType) => ({
        columnName: item.columnName,
        operator: item.operator,
        value: item.value,
      })),
    )
    const params = {
      tableId,
      userIdList,
      columns: toRaw(values.columns).join(),
      configValue: configValue,
      permissionId: currentAuthItem.value?.permissionId,
    }
    // If editing, we need to include the ID of the record being edited
    if (props.actionType === ActionType.EDIT) {
      await reqEditDataList(params)
    } else {
      await reqAddDataList(params)
    }
    await props.getAuthList()
    open.value = false
    message.success(`${actionMap[props.actionType]}成功`)
    formRef.value?.resetFields()
  } catch (error) {
    console.log('操作失败', error)
  } finally {
    loading.value = false
  }
}

interface ConfigValueType {
  columnName: string | undefined
  operator: string | undefined
  value: string | undefined
  id: number
}
const removeUser = (item: ConfigValueType) => {
  const index = formState.configValue.indexOf(item)
  if (index !== -1) {
    formState.configValue.splice(index, 1)
  }
}
const addUser = () => {
  formState.configValue.push({
    columnName: undefined,
    operator: undefined,
    value: undefined,
    id: Date.now(),
  })
}

const handleSelectColumns = (columns: string[]) => {
  selectedColumns.value = columns
}

defineExpose({
  showModal,
})
</script>
