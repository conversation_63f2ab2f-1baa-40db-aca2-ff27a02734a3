import { useUserStore } from '@/stores/user'
import { request } from '../services/axios-instances'
import { DBConnection, DbTableColumn, NodeType } from '../models/sql-tree'
import type { SqlDataNode } from '@/components/sql-tree-comp/types'
import { DbTableInfo, ColumnData, type TableFieldData } from '../models/sql-editing'
import type { DatabaseDesign } from '@/views/sql-editing/db-designer/types'
import { convertToDatabaseDesign } from '@/views/sql-editing/db-designer/transformer'

export default {
  /**
   * 获取 SQL编辑 树目录-数据连接列表
   */
  async getDBConnections(): Promise<SqlDataNode[]> {
    const ownerId = useUserStore().userInfo.userName
    const res = await request.post('/api/sodataFlink/sqlEditor/PageListScameInfo', {
      ownerId,
    })
    const arr: DBConnection[] = res.map((item: any) => DBConnection.fromJson(item))
    const nodes: SqlDataNode[] = arr.map((item) => ({
      key: item.id,
      title: item.name,
      nodeType: 'connection',
      dbConnection: item,
    }))
    return nodes
  },

  /**
   * 获取 SQL编辑 树目录-数据库实例列表
   * @param id 数据连接ID
   * @returns 数据库实例列表
   */
  async getDbInstances(id: string | number): Promise<SqlDataNode[]> {
    const res = await request.post('/api/sodataFlink/AISQL/dbNameFun', {
      json: JSON.stringify({ id: id }),
    })
    const nodes: SqlDataNode[] = res.obj.map((item: any) => ({
      key: item.key,
      title: item.title,
      nodeType: 'database',
      dbTypeName: item.dbTypMap,
      dbName: item.title,
    }))
    return nodes
  },

  /**
   * 获取 SQL编辑 树目录-数据库表列表
   * @param data 请求参数
   * @param data.id 数据连接ID
   * @param data.dbName 数据库名称
   * @returns 数据库表列表
   */
  async getDbTables(data: {
    id: string | number
    dbName?: string
    dbSchema?: string
  }): Promise<SqlDataNode[]> {
    const res = await request.post('/api/sodataFlink/AISQL/dbnameTableParmaTranFun', {
      json: JSON.stringify(data),
    })
    const arr: SqlDataNode[] = res.obj.map((item: any) => {
      const nodeType = item.scopedSlots.icon as string
      const result: SqlDataNode = {
        key: item.key,
        title: item.title,
        nodeType: nodeType,
      }
      if (nodeType === NodeType.schema.value) {
        result.dbSchema = item.title
      }
      return result
    })
    return arr
  },

  /**
   * 获取 SQL编辑 树目录-数据库表字段列表
   * @param data 请求参数
   * @param data.id 数据连接ID
   * @param data.dbName 数据库名称
   * @param data.tableName 数据库表名称
   * @returns 数据库表字段列表
   */
  async getDbTableColumns(data: {
    id: string
    dbName: string
    tableName: string
  }): Promise<DbTableColumn[]> {
    const res = await request.post('/api/sodataFlink/metadata/queryTableColumnInfoFun', {
      json: JSON.stringify(data),
    })
    const arr = res.obj.map((item: any) => DbTableColumn.fromJson(item))
    return arr
  },

  /**
   * 获取 SQL编辑 属性-数据库表信息
   * @param data 请求参数
   * @param data.id 数据连接ID
   * @param data.dbSchema 数据库模式
   * @param data.dbType 数据库类型
   * @param data.dbName 数据库名称
   * @param data.type 节点类型
   * @returns
   */
  async getDbTablesInfo(data: {
    id?: string | number
    dbSchema?: string
    dbType?: number
    dbName?: string
    type?: string
  }): Promise<DbTableInfo[]> {
    const res = await request.post(`/api/sodataFlink/sqlEditor/queryTabledescFun`, data)
    const arr = res.obj.map((item: any) => DbTableInfo.fromJson(item))
    return arr
  },

  /**
   * 获取 SQL编辑 属性-数据库表数据, 全量返回, 不分页
   * @description 字段是动态的，不同表可能有不同的字段
   * @note 该方法可能会返回大量数据，使用时请注意性能和内存消耗
   * @param data 请求参数
   * @param data.id 数据连接ID
   * @param data.dbName 数据库名称
   * @param data.tableName 数据库表名称
   * @param data.dbSchema 数据库模式
   */
  async getDbTableData(data: {
    id?: string | number
    dbName?: string
    tableName?: string
    dbSchema?: string
  }): Promise<Record<string, any>[]> {
    const res = await request.post(`/api/sodataFlink/sqlEditor/queryDefaultableData`, {
      dbName: data.dbName,
      dbSchema: data.dbSchema,
      id: data.id,
      tabName: data.tableName,
    })
    const parsedData = JSON.parse(res.obj)
    return parsedData.data
  },
  /**
   * 查看数据调用接口
   * @param data 请求参数
   * @param data.id 数据连接ID
   * @param data.dbName 数据库名称
   * @param data.tableName 数据库表名称
   * @param data.dbSchema 数据库模式
   * @description 获取 数据表 字段数据类型和主键信息
   * @note 该方法用于获取表字段的类型和主键信息，适用于动态表结构
   */
  async getDbTableDataColumns(data: {
    id?: string | number
    dbName?: string
    tableName?: string
    dbSchema?: string
  }): Promise<ColumnData[]> {
    const res = await request.post(`/api/sodataFlink/sqlEditor/mysqlColumnTypePkFun`, {
      dbName: data.dbName,
      dbSchema: data.dbSchema,
      id: data.id,
      tabName: data.tableName,
    })
    const arr = res.obj.column.map((item: any) => ColumnData.fromJson(item))
    return arr
  },
  /**
   * 获取单个表的详情
   * @param data 请求参数
   * @param data.id 数据连接ID
   * @param data.dbName 数据库名称
   * @param data.tableName 数据库表名称
   * @param data.dbSchema 数据库模式
   * @param data.dbType 数据库类型
   * @param data.nodeType 节点类型
   */
  async getTableDetail(data: {
    id?: string | number
    dbName?: string
    tableName?: string
    dbSchema?: string
    dbType?: number
    nodeType?: string
  }): Promise<Record<string, { key: string; val: any }>> {
    const res = await request.post('/api/sodataFlink/sqlEditor/querySingleTabledescFun', {
      id: data.id,
      tabName: data.tableName,
      dbName: data.dbName,
      dbSchema: data.dbSchema,
      dbType: data.dbType,
      type: data.nodeType,
      lang: 'zh-CN',
    })
    return res.obj
  },
  /**
   * 获取数据库表和字段信息和表关联信息
   * @param params 请求参数
   * @param params.databaseName 数据库名称
   * @param params.id 数据连接ID
   * @param params.schemaName 数据库模式
   * @returns
   */
  async queryDBTablesAndColumns(params: {
    databaseName?: string
    id: string | number
    schemaName?: string
  }) {
    const res = await request.get(`/api/sodataFlink/SchemaInfo/getDbAllColumnForeignKeyInfo`, {
      params: params,
    })
    let result: DatabaseDesign | null = null
    if (res.obj) {
      result = convertToDatabaseDesign(res.obj)
    }
    return result
  },
  /**
   * 复制表
   * @param data 请求参数
   * @param data.id 数据连接ID
   * @param data.dbName 数据库名称
   * @param data.schema 数据库模式
   * @param data.srcName 源表名称
   * @param data.desName 目标表名称
   * @param data.isCopyData 是否复制数据
   */
  async copyTable(data: {
    id: string | number
    dbName?: string
    schema?: string
    srcName: string
    desName: string
    isCopyData: boolean
  }): Promise<boolean> {
    const res = await request.post(`/api/sodataFlink/sqlEditor/copyTable`, {
      tabInfo: data,
    })
    return res?.code === 1
  },
  async renameTable(data: {
    id: string | number
    dbName?: string
    schema?: string
    srcName: string
    desName: string
  }) {
    const res = await request.post(`/api/sodataFlink/sqlEditor/renameTable`, {
      tabInfo: data,
    })
    return res?.code === 1
  },

  async getTableFieldData(data: {
    id: string | number
    dbName?: string
    dbSchema?: string
    tableName: string
    dbType: number
  }):Promise<TableFieldData[]> {
    const res = await request.post(`/api/sodataFlink/sqlEditor/queryColumnInfoFun`, {
      id: data.id,
      dbName: data.dbName,
      dbType: data.dbType,
      schema: data.dbSchema,
      tabName: data.tableName,
    })
    return res.obj.fieldData
  },
}
