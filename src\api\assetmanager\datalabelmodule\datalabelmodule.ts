import service from '@/api'
import { MOCK_DATA } from './datalabelmodule-mock'

/**
 *  - DLM001-新增数据标签
 * @param {boolean} categoryId - 分类ID
 * @param {string} labelName - 标签名称
 * @param {string} description - 标签描述
 * @param {string} opUser -
 * @param {string} opUserName -
 * @returns {any} - 返回一个解析为响应数据的Promise
 */

export async function addDataAssetsLabel(
  data: {
    categoryId?: boolean
    labelName: string
    description?: string
  },
  headers: {
    opUser?: string
    opUserName?: string
  },
): Promise<any> {
  return service(
    {
      method: 'POST',
      url: '/assetManager/dataLabelModule/addDataAssetsLabel',
      data,
      headers,
    },
    MOCK_DATA.addDataAssetsLabel,
  )
}

/**
 *  - DLM002-修改数据标签
 * @param {boolean} labelId - 标签ID
 * @param {boolean} categoryId - 分类ID
 * @param {string} labelName - 标签名称
 * @param {string} description - 标签描述
 * @param {string} opUser -
 * @param {string} opUserName -
 * @returns {any} - 返回一个解析为响应数据的Promise
 */

export async function updateDataAssetsLabel(
  data: {
    labelId: boolean
    categoryId?: boolean
    labelName?: string
    description?: string
  },
  headers: {
    opUser?: string
    opUserName?: string
  },
): Promise<any> {
  return service(
    {
      method: 'POST',
      url: '/assetManager/dataLabelModule/updateDataAssetsLabel',
      data,
      headers,
    },
    MOCK_DATA.updateDataAssetsLabel,
  )
}

/**
 *  - DLM003-删除数据标签
 * @param {boolean} labelId -
 * @param {string} opUser -
 * @param {string} opUserName -
 * @returns {any} - 返回一个解析为响应数据的Promise
 */

export async function delDataAssetsLabel(
  data: {
    labelId: boolean
  },
  headers: {
    opUser?: string
    opUserName?: string
  },
): Promise<any> {
  return service(
    {
      method: 'POST',
      url: '/assetManager/dataLabelModule/delDataAssetsLabel',
      data,
      headers,
    },
    MOCK_DATA.delDataAssetsLabel,
  )
}

/**
 *  - DLM004-分页查询数据标签
 * @param {number} pageIndex - undefined
 * @param {number} pageSize - undefined
 * @param {boolean} categoryId - 分类ID
 * @param {string} labelName - 标签名称
 * @param {string} description - 标签描述
 * @param {string} opUser - undefined
 * @param {string} opUserName - undefined
 * @returns {any} - 返回一个解析为响应数据的Promise
 */

export async function getPageDataAssetsLabel(
  data: {
    pageIndex: number
    pageSize: number
    categoryId?: boolean
    labelName?: string
    description?: string
  },
  headers: {
    opUser?: string
    opUserName?: string
  },
): Promise<{
  code: string
  msg: string
  data: Array<{
    labelId: number
    categoryId: number
    labelName: string
    description: string
    createdTime: string
    createdUser: string
    updatedTime: string
    updatedUser: string
  }>
}> {
  return service(
    {
      method: 'GET',
      url: '/assetManager/dataLabelModule/getPageDataAssetsLabel',
      params: data,
      headers,
    },
    MOCK_DATA.getPageDataAssetsLabel,
  )
}

/**
 *  - DLM005-查询数据标签
 * @param {boolean} categoryId - 分类ID
 * @param {string} labelName - 标签名称
 * @param {boolean} labelId - 自增主键
 * @param {string} opUser - undefined
 * @param {string} opUserName - undefined
 * @returns {any} - 返回一个解析为响应数据的Promise
 */

export async function getDataAssetsLabel(
  data: {
    categoryId?: boolean
    labelName?: string
    labelId?: boolean
  },
  headers: {
    opUser?: string
    opUserName?: string
  },
): Promise<{
  code: string
  msg: string
  data: Array<{
    labelId: number
    categoryId: number
    labelName: string
    description: string
    createdTime: string
    createdUser: string
    updatedTime: string
    updatedUser: string
  }>
}> {
  return service(
    {
      method: 'GET',
      url: '/assetManager/dataLabelModule/getDataAssetsLabel',
      params: data,
      headers,
    },
    MOCK_DATA.getDataAssetsLabel,
  )
}

/**
 *  - DLM006-查询数据标签树
 * @param {string} opUser - undefined
 * @param {string} opUserName - undefined
 * @returns {any} - 返回一个解析为响应数据的Promise
 */

export async function getDataAssetsLabelTree(
  data: any,
  headers: {
    opUser?: string
    opUserName?: string
  },
): Promise<{
  code: string
  msg: string
  data: Array<{
    categoryName: number
    categoryId: number
    children: Array<{ labelName: string; labelId: string; description: string }>
  }>
}> {
  return service(
    {
      method: 'GET',
      url: '/assetManager/dataLabelModule/getDataAssetsLabelTree',
      params: data,
      headers,
    },
    MOCK_DATA.getDataAssetsLabelTree,
  )
}
