import { ref } from 'vue'
import { defineStore } from 'pinia'
import type { generationType } from '@/components/page/generation'
import { useLocalStorage } from '@vueuse/core'

interface menu {
  menuId: string
  menuName: string
  menuUrl: string
  path?: string
}

export const useAppStore = defineStore('app', () => {
  const appLoading = ref(false) // 全局 Loading 状态
  const serverType = ref('200') // 服务状态
  const generationType = ref({}) // 代码生成弹窗type
  const menuTabs = useLocalStorage<menu[]>('menuTabs', [])
  // 不缓存列表
  const excludeCache = ref<string[]>([])
  // 是否展示路由 用来处理刷新全部
  const isRouterShow = ref(true)

  const setServerType = (type: string) => {
    serverType.value = type
  }
  const setGenerationType = (type: generationType) => {
    generationType.value = type
  }

  const setMenuTabs = (value: any) => {
    console.log('🚀 ~ setMenuTabs ~ value:', value)
    const index = menuTabs.value.findIndex(
      (item: any) => item.menuUrl === value.menuUrl || item.menuId === value.menuId,
    )
    console.log('🚀 ~ setMenuTabs ~ menuTabs.value:', menuTabs.value)
    if (index === -1) {
      menuTabs.value.push(value)
    } else {
      menuTabs.value.splice(index, 1, value)
    }
  }

  // 添加移除缓存的方法
  const removeCache = (route: string) => {
    if (route.startsWith('/')) {
      route = route.slice(1)
    }
    if (!excludeCache.value.includes(route)) {
      excludeCache.value.push(route)
    }
  }

  // 添加恢复缓存的方法
  const addCache = (route: string) => {
    if (route.startsWith('/')) {
      route = route.slice(1)
    }
    const index = excludeCache.value.indexOf(route)
    if (index > -1) {
      excludeCache.value.splice(index, 1)
    }
  }

  const setRouterShow = (val: boolean) => {
    isRouterShow.value = val
  }

  return {
    appLoading,
    serverType,
    setServerType,
    generationType,
    setGenerationType,
    menuTabs,
    setMenuTabs,
    excludeCache,
    removeCache,
    addCache,
    isRouterShow,
    setRouterShow,
  }
})
