<template>
  <div class="column-selector">
    <a-form-item-rest>
      <a-table
        :columns="columns"
        :data-source="tableColumns"
        :pagination="false"
        :row-selection="{
          selectedRowKeys: selectedColumns,
          onChange: onSelectionChange,
          type: 'checkbox',
        }"
        rowKey="columnName"
        size="small"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'columnName'">
            <a-tooltip>
              <template #title>{{ record.columnComment || '' }}</template>
              {{ record.columnName }}
            </a-tooltip>
          </template>
        </template>
      </a-table>
    </a-form-item-rest>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, watch } from 'vue'
import { getTableInfoListById } from '@/api/assetmanager/dataassertmanage/dataassertmanage'

interface Column {
  columnName: string
  columnComment: string
  dataType: string
  columnType: string
}

const props = defineProps<{
  tableId: string
  modelValue: string[]
}>()

const emit = defineEmits(['update:modelValue', 'select-columns'])

const tableColumns = ref<Column[]>([])
const selectedColumns = ref<string[]>([])

const columns = [
  {
    title: '列名',
    dataIndex: 'columnName',
    key: 'columnName',
  },
  {
    title: '注释',
    dataIndex: 'columnComment',
    key: 'columnComment',
  },
]

const fetchColumns = async () => {
  try {
    const response = await getTableInfoListById({ tableId: props.tableId })
    if (response.data && response.data.length > 0) {
      tableColumns.value = response.data
    }
  } catch (error) {
    console.error('Failed to fetch table columns:', error)
  }
}

const onSelectionChange = (selectedKeys: string[]) => {
  selectedColumns.value = selectedKeys
  emit('update:modelValue', selectedKeys)
  emit(
    'select-columns',
    tableColumns.value.filter((col) => selectedKeys.includes(col.columnName)),
  )
}

watch(
  () => props.modelValue,
  (newVal) => {
    selectedColumns.value = newVal
  },
  { immediate: true },
)

onMounted(() => {
  fetchColumns()
})
</script>

<style scoped>
.column-selector {
  max-height: 400px;
  overflow-y: auto;
}
</style>
