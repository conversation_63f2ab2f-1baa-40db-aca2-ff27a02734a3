<template>
  <div class="sort">
    <div class="sort-title">行数限制</div>
    <div class="sort-containter">
      <a-input-number v-model:value="modelValue" :min="0" style="width: 210px"></a-input-number>
    </div>
  </div>
</template>

<script setup lang="ts">
const modelValue = defineModel()
</script>

<style lang="less" scoped>
.sort-title {
  margin-bottom: 0.5rem;
  color: rgb(147, 161, 171);
  font-weight: bold;
  display: flex;
}
.sort-containter {
  display: flex;
  -webkit-box-align: center;
  align-items: center;
  flex-wrap: wrap;
  border-radius: 8px;
  background-color: rgba(147, 161, 171, 0.1);
  padding: 14px;
  color: rgb(147, 161, 171);
  gap: 10px;
}
.sort-item {
  // color: #fff;
  // padding: 10px;
  // cursor: pointer;
  // background-color: rgba(147, 161, 171, 0.8);

  // border-radius: 6px;
  // display: flex;
  // -webkit-box-align: center;
  // align-items: center;
}
</style>
