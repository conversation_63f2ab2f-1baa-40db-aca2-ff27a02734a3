<template>
  <LayoutCrumbsView
    title="指标管理系统"
    :menu="MenuData"
    :menu-selected-keys="currentMenuKeyPath"
    :menu-open-keys="currentMenuKeyPath"
    :isSecondary="isSecondary"
    :breadcrumb="breadcrumb"
    :user-info="{
      userAvatar: '',
      userName: userInfo.userName,
    }"
    :config-provider="{ theme }"
    @click-menu-item="handleClickMenuItem"
    @logout="accountLogoutFn"
    :siderWidth="150"
  >
    <template #content>
      <RouterView v-slot="{ Component }">
        <template v-if="Component && appStore.serverType === '200'">
          <component :is="Component"></component>
        </template>
        <ServerError v-else></ServerError>
      </RouterView>
    </template>
    <template #header>
      <a-button type="text" @click="resetPasswordModalOpen = true"
        ><FormOutlined />修改密码</a-button
      >
    </template>
    <template #footer>
      <div class="footer">指标管理系统</div>
    </template>
  </LayoutCrumbsView>
  <!-- 重置密码 -->
  <ResetPasswordModal
    v-if="resetPasswordModalOpen"
    v-model="resetPasswordModalOpen"
    :user-id="userInfo.userId"
    title="修改密码"
  />
  <!-- 生成代码弹窗 -->
  <codeGeneration v-if="isDev()" ref="codeGenerationRef" />
  <quickAccess v-if="isDev()" />
</template>

<script setup lang="ts">
import { LayoutCrumbsView } from '@fs/fs-components'
import { storeToRefs } from 'pinia'
import { useRouter, useRoute } from 'vue-router'
import { RouterView } from 'vue-router'
import { useAppStore } from '@/stores/app'
import { useUserStore } from '@/stores/user'
import { ref, onBeforeMount, watch, computed } from 'vue'
import { FormOutlined } from '@ant-design/icons-vue'
import { theme } from '@/config/antd-config-provider'
import { getUserInfo, accountLogout } from '@/api/services/permission'
import { isDev } from '@/utils'
import type { UserMenu } from '@fs/fs-components/src/components/layout/layout'
import ResetPasswordModal from '@/components/permission/reset-password-modal.vue'
import ServerError from '@/views/permission/server-error.vue'
import codeGeneration from '@/components/code-generation/code-generation.vue'
import quickAccess from '@/components/quick-access/quick-access.vue'

const router = useRouter()
const route = useRoute()
const appStore = useAppStore()
const userStore = useUserStore()
const { isLoggedIn, token, userInfo, currentMenuKeyPath, userMenu } = storeToRefs(userStore)

const resetPasswordModalOpen = ref(false)
const MenuData = ref<UserMenu[]>([])
const codeGenerationRef = ref<any>(null)

const generationType = computed(() => {
  return appStore.generationType
})

const isSecondary = computed(() => !!route.meta.isSecondary)
const breadcrumb = computed(() => {
  if (route.meta.breadcrumb === undefined) return true
  return !!route.meta.breadcrumb
})

watch(
  userMenu,
  (newValue) => {
    if (newValue) {
      MenuData.value = filterTree(JSON.parse(JSON.stringify(newValue)))
      console.log(
        '%c 🍱  MenuData.value: ',
        'font-size:12px;background-color: #6EC1C2;color:#fff;',
        MenuData.value,
      )
    }
  },
  { deep: true, immediate: true },
)

watch(
  generationType,
  (newValue) => {
    if (JSON.stringify(newValue) === '{}') return
    codeGenerationRef.value.show(newValue)
  },
  { deep: true, immediate: true },
)

// 递归函数来筛选所有 menuType 等于 1 的菜单   1为菜单 2 是权限 3 页签
function filterTree(tree: UserMenu[]): UserMenu[] {
  const filterNode = (node: UserMenu): UserMenu | null => {
    if (node.isChildrenMenu === true || node.menuType === 2 || node.menuType === 3) {
      return null
    }
    let filteredChildren: UserMenu[] | undefined
    if (node.childrens) {
      filteredChildren = node.childrens
        .map(filterNode) // 递归过滤子节点
        .filter((childNode): childNode is UserMenu => childNode !== null) // 去掉值为 null 的节点
    }
    //@ts-ignore
    return { ...node, childrens: filteredChildren }
  }
  return tree.map(filterNode).filter((node): node is UserMenu => node !== null) // 最终去掉值为 null 的节点
}

onBeforeMount(() => {
  isLoggedIn.value && getUserInfoFn()
})

/**
 * 获取用户信息
 */
async function getUserInfoFn() {
  try {
    appStore.appLoading = true
    //@ts-ignore
    if (!userInfo?.userId) {
      const { data } = await getUserInfo({ userToken: token.value })
      userInfo.value = data
    }
  } catch (error) {
    console.log(error)
  } finally {
    appStore.appLoading = false
  }
}

/**
 * 退出登录
 */
async function accountLogoutFn() {
  try {
    await accountLogout({
      userToken: token.value,
      tenantCode: userInfo.value.tenantCode,
    })
    userStore.clearLoginData()
    router.push('/login')
  } catch (error) {
    console.log(error)
  }
}

function handleClickMenuItem(e: any) {
  const menu: UserMenu = e.item
  console.log('%c 🥟 menu: ', 'font-size:12px;background-color: #B03734;color:#fff;', menu)
  userStore.currentMenuKeyPath = e.keyPath
  router.push(`/${menu.menuUrl}`)
}
</script>
<style scoped lang="less">
:global(.fs-layout .layout-content) {
  border-radius: 0 !important;
  height: calc(100% - 40px);
  overflow: auto;
}
.footer {
  height: 50px;
  line-height: 50px;
}
</style>
