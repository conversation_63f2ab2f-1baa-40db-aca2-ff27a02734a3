import service from '@/api'
import type {
  FunctionItem,
  FunctionListResponse,
  ProjectItem,
  RuleItem,
  RuleListResponse,
} from './data'

/**
 * 获取项目列表
 * @returns 项目列表
 */
export function getProjectList() {
  return service({
    method: 'get',
    url: '/api/jupyter/project/all',
  })
}

/**
 * 创建项目
 * @param data 项目数据
 */
export function createProject(data: ProjectItem) {
  return service({
    method: 'post',
    url: '/api/jupyter/project',
    data,
  })
}

/**
 * 更新项目
 * @param data 项目数据
 */
export function updateProject(data: ProjectItem) {
  return service({
    method: 'put',
    url: '/api/jupyter/project',
    data,
  })
}

/**
 * 删除项目
 * @param id 项目ID
 */
export function deleteProject(id: number) {
  return service({
    method: 'delete',
    url: `/api/jupyter/project/${id}`,
  })
}

/**
 * 获取函数列表
 * @param projectId 项目ID
 * @returns 函数列表
 */
export function getFunctionList(projectId: string): Promise<FunctionListResponse> {
  return service({
    method: 'get',
    url: `/api/function/search`,
    params: {
      projectId,
    },
  })
}

/**
 * 添加函数
 * @param data 函数数据
 */
export function addFunction(data: FunctionItem) {
  return service({
    method: 'post',
    url: '/api/function',
    data,
  })
}

/**
 * 更新函数
 * @param id 函数ID
 * @param data 函数数据
 */
export function updateFunction(data: FunctionItem) {
  return service({
    method: 'put',
    url: `/api/function`,
    data,
  })
}

/**
 * 删除函数
 * @param id 函数ID
 */
export function deleteFunction(id: number) {
  return service({
    method: 'delete',
    url: `/api/function/${id}`,
  })
}

/**
 * 获取规则列表
 * @param projectId 项目ID
 * @returns 规则列表
 */
export function getRuleList(projectId: string): Promise<RuleListResponse> {
  return service({
    method: 'get',
    url: `/api/rule/search`,
    params: { projectId },
  })
}

/**
 * 添加规则
 * @param data 规则数据
 */
export function addRule(data: RuleItem) {
  return service({
    method: 'post',
    url: '/api/rule',
    data,
  })
}

/**
 * 更新规则
 * @param data 规则数据
 */
export function updateRule(data: RuleItem) {
  return service({
    method: 'put',
    url: `/api/rule`,
    data,
  })
}

/**
 * 删除规则
 * @param id 规则ID
 */
export function deleteRule(id: number) {
  return service({
    method: 'delete',
    url: `/api/rule/${id}`,
  })
}

/**
 * 调用规则进行计算
 * @param projectId 项目ID
 * @param ruleId 规则ID
 * @param args 参数obj
 */
export function applyRule(data: any) {
  return service({
    method: 'post',
    url: `/api/rule/apply`,
    data,
  })
}

/**
 * 根据角色获取项目列表
 * @returns 项目列表
 */
export function getProjectRole(data: any) {
  return service({
    method: 'get',
    url: '/api/project/role?roleId=' + data.roleId,
  })
}

/**
 * 更新角色项目
 * @param data 项目列表
 */
export function updateByRole(data: any) {
  return service({
    method: 'put',
    url: `/api/project/updateByRole?roleId=${data.roleId}`,
    data: data.roleProjects,
  })
}

/**
 * 根据角色获取项目列表
 * @returns 项目列表
 */
export function getJupyterProjectRole(data: any) {
  return service({
    method: 'get',
    url: '/api/jupyter/project/role?roleId=' + data.roleId,
  })
}

/**
 * 更新角色项目
 * @param data 项目列表
 */
export function updateJupyterByRole(data: any) {
  return service({
    method: 'put',
    url: `/api/jupyter/project/updateByRole?roleId=${data.roleId}`,
    data: data.roleProjects,
  })
}
