<template>
  <div class="field-box">
    <a
      class="link"
      :href="column.linkUrl"
      target="_blank"
      v-if="column.type === 'LINK' && column.linkUrl"
      >{{ column.linkText || displayFn(column.type, propsItem[column.dataIndex], column) }}</a
    >
    <div :style="{ backgroundColor: column.backgroundColor }">
      <div class="field-text" v-if="column.showType === 'link'" @click="handleLink">
        <a-progress :percent="propsItem.value" stroke-width="8" v-if="column.showProgress" />

        <a-tooltip>
          <template #title>
            {{ displayFn(column.type, propsItem[column.dataIndex], column) || '-' }}
          </template>
          <span class="ellipsis">{{
            displayFn(column.type, propsItem[column.dataIndex], column) || '-'
          }}</span>
        </a-tooltip>
      </div>
      <div class="field-text" v-else-if="column.showType === 'text'" @click="handleClickField">
        <div style="display: flex; gap: 10px">
          <a-progress
            :percent="propsItem.value"
            stroke-width="8"
            style="width: 100px; margin: 0; line-height: 30px"
            v-if="column.showProgress"
          />
          <!-- <span style="line-height: 30px">{{
            displayFn(column.type, propsItem[column.dataIndex], column) || '-'
          }}</span> -->
          <a-tooltip>
            <template #title>
              {{ displayFn(column.type, propsItem[column.dataIndex], column) || '-' }}
            </template>
            <span class="ellipsis">{{
              displayFn(column.type, propsItem[column.dataIndex], column) || '-'
            }}</span>
          </a-tooltip>
        </div>
      </div>
      <div class="field-text" v-else-if="column.showType === 'email'" @click="handleClickEmail">
        <a-flex>
          <a-progress :percent="propsItem.value" stroke-width="8" v-if="column.showProgress" />

          <a-tooltip>
            <template #title>
              {{ displayFn(column.type, propsItem[column.dataIndex], column) || '-' }}
            </template>
            <span class="ellipsis">{{
              displayFn(column.type, propsItem[column.dataIndex], column) || '-'
            }}</span>
          </a-tooltip>
        </a-flex>
      </div>
      <div class="field-text" v-else @click="handleClickField">
        <div style="display: flex; gap: 10px">
          <a-progress
            :percent="propsItem.value"
            stroke-width="8"
            style="width: 100px; margin: 0; line-height: 30px"
            v-if="column.showProgress"
          />
          <!-- <span style="line-height: 30px">{{
            displayFn(column.type, propsItem[column.dataIndex], column) || '-'
          }}</span> -->
          <a-tooltip>
            <template #title>
              {{ displayFn(column.type, propsItem[column.dataIndex], column) || '-' }}
            </template>
            <span class="ellipsis">{{
              displayFn(column.type, propsItem[column.dataIndex], column) || '-'
            }}</span>
          </a-tooltip>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import MenuList from '@/components/menu-list/menu-list.vue'
import { ArrowsAltOutlined } from '@ant-design/icons-vue'
import { isTimeType } from '@/utils/tool'
import { displayFn } from './utile'

// import { fieldTypeOptions } from '@/config/field'

/**
 * propsItem字段说明
 * field：表头字段名称
 * showType：展示字段的类型，文字、链接、邮件
 * fieldType：字段类型，参考config/field.ts文件
 * value：字段值
 * showProgress: 是否展示进度条
 * progress: 进度条配置
 */
const props = defineProps({
  propsItem: {
    type: Object,
    default: () => {},
  },
  column: {
    type: Object,
    default: () => {},
  },
})
const menuMap: any = ref([])

const showMenu = ref(false)

const timeMenu = ref([
  {
    label: '之前',
    value: 'before',
  },
  {
    label: '之后',
    value: 'after',
  },
  {
    label: '当前',
    value: 'current',
  },
  {
    label: '不在',
    value: 'notIn',
  },
])

const handleClickField = () => {
  showMenu.value = !showMenu.value
}

const handleClickEmail = () => {
  window.location.href = `mailto:${props.propsItem[props.column.dataIndex]}`
}

const handleLink = () => {
  window.open(props.propsItem.value)
}

watch(
  () => props.propsItem,
  (val) => {
    // console.log(
    //   '🚀 ~ val && !isTimeType():',
    //   val && !isTimeType(props.propsItem[props.column.dataIndex]),
    // )
    if (val && !isTimeType(props.propsItem[props.column.dataIndex])) {
      menuMap.value = [
        {
          label: `= 是 ${val.field}`,
          value: 1,
          // icon: h(ArrowsAltOutlined)
        },
        {
          label: `≠ 不是 ${val.field}`,
          value: 0,
        },
      ]
    }
  },
  { immediate: true, deep: true },
)
</script>

<style lang="less" scoped>
.field-box {
  display: inline-block;
  width: 100%;
  .link:hover {
    text-decoration: underline;
  }
}
.field-popover {
  width: 160px;
}
.field-text {
  color: #4c5773;
  font-weight: 700;
  cursor: default;
  padding: 6px 12px;
  word-break: break-all;
}
.field-text:hover {
  background-color: #edf5fc;
  white-space: normal;
  word-wrap: break-word;
}
</style>
