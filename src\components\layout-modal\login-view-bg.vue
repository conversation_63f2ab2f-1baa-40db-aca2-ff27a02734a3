<template>
  <div class="cloud-login" :class="{ preview: preview }">
    <div class="login-container">
      <div class="cloud-login-content shadow-1">
        <div class="cloud-login-content_form">
          <cloud-login ref="login" :backdrop="true" :preview="preview"></cloud-login>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import CloudLogin from './login-view.vue'

defineProps({
  preview: {
    type: Boolean,
    default: false,
  },
})
</script>
<style lang="less" scoped>
.cloud-login {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  height: 100%;
  width: 100%;
  background: no-repeat url('@/assets/bg.png');
  background-size: cover;
  position: relative;
  -webkit-app-region: drag;
  cursor: move;
}

.preview {
  height: 910px;
  .login-container {
    height: 910px;
  }
}

.login-container {
  display: flex;
  align-items: center;
  justify-content: center;
}

.cloud-login-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  border-radius: 8px;
  background: white;
  -webkit-app-region: none;
  cursor: default;
}

.cloud-login-content_tabs {
  display: flex;
  width: 100%;
  justify-content: space-around;
  margin: 30px 0;

  span {
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    color: #c9cdd4;
  }

  .active-tab {
    color: black;
  }

  .active-tab::after {
    content: '';
    width: 15px;
    display: block;
    margin: 0 auto;
    margin-top: 5px;
    height: 3px;
    background: var(--brand-color);
  }
}

.cloud-login-content_form {
  width: 100%;
}

.cloud-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  overflow: hidden;
  position: relative;
  color: white;
  background: #232324;
  padding: 0px 24px;
  height: 56px;
  cursor: move;
  -webkit-app-region: drag;
}

.cloud-header-logo {
  display: flex;
  height: 56px;

  &_title {
    font-weight: 600;
    font-size: 18px;
    color: white;
    margin-left: 8px;
  }
}

.cloud-header-menus {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 32px;

  &_user {
    display: flex;
    align-items: center;

    &_center {
      cursor: pointer;

      span {
        margin-left: 3px;
      }
    }

    &_avatar {
      cursor: pointer;
      margin-left: 20px;
    }
  }
}

.cloud-header-opration {
  width: 100px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: absolute;
  top: 10px;
  right: 10px;
  -webkit-app-region: none;
  cursor: default;

  i {
    cursor: pointer;
  }
}

.ant-btn {
  background-color: rgba(#ffffff, 0.1) !important;
  border: unset;
  color: rgb(135, 135, 136);

  &::after {
    display: none;
  }
}

.ant-btn:hover,
.ant-btn:focus,
.ant-btn:active,
.ant-btn.active {
  color: white;
  background: #17171a !important;
}

.active {
  background: #17171a !important;
}
</style>
