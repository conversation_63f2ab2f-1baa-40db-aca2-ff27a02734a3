interface CookieOptions {
  expires?: number | Date
  /**
   * Cookie路径
   * 默认值为 /
   * 设置为 / 表示在整个网站范围内有效
   * 设置为 /admin 表示只在 admin 目录下有效
   * 设置为 /admin/user 表示只在 admin/user 目录下有效
   */
  path?: string
  /**
   * Cookie域名
   * 默认值为当前域名
   * 设置为 www.example.com 表示只在 www.example.com 域名下有效
   */
  domain?: string
  /** 是否仅允许HTTPS请求携带Cookie */
  secure?: boolean
  /**
   * 跨域请求时，是否允许携带Cookie
   * strict: 仅允许同源请求携带Cookie
   * lax: 允许同源和跨域请求携带Cookie
   * none: 允许所有请求携带Cookie
   */
  sameSite?: 'strict' | 'lax' | 'none'
  /** 是否仅允许HTTP请求携带Cookie */
  httpOnly?: boolean
}

export class CookieManager {
  /**
   * 设置Cookie
   */
  static set(name: string, value: string, options: CookieOptions = {}) {
    const { expires, path = '/', domain, secure = false, sameSite = 'lax' } = options

    let cookieString = `${name}=${encodeURIComponent(value)}`

    if (expires) {
      const expiresDate =
        typeof expires === 'number' ? new Date(Date.now() + expires * 24 * 60 * 60 * 1000) : expires
      cookieString += `; expires=${expiresDate.toUTCString()}`
    }

    cookieString += `; path=${path}`

    if (domain) {
      cookieString += `; domain=${domain}`
    }

    if (secure) {
      cookieString += '; secure'
    }

    cookieString += `; SameSite=${sameSite}`

    document.cookie = cookieString
  }

  /**
   * 获取Cookie
   */
  static get(name: string): string | null {
    const cookies = document.cookie.split(';')

    for (const cookie of cookies) {
      const [cookieName, cookieValue] = cookie.trim().split('=')
      if (cookieName === name) {
        return decodeURIComponent(cookieValue)
      }
    }

    return null
  }

  /**
   * 删除Cookie
   */
  static remove(name: string, path: string = '/', domain?: string) {
    let cookieString = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=${path}`

    if (domain) {
      cookieString += `; domain=${domain}`
    }

    document.cookie = cookieString
  }

  /**
   * 检查Cookie是否存在
   */
  static has(name: string): boolean {
    return this.get(name) !== null
  }
}
// 导出配置常量
export const COOKIE_CONFIG = {
  TOKEN_KEY: 'app_token',
  EXPIRES_DAYS: 7,
  DOMAIN: undefined, // 如需支持子域名共享，设置为 '.yourdomain.com'
}
