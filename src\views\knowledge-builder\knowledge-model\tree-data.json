{"entityTypeDTO": {"id": 1, "name": "Thing", "nameZh": "事物", "description": "Base class for all schema types, all of which inherit the type either directly or indirectly", "inheritedPropertyList": [], "propertyList": [{"id": 1, "name": "description", "nameZh": "描述", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {}, "defaultTime": false, "subPropertyList": []}, {"id": 2, "name": "id", "nameZh": "实体主键", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {}, "defaultTime": false, "subPropertyList": []}, {"id": 3, "name": "name", "nameZh": "名称", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {}, "defaultTime": false, "subPropertyList": []}], "relations": [], "entityCategory": "ENTITY_TYPE"}, "children": [{"entityTypeDTO": {"id": 2025032190560455, "name": "Medicine.Medicine", "nameZh": "药品", "description": "", "inheritedPropertyList": [{"id": 1, "name": "description", "nameZh": "描述", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {}, "defaultTime": false, "subPropertyList": []}, {"id": 2, "name": "id", "nameZh": "实体主键", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {}, "defaultTime": false, "subPropertyList": []}, {"id": 3, "name": "name", "nameZh": "名称", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {}, "defaultTime": false, "subPropertyList": []}], "propertyList": [{"id": 2025032142802638, "name": "desc", "nameZh": "描述", "description": "", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {"valueType": "BASIC_TYPE"}, "defaultTime": false, "subPropertyList": []}, {"id": 2025032198948812, "name": "semanticType", "nameZh": "语义类型", "description": "", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {"valueType": "BASIC_TYPE"}, "defaultTime": false, "subPropertyList": []}], "relations": [], "parentId": 1, "parentName": "Thing", "belongToProject": 1, "entityCategory": "ENTITY_TYPE", "extInfo": {}}, "children": []}, {"entityTypeDTO": {"id": 2025032138767053, "name": "Medicine.Hospital", "nameZh": "医院", "inheritedPropertyList": [{"id": 1, "name": "description", "nameZh": "描述", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {}, "defaultTime": false, "subPropertyList": []}, {"id": 2, "name": "id", "nameZh": "实体主键", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {}, "defaultTime": false, "subPropertyList": []}, {"id": 3, "name": "name", "nameZh": "名称", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {}, "defaultTime": false, "subPropertyList": []}], "propertyList": [{"id": 2025032172228266, "name": "desc", "nameZh": "描述", "description": "", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {"valueType": "BASIC_TYPE"}, "defaultTime": false, "subPropertyList": []}, {"id": 2025032195471838, "name": "semanticType", "nameZh": "语义类型", "description": "", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {"valueType": "BASIC_TYPE"}, "defaultTime": false, "subPropertyList": []}], "relations": [], "parentId": 1, "parentName": "Thing", "belongToProject": 1, "entityCategory": "ENTITY_TYPE", "extInfo": {"commonIndex": false}}, "children": []}, {"entityTypeDTO": {"id": 2025032117297797, "name": "Medicine.MedicalInsuranceReimbursementPolicy", "nameZh": "医保报销政策", "inheritedPropertyList": [{"id": 1, "name": "description", "nameZh": "描述", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {}, "defaultTime": false, "subPropertyList": []}, {"id": 2, "name": "id", "nameZh": "实体主键", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {}, "defaultTime": false, "subPropertyList": []}, {"id": 3, "name": "name", "nameZh": "名称", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {}, "defaultTime": false, "subPropertyList": []}], "propertyList": [{"id": 2025032126379790, "name": "desc", "nameZh": "描述", "description": "", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {"valueType": "BASIC_TYPE"}, "defaultTime": false, "subPropertyList": []}, {"id": 2025032149205339, "name": "semanticType", "nameZh": "语义类型", "description": "", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {"valueType": "BASIC_TYPE"}, "defaultTime": false, "subPropertyList": []}], "relations": [], "parentId": 1, "parentName": "Thing", "belongToProject": 1, "entityCategory": "ENTITY_TYPE", "extInfo": {"commonIndex": false}}, "children": []}, {"entityTypeDTO": {"id": 2025032171902535, "name": "Medicine.MedicalAdvice", "nameZh": "诊疗建议", "inheritedPropertyList": [{"id": 1, "name": "description", "nameZh": "描述", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {}, "defaultTime": false, "subPropertyList": []}, {"id": 2, "name": "id", "nameZh": "实体主键", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {}, "defaultTime": false, "subPropertyList": []}, {"id": 3, "name": "name", "nameZh": "名称", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {}, "defaultTime": false, "subPropertyList": []}], "propertyList": [{"id": 2025032139440388, "name": "semanticType", "nameZh": "语义类型", "description": "", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {"valueType": "BASIC_TYPE"}, "defaultTime": false, "subPropertyList": []}, {"id": 2025032177271208, "name": "desc", "nameZh": "描述", "description": "", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {"valueType": "BASIC_TYPE"}, "defaultTime": false, "subPropertyList": []}], "relations": [], "parentId": 1, "parentName": "Thing", "belongToProject": 1, "entityCategory": "ENTITY_TYPE", "extInfo": {"commonIndex": false}}, "children": []}, {"entityTypeDTO": {"id": 2025032179693631, "name": "Medicine.ExaminationTest", "nameZh": "检查检验", "inheritedPropertyList": [{"id": 1, "name": "description", "nameZh": "描述", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {}, "defaultTime": false, "subPropertyList": []}, {"id": 2, "name": "id", "nameZh": "实体主键", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {}, "defaultTime": false, "subPropertyList": []}, {"id": 3, "name": "name", "nameZh": "名称", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {}, "defaultTime": false, "subPropertyList": []}], "propertyList": [{"id": 2025032168681145, "name": "semanticType", "nameZh": "语义类型", "description": "", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {"valueType": "BASIC_TYPE"}, "defaultTime": false, "subPropertyList": []}, {"id": 2025032198164863, "name": "desc", "nameZh": "描述", "description": "", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {"valueType": "BASIC_TYPE"}, "defaultTime": false, "subPropertyList": []}], "relations": [], "parentId": 1, "parentName": "Thing", "belongToProject": 1, "entityCategory": "ENTITY_TYPE", "extInfo": {"commonIndex": false}}, "children": []}, {"entityTypeDTO": {"id": 2025032163968591, "name": "Medicine.HospitalDepartment", "nameZh": "科室", "inheritedPropertyList": [{"id": 1, "name": "description", "nameZh": "描述", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {}, "defaultTime": false, "subPropertyList": []}, {"id": 2, "name": "id", "nameZh": "实体主键", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {}, "defaultTime": false, "subPropertyList": []}, {"id": 3, "name": "name", "nameZh": "名称", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {}, "defaultTime": false, "subPropertyList": []}], "propertyList": [{"id": 2025032129652805, "name": "stdId", "nameZh": "标准ID", "description": "标准ID", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {"valueType": "BASIC_TYPE"}, "defaultTime": false, "subPropertyList": []}, {"id": 2025032180019184, "name": "alias", "nameZh": "别名", "description": "别名", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [{"id": "MULTI_VALUE", "name": "MULTI_VALUE", "nameZh": "多值"}], "extInfo": {"valueType": "BASIC_TYPE"}, "defaultTime": false, "subPropertyList": []}], "relations": [], "parentId": 1, "parentName": "Thing", "belongToProject": 1, "entityCategory": "CONCEPT_TYPE", "extInfo": {"conceptLayerConfig": {"hypernymPredicate": "isA"}, "commonIndex": true}}, "children": []}, {"entityTypeDTO": {"id": 2025032182835688, "name": "Medicine.MedicalEquipment", "nameZh": "医疗器械", "inheritedPropertyList": [{"id": 1, "name": "description", "nameZh": "描述", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {}, "defaultTime": false, "subPropertyList": []}, {"id": 2, "name": "id", "nameZh": "实体主键", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {}, "defaultTime": false, "subPropertyList": []}, {"id": 3, "name": "name", "nameZh": "名称", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {}, "defaultTime": false, "subPropertyList": []}], "propertyList": [{"id": 2025032165775977, "name": "desc", "nameZh": "描述", "description": "", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {"valueType": "BASIC_TYPE"}, "defaultTime": false, "subPropertyList": []}, {"id": 2025032195328693, "name": "semanticType", "nameZh": "语义类型", "description": "", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {"valueType": "BASIC_TYPE"}, "defaultTime": false, "subPropertyList": []}], "relations": [], "parentId": 1, "parentName": "Thing", "belongToProject": 1, "entityCategory": "ENTITY_TYPE", "extInfo": {"commonIndex": false}}, "children": []}, {"entityTypeDTO": {"id": 2025032175147062, "name": "Medicine.SurgicalOperation", "nameZh": "手术操作", "inheritedPropertyList": [{"id": 1, "name": "description", "nameZh": "描述", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {}, "defaultTime": false, "subPropertyList": []}, {"id": 2, "name": "id", "nameZh": "实体主键", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {}, "defaultTime": false, "subPropertyList": []}, {"id": 3, "name": "name", "nameZh": "名称", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {}, "defaultTime": false, "subPropertyList": []}], "propertyList": [{"id": 2025032159019796, "name": "semanticType", "nameZh": "语义类型", "description": "", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {"valueType": "BASIC_TYPE"}, "defaultTime": false, "subPropertyList": []}, {"id": 2025032161090656, "name": "desc", "nameZh": "描述", "description": "", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {"valueType": "BASIC_TYPE"}, "defaultTime": false, "subPropertyList": []}], "relations": [], "parentId": 1, "parentName": "Thing", "belongToProject": 1, "entityCategory": "ENTITY_TYPE", "extInfo": {"commonIndex": false}}, "children": []}, {"entityTypeDTO": {"id": 2025032196791184, "name": "Medicine.Symptom", "nameZh": "症状", "inheritedPropertyList": [{"id": 1, "name": "description", "nameZh": "描述", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {}, "defaultTime": false, "subPropertyList": []}, {"id": 2, "name": "id", "nameZh": "实体主键", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {}, "defaultTime": false, "subPropertyList": []}, {"id": 3, "name": "name", "nameZh": "名称", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {}, "defaultTime": false, "subPropertyList": []}], "propertyList": [{"id": 2025032115953973, "name": "desc", "nameZh": "描述", "description": "", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {"valueType": "BASIC_TYPE"}, "defaultTime": false, "subPropertyList": []}, {"id": 2025032125779426, "name": "semanticType", "nameZh": "语义类型", "description": "", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {"valueType": "BASIC_TYPE"}, "defaultTime": false, "subPropertyList": []}], "relations": [], "parentId": 1, "parentName": "Thing", "belongToProject": 1, "entityCategory": "ENTITY_TYPE", "extInfo": {"commonIndex": false}}, "children": []}, {"entityTypeDTO": {"id": 2025032160058949, "name": "Medicine.Indicator", "nameZh": "医学指征", "inheritedPropertyList": [{"id": 1, "name": "description", "nameZh": "描述", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {}, "defaultTime": false, "subPropertyList": []}, {"id": 2, "name": "id", "nameZh": "实体主键", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {}, "defaultTime": false, "subPropertyList": []}, {"id": 3, "name": "name", "nameZh": "名称", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {}, "defaultTime": false, "subPropertyList": []}], "propertyList": [{"id": 2025032141813116, "name": "semanticType", "nameZh": "语义类型", "description": "", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {"valueType": "BASIC_TYPE"}, "defaultTime": false, "subPropertyList": []}, {"id": 2025032151663834, "name": "desc", "nameZh": "描述", "description": "", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {"valueType": "BASIC_TYPE"}, "defaultTime": false, "subPropertyList": []}], "relations": [], "parentId": 1, "parentName": "Thing", "belongToProject": 1, "entityCategory": "ENTITY_TYPE", "extInfo": {"commonIndex": false}}, "children": []}, {"entityTypeDTO": {"id": 2025032136951108, "name": "Medicine.ComprehensiveService", "nameZh": "综合服务", "inheritedPropertyList": [{"id": 1, "name": "description", "nameZh": "描述", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {}, "defaultTime": false, "subPropertyList": []}, {"id": 2, "name": "id", "nameZh": "实体主键", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {}, "defaultTime": false, "subPropertyList": []}, {"id": 3, "name": "name", "nameZh": "名称", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {}, "defaultTime": false, "subPropertyList": []}], "propertyList": [{"id": 2025032133553485, "name": "desc", "nameZh": "描述", "description": "", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {"valueType": "BASIC_TYPE"}, "defaultTime": false, "subPropertyList": []}, {"id": 2025032194339923, "name": "semanticType", "nameZh": "语义类型", "description": "", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {"valueType": "BASIC_TYPE"}, "defaultTime": false, "subPropertyList": []}], "relations": [], "parentId": 1, "parentName": "Thing", "belongToProject": 1, "entityCategory": "ENTITY_TYPE", "extInfo": {"commonIndex": false}}, "children": []}, {"entityTypeDTO": {"id": 2025032196980811, "name": "Medicine.Doctor", "nameZh": "医生", "inheritedPropertyList": [{"id": 1, "name": "description", "nameZh": "描述", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {}, "defaultTime": false, "subPropertyList": []}, {"id": 2, "name": "id", "nameZh": "实体主键", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {}, "defaultTime": false, "subPropertyList": []}, {"id": 3, "name": "name", "nameZh": "名称", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {}, "defaultTime": false, "subPropertyList": []}], "propertyList": [{"id": 2025032137793749, "name": "desc", "nameZh": "描述", "description": "", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {"valueType": "BASIC_TYPE"}, "defaultTime": false, "subPropertyList": []}, {"id": 2025032153976314, "name": "semanticType", "nameZh": "语义类型", "description": "", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {"valueType": "BASIC_TYPE"}, "defaultTime": false, "subPropertyList": []}], "relations": [], "parentId": 1, "parentName": "Thing", "belongToProject": 1, "entityCategory": "ENTITY_TYPE", "extInfo": {"commonIndex": false}}, "children": []}, {"entityTypeDTO": {"id": 2025032185103994, "name": "Medicine.HealthFood", "nameZh": "保健食品", "inheritedPropertyList": [{"id": 1, "name": "description", "nameZh": "描述", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {}, "defaultTime": false, "subPropertyList": []}, {"id": 2, "name": "id", "nameZh": "实体主键", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {}, "defaultTime": false, "subPropertyList": []}, {"id": 3, "name": "name", "nameZh": "名称", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {}, "defaultTime": false, "subPropertyList": []}], "propertyList": [{"id": 2025032114257951, "name": "desc", "nameZh": "描述", "description": "", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {"valueType": "BASIC_TYPE"}, "defaultTime": false, "subPropertyList": []}, {"id": 2025032165592362, "name": "semanticType", "nameZh": "语义类型", "description": "", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {"valueType": "BASIC_TYPE"}, "defaultTime": false, "subPropertyList": []}], "relations": [], "parentId": 1, "parentName": "Thing", "belongToProject": 1, "entityCategory": "ENTITY_TYPE", "extInfo": {"commonIndex": false}}, "children": []}, {"entityTypeDTO": {"id": 2025032169579799, "name": "Medicine.DiseaseCategory", "nameZh": "疾病类目", "inheritedPropertyList": [{"id": 1, "name": "description", "nameZh": "描述", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {}, "defaultTime": false, "subPropertyList": []}, {"id": 2, "name": "id", "nameZh": "实体主键", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {}, "defaultTime": false, "subPropertyList": []}, {"id": 3, "name": "name", "nameZh": "名称", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {}, "defaultTime": false, "subPropertyList": []}], "propertyList": [{"id": 2025032151356065, "name": "semanticType", "nameZh": "语义类型", "description": "", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {"valueType": "BASIC_TYPE"}, "defaultTime": false, "subPropertyList": []}, {"id": 2025032161498219, "name": "desc", "nameZh": "描述", "description": "", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {"valueType": "BASIC_TYPE"}, "defaultTime": false, "subPropertyList": []}], "relations": [], "parentId": 1, "parentName": "Thing", "belongToProject": 1, "entityCategory": "ENTITY_TYPE", "extInfo": {"commonIndex": false}}, "children": []}, {"entityTypeDTO": {"id": 2025032171034084, "name": "Medicine.SpecialMedicalFood", "nameZh": "特医食品", "inheritedPropertyList": [{"id": 1, "name": "description", "nameZh": "描述", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {}, "defaultTime": false, "subPropertyList": []}, {"id": 2, "name": "id", "nameZh": "实体主键", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {}, "defaultTime": false, "subPropertyList": []}, {"id": 3, "name": "name", "nameZh": "名称", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {}, "defaultTime": false, "subPropertyList": []}], "propertyList": [{"id": 2025032132247820, "name": "semanticType", "nameZh": "语义类型", "description": "", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {"valueType": "BASIC_TYPE"}, "defaultTime": false, "subPropertyList": []}, {"id": 2025032139795505, "name": "desc", "nameZh": "描述", "description": "", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {"valueType": "BASIC_TYPE"}, "defaultTime": false, "subPropertyList": []}], "relations": [], "parentId": 1, "parentName": "Thing", "belongToProject": 1, "entityCategory": "ENTITY_TYPE", "extInfo": {"commonIndex": false}}, "children": []}, {"entityTypeDTO": {"id": 2025032195494490, "name": "Medicine.Population", "nameZh": "人群", "inheritedPropertyList": [{"id": 1, "name": "description", "nameZh": "描述", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {}, "defaultTime": false, "subPropertyList": []}, {"id": 2, "name": "id", "nameZh": "实体主键", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {}, "defaultTime": false, "subPropertyList": []}, {"id": 3, "name": "name", "nameZh": "名称", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {}, "defaultTime": false, "subPropertyList": []}], "propertyList": [{"id": 2025032135950931, "name": "semanticType", "nameZh": "语义类型", "description": "", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {"valueType": "BASIC_TYPE"}, "defaultTime": false, "subPropertyList": []}, {"id": 2025032184270382, "name": "desc", "nameZh": "描述", "description": "", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {"valueType": "BASIC_TYPE"}, "defaultTime": false, "subPropertyList": []}], "relations": [], "parentId": 1, "parentName": "Thing", "belongToProject": 1, "entityCategory": "ENTITY_TYPE", "extInfo": {"commonIndex": false}}, "children": []}, {"entityTypeDTO": {"id": 2025032111177780, "name": "Medicine.Disease", "nameZh": "疾病", "inheritedPropertyList": [{"id": 1, "name": "description", "nameZh": "描述", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {}, "defaultTime": false, "subPropertyList": []}, {"id": 2, "name": "id", "nameZh": "实体主键", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {}, "defaultTime": false, "subPropertyList": []}, {"id": 3, "name": "name", "nameZh": "名称", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {}, "defaultTime": false, "subPropertyList": []}], "propertyList": [{"id": 2025032119273129, "name": "applicableMedicine", "nameZh": "适用药品", "description": "", "rangeId": 2025032190560455, "rangeName": "Medicine.Medicine", "rangeNameZh": "药品", "propertyCategoryEnum": "ENTITY_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [{"id": "MULTI_VALUE", "name": "MULTI_VALUE", "nameZh": "多值"}], "extInfo": {"valueType": "ENTITY_TYPE"}, "defaultTime": false, "subPropertyList": []}, {"id": 2025032120622754, "name": "desc", "nameZh": "描述", "description": "", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {"valueType": "BASIC_TYPE"}, "defaultTime": false, "subPropertyList": []}, {"id": 2025032120756305, "name": "complication", "nameZh": "并发症", "description": "", "rangeId": 2025032111177780, "rangeName": "Medicine.Disease", "rangeNameZh": "疾病", "propertyCategoryEnum": "ENTITY_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [{"id": "MULTI_VALUE", "name": "MULTI_VALUE", "nameZh": "多值"}], "extInfo": {"valueType": "ENTITY_TYPE"}, "defaultTime": false, "subPropertyList": []}, {"id": 2025032123032147, "name": "commonSymptom", "nameZh": "常见症状", "description": "", "rangeId": 2025032196791184, "rangeName": "Medicine.Symptom", "rangeNameZh": "症状", "propertyCategoryEnum": "ENTITY_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [{"id": "MULTI_VALUE", "name": "MULTI_VALUE", "nameZh": "多值"}], "extInfo": {"valueType": "ENTITY_TYPE"}, "defaultTime": false, "subPropertyList": []}, {"id": 2025032162784940, "name": "semanticType", "nameZh": "语义类型", "description": "", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {"valueType": "BASIC_TYPE"}, "defaultTime": false, "subPropertyList": []}, {"id": 2025032164479342, "name": "hospitalDepartment", "nameZh": "就诊科室", "description": "", "rangeId": 2025032163968591, "rangeName": "Medicine.HospitalDepartment", "rangeNameZh": "科室", "propertyCategoryEnum": "CONCEPT_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [{"id": "MULTI_VALUE", "name": "MULTI_VALUE", "nameZh": "多值"}], "extInfo": {"valueType": "CONCEPT_TYPE"}, "defaultTime": false, "subPropertyList": []}, {"id": 2025032179363421, "name": "diseaseSite", "nameZh": "发病部位", "description": "", "rangeId": 2025032160057237, "rangeName": "Medicine.HumanBodyPart", "rangeNameZh": "人体部位", "propertyCategoryEnum": "CONCEPT_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [{"id": "MULTI_VALUE", "name": "MULTI_VALUE", "nameZh": "多值"}], "extInfo": {"valueType": "CONCEPT_TYPE"}, "defaultTime": false, "subPropertyList": []}], "relations": [], "parentId": 1, "parentName": "Thing", "belongToProject": 1, "entityCategory": "ENTITY_TYPE", "extInfo": {"commonIndex": false}}, "children": []}, {"entityTypeDTO": {"id": 2025032149769517, "name": "Medicine.Others", "nameZh": "其他", "description": "", "inheritedPropertyList": [{"id": 1, "name": "description", "nameZh": "描述", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {}, "defaultTime": false, "subPropertyList": []}, {"id": 2, "name": "id", "nameZh": "实体主键", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {}, "defaultTime": false, "subPropertyList": []}, {"id": 3, "name": "name", "nameZh": "名称", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {}, "defaultTime": false, "subPropertyList": []}], "propertyList": [{"id": 2025032182126251, "name": "desc", "nameZh": "描述", "description": "", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {"valueType": "BASIC_TYPE"}, "defaultTime": false, "subPropertyList": []}, {"id": 2025032195748170, "name": "semanticType", "nameZh": "语义类型", "description": "", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {"valueType": "BASIC_TYPE"}, "defaultTime": false, "subPropertyList": []}], "relations": [], "parentId": 1, "parentName": "Thing", "belongToProject": 1, "entityCategory": "ENTITY_TYPE", "extInfo": {}}, "children": []}, {"entityTypeDTO": {"id": 2025032160057237, "name": "Medicine.HumanBodyPart", "nameZh": "人体部位", "inheritedPropertyList": [{"id": 1, "name": "description", "nameZh": "描述", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {}, "defaultTime": false, "subPropertyList": []}, {"id": 2, "name": "id", "nameZh": "实体主键", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {}, "defaultTime": false, "subPropertyList": []}, {"id": 3, "name": "name", "nameZh": "名称", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {}, "defaultTime": false, "subPropertyList": []}], "propertyList": [{"id": 2025032167140279, "name": "stdId", "nameZh": "标准ID", "description": "标准ID", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {"valueType": "BASIC_TYPE"}, "defaultTime": false, "subPropertyList": []}, {"id": 2025032177777265, "name": "alias", "nameZh": "别名", "description": "别名", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [{"id": "MULTI_VALUE", "name": "MULTI_VALUE", "nameZh": "多值"}], "extInfo": {"valueType": "BASIC_TYPE"}, "defaultTime": false, "subPropertyList": []}], "relations": [], "parentId": 1, "parentName": "Thing", "belongToProject": 1, "entityCategory": "CONCEPT_TYPE", "extInfo": {"conceptLayerConfig": {"hypernymPredicate": "isA"}, "commonIndex": true}}, "children": []}, {"entityTypeDTO": {"id": 2025032150929017, "name": "Medicine.Chunk", "nameZh": "文本块", "description": "", "inheritedPropertyList": [{"id": 1, "name": "description", "nameZh": "描述", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {}, "defaultTime": false, "subPropertyList": []}, {"id": 2, "name": "id", "nameZh": "实体主键", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {}, "defaultTime": false, "subPropertyList": []}, {"id": 3, "name": "name", "nameZh": "名称", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {}, "defaultTime": false, "subPropertyList": []}], "propertyList": [{"id": 2025032176782508, "name": "content", "nameZh": "内容", "description": "", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {"valueType": "BASIC_TYPE"}, "defaultTime": false, "subPropertyList": []}], "relations": [], "parentId": 1, "parentName": "Thing", "belongToProject": 1, "entityCategory": "ENTITY_TYPE", "extInfo": {}}, "children": []}, {"entityTypeDTO": {"id": 2025032191556852, "name": "Medicine.MedicalTerminology", "nameZh": "医学术语", "inheritedPropertyList": [{"id": 1, "name": "description", "nameZh": "描述", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {}, "defaultTime": false, "subPropertyList": []}, {"id": 2, "name": "id", "nameZh": "实体主键", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {}, "defaultTime": false, "subPropertyList": []}, {"id": 3, "name": "name", "nameZh": "名称", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {}, "defaultTime": false, "subPropertyList": []}], "propertyList": [{"id": 2025032153089647, "name": "desc", "nameZh": "描述", "description": "", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {"valueType": "BASIC_TYPE"}, "defaultTime": false, "subPropertyList": []}, {"id": 2025032184086120, "name": "semanticType", "nameZh": "语义类型", "description": "", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {"valueType": "BASIC_TYPE"}, "defaultTime": false, "subPropertyList": []}], "relations": [], "parentId": 1, "parentName": "Thing", "belongToProject": 1, "entityCategory": "ENTITY_TYPE", "extInfo": {"commonIndex": false}}, "children": []}, {"entityTypeDTO": {"id": 2025032139604428, "name": "Medicine.Vaccine", "nameZh": "疫苗", "inheritedPropertyList": [{"id": 1, "name": "description", "nameZh": "描述", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {}, "defaultTime": false, "subPropertyList": []}, {"id": 2, "name": "id", "nameZh": "实体主键", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {}, "defaultTime": false, "subPropertyList": []}, {"id": 3, "name": "name", "nameZh": "名称", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {}, "defaultTime": false, "subPropertyList": []}], "propertyList": [{"id": 2025032119643360, "name": "semanticType", "nameZh": "语义类型", "description": "", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {"valueType": "BASIC_TYPE"}, "defaultTime": false, "subPropertyList": []}, {"id": 2025032140981267, "name": "desc", "nameZh": "描述", "description": "", "rangeId": 2, "rangeName": "Text", "rangeNameZh": "文本", "propertyCategoryEnum": "BASIC_TYPE", "spreadable": false, "maskType": "NONE", "constraints": [], "extInfo": {"valueType": "BASIC_TYPE"}, "defaultTime": false, "subPropertyList": []}], "relations": [], "parentId": 1, "parentName": "Thing", "belongToProject": 1, "entityCategory": "ENTITY_TYPE", "extInfo": {"commonIndex": false}}, "children": []}]}