import { defineStore } from 'pinia'
import { useLocalStorage } from '@vueuse/core'

export const useUserStore = defineStore('data-asset', () => {
  const pageData = useLocalStorage<any>('pageData', {
    expandedKeys: [], // 展开的节点
    selectedKeys: [], // 选中的节点
    treeData: [], // 树形数据
    dataRef: {}, // 当前选中的节点
    originalTreeData: [], // 原始树形数据
  })
  const metadata = useLocalStorage<any>('metadata', {
    expandedKeys: [], // 展开的节点
    selectedKeys: [], // 选中的节点
    treeData: [], // 树形数据
    dataRef: {}, // 当前选中的节点
    originalTreeData: [], // 原始树形数据
  })
  /**
   * 保存页面数据
   */
  function setPageData(key: string, data: any) {
    pageData.value[key] = data
  }

  /**
   * 保存页面数据
   */
  function setMetadata(key: string, data: any) {
    metadata.value[key] = data
  }

  function clearPageData() {
    pageData.value = {
      expandedKeys: [],
      selectedKeys: [],
      treeData: [],
      dataRef: {},
      originalTreeData: [],
    }
  }

  function clearMetadata() {
    metadata.value = {
      expandedKeys: [],
      selectedKeys: [],
      treeData: [],
      dataRef: {},
      originalTreeData: [],
    }
  }

  function clearAllData() {
    clearPageData()
    clearMetadata()
  }

  return {
    pageData,
    metadata,
    setPageData,
    setMetadata,
    clearPageData,
    clearMetadata,
    clearAllData,
  }
})
