<template>
  <Modal
    :title="title"
    :open="open"
    @ok="handleOk"
    @cancel="handleCancel"
    :confirmLoading="confirmLoading"
  >
    <Form ref="formRef" :model="formState" :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }">
      <FormItem
        label="函数名称"
        name="name"
        :rules="[{ required: true, message: '请输入函数名称' }]"
      >
        <Input v-model:value="formState.name" placeholder="请输入函数名称" />
      </FormItem>
      <FormItem label="版本" name="version">
        <Input v-model:value="formState.version" placeholder="请输入版本号" />
      </FormItem>
      <FormItem label="项目包名" name="projectPackageName">
        <Input v-model:value="formState.projectPackageName" placeholder="请输入项目包名" />
      </FormItem>
      <FormItem label="标签" name="tag">
        <Input v-model:value="formState.tag" placeholder="请输入标签" />
      </FormItem>
      <FormItem
        label="函数代码"
        name="code"
        :rules="[{ required: true, message: '请输入函数代码' }]"
      >
        <VAceEditor
          v-model:value="formState.code!"
          lang="java"
          theme="github"
          :options="editorOptions"
          style="height: 300px; width: 100%"
        />
      </FormItem>
      <FormItem label="状态" name="status">
        <Select v-model:value="formState.status" placeholder="请选择状态">
          <SelectOption value="active">启用</SelectOption>
          <SelectOption value="inactive">禁用</SelectOption>
        </Select>
      </FormItem>
    </Form>
  </Modal>
</template>

<script setup lang="ts">
import { ref, defineProps, defineEmits } from 'vue'
import { Modal, Form, Input, Select, message } from 'ant-design-vue'
import type { FormInstance } from 'ant-design-vue'
import type { FunctionItem } from '../data'
import { addFunction, updateFunction } from '../service'
import { VAceEditor } from 'vue3-ace-editor'
import 'ace-builds/src-noconflict/mode-java' // 导入文本模式
import 'ace-builds/src-noconflict/theme-github' // 导入github主题

const FormItem = Form.Item
const SelectOption = Select.Option

const props = defineProps<{
  title: string
  data?: Partial<FunctionItem>
}>()
const open = defineModel<boolean>('open', { required: true })
const route = useRoute()
const projectId = route.query.projectId
const emit = defineEmits<{
  (e: 'ok', values: Partial<FunctionItem>): void
  (e: 'cancel'): void
}>()

const formRef = ref<FormInstance>()
const confirmLoading = ref(false)
const getOriginalData = () => {
  return {
    name: '',
    version: '',
    projectPackageName: '',
    tag: '',
    status: 'active',
    code: '',
  }
}
const formState = ref<FunctionItem>(getOriginalData())

const editorOptions = ref({
  fontSize: '14px', // 设置字体大小
  showPrintMargin: false, // 是否显示打印边距
})
// 监听项目数据变化
watch(
  () => props.data,
  (data) => {
    console.log('函数 props', props.data)
    if (data) {
      // 复制对象到表单
      formState.value = { ...data }
      if (!data.code) formState.value.code = ''
    } else {
      formState.value = getOriginalData()
    }
  },
  { immediate: true },
)
const isEdit = computed(() => !!props.data)
const handleCreate = async () => {
  try {
    const res = await addFunction({ ...formState.value, projectId: Number(projectId) })
    if (res.code === '000000') {
      message.success('新增成功')
      emit('ok', { ...formState.value })
    } else {
      message.error(res.msg || '新增失败')
    }
  } catch (error) {
    console.error('新增失败:', error)
  } finally {
    confirmLoading.value = false
  }
}
const handleUpdate = async () => {
  try {
    const res = await updateFunction(formState.value)
    if (res.code === '000000') {
      message.success('更新成功')
      emit('ok', { ...formState.value })
    } else {
      message.error(res.msg || '更新失败')
    }
  } catch (error) {
    console.error('更新失败:', error)
  } finally {
    confirmLoading.value = false
  }
}

const handleOk = async () => {
  try {
    console.log('formState', formState.value)
    await formRef.value?.validate()
    confirmLoading.value = true
    if (isEdit.value) {
      await handleUpdate()
    } else {
      await handleCreate()
    }
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    confirmLoading.value = false
  }
}

const handleCancel = () => {
  formRef.value?.resetFields()
  formState.value = getOriginalData()
  open.value = false
  emit('cancel')
}
</script>
