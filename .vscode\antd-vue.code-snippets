{"a-button": {"prefix": "a-button", "description": "antd <a-button> 组件", "body": ["<a-button", "  type=\"${1|default,primary,dashed,danger,link|}\"", "  shape=\"${2|default,circle,round|}\"", "  size=\"${3|small,middle,large|}\"", "  :loading=\"${4|false,true|}\"", "  :disabled=\"${5|false,true|}\"", "  @click=\"${6:handleClick}\"", ">", "  ${7:按钮文本}", "</a-button>"]}, "a-icon": {"prefix": "a-icon", "description": "antd <a-icon> 组件", "body": ["<a-icon", "  type=\"${1:type}\"", "  :theme=\"${2|filled,outlined,twoTone|}\"", "  :two-tone-color=\"${3:#ff0000}\"", "  spin=\"${4|false,true|}\"", "  rotate=\"${5:rotate}\"", "/>"]}, "a-typography": {"prefix": "a-typography", "description": "antd <a-typography> 组件", "body": ["<a-typography>", "  <a-typography-title :level=\"${1:1}\">${2:title}</a-typography-title>", "  <a-typography-paragraph>${3:description}</a-typography-paragraph>", "  <a-typography-text>${4:text}</a-typography-text>", "</a-typography>"]}, "a-divider": {"prefix": "a-divider", "description": "antd <a-divider> 组件", "body": ["<a-divider", "  type=\"${1|horizontal,vertical|}\"", "  orientation=\"${2:left,right,center}\"", "  dashed=${3|false,true|}", "  plain=${4|false,true|}", "  :style=\"{ ${5:color:'black', fontWeight:'bold'} }\"", ">", "  {{ ${6:text} }}", "</a-divider>"]}, "a-row": {"prefix": "a-row", "description": "antd <a-row> 组件", "body": ["<a-row", "  :gutter=\"${1:gutter}\"", "  justify=\"${2|start,end,center,space-around,space-between|}\"", "  align=\"${3|top,middle,bottom|}\"", "  type=\"${4|flex|}\"", "  :wrap=\"${5|true,false|}\"", ">", "  <a-col :span=\"${6:span}\" :offset=\"${7:offset}\">", "    {{ ${8:item.value} }}", "  </a-col>", "</a-row>"]}, "a-col": {"prefix": "a-col", "description": "antd <a-col> 组件", "body": ["<a-col", "  span=\"${1:24}\"", "  offset=\"${2:0}\"", "  push=\"${3:0}\"", "  pull=\"${4:0}\"", "  xs=\"${5:|auto|}\"", "  sm=\"${6:|auto|}\"", "  md=\"${7:|auto|}\"", "  lg=\"${8:|auto|}\"", "  xl=\"${9:|auto|}\"", "  xxl=\"${10:|auto|}\"", ">", "  {{ ${11:content} }}", "</a-col>"]}, "a-layout": {"prefix": "a-layout", "description": "antd <a-layout> 组件", "body": ["<a-layout>", "  <a-layout-header>${1:<PERSON><PERSON>}</a-layout-header>", "  <a-layout-content>", "    ${2:Content}", "  </a-layout-content>", "  <a-layout-footer>${3:Footer}</a-layout-footer>", "</a-layout>"]}, "a-layout-header": {"prefix": "a-layout-header", "description": "antd <a-layout-header> 组件", "body": ["<a-layout-header", "  class='${1:class}'", "  style='${2:style}'", "  :has-sider='${3|false,true|}'", ">", "  ${4:Header content}", "</a-layout-header>"]}, "a-layout-content": {"prefix": "a-layout-content", "description": "antd <a-layout-content> 组件", "body": ["<a-layout-content", "  :style=\"${1:{ padding: '24px 16px', background: '#fff' }}\"", ">", "  ${2:<div>}", "    ${3:内容}", "  ${4:</div>}", "</a-layout-content>"]}, "a-layout-footer": {"prefix": "a-layout-footer", "description": "antd <a-layout-footer> 组件", "body": ["<a-layout-footer", "  :style=\"{ textAlign: '${1:center}' }\"", ">", "  ${2:©2023 Ant Design}", "</a-layout-footer>"]}, "a-layout-sider": {"prefix": "a-layout-sider", "description": "antd <a-layout-sider> 组件", "body": ["<a-layout-sider", "  :collapsible=\"${1|true,false|}\"", "  :collapsed=\"${2:collapsed}\"", "  :width=\"${3:200}\"", "  :theme=\"${4|light,dark|}\"", "  :trigger=\"${5:'>'}\"", ">", "  <a-menu", "    mode=\"${6:inline}\"", "    :default-selected-keys=\"['${7:1}']\"", "    :default-open-keys=\"['${8:sub1}']\"", "  >", "    <a-menu-item key=\"${7:1}\">", "      <a-icon type=\"user\" />", "      <span>nav 1</span>", "    </a-menu-item>", "    <a-menu-item key=\"${9:2}\">", "      <a-icon type=\"video-camera\" />", "      <span>nav 2</span>", "    </a-menu-item>", "    <a-menu-item key=\"${10:3}\">", "      <a-icon type=\"upload\" />", "      <span>nav 3</span>", "    </a-menu-item>", "    <a-sub-menu key=\"${8:sub1}\">", "      <template #title>", "        <span>", "          <a-icon type=\"mail\" />", "          <span>Navigation One</span>", "        </span>", "      </template>", "      <a-menu-item key=\"${11:5}\">Option 5</a-menu-item>", "      <a-menu-item key=\"${12:6}\">Option 6</a-menu-item>", "      <a-menu-item key=\"${13:7}\">Option 7</a-menu-item>", "      <a-menu-item key=\"${14:8}\">Option 8</a-menu-item>", "    </a-sub-menu>", "    <a-sub-menu key=\"${15:sub2}\">", "      <template #title>", "        <span>", "          <a-icon type=\"appstore\" />", "          <span>Navigation Two</span>", "        </span>", "      </template>", "      <a-menu-item key=\"${16:9}\">Option 9</a-menu-item>", "      <a-menu-item key=\"${17:10}\">Option 10</a-menu-item>", "      <a-sub-menu key=\"${18:sub3}\">", "        <template #title>Submenu</template>", "        <a-menu-item key=\"${19:11}\">Option 11</a-menu-item>", "        <a-menu-item key=\"${20:12}\">Option 12</a-menu-item>", "      </a-sub-menu>", "    </a-sub-menu>", "  </a-menu>", "</a-layout-sider>"]}, "a-space": {"prefix": "a-space", "description": "antd <a-space> 组件", "body": ["<a-space", "  :size=\"${1|small,middle,large|}\"", "  :direction=\"${2|horizontal,vertical|}\"", "  :align=\"${3|start,end,center,baseline,stretch|}\"", "  :class=\"${4:class}\"", "  :style=\"${5:style}\"", ">", "  ${6:<a-button>${7:按钮}</a-button>}", "</a-space>"]}, "a-grid": {"prefix": "a-grid", "description": "antd <a-grid> 组件", "body": ["<a-grid", "  :column-count=\"${1:4}\"", "  :gutter=\"${2:[16, 16]}\"", "  :data-source=\"${3:getDataSource}\"", ">", "  <template #renderItem=\"{ item }\">", "    <a-grid-item>", "      {{ item.value }}", "    </a-grid-item>", "  </template>", "</a-grid>"]}, "a-affix": {"prefix": "a-affix", "description": "antd <a-affix> 组件", "body": ["<a-affix", "  :offset-top=\"${1:0}\"", "  :offset-bottom=\"${2:0}\"", ">", "  ${3:<div>固定内容</div>}", "</a-affix>"]}, "a-breadcrumb": {"prefix": "a-breadcrumb", "description": "antd <a-breadcrumb> 组件", "body": ["<a-breadcrumb>", "  <template #separator>", "    ${1:/}", "  </template>", "  <a-breadcrumb-item>${2:Home}</a-breadcrumb-item>", "  <a-breadcrumb-item>${3:Application Center}</a-breadcrumb-item>", "  <a-breadcrumb-item>${4:Application List}</a-breadcrumb-item>", "  <a-breadcrumb-item>${5:An Application}</a-breadcrumb-item>", "</a-breadcrumb>"]}, "a-breadcrumb-item": {"prefix": "a-breadcrumb-item", "description": "antd <a-breadcrumb-item> 组件", "body": ["<a-breadcrumb-item", "  :href='${1:href}'", "  :overlay='${2:overlay}'", "  :replace='${3|false,true|}'", ">", "  {{ ${4:item<PERSON><PERSON><PERSON>} }}", "</a-breadcrumb-item>"]}, "a-dropdown": {"prefix": "a-dropdown", "description": "antd <a-dropdown> 组件", "body": ["<a-dropdown", "  :trigger=\"${1:[\\'click\\']}\"", "  :overlay-visible=\"${2:overlayVisible}\"", "  :placement=\"${3|bottomLeft,bottomRight,topLeft,topRight|}\"", ">", "  <template #overlay>", "    <a-menu>", "      <a-menu-item key=\"1\">", "        {{ ${4:item1} }}", "      </a-menu-item>", "      <a-menu-item key=\"2\">", "        {{ ${5:item2} }}", "      </a-menu-item>", "    </a-menu>", "  </template>", "  <a-button>", "    {{ ${6:text} }}", "    <DownOutlined />", "  </a-button>", "</a-dropdown>"]}, "a-dropdown-button": {"prefix": "a-dropdown-button", "description": "antd <a-dropdown-button> 组件", "body": ["<a-dropdown-button", "  type=\"${1|primary,dashed,default,link,text|}\"", "  :overlay=\"${2:menu}\"", "  :trigger=\"${3|['click'], ['hover'], ['click', 'hover']|}\"", "  placement=\"${4|bottomLeft,bottomCenter,bottomRight,topLeft,topCenter,topRight|}\"", "  :loading=\"${5:false}\"", "  :disabled=\"${6:false}\"", ">", "  ${7:按钮文本}", "</a-dropdown-button>"]}, "a-menu": {"prefix": "a-menu", "description": "antd <a-menu> 组件", "body": ["<a-menu", "  mode=\"${1|horizontal,vertical|}\"", "  theme=\"${2|light,dark|}\"", "  :default-selected-keys=\"${3:['default']}\"", "  :default-open-keys=\"${4:['default']}\"", "  :open-keys=\"${5:openKeys}\"", "  :selectable=\"${6|true,false|}\"", "  :inline-collapsed=\"${7:inlineCollapsed}\"", "  :inline-indent=\"${8:24}\"", ">", "  <a-menu-item key=\"1\">", "    <template #icon><MailOutlined /></template>", "    导航一", "  </a-menu-item>", "  <a-sub-menu key=\"sub1\">", "    <template #icon><AppstoreOutlined /></template>", "    <template #title>导航二</template>", "    <a-menu-item key=\"2\">选项二</a-menu-item>", "    <a-menu-item key=\"3\">选项三</a-menu-item>", "    <a-sub-menu key=\"sub1-2\" title=\"子导航二\">", "      <a-menu-item key=\"4\">选项四</a-menu-item>", "      <a-menu-item key=\"5\">选项五</a-menu-item>", "    </a-sub-menu>", "  </a-sub-menu>", "  <a-menu-item key=\"6\">", "    <template #icon><TeamOutlined /></template>", "    导航三", "  </a-menu-item>", "  <a-menu-item key=\"7\">", "    <template #icon><FileOutlined /></template>", "    导航四", "  </a-menu-item>", "</a-menu>"]}, "a-menu-item": {"prefix": "a-menu-item", "description": "antd <a-menu-item> 组件", "body": ["<a-menu-item", "  :key=\"${1:key}\"", "  :title=\"${2:title}\"", ">", "  {{ ${3:itemValue} }}", "</a-menu-item>"]}, "a-menu-divider": {"prefix": "a-menu-divider", "description": "antd <a-menu-divider> 组件", "body": ["<a-menu-divider />"]}, "a-menu-sub-menu": {"prefix": "a-menu-sub-menu", "description": "antd <a-menu-sub-menu> 组件", "body": ["<a-menu-sub-menu", "  :key=\"${1:key}\"", "  title=\"{{ ${2:title} }}\"", ">", "  <template #icon>", "    <${3:IconComponent} />", "  </template>", "  <a-menu-item :key=\"${4:itemKey}\">", "    {{ ${5:itemTitle} }}", "  </a-menu-item>", "  <!-- 其他子菜单项 -->", "</a-menu-sub-menu>"]}, "a-menu-item-group": {"prefix": "a-menu-item-group", "description": "antd <a-menu-item-group> 组件", "body": ["<a-menu-item-group", "  key=\"${1:key}\"", "  title=\"${2:title}\"", ">", "  <a-menu-item key=\"${3:itemKey}\">{{ ${4:itemValue} }}</a-menu-item>", "</a-menu-item-group>"]}, "a-pagination": {"prefix": "a-pagination", "description": "antd <a-pagination> 组件", "body": ["<a-pagination", "  :current=\"${1:currentPage}\"", "  :total=\"${2:total}\"", "  :page-size=\"${3:pageSize}\"", "  show-size-changer=\"${4|false,true|}\"", "  :page-size-options=\"${5:['10', '20', '30', '40']}", "  @change=\"${6:onPageChange}\"", "  @showSizeChange=\"${7:onShowSizeChange}\"", "/>"]}, "a-steps": {"prefix": "a-steps", "description": "antd <a-steps> 组件", "body": ["<a-steps", "  :current=\"${1:currentStep}\"", "  direction=\"${2|horizontal,vertical|}\"", "  label-placement=\"${3|horizontal,vertical|}\"", "  size=\"${4|default,small|}\"", "  :status=\"${5|wait,process,finish,error|}\"", ">", "  <a-step", "    v-for=\"(step, index) in ${6:steps}\"", "    :key=\"index\"", "    title=\"{{ step.title }}\"", "    description=\"{{ step.description }}\"", "    :status=\"step.status\"", "  />", "</a-steps>"]}, "a-step": {"prefix": "a-step", "description": "antd <a-step> 组件", "body": ["<a-step", "  :title=\"${1:title}\"", "  :description=\"${2:description}\"", "  :status=\"${3|wait,process,finish,error|}\"", "  :icon=\"${4:icon}\"", "  :disabled=\"${5|false,true|}\"", "  :onClick=\"${6:onClick}\"", "/>"]}, "a-auto-complete": {"prefix": "a-auto-complete", "description": "antd <a-auto-complete> 组件", "body": ["<a-auto-complete", "  v-model:value=\"${1:value}\"", "  :data-source=\"${2:getDataSource}\"", "  placeholder=\"${3:输入关键字}\"", "  :filter-option=\"${4|false,true|}\"", ">", "  <template #option=\"{ item }\">", "    {{ item.value }}", "  </template>", "</a-auto-complete>"]}, "a-cascader": {"prefix": "a-cascader", "description": "antd <a-cascader> 组件", "body": ["<a-cascader", "  v-model:value=\"selectedOptions\"", "  :options=\"getOptions\"", "  placeholder=\"${1:请选择}\"", "  change-on-select=\"${2|false,true|}\"", "  expand-trigger=\"${3:hover,click}\"", "  size=\"${4|default,large,small}\"", "  disabled=\"${5|false,true|}\"", "  allow-clear=\"${6|true,false|}\"", "  show-search=\"${7|false,true|}\"", " />"]}, "a-checkbox": {"prefix": "a-checkbox", "description": "antd <a-checkbox> 组件", "body": ["<a-checkbox", "  v-model:checked='${1:checked}'", "  :disabled='${2|false,true|}'", "  :indeterminate='${3|false,true|}'", "  name='${4:name}'", ">", "  ${5:Checkbox Label}", "</a-checkbox>"]}, "a-checkbox-group": {"prefix": "a-checkbox-group", "description": "antd <a-checkbox-group> 组件", "body": ["<a-checkbox-group", "  v-model:value=\"${1:value}\"", "  :options=\"${2:getOptions}\"", "  :name=\"${3:name}\"", "  :disabled=\"${4|false,true|}\"", "  :defaultValue=\"${5:defaultValue}\"", "  @change=\"${6:handleChange}\"", ">", "</a-checkbox-group>"]}, "a-date-picker": {"prefix": "a-date-picker", "description": "antd <a-date-picker> 组件", "body": ["<a-date-picker", "  v-model:value=\"${1:value}\"", "  :show-time=\"${2|false,true|}\"", "  format=\"${3:YYYY-MM-DD}\"", "  placeholder=\"${4:请选择日期}\"", "  :disabled-date=\"${5:getDisabledDate}\"", "  :disabled-time=\"${6:getDisabledTime}\"", "  :show-now=\"${7|false,true|}\"", "  :allow-clear=\"${8|true,false|}\"", "  :open=\"${9|false,true|}\"", "  :dropdown-class-name=\"${10:dropdownClassName}\"", "  :get-calendar-container=\"${11:getCalendarContainer}\"", "  :locale=\"${12:locale}\"", "  :input-read-only=\"${13|false,true|}\"", "  :suffix-icon=\"${14:suffixIcon}\"", "  :clear-icon=\"${15:clearIcon}\"", "  :separator=\"${16:separator}\"", "  :ranges=\"${17:ranges}\"", "  :show-week-number=\"${18|false,true|}\"", "  :picker=\"${19|date,week,month,quarter,year|}\"", "  :input-render=\"${20:inputRender}\"", "  :render-extra-footer=\"${21:renderExtraFooter}\"", "  :show-time-picker=\"${22|false,true|}\"", "/>"]}, "a-time-picker": {"prefix": "a-time-picker", "description": "antd <a-time-picker> 组件", "body": ["<a-time-picker", "  v-model:value=\"${1:value}\"", "  format=\"${2:HH:mm}\"", "  :disabled=\"${3|false,true|}\"", "  :allow-clear=\"${4|true,false|}\"", "  :show-now=\"${5|false,true|}\"", "  :input-read-only=\"${6|false,true|}\"", "  :placeholder=\"${7:'Select time'}\"", "/>"]}, "a-form": {"prefix": "a-form", "description": "antd <a-form> 组件", "body": ["<a-form", "  :model=\"${1:form}\"", "  :rules=\"${2:rules}\"", "  :label-col=\"{ span: ${3:6} }\"", "  :wrapper-col=\"{ span: ${4:18} }\"", "  layout=\"${5|horizontal,vertical,inline|}\"", "  :colon=\"${6:true}\"", "  :label-align=\"${7|left,right|}\"", "  :validate-trigger=\"${8|change,blur|}\"", "  :auto-complete=\"${9|off,on|}\"", "  @finish=\"${10:onFinish}\"", "  @finish-failed=\"${11:onFinishFailed}\"", ">", "  <a-form-item", "    label=\"${12:Field Label}\"", "    name=\"${13:fieldName}\"", "  >", "    <a-input v-model:value=\"${14:form.fieldName}\" placeholder=\"${15:Please input}\" />", "  </a-form-item>", "</a-form>"]}, "a-form-item": {"prefix": "a-form-item", "description": "antd <a-form-item> 组件", "body": ["<a-form-item", "  label=\"${1:Label}\"", "  name=\"${2:name}\"", "  :rules=\"${3:rules}\"", "  :colon=\"${4|true,false|}\"", "  :has-feedback=\"${5|false,true|}\"", "  :validate-status=\"${6:validateStatus}\"", ">", "  <${7:a-input}>${8:<template #prefix>Prefix</template>}", "    <${7} v-model:value=\"${9:value}\" ${10:placeholder=\"\"} />", "  </${7}>", "</a-form-item>"]}, "a-input": {"prefix": "a-input", "description": "antd <a-input> 组件", "body": ["<a-input", "  v-model:value=\"${1:value}\"", "  placeholder=\"${2:请输入内容}\"", "  :bordered=\"${3|true,false|}\"", "  :allow-clear=\"${4|false,true|}\"", "  :disabled=\"${5|false,true|}\"", "  :readonly=\"${6|false,true|}\"", "  :size=\"${7|default,large,small|}\"", "  :prefix=\"${8:prefix}\"", "  :suffix=\"${9:suffix}\"", "  :max-length=\"${10}\"", "  :show-count=\"${11|false,true|}\"", "  @change=\"${12:handleChange}\"", "  @focus=\"${13:handleFocus}\"", "  @blur=\"${14:handleBlur}\"", "  @press-enter=\"${15:handlePressEnter}\"", "/>"]}, "a-input-number": {"prefix": "a-input-number", "description": "antd <a-input-number> 组件", "body": ["<a-input-number", "  v-model:value=\"${1:value}\"", "  :min=\"${2:min}\"", "  :max=\"${3:max}\"", "  :step=\"${4:step}\"", "  :precision=\"${5:precision}\"", "  :decimalSeparator=\"${6:.}\"", "  :formatter=\"${7:formatter}\"", "  :parser=\"${8:parser}\"", "  :disabled=\"${9|false,true|}\"", "  :readonly=\"${10|false,true|}\"", "  :size=\"${11|default,large,small|}\"", "  :placeholder=\"'${12:请输入数字}'\"", "  :controls=\"${13|true,false|}\"", "  :keyboard=\"${14|true,false|}\"", "  :status=\"${15|''success,'warning,'error|}\"", "  :inputReadOnly=\"${16|false,true|}\"", "  :useGrouping=\"${17|true,false|}\"", "  :groupSeparator=\"${18:,}\"", "/>"]}, "a-input-search": {"prefix": "a-input-search", "description": "antd <a-input-search> 组件", "body": ["<a-input-search", "  v-model:value=\"${1:value}\"", "  placeholder=\"${2:请输入内容}\"", "  allow-clear=\"${3|false,true|}\"", "  enter-button=\"${4|false,true,'搜索'|}\"", "  :loading=\"${5|false,true|}\"", "  @search=\"${6:onSearch}\"", "  size=\"${7|default,large,small|}\"", "  :disabled=\"${8|false,true|}\"", "/>"]}, "a-input-password": {"prefix": "a-input-password", "description": "antd <a-input-password> 组件", "body": ["<a-input-password", "  v-model:value=\"${1:value}\"", "  placeholder=\"${2:请输入密码}\"", "  :visibility-toggle=\"${3|true,false|}\"", "  :password-char=\"${4:*}\"", "  :strength-validator=\"${5:validateStrength}\"", "  :input-props=\"${6:{}}\"", "  @change=\"${7:onPasswordChange}\"", "/>"]}, "a-input-textarea": {"prefix": "a-input-textarea", "description": "antd <a-input-textarea> 组件", "body": ["<a-input-textarea", "  v-model:value=\"${1:value}\"", "  :auto-size=\"${2|{ minRows: 2, maxRows: 6 },false,true|}\"", "  :bordered=\"${3|true,false|}\"", "  :disabled=\"${4|false,true|}\"", "  :readonly=\"${5|false,true|}\"", "  :allow-clear=\"${6|false,true|}\"", "  placeholder=\"${7:请输入内容}\"", "  :maxlength=\"${8:100}\"", "/>"]}, "a-mentions": {"prefix": "a-mentions", "description": "antd <a-mentions> 组件", "body": ["<a-mentions", "  v-model:value=\"${1:value}\"", "  :data-source=\"${2:getDataSource}\"", "  prefix=\"${3:@}\"", "  :split=\"${4:,}\"", "  :loading=\"${5|false,true|}\"", "  :notFoundContent=\"${6:'Not Found'}\"", ">", "  ${7}", "</a-mentions>"]}, "a-radio": {"prefix": "a-radio", "description": "antd <a-radio> 组件", "body": ["<a-radio v-model:checked='${1:checked}' value='${2:value}'>", "  ${3:选项文本}", "</a-radio>"]}, "a-radio-group": {"prefix": "a-radio-group", "description": "antd <a-radio-group> 组件", "body": ["<a-radio-group", "  v-model:value=\"${1:value}\"", "  :options=\"${2:options}\"", "  :disabled=\"${3|false,true|}\"", "  :button-style=\"${4|outline,solid|}\"", "  name=\"${5}\"", ">", "</a-radio-group>"]}, "a-radio-button": {"prefix": "a-radio-button", "description": "antd <a-radio-button> 组件", "body": ["<a-radio-button", "  v-model:value=${1:value}", "  value=${2:item.value}", "  style=${3:style}", "  class=${4:class}", "  disabled=${5|false,true|}", "  name=${6:name}", ">", "  {{ ${7:item.label} }}", "</a-radio-button>"]}, "a-rate": {"prefix": "a-rate", "description": "antd <a-rate> 组件", "body": ["<a-rate", "  v-model:value=\"${1:value}\"", "  :count=\"${2:5}\"", "  :allow-clear=\"${3|true,false|}\"", "  :allow-half=\"${4|false,true|}\"", "  :tooltips=\"${5:[\"terrible\", \"bad\", \"normal\", \"good\", \"wonderful\"]}\"", "  :disabled=\"${6|false,true|}\"", "  :character=\"${7:<StarOutlined/>}\"", "  :character-render=\"${8:(node, props) => <StarOutlined {...props} />}\"", "  :auto-focus=\"${9|false,true|}\"", "  :focusable=\"${10|false,true|}\"", "  :value=\"${11:value}\"", "/>"]}, "a-select": {"prefix": "a-select", "description": "antd <a-select> 组件", "body": ["<a-select", "  v-model:value='${1:value}'", "  :options='${2:getOptions}'", "  allow-clear='${3|false,true|}'", "  show-search='${4|false,true|}'", "  placeholder='${5:请选择}'", "  disabled='${6|false,true|}'", "  not-found-content='${7:未找到}'", "  :filter-option='${8:filterOption}'", "  :dropdown-style='${9:dropdownStyle}'", ">", "</a-select>"]}, "a-select-option": {"prefix": "a-select-option", "description": "antd <a-select-option> 组件", "body": ["<a-select-option", "  v-model:value=\"${1:value}\"", "  :key=\"${2:key}\"", "  :value=\"${3:item.value}\"", ">", "  {{ ${4:item.label} }}", "</a-select-option>"]}, "a-select-opt-group": {"prefix": "a-select-opt-group", "description": "antd <a-select-opt-group> 组件", "body": ["<a-select-opt-group label=\"${1:分组标题}\">", "  <a-select-option value=\"${2:option1}\">", "    {{ ${3:item1} }}", "  </a-select-option>", "  <a-select-option value=\"${4:option2}\">", "    {{ ${5:item2} }}", "  </a-select-option>", "</a-select-opt-group>"]}, "a-slider": {"prefix": "a-slider", "description": "antd <a-slider> 组件", "body": ["<a-slider", "  v-model:value=${1:value}", "  :min=${2:min}", "  :max=${3:max}", "  :step=${4:step}", "  :marks=${5:marks}", "  :included=${6|true,false|}", "  :range=${7|false,true|}", "  :tooltip-visible=${8|false,true|}", "  :tooltip-formatter=${9:formatter}", "  :disabled=${10|false,true|}", "/>"]}, "a-switch": {"prefix": "a-switch", "description": "antd <a-switch> 组件", "body": ["<a-switch", "  v-model:checked=\"${1:checked}\"", "  :checked-children=\"${2|开,ON,1|}\"", "  :un-checked-children=\"${3|关,OFF,0|}\"", "  :disabled=\"${4|false,true|}\"", "  :loading=\"${5|false,true|}\"", "  @change=\"${6:handleChange}\"", "/>"]}, "a-transfer": {"prefix": "a-transfer", "description": "antd <a-transfer> 组件", "body": ["<a-transfer", "  v-model:value=\"${1:selectedKeys}\"", "  :data-source=\"${2:getDataSource}\"", "  show-search=\"${3|false,true|}\"", "  :titles=\"${4:['Source', 'Target']}\"", "  :operations=\"${5:['>>', '<<']}\"", "  :render=\"${6:(item) => item.title}\"", "  :target-keys=\"${7:targetKeys}\"", "  @change=\"${8:handleChange}\"", "  @selectChange=\"${9:handleSelectChange}\"", "  :disabled=\"${10|false,true|}\"", "  :list-style=\"${11:{}}\"", "/>"]}, "a-tree-select": {"prefix": "a-tree-select", "description": "antd <a-tree-select> 组件", "body": ["<a-tree-select", "  v-model:value=\"${1:value}\"", "  :tree-data=\"${2:getTreeData}\"", "  placeholder=\"${3:请选择}\"", "  :allow-clear=\"${4|false,true|}\"", "  :show-arrow=\"${5|true,false|}\"", "  :disabled=\"${6|false,true|}\"", "  :multiple=\"${7|false,true|}\"", "  :max-tag-count=\"${8:0}\"", "  :dropdown-style=\"${9:{height: '400px', maxHeight: '400px'}}\"", "  :dropdown-class-name=\"${10:dropdownClassName}\"", "  :auto-clear-search-value=\"${11|true,false|}\"", "  :show-search=\"${12|true,false|}\"", "  :search-placeholder=\"${13:搜索}\"", "  :not-found-content=\"${14:'未找到'}\"", "  :virtual=\"${15|false,true|}\"", "  :tree-checkable=\"${16|false,true|}\"", "  :tree-default-expand-all=\"${17|false,true|}\"", "  :tree-node-filter-prop=\"${18:'title'}\"", "  :tree-line=\"${19|false,true|}\"", "  :tree-props=\"${20:{showIcon: false, showLine: false}}\"", ">", "  <template #title=\"{ node }\">", "    {{ node.title }}", "  </template>", "</a-tree-select>"]}, "a-upload": {"prefix": "a-upload", "description": "antd <a-upload> 组件", "body": ["<a-upload", "  v-model:file-list=\"${1:fileList}\"", "  :action=\"${2:uploadAction}\"", "  :headers=\"${3:headers}\"", "  :data=\"${4:data}\"", "  :name=\"${5:'file'}\"", "  :before-upload=\"${6:beforeUpload}\"", "  :show-upload-list=\"${7|true,false|}\"", ">", "  <template #default>", "    <a-button>", "      <upload-outlined></upload-outlined>", "      点击上传", "    </a-button>", "  </template>", "</a-upload>"]}, "a-avatar": {"prefix": "a-avatar", "description": "antd <a-avatar> 组件", "body": ["<a-avatar", "  :shape=\"${1|circle,square|}\"", "  :size=\"${2|large,default,small,number|}\"", "  :src=\"${3:imageUrl}\"", "  :icon=\"${4:UserOutlined}\"", "  :alt=\"${5:avatarAlt}\"", "  :loading=\"${6|false,true|}\"", "  :gap=\"${7:number}\"", ">", "  {{ ${8:avatar<PERSON>ext} }}", "</a-avatar>"]}, "a-badge": {"prefix": "a-badge", "description": "antd <a-badge> 组件", "body": ["<a-badge", "  :count=\"${1:count}\"", "  :show-zero=\"${2|false,true|}\"", "  :overflow-count=\"${3:99}\"", "  :dot=\"${4|false,true|}\"", "  :offset=\"[${5:0}, ${6:0}]\"", "  :status=\"${7|success,processing,error,default,warning|}\"", "  :text=\"${8:text}\"", "  :number-style=\"{ ${9:backgroundColor: '#fff', color: '#999'} }\"", ">", "  ${10:<a href='#'>链接</a>}", "</a-badge>"]}, "a-calendar": {"prefix": "a-calendar", "description": "antd <a-calendar> 组件", "body": ["<a-calendar", "  :value=\"selectedDate\"", "  :locale=\"locale\"", "  :fullscreen=\"${1|true,false|}\"", "  :header-render=\"customHeader\"", "  @panelChange=\"handlePanelChange\"", "  @select=\"handleSelect\"", ">", "  <template #dateCellRender=\"{ current, value }\">", "    <div style=\"background: #e6f7ff; border-radius: 50%; text-align: center;\">", "      {{ current.getDate() }}", "    </div>", "  </template>", "  <template #monthCellRender=\"{ current, value }\">", "    <div style=\"background: #fff1b8; padding: 8px 0; text-align: center;\">", "      {{ current.format('MM') }}", "    </div>", "  </template>", "</a-calendar>"]}, "a-card": {"prefix": "a-card", "description": "antd <a-card> 组件", "body": ["<a-card", "  title=\"${1:title}\"", "  :bordered=\"${2|true,false|}\"", "  :hoverable=\"${3|true,false|}\"", "  :loading=\"${4|false,true|}\"", "  :type=\"${5|inner,outter|}\"", "  :cover=\"${6:getCoverImage}\"", ">", "  <template #actions>", "    <a-button>${7:Action}</a-button>", "  </template>", "  <a-card-meta", "    title=\"${8:item.title}\"", "    description=\"${9:item.description}\"", "  >", "    <template #avatar>", "      <a-avatar src=\"${10:item.avatar}\" />", "    </template>", "  </a-card-meta>", "</a-card>"]}, "a-card-meta": {"prefix": "a-card-meta", "description": "antd <a-card-meta> 组件", "body": ["<a-card-meta", "  :title='${1:item.title}'", "  :avatar='${2:<a-avatar src='${3:item.avatarUrl}' />}'", "  :description='${4:item.description}'", ">"]}, "a-card-grid": {"prefix": "a-card-grid", "description": "antd <a-card-grid> 组件", "body": ["<a-card-grid>", "  <a-card title=\"${1:item.title}\" :bordered=\"${2:true}\" style=\"width: 25%;\">", "    {{ ${3:item.description} }}", "  </a-card>", "</a-card-grid>"]}, "a-carousel": {"prefix": "a-carousel", "description": "antd <a-carousel> 组件", "body": ["<a-carousel", "  :after-change=${1:onAfterChange}", "  :before-change=${2:onBeforeChange}", "  autoplay=${3|false,true|}", "  dots=${4|false,true|}", "  effect=${5|scrollx,fade|}", "  :vertical=${6|false,true|}", ">", "  <template #default>", "    <div v-for=\"item in ${7:dataSource}\" :key=\"item.${8:key}\" class=\"carousel-item\">", "      {{ item.${9:value} }}", "    </div>", "  </template>", "</a-carousel>"]}, "a-collapse": {"prefix": "a-collapse", "description": "antd <a-collapse> 组件", "body": ["<a-collapse", "  :bordered=\"${1|true,false|}\"", "  :default-active-key=\"${2:['1']}\"", "  :expand-icon-position=\"${3|start,end|}\"", "  :accordion=\"${4|false,true|}\"", ">", "  <a-collapse-panel header=\"${5:This is panel header}\" key=\"${6:1}\">", "    <p>${7:This is the content of the panel}</p>", "  </a-collapse-panel>", "</a-collapse>"]}, "a-collapse-panel": {"prefix": "a-collapse-panel", "description": "antd <a-collapse-panel> 组件", "body": ["<a-collapse-panel", "  header=\"${1:面板标题}\"", "  :key=\"${2:key}\"", "  :show-arrow=\"${3|true,false|}\"", "  :force-render=\"${4|false,true|}\"", "  :disabled=\"${5|false,true|}\"", ">", "  ${6:面板内容}", "</a-collapse-panel>"]}, "a-descriptions": {"prefix": "a-descriptions", "description": "antd <a-descriptions> 组件", "body": ["<a-descriptions", "  title=\"${1:title}\"", "  bordered=\"${2|false,true|}\"", "  column=\"${3:3}\"", "  :data-source=\"${4:getDataSource}\"", "  layout=\"${5|horizontal,vertical|}\"", ">", "  <template #item=\"{ item }\">", "    <a-descriptions-item", "      :label=\"item.label\"", "      :span=\"item.span\"", "    >", "      {{ item.value }}", "    </a-descriptions-item>", "  </template>", "</a-descriptions>"]}, "a-descriptions-item": {"prefix": "a-descriptions-item", "description": "antd <a-descriptions-item> 组件", "body": ["<a-descriptions-item", "  label=\"${1:itemLabel}\"", "  :span=\"${2:1}\"", ">", "  {{ ${3:itemValue} }}", "</a-descriptions-item>"]}, "a-empty": {"prefix": "a-empty", "description": "antd <a-empty> 组件", "body": ["<a-empty", "  :image=\"${1:image}\"", "  :image-style=\"${2:imageStyle}\"", "  description=\"${3:描述}\"", ">", "  <template #default>", "    ${4:<a-button>创建</a-button>}", "  </template>", "</a-empty>"]}, "a-image": {"prefix": "a-image", "description": "antd <a-image> 组件", "body": ["<a-image", "  :width='${1:200}'", "  :height='${2:200}'", "  :src='${3:imageSrc}'", "  :preview='${4:true}'", "  alt='${5:imageAlt}'", "  :fallback='${6:fallbackSrc}'", "  :placeholder='${7:placeholderSrc}'", "/>"]}, "a-list": {"prefix": "a-list", "description": "antd <a-list> 组件", "body": ["<a-list", "  bordered=\"${1|false,true|}\"", "  item-layout=\"${2|horizontal,vertical|}\"", "  size=\"${3|default,small|}\"", "  :data-source=\"${4:getDataSource}\"", "  :pagination=\"${5:pagination}\"", "  :loading=\"${6|false,true|}\"", ">", "  <template #renderItem=\"{ item }\">", "    <a-list-item>", "      <a-card title=\"{{ item.title }}\">", "        {{ item.description }}", "      </a-card>", "    </a-list-item>", "  </template>", "</a-list>"]}, "a-list-item": {"prefix": "a-list-item", "description": "antd <a-list-item> 组件", "body": ["<a-list-item>", "  <template #actions>", "    <span>${1:Action 1}</span>", "    <span>${2:Action 2}</span>", "  </template>", "  <a-list-item-meta", "    :title=\"'${3:item.title}'\"", "    :description=\"'${4:item.description}'\"", "  >", "    <template #avatar>", "      <a-avatar>${5:item.avatar}</a-avatar>", "    </template>", "  </a-list-item-meta>", "  ${6:item.content}", "</a-list-item>"]}, "a-list-item-meta": {"prefix": "a-list-item-meta", "description": "antd <a-list-item-meta> 组件", "body": ["<a-list-item-meta", "  :avatar=\"{ ${1:url: 'avatarUrl'} }\"", "  :title=\"{ ${2:text: 'itemTitle'} }\"", "  :description=\"{ ${3:text: 'itemDescription'} }\"", "/>"]}, "a-popover": {"prefix": "a-popover", "description": "antd <a-popover> 组件", "body": ["<a-popover", "  v-model:visible=\"${1:visible}\"", "  title=\"${2:Title}\"", "  content=\"${3:Content}\"", "  trigger=\"${4|click,hover,focus,contextmenu|}\"", "  placement=\"${5|top,tl,tr,left,center,lr,right,rl,rr,bottom,bl,br|}\"", "  :overlay-style=\"${6:overlayStyle}\"", "  :overlay-class-name=\"${7:overlayClassName}\"", "  :overlay-inner-style=\"${8:overlayInnerStyle}\"", "  :get-popup-container=\"${9:getPopupContainer}\"", ">", "  <template #default>", "    <a-button>${10:Click me}</a-button>", "  </template>", "</a-popover>"]}, "a-statistic": {"prefix": "a-statistic", "description": "antd <a-statistic> 组件", "body": ["<a-statistic", "  title=\"${1:title}\"", "  :value=\"${2:value}\"", "  :prefix=\"${3:prefix}\"", "  :suffix=\"${4:suffix}\"", "  :value-style=\"${5:valueStyle}\"", "  :loading=\"${6|false,true|}\"", "  :decimal-separator=\"${7:decimalSeparator}\"", "  :group-separator=\"${8:groupSeparator}\"", "  :precision=\"${9:precision}\"", "  :value-from=\"${10:valueFrom}\"", "  :value-to=\"${11:valueTo}\"", "  :count-duration=\"${12:countDuration}\"", "  :formatter=\"${13:formatter}\"", "  :show-title=\"${14|true,false|}\"", "  :value-template=\"${15:valueTemplate}\"", "/>"]}, "a-table": {"prefix": "a-table", "description": "antd <a-table> 组件", "body": ["<a-table", "  :columns=\"${1:getColumns}\"", "  :data-source=\"${2:getDataSource}\"", "  row-key=\"${3:id}\"", "  :pagination=\"${4:pagination}\"", "  :loading=\"${5|false,true|}\"", "  :bordered=\"${6|false,true|}\"", "  :scroll=\"{ x: '${7:1300px}', y: '${8:300px}' }\"", ">", "  <template #bodyCell=\"{ column, record }\">", "    <template v-if=\"column.dataIndex === '${9:action}'\">", "      <span>", "        <a>操作一</a>", "        <a>操作二</a>", "      </span>", "    </template>", "  </template>", "</a-table>"]}, "a-tabs": {"prefix": "a-tabs", "description": "antd <a-tabs> 组件", "body": ["<a-tabs", "  :active-key=${1:activeKey}", "  :default-active-key=${2:'1'}", "  :tab-list=${3:[{key: '1', tab: 'Tab 1'}, {key: '2', tab: 'Tab 2'}]}", "  @change=${4:(key) => {}}", ">", "  <template #tabBarExtraContent>", "    <a-button>${5:Extra Action}</a-button>", "  </template>", "  <a-tab-pane", "    v-for=\"pane in ${3}\"", "    :key=\"pane.key\"", "    :tab=\"pane.tab\"", "  >", "    {{ pane.content }}", "  </a-tab-pane>", "</a-tabs>"]}, "a-tab-pane": {"prefix": "a-tab-pane", "description": "antd <a-tab-pane> 组件", "body": ["<a-tab-pane", "  key=\"$1\"", "  tab=\"$2\"", ">", "  {{ $3 }}", "</a-tab-pane>"]}, "a-tag": {"prefix": "a-tag", "description": "antd <a-tag> 组件", "body": ["<a-tag", "  :color=\"${1:tagColor}\"", "  :closable=\"${2:false}\"", "  @close=\"${3:onClose}\"", ">", "  {{ ${4:text} }}", "</a-tag>"]}, "a-timeline": {"prefix": "a-timeline", "description": "antd <a-timeline> 组件", "body": ["<a-timeline", "  :pending=\"${1|false,true|}\"", "  pending-dot=\"${2:<a-icon type='loading' style='font-size: 24px;' />}\"", "  reverse=\"${3|false,true|}\"", "  mode=\"${4|left,right,alternate|}\"", ">", "  <a-timeline-item", "    v-for=\"item in ${5:timelineItems}\"", "    :key=\"item.key\"", "    :color=\"item.color\"", "  >", "    <p>{{ item.label }}</p>", "    <p>{{ item.content }}</p>", "  </a-timeline-item>", "</a-timeline>"]}, "a-timeline-item": {"prefix": "a-timeline-item", "description": "antd <a-timeline-item> 组件", "body": ["<a-timeline-item", "  :key=\"${1:key}\"", "  color=\"${2|blue,green,red,grey|#color}\"", "  dot=\"${3|<span></span>,|}\"", "  label=\"{{ ${4:label} }}\"", "  :pending=\"${5|false,true|}\"", ">", "  ${6:内容}", "</a-timeline-item>"]}, "a-tooltip": {"prefix": "a-tooltip", "description": "antd <a-tooltip> 组件", "body": ["<a-tooltip", "  :title=\"${1:title}\"", "  :placement=\"${2|top,topLeft,topRight,bottom,bottomLeft,bottomRight,left,leftTop,leftBottom,right,rightTop,rightBottom|}\"", "  :overlay-class-name=\"${3:overlayClassName}\"", "  :overlay-style=\"${4:overlayStyle}\"", "  :trigger=\"${5|hover,focus,click,contextmenu|}\"", "  :mouse-enter-delay=\"${6:mouseEnterDelay}\"", "  :mouse-leave-delay=\"${7:mouseLeaveDelay}\"", "  :auto-adjustOverflow=\"${8|true,false|}\"", "  :get-popup-container=\"${9:getPopupContainer}\"", ">", "  ${10:<template #default>", "  <a-button>${11:Hover me}", "  </template>}", "</a-tooltip>"]}, "a-tree": {"prefix": "a-tree", "description": "antd <a-tree> 组件", "body": ["<a-tree", "  :tree-data=${1:treeData}", "  :auto-expand-parent=${2:true}", "  :default-expanded-keys=${3:['0-0-0']}", "  :default-selected-keys=${4:['0-0-0']}", "  :default-checked-keys=${5:['0-0-0']}", "  :checkable=${6:true}", "  :show-icon=${7:false}", "  :show-line=${8:false}", "  :block-node=${9:false}", "  :draggable=${10:false}", "  @expand=${11:handleExpand}", "  @select=${12:handleSelect}", "  @check=${13:handleCheck}", ">", "  <template #title='{ ${14:title} }'>", "    <span>{{ ${14:title} }}</span>", "  </template>", "</a-tree>"]}, "a-alert": {"prefix": "a-alert", "description": "antd <a-alert> 组件", "body": ["<a-alert", "  :message=\"${1:message}\"", "  :description=\"${2:description}\"", "  :type=\"${3|info,success,warning,error|}\"", "  :show-icon=\"${4:|true,false|}\"", "  :closable=\"${5:|true,false|}\"", "  :after-close=\"${6:handleAfterClose}\"", "  :banner=\"${7:|false,true|}\"", "/>"]}, "a-drawer": {"prefix": "a-drawer", "description": "antd <a-drawer> 组件", "body": ["<a-drawer", "  title=\"${1:Drawer Title}\"", "  :width=\"${2:320}\"", "  :placement=\"${3|right,left,top,bottom|}\"", "  :closable=\"${4:true}\"", "  :mask-closable=\"${5:true}\"", "  :visible=\"${6:visible}\"", "  @close=\"${7:onClose}\"", ">", "  <p>${8:Some contents...}</p>", "</a-drawer>"]}, "a-message": {"prefix": "a-message", "description": "antd <a-message> 组件", "body": ["<a-message", "  type=\"${1|success,error,info,warning|}\"", "  show-icon=\"${2|false,true|}\"", "  :closable=\"${3|false,true|}\"", "  :duration=\"${4:3}\"", ">", "  {{ ${5:message} }}", "</a-message>"]}, "a-modal": {"prefix": "a-modal", "description": "antd <a-modal> 组件", "body": ["<a-modal", "  v-model:visible=\"${1:isVisible}\"", "  title=\"${2:Modal Title}\"", "  :width=\"${3:520}\"", "  :confirm-loading=\"${4:confirmLoading}\"", "  @ok=\"${5:handleOk}\"", "  @cancel=\"${6:handleCancel}\"", ">", "  <p>${7:Some contents...}</p>", "</a-modal>"]}, "a-notification": {"prefix": "a-notification", "description": "antd <a-notification> 组件", "body": ["<a-notification", "  :message=\"${1:message}\"", "  :description=\"${2:description}\"", "  :duration=\"${3:4.5}\"", "  :placement=\"${4|topLeft,topRight,bottomLeft,bottomRight|}\"", "  :bottom=\"${5:8}\"", "  :top=\"${6:10}\"", "  :get-container=\"${7:() => document.body}\"", "  :close-icon=\"${8:<icon type='close' />}\"", ">"]}, "a-popconfirm": {"prefix": "a-popconfirm", "description": "antd <a-popconfirm> 组件", "body": ["<a-popconfirm", "  title=\"${1:您确定要删除吗？}\"", "  :visible=\"${2:visible}\"", "  @confirm=\"${3:onConfirm}\"", "  @cancel=\"${4:onCancel}\"", "  ok-text=\"${5:确定}\"", "  cancel-text=\"${6:取消}\"", ">", "  <template #icon>", "    <${7:a-exclamation-circle} />", "  </template>", "  <a-button>${8:删除}</a-button>", "</a-popconfirm>"]}, "a-progress": {"prefix": "a-progress", "description": "antd <a-progress> 组件", "body": ["<a-progress", "  :percent=\"${1:50}\"", "  :show-info=\"${2|true,false|}\"", "  status=\"${3|normal,active,success,exception|}\"", "  type=\"${4|line,circle,dashboard|}\"", "  :width=\"${5:160}\"", "  :stroke-width=\"${6:6}\"", "  format=\"${7|function(percent, successPercent) { return `${percent}%` }|}\"", "  :stroke-color=\"${8:{}}\"", "  :trail-color=\"${9:#f3f3f3}\"", "  :stroke-linecap=\"${10|round,square|}\"", "  :show-text=\"${11|true,false|}\"", "  :steps=\"${12:1}\"", "/>"]}, "a-result": {"prefix": "a-result", "description": "antd <a-result> 组件", "body": ["<a-result", "  status=\"${1|success,error,info,warning,404,403,500|}\"", "  title=\"{{ ${2:title} }}\"", "  sub-title=\"{{ ${3:subTitle} }}\"", ">", "  <template #extra>", "    <a-button type=\"primary\">${4:Primary Button}</a-button>", "  </template>", "  <div>${5:Some content}</div>", "</a-result>"]}, "a-skeleton": {"prefix": "a-skeleton", "description": "antd <a-skeleton> 组件", "body": ["<a-skeleton", "  :active=\"${1|false,true|}\"", "  :round=\"${2|false,true|}\"", "  :loading=\"${3:isLoading}\"", "  :avatar=\"{ size: '${4|small,default,large|}', shape: '${5|square,circle|}' }\"", "  :title=\"{ width: '${6:200px}' }\"", "  :paragraph=\"{ rows: ${7:4}, width: '${8|100%,80%,60%,40%,20%}' }\"", "  :count=\"${9:3}\"", "/>"]}, "a-spin": {"prefix": "a-spin", "description": "antd <a-spin> 组件", "body": ["<a-spin", "  :spinning=\"${1:loading}\"", "  size=\"${2|default,small,large|}\"", "  :delay=\"${3:delay}\"", "  :tip=\"${4:loading...}\"", "  :indicator=\"${5:indicator}\"", ">", "  ${6:<div>Some content here</div>}", "</a-spin>"]}, "a-anchor": {"prefix": "a-anchor", "description": "antd <a-anchor> 组件", "body": ["<a-anchor", "  :affix='${1|true,false|}'", "  :show-ink-ball='${2|true,false|}'", "  :get-container='${3|() => document.body|#container|}'", "  :scroll-offset='${4:10}'", ">", "  <template #inkBall>", "    <div class=\"custom-ink-ball\" />", "  </template>", "  <a-anchor-link", "    v-for='link in ${5:links}'", "    :key='link.href'", "    :href='link.href'", "    :title='${6:link.title}'", "  />", "</a-anchor>"]}, "a-anchor-link": {"prefix": "a-anchor-link", "description": "antd <a-anchor-link> 组件", "body": ["<a-anchor-link", "  href=\"${1:#section-1}\"", "  title=\"${2:Section 1}\"", "  :target=\"${3:target}\"", "  :bind-click=\"${4:handleClick}\"", "/>"]}, "a-back-top": {"prefix": "a-back-top", "description": "antd <a-back-top> 组件", "body": ["<a-back-top", "  :visibility-height=\"${1:400}\"", "  :target=\"${2:undefined}\"", "  :duration=\"${3:450}\"", ">", "  <!-- 自定义回到顶部的内容 -->", "  <template #default>", "    <div", "      class=\"ant-back-top-inner\"", "      style=\"height: 40px; width: 40px; line-height: 40px; border-radius: 4px; background-color: #1088e9; color: #fff; text-align: center; font-size: 20px;\"", "    >", "      {{ ${4:'↑'} }}", "    </div>", "  </template>", "</a-back-top>"]}, "a-config-provider": {"prefix": "a-config-provider", "description": "antd <a-config-provider> 组件", "body": ["<a-config-provider", "  :locale=\"${1:locale}\"", "  :theme=\"${2:theme}\"", "  :prefix-cls=\"${3:prefixCls}\"", "  :auto-insert-space-in-button=\"${4|false,true|}\"", ">", "  ${5:<slot></slot>}", "</a-config-provider>"]}, "a-watermark": {"prefix": "a-watermark", "description": "antd <a-watermark> 组件", "body": ["<a-watermark", "  :content=\"${1:watermarkText}\"", "  :font-style=\"${2:{color: '#e8e8e8', fontSize: 16}}\"", "  :gap-x=\"${3:24}\"", "  :gap-y=\"${4:48}\"", "  :width=\"${5:120}\"", "  :height=\"${6:64}\"", "  :rotate=\"${7:-22}\"", "  :z-index=\"${8:999}\"", "  :style=\"${9:{position: 'fixed', left: 0, top: 0}}\"", ">", "  ${10:<slot></slot>}", "</a-watermark>"]}}