<template>
  <div class="script-template">
    <cardBox title="Python/Shell模板管理" subTitle="Python/Shell脚本模板管理配置">
      <template #headerRight>
        <a-button v-action:add type="primary" @click="handleAddModal">新增</a-button>
      </template>
      <Table
        :columns="getColumns()"
        :getData="getTemplatList"
        ref="tableRef"
        :searchFormState="formMsg"
        :tableConfig="{
          scroll: { x: 1800 },
        }"
      >
        <template #search>
          <a-form-item>
            <a-input
              v-model:value="formMsg.templateName"
              allowClear
              @pressEnter="handleQuery"
              placeholder="请输入模板名称"
            />
          </a-form-item>
          <a-form-item>
            <a-input
              v-model:value="formMsg.templateDesc"
              allowClear
              @pressEnter="handleQuery"
              placeholder="请输入模板描述"
            />
          </a-form-item>
          <a-form-item>
            <a-select v-model:value="formMsg.templateType" allowClear placeholder="请选择模板类型">
              <a-select-option value="shell">Shell</a-select-option>
              <a-select-option value="python">Python</a-select-option>
              <a-select-option value="java">Java</a-select-option>
              <a-select-option value="scala">Scala</a-select-option>
            </a-select>
          </a-form-item>
        </template>
        <template #bodyCell="{ record, column, text }">
          <template v-if="column.key === 'templateContent'">
            <a class="table-cell" @click="logModal(text, '查看详情', true, 60)">{{
              text || '-'
            }}</a>
          </template>
          <template v-if="column.key === 'action'">
            <a-space>
              <a-button size="small" type="link" v-action:edit @click="handleEditModel(record)"
                >修改</a-button
              >
              <a-button size="small" type="link" v-action:delete @click="handelDelete(record.id)"
                >删除</a-button
              >
            </a-space>
          </template>
        </template>
      </Table>
    </cardBox>
    <EditTemplate ref="editTemplate" @success="loadData" />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { message, Modal } from 'ant-design-vue'
import type { TableColumnsType } from 'ant-design-vue'
import { getTemplatList, deleteTemplat, type TemplateDTO } from '@/api/setting/script-template'
import EditTemplate from './edit-template.vue'
import cardBox from '@/components/card-box/card-box.vue'
import { Table } from '@fs/fs-components'
import { logModal } from '@/utils/log-modal'

// 定义接口
interface PaginationType {
  total: number
  pageIndex: number
  pageSize: number
  pageSizeOptions: string[]
}

const pageMain = ref<HTMLElement>()
const editTemplate = ref<InstanceType<typeof EditTemplate>>()
const tableRef = ref()

const formMsg = reactive({
  templateName: '',
  templateDesc: '',
  templateType: undefined as string | undefined,
})

const pagination = reactive<PaginationType>({
  total: 0,
  pageIndex: 1,
  pageSize: 24,
  pageSizeOptions: ['12', '24', '36', '48'],
})

// 获取表格列配置
const getColumns = (): TableColumnsType => {
  return [
    {
      title: 'Id',
      dataIndex: 'id',
      key: 'id',
    },
    {
      title: '模板名称',
      dataIndex: 'templateName',
      key: 'templateName',
    },
    {
      title: '模板描述',
      dataIndex: 'templateDesc',
      key: 'templateDesc',
    },
    {
      title: '模板内容',
      dataIndex: 'templateContent',
      key: 'templateContent',
      ellipsis: true,
    },
    {
      title: '模板类型',
      dataIndex: 'templateType',
      key: 'templateType',
    },
    {
      title: '操作',
      key: 'action',
      width: 130,
      fixed: 'right',
    },
  ]
}

// 方法
const handleQuery = () => {
  pagination.pageIndex = 1
  loadData()
}

const loadData = async () => {
  tableRef.value?.getTableData()
}

const handleAddModal = () => {
  editTemplate.value?.showModal('add')
}

const handleEditModel = (record: TemplateDTO) => {
  editTemplate.value?.showModal('edit', record)
}

const handelDelete = (id: string) => {
  Modal.confirm({
    title: '提示',
    content: '确定要删除吗？',
    onOk: async () => {
      try {
        const res = await deleteTemplat({ id, ownerId: 'system' })
        if (res.result === '1') {
          message.success('删除成功')
          loadData()
        } else {
          message.warning(res.data.message)
        }
      } catch (error) {
        console.error('删除失败:', error)
      }
    },
    okText: '确认',
    cancelText: '取消',
  })
}
</script>

<style scoped>
.script-template {
  height: calc(100vh - 40px);
  padding: 24px;
}

.nav-page-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background: #fff;
  border-bottom: 1px solid #f0f0f0;
}

.title-left p {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
}

.title-left .desc {
  margin-top: 4px;
  color: #666;
  font-size: 12px;
}

.page-main {
  flex: 1;
  padding: 24px;
  overflow: auto;
}

.query-container {
  background: #fff;
  padding: 24px;
  margin-bottom: 16px;
  border-radius: 6px;
}

.list-content {
  background: #fff;
  border-radius: 6px;
}

.table-card {
  border-radius: 6px;
}

.table-cell {
  display: inline-block;
  color: #1890ff;
  cursor: pointer;
  width: 100%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.card-pagination {
  padding: 16px;
  text-align: right;
}
</style>
