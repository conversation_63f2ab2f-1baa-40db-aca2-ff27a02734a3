<template>
  <a-modal v-model:open="open" :title="modalTitle" width="600px" :footer="null">
    <div class="modal-content">
      <div v-for="(value, key) in data" :key="key" class="content-row">
        <div class="field-name">{{ key }}</div>
        <div class="field-value">{{ value }}</div>
      </div>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { title } from 'process'
import { ref, defineProps } from 'vue'

const open = ref(false)
const props = defineProps<{ modalTitle: string; data: any }>()

// const fieldData: any = ref(null)

const show = () => {
  open.value = !open.value
}

defineExpose({
  show,
})
</script>

<style scoped lang="less">
.modal-content {
  display: flex;
  flex-direction: column;
  border-top: 1px solid #e8e8e8;
  padding-top: 20px;
  margin-top: 20px;
}

.content-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.field-name {
  width: 40%;
  font-weight: bold;
  color: #696e7b;
}

.field-value {
  width: 60%;
  color: #4c5773;
}
</style>
