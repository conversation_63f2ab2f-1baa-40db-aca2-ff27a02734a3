<template>
  <div>
    <div class="group-title">汇总</div>
    <div class="group-container">
      <div class="group-item">
        <customsummary
          v-bind="props"
          v-for="(item, index) in groupData.summary"
          :key="index"
          @handleClick="handleSummaryClick($event, 'edit', index)"
        >
          <template #default>
            <div class="group-add">
              <span style="margin-right: 10px"
                >{{ item.label }} {{ item.fieldName ? `: ${item.fieldName}` : '' }}</span
              >
              <CloseOutlined @click.stop="handleRemoveSummary(index)" />
            </div>
          </template>
        </customsummary>
        <customsummary v-bind="props" @handleClick="handleSummaryClick($event, 'add')">
          <template v-if="groupData.summary?.length">
            <div class="group-action-trigger" :class="{ 'group-add': groupData.summary?.length }">
              <PlusOutlined style="font-size: 22px" />
            </div>
          </template>
        </customsummary>
      </div>
      <div
        style="width: 40px; text-align: center; font-weight: bold; color: #509ee3; cursor: default"
      >
        BY
      </div>
      <div class="group-item">
        <group
          v-bind="props"
          v-for="(item, index) in groupData.group"
          :key="index"
          @handleClick="handleClick($event, 'edit', index)"
        >
          <template #default>
            <div class="group-add">
              <span style="margin-right: 10px"
                >{{ item.title }} {{ item.extalName ? `: ${item.extalName}` : '' }}</span
              >
              <CloseOutlined @click.stop="handleRemove(index)" />
            </div>
          </template>
        </group>
        <group v-bind="props" @handleClick="handleClick($event, 'add')">
          <template #default v-if="groupData.group?.length">
            <div class="group-action-trigger" :class="{ 'group-add': groupData.group?.length }">
              <PlusOutlined style="font-size: 22px" />
            </div>
          </template>
        </group>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import type { dataColumn } from '@/components/data-columns/type'
import { PlusOutlined, CloseOutlined } from '@ant-design/icons-vue'
import group from '@/components/group/index.vue'
import customsummary from '@/components/summary/index.vue'

const props = defineProps<{
  tableName: string
  columns: dataColumn[]
}>()

const groupData = ref<any>({
  summary: [],
  group: [],
})

const modelValue = defineModel<any>({
  default: {},
})

function handleRemove(index: number) {
  groupData.value.group = groupData.value.group.filter((_, i) => i !== index)
}

function handleRemoveSummary(index: number) {
  groupData.value.summary = groupData.value.summary.filter((_, i) => i !== index)
}

function handleSummaryClick(e: dataColumn, type: 'add' | 'edit', index = 0) {
  if (type === 'add') {
    groupData.value.summary = [...(groupData.value.summary || []), e]
  } else {
    groupData.value.summary[index] = e
  }
  modelValue.value = groupData.value
}

function handleClick(e: dataColumn, type: 'add' | 'edit', index = 0) {
  if (type === 'add') {
    groupData.value.group = [...(groupData.value.group || []), e]
  } else {
    groupData.value.group[index] = e
  }
  modelValue.value = groupData.value
}

watch(
  () => modelValue.value,
  () => {
    groupData.value = modelValue.value || { summary: [], group: [] }
  },
  {
    immediate: true,
  },
)
</script>

<style lang="less" scoped>
.group-title {
  margin-bottom: 0.5rem;
  color: rgb(136, 191, 77);
  font-weight: bold;
  display: flex;
}
.group-container {
  display: flex;
  align-items: center;
}
.group-item {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  border-radius: 8px;
  gap: 10px;
  background-color: rgba(136, 191, 77, 0.1);
  padding: 14px;
  color: rgb(136, 191, 77);
  width: 50%;
  .group-add {
    border-radius: 6px;
    padding: 10px;
    color: #fff;
    background-color: rgb(136, 191, 77);
  }
  .group-add:hover,
  .group-action:hover,
  .group-action-trigger:hover {
    background-color: #9fcc70;
  }
}
</style>
