import service from '@/api/index'
import axios from 'axios'

// 从虚拟文件系统获取api数据
export function reqGetAuthList(params: any = {}) {
  return service({
    method: 'get',
    url: '/assetManager/dataAssertManage/dataPermissionPageList',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 查询字段列表(下拉框)
export const reqGetParamsList = (tableId: string) => {
  return service({
    method: 'get',
    url: '/assetManager/dataAssertManage/getTableInfoListById',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params: { tableId },
  })
}

// 查询用户列表(下拉框)
export function reqGetUserList(tableId: string) {
  return service({
    method: 'get',
    url: '/assetManager/dataAssertManage/getUserList',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
      Authorization: 'f0a058e5f2efaac568e3ea33d8ba6da5', // 暂时写死
      opUser: '653f9b5c057a6824a06301cb',
    },
    params: { tableId },
  })
}

// 新增用户数据
export const reqAddDataList = (data: any) => {
  return service({
    method: 'post',
    url: '/assetManager/dataAssertManage/saveDataPermission',
    data,
  })
}

// 删除用户数据
// /assetManager/dataAssertManage/deletedataPermission
export const reqDelDataList = (permissionId: string) => {
  return service({
    method: 'post',
    url: '/assetManager/dataAssertManage/deletedataPermission',
    data: {
      permissionId,
    },
  })
}
// 编辑用户数据
export const reqEditDataList = (data: any) => {
  return service({
    method: 'post',
    url: '/assetManager/dataAssertManage/updateDataPermission',
    data,
  })
}
