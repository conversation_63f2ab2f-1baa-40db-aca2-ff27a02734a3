<template>
  <a-modal
    :title="'查看详情'"
    v-model:open="showDialog"
    width="700px"
    @cancel="cancelHandleEnv"
    @ok="cancelHandleEnv"
    :ok-text="'确认'"
    :cancel-text="'取消'"
  >
    <a-form layout="vertical">
      <a-row>
        <a-col span="8">
          <a-form-item :label="'操作系统'">
            {{ handleFill(formData.osEvn.name) }}
          </a-form-item>
        </a-col>
        <a-col span="8">
          <a-form-item :label="'操作系统版本'">
            {{ handleFill(formData.osEvn.version) }}
          </a-form-item>
        </a-col>
        <a-col span="8">
          <a-form-item :label="'处理器类型'">
            {{ handleFill(formData.osEvn.arch) }}
          </a-form-item>
        </a-col>
        <a-col span="8">
          <a-form-item :label="'核数'">
            {{ handleFill(formData.osEvn.availableProcessors) }}
          </a-form-item>
        </a-col>
        <a-col span="8">
          <a-form-item :label="'内存'">
            {{ handleFill(formData.osEvn.totalPhysicalMemorySize) }}
          </a-form-item>
        </a-col>
        <a-col span="8">
          <a-form-item :label="'剩余内存'">
            {{ handleFill(formData.osEvn.freePhysicalMemorySize) }}
          </a-form-item>
        </a-col>
        <a-col span="8">
          <a-form-item :label="'剩余交换分区'">
            {{ handleFill(formData.osEvn.freeSwapSpaceSize) }}
          </a-form-item>
        </a-col>
        <a-col span="8">
          <a-form-item :label="'交换分区'">
            {{ handleFill(formData.osEvn.totalSwapSpaceSize) }}
          </a-form-item>
        </a-col>

        <a-col span="8">
          <a-form-item :label="'线程总数'">
            {{ handleFill(formData.jvmEnv.threadCount) }}
          </a-form-item>
        </a-col>
        <a-col span="8">
          <a-form-item :label="'jvm最大可用内存'">
            {{ handleFill(formData.jvmEnv?.maxMemory) }}
          </a-form-item>
        </a-col>
        <a-col span="8">
          <a-form-item :label="'jvm内存'">
            {{ handleFill(formData.jvmEnv?.totalMemory) }}
          </a-form-item>
        </a-col>
        <a-col span="8">
          <a-form-item :label="'Jvm剩余内存'">
            {{ handleFill(formData.jvmEnv?.freeMemory) }}
          </a-form-item>
        </a-col>
        <a-col span="24" v-if="formData.jvmEnv?.gcs">
          <a-table
            :data-source="formData.jvmEnv.gcs"
            :columns="gscColumns"
            :pagination="false"
          ></a-table>
        </a-col>
      </a-row>
    </a-form>
  </a-modal>
</template>

<script setup lang="ts">
const props = defineProps({
  open: {
    type: Boolean,
    default: false,
  },
  envForm: { type: Object, required: false, defualt: null },
})
const showDialog = ref(false)

const formData = ref<any>({
  osEvn: {},
  jvmEnv: {},
})

watch(
  () => props.open,
  () => {
    showDialog.value = props.open
    if (props.open) {
      console.log('props.open', props.open)
      formData.value = {
        osEvn: props.envForm?.osEvn || {},
        jvmEnv: props.envForm?.jvmEnv,
      }
    }
  },
  { immediate: true, deep: true },
)

const emits = defineEmits(['modalCancelHandleEnv'])
const gscColumns = [
  {
    align: 'center',
    title: 'Gc区域',
    key: 'name',
    dataIndex: 'name',
    scopedSlots: { customRender: 'name' },
  },
  {
    align: 'center',
    title: 'Gc次数',
    key: 'count',
    dataIndex: 'count',
    scopedSlots: { customRender: 'count' },
  },
  {
    align: 'center',
    title: 'Gc用时',
    key: 'time',
    dataIndex: 'time',
    scopedSlots: { customRender: 'time' },
  },
]
/**
 * 表格数据填充
 * @param data
 */
const handleFill = (data: any) => {
  return data === '' || data === null || data === undefined ? '--' : data
}

// const showModal = (data: any = {}) => {
//   formData.value.osEvn = data.osEvn || {}
//   formData.value.jvmEnv = data.jvmEnv || {}
//   visible.value = true
// }

function cancelHandleEnv() {
  emits('modalCancelHandleEnv')
}
</script>
