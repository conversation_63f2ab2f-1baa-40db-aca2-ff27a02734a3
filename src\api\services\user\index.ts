import service from '@/api'
import { SODATAFLINK } from '@/api/base'

export function checkLicense(data: any): Promise<any> {
  return service({
    method: 'get',
    url: `${SODATAFLINK}/config/LicenseManage/checkLicenseValid`,
    data,
  })
}

export function sysSystemFunUploadFile(data: any): Promise<any> {
  return service({
    method: 'post',
    url: `/api/301UC/systemFun/sysSystemFun/uploadFile`,
    data,
  })
}

export function upadateUserAvatar(data: any): Promise<any> {
  return service({
    method: 'post',
    url: `/api/301UC/userManage/upadateUserAvatar`,
    data,
  })
}

export function upgradeLicense(data: any): Promise<any> {
  return service({
    method: 'post',
    url: `${SODATAFLINK}/config/LicenseManage/upgradeLicense`,
    data,
  })
}

export function downLoadFile(data: any): Promise<any> {
  return service({
    method: 'post',
    url: `/api/301UC/systemFun/sysSystemFun/downLoadFile`,
    data,
    responseType: 'blob',
  })
}
