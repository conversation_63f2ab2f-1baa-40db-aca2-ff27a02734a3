<template>
  <a-button type="primary" @click="showModal = true">
    <SettingOutlined />
  </a-button>
  <a-modal
    v-model:visible="showModal"
    title="设置"
    :width="400"
    :destroyOnClose="true"
    @ok="showModal = false"
  >
    <div class="btn-link">添加或删除栏目</div>
    <div class="field-list">
      <div class="field-item" v-for="(item, index) in fieldList" :key="index">
        <div class="field-left">
          <div class="field-icon">
            <FontSizeOutlined />
          </div>
          <div class="field-name">{{ item.field }}</div>
        </div>
        <div class="field-right">
          <a-popover trigger="click" placement="bottom">
            <MoreOutlined class="mr-10" />
            <template #content>
              <setField :props-item="item" />
            </template>
          </a-popover>
          <a-tooltip>
            <template #title>{{ item.isShow === 'hide' ? '显示' : '隐藏' }}该字段</template>
            <EyeOutlined v-if="item.isShow === 'hide'" @click="hideFieldFn(item.field, 'show')" />
            <EyeInvisibleOutlined
              v-else-if="item.isShow === 'show'"
              @click="hideFieldFn(item.field, 'hide')"
            />
          </a-tooltip>
        </div>
      </div>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import {
  SettingOutlined,
  MoreOutlined,
  EyeInvisibleOutlined,
  EyeOutlined,
  FontSizeOutlined,
} from '@ant-design/icons-vue'

import { ref, inject } from 'vue'
import setField from './set-field.vue'
const hideFieldFn: any = inject('hideField')

const props = defineProps({
  fieldList: {
    type: Object,
    default: () => {},
  },
})

const showModal = ref(false)
const showContent = ref(false)
</script>

<style lang="less" scoped>
.field-list {
  .field-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
    border: 1px solid #eeecec;
    border-radius: 10px;
    cursor: pointer;
    padding: 10px 10px;
    background: rgb(255, 255, 255);
    color: rgb(105, 110, 123);
    .field-left {
      display: flex;
      gap: 12px;
      .field-name {
        font-weight: 600;
      }
    }
  }
}

.btn-link {
  color: #65b5ec;
  font-weight: 600;
  margin-bottom: 20px;
  cursor: pointer;
}
</style>
