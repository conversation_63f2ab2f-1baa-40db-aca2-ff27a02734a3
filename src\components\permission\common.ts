import { getDeptList } from '@/api/services/permission'
import { useUserStore } from '@/stores/user'
import { storeToRefs } from 'pinia'
const userStore = useUserStore()
const { userInfo } = storeToRefs(userStore)
/**
 * 获取部门数据
 */
export async function getDeptListFn() {
  try {
    const { data } = await getDeptList({
      isUse: 1,
      buId: userInfo.value.buId,
    })

    if (Array.isArray(data)) {
      return data
    } else {
      return []
    }
  } catch (error) {
    console.error(error)
    return []
  }
}

/**
 * 构建选项映射
 */
export function buildOptionsMap(tree: any[], map: string[] = ['id', 'children']) {
  const [id, children] = map
  const pathsMap: Record<string, string[]> = {}
  const findPaths = (tree: any[], parentPath: string[] = []) => {
    tree.forEach((item) => {
      const newPath: string[] = [...parentPath, item[id]]
      pathsMap[item[id]] = newPath
      if (item[children]?.length) {
        findPaths(item[children], newPath)
      }
    })
  }
  findPaths(tree)
  return pathsMap
}
