import service from '../index'
import axios from 'axios'

// 从虚拟文件系统获取api数据
export function getApiData(data: any = {}) {
  return service({
    method: 'post',
    url: '/server/getApiData',
    data,
  })
}

// 增加swagger文档接口
export function saveUrlData(data: any) {
  return service({
    method: 'post',
    url: '/server/saveUrlData',
    data,
  })
}

// 修改swagger文档接口
export function editUrlData(data: any) {
  return service({
    method: 'post',
    url: '/server/editUrlData',
    data,
  })
}

// 查询swagger文档数据
export function getUrlDetail(data: any) {
  return service({
    method: 'post',
    url: '/server/getUrlDetail',
    data,
  })
}

// swagger文档同步
export function synchronous(data: any) {
  return service({
    method: 'post',
    url: '/server/synchronous',
    data,
  })
}

// 删除swagger文档数据
export function delUrl(data: any) {
  return service({
    method: 'post',
    url: '/server/delUrl',
    data,
  })
}

// 代码部署
export function generateDeploy(data: any) {
  return service({
    method: 'post',
    url: '/server/generateDeploy',
    data,
  })
}

// mock调用测试
export function getMockTest(data: any, headers = {}) {
  return axios({
    method: 'post',
    url: '/server/mockTest',
    data,
    headers,
  })
}

// mock调用测试formdata格式
export function mockFormDataTest(data: any, headers = {}) {
  return axios({
    method: 'post',
    url: '/server/mockFormDataTest',
    data,
    headers,
  })
}

// 获取gpt模块数据
export function getGptMoudles() {
  return service({
    method: 'post',
    url: '/server/gpt/getGptMoudles',
  })
}

// 新增gpt模块数据
export function addGptMoudles(data: any) {
  return service({
    method: 'post',
    url: '/server/gpt/addGptMoudles',
    data,
  })
}

// 新增gpt模块下接口数据
export function addInterface(data: any) {
  return service({
    method: 'post',
    url: '/server/gpt/gptMoudles/addInterface',
    data,
  })
}

// 生成gpt模块接口代码
export function generationGpt(data: any) {
  return service({
    method: 'post',
    url: '/server/gpt/generationGpt',
    data,
  })
}

// 删除gpt模块接口代码
export function delGenerationGpt(data: any) {
  return service({
    method: 'post',
    url: '/server/gpt/delGenerationGpt',
    data,
  })
}

// 连接gpt  生成入参出参
export function completions(data: any) {
  return axios({
    method: 'post',
    url: '/v1/chat/completions',
    data,
    headers: {
      'Content-Type': 'application/json',
    },
  })
}

// 获取接口ts函数字符串模板
export function getApiTemplate(data: any) {
  return service({
    method: 'post',
    url: '/server/getApiTemplate',
    data,
  })
}

// 保存模块mock数据
export function saveApiMock(data: any) {
  return service({
    method: 'post',
    url: '/server/gpt/saveApiMock',
    data,
  })
}
