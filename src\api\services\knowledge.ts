import { ctoken } from '../base'
// import service from '../index'
import axios from './axios-instances'
import { SODATAFLINK } from '@/api/base'
import service from '@/api'
// import type { AnyFn } from '@vueuse/core'

// 登录
// export function reqLogin(data: { account?: string; password?: string }) {

//   return axios({
//     method: 'post',
//     url: '/v1/accounts/login?ctoken=bigfish_ctoken_19h4i9iei9',
//     data: {
//       account: 'openspg',
//       password: '2e3af77a14702ef088e858a79d16485230efbdbb4015a8b50e32dcb0b5127253',
//     },
//     withCredentials: true,
//   })
// }
export function reqLogin() {
  return axios({
    method: 'post',
    url: '/v1/accounts/login?ctoken=bigfish_ctoken_19h4i9iei9',
    data: {
      account: 'openspg',
      password: '2e3af77a14702ef088e858a79d16485230efbdbb4015a8b50e32dcb0b5127253',
    },
    withCredentials: true,
  })
}
// 1.知识库列表
export function reqGetKnowledgeList(
  data: {
    queryType?: '0' | '1'
    gmtCreate?: string
    pageSize?: string
    pageIndex?: number
    displayName?: string
  } = {},
): Promise<any> {
  return axios({
    method: 'get',
    url: '/knowledge/project/projectPageList',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params: {
      pageIndex: 1,
      pageSize: 999,
      ...data,
    },
  })
}

// 2.新建知识库
export function reqCreateKnowledge(data: any = {}): Promise<any> {
  return axios({
    method: 'post',
    url: '/knowledge/project/save',
    data,
  })
}

// 3.编辑知识库
export function reqEditKnowledge(data: any = {}): Promise<any> {
  return axios({
    method: 'put',
    url: '/knowledge/project/update',
    data,
  })
}

// 4.删除知识库
export function reqDeleteKnowledge(data: any = {}): Promise<any> {
  return axios({
    method: 'delete',
    url: '/knowledge/project/delete',
    data,
  })
}

// 5.知识库构建
export function reqBuildKnowledge(data: any = {}): Promise<any> {
  return axios({
    method: 'post',
    url: '/knowledge/build/task',
    data,
  })
}

// 6.知识库任务列表
export function reqGetKnowledgeTaskList(data: any = {}): Promise<any> {
  return axios({
    method: 'get',
    url: '/knowledge/build/list',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params: data,
  })
}

// 7.删除知识任务
export function reqDeleteBuildTask(data: any = {}): Promise<any> {
  return axios({
    method: 'post',
    url: '/knowledge/build/delete',
    data,
  })
}

// 8.KM001-知识模型点状模式查询
export function reqGetKnowledgeModel(data: any = {}): Promise<any> {
  return axios({
    method: 'post',
    url: '/knowledge/model/graph',
    data,
  })
}

// 9.KM002-查询所有的标签
export function reqGetAllTags(data: any = {}): Promise<any> {
  return axios({
    method: 'get',
    url: '/knowledge/model/lables',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params: data,
  })
}

// 10.KM003-查询所有关系类型
export function reqGetAllRelationType(data: any = {}): Promise<any> {
  return axios({
    method: 'get',
    url: '/knowledge/model/relationship/type',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params: data,
  })
}

// 11.KM004-获取数据属性汇总信息
export function reqGetAttributeSummary(data: any = {}): Promise<any> {
  return axios({
    method: 'get',
    url: '/knowledge/model/metrics',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params: data,
  })
}

// 12.KA001-中心性算法
export function reqGetCentrality(data: any = {}): Promise<any> {
  return axios({
    method: 'post',
    url: '/knowledge/algorithm/pageRankScores',
    data,
  })
}

// 13.KA002-社区检测算法
export function reqGetCommunityDetection(data: any = {}) {
  return axios({
    method: 'post',
    url: '/knowledge/algorithm/louvain',
    data,
  })
}

// 14.KA003-相似度算法
export function reqGetSimilarity(data: any = {}) {
  return axios({
    method: 'post',
    url: '/knowledge/algorithm/nodeSimilarity',
    data,
  })
}

// 15.KA004-路径查找算法
export function reqGetPathDijkstra(data: any = {}) {
  return axios({
    method: 'post',
    url: '/knowledge/algorithm/pathDijkstra',
    data,
  })
}

// 16.KA005-Dag算法
export function reqGetDag(data: any = {}) {
  return axios({
    method: 'post',
    url: '/knowledge/algorithm/topologicalSort',
    data,
  })
}

// 17.KA006-节点嵌入算法
// /knowledge/algorithm/fastRP
export function reqGetFastRP(data: any = {}) {
  return axios({
    method: 'post',
    url: '/knowledge/algorithm/fastRP',
    data,
  })
}

/* -----------------------------分割线(下面的是copy过来的老代码)------------------------------- */

// 2.知识任务列表
export function reqGetTaskList(data: any = {}) {
  return axios({
    method: 'get',
    url: '/public/v1/builder/job/list',
    params: data,
  })
}

// 3.抽取模型下拉列表
export function reqGetModelList(data: any = {}) {
  return axios({
    method: 'get',
    url: '/v1/datas/getEnumValues/LLMType',
    params: data,
  })
}

// 4.创建任务
export function reqCreateTask(data: any = {}) {
  return axios({
    method: 'post',
    url: '/public/v1/builder/job/submit',
    data,
  })
}

// 5.预览分段效果
export function reqPreviewSegment(data: any = {}) {
  return axios<Promise<any>>({
    method: 'post',
    url: '/public/v1/builder/job/split/preview',
    data,
  })
}

// 6.查看日志
export function reqGetLog(data: any = {}): Promise<any> {
  return axios({
    method: 'get',
    url: '/public/v1/reasoner/task/builder/query',
    params: data,
  })
}

// 7.图标数据
export function reqGetChartData(data: any = { projectId: 1 }): Promise<any> {
  return axios({
    method: 'get',
    url: `/v1/schemas/graph/${data.projectId}`,
    params: data,
  })
}

// 8.抽取结果数据
export function reqGetReasonerData(data: any): Promise<any> {
  return axios({
    method: 'get',
    url: `/public/v1/reasoner/task/builder/query`,
    params: data,
  })
}

// 9.获取抽样结果映射map
export function reqGetSampleMap(data: any): Promise<any> {
  return axios({
    method: 'get',
    url: `/v1/schemas/getSchemaNameMap`,
    params: data,
  })
}

// 10.删除列表条目
export function reqDeleteTask(data: any): Promise<any> {
  return axios({
    method: 'get',
    url: `/public/v1/builder/job/delete`,
    params: data,
  })
}
// 11.获取通用配置
export function getGloablConfig(data: any = {}) {
  return axios({
    method: 'get',
    url: '/v1/configs/KAG_CONFIG/version/1',
    params: data,
  })
}
// 12.编辑通用配置
export function updateGloablConfig(data: any = {}, requestBody: any = {}) {
  return axios({
    method: 'put',
    url: '/v1/configs/2',
    params: { ctoken: ctoken, ...data },
    data: requestBody,
    headers: {
      'Content-Type': 'application/json',
    },
  })
}

// 13.获取schemaScript
export function reqGetSchemaScript(data: any = {}): Promise<any> {
  return axios({
    method: 'get',
    url: '/v1/schemas/getSchemaScript',
    params: data,
  })
}

// 14.保存schema
export function reqSaveSchemaScript(data: any = {}): Promise<any> {
  return axios({
    method: 'post',
    url: '/v1/schemas',
    data,
  })
}

// 15.树状图数据
export function reqGetTreeData(data: any = { projectId: 1 }): Promise<any> {
  return axios({
    method: 'get',
    url: `/v1/schemas/tree/${data.projectId}`,
    params: data,
  })
}

// 16.图标点状详情
export function reqGetDotDetail(data: any): Promise<any> {
  return axios({
    method: 'get',
    url: `/v1/schemas/entity/${data.id}`,
    params: data,
  })
}

// 17.获取抽样数据
export function reqGetSampleData(data: any): Promise<any> {
  return axios({
    method: 'get',
    url: '/v1/datas/getSampleData',
    params: data,
  })
}

// 18.概念模型树
export function reqGetConceptTreeData(data: any): Promise<any> {
  return axios({
    method: 'get',
    url: '/concept/api/getConceptTree.json',
    params: data,
  })
}

// 19.规则列表
export function reqGetRuleList(data: any): Promise<any> {
  return axios({
    method: 'get',
    url: '/rule/api/getRuleList.json',
    params: data,
  })
}

// 20.知识探查列表数据
export function reqGetKnowledgeData(data: any): Promise<any> {
  return axios({
    method: 'get',
    url: '/v1/datas/search',
    params: data,
  })
}
// 20.获取知识库配置
export function reqGetKnowledgeConfig(data: any = { projectId: 1 }): Promise<any> {
  return axios({
    method: 'get',
    url: `/v1/projects/${data.projectId}`,
    params: data,
  })
}
// 21.编辑知识库配置
export function updateKnowledgeConfig(data: any = {}, requestBody: any = {}) {
  return axios({
    method: 'put',
    url: `/v1/projects/${data.id}`,
    params: data,
    data: requestBody,
    headers: {
      'Content-Type': 'application/json',
    },
  })
}
// 22.删除知识库配置
export function deleteKnowledgeConfig(data: any = {}) {
  return axios({
    method: 'delete',
    url: `/v1/projects/${data.projectId}`,
    params: data,
  })
}

/**
 * 获取元数据库名
 * @param params
 */
export function PageListScameInfo(params: any): Promise<any> {
  return service({
    url: `${SODATAFLINK}/sqlEditor/PageListScameInfo`,
    method: 'POST',
    data: params,
  })
}

/**
 * aisql，获取元数据库名
 * @param params 请求参数
 * @returns Promise<DbNameFunResponseDTO> 数据库名称列表响应
 */
export function getDbNameFun(params: any): Promise<any> {
  return service({
    url: `${SODATAFLINK}/AISQL/dbNameFun`,
    method: 'POST',
    data: params,
  })
}

/**
 * aisql，获取元数据库名
 * @param params 请求参数
 * @returns Promise<DbNameFunResponseDTO> 数据库名称列表响应
 */
export function dbnameTableParmaTranFun(params: any): Promise<any> {
  return service({
    url: `${SODATAFLINK}/AISQL/dbnameTableParmaTranFun`,
    method: 'POST',
    data: params,
  })
}

/**
 * 查询字段
 * @param params
 */
export function queryTableColumnInfoFun(params: any) {
  return service({
    url: `${SODATAFLINK}/metadata/queryTableColumnInfoFun`,
    method: 'post',
    data: params,
  })
}

/**
 * KT004-新增非结构化知识任务
 * @param params
 */
export function addUnKnowledgeTask(params: any) {
  return service({
    url: `/knowledge/build/addUnKnowledgeTask`,
    method: 'post',
    data: params,
  })
}

/**
 * KT005-分页预览节点提取数据
 * @param params
 */
export function nodePreview(params: any) {
  return service({
    url: `/knowledge/build/node/preview`,
    method: 'GET',
    params: params,
  })
}

/**
 * KT007-节点名称修改
 * @param params
 */
export function updateNodeName(params: any) {
  return service({
    url: `/knowledge/build/update/name`,
    method: 'POST',
    data: params,
  })
}

/**
 * KT008-知识任务提交
 * @param params
 */
export function taskCommit(params: any) {
  return service({
    url: `/knowledge/build/commit`,
    method: 'POST',
    data: params,
  })
}

/**
 * KT006-分页预览关系提取数据
 * @param params
 */
export function relationshipPreview(params: any) {
  return service({
    url: `/knowledge/build/relationship/preview`,
    method: 'GET',
    params: params,
  })
}
