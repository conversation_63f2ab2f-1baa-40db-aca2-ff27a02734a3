import './assets/main.less'
import { createApp } from 'vue'
import { createPinia } from 'pinia'
import 'virtual:uno.css'
import App from './app.vue'
import router from './router/core'
import actionDirective from './directives/action'
import FcDesigner from '@form-create/designer'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
// main.js
import 'prismjs/themes/prism-okaidia.css' // 推荐主题
import 'prismjs/plugins/line-numbers/prism-line-numbers.css'
import { logModal } from '@/utils/log-modal'
import { get301UcUserInfo } from './api/services/permission'
import { useUserStore } from '@/stores/user'
import { getUserMenuFn } from '@/utils/user'
import { COOKIE_CONFIG, CookieManager } from '@/utils/cookie'
import './assets/fonts/iconfont.css'
const app = createApp(App)

app.use(createPinia())
app.use(router)
app.use(ElementPlus)
app.use(FcDesigner)
app.use(FcDesigner.formCreate)

app.directive('action', actionDirective)
// 注册全局方法
app.config.globalProperties.$logModal = logModal
// app.component('logModal', logModal)

const urlParams = new URLSearchParams(window.location.search)
const token = urlParams.get('token')

async function bootstrap() {
  if (import.meta.env.VITE_APP_NEED_AUTH === 'false') {
    const token = CookieManager.get(COOKIE_CONFIG.TOKEN_KEY) || ''
    const userStore = useUserStore()
    try {
      const { data } = await get301UcUserInfo({ userToken: token })
      userStore.userInfo = data
      await getUserMenuFn(true, data)
    } catch (e) {
      // 可以根据需要处理异常，比如弹窗提示
      console.error('获取用户信息失败', e)
    }
  }
  app.mount('#app')
}

bootstrap()

// 追踪运行时错误
app.config.errorHandler = (err, instance, info) => {
  console.log('app.config.errorHandler', err, instance, info)
}
