<template>
  <div class="echarts-flex">
    <a-tabs v-model:activeKey="activeKey">
      <a-tab-pane key="1" tab="图表布局">
        <div>
          <Column
            :data="obj"
            :indexNum="-1"
            :projectData="projectData"
            @update:import="updateImport"
            :selectData="selectData"
          ></Column>
        </div>
      </a-tab-pane>
      <a-tab-pane key="2" tab="筛选配置" force-render>
        <selectColumns v-model="selectData"></selectColumns
      ></a-tab-pane>
    </a-tabs>
    <div class="footer">
      <a-tooltip placement="bottom">
        <template #title>
          <span>筛选配置</span>
        </template>
      </a-tooltip>
      <a-button @click="saveDraft" style="margin-right: 16px">保存草稿</a-button>
      <a-button @click="generateCode" type="primary">生成代码</a-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { getData, formatting, getdraft } from '../code-generation'
import selectColumns from './components/select-columns.vue'

import Column from './components/column.vue'

interface echatsFlexProps {
  routerPath: string
}

const props: echatsFlexProps = withDefaults(defineProps<echatsFlexProps>(), {})
const obj = ref<any>({
  compnentName: 'a-flex',
  comProps: {
    vertical: true,
    gap: 0,
  },
  children: [],
})
const confirmLoading = ref(false)
const importData = ref<any>([])
const selectData = ref<any>([])
const activeKey = ref('1')
const projectData = ref<any[]>([])

const emit = defineEmits(['saveDraft', 'generateCode'])

const saveDraft = async (state = true) => {
  obj.value.selectData = selectData.value
  emit('saveDraft', { ...obj.value, pageType: 'echarts' }, state)
}

const updateImport = (data: any) => {
  importData.value.push(data)
}

const generateCode = async () => {
  confirmLoading.value = true
  const content = await getAllCode()
  const regex = /"FucName-(.*?)"/g
  let replacedStr = content
  let match
  // 去除函数前后空格 实现函数名
  while ((match = regex.exec(replacedStr)) !== null) {
    replacedStr = replacedStr.replace(`"FucName-${match[1]}"`, match[1])
  }
  emit('generateCode', { content: replacedStr })
  saveDraft(false)
  confirmLoading.value = false
}

const getAllCode = async () => {
  const str =
    `
  <template>
   <div>
    <flex-componnent :options="objData"></flex-componnent>
   </div>
  </template>
  ` +
    '<' +
    `script setup lang='ts'>
    import { ref } from 'vue'
    import flexComponnent from '@/views/component-page/component/flex-componnent.vue'

    ${getImport()}

      const objData = ref<any>(${getObjData()})
    </` +
    `script>
  `
  const { data } = await formatting(str)
  return data.data
}

const getTypeValue = (name: string) => {
  const item = selectData.value.find((item: any) => item.name === name)
  const obj: any = {
    type: item.type,
    name: item.name,
    comProps: {},
    defaultValue: [],
  }
  if (item.type === 'radio-group') {
    if (item?.options && item.options.length > 0) {
      obj.defaultValue = item.options[0].value
    }
    obj.comProps = {
      options: item.options,
      optionType: 'button',
    }
  } else if (item.type === 'select') {
    obj.comProps = {
      placeholder: '请选择类型',
      mode: 'multiple',
      maxTagCount: 2,
      style: {
        width: '200px',
      },
      options: item.options,
    }
  }
  return obj
}

const getObjData = () => {
  const newData = JSON.parse(JSON.stringify(obj.value))
  const list = newData.children
  addDataStyle(list, true)

  return JSON.stringify(newData, null, 2)
}

const addDataStyle = (list: any[], state = false) => {
  if (list.length > 0) {
    list.map((item) => {
      const { vertical = false } = item.comProps

      item.comProps = {
        ...item.comProps,
        getOptions: getOptionsHandle(item),
        style: {
          // 如果是垂直布局，则子元素宽度为100%，否则为子元素平分
          width: `calc((100% - (${state ? 1 : list.length} - 1) * 16px) / ${state ? 1 : list.length})`,
        },
      }

      if (item.comProps?.search) {
        item.comProps.items = [getTypeValue(item.comProps?.search)]
      }
      if (item.compnentName === 'a-flex') {
        delete item.comProps.getOptions
      }
      delete item.history
      if (item.children && item.children.length > 0) {
        addDataStyle(item.children, vertical)
      }
    })
  }
}

const getOptionsHandle = (item: any) => {
  if (!item.comProps?.getOptions) return ''
  if (item.comProps.getOptions.includes('FucName-')) {
    return item.comProps.getOptions
  }
  return 'FucName-' + item.comProps.getOptions
}

const getImport = () => {
  try {
    // 数组去重 防止重复引入
    const uniqueArr = [...new Set(importData.value.map((item: any) => JSON.stringify(item)))].map(
      (str: any) => JSON.parse(str),
    )
    const uniqueImports: { [key: string]: string[] } = {}
    const functionByName: any = {}
    uniqueArr.forEach((item) => {
      const key = `${item.project.toLocaleLowerCase()}/${item.module.toLocaleLowerCase()}/${item.module.toLocaleLowerCase()}`
      if (!uniqueImports[key]) {
        uniqueImports[key] = []
      }
      uniqueImports[key].push(item.interface)
      functionByName[item.interface] = item.interface
    })
    const importStatements = Object.entries(uniqueImports).map(
      ([path, interfaces]) => `import { ${interfaces.join(', ')} } from '@/api/${path}';`,
    )

    return importStatements.join('\n')
  } catch (err) {
    console.log('%c 🍯 err: ', 'font-size:12px;background-color: #6EC1C2;color:#fff;', err)
    //
  }
}

const getImportHandle = (data: any) => {
  if (data.children && data.children.length > 0) {
    data.children.forEach((item: any) => {
      if (item.compnentName === 'Echarts') {
        const { project, module } = item.history
        importData.value.push({
          project,
          module,
          interface: item.history.interface,
        })
      }
      if (item?.children && item.children.length > 0) {
        getImportHandle(item)
      }
    })
  }
}

onMounted(async () => {
  try {
    const val = await getdraft({ name: props.routerPath })
    const pageType = val?.data?.data?.pageType ?? ''
    // 草稿箱为表格类型时 读取草稿数据
    if (pageType && pageType === 'echarts') {
      if (val?.data?.data && JSON.stringify(val?.data?.data) !== '{}') {
        obj.value = val.data.data
        getImportHandle(obj.value)
        if (obj.value.selectData) {
          selectData.value = obj.value.selectData
        }
      }
    }
    const res = await getData()
    const { data } = res
    if (data?.code === '000000') {
      projectData.value = data.data
    }
  } catch (error) {
    console.log('%c 🍤 error: ', 'font-size:12px;background-color: #B03734;color:#fff;', error)
  }
})
</script>
<style scoped lang="less">
.echarts-flex {
  width: 100%;
  .footer {
    text-align: center;
    margin-top: 20px;
    position: relative;
  }
}
</style>
