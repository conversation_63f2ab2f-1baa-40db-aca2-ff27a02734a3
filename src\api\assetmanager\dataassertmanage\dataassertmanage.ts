import service from '@/api'
import { MOCK_DATA } from './dataassertmanage-mock'

/**
 * 查询数据库类型列表 - DAM001-查询数据库类型列表
 * @param {string} opUser - 操作人
 * @param {string} opUserName - 操作人名称
 * @returns {any} - 返回一个解析为响应数据的Promise
 */

export async function getDbTypeList(
  data: any,
  headers?: {
    opUser?: string
    opUserName?: string
  },
): Promise<{
  code: string
  msg: string
  data: Array<{ dbType: string }>
}> {
  return service(
    {
      method: 'GET',
      url: '/assetManager/dataAssertManage/getDbTypeList',
      params: data,
      headers,
    },
    MOCK_DATA.getDbTypeList,
  )
}

/**
 * 查询数据库列表 - DAM002-查询数据库列表
 * @param {string} dbType - 数据库类型
 * @param {string} opUser - 操作人
 * @param {string} opUserName - 操作人名称
 * @returns {any} - 返回一个解析为响应数据的Promise
 */

export async function getDbNameByType(
  data: {
    dbType: string
  },
  headers?: {
    opUser?: string
    opUserName?: string
  },
): Promise<{
  code: string
  msg: string
  data: Array<{
    databaseName: string
    ip: string
    port: string
    engine: string
    datasourceId: string
  }>
}> {
  return service(
    {
      method: 'GET',
      url: '/assetManager/dataAssertManage/getDbNameByType',
      params: data,
      headers,
    },
    MOCK_DATA.getDbNameByType,
  )
}

/**
 * 查询数据表列表 - DAM003-查询数据表与表字段列表
 * @param {string} dbType - 数据库类型
 * @param {string} key - ip+端口+数据库
 * @param {string} databaseName - 数据库名称
 * @param {string} tableName - 表名称
 * @param {string} opUser - 操作人
 * @param {string} opUserName - 操作人名称
 * @returns {any} - 返回一个解析为响应数据的Promise
 */

export async function getTableInfoList(
  data: {
    dbType: string
    key: string
    databaseName: string
    tableName?: string
  },
  headers?: {
    opUser?: string
    opUserName?: string
  },
): Promise<{
  code: string
  msg: string
  data: Array<{
    databaseName: string
    tableComment: string
    tableCollation: string
    tableName: string
    columnList: Array<{
      columnType: string
      isNullable: string
      dataType: string
      columnComment: string
      ordinalPosition: string
      columnKey: string
      tableName: string
      columnName: string
    }>
  }>
}> {
  return service(
    {
      method: 'GET',
      url: '/assetManager/dataAssertManage/getTableInfoList',
      params: data,
      headers,
    },
    MOCK_DATA.getTableInfoList,
  )
}

/**
 * 预览数据 - DAM004-预览资产数据
 * @param {string} tableId - 数据表ID
 * @param {number} pageIndex - 分页页码
 * @param {number} pageSize - 分页数据量
 * @param {string} opUser - 操作人
 * @param {string} opUserName - 操作人名称
 * @returns {any} - 返回一个解析为响应数据的Promise
 */

export async function previewData(
  data: {
    tableId: string
    pageIndex: number
    pageSize: number
  },
  headers?: {
    opUser?: string
    opUserName?: string
  },
): Promise<{
  code: string
  msg: string
  data: Array<{
    columnList: Array<{
      columnType: string
      isNullable: string
      dataType: string
      columnComment: string
      ordinalPosition: string
      columnKey: string
      tableName: string
      columnName: string
    }>
    dataList: undefined
  }>
}> {
  return service(
    {
      method: 'GET',
      url: '/assetManager/dataAssertManage/previewData',
      params: data,
      headers,
    },
    MOCK_DATA.previewData,
  )
}

/**
 *  - DAM013-修改数据资产状态
 * @param {string} tableId -
 * @param {number} status -
 * @param {string} opUser -
 * @param {string} opUserName -
 * @returns {any} - 返回一个解析为响应数据的Promise
 */

export async function updateDataAssets(
  data: {
    tableId: string
    status: number
  },
  headers?: {
    opUser?: string
    opUserName?: string
  },
): Promise<any> {
  return service(
    {
      method: 'POST',
      url: '/assetManager/dataAssertManage/updateDataAssets',
      data,
      headers,
    },
    MOCK_DATA.updateDataAssets,
  )
}

/**
 *  - DAM014-数据资产打标
 * @param {string} tableId -
 * @param {boolean} labelId -
 * @param {string} opUser -
 * @param {string} opUserName -
 * @returns {any} - 返回一个解析为响应数据的Promise
 */

export async function dataAssetsMarking(
  data: {
    tableId: string
    labelId: boolean
  },
  headers?: {
    opUser?: string
    opUserName?: string
  },
): Promise<any> {
  return service(
    {
      method: 'POST',
      url: '/assetManager/dataAssertManage/dataAssetsMarking',
      data,
      headers,
    },
    MOCK_DATA.dataAssetsMarking,
  )
}

/**
 * 新增数据资产 - DAM005-新增数据资产
 * @param {string} databaseName - 数据库名称
 * @param {string} databaseType - 数据库类型
 * @param {number} datasourceId - 数据源ID
 * @param {number} directoryId - 数据目录ID
 * @param {string} ip - 数据源IP
 * @param {number} port - 数据源IP
 * @param {string} engine - 数据库引擎
 * @param {Object} tableList - 数据表集合
 * @param {string} opUser - 操作人
 * @param {string} opUserName - 操作人名称
 * @returns {any} - 返回一个解析为响应数据的Promise
 */

export async function addDataAssert(
  data: {
    databaseName: string
    databaseType: string
    datasourceId: number
    directoryId: number
    ip: string
    port: number
    engine?: string
    tableList?: Array<{
      tableName?: string
      tableType?: string
      tableComment?: string
      tableCollation?: string
      columnList?: Array<{
        columnName?: string
        dataType?: string
        columnType?: string
        columnlength?: number
        columnComment?: string
        columnDefault?: string
        ordinalPosition?: string
        isNullable?: string
        isPrimaryKey?: string
      }>
    }>
  },
  headers?: {
    opUser?: string
    opUserName?: string
  },
): Promise<{
  code: string
  msg: string
  data: string
}> {
  return service(
    {
      method: 'POST',
      url: '/assetManager/dataAssertManage/addDataAssert',
      data,
      headers,
    },
    MOCK_DATA.addDataAssert,
  )
}

/**
 * 修改数据资产 - DAM006-修改数据资产
 * @param {string} tableId - 数据表ID
 * @param {string} description - 数据表描述
 * @param {Object} columnList - 数据表字段集合
 * @param {string} opUser - 操作人
 * @param {string} opUserName - 操作人名称
 * @returns {any} - 返回一个解析为响应数据的Promise
 */

export async function updateDataAssetTable(
  data: {
    tableId: string
    description?: string
    columnList?: Array<{
      columnId?: string
      columnName?: string
      dataType?: string
      columnType?: string
      columnlength?: number
      columnComment?: string
      columnDefault?: string
      ordinalPosition?: string
      isNullable?: string
      isPrimaryKey?: string
    }>
  },
  headers?: {
    opUser?: string
    opUserName?: string
  },
): Promise<{
  code: string
  msg: string
  data: string
}> {
  return service(
    {
      method: 'POST',
      url: '/assetManager/dataAssertManage/updateDataAssetTable',
      data,
      headers,
    },
    MOCK_DATA.updateDataAssetTable,
  )
}

/**
 * 新增接口 - DAM007-查询目录下资产数据库列表
 * @param {number} directoryId - 目录ID
 * @param {string} databaseName - 数据库名称
 * @param {string} opUser - 操作人
 * @param {string} opUserName - 操作人名称
 * @returns {any} - 返回一个解析为响应数据的Promise
 */

export async function getDataAssetList(
  data: {
    directoryId?: number
    databaseName?: string
  },
  headers?: {
    opUser?: string
    opUserName?: string
  },
): Promise<{
  code: string
  msg: string
  data: Array<{
    databaseId: string
    databaseName: string
    databaseType: string
    engine: string
    description: string
    tableCount: string
  }>
}> {
  return service(
    {
      method: 'GET',
      url: '/assetManager/dataAssertManage/getDataAssetList',
      params: data,
      headers,
    },
    MOCK_DATA.getDataAssetList,
  )
}

/**
 * 查询数据库下表信息列表 - DAM008-查询数据库下表信息列表
 * @param {string} labelId - 标签ID
 * @param {string} tableName - 表名
 * @param {string} tableComment - 表注释
 * @param {number} status - 资产状态1已发布0未发布
 * @param {string} databaseId - 数据库ID
 * @param {number} pageIndex - 页码
 * @param {number} pageSize - 每页数量
 * @param {string} opUser - 操作人
 * @param {string} opUserName - 操作人名称
 * @returns {any} - 返回一个解析为响应数据的Promise
 */

export async function getDataAssetTableList(
  data: {
    labelId?: string
    tableName?: string
    tableComment?: string
    status?: number | null
    databaseId?: string
    pageIndex: number
    pageSize: number
  },
  headers?: {
    opUser?: string
    opUserName?: string
  },
): Promise<{
  code: string
  msg: string
  data: {
    pageIndex: number
    pageSize: number
    totalPages: number
    totalRecords: number
    records: Array<{
      tableId: string
      tableName: string
      status: number
      tableType: string
      labelId: number
      tableComment: string
      tableCollation: string
      description: string
    }>
  }
}> {
  return service(
    {
      method: 'GET',
      url: '/assetManager/dataAssertManage/getDataAssetTableList',
      params: data,
      headers,
    },
    MOCK_DATA.getDataAssetTableList,
  )
}

/**
 * 查询数据表详情 - DAM009-查询数据表详情
 * @param {string} tableId - 数据表ID
 * @param {string} opUser - 操作人
 * @param {string} opUserName - 操作人名称
 * @returns {any} - 返回一个解析为响应数据的Promise
 */

export async function getDataAssetTableDetail(
  data: {
    tableId: string
  },
  headers?: {
    opUser?: string
    opUserName?: string
  },
): Promise<{
  code: string
  msg: string
  data: {
    tableId: string
    tableName: string
    status: number
    tableType: string
    labelId: number
    tableComment: string
    tableCollation: string
    description: string
    columnList: Array<{
      columnName: string
      dataType: string
      columnType: string
      columnlength: string
      columnComment: string
      columnDefault: string
      columnPosition: string
      isNullable: string
      isPrimaryKey: string
    }>
  }
}> {
  return service(
    {
      method: 'GET',
      url: '/assetManager/dataAssertManage/getDataAssetTableDetail',
      params: data,
      headers,
    },
    MOCK_DATA.getDataAssetTableDetail,
  )
}

/**
 * 查询数据表血缘关系 - DAM010-查询数据表血缘关系
 * @param {string} tableId - 数据表ID
 * @param {string} opUser - 操作人
 * @param {string} opUserName - 操作人名称
 * @returns {any} - 返回一个解析为响应数据的Promise
 */

export async function getTableLineage(
  data: {
    tableId: string
  },
  headers?: {
    opUser?: string
    opUserName?: string
  },
): Promise<{
  code: string
  msg: string
  data: undefined
}> {
  return service(
    {
      method: 'GET',
      url: '/assetManager/dataAssertManage/getTableLineage',
      params: data,
      headers,
    },
    MOCK_DATA.getTableLineage,
  )
}

/**
  *  - DAM015-数据资产数据库统计
  
  * @returns {any} - 返回一个解析为响应数据的Promise
*/

export async function databaseCount(data: any): Promise<any> {
  return service(
    {
      method: 'GET',
      url: '/assetManager/dataAssertManage/databaseCount',
      params: data,
    },
    MOCK_DATA.databaseCount,
  )
}

/**
  *  - DAM016-数据资产表统计
  
  * @returns {any} - 返回一个解析为响应数据的Promise
*/

export async function tableCount(data: any): Promise<any> {
  return service(
    {
      method: 'GET',
      url: '/assetManager/dataAssertManage/tableCount',
      params: data,
    },
    MOCK_DATA.tableCount,
  )
}

/**
  *  - DAM017-数据资产表统计
  
  * @returns {any} - 返回一个解析为响应数据的Promise
*/

export async function columnCount(data: any): Promise<any> {
  return service(
    {
      method: 'GET',
      url: '/assetManager/dataAssertManage/columnCount',
      params: data,
    },
    MOCK_DATA.columnCount,
  )
}

/**
 * 根据permissionId删除数据资产权限配置表数据 - DAM021-删除用户数据权限
 * @param {string} permissionId - permissionId
 * @param {string} opUser -
 * @param {string} opUserName -
 * @returns {any} - 返回一个解析为响应数据的Promise
 */

export async function deletedataPermission(
  data: {
    permissionId: string
  },
  headers?: {
    opUser?: string
    opUserName?: string
  },
): Promise<any> {
  return service(
    {
      method: 'POST',
      url: '/assetManager/dataAssertManage/deletedataPermission',
      data,
      headers,
    },
    MOCK_DATA.deletedataPermission,
  )
}

/**
 * 新增数据资产权限配置表数据 - DAM018-新增用户数据权限
 * @param {Object} userIdList - 用户ID集合
 * @param {string} tableId - 表ID
 * @param {string} configValue - 行级别配置信息[{columnName:id,operator:>,value:10}]
 * @param {string} columns - 字段列权限配置，逗号隔开
 * @param {string} opUser - 用户ID
 * @param {string} opUserName - 用户名称
 * @returns {any} - 返回一个解析为响应数据的Promise
 */

export async function saveDataPermission(
  data: {
    userIdList?: Array<{
      userId: string
      userName: string
    }>
    tableId?: string
    configValue?: string
    columns?: string
  },
  headers?: {
    opUser?: string
    opUserName?: string
  },
): Promise<{
  code: number
  msg: string
  data: string
}> {
  return service(
    {
      method: 'POST',
      url: '/assetManager/dataAssertManage/saveDataPermission',
      data,
      headers,
    },
    MOCK_DATA.saveDataPermission,
  )
}

/**
 * 分页查询用户数据权限 - DAM020-分页查询用户数据权限
 * @param {string} tableId - 表ID
 * @param {string} userName - 用户名称
 * @param {number} pageIndex - 查询当前页数
 * @param {number} pageSize - 每页显示的记录条数
 * @returns {any} - 返回一个解析为响应数据的Promise
 */

export async function dataPermissionPageList(data: {
  tableId: string
  userName?: string
  pageIndex: number
  pageSize: number
}): Promise<{
  code: number
  msg: string
  data: Array<{
    pageIndex: number
    pageSize: number
    totalPages: number
    totalRecords: number
    records: Array<{
      permissionId: string
      userId: string
      userName: number
      configValue: string
      columns: number
      createTime: string
      createUser: string
      updateTime: string
      updateUser: string
    }>
  }>
}> {
  return service(
    {
      method: 'GET',
      url: '/assetManager/dataAssertManage/dataPermissionPageList',
      params: data,
    },
    MOCK_DATA.dataPermissionPageList,
  )
}

/**
 * 新增数据资产权限配置表数据 - DAM019-修改用户数据权限
 * @param {string} permissionId - 权限ID
 * @param {string} configValue - 行级别配置信息[{columnName:id,operator:>,value:10}]
 * @param {string} columns - 字段列权限配置，逗号隔开
 * @param {string} opUser - 用户ID
 * @param {string} opUserName - 用户名称
 * @returns {any} - 返回一个解析为响应数据的Promise
 */

export async function updateDataPermission(
  data: {
    permissionId: string
    configValue?: string
    columns?: string
  },
  headers?: {
    opUser?: string
    opUserName?: string
  },
): Promise<{
  code: number
  msg: string
  data: string
}> {
  return service(
    {
      method: 'POST',
      url: '/assetManager/dataAssertManage/updateDataPermission',
      data,
      headers,
    },
    MOCK_DATA.updateDataPermission,
  )
}

/**
 * 查询数据表字段列表 - DAM022-查询数据表字段列表
 * @param {string} tableId - 表ID
 * @param {string} opUser - 操作人
 * @param {string} opUserName - 操作人名称
 * @returns {any} - 返回一个解析为响应数据的Promise
 */

export async function getTableColumnsList(
  data: {
    tableId: string
  },
  headers?: {
    opUser?: string
    opUserName?: string
  },
): Promise<{
  code: string
  msg: string
  data: Array<{
    databaseName: string
    tableComment: string
    tableCollation: string
    tableName: string
    columnList: Array<{
      columnType: string
      isNullable: string
      dataType: string
      columnComment: string
      ordinalPosition: string
      columnKey: string
      tableName: string
      columnName: string
    }>
  }>
}> {
  return service(
    {
      method: 'GET',
      url: '/assetManager/dataAssertManage/getTableColumnsList',
      params: data,
      headers,
    },
    MOCK_DATA.getTableColumnsList,
  )
}

/**
 * 查询用户数据权限详情 - DAM023-查询用户数据权限详情
 * @param {string} permissionId - 表ID
 * @param {string} opUser - 操作人
 * @param {string} opUserName - 操作人名称
 * @returns {any} - 返回一个解析为响应数据的Promise
 */

export async function getDataPermissionDetail(
  data: {
    permissionId: string
  },
  headers?: {
    opUser?: string
    opUserName?: string
  },
): Promise<{
  code: string
  msg: string
  data: {
    permissionId: string
    userId: string
    userName: string
    tableId: string
    tableName: string
    configValue: string
    columnList: Array<{
      columnType: string
      isNullable: string
      dataType: string
      columnComment: string
      ordinalPosition: string
      columnKey: string
      tableName: string
      columnName: string
    }>
  }
}> {
  return service(
    {
      method: 'GET',
      url: '/assetManager/dataAssertManage/getDataPermissionDetail',
      params: data,
      headers,
    },
    MOCK_DATA.getDataPermissionDetail,
  )
}

/**
 *  - DAM024-查询用户列表
 * @param {string} userName -
 * @param {string} tableId - tableId
 * @param {string} opUser -
 * @param {string} opUserName -
 * @returns {any} - 返回一个解析为响应数据的Promise
 */

export async function getUserList(
  data: {
    userName?: string
    tableId: string
  },
  headers?: {
    opUser?: string
    opUserName?: string
  },
): Promise<any> {
  return service(
    {
      method: 'GET',
      url: '/assetManager/dataAssertManage/getUserList',
      params: data,
      headers,
    },
    MOCK_DATA.getUserList,
  )
}

/**
 * 查询数据表列表 - DAM025-查询字段列表
 * @param {string} tableId - 表ID
 * @param {string} opUser - 操作人
 * @param {string} opUserName - 操作人名称
 * @returns {any} - 返回一个解析为响应数据的Promise
 */

export async function getTableInfoListById(
  data: {
    tableId: string
  },
  headers?: {
    opUser?: string
    opUserName?: string
  },
): Promise<{
  code: string
  msg: string
  data: Array<{
    columnType: string
    isNullable: string
    dataType: string
    columnComment: string
    ordinalPosition: string
    columnKey: string
    tableName: string
    selected: boolean
    columnName: string
  }>
}> {
  return service(
    {
      method: 'GET',
      url: '/assetManager/dataAssertManage/getTableInfoListById',
      params: data,
      headers,
    },
    MOCK_DATA.getTableInfoListById,
  )
}

/**
 *  - DAM026-删除数据资产
 * @param {string} tableId -
 * @param {string} opUser -
 * @param {string} opUserName -
 * @returns {any} - 返回一个解析为响应数据的Promise
 */

export async function deleteDataAssets(
  data: {
    tableId: string
  },
  headers?: {
    opUser?: string
    opUserName?: string
  },
): Promise<any> {
  return service(
    {
      method: 'POST',
      url: '/assetManager/dataAssertManage/deleteDataAssets',
      data,
      headers,
    },
    MOCK_DATA.deleteDataAssets,
  )
}

/**
 *  - DAM027-查询数据库详情
 * @param {string} databaseId - 数据库ID
 * @param {string} opUser -
 * @param {string} opUserName -
 * @returns {any} - 返回一个解析为响应数据的Promise
 */

export async function getDatabaseDetail(
  data: {
    databaseId: string
  },
  headers?: {
    opUser?: string
    opUserName?: string
  },
): Promise<{
  code: string
  msg: string
  data: {
    databaseName: string
    tableCount: number
    ip: string
    description: string
    updateUser: string
    updateTime: string
    columnCount: number
    databaseType: string
    port: string
    engine: string
    createTime: string
    createUser: string
    releaseCount: number
  }
}> {
  return service(
    {
      method: 'GET',
      url: '/assetManager/dataAssertManage/getDatabaseDetail',
      params: data,
      headers,
    },
    MOCK_DATA.getDatabaseDetail,
  )
}

/**
 * 发布/停用数据资产
 * @param {Object} data - 请求参数
 * @param {string[]} data.idList - 数据表ID列表
 * @param {number} data.status - 发布状态
 * @param {Object} headers - 请求头
 * @param {string} headers.opUser - 操作人
 * @param {string} headers.opUserName - 操作人名称
 */
export async function releaseDataAssets(
  data: {
    idList: string[]
    status: number
  },
  headers?: {
    opUser?: string
    opUserName?: string
  },
) {
  return service({
    method: 'POST',
    url: '/assetManager/dataAssertManage/releaseDataAsset',
    data,
    headers,
  })
}

/**
 *  - DAM028-批量发布数据资产
 * @param {string} idList - ID集合
 * @param {number} status - 资产状态1已发布0未发布
 * @param {string} opUser -
 * @param {string} opUserName -
 * @returns {any} - 返回一个解析为响应数据的Promise
 */

export async function releaseDataAsset(
  data: {
    idList: string
    status: number
  },
  headers?: {
    opUser?: string
    opUserName?: string
  },
): Promise<any> {
  return service(
    {
      method: 'POST',
      url: '/assetManager/dataAssertManage/releaseDataAsset',
      data,
      headers,
    },
    MOCK_DATA.releaseDataAsset,
  )
}

/**
 * 查询数据表字段列表 - DAM030-根据角色ID查询字段列表
 * @param {string} tableId - 表ID
 * @param {string} roleId - 角色ID
 * @param {string} opUser - 操作人
 * @param {string} opUserName - 操作人名称
 * @returns {any} - 返回一个解析为响应数据的Promise
 */

export async function getColumnsListByRoleId(
  data: {
    tableId: string
    roleId: string
  },
  headers?: {
    opUser?: string
    opUserName?: string
  },
): Promise<{
  code: string
  msg: string
  data: Array<{
    columnType: string
    isNullable: string
    dataType: string
    columnComment: string
    ordinalPosition: string
    columnKey: string
    tableName: string
    selected: string
    columnName: string
  }>
}> {
  return service(
    {
      method: 'GET',
      url: '/assetManager/dataAssertManage/getColumnsListByRoleId',
      params: data,
      headers,
    },
    MOCK_DATA.getColumnsListByRoleId,
  )
}

/**
 * 新增角色数据权限 - DAM031-新增角色数据权限
 * @param {string} roleId - 角色Id
 * @param {string} roleName - 角色名称
 * @param {number} directoryList - 文件夹ID集合
 * @param {Object} databaseList - 数据库集合
 * @param {Object} tableList - 表集合
 * @param {string} opUser - 用户ID
 * @param {string} opUserName - 用户名称
 * @returns {any} - 返回一个解析为响应数据的Promise
 */

export async function saveDataPermissionRole(
  data: {
    roleId: string
    roleName: string
    directoryList?: any[]
    databaseList?: Array<{
      databaseId: string
      databaseName?: string
    }>
    tableList?: Array<{
      tableId: string
      tableName: string
      selected: string
      columns: string
      configValue: string
    }>
  },
  headers?: {
    opUser?: string
    opUserName?: string
  },
): Promise<{
  code: number
  msg: string
  data: string
}> {
  return service(
    {
      method: 'POST',
      url: '/assetManager/dataAssertManage/saveDataPermissionRole',
      data: {
        ...data,
        tableList: JSON.stringify(data.tableList),
      },
      headers,
    },
    MOCK_DATA.saveDataPermissionRole,
  )
}

/**
 * 目录树响应数据结构
 */
export interface DirectoryTreeResponse {
  /** 响应状态码 */
  code: string
  /** 目录树数据数组 */
  data: DirectoryTreeNode[]
  /** 响应消息 */
  msg: string
  /** 处理结果状态 */
  result: string
  [property: string]: any
}

/**
 * 目录树根节点数据结构
 */
export interface DirectoryTreeNode {
  /** 资产数量 */
  assertCount: number
  /** 选中状态 */
  checked: string
  /** 子节点数组（数据库节点） */
  children: DatabaseNode[]
  /** 目录描述信息 */
  description: null | string
  /** 目录唯一标识ID */
  directoryId: string
  /** 目录名称 */
  name: string
  /** 节点类型（用于区分不同层级的节点） */
  nodeType: number
  /** 父节点ID（根节点为null） */
  parentId: null
  [property: string]: any
}

/**
 * 数据库节点数据结构
 */
export interface DatabaseNode {
  /** 资产数量 */
  assertCount: number
  /** 选中状态 */
  checked: string
  /** 子节点数组（表节点） */
  children: TableNode[]
  /** 数据库ID */
  databaseId: string
  /** 数据库名称 */
  databaseName: string
  /** 数据库类型（如MySQL、PostgreSQL等） */
  databaseType: string
  /** 数据库描述信息 */
  description: null | string
  /** 所属目录ID */
  directoryId: string
  /** 数据库引擎类型 */
  engine: null | string
  /** 显示名称 */
  name: string
  /** 节点类型标识 */
  nodeType: number
  /** 父节点ID */
  parentId: string
  /** 数据表数量 */
  tableCount: number
  [property: string]: any
}

/**
 * 数据表节点数据结构
 */
export interface TableNode {
  /** 资产数量 */
  assertCount: number
  /** 选中状态 */
  checked: string
  /** 子节点数组（子表节点） */
  children: SubTableNode[]
  /** 表字段信息 */
  columns: null
  /** 配置值 */
  configValue: null
  /** 所属数据库ID */
  databaseId: string
  /** 所属数据库名称 */
  databaseName: string
  /** 数据库类型 */
  databaseType: string
  /** 表描述信息 */
  description: null | string
  /** 所属目录ID */
  directoryId: string
  /** 数据库引擎类型 */
  engine: null | string
  /** 显示名称 */
  name: string
  /** 节点类型标识 */
  nodeType: number
  /** 父节点ID */
  parentId: string
  /** 是否被选中 */
  selected: null
  /** 表注释说明 */
  tableComment: null | string
  /** 子表数量 */
  tableCount: number
  /** 表唯一标识ID */
  tableId: string
  /** 表名称 */
  tableName: string
  [property: string]: any
}

/**
 * 子表节点数据结构（第三层表节点）
 */
export interface SubTableNode {
  /** 资产数量 */
  assertCount: number
  /** 选中状态 */
  checked: string
  /** 子节点数组（最底层表节点） */
  children: LeafTableNode[]
  /** 表字段信息 */
  columns: null
  /** 配置值 */
  configValue: null
  /** 所属数据库ID */
  databaseId: string
  /** 所属数据库名称 */
  databaseName: string
  /** 数据库类型 */
  databaseType: string
  /** 表描述信息 */
  description: null | string
  /** 所属目录ID */
  directoryId: string
  /** 数据库引擎类型 */
  engine: null | string
  /** 显示名称 */
  name: string
  /** 节点类型标识 */
  nodeType: number
  /** 父节点ID */
  parentId: string
  /** 是否被选中 */
  selected: null
  /** 表注释说明 */
  tableComment: null | string
  /** 子表数量 */
  tableCount: number
  /** 表唯一标识ID */
  tableId: string
  /** 表名称 */
  tableName: string
  [property: string]: any
}

/**
 * 叶子表节点数据结构（最底层的表节点）
 */
export interface LeafTableNode {
  /** 选中状态 */
  checked: string
  /** 表字段信息 */
  columns: null
  /** 配置值 */
  configValue: null
  /** 所属数据库ID */
  databaseId: string
  /** 节点类型标识 */
  nodeType: number
  /** 是否被选中 */
  selected: null
  /** 表注释说明 */
  tableComment: null
  /** 表唯一标识ID */
  tableId: string
  /** 表名称 */
  tableName: string
  [property: string]: any
}

/**
 * 获取目录树数据
 * @param params - 请求参数
 * @param params.roleId - 角色ID，用于权限控制
 * @param headers - 请求头信息
 * @param headers.opUser - 操作用户ID
 * @param headers.opUserName - 操作用户名称
 * @returns {Promise<DirectoryTreeResponse>} 返回目录树数据的Promise
 */
export async function getDiretoryTree(
  params: {
    /** 角色ID，用于获取该角色有权限访问的目录树 */
    roleId: string
  },
  headers?: {
    /** 操作用户ID */
    opUser?: string
    /** 操作用户名称 */
    opUserName?: string
  },
): Promise<DirectoryTreeResponse> {
  return service({
    method: 'GET',
    url: '/assetManager/dataAssertManage/getDiretoryTree',
    params,
    headers,
  })
}

export interface SchemaInfo {
  port: string
  ip: string
  dbType: string
  tableDbname: string
  tableSchema: string
  key: string
}
export interface DatabaseInfo {
  /** 模式列表，特定数据库有三层结构，可能库下面是模式，模式下面是表 */
  schemaList: SchemaInfo[]
  /** 数据库名称 */
  databaseName: string
  /** 数据库描述 */
  customComment: string
  /** 数据库IP */
  ip: string
  /** 数据库类型，如 mysql */
  dbType: string
  /** 数据库表名 */
  tableDbname: string
  /** 数据库模式 */
  tableSchema: string
  /** 数据库端口 */
  port: string
  /** 数据库引擎，如 InnoDB */
  engine: string
  /** 数据源ID */
  datasourceId: string
  /** 数据库唯一标识 */
  key: string
  /** 是否选中 */
  selected: boolean
}
export interface SourceAndDBListData {
  /** 数据源描述 */
  dbDesc: string
  /** 数据源类型 */
  dbType: string
  /** 数据源URL */
  dbUrl: string
  /** 数据源ID */
  id?: string
  /** 数据源元信息 */
  metaUrlInfo: any
  /** 数据库列表 */
  databaseList: DatabaseInfo[]
  key?: string
}
export interface SourceAndDBListResponse {
  code: string
  msg: string
  data: SourceAndDBListData[]
}

/**
 *  - DAM032-查询数据源+数据库目录结构
 * @param {string} keyword - 搜索数据源
 * @param {string} opUser -
 * @param {string} opUserName -
 * @returns {any} - 返回一个解析为响应数据的Promise
 */
export async function getSourceAndDBList(
  data: {
    keyword?: string
  },
  headers?: {
    opUser?: string
    opUserName?: string
  },
): Promise<SourceAndDBListResponse> {
  return service(
    {
      method: 'GET',
      url: '/assetManager/dataAssertManage/getSourceAndDBList',
      params: data,
      headers,
    },
    MOCK_DATA.getSourceAndDBList,
  )
}

/**
 *  - 编辑数据库注释
 * @param {string} description
 * @param {string} databaseId -
 * @returns {any} - 返回一个解析为响应数据的Promise
 */

export async function updateDatabaseDesc(
  data: {
    databaseId: string
    description?: string
  },
  headers?: {
    opUser?: string
    opUserName?: string
  },
): Promise<any> {
  return service(
    {
      method: 'post',
      url: '/assetManager/dataAssertManage/updateDatabaseDesc',
      data: data,
      headers,
    },
    MOCK_DATA.getSourceAndDBList,
  )
}

/**
 *  - 删除库
 * @param {string} databaseId -
 * @returns {any} - 返回一个解析为响应数据的Promise
 */

export async function deleteDatabase(
  data: {
    databaseId: string
  },
  headers?: {
    opUser?: string
    opUserName?: string
  },
): Promise<any> {
  return service({
    method: 'post',
    url: '/assetManager/dataAssertManage/deleteDatabase',
    data: data,
    headers,
  })
}
