<template>
  <a-space direction="vertical" class="approver-selector" style="width: 100%">
    <div class="tree-container">
      <a-tree
        :tree-data="treeData"
        :load-data="onLoadData"
        :selected-keys="selectedKeys"
        :expanded-keys="expandedKeys"
        @select="handleTreeNodeSelect"
        :auto-expand-parent="false"
        :show-icon="false"
        class="scrollable-tree"
      />
    </div>
    <div class="approver-selector__footer">
      <a-button @click="handleCancel">取消</a-button>
      <a-button type="primary" :disabled="!canConfirm" @click="handleConfirm">确定</a-button>
    </div>
  </a-space>
</template>

<script setup lang="ts">
import { ref, reactive, watch, defineEmits, defineProps } from 'vue'
import { getDeptChildList, getDeptUserList } from '@/api/approval-workflow'

interface LabelAndValue {
  label: string
  value: string
}

interface TreeNode {
  key: string
  title: string
  value: string
  tenantId?: string
  isLeaf: boolean
  children?: TreeNode[]
  parentId?: string
}

interface ApiResponse {
  code: string
  data: {
    records: Array<{
      userId: string
      userName: string
      userStatus: number
      userType: number
      userTypeName: string
    }>
    totalRecords: number
    pageIndex: number
    pageSize: number
  }
}

// Props
const props = defineProps<{
  deptList: any[]
}>()

// Emits
const emit = defineEmits<{
  cancel: []
  confirm: [value: Partial<LabelAndValue>]
}>()

// Reactive data
const treeData = ref<TreeNode[]>([])
const selectedValue = ref<Partial<LabelAndValue>>({})
const selectedKeys = ref<string[]>([])
const expandedKeys = ref<string[]>([])
const canConfirm = ref<boolean>(false)
const pageIndex = ref<number>(1)
const pageSize = ref<number>(30)

/**
 * 将部门列表转换为树形数据结构
 * @param deptList 部门列表
 * @returns 树形数据
 */
const convertDeptListToTreeData = (deptList: any[]): TreeNode[] => {
  return deptList.map((dept) => ({
    key: dept.deptId,
    title: dept.deptName,
    value: dept.deptId,
    tenantId: dept.tenantId,
    isLeaf: false, // 部门不是叶子节点
    children: [],
  }))
}

/**
 * 递归查找树形数据中的节点
 */
const findNodeInTreeData = (key: string): TreeNode | null => {
  const findInTree = (nodes: TreeNode[]): TreeNode | null => {
    for (const node of nodes) {
      if (node.key === key) {
        return node
      }
      if (node.children && node.children.length > 0) {
        const found = findInTree(node.children)
        if (found) return found
      }
    }
    return null
  }
  return findInTree(treeData.value)
}

const handleTreeNodeSelect = (keys: string[]) => {
  if (keys.length === 0) {
    selectedKeys.value = []
    selectedValue.value = {}
    canConfirm.value = false
    return
  }

  const selectedKey = keys[0]
  const nodeData = findNodeInTreeData(selectedKey)
  console.log('节点数据', nodeData)

  if (nodeData) {
    if (nodeData.isLeaf && nodeData.key !== 'load-more') {
      console.log('叶子节点 - 用户')
      canConfirm.value = true
      // 如果是叶子节点（用户），则选中
      selectedKeys.value = [selectedKey]
      selectedValue.value = {
        label: nodeData.title,
        value: nodeData.key,
      }
    } else {
      // 如果是非叶子节点（部门），不选中，只是展开
      canConfirm.value = false
      if (nodeData.key === 'load-more') {
        console.log('加载更多用户...', nodeData)
        getDeptUserList({
          deptId: nodeData.value,
          pageIndex: ++pageIndex.value,
          pageSize: pageSize.value,
          queryType: 1,
          tenantId: nodeData.tenantId || '',
        }).then((res: ApiResponse) => {
          if (res.code === '000000') {
            console.log('成功加载更多用户', res)
            // 找出 当前节点 的 parent 节点，将 mockUserList 添加到 parent 节点的 children 中
            const parentNode = findNodeInTreeData(nodeData.parentId!)
            if (parentNode) {
              const arr: TreeNode[] = res.data.records.map((user: any) => ({
                key: user.userId,
                title: user.userName,
                value: user.userId,
                isLeaf: true,
              }))
              // 删除之前的加载更多按钮
              parentNode.children =
                parentNode.children?.filter((child: TreeNode) => child.key !== 'load-more') || []
              // 判断是否还有下一页，如果有，则添加加载更多按钮
              const isLastPage = res.data.totalRecords <= res.data.pageIndex * res.data.pageSize
              if (!isLastPage) {
                arr.push({
                  key: 'load-more',
                  title: '加载更多用户...',
                  value: nodeData.parentId!,
                  tenantId: nodeData.tenantId,
                  parentId: nodeData.parentId,
                  isLeaf: false,
                })
              }
              parentNode.children = [...parentNode.children, ...arr]
              treeData.value = [...treeData.value]
            }
          }
        })
      } else {
        console.log('非叶子节点 - 部门', selectedKey)
      }
      if (!expandedKeys.value.includes(selectedKey)) {
        expandedKeys.value = [...expandedKeys.value, selectedKey]
      } else {
        expandedKeys.value = expandedKeys.value.filter((key) => key !== selectedKey)
      }
      selectedKeys.value = []
      selectedValue.value = {}
    }
  }
}

const onLoadData = async (treeNode: any) => {
  console.log('加载数据', treeNode.dataRef)

  if (treeNode.dataRef.children && treeNode.dataRef.children.length > 0) {
    return
  }

  const { key, tenantId, isLeaf, value: deptId } = treeNode.dataRef

  if (!isLeaf && key !== 'load-more') {
    try {
      // 获取部门列表和用户列表
      const [deptListRes, userListRes] = await Promise.all([
        getDeptChildList({ deptId: key, tenantId }),
        getDeptUserList({
          deptId: key,
          pageIndex: pageIndex.value,
          pageSize: pageSize.value,
          queryType: 1,
          tenantId,
        }),
      ])

      // 转换部门数据
      const deptList: TreeNode[] = deptListRes.data.map((dept: any) => ({
        key: dept.deptId,
        title: dept.deptName,
        value: dept.deptId,
        tenantId: dept.tenantId,
        isLeaf: false,
        children: [],
      }))

      // 转换用户数据
      const userList: TreeNode[] = userListRes.data.records.map((user: any) => {
        const result: TreeNode = {
          key: user.userId,
          title: user.userName,
          value: user.userId,
          isLeaf: true,
        }
        return result
      })
      // 判断是否还有下一页，如果有，则添加加载更多按钮
      const isLastPage =
        userListRes.data.totalRecords <= userListRes.data.pageIndex * userListRes.data.pageSize
      if (!isLastPage) {
        userList.push({
          key: 'load-more',
          title: '加载更多用户...',
          value: deptId,
          tenantId,
          parentId: treeNode.dataRef.key,
          isLeaf: false,
        })
      }

      // 将子节点添加到当前节点
      treeNode.dataRef.children = [...deptList, ...userList]

      // 更新 treeData 以触发重新渲染
      treeData.value = [...treeData.value]

      expandedKeys.value = [...expandedKeys.value, key]

      console.log('加载完成', treeNode.dataRef.children)
    } catch (error) {
      console.error('加载数据失败', error)
    }
  }
}

const handleCancel = () => {
  emit('cancel')
  selectedValue.value = {}
  selectedKeys.value = []
  canConfirm.value = false
}

const handleConfirm = () => {
  emit('confirm', selectedValue.value)
  selectedValue.value = {}
  selectedKeys.value = []
  canConfirm.value = false
}

// Watch
watch(
  () => props.deptList,
  (newVal: any[]) => {
    console.log('部门列表', newVal)
    if (!newVal.length) {
      treeData.value = []
      return
    }

    // 转换为树形数据格式
    treeData.value = newVal.map((dept) => ({
      key: dept.deptId,
      title: dept.deptName,
      value: dept.deptId,
      tenantId: dept.tenantId,
      isLeaf: false,
      children: [],
    }))
  },
  { immediate: true },
)
</script>

<style lang="less" scoped>
.approver-selector {
  &__footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 10px;
  }
}

.tree-container {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  padding: 8px;
  height: 300px;
  overflow-y: auto;
}

.scrollable-tree {
  max-height: 800px;
  overflow-y: auto;

  // 自定义滚动条样式
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;

    &:hover {
      background: #a8a8a8;
    }
  }
}
</style>
