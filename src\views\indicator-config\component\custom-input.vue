<template>
  <div class="sql-input-container">
    <!-- 输入框 -->
    <a-input
      ref="inputRef"
      type="textarea"
      v-model:value="inputValue"
      @click="handleInputFocus"
      @blur="handleInputBlur"
      @change="handleInputChange"
      @keyup="handleEnterKeyDown"
      placeholder="输入 SQL 表达式，例如 SUM([column])"
    >
      <template #prefix> = </template>
    </a-input>
    <!-- 函数提示列表 -->
    <div v-if="showHints && filterSqlFunctions.length" class="hint-list">
      <div
        v-for="func in filterSqlFunctions"
        :class="`hint-item hint-item-hover`"
        :key="func.name"
        @mousedown.prevent="handleFunctionSelect(func)"
      >
        <FunctionOutlined /> {{ func.name }}
      </div>
    </div>
    <div v-if="showFiled" class="hint-list">
      <div
        class="hint-item hint-item-hover"
        v-for="func in fieldList"
        :key="func.title"
        @mousedown.prevent="filedSelect(func)"
      >
        <FunctionOutlined /> {{ func.title }}
      </div>
    </div>
    <!-- 函数详情列表 -->
    <div v-if="showFuncTips" class="hint-list func-tips">
      <template v-if="attribute">
        <div class="top-box">
          {{ attribute.name }} (
          <span v-for="(item, index) in attribute.children" :key="item.name">
            <span class="name">{{ item.name }} </span>
            <span v-if="index < attribute.children.length - 1">，</span>
          </span>
          )
        </div>
        <a-divider />
        <div class="content">
          <div>{{ attribute.tips }}</div>
          <div class="expression-helper-popover-arguments">
            <div class="expression-helper" v-for="item in attribute.children" :key="item.name">
              <div class="emotion-1ui2wm">{{ item.name }}</div>
              <div class="emotion-tips">{{ item.tips }}</div>
            </div>
          </div>
          <div class="emotion-1xcnth1">示例</div>
          <div class="emotion-pxzft">
            {{ attribute.example }}
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed, onMounted, nextTick } from 'vue'
import { FunctionOutlined } from '@ant-design/icons-vue'
import { attributeList } from './attribute'

interface Item {
  name: string
  template?: string
  title?: string
}

// 响应式数据
const startPos = ref(0)
const endPos = ref(0)
const filterValue = ref('')
const inputValue = defineModel({
  default: '',
}) // 绑定输入框的值
const inputValueBak = ref('')
const previousValue = ref('') // 存储上一次的值
const selectValue: Record<string, any> = ref({})
const showHints = ref(inputValue.value ? false : true)
const showFuncTips = ref(false)
const showFiled = ref(false)
const selectedParamIndex = ref(-1)
const inputRef = ref<any>(null)

const props = defineProps({
  fieldList: {
    type: Array,
    default: () => [],
  },
})

const attribute: any = computed(() => {
  return attributeList.find((item) => item.name === selectValue.value?.name)
})

// 预定义 SQL 函数
const sqlFunctions: Item[] = attributeList.map((item) => {
  return {
    name: item.name,
    template: item.template,
  }
})

const filterSqlFunctions = computed(() => {
  console.log('[ filterValue.value ] >', filterValue.value)
  if (!filterValue.value) return sqlFunctions
  return sqlFunctions.filter((item) => {
    return item.name.toLowerCase().includes(filterValue.value.trim().toLowerCase())
  })
})

onMounted(() => {
  document.addEventListener('click', (e) => {
    if (!inputRef.value?.$el?.contains(e.target)) {
      console.log('[ 1 ] >', 1)
      showHints.value = false
      showFuncTips.value = false
      showFiled.value = false
    }
  })
})

// 处理函数选择
const handleFunctionSelect = (item: Item) => {
  // inputValue.value += item.name + '()'
  showHints.value = false
  showFuncTips.value = true
  selectValue.value = item
  if (startPos.value === endPos.value) {
    let index = inputValue.value.length - 1
    let str = inputValue.value[index]
    while (!item.name.startsWith(str)) {
      if (str && str.startsWith(' ')) {
        startPos.value = index
        break
      }
      if (index >= 1) {
        index -= 1
        str = inputValue.value[index] + str
      } else {
        break
      }
    }
    startPos.value = index
  }
  const beforeStr = inputValue.value.substring(0, startPos.value)
  const afterStr = inputValue.value.substring(endPos.value)
  const newValue =
    beforeStr + (beforeStr && !beforeStr.endsWith(' ') ? ' ' : '') + item.name + '()' + afterStr
  inputValue.value = newValue
  nextTick(() => {
    const inputElement = inputRef.value.$el.querySelector('input')
    startPos.value = inputElement.selectionEnd
  })
  inputValueBak.value = inputValue.value
  previousValue.value = newValue
}

// 参数选择
const filedSelect = (item: Item) => {
  // inputValue.value += item.name + '()'
  showFiled.value = false
  showFuncTips.value = true
  const inputElement = inputRef.value.$el.querySelector('input') // 获取 input 的 DOM 元素
  const startPos = inputElement.selectionStart // 获取光标起始位置
  const endPos = inputElement.selectionEnd // 获取光标结束位置
  // 在光标处插入新内容
  const newValue =
    inputValue.value.substring(0, startPos) + // 光标前的部分
    item.title +
    ']' + // 要插入的内容
    inputValue.value.substring(endPos) // 光标后的部分
  inputValue.value = newValue
  previousValue.value = newValue
}

// 监听 input 事件
const handleInputChange = (event: any) => {
  const newValue = inputValue.value // 获取当前输入框的值
  const inputElement = inputRef.value.$el.querySelector('input')
  endPos.value = inputElement.selectionEnd // 获取光标位置
  if (event.inputType === 'deleteContentBackward') {
    const lastStr = inputValue.value.split(' ').pop()
    if (!lastStr) filterValue.value = ''
    if (lastStr && !['(', ')'].includes(lastStr)) {
      filterValue.value = lastStr
    }
    startPos.value = endPos.value
  } else if (event.inputType === 'insertText') {
    if (!event.data.trim()) {
      startPos.value = endPos.value - 1
      filterValue.value = ''
    } else {
      filterValue.value = inputValue.value.slice(startPos.value, endPos.value)
    }
  }

  if (filterSqlFunctions.value.length) {
    showHints.value = true
    showFuncTips.value = false
  }
  const diffStr = getStringDifference(newValue, previousValue.value) // 计算两个字符串的长度差异

  if (diffStr === '[') {
    showFiled.value = true
    showHints.value = false
    showFuncTips.value = false
  }
}

// 计算两个字符串的长度差异
const getStringDifference = (a: string, b: string) => {
  const maxLength = Math.max(a.length, b.length)
  const length = Math.abs(a.length - b.length)
  const str = a.length >= b.length ? a : b
  let diffIndex = -1
  // 找到第一个不同的字符的索引
  for (let i = 0; i < maxLength; i++) {
    if (a[i] !== b[i]) {
      diffIndex = i
      break
    }
  }
  // console.log(diffIndex)
  // 如果没有不同的字符，则返回空字符串
  if (diffIndex === -1) {
    return ''
  }
  // 从第一个不同字符开始截取差异部分
  let diff = str.slice(diffIndex, diffIndex + length)
  return diff
}

// 处理字段选择
const handleFieldSelect = (field: string) => {
  if (selectedParamIndex.value === -1) return
  const newValue =
    inputValue.value.slice(0, selectedParamIndex.value) +
    field +
    inputValue.value.slice(selectedParamIndex.value + 2)

  inputValue.value = newValue
  selectedParamIndex.value = -1
  inputRef.value?.focus()
}

// 查找下一个参数占位符
const findNextParamPosition = () => {
  const position = inputValue.value.indexOf('[]')
  selectedParamIndex.value = position > -1 ? position : -1
}

function handleInputFocus() {
  startPos.value = inputRef.value.$el.querySelector('input').selectionStart
  if (!inputValue.value) showHints.value = true
  if (inputValue.value) {
    const { inKuoHao, openParenIndex } = isCursorInsideParentheses(inputValue.value, startPos.value)
    if (inKuoHao) {
      const matchedString = findMatchedString(inputValue.value, openParenIndex - 1)
      if (matchedString) {
        selectValue.value = {
          name: matchedString,
        }
        showHints.value = false
        showFuncTips.value = true
      }
    } else {
      const matchedString = findMatchedString(inputValue.value, startPos.value)
      filterValue.value = matchedString
      showHints.value = !!matchedString
      showFuncTips.value = false
    }
  }
}
// 找括号前的字符串
function findMatchedString(text: string, cursorPosition: number) {
  // 从光标位置向后查找最近的 '('
  let openParenIndex = -1
  for (let i = cursorPosition; i < text.length; i++) {
    if (text[i] === '(') {
      openParenIndex = i
      break
    }
  }

  // 如果没有找到 '(', 返回空字符串
  if (openParenIndex === -1) {
    return ''
  }

  // 从光标位置向前查找函数名的起始位置（直到空格或字符串开头）
  let startIndex = cursorPosition
  for (let i = cursorPosition; i >= 0; i--) {
    if (text[i] === ' ' || i === 0) {
      startIndex = i === 0 ? 0 : i + 1
      break
    }
  }

  // 提取函数名（从起始位置到 '(' 之间的内容）
  const matchedString = text.slice(startIndex, openParenIndex)

  return matchedString
}
// 判断是否在括号
function isCursorInsideParentheses(text: string, cursorPosition: number) {
  const obj = {
    inKuoHao: false,
    openParenIndex: -1,
    closeParenIndex: -1,
  }
  // 从光标位置向前查找最近的 '('

  for (let i = cursorPosition - 1; i >= 0; i--) {
    if (!text[i].trim()) break
    if (text[i] === '(') {
      obj.openParenIndex = i
      break
    }
  }

  // 从光标位置向后查找最近的 ')'
  for (let i = cursorPosition; i < text.length; i++) {
    if (text[i] === ')') {
      obj.closeParenIndex = i
      break
    }
  }

  // 判断光标是否严格在 () 内
  if (obj.openParenIndex !== -1 && obj.closeParenIndex !== -1) {
    obj.inKuoHao = true
  }

  return obj
}

// 处理输入框失焦
const handleInputBlur = () => {
  setTimeout(() => {
    showHints.value = false
    showFuncTips.value = false
    showFiled.value = false
    selectedParamIndex.value = -1
  }, 200)
}

const getValue = () => {
  const value = inputValue.value
  const newStr = replaceContentInsideBrackets(value, props.fieldList)
  return newStr
}

const replaceContentInsideBrackets = (str, replacementMap) => {
  // 使用正则表达式匹配所有 [] 中的内容，并根据 replacementMap 动态替换
  const result = str.replace(/\[(.*?)\]/g, (match, content) => {
    // 在 replacementMap 中查找匹配的 name
    const replacement = replacementMap.find((item) => item.name === content)
    // 如果找到匹配项，返回对应的 value；否则保留原内容
    return replacement ? replacement.value : match
  })

  return result
}

const handleEnterKeyDown = (e: any) => {
  if (e.key === '[') {
    showFiled.value = true
    showHints.value = false
    showFuncTips.value = false
  }
}

defineExpose({
  getValue,
})

// 监听输入值变化
watch(inputValue, (newVal) => {
  if (newVal.includes('[]')) {
    findNextParamPosition()
  }
})
</script>

<style lang="less" scoped>
.sql-input-container {
  position: relative;
}

input {
  width: 100%;
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
}

.hint-list {
  width: 350px !important;
}

.hint-list,
.field-selector {
  position: absolute;
  top: 36px;
  width: 100%;
  background: white;
  border: 1px solid #ddd;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  max-height: 400px;
  overflow-y: auto;
  z-index: 1000;
}

.hint-item,
.field-item {
  padding: 6px 8px;
  cursor: pointer;
  transition: background 0.2s;
}

.hint-item-hover:hover,
.field-item:hover {
  background: #509ee3;
}

.field-selector {
  top: calc(100% + 5px);
}

.func-tips {
  padding: 20px 0;
  max-width: 500px;
  .top-box {
    padding: 0 16px;
    .name {
      color: rgb(239, 140, 140);
    }
  }
  .content {
    padding: 0 16px;
    .expression-helper-popover-arguments {
      margin: 16px 0;
      display: grid;
      gap: 10px;
      .expression-helper {
        display: flex;
        gap: 20px;
        .emotion-1ui2wm {
          width: 64px;
          color: rgb(239, 140, 140);
          text-align: right;
        }
        .emotion-tips {
        }
      }
    }
    .emotion-1xcnth1 {
      margin-bottom: 8px;
    }
    .emotion-pxzft {
      padding: 8px;
      background: rgb(250, 251, 252);
      color: rgb(76, 87, 115);
      font-size: 13px;
    }
  }
}
</style>
