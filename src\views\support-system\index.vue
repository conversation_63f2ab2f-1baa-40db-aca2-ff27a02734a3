<script lang="ts" setup>
import { ref } from 'vue'
import html2canvas from 'html2canvas'

import cardBox from '@/components/card-box/card-box.vue'
import {
  tableColumns,
  tableColumns2,
  tableColumns3,
  tableColumns4,
  tableDataSource,
  tableDataSource2,
  tableDataSource3,
  tableDataSource4,
} from './data'
import chart1 from './components/chart1.vue'
import chart2 from './components/chart2.vue'
import chart3 from './components/chart3.vue'
import chart4 from './components/chart4.vue'
import { reqAnalysis } from './api'

const activeKey = ref('2')
const loading = ref(false)
const chartFile = ref<any>([])

const handleDownload = async () => {
  loading.value = true
  setTimeout(async () => {
    const container = document.querySelectorAll('.download-chart') as unknown as HTMLElement
    const tasks = [].map.call(container, (item: HTMLElement, index: number) => {
      return html2canvas(item, { scale: 2 }).then((canvas) => {
        return new Promise<File>((resolve) => {
          canvas.toBlob((blob) => {
            if (blob) {
              const file = new File([blob], `医院管理决策支持系统${index + 1}.png`, {
                type: 'image/png',
              })

              // 你可以将 file 用于上传、下载等操作
              // 示例：下载
              // if (index === 5) {
              //   const link = document.createElement('a')
              //   link.href = URL.createObjectURL(file)
              //   link.download = file.name
              //   link.click()
              //   URL.revokeObjectURL(link.href)
              // }

              chartFile.value.push(file)
              resolve(file)
            }
          }, 'image/png')
        })
      })
    })

    const res = await Promise.all(tasks)
    console.log('%c [ res ]-44', 'font-size:13px; background:#aa9d15; color:#eee159;', res)
    const data: any = new FormData()
    data.append('p1', res[0])
    data.append('p2', res[1])
    data.append('p3', res[2])
    data.append('p4', res[3])
    data.append('p5', res[4])
    data.append('p6', res[5])
    data.append('p7', res[6])
    data.append('p8', res[7])
    data.append('recordId', '123')

    const res2 = await reqAnalysis(data)
    console.log('%c [ res2 ]-71', 'font-size:13px; background:#49058d; color:#8d49d1;', data, res2)
    loading.value = false

    // 如果需要下载所有图表，可以在这里处理
    // 例如，将所有 canvas 合并为一个大图表，然后下载
    // const link = document.createElement('a');
    // link.href = canvas.toDataURL('image/png');
    // link.download = '医院管理决策支持系统.png';
    // link.click();
  }, 500)
}
</script>

<template>
  <cardBox title="LQ医院管理决策支持" subTitle="LQ医院管理决策支持系统">
    <div class="support-system-page">
      <a-tabs v-model:activeKey="activeKey">
        <a-tab-pane key="1" tab="样本列表">todo...</a-tab-pane>
        <a-tab-pane key="2" tab="分析" force-render>
          <h3>医院有效性可视化结果</h3>
          <div class="echarts-box">
            <chart1 />
            <chart2 />
            <chart3 />
            <chart4 />
          </div>
          <div class="table-box">
            <h3>医院有效性得分明细列表</h3>
            <a-table
              class="download-chart"
              bordered
              :columns="tableColumns"
              :dataSource="tableDataSource"
              style="margin-bottom: 16px"
            ></a-table>
            <a-table
              class="download-chart"
              bordered
              :columns="tableColumns2"
              :dataSource="tableDataSource2"
              style="margin-bottom: 16px"
            ></a-table>
            <a-table
              class="download-chart"
              bordered
              :columns="tableColumns3"
              :dataSource="tableDataSource3"
              style="margin-bottom: 16px"
            ></a-table>
            <a-table
              class="download-chart"
              bordered
              :columns="tableColumns4"
              :dataSource="tableDataSource4"
              style="margin-bottom: 16px"
            ></a-table>
          </div>
          <div class="btns">
            <a-button type="primary" @click="handleDownload" :loading="loading">下载报告</a-button>
          </div>
        </a-tab-pane>
      </a-tabs>
    </div>
  </cardBox>
</template>

<style lang="less" scoped>
.support-system-page {
  h3 {
    color: #08e;
  }
  .echarts-box {
    overflow: hidden;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: repeat(2, 1fr);
    gap: 16px;
    width: 100%;
  }
  .btns {
    display: flex;
    justify-content: flex-end;
  }
}
</style>
