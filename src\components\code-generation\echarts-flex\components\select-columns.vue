<template>
  <div class="table-columns-add">
    <a-button type="primary" @click="addHandle">新增</a-button>
  </div>
  <a-table :dataSource="newModelValue" :columns="columns">
    <template #bodyCell="{ column, record, index }">
      <template v-if="column.key === 'operate'">
        <a-button type="link" @click="del(index)">删除</a-button>
      </template>
      <template v-else-if="column.key === 'type'">
        <a-select
          v-model:value="record[column.key]"
          @change="changeType(record)"
          style="width: 120px"
          placeholder="请选择类型"
        >
          <a-select-option value="date-picker">date-picker</a-select-option>
          <a-select-option value="select">select</a-select-option>
          <a-select-option value="radio-group">radio-group</a-select-option>
        </a-select>
        <FormOutlined
          v-if="record[column.key] !== 'date-picker'"
          @click="addOptions(record)"
          style="margin-left: 3px"
        />
      </template>
      <a-input
        type="text"
        v-model:value="record[column.key]"
        :placeholder="'请输入' + column.title"
        v-else
      />
    </template>
  </a-table>

  <a-modal v-model:open="open" title="选项列表" @ok="open = false">
    <div>
      <a-button @click="addValue">新增</a-button>
    </div>
    <a-table :dataSource="selectValue.options" :columns="optionsColumns">
      <template #bodyCell="{ column, record, index }">
        <template v-if="column.key === 'operate'">
          <a-button type="link" @click="delOptions(index)">删除</a-button>
        </template>
        <a-input
          v-else
          type="text"
          v-model:value="record[column.key]"
          :placeholder="'请输入' + column.title"
        />
      </template>
    </a-table>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { FormOutlined } from '@ant-design/icons-vue'

interface Columns {
  modelValue: any[]
}

const open = ref(false)
const selectValue: Record<string, any> = ref({})

const emit = defineEmits(['update:modelValue'])

const props: Columns = withDefaults(defineProps<Columns>(), {
  modelValue: () => [],
})

const optionsColumns = ref([
  {
    title: '名称',
    dataIndex: 'label',
    key: 'label',
  },
  {
    title: '值',
    dataIndex: 'value',
    key: 'value',
  },
  {
    title: '操作',
    dataIndex: 'operate',
    key: 'operate',
  },
])

const columns = ref([
  {
    title: '字段名',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '字段描述',
    dataIndex: 'description',
    key: 'description',
  },
  {
    title: '类型',
    dataIndex: 'type',
    key: 'type',
  },
  {
    title: '操作',
    dataIndex: 'operate',
    key: 'operate',
  },
])

const newModelValue = computed({
  get: () => props.modelValue,
  set: (value) => {
    emit('update:modelValue', value)
  },
})

const addHandle = () => {
  newModelValue.value.push({
    name: '',
    desc: '',
    type: 'date-picker',
  })
}

const changeType = (data: any) => {
  if (data.type === 'select') data.options = []
}

const addValue = () => {
  if (!selectValue.value?.options) selectValue.value.options = []
  selectValue.value.options.push({ label: '', value: '' })
}

const delOptions = (index: number) => {
  selectValue.value.options.splice(index, 1)
}

const addOptions = (data: any) => {
  open.value = true
  selectValue.value = data
}

const del = (index: number) => {
  if (newModelValue.value.length > 1) {
    newModelValue.value.splice(index, 1)
  }
}

const upData = () => {
  emit('update:modelValue', newModelValue.value)
}

// 导出 show 方法
defineExpose({
  upData,
})
</script>

<style scoped lang="less">
.table-columns-add {
  text-align: right;
  margin-bottom: 16px;
}
</style>
