import { useUserStore } from '@/stores/user'
import router from '@/router/core'

export function parseUrlWithVaribles(url?: string): string | undefined {
  if (!url) {
    return
  }
  const ENV = window.ENV
  // 匹配 url 中 `${xxx}` 格式的字符
  const reg = /\${.+\}/gi
  const matched = url.match(reg)
  if (matched) {
    const key = matched[0].replace(/\${|}/g, '')

    // 检查 window.ENV[key] 是否存在
    if (key in ENV) {
      const repalcedUrl = url.replace(reg, ENV[key])
      return repalcedUrl
    }
  }
  return url
}

export function jumpToUrl(currentItem: any) {
  const { token } = useUserStore()
  if (currentItem && currentItem.menuUrl) {
    if (currentItem.menuType === 4) {
      // 判断menuUrl是否有?号
      if (currentItem.menuName === '模型开发训练') {
        router.push(
          `/iframePage?pageName=${currentItem.menuName}&token=${token}&url=${encodeURIComponent(currentItem.menuUrl)}&menuId=${currentItem.menuId}`,
        )
      } else {
        router.push(
          `/iframePage?pageName=${currentItem.menuName}&url=${encodeURIComponent(currentItem.menuUrl)}&menuId=${currentItem.menuId}`,
        )
      }
    } else if (currentItem.menuType === 3) {
      const url =
        currentItem.menuUrl.indexOf('?') > -1 ? `${currentItem.menuUrl}` : `${currentItem.menuUrl}`
      window.open(url)
    } else {
      let url = currentItem.menuUrl || ''
      if (url.startsWith('/')) {
        url = url.slice(1)
      }
      router.push(`/${url}`)
    }
  }
}
