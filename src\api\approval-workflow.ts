import service from '@/api'

export interface WorkflowItem {
  id?: string
  name: string
  description: string
  workflowType?: string
  pushResultUrl: string
  updatedTime?: string
  updateUserName?: string
  nodes?: WorkflowNode[]
}

export interface WorkflowNode {
  id?: string
  nodeName?: string
  nodeOrder?: number
  approverId?: any
  approverType?: number
  nodeType?: number
}

export interface ApiResponse<T = any> {
  code: string
  msg?: string
  data?: T
}

export interface WorkflowListResponse {
  records: WorkflowItem[]
  totalRecords: number
}

export interface WorkflowTypeItem {
  label: string
  value: string
  address: string
}

/**
 * 获取审批流列表
 * @param params 查询参数
 * @returns 审批流列表
 */
export async function getApprovalWorkflowList(params: { pageIndex?: number; pageSize?: number }) {
  const res = await service({
    url: `/uipAdmin/workflow/listWorkFlow`,
    method: 'get',
    params,
  })
  if (!res.data.records) {
    res.data.records = []
  }
  return Promise.resolve(res)
}

// 新增审批工作流
export const addWorkflow = async (data: WorkflowItem): Promise<ApiResponse> => {
  return service({
    url: `/uipAdmin/workflow/save`,
    method: 'post',
    data,
  })
}

// 更新审批工作流
export const updateWorkflow = async (data: WorkflowItem): Promise<ApiResponse> => {
  return service({
    url: `/uipAdmin/workflow/update`,
    method: 'post',
    data,
  })
}

// 删除审批工作流
export const deleteWorkflow = async (id: string): Promise<ApiResponse> => {
  return service({
    url: `/uipAdmin/workflow`,
    method: 'delete',
    params: {
      id,
    },
  })
}

/**
 * 获取审批流类型列表
 */
export async function getApprovalWorkflowTypeList(): Promise<any> {
  const res = await service({
    url: `/uipAdmin/workflow/config`,
    method: 'get',
    params: {
      configType: 1,
    },
  })
  return {
    code: res.code,
    data: res.data.map((item: any) => ({
      label: item.configName,
      value: item.configKey,
      address: item.configValue,
    })),
    msg: res.msg,
  }
}

/**
 * 获取部门列表
 * @returns 部门列表
 */
export function getDeptList() {
  return service({
    url: `/api/301UC/deptManage/getDeptList`,
    method: 'get',
    customSystemCode: 'fsAuthorityCenter',
    headers: {
      tenantId: 'system',
      Buid: 'system_bu',
    },
  } as any)
}

/**
 * 查询审批流
 * @param id 审批流id
 */
export function queryWorkflowDetail(workFlowId: string) {
  return service({
    url: `/uipAdmin/workflow/details`,
    method: 'get',
    params: {
      workFlowId,
    },
  })
}

/**
 * 获取部门子部门列表
 * @param params 查询参数
 * @returns 部门子部门列表
 */
export function getDeptChildList(params: { deptId: string; tenantId: string }) {
  return service({
    url: `/api/301UC/deptManage/getChildDeptList`,
    method: 'get',
    params,
    customSystemCode: 'fsAuthorityCenter',
    headers: {
      tenantId: params.tenantId,
    },
  } as any)
}

/**
 * 获取部门用户列表
 * @param params.deptId 部门id
 * @param params.pageIndex 页码
 * @param params.pageSize 每页条数
 * @param params.queryType 查询类型, 1-本级, 2-本级和下级
 * @returns 部门用户列表
 */
export function getDeptUserList(params: {
  deptId: string
  pageIndex: number
  pageSize: number
  queryType: number
  tenantId: string
}) {
  return service({
    url: `/api/301UC/userManage/getUserList`,
    method: 'get',
    params,
    customSystemCode: 'fsAuthorityCenter',
    headers: {
      tenantId: params.tenantId,
    },
  } as any)
}
