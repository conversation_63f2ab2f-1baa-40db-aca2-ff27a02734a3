<template>
  <component :is="map[options?.compnentName]" v-bind="options?.comProps">
    <template v-for="(item, index) in options?.children" :key="index">
      <flexComponnent :options="item"></flexComponnent>
    </template>
  </component>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import flexComponnent from './flex-componnent.vue'
import { Echarts } from '@fs/fs-components'
import { Flex } from 'ant-design-vue'
import tableList from '../table-list.vue'

interface items {
  compnentName: string
  comProps?: any
  children?: any[]
}

interface flexComp {
  options: items
}

const map: any = {
  Echarts: Echarts,
  'a-flex': Flex,
  'table-list': tableList,
}

const prop = withDefaults(defineProps<flexComp>(), {})
</script>
<style scoped lang="less">
.flex-componnent {
}
</style>
