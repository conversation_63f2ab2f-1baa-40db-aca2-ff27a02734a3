<template>
  <a-space v-if="showBtns" class="date-selector">
    <a-button
      v-for="item in options"
      :class="activeKey === item.value && 'ant-btn-active'"
      :key="item.value"
      @click="handleClickOption(item)"
    >
      {{ item.label }}
    </a-button>

    <a-popover
      trigger="click"
      placement="bottom"
      v-model:open="showMore"
      :overlayStyle="{ padding: 0 }"
    >
      <a-button type="default" @click="handleClickMore">
        <MoreOutlined />
      </a-button>
      <template #content>
        <DateMoreMenu v-show="!showDatePicker" :onChange="handleClickOption" />
        <!-- prettier-ignore -->
        <CustomDatePicker
          v-show="showDatePicker"
          :onConfirm="handleConfirm"
        />
      </template>
    </a-popover>
  </a-space>
  <a-space class="date-selector" v-else>
    <a-popover
      v-if="dayjs.isDayjs(data) || Array.isArray(data)"
      v-model:open="showDatePicker2"
      title="Title"
      trigger="click"
      placement="bottom"
    >
      <a-tag closable color="#58a3e4" @close="clearDate" @click="handleClickTag">{{
        formatTime
      }}</a-tag>
      <template #content>
        <!-- prettier-ignore -->
        <CustomDatePicker
          :value="data"
          :onConfirm="handleConfirm"
        />
      </template>
    </a-popover>
    <a-tag v-else closable color="#58a3e4" @close="clearDate" @click="handleClickTag">{{
      formatTime
    }}</a-tag>
  </a-space>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { Dayjs } from 'dayjs'
import { MoreOutlined } from '@ant-design/icons-vue'
import { ConditionEnum, moreOptions, MorePeriodEnum, options, ResidentEnum } from './constant'
import DateMoreMenu from './date-more-menu.vue'
import CustomDatePicker from './custom-date-picker.vue'
import dayjs from 'dayjs'
import type { FilterItem } from './type'

type ValueTypes = MorePeriodEnum | ResidentEnum | Array<Dayjs> | Dayjs | string | null
const data = defineModel<ValueTypes>('value', { required: true })
const props = defineProps<{
  filterData: FilterItem
  onConditionChange?: (value?: ConditionEnum, filter?: FilterItem) => void
}>()
const showBtns = computed(() => {
  let flag = !!moreOptions.find((item) => item.value === data.value)
  if (flag) return false
  return !data.value || typeof data.value === 'string'
})
const showDatePicker = ref(false)
const showDatePicker2 = ref(false)
const showMore = ref(false)
const activeKey = ref('')
const formatTime = computed(() => {
  let res = ''
  if (Array.isArray(data.value)) {
    if (dayjs.isDayjs(data.value[0])) {
      const startTime = data.value[0].format('YYYY-MM-DD')
      const endTime = data.value[1].format('YYYY-MM-DD')
      res = `${startTime} ~ ${endTime}`
    } else {
      res = `${data.value[0]} ~ ${data.value[1]}`
    }
  } else if (dayjs.isDayjs(data.value)) {
    res = data.value.format('YYYY-MM-DD')
  } else {
    res = moreOptions.find((item) => item.value === data.value)?.label ?? '-'
  }
  return res
})

const handleClickOption = (option: (typeof options)[0]) => {
  console.log(option)
  if (option.value === MorePeriodEnum.specificDate) {
    showDatePicker.value = true
  } else {
    activeKey.value = option.value
    data.value = option.value
    showMore.value = false
  }
}
const hidePopover = () => {
  showDatePicker.value = false
  showDatePicker2.value = false
  showMore.value = false
}
const handleConfirm = (val?: Dayjs[] | Dayjs, condition?: ConditionEnum) => {
  hidePopover()
  if (val) {
    console.log('日期', props.filterData, condition)
    data.value = val
    props.onConditionChange?.(condition, props.filterData)
  }
}
const handleClickMore = () => {
  hidePopover()
}
const clearDate = () => {
  data.value = null
}
const handleClickTag = () => {
  showDatePicker2.value = true
}
</script>

<style scoped lang="less">
@ac-color: rgb(80, 158, 227);
.ant-btn-active {
  border-color: @ac-color;
  color: @ac-color;
}
.date-selector {
  height: 32px;
}
</style>
