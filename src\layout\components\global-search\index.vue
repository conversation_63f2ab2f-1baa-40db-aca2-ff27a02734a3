<template>
  <div class="global-search">
    <div class="search-wrapper">
      <!-- 搜索类型下拉框 -->
      <a-select
        v-model:value="searchType"
        @change="onSearchTypeSelectChange"
        class="search-type-select"
        default-value="all"
      >
        <a-select-option
          v-for="option in searchTypeOptions"
          :key="option.value"
          :value="option.value"
        >
          {{ option.label }}
        </a-select-option>
      </a-select>

      <div class="search-input-wrapper">
        <!-- 前置条件标签 -->
        <div class="search-prefix-tags" v-if="searchType === SearchType.ASSETDATA">
          <a-tag :closable="false" class="search-prefix-tag" @click="handleOpenAssetDataSelector">
            库表信息
          </a-tag>
        </div>
        <!-- 搜索输入框 -->
        <a-input
          v-model:value="searchKeyword"
          class="search-input"
          ref="searchInputRef"
          :style="inputStyle"
          :placeholder="getPlaceholder()"
          @pressEnter="handleSearch"
          @focus="handleFocus"
          @blur="handleBlur"
          @input="handleInput"
        />

        <!-- 搜索图标和快捷键提示 -->
        <div class="search-suffix">
          <i class="iconfont icon-sousuo search-icon" @click="handleSearch"></i>
          <div class="shortcut-hint">
            <kbd class="kbd-key">Ctrl</kbd>
            <kbd class="kbd-key">K</kbd>
          </div>
          <i
            class="iconfont icon-shanchu clear-icon"
            v-show="!!searchKeyword"
            @click="handleClear"
          ></i>
        </div>
      </div>
    </div>

    <!-- 搜索预览组件 -->
    <SearchPreview
      :visible="showPreview"
      :keyword="searchKeyword"
      :results="searchResults"
      :selected-index="selectedPreviewIndex"
      :search-type="searchType"
      :loading="isSearching"
      @select="handlePreviewSelect"
      @hover="handlePreviewHover"
      @close="handlePreviewClose"
    />

    <!-- 资产数据选择器 -->
    <a-modal v-model:open="showAssetDataSelector" title="选择资产数据" :width="1000" :footer="null">
      <AssetDataSelector
        @cancel="showAssetDataSelector = false"
        @confirm="handleAssetDataSelectorConfirm"
      />
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted, onBeforeUnmount } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import {
  SearchType,
  getSearchTypeOptions,
  type SearchTypeOption,
  type SearchResultItem,
  performPreviewSearch,
  handleResultClick,
} from './search'
import SearchPreview from './components/search-preview.vue'
import eventBus, { EventNames } from './event-bus'
import AssetDataSelector from './components/asset-data-selector.vue'

export interface SearchResult {
  type: SearchType
  keyword: string
  esSearchParams?: {
    databases?: string[]
    tables?: string[]
    schemas?: string[]
    queryWords?: string
  } | null
}

/** 资产数据搜索详情 */
export interface AssetSearchDetail {
  databaseId?: string
  databaseName: string
  tableId?: string
  tableName: string
  tables?: string[]
}

interface SearchPrefixTag {
  key: string
  label: string
  value: string
  closable: boolean
}

// 使用Vue Router
const router = useRouter()
const route = useRoute()

// 响应式数据
const searchType = ref<SearchType>(SearchType.DATABASE)
const searchKeyword = ref('')
const isFocused = ref(false)
const showPreview = ref(false)
const selectedPreviewIndex = ref(-1)
const searchResults = ref<SearchResultItem[]>([])
const isSearching = ref(false)
const searchDebounceTimer = ref<number | null>(null)
const assetSearchDetail = reactive<AssetSearchDetail>({
  databaseName: 'test_db',
  tableName: 'test_table',
})
const showAssetDataSelector = ref(false)
const searchInputRef = ref<HTMLInputElement>()

// es 搜索的参数
const esSearchParams = ref<{
  databases?: string[]
  tables?: string[]
  schemas?: string[]
} | null>(null)

// 计算属性
const searchTypeOptions = computed((): SearchTypeOption[] => {
  return getSearchTypeOptions()
})

const inputStyle = computed(() => {
  return {
    paddingLeft: searchType.value === SearchType.ASSETDATA ? '70px' : '4px',
  }
})

// 获取占位符文本
const getPlaceholder = (): string => {
  const typeMap: Record<string, string> = {
    [SearchType.DATABASE]: '搜索元数据库...',
    [SearchType.TABLE]: '搜索元数据表...',
    [SearchType.ASSET]: '搜索数据资产库...',
    [SearchType.ASSET_TABLE]: '搜索数据资产表...',
    [SearchType.METRIC]: '搜索指标...',
    [SearchType.API_LIST]: '搜索API...',
  }
  return typeMap[searchType.value] || '搜索...'
}

// 处理搜索
const handleSearch = (): void => {
  if (!searchKeyword.value.trim()) {
    return
  }

  const searchResult: SearchResult = {
    type: searchType.value,
    keyword: searchKeyword.value.trim(),
  }

  // 记录搜索历史
  addToSearchHistory(searchResult)

  // 隐藏预览
  showPreview.value = false

  const searchResultRoute = '/search-result'
  const query = {
    searchType: searchType.value,
    keyword: searchResult.keyword,
  }
  if (esSearchParams.value) {
    Object.assign(query, {
      esSearchParams: esSearchParams.value,
    })
  }
  if (route.path === searchResultRoute) {
    searchInputRef.value?.blur()
    const isQueryEqual = JSON.stringify(query) === JSON.stringify(route.query)
    if (!isQueryEqual) {
      router.replace({
        query,
      })
    }
  } else {
    router.push({
      path: searchResultRoute,
      query,
    })
  }
}

// 清空搜索
const handleClear = (): void => {
  searchKeyword.value = ''
  showPreview.value = false
  selectedPreviewIndex.value = -1
  searchResults.value = []
  isSearching.value = false

  // 清除防抖定时器
  if (searchDebounceTimer.value) {
    clearTimeout(searchDebounceTimer.value)
    searchDebounceTimer.value = null
  }
}

// 处理输入
const handleInput = (): void => {
  updatePreviewVisibility()
  selectedPreviewIndex.value = -1
}

// 处理焦点
const handleFocus = (): void => {
  isFocused.value = true
  updatePreviewVisibility()
}

// 处理失去焦点
const handleBlur = (): void => {
  isFocused.value = false
  // 延迟隐藏预览，给用户时间点击预览项
  // setTimeout(() => {
  //   if (!isFocused.value) {
  //     showPreview.value = false
  //     selectedPreviewIndex.value = -1
  //   }
  // }, 200)
}

// 更新预览显示状态
const updatePreviewVisibility = (): void => {
  if (searchType.value === SearchType.ASSETDATA) {
    return
  }
  showPreview.value = isFocused.value && searchKeyword.value.trim().length > 0
  if (!showPreview.value) {
    selectedPreviewIndex.value = -1
  }
}

// 防抖搜索函数
const debounceSearch = (keyword: string, searchType: SearchType): void => {
  // 清除之前的定时器
  if (searchDebounceTimer.value) {
    clearTimeout(searchDebounceTimer.value)
  }

  // 如果搜索关键词为空，清空结果
  if (!keyword.trim()) {
    searchResults.value = []
    isSearching.value = false
    return
  }

  // 设置新的防抖定时器，延迟300ms执行搜索
  searchDebounceTimer.value = window.setTimeout(() => {
    performSearchRequest(keyword.trim(), searchType)
  }, 300)
}

// 预览搜索请求
const performSearchRequest = async (keyword: string, searchType: SearchType): Promise<void> => {
  try {
    isSearching.value = true

    // 使用统一的预览搜索API
    const results = await performPreviewSearch(keyword, searchType)
    // 只有当前搜索词还匹配时才更新结果（避免快速输入时的结果错乱）
    if (keyword === searchKeyword.value.trim()) {
      searchResults.value = results
    }
  } catch (error) {
    console.error('预览搜索请求失败:', error)
    searchResults.value = []
  } finally {
    isSearching.value = false
  }
}

// 添加到搜索历史
const addToSearchHistory = (searchResult: SearchResult): void => {
  const history = JSON.parse(localStorage.getItem('global_search_history') || '[]')
  const newHistory = [
    searchResult,
    ...history.filter(
      (item: SearchResult) =>
        item.keyword !== searchResult.keyword || item.type !== searchResult.type,
    ),
  ].slice(0, 10) // 保留最近10条

  localStorage.setItem('global_search_history', JSON.stringify(newHistory))
}

// 处理全局快捷键
const handleGlobalShortcut = (event: KeyboardEvent): void => {
  // Ctrl + K 快捷键
  if (event.ctrlKey && event.key === 'k') {
    event.preventDefault()
    if (searchInputRef.value) {
      searchInputRef.value.focus()
    }
  }
}

// 处理预览项选择
const handlePreviewSelect = (item: SearchResultItem): void => {
  console.log('GlobalSearch: 接收到预览项选择事件', item)
  showPreview.value = false
  handleResultClick(item, router)
  eventBus.emit(EventNames.CLEAR_SEARCH_KEYWORD)
}

// 处理预览项悬停
const handlePreviewHover = (index: number): void => {
  selectedPreviewIndex.value = index
}

// 处理预览导航
const handlePreviewNavigate = (direction: 'up' | 'down'): void => {
  const totalItems = searchResults.value.length
  if (totalItems === 0) return

  if (direction === 'down') {
    selectedPreviewIndex.value =
      selectedPreviewIndex.value < totalItems - 1 ? selectedPreviewIndex.value + 1 : 0
  } else {
    selectedPreviewIndex.value =
      selectedPreviewIndex.value > 0 ? selectedPreviewIndex.value - 1 : totalItems - 1
  }
}

// 处理预览关闭
const handlePreviewClose = (): void => {
  showPreview.value = false
  selectedPreviewIndex.value = -1
  if (searchInputRef.value) {
    searchInputRef.value.blur()
  }
}

// 处理资产数据选择器确定
const handleAssetDataSelectorConfirm = (params: any): void => {
  showAssetDataSelector.value = false
  console.log('GlobalSearch: 资产数据选择器', params)
  esSearchParams.value = params
}

// 搜索类型下拉框 change 事件处理
const onSearchTypeSelectChange = (value: SearchType): void => {
  handleSearchTypeChange(value)
  if (value !== SearchType.ASSETDATA) {
    esSearchParams.value = null
  }
}

// 处理搜索类型变化
const handleSearchTypeChange = (value: SearchType): void => {
  searchType.value = value
}

// 打开资产选择器
const handleOpenAssetDataSelector = (): void => {
  showAssetDataSelector.value = true
}

// 监听搜索关键词变化
watch(searchKeyword, (newKeyword: string) => {
  updatePreviewVisibility()
  // 资产数据类型不需要重新搜索
  if (searchType.value === SearchType.ASSETDATA) {
    return
  }

  // 触发防抖搜索
  debounceSearch(newKeyword, searchType.value)
})

// 监听搜索类型变化
watch(searchType, (newType: SearchType) => {
  selectedPreviewIndex.value = -1
  // 如果有搜索关键词，重新搜索
  if (searchKeyword.value.trim()) {
    // 资产数据类型不需要重新搜索
    if (searchType.value === SearchType.ASSETDATA) {
      return
    }

    debounceSearch(searchKeyword.value, newType)
  }
})

// 生命周期钩子
onMounted(() => {
  document.addEventListener('keydown', handleGlobalShortcut)
  eventBus.on(EventNames.CLEAR_SEARCH_KEYWORD, handleClear)
})

onBeforeUnmount(() => {
  document.removeEventListener('keydown', handleGlobalShortcut)
  eventBus.off(EventNames.CLEAR_SEARCH_KEYWORD, handleClear)

  // 清理防抖定时器
  if (searchDebounceTimer.value) {
    clearTimeout(searchDebounceTimer.value)
    searchDebounceTimer.value = null
  }
})
</script>

<style lang="less" scoped>
.global-search {
  position: relative;
  flex: 1;

  .search-wrapper {
    display: flex;
    align-items: center;
    background: #fff;
  }
  .search-type-select {
    width: 110px;

    :deep(.ant-select-selector) {
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
      color: #8c8c8c !important;

      .ant-select-selector-item {
        font-size: 14px;
        color: #666;
      }
    }

    :deep(.ant-select-arrow) {
      color: #999;
    }
  }

  .search-input-wrapper {
    flex: 1;
    position: relative;
    display: flex;
    align-items: center;

    .search-prefix-tags {
      position: absolute;
      left: 4px;
      z-index: 10;
      display: flex;
      flex-wrap: wrap;
      gap: 4px;
      margin-right: 8px;

      .search-prefix-tag {
        margin: 0;
        font-size: 12px;
        cursor: pointer;

        &:hover {
          background: #e6f7ff;
          border-color: #1890ff;
        }
      }
    }

    .search-input {
      flex: 1;
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
      border-left: 0;
    }
    :deep(.ant-input) {
      padding-right: 120px;
      font-size: 14px;

      &::placeholder {
        color: #bfbfbf;
      }
    }

    .search-suffix {
      position: absolute;
      right: 8px;
      display: flex;
      align-items: center;
      gap: 8px;

      .search-icon,
      .clear-icon {
        font-size: 16px;
        color: #999;
        cursor: pointer;
        transition: color 0.3s;

        &:hover {
          color: #1890ff;
        }
      }

      .shortcut-hint {
        display: flex;
        align-items: center;
        gap: 2px;

        .kbd-key {
          display: inline-block;
          padding: 2px 6px;
          font-size: 11px;
          line-height: 1.2;
          color: #666;
          background-color: #fafafa;
          border: 1px solid #d9d9d9;
          border-radius: 3px;
          font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
          font-weight: normal;
          min-width: 20px;
          text-align: center;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .global-search {
    .search-wrapper {
      max-width: 200px;
    }

    .search-type-select {
      width: 60px;
    }

    .shortcut-hint {
      display: none !important;
    }
  }
}
</style>
