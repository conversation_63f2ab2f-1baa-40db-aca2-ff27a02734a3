// 存储每个 key 的调用次数
const callCounts = {}

export function renderItemByCallCount(key, dataList = []) {
  // 初始化该 key 的调用次数为 0
  if (!callCounts[key]) {
    callCounts[key] = 0
  }

  // 查找对应的 data 对象
  const data = dataList.find((item) => item.name === key)

  if (data) {
    // 获取当前的调用次数
    const count = callCounts[key]

    // 获取对应的 value 数组
    const valueArray = data.value

    // 计算下标，使用取模运算来实现循环
    const index = count % valueArray.length

    // 输出当前调用的信息
    console.log(
      `打印 key: ${key}, 执行第 ${count + 1} 次, 打印内容: ${JSON.stringify(valueArray[index])}`,
    )

    // 更新调用次数
    callCounts[key]++

    // 返回对应的 value
    return valueArray[index].value
  }
}
