<template>
  <!-- 标签弹框 -->
  <a-modal
    v-model:open="showDialog"
    :title="`${dialogType === 'edit' ? '编辑' : '新增'}标签`"
    @ok="okHandle"
    @cancel="canceHandle"
    width="600px"
  >
    <a-form
      :model="formData"
      name="basic"
      :label-col="{ span: 4 }"
      :wrapper-col="{ span: 16 }"
      autocomplete="off"
      ref="formRef"
    >
      <a-form-item
        label="标签名称"
        name="labelName"
        :rules="[{ required: true, message: '请输入标签名称' }]"
      >
        <a-input
          show-count
          :maxlength="30"
          v-model:value="formData.labelName"
          placeholder="请输入标签名称"
        />
      </a-form-item>
      <a-form-item label="描述" name="description">
        <a-textarea
          :auto-size="{ minRows: 3, maxRows: 6 }"
          v-model:value="formData.description"
          placeholder="请输入描述"
          show-count
          :maxlength="120"
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script lang="ts" setup>
import {
  addTagItem,
  editTagItem,
  type AssetResponse,
  type ITagForm,
  type ITag,
} from '@/api/services/data-asset/tag-management'
import { message } from 'ant-design-vue'

const props = defineProps({
  currentItem: {
    type: Object,
    required: false,
    defualt: null,
  },
  visibleDialog: {
    type: Boolean,
    default: false,
  },
  dialogType: {
    type: String,
    default: 'add', // add edit
  },
  categoryId: {
    type: Number,
    default: 0,
  },
})
const emit = defineEmits(['okHandle', 'canceHandle'])

const initForm = () => {
  return {
    labelName: '',
    description: '',
  }
}
const formRef = ref()
const showDialog = ref(false)
const formData = ref<ITagForm>(initForm())

watch(
  () => props.visibleDialog,
  () => {
    showDialog.value = props.visibleDialog
    if (props.currentItem) {
      formData.value = {
        labelName: props.currentItem.labelName,
        description: props.currentItem.description,
      }
    }
  },
  { immediate: true },
)

// 分类保存
const okHandle = async () => {
  formRef.value.validate().then(async () => {
    const params: ITag = {
      ...formData.value,
      categoryId: props.categoryId,
    }
    let res: AssetResponse<string>
    if (props.dialogType === 'add') {
      res = await addTagItem(params)
    } else {
      params.labelId = props.currentItem?.labelId ?? -1
      res = await editTagItem(params)
    }
    if (res.code === '000000') {
      message.success('操作成功')
      canceHandle()
      emit('okHandle')
    } else {
      message.error(res.msg)
    }
  })
}

// 取消操作
const canceHandle = () => {
  showDialog.value = false
  formData.value = initForm()
  emit('canceHandle')
}
</script>
