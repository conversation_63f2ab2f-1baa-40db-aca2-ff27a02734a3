<template>
  <searchColumns v-model="list" :type="type"></searchColumns>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import searchColumns from '@/components/code-generation/components/search-columns.vue'

interface columnsApi {
  data: any[]
}

const props: columnsApi = withDefaults(defineProps<columnsApi>(), {})
const emit = defineEmits(['change'])
const type = ref<any>('')

const determineTypeData = (item: { type: any; required: any }) => {
  // 如果不包含 type 和 required，typeData 为空
  if (!item.type && !item.required) {
    return 'default'
  }

  // 如果包含 type 但不包含 required，typeData 为 'search'
  if (item.type && !item.required) {
    return 'search'
  }

  // 如果包含 type 和 required，typeData 为 'form'
  if (item.type && item.required) {
    return 'form'
  }

  // 默认返回空字符串
  return ''
}

watch(
  () => props.data,
  (newData) => {
    if (newData && newData.length > 0) {
      type.value = determineTypeData(newData[0])
      console.log(
        '%c 🍔 type.value: ',
        'font-size:12px;background-color: #E41A6A;color:#fff;',
        type.value,
      )
    }
  },
  { immediate: true },
)

const list = computed(() => props.data)

watch(
  () => list.value,
  (newData) => {
    emit('change', newData)
  },
  { deep: true },
)
</script>
<style scoped lang="less"></style>
