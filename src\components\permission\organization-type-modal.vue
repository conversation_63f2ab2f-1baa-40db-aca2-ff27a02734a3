<template>
  <a-modal
    v-model:open="addOpen"
    @ok="modalHandleOk"
    :title="modalType === 'edit' ? '编辑' : '新增' + '组织'"
    @cancel="cancel"
    width="800px"
    :confirm-loading="confirmLoading"
  >
    <a-form
      ref="formRef"
      :model="form"
      :rules="rules"
      :label-col="labelCol"
      :wrapper-col="wrapperCol"
    >
      <a-form-item label="组织编号" name="deptTypeId" v-if="modalType === 'edit'">
        <a-input v-model:value="form.deptTypeId" :disabled="true" placeholder="请输入组织编号" />
      </a-form-item>
      <a-form-item label="组织名称" name="deptTypeName">
        <a-input v-model:value="form.deptTypeName" placeholder="请输入组织名称" />
      </a-form-item>
      <a-form-item label="等级" name="levelId">
        <a-input type="number" v-model:value="form.levelId" placeholder="请输入等级" />
      </a-form-item>
      <a-form-item label="是否为部门" name="isDept">
        <a-radio-group v-model:value="form.isDept" placeholder="请选择是否为部门">
          <a-radio :value="1">是</a-radio>
          <a-radio :value="0">否</a-radio>
        </a-radio-group>
      </a-form-item>
      <a-form-item label="备注" name="remark">
        <a-textarea v-model:value="form.remark" placeholder="请输入备注" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>
<script lang="ts" setup>
import { defineEmits, defineProps, defineExpose, ref } from 'vue'
import { message } from 'ant-design-vue'
import type { Rule } from 'ant-design-vue/es/form'
import { updateDeptType, insertDeptType } from '@/api/services/permission'
interface FormState {
  deptTypeId: string
  deptTypeName: string
  levelId: number
  isDept: string
  remark: string
}

const emits = defineEmits(['modalHandleOk', 'modalCancel', 'confirmLoadingHandle'])
const formRef = ref<HTMLFormElement | null>(null)
const labelCol = { span: 5 }
const wrapperCol = { span: 13 }
const modalType = ref<string>('')
const confirmLoading = ref<boolean>(false)
const addOpen = ref<boolean>(false)
const form = ref<FormState>({
  deptTypeId: '',
  deptTypeName: '',
  levelId: 1,
  isDept: '',
  remark: '',
})

defineProps({
  disabled: Boolean,
})

const rules: Record<string, Rule[]> = {
  deptTypeName: [{ required: true, message: '请输入组织名称', trigger: 'blur' }],
  isDept: [{ required: true, message: '请选择是否为部门', trigger: 'blur' }],
  levelId: [{ required: true, message: '请输入等级', trigger: 'blur' }],
}
function modalHandleOk() {
  formRef.value &&
    formRef.value
      .validate()
      .then(() => {
        confirmLoading.value = true
        if (modalType.value === 'edit') {
          updateDeptType({ ...form.value })
            .then(() => {
              emits('modalHandleOk', form.value)
              message.success('更新成功!')
              addOpen.value = false
            })
            .finally(() => {
              confirmLoading.value = false
            })
        } else {
          const { deptTypeName, isDept, levelId, remark } = form.value
          const params = {
            deptTypeName,
            isDept: Number(isDept),
            levelId,
            remark,
          }
          insertDeptType(params)
            .then(() => {
              emits('modalHandleOk', form.value)
              message.success('新增成功!')
              addOpen.value = false
            })
            .finally(() => {
              confirmLoading.value = false
            })
        }
      })
      .catch((error: any) => {
        console.log(error)
      })
}

function cancel() {
  emits('modalCancel')
  formRef.value?.resetFields()
}

function show(type: string, data?: Record<string, any>) {
  formRef.value?.resetFields()
  modalType.value = type
  addOpen.value = true
  if (type === 'edit') {
    editForm(data)
  }
}

function editForm(data: any) {
  if (data) {
    const { remark, deptTypeName, isDept, levelId, deptTypeId } = JSON.parse(JSON.stringify(data))
    const formData = { remark, deptTypeName, isDept, deptTypeId, levelId }
    form.value = formData
  }
}

defineExpose({ show })
</script>
