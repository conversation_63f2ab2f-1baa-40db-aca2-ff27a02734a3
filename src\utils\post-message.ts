import { logoutFn } from '@/utils/user'

/**
 * 向父容器 iframe 发送消息
 * @param type 消息类型
 * @param payload 发送的数据内容
 */
export function postMessageToParent(type: string, payload?: any) {
  if (window.parent) {
    window.parent.postMessage(
      {
        type,
        payload,
      },
      '*',
    )
  }
}

export function postMsgPushRoute(path: string, query?: Record<string, any>) {
  postMessageToParent('pushRoute', {
    path,
    query,
  })
}

export function postMsgLogout() {
  postMessageToParent('logout', {})
}

/** 来自父窗口的 postMessage 事件 */
window.addEventListener('message', (event) => {
  const { type } = event.data
  if (type === 'logout') {
    // 处理退出逻辑
    localStorage.clear()
    document.cookie.split(';').forEach((cookie) => {
      const name = cookie.split('=')[0].trim()
      document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/`
    })
    document.cookie = ''
    logoutFn()
  } else if (type === 'clearData') {
    localStorage.removeItem('metadata')
    localStorage.removeItem('pageData')
  }
})
