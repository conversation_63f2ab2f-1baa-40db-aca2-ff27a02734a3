<template>
  <!-- <a-tabs v-model:activeKey="activeKey" tab-position="left">
      <a-tab-pane v-for="item in tabList" :key="item.value" :tab="item.label">{{
        item.label
      }}</a-tab-pane>
    </a-tabs> -->
  <div class="content-steps">
    <a-steps
      direction="vertical"
      :current="current"
      :items="tabList"
      style="height: 100%"
    ></a-steps>
  </div>
  <div class="content-right">
    <a-tabs v-model:activeKey="activeKey" type="card">
      <a-tab-pane key="0" tab="条件选项" v-if="current < 2">
        <generationApi
          :project="projectData"
          v-if="current === 0"
          v-model="pageData"
          @interfaceInfos="interfaceInfosHanlde"
          ref="generationApiRef"
          type="table"
        ></generationApi>
        <columns
          v-if="current === 1"
          :dataSource="pageData.dataSource"
          :search="pageData.search"
          :form="pageData.form"
        ></columns>
      </a-tab-pane>
      <a-tab-pane key="1" tab="代码" v-if="current === 2">
        <v-ace-editor
          v-model:value="jsonInput"
          lang="javascript"
          theme="chrome"
          style="width: auto; height: 500px"
          :readonly="true"
        />
      </a-tab-pane>
    </a-tabs>
  </div>
  <div class="content-footer">
    <a-button @click="curtHandle" v-show="current > 0" style="margin-right: 16px">上一步</a-button>
    <a-button style="margin-right: 16px" type="primary" @click="nextHandle" v-show="current < 2"
      >下一步</a-button
    >
    <template v-if="!props.fetchCode">
      <a-button @click="saveDraft" style="margin-right: 16px">保存草稿</a-button>
      <a-button v-show="current === 2" @click="generateCode" type="primary">生成代码</a-button>
    </template>

    <a-button v-show="current === 2" @click="submit" type="primary">确定</a-button>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick, watch } from 'vue'
import type { generation, item } from '../generation'
import { getData, formatting, getdraft } from '../code-generation'
import { VAceEditor } from 'vue3-ace-editor'
import 'ace-builds/src-noconflict/mode-javascript' // Load the language definition file used below
import 'ace-builds/src-noconflict/theme-chrome' // Load the theme definition file used below
import generationApi from '../components/generation-api.vue'
import columns from '../components/columns.vue'
import { getTableAllCode } from '../tool'
import type { apiDataType } from '@/components/page/generation'

const activeKey = ref('0')
const current = ref(0)
const jsonInput = ref('')
const generationApiRef = ref()
const tabList = ref([
  {
    title: '配置引入接口',
    value: 0,
  },
  {
    title: '配置字段',
    value: 1,
  },
  {
    title: '生成代码',
    value: 2,
  },
])
const projectData = ref<any>(undefined)
const pageData = ref<generation>({
  request: [],
  search: [],
  form: [],
  interfaceInfos: [],
  dataSource: [],
})
const interfaceInfos = ref<item[]>([])

interface tableProps {
  routerPath: string
  fetchCode: boolean
  apiData: apiDataType
}

const props: tableProps = withDefaults(defineProps<tableProps>(), {})

const emit = defineEmits(['saveDraft', 'generateCode'])

watch(
  props.apiData,
  (val) => {
    if (val) {
      pageData.value.project = val.project
      pageData.value.module = val.module
      // 匹配项目和模块数据
      setTimeout(() => {
        generationApiRef.value.handleChange(pageData.value.project, true)
        generationApiRef.value.modulesChange(pageData.value.module, true)
      }, 100)
      nextTick(() => {})
    }
  },
  {
    immediate: true,
    deep: true,
  },
)

onMounted(async () => {
  pageData.value.request = [
    {
      name: '查询',
      api: undefined,
    },
    {
      name: '新增',
      api: undefined,
    },
    {
      name: '编辑',
      api: undefined,
    },
    {
      name: '删除',
      api: undefined,
    },
  ]

  const res = await getData()
  const { data } = res
  if (data?.code === '000000') {
    projectData.value = data.data
  }
  const val = await getdraft({ name: props.routerPath })
  const pageType = val?.data?.data?.pageType ?? ''
  // 草稿箱为表格类型时 读取草稿数据
  if (pageType === '' || pageType !== 'table') return
  if (val?.data?.data && JSON.stringify(val?.data?.data) !== '{}') {
    pageData.value = val.data.data
    nextTick(() => {
      generationApiRef.value.handleChange(pageData.value.project, true)
      generationApiRef.value.modulesChange(pageData.value.module, true)
    })
  }
  current.value = 0
})

const saveDraft = async (state = true) => {
  emit('saveDraft', { ...pageData.value, pageType: 'table' }, state)
}

const generateCode = async () => {
  emit('generateCode', { content: jsonInput.value })
  saveDraft(false)
}

const submit = () => {
  emit('generateCode', { content: jsonInput.value })
}

const nextHandle = async () => {
  if (current.value === 0) {
    await generationApiRef.value.onSubmit()
  }
  // 优化逻辑流，减少重复
  const isFinalStep = current.value === 1
  activeKey.value = isFinalStep ? '1' : '0'

  if (isFinalStep) {
    const str = await getTableAllCode(pageData.value, interfaceInfos.value)
    if (str) {
      const { data } = await formatting(str)
      jsonInput.value = data.data
    }
  }

  current.value++
}

const interfaceInfosHanlde = (data: item[]) => {
  interfaceInfos.value = data
}

const curtHandle = () => {
  if (current.value > 0) current.value--
  activeKey.value = '0'
  // 重新计算函数下拉值
  if (current.value === 0) {
    nextTick(() => {
      generationApiRef.value.handleChange(pageData.value.project, true)
      generationApiRef.value.modulesChange(pageData.value.module, true)
    })
  }
}
</script>

<style scoped lang="less">
.content {
  display: flex;
  &-steps {
    height: 400px;
    width: 200px;
  }
  &-right {
    position: relative;
    width: 100%;
    max-height: 600px;
    overflow: auto;
    padding-bottom: 20px;
  }
  &-footer {
    width: 100%;
    position: absolute;
    bottom: 20px;
    text-align: center;
    background: white;
    left: 0;
  }
}
</style>
