<template>
  <div class="content">
    <a-steps
      :current="current"
      :items="[
        {
          title: '第一步',
        },
        {
          title: '第二步',
        },
        {
          title: '第三步',
        },
      ]"
      style="width: 800px; margin: 0 auto; margin-bottom: 50px"
    ></a-steps>
    <div class="form-box">
      <Form
        :items="items"
        ref="FormRefs"
        v-model="formData"
        :extral="{}"
        @submit="submitHandle"
        :layout="layout"
        v-show="current === 0"
      />

      <Form
        :items="items2"
        ref="FormRefs2"
        v-model="formData"
        :extral="{}"
        @submit="submitHandle2"
        :layout="layout"
        v-show="current === 1"
      />

      <a-result v-show="current === 2" status="success" title="创建成功" sub-title="账号创建成功">
      </a-result>
    </div>

    <div class="button-group">
      <a-button class="submit-button" @click="current--" v-show="current > 0">上一步</a-button>
      <a-button type="primary" @click="submit" v-show="current < 2">下一步</a-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, nextTick, onMounted } from 'vue'
import { Form } from '@fs/fs-components'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { debounce } from '@/utils'

const router = useRouter()
const formData = ref<any>({})
const current = ref(0)
const FormRefs = ref()
const FormRefs2 = ref()

const items = ref<any[]>([
  {
    name: 'name',
    label: '姓名',
    required: true,
    span: 24,
    rules: [{ required: true, message: '请输入姓名', trigger: 'change' }],
  },
  {
    name: 'age',
    label: '年龄',
    required: true,
    span: 24,
    rules: [
      { required: true, message: '请输入年龄' },
      { pattern: /^[0-9a-zA-Z_]{1,}$/, message: '只允许包含数字、字母、下划线' },
    ],
  },
  {
    name: 'timer',
    label: '生日',
    span: 24,
    type: 'date-picker',
    compProps: {},
  },
  {
    name: 'desc',
    label: '描述',
    span: 24,
    type: 'textarea',
    compProps: {},
  },
])

const items2 = ref<any[]>([
  {
    name: 'account',
    label: '账号',
    required: true,
    span: 24,
    rules: [{ required: true, message: '请输入账号', trigger: 'change' }],
  },
  {
    name: 'password',
    label: '密码',
    required: true,
    span: 24,
    rules: [{ required: true, message: '请输入密码', trigger: 'change' }],
  },
  {
    name: 'newPassword',
    label: '确认密码',
    required: true,
    span: 24,
    rules: [
      {
        required: true,
        validator: () => {
          if (!formData.value?.newPassword) {
            return Promise.reject('请再次输入密码')
          }
          if (formData.value?.password !== formData.value?.newPassword) {
            return Promise.reject('两次输入密码不一致')
          }
          return Promise.resolve()
        },
        trigger: 'change',
      },
    ],
  },
])

const layout = ref({
  layout: 'horizontal',
  labelCol: { span: 5 },
  wrapperCol: { span: 15 },
})

function submit() {
  if (current.value === 0) {
    FormRefs.value.submit()
  } else if (current.value === 1) {
    FormRefs2.value.submit()
  }
}

function submitHandle(data: any) {
  current.value++
}

function submitHandle2(data: any) {
  current.value++
}

function reset() {
  formData.value = {}
  FormRefs.value.reset()
}
</script>

<style scoped lang="less">
/* Styles for 404 page */
.content {
  position: relative;
  width: 100%;
  padding-top: 30px;
  .button-group {
    text-align: center;
    .submit-button {
      margin-right: 20px;
    }
  }
  .form-box {
    width: 800px;
    margin: 0 auto;
  }
}
</style>
