<template>
  <a-modal destroyOnClose v-model:open="open" title="生成代码" width="80%" :footer="null">
    <div class="content">
      <template v-if="codeType === 'table'">
        <table-steps
          :routerPath="routerPath"
          @saveDraft="saveDraft"
          @generateCode="generateCode"
          :fetchCode="fetchCode"
          :apiData="apiValue"
        ></table-steps>
      </template>
      <template v-else-if="codeType === 'form'">
        <form-steps
          :routerPath="routerPath"
          @saveDraft="saveDraft"
          @generateCode="generateCode"
        ></form-steps>
      </template>
      <template v-else-if="codeType === 'echarts'">
        <echarts-flex
          :routerPath="routerPath"
          @saveDraft="saveDraft"
          @generateCode="generateCode"
        ></echarts-flex>
      </template>
      <template v-else-if="codeType === 'grid'">
        <grid :routerPath="routerPath" @saveDraft="saveDraft" @generateCode="generateCode"></grid>
      </template>
      <template v-else-if="codeType === 'custom'">
        <custom
          :routerPath="routerPath"
          :customType="customType"
          @saveDraft="saveDraft"
          @generateCode="generateCode"
        ></custom>
      </template>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import type { generationType } from '@/components/page/generation'
import { savedraft, generationCode } from './code-generation'
import { useRoute } from 'vue-router'
import { message } from 'ant-design-vue/es/components'
import tableSteps from './table-steps/index.vue'
import formSteps from './form-steps/index.vue'
import echartsFlex from './echarts-flex/index.vue'
import grid from './grid/index.vue'
import custom from './custom/custom.vue'

const route = useRoute()
const routerPath = ref('')
const open = ref(false)
const codeType = ref('')
const customType = ref('table')
const fetchCode = ref(false)
const fetchCallback = ref<any>()
const apiValue = ref<any>()
const show = async (value: generationType) => {
  // customType 自定义模板-模板类型
  const {
    type,
    state,
    customType: custom = '',
    apiData = undefined,
    fetchCode: fetch = false,
    fetchCallback: callback,
  } = value
  customType.value = custom
  codeType.value = type
  if (route.path) routerPath.value = route.path
  open.value = state
  // fetchCode 自定义模板-是否获取模板代码
  fetchCode.value = fetch
  if (callback) {
    fetchCallback.value = callback
  }
  // apiData 回显选择项目和模块 一般用于API快速生成快速选中
  if (apiData) {
    apiValue.value = apiData
  }
}

const saveDraft = async (data: Record<string, any>, state = true) => {
  await savedraft({ name: routerPath.value, content: { ...data } })
  if (state) message.success('保存草稿成功')
}

const generateCode = async (obj: any) => {
  // 只获取代码字符串
  if (fetchCode.value) {
    fetchCallback.value(obj.content)
    open.value = false
    return
  }
  if (obj.modal) {
    // 弹窗模式同时生成弹窗组件
    await generationCode({
      pagePath: `${routerPath.value}/component/modal-component.vue`,
      ...obj,
    })
  } else {
    const { data } = await generationCode({
      pagePath: getRouterPath(),
      ...obj,
    })

    if (data.code === '000000') {
      open.value = false
      message.success('生成代码成功')
    }
  }
}

// 生成页面路径
const getRouterPath = () => {
  return `${routerPath.value}/index.vue`
}

// 导出 show 方法
defineExpose({
  show,
})
</script>

<style scoped lang="less">
.content {
  display: flex;
}
</style>
