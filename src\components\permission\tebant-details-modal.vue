<template>
  <a-modal
    v-model:open="modalOpen"
    @ok="modalHandleOk"
    title="详情"
    @cancel="cancel"
    width="800px"
    :confirm-loading="ConfirmLoading"
  >
    <a-form
      ref="formRef"
      :model="form"
      :label-col="labelCol"
      :wrapper-col="wrapperCol"
      class="form-row"
    >
      <a-form-item label="租户名称" name="tenantName">
        <div>{{ form.tenantName }}</div>
      </a-form-item>
      <a-form-item label="租户代码" name="tenantCode">
        <div>{{ form.tenantCode }}</div>
      </a-form-item>
      <a-form-item label="管理员账号" name="manageUserAccount">
        <div>{{ form.manageUserAccount }}</div>
      </a-form-item>
      <a-form-item label="管理员角色" name="manageRoleId" v-if="modalType != 'add'">
        <div>{{ form.manageRoleId }}</div>
      </a-form-item>
      <a-form-item label="是否启用" name="isUse" v-if="modalType != 'add'">
        <div>{{ form.isUseName }}</div>
      </a-form-item>
      <a-form-item label="备注">
        <div>{{ form.remark }}</div>
      </a-form-item>
      <a-form-item label="创建时间">
        <div>{{ form.createTime }}</div>
      </a-form-item>
      <a-form-item label="更新时间">
        <div>{{ form.updateTime }}</div>
      </a-form-item>
      <a-form-item label="创建人">
        <div>{{ form.createUserName }}</div>
      </a-form-item>
    </a-form>
  </a-modal>
</template>
<script lang="ts" setup>
import { defineExpose, ref } from 'vue'
import type { FormInstance } from 'ant-design-vue/es/form'
import { viewTenantDetail } from '@/api/services/permission'
interface FormState {
  tenantCode: string
  tenantName: string
  manageUserAccount: string
  isUseName: string
  manageRoleId: string
  updateTime: string
  remark: string
  tenantId?: string
  createUserName?: string
  createTime?: string
}

const formRef = ref<FormInstance>()
const labelCol = { span: 5 }
const wrapperCol = { span: 13 }
const modalType = ref<string>('')
const ConfirmLoading = ref<boolean>(false)
const modalOpen = ref<boolean>(false)
const form = ref<FormState>({
  tenantCode: '',
  tenantName: '',
  manageUserAccount: '',
  manageRoleId: '',
  isUseName: '',
  updateTime: '',
  remark: '',
  createUserName: '',
  createTime: '',
})

function cancel() {
  modalOpen.value = false
}

function modalHandleOk() {
  modalOpen.value = false
}

function show(type: string, data: Record<string, any>) {
  modalOpen.value = true
  viewTenantDetail({ tenantId: data?.tenantId }).then((res) => {
    form.value = res?.data
  })
}

defineExpose({ show })
</script>
<style scoped lang="less">
.form-row {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
}
/deep/.ant-form-item-label {
  text-align: start;
}
/deep/.ant-form-item-control {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
