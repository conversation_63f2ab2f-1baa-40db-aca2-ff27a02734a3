import { h } from 'vue'
import { Tag } from 'ant-design-vue'

export const COLUMS = [
  {
    title: '指标名称',
    dataIndex: 'indexName',
    key: 'indexName',
    width: 100,
  },
  {
    title: '是否已发布',
    dataIndex: 'isRelease',
    key: 'isRelease',
    width: 50,
    customRender: ({ text }) => {
      return h(Tag, { color: text === 1 ? 'green' : 'red' }, () => (text === 1 ? '是' : '否'))
    },
  },
  {
    title: '创建人名称',
    dataIndex: 'createUsername',
    key: 'createUsername',
    width: 80,
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: 80,
  },
  {
    title: '更新人',
    dataIndex: 'updateUsername',
    key: 'updateUsername',
    width: 80,
  },
  {
    title: '更新时间',
    dataIndex: 'updateTime',
    key: 'updateTime',
    width: 180,
  },
  {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
    fixed: 'right',
    width: 180,
  },
]
