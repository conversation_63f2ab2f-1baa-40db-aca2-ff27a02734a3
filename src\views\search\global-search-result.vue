<template>
  <div class="global-search-result">
    <splitpanes>
      <pane size="20">
        <div class="search-result-left">
          <div class="search-result-left-header">搜索分类</div>

          <div class="search-categories">
            <div
              v-for="category in searchCategories"
              :key="category.type"
              class="category-item"
              :class="{ active: activeTab === category.type }"
              @click="handleCategoryClick(category.type)"
            >
              <div class="category-icon">
                <i class="iconfont" :class="getIconClass(category.type)"></i>
              </div>
              <span class="category-name">{{ category.name }}</span>
              <span class="category-count" v-if="category.loading">...</span>
              <span class="category-count" v-else>{{ category.count }}</span>
            </div>
          </div>
        </div>
      </pane>
      <pane>
        <div class="search-result-right">
          <!-- 搜索结果头部 -->
          <div class="search-result-header" v-if="searchInfo">
            <h2>搜索结果</h2>
            <div v-if="loading">
              <p class="search-info">
                正在搜索 "<span class="search-keyword">{{ searchInfo.keyword }}</span
                >" 的结果...
              </p>
            </div>
            <div v-else>
              <p class="search-info">
                搜索 "<span class="search-keyword">{{ searchInfo.keyword }}</span
                >" 在
                <span class="search-type">{{ getSearchTypeText(activeTab) }}</span>
                中找到
                <span class="result-count">{{ getResultsCountByType(activeTab) }}</span>
                个结果
              </p>
            </div>
          </div>

          <!-- 搜索结果列表 -->
          <div class="search-results-container" ref="searchResultsContainer">
            <a-spin :spinning="loading">
              <div class="search-results-list">
                <result-card
                  v-for="(item, index) in currentResults"
                  :key="item.id + index"
                  :item="item"
                  :keyword="searchInfo ? searchInfo.keyword : ''"
                  :db-icon="getItemDbIcon(item)"
                  @click="handleClickResultCard"
                />

                <div v-if="getCurrentTotal() === 0 && !loading" style="padding-top: 200px">
                  <a-empty
                    description="系统繁忙,请稍候再试"
                    v-if="categoryResults[activeTab].error"
                  />
                  <a-empty description="没有找到相关结果" v-else />
                </div>
                <!-- 分页 -->
                <div class="search-pagination" v-if="getCurrentTotal() > 0">
                  <a-pagination
                    :current="currentPage"
                    :total="getCurrentTotal()"
                    :page-size="getPageSize()"
                    @change="handlePageChange"
                  />
                </div>
              </div>
            </a-spin>
          </div>
        </div>
      </pane>
    </splitpanes>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted, nextTick } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { message } from 'ant-design-vue'
import {
  SearchType,
  SearchTypeLabels,
  type SearchResultItem,
  type SearchParams,
  performSearch,
  handleResultClick,
  type CategorySearchResult,
} from '@/layout/components/global-search/search'
import { Pane, Splitpanes } from 'splitpanes'
import 'splitpanes/dist/splitpanes.css'
import ResultCard from './result-card.vue'

interface EsSearchParams {
  databases?: string[]
  tables?: string[]
  schemas?: string[]
}
interface SearchInfo {
  searchType: SearchType
  keyword: string
  esSearchParams?: EsSearchParams
}

interface Pagination {
  current: number
}

const CONST_PAGE_SIZE = 10

const PAGE_SIZE: Record<SearchType, number> = {
  [SearchType.DATABASE]: CONST_PAGE_SIZE, // 数据库
  [SearchType.TABLE]: CONST_PAGE_SIZE, // 表
  [SearchType.ASSET]: CONST_PAGE_SIZE, // 资产
  [SearchType.ASSET_TABLE]: CONST_PAGE_SIZE, // 资产表
  [SearchType.METRIC]: CONST_PAGE_SIZE, // 指标
  [SearchType.API_LIST]: CONST_PAGE_SIZE, // API列表
  [SearchType.ALL]: CONST_PAGE_SIZE, // 全部类型
  [SearchType.ASSETDATA]: CONST_PAGE_SIZE, // 资产数据 es搜索
}

const searchResultDefault: Record<SearchType, CategorySearchResult> = {
  [SearchType.DATABASE]: { total: 0, list: [], loading: true },
  [SearchType.TABLE]: { total: 0, list: [], loading: true },
  [SearchType.ASSET]: { total: 0, list: [], loading: true },
  [SearchType.ASSET_TABLE]: { total: 0, list: [], loading: true },
  [SearchType.METRIC]: { total: 0, list: [], loading: true },
  [SearchType.API_LIST]: { total: 0, list: [], loading: true },
  [SearchType.ALL]: { total: 0, list: [], loading: true },
  [SearchType.ASSETDATA]: { total: 0, list: [], loading: true },
}

// 使用Vue Router
const router = useRouter()
const route = useRoute()

// 响应式数据
const loading = ref(false)
const activeTab = ref<SearchType>(SearchType.DATABASE)
const searchInfo = ref<SearchInfo | null>(null)
const currentPage = ref(1)
const searchResultsContainer = ref<HTMLElement>()

// 搜索结果数据 - 使用响应式对象存储每个类型的结果和总数
const categoryResults = reactive<{ [key in SearchType]: CategorySearchResult }>(searchResultDefault)

// 分页信息 - 使用响应式对象存储每个类型的分页信息
const pagination = reactive<Record<SearchType, Pagination>>({
  [SearchType.DATABASE]: { current: 1 },
  [SearchType.TABLE]: { current: 1 },
  [SearchType.ASSET]: { current: 1 },
  [SearchType.ASSET_TABLE]: { current: 1 },
  [SearchType.METRIC]: { current: 1 },
  [SearchType.API_LIST]: { current: 1 },
  [SearchType.ALL]: { current: 1 },
  [SearchType.ASSETDATA]: { current: 1 },
})

// 根据类型获取结果数量
const getResultsCountByType = (type: SearchType): number => {
  const categoryResult = categoryResults[type]
  return categoryResult ? categoryResult.total : 0
}

// 搜索分类数据
const searchCategories = computed(() => {
  const arr = Object.values(SearchType) as SearchType[]
  const categories = arr
    .filter((type) => type !== SearchType.ALL)
    .map((type) => {
      const { loading = false } = categoryResults[type] || {}

      return {
        type: type,
        name: SearchTypeLabels[type],
        count: getResultsCountByType(type),
        loading,
      }
    })
  return categories
})

// 获取分页大小
const getPageSize = (): number => {
  return PAGE_SIZE[activeTab.value] || CONST_PAGE_SIZE
}

// 获取当前激活标签的总数（用于分页）
const getCurrentTotal = (): number => {
  const categoryResult = categoryResults[activeTab.value]
  return categoryResult ? categoryResult.total : 0
}

// 是否静态分页
const isStaticPagination = computed((): boolean => {
  return [SearchType.TABLE, SearchType.DATABASE].includes(activeTab.value)
})

// 获取当前激活标签下的所有结果
const getAllResults = (): SearchResultItem[] => {
  const categoryResult = categoryResults[activeTab.value]
  return categoryResult ? categoryResult.list : []
}

const currentResults = computed((): SearchResultItem[] => {
  const result = getAllResults()
  const pageSize = PAGE_SIZE[activeTab.value]
  if (isStaticPagination.value) {
    const start = (currentPage.value - 1) * pageSize
    const end = start + pageSize
    return result.slice(start, end)
  }
  return result
})

// 获取搜索类型文本
const getSearchTypeText = (type: SearchType): string => {
  return SearchTypeLabels[type] || type
}

// 获取图标组件
const getIconClass = (type: SearchType): string => {
  const iconMap: Record<string, string> = {
    [SearchType.DATABASE]: 'icon-shujuku',
    [SearchType.TABLE]: 'icon-shujubiaoge',
    [SearchType.ASSET]: 'icon-zichanku',
    [SearchType.ASSET_TABLE]: 'icon-zichanbiaodan',
    [SearchType.METRIC]: 'icon-zhibiao',
    [SearchType.API_LIST]: 'icon-apiliebiao',
  }
  return iconMap[type] || 'icon-shujuku'
}

// 高亮关键字
const highlightKeyword = (text: string): string => {
  if (!searchInfo.value || !searchInfo.value.keyword || !text) return text

  const keyword = searchInfo.value.keyword
  const regex = new RegExp(`(${keyword})`, 'gi')
  return text.replace(regex, '<span class="highlight">$1</span>')
}

// 格式化日期
const formatDate = (date?: string): string => {
  if (!date) return '未知'
  return new Date(date).toLocaleDateString()
}

// 处理分类点击
const handleCategoryClick = (type: SearchType): void => {
  console.log('categoryResults[type]', categoryResults[type])
  loading.value = categoryResults[type].loading
  activeTab.value = type
  currentPage.value = pagination[type].current || 1 // 切换分类时重置页码
}

const setPagination = (page: number): void => {
  // 更新当前页码
  pagination[activeTab.value].current = page
}

// 处理分页变化
const handlePageChange = async (page: number) => {
  currentPage.value = page // 直接更新当前页码

  setPagination(page)

  // 非静态页面重新请求数据
  if (!isStaticPagination.value) {
    await performSearchAction(
      {
        searchType: activeTab.value,
        keyword: searchInfo.value ? searchInfo.value.keyword : '',
        esSearchParams: searchInfo.value ? searchInfo.value.esSearchParams : undefined,
      },
      activeTab.value,
    )
  }

  // 滚动到顶部
  if (searchResultsContainer.value) {
    searchResultsContainer.value.scrollTo({
      top: 0,
      behavior: 'smooth',
    })
  }
}

// 处理结果点击
const handleClickResultCard = (item: SearchResultItem): void => {
  handleResultClick(item, router)
}

// 执行搜索
const performSearchAction = async (
  searchInfoParam: SearchInfo,
  searchType: SearchType = SearchType.ALL,
): Promise<void> => {
  loading.value = true
  searchInfo.value = searchInfoParam
  activeTab.value =
    searchInfoParam.searchType === SearchType.ALL ? SearchType.DATABASE : searchInfoParam.searchType

  try {
    const searchParams: SearchParams = {
      keyword: searchInfoParam.keyword,
      searchType,
      page: currentPage.value,
      pageSize: PAGE_SIZE[activeTab.value],
    }
    if (searchInfoParam.esSearchParams) {
      searchParams.esParams = searchInfoParam.esSearchParams
    }
    console.log('🚀 ~ performSearchAction ~ searchInfoParam:', searchInfoParam)

    const useMock = !!sessionStorage.getItem(`useMock-${searchInfoParam.searchType}`)

    const response = await performSearch(searchParams, {
      useMock,
      // 异步回调渲染
      onCategoryResult: (type, result) => {
        categoryResults[type] = result
        if (type === activeTab.value) {
          loading.value = false
        }
      },
    })

    // 单条搜索
    if (searchType !== SearchType.ALL && !('categoryResults' in response)) {
      categoryResults[searchInfoParam.searchType] = {
        total: response.total,
        list: response.list,
        loading: false,
      }
    }
  } catch (error) {
    message.error('搜索失败，请重试')
    console.error('搜索错误:', error)
    // categoryResults = searchResultDefault
  } finally {
    loading.value = false
  }
}

// 从路由加载搜索参数
const loadSearchFromRoute = (): void => {
  const { keyword, searchType, esSearchParams } = route.query
  if (searchType && keyword && typeof searchType === 'string' && typeof keyword === 'string') {
    // 验证搜索类型是否有效
    const validSearchType = Object.values(SearchType).includes(searchType as SearchType)
      ? (searchType as SearchType)
      : SearchType.ALL

    const searchInfo1: SearchInfo = {
      searchType: validSearchType,
      keyword: keyword as string,
    }
    if (esSearchParams) {
      searchInfo1.esSearchParams = esSearchParams as EsSearchParams
    }

    Object.keys(categoryResults).forEach((key) => {
      categoryResults[key as SearchType] = {
        total: 0,
        list: [],
        loading: true,
      }
    })

    currentPage.value = 1
    performSearchAction(searchInfo1)
  }
}

// 监听路由变化
watch(
  () => route.query,
  () => {
    loadSearchFromRoute()
  },
  { immediate: true },
)

// 组件挂载时获取搜索参数
onMounted(() => {
  loadSearchFromRoute()
})

// 获取搜索结果项的数据库图标
const getItemDbIcon = (item: SearchResultItem): string | undefined => {
  // 只有数据库类型的搜索结果才返回图标
  if (item.type === SearchType.DATABASE && item.dbType) {
    return item.dbType
  }
  return undefined
}
</script>

<style lang="less" scoped>
.global-search-result {
  width: 100%;
  height: 100%;

  .search-result-left {
    padding: 16px 0;
    background: #fff;
    border-right: 1px solid #f0f0f0;
    height: 100%;
    overflow-y: auto;

    .search-result-left-header {
      font-size: 16px;
      color: #262626;
      font-weight: 500;
      padding: 0 16px 16px 16px;
      border-bottom: 1px solid #f0f0f0;
      margin-bottom: 8px;
    }

    .search-categories {
      .category-item {
        display: flex;
        align-items: center;
        padding: 8px 16px;
        cursor: pointer;
        transition: all 0.2s;
        color: #595959;
        position: relative;

        &:hover {
          background: #f5f5f5;
          color: #262626;
        }

        &.active {
          background: #e6f7ff;
          color: #1890ff;

          &::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            width: 4px;
            height: 100%;
            background: #1890ff;
          }

          .category-icon svg {
            color: #1890ff;
          }

          .category-count {
            background: #1890ff;
            color: #fff;
          }
        }

        .category-icon {
          width: 20px;
          margin-right: 8px;
          display: flex;
          justify-content: center;
          align-items: center;

          svg {
            width: 16px;
            height: 16px;
            color: #8c8c8c;
            transition: color 0.2s;
          }
        }

        .category-name {
          flex: 1;
          font-size: 14px;
          line-height: 1.4;
        }

        .category-count {
          background: #f5f5f5;
          color: #8c8c8c;
          padding: 2px 8px;
          border-radius: 10px;
          font-size: 12px;
          line-height: 1.4;
          min-width: 20px;
          text-align: center;
          font-weight: 500;
          transition: all 0.2s;
        }
      }
    }
  }

  .search-result-right {
    height: 100%;
    display: flex;
    flex-direction: column;
    background: #fff;
    overflow: hidden;

    .search-result-header {
      flex-shrink: 0;
      border-bottom: 1px solid #f0f0f0;
      padding: 24px 24px 16px 24px;

      h2 {
        margin: 0 0 8px 0;
        font-size: 20px;
        font-weight: 500;
        color: #262626;
      }

      .search-info {
        margin: 0;
        color: #595959;
        font-size: 14px;
        line-height: 1.6;

        .search-keyword {
          color: #1890ff;
          font-weight: 500;
        }

        .search-type {
          color: #1890ff;
          font-weight: 500;
        }

        .result-count {
          color: #f5222d;
          font-weight: 600;
        }
      }
    }

    .search-results-container {
      flex: 1;
      padding: 16px;
      overflow-y: auto;

      .search-results-list {
        width: 100%;
        min-height: 100px;
        margin-bottom: 24px;
      }

      .empty-results {
        padding: 64px 0;
        text-align: center;
      }

      .search-pagination {
        display: flex;
        justify-content: center;
        padding: 24px 0;
        border-top: 1px solid #f0f0f0;
        margin-top: auto;
      }
    }
  }
}

/* splitpanes容器样式 */
/deep/ .splitpanes {
  height: 100%;
}

/deep/ .splitpanes__pane {
  height: 100%;
}

/* 分栏分割线样式 */
/deep/ .splitpanes__splitter {
  min-width: 4px; // 分割线最小宽度
  background: #fcfcfc;
}
</style>
