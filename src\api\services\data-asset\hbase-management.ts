import service from '@/api'
import { SODATAFLINK } from '@/api/base'
import type { AssetResponse } from './subscription-settings'
import type { AxiosResponse } from 'axios'

export interface ISchema {
  dbDesc: string
  dbOther: string
  dbPassword: string
  dbType: number
  dbUrl: string
  dbUsername: string
  id: number
}

export interface ISchemaTable {
  dbTypMap: string
  key: string
  scopedSlots: { icon: string }
  title: string
}

export interface IDbParams {
  id?: string // 数据源id
  namespace: string
  tableName?: string
}

export interface ITableItem {
  id: number
  namespace: string
  tableName: string
  colFamily: string[]
}

export interface ITableColumnItem {
  id: number
  namespace: string
  tableName: string
  colFamily: string
  type: number //1.新增 2.删除
}

export interface ITableItemParams {
  tableName: string
  colFamily: string[]
}

export interface Parameter {
  [prop: string]: any
}
/**
 *
 * 获取数据源列表
 * @param params
 */
export function getSchemaInfo(params: Parameter): Promise<AssetResponse<ISchema[]>> {
  return service({
    url: `${SODATAFLINK}/sqlEditor/PageListScameInfo`,
    method: 'post',
    data: params,
  })
}

/**
 * 获取元数据库名
 * @param params
 */
export function getDbNameList(params: Parameter): Promise<AssetResponse<string[]>> {
  return service({
    url: `${SODATAFLINK}/hbaseSetting/getNamespaceList`,
    method: 'get',
    params: params,
  })
}

// 新增库
export function addDbItem(data: IDbParams): Promise<AssetResponse<string>> {
  return service({
    method: 'post',
    url: `${SODATAFLINK}/hbaseSetting/addNamespace`,
    data: data,
  })
}

// 删除库
export function deleteDbItem(data: IDbParams): Promise<AssetResponse<string>> {
  return service({
    method: 'post',
    url: `${SODATAFLINK}/hbaseSetting/deleteNamespace`,
    data: data,
  })
}

export interface IPageListByName extends IDbParams {
  pageSize?: number // 页大小
  pageIndex?: number // 起始页
}

// 获取表列表
export function getHbaseTableList(params: IPageListByName): Promise<AssetResponse<string[]>> {
  return service({
    method: 'GET',
    url: `${SODATAFLINK}/hbaseSetting/getTableList`,
    params,
  })
}

// 新增表
export function addHbaseTable(data: ITableItem): Promise<AssetResponse<string>> {
  return service({
    method: 'post',
    url: `${SODATAFLINK}/hbaseSetting/createTable`,
    data: data,
  })
}

// 新增/删除列族
export function updateHbaseTableColumn(data: ITableItem): Promise<AssetResponse<string>> {
  return service({
    method: 'post',
    url: `${SODATAFLINK}/hbaseSetting/addOrDeleteColFamily`,
    data: data,
  })
}

// 删除表
export function deleteHbaseTable(data: IDbParams): Promise<AssetResponse<string>> {
  return service({
    method: 'post',
    url: `${SODATAFLINK}/hbaseSetting/deleteTable`,
    data: data,
  })
}

//获取表的列族
export function getHbaseTableColumnList(params: IDbParams): Promise<AssetResponse<string[]>> {
  return service({
    method: 'GET',
    url: `${SODATAFLINK}/hbaseSetting/getColumnFamilyList`,
    params,
  })
}

/**
 * 页面内容加载
 * @param params
 */
export function metadatainfoList(params: any): Promise<AssetResponse<any>> {
  return service({
    method: 'get',
    url: `${SODATAFLINK}/metadata/metadatainfoList`,
    params,
  })
}

/**
 * 加载数据源列表
 * @param params
 */
export function getDataSourceList(params: Parameter) {
  return service({
    method: 'get',
    url: `${SODATAFLINK}/new2dataplatform/InfoSchema/PageListScameInfo`,
    params,
  })
}

/**
 * 更新表注释
 * @param params
 */
export function tableinfoUpdate(params: Parameter) {
  return service({
    method: 'post',
    url: `${SODATAFLINK}/AISQL/tableinfoUpdate`,
    data: params,
  })
}

/**
 * 更新字段注释
 * @param params
 */
export function columnsinfoUpdate(params: Parameter) {
  return service({
    method: 'post',
    url: `${SODATAFLINK}/AISQL/columnsinfoUpdate`,
    data: params,
  })
}

/**
 * 获取Hbase数据
 * @param params
 */

export interface IScanItemParams {
  id: number // 数据库信息编号
  namespace: string // 数据库名
  tableName: string // 表名
  pageSize: number // 页大小
  pageIndex: number // 起始页
  colFamily?: string[] // 列族（字段名）
  maxLine?: null // 读取行数，null为所有
}

export function getScanDataApi(data: IScanItemParams): Promise<AxiosResponse<any[]>> {
  return service({
    method: 'GET',
    url: `${SODATAFLINK}/hbaseSetting/scanData`,
    params: data,
  })
}
