<template>
  <a-modal
    v-model:open="open"
    @ok="modalHandleOk"
    @cancel="handleCancel"
    title="已分配系统"
    width="800px"
    :confirm-loading="confirmLoading"
    class="assignedMenuModal"
  >
    <a-tabs v-model:activeKey="activeKey">
      <a-tab-pane key="1" tab="查看菜单">
        <Table
          :columns="columns"
          :getData="getRoleSystemList"
          ref="tableRef"
          :pagination="{}"
          :searchFormState="formState"
        ></Table>
      </a-tab-pane>
      <a-tab-pane key="2" tab="授权菜单" force-render>
        <Table
          :columns="columns"
          :getData="getRoleSystemList"
          ref="authorizationTableRef"
          :pagination="{}"
          :searchFormState="authorizationFormState"
        ></Table>
      </a-tab-pane>
    </a-tabs>
  </a-modal>
  <a-modal v-model:open="viewOpen" :title="title">
    <template #footer> </template>
    <tree-node :treeData="treeData" :fNames="fNames" :isMenus="false" :checkable="true"></tree-node>
  </a-modal>
</template>
<script lang="ts" setup>
import { defineProps, defineExpose, ref, watch, onBeforeMount, h } from 'vue'
import { getRoleSystemList, getRoleMenuList } from '@/api/services/permission'
import { Table } from '@fs/fs-components'
import { assignedMenuDTO } from '@/utils/column'
import { Button } from 'ant-design-vue'

const formRef = ref<HTMLFormElement | null>(null)
const confirmLoading = ref<boolean>(false)
const open = ref<boolean>(false)
const viewOpen = ref<boolean>(false)
const activeKey = ref('1')
const title = ref('')
let columns = ref<any[]>([])
const tableRef = ref<InstanceType<typeof Table> | null>(null)
const authorizationTableRef = ref<InstanceType<typeof Table> | null>(null)
const treeData = ref<any>([])
const props = defineProps({
  roleId: String,
})
const fNames = {
  children: 'datas',
  title: 'menuName',
  key: 'menuId',
}
const formState = ref<any>({
  roleId: undefined,
  queryType: 1,
})

const authorizationFormState = ref<any>({
  roleId: undefined,
  queryType: 2,
})

watch(
  () => props.roleId,
  (value) => {
    formState.value.roleId = value
    authorizationFormState.value.roleId = value
    value && tableRef.value?.getTableData()
    value && authorizationTableRef.value?.getTableData()
  },
  { immediate: true },
)

onBeforeMount(() => {
  initColumns()
})

function getTreeLists(record: Record<string, any>) {
  title.value = activeKey.value === '1' ? '分配可查看菜单' : '分配可授权菜单'
  const params = {
    roleId: record?.roleId,
    systemId: record?.systemId,
    queryType: activeKey.value === '1' ? 1 : 2,
  }
  getRoleMenuList(params).then((result: any) => {
    if (result.code === '000000') {
      treeData.value = result.data
      viewOpen.value = true
    }
  })
  confirmLoading.value = false
}
function show() {
  formRef.value?.resetFields()
  activeKey.value = '1'
  open.value = true
}
const emits = defineEmits(['modalAssignedOk', 'modalAssignedCancel'])
function modalHandleOk() {
  confirmLoading.value = true
  open.value = false
  emits('modalAssignedOk')
  confirmLoading.value = false
}

function handleCancel() {
  emits('modalAssignedCancel')
  confirmLoading.value = false
}

async function initColumns() {
  columns.value = assignedMenuDTO.map((item) => {
    return {
      ...item,
      dataIndex: item.key,
    }
  })
  columns.value.push({
    key: 'action',
    title: '操作',
    customRender(data: Record<string, any>) {
      return [
        h(
          Button,
          {
            type: 'link',
            onClick() {
              operationHandle(data?.record)
              confirmLoading.value = false
            },
          },
          {
            default: () => '查看',
          },
        ),
      ]
    },
  })
}

function operationHandle(record: Record<string, any>) {
  getTreeLists(record)
}

defineExpose({ show })
</script>
<style lang="less">
.assignedMenuModal {
  .filter-box {
    display: none;
  }
}
</style>
