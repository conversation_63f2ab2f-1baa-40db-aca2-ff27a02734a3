import service from '@/api'
import { SODATAFLINK } from '@/api/base'
import type { Parameter } from '@/api/services/data-asset/hbase-management'

export function reqGetDataSourceList(params: any): Promise<any> {
  return service({
    method: 'get',
    url: `/api/sodataFlink/new2dataplatform/InfoSchema/PageListScameInfo`,
    params,
  })
}

export function getDataSourceTemplate(params: Parameter) {
  return service({
    url: `/api/sodataFlink/new2dataplatform/InfoSchema/dataSourceTemplate`,
    method: 'get',
    params,
  })
}

export function addDataSource(params: Parameter) {
  return service({
    url: `/api/sodataFlink/new2dataplatform/InfoSchema/schemaInfoInsert`,
    method: 'post',
    data: params,
  })
}

export function deleteDataSource(params: Parameter) {
  return service({
    url: `/api/sodataFlink/new2dataplatform/InfoSchema/schemaInfoDelete/${params.id}`,
    method: 'delete',
    data: params,
  })
}

export function editDataSource(params: Parameter) {
  return service({
    url: `/api/sodataFlink/new2dataplatform/InfoSchema/schemaInfoUpdate/${params.id}`,
    method: 'put',
    data: params,
  })
}

export function getDataSourceList(params: Parameter) {
  return service({
    url: `/api/sodataFlink/new2dataplatform/InfoSchema/PageListScameInfo`,
    params,
  })
}

export function checkServer(params: Parameter) {
  return service({
    url: `/api/sodataFlink/config/DbSchema/testDBConnection`,
    method: 'post',
    data: params,
  })
}
