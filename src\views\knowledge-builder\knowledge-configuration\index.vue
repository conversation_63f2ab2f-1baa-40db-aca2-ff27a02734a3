<template>
  <a-spin :spinning="loading">
    <div style="background-color: #fff">
      <div class="title-layout">
        <div class="title-layout-left">
          <ArrowLeftOutlined style="margin-right: 10px" @click="$router.go(-1)" />
          <h1>知识库配置</h1>
        </div>
        <a-button class="delete-btn" @click="visible = true">删除项目</a-button>
      </div>
      <div
        style="
          margin-top: 20px;
          width: 100%;
          align-items: center;
          justify-content: center;
          display: flex;
        "
      >
        <a-form :model="formState" ref="formRef" layout="vertical">
          <a-form-item label="知识库中文名称" style="width: 500px" name="name" required>
            <a-input v-model:value="formState.name" placeholder="input placeholder" />
          </a-form-item>
          <a-form-item label="知识库英文名称" style="width: 500px" name="namespace" required>
            <a-input v-model:value="formState.namespace" disabled />
          </a-form-item>
          <a-form-item label="知识库描述" style="width: 500px" name="namespace">
            <a-textarea
              v-model:value="formState.description"
              :auto-size="{ minRows: 4 }"
              placeholder="请输入知识库描述，在知识抽取和知识问答时作为prompt;例:这是一个法学知识库，主要描述法律相关的知识"
            />
          </a-form-item>
          <a-form-item label="图储存配置" style="width: 500px" name="graph_store">
            <a-radio-group v-model:value="formState.graph_store" name="radioGroup">
              <a-radio value="default">默认配置</a-radio>
              <a-radio value="custom">自定义配置</a-radio>
            </a-radio-group>
          </a-form-item>
          <a-form-item label="database" style="width: 500px" name="database" required>
            <a-input v-model:value="formState.database" disabled />
          </a-form-item>
          <a-form-item label="password" style="width: 500px" name="password" required>
            <a-input
              v-model:value="formState.password"
              :disabled="formState.graph_store === 'default'"
            />
          </a-form-item>
          <a-form-item label="uri" style="width: 500px" name="uri" required>
            <a-input
              v-model:value="formState.uri"
              :disabled="formState.graph_store === 'default'"
            />
          </a-form-item>
          <a-form-item label="user" style="width: 500px" name="user" required>
            <a-input
              v-model:value="formState.user"
              :disabled="formState.graph_store === 'default'"
            />
          </a-form-item>
          <a-form-item label="向量配置" style="width: 500px" name="vectorizer">
            <a-radio-group v-model:value="formState.vectorizer" name="radioGroup">
              <a-radio value="default">默认配置</a-radio>
              <a-radio value="custom">自定义配置</a-radio>
            </a-radio-group>
          </a-form-item>
          <a-form-item label="type" style="width: 500px" name="type" required>
            <a-input v-model:value="formState.type" disabled />
          </a-form-item>
          <a-form-item label="model" style="width: 500px" name="model" required>
            <a-input
              v-model:value="formState.model"
              :disabled="formState.vectorizer === 'default'"
            />
          </a-form-item>
          <a-form-item label="base_url" style="width: 500px" name="base_url" required>
            <a-input
              v-model:value="formState.base_url"
              :disabled="formState.vectorizer === 'default'"
            />
          </a-form-item>
          <a-form-item label="api_key" style="width: 500px" name="api_key" required>
            <a-input
              v-model:value="formState.api_key"
              :disabled="formState.vectorizer === 'default'"
            />
          </a-form-item>
          <a-form-item label="提示词中英文配置" style="width: 500px" name="prompt">
            <a-radio-group v-model:value="formState.prompt" name="radioGroup">
              <a-radio value="default">默认配置</a-radio>
              <a-radio value="custom">自定义配置</a-radio>
            </a-radio-group>
          </a-form-item>

          <a-form-item label="biz_scene" style="width: 500px" name="biz_scene" required>
            <a-input
              v-model:value="formState.biz_scene"
              :disabled="formState.prompt === 'default'"
            />
          </a-form-item>
          <a-form-item label="language" style="width: 500px" name="language" required>
            <a-input
              v-model:value="formState.language"
              :disabled="formState.prompt === 'default'"
            />
          </a-form-item>
        </a-form>
      </div>
      <div style="display: flex; justify-content: center; margin-bottom: 20px">
        <a-button type="primary" style="margin-top: 20px; border-radius: 6px" @click="handleSave"
          >保存</a-button
        >
      </div>
    </div>
  </a-spin>
  <a-modal
    v-model:visible="visible"
    title="删除项目"
    width="300px"
    @ok="handleDelete"
    @cancel="handleCancel"
  >
    <p>删除后项目中全部知识将一并清空</p>
  </a-modal>
</template>
<script setup lang="ts">
import { ref, onBeforeMount, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import type { KnowledgeBaseConfig } from '@/api/type'
import { ArrowLeftOutlined } from '@ant-design/icons-vue'
import {
  //   reqGetKnowledgeConfig,
  updateKnowledgeConfig,
  deleteKnowledgeConfig,
} from '@/api/services/knowledge'
const route = useRoute()
const router = useRouter()
const formRef = ref({})
const loading = ref(false)
const visible = ref(false)
const formState = ref({
  name: '',
  namespace: 'Test2',
  description: '',
  vectorizer: 'default',
  database: 'Test2',
  graph_store: 'default',
  prompt: 'default',
})
const emits = defineEmits(['modalAddOk'])
const props = defineProps<{ data: KnowledgeBaseConfig }>()
const formData = ref({})
onBeforeMount(async () => {
  // route.params.projectId && (await getData())
  console.log('================')
  //   formData.value = props.data
  //   setFormState()
  //   getData()
})
watch(
  () => props.data,
  (val) => {
    formData.value = val
    setFormState()
  },
  {
    immediate: true,
  },
)
// async function getData() {
//   const data = {
//     projectId: route.params.projectId,
//   }
//   await reqGetKnowledgeConfig(data).then((res) => {
//     console.log(res)

//     formData.value = res
//     setFormState()
//   })
// }
function setFormState() {
  const config = JSON.parse(formData.value.config) as KnowledgeBaseConfig
  console.log(JSON.parse(formData.value.config) as KnowledgeBaseConfig)
  formState.value = {
    name: formData.value.name,
    namespace: formData.value.namespace,
    description: formData.value.description,
    graph_store: config.graph_store.source_type,
    database: config.graph_store.database,
    password: config.graph_store.password,
    uri: config.graph_store.uri,
    user: config.graph_store.user,
    vectorizer: config.vectorizer.source_type,
    type: config.vectorizer.type,
    model: config.vectorizer.model,
    base_url: config.vectorizer.base_url,
    api_key: config.vectorizer.api_key,
    prompt: config.prompt.source_type,
    biz_scene: config.prompt.biz_scene,
    language: config.prompt.language,
  }
}

async function handleSave() {
  loading.value = true
  const config = JSON.parse(formData.value.config) as KnowledgeBaseConfig
  config.graph_store.database = formState.value.database
  config.graph_store.password = formState.value.password
  config.graph_store.uri = formState.value.uri
  config.graph_store.source_type = formState.value.graph_store
  config.vectorizer.source_type = formState.value.vectorizer
  config.prompt.source_type = formState.value.prompt
  config.graph_store.user = formState.value.user
  config.vectorizer.type = formState.value.type
  config.vectorizer.model = formState.value.model
  config.vectorizer.base_url = formState.value.base_url
  config.vectorizer.api_key = formState.value.api_key
  config.prompt.biz_scene = formState.value.biz_scene
  config.prompt.language = formState.value.language
  const data = {
    id: route.params.projectId,
    name: formState.value.name,
    namespace: formState.value.namespace,
    description: formState.value.description,
    config: JSON.stringify(config),
  }
  await updateKnowledgeConfig({ id: route.params.projectId }, data)
  loading.value = false
  message.success('保存成功')
  emits('modalAddOk')
}
function handleCancel() {
  visible.value = false
}
async function handleDelete() {
  handleCancel()
  loading.value = true
  const data = {
    projectId: route.params.projectId,
  }
  await deleteKnowledgeConfig(data)
  loading.value = false
  message.success('删除成功')
  router.back()
}
</script>
<style scoped>
.title-layout {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}
.title-layout-left {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.delete-btn {
  border-radius: 6px;
  background-color: #ff0000;
  color: #fff;
}
/* 方式 2：直接修改所有输入框 */
::v-deep .ant-input {
  border-radius: 4px !important;
}
</style>
