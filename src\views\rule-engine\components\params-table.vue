<template>
  <div class="params-table">
    <div class="add-button">
      <a-button type="primary" @click="handleAdd" size="small">添加参数</a-button>
    </div>
    <a-table :columns="columns" :data-source="dataSource" :pagination="false">
      <template #bodyCell="{ column, record, index }">
        <template v-if="column.key === 'name'">
          <a-input v-model:value="record.name" placeholder="请输入参数说明" />
        </template>
        <template v-if="column.key === 'valueType'">
          <a-select v-model:value="record.valueType" style="width: 100%">
            <a-select-option value="BOOLEAN">布尔</a-select-option>
            <a-select-option value="STRING">字符串</a-select-option>
            <a-select-option value="NUMBER">数值</a-select-option>
            <a-select-option value="DATE">日期</a-select-option>
          </a-select>
        </template>
        <template v-if="column.key === 'keyName'">
          <a-input v-model:value="record.keyName" placeholder="请输入参数名称" />
        </template>
        <template v-if="column.key === 'action'">
          <a-space>
            <a-popconfirm
              title="是否确定删除当前参数?"
              ok-text="是"
              cancel-text="否"
              @confirm="handleDelete(index)"
            >
              <a-button type="link" danger>删除</a-button>
            </a-popconfirm>
          </a-space>
        </template>
      </template>
    </a-table>
  </div>
</template>

<script lang="ts" setup>
interface ParamItem {
  name: string
  valueType: string
  key: string
}

const columns = [
  {
    title: '参数说明',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '参数类型',
    dataIndex: 'valueType',
    key: 'valueType',
  },
  {
    title: '参数名称',
    dataIndex: 'keyName',
    key: 'keyName',
  },
  {
    title: '操作',
    key: 'action',
  },
]

const dataSource = defineModel<ParamItem[]>('modelValue', { default: () => [] })

const handleAdd = () => {
  dataSource.value.push({
    name: '',
    valueType: 'STRING',
    key: '',
  })
}

const handleDelete = (index: number) => {
  dataSource.value.splice(index, 1)
}
</script>

<style scoped>
.params-table {
  width: 100%;
}
.add-button {
  margin-top: 16px;
  text-align: right;
}
</style>
