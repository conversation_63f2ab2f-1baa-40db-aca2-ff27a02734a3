import { getApiRequest } from '../tools/file-api.js'
import {
  createApiFile,
  updateFile,
  writeApiFolderToDisk,
} from '../base-business/virtual-file-system.js'
import process from 'node:process'
import { getCurrentTime, parseUrl } from '../tools/index.js'
import { extractAfterSlash, getModuleName } from '../tools/index.js'
import { API_CONFIG } from '../config/index.js'

const args = process.argv.slice(2)
const argumentValue = args[0]
const moduleName = args[1]
const funcsName = args[2]
const funcsList = funcsName ? funcsName.split(' ') : []
const isGenerate = argumentValue === 'generate' // 是否为命令行启动
/**
 * 异步函数，用于写入文件的API。
 * @async
 * @function writeFilesApi
 * @returns {Promise<void>} - 返回一个解析为undefined的Promise
 * @throws {Error} - 如果写入文件时发生错误，则抛出错误
 */
export async function writeFilesApi(url, params, state = true) {
  try {
    const { ipWithPort, uid } = parseUrl(API_CONFIG.url)
    const newUrl = argumentValue === 'generate' ? ipWithPort : url
    const newParams = argumentValue === 'generate' ? { uid: uid } : params
    const swaggerUrl = newUrl + '/flow/swagger/share'
    let data = await getApiRequest(swaggerUrl, newParams)
    if (!data) throw new Error('获取swagger文档失败 接口数据为空')
    let modules = data?.modules
    const projectPath = extractAfterSlash(data?.projectInfo?.projectPath)
    // 过滤对应模块
    if (moduleName) {
      modules = modules.filter((item) => item.moduleName === moduleName)
    }
    // 过滤模块中对应函数
    if (funcsName) {
      modules[0].interfaceInfos = modules[0].interfaceInfos.filter((item) =>
        funcsList.includes(item.interfaceName),
      )
    }
    const promisesArray = []
    // 为true需要写入模块信息 false只需写入swaager配置
    if (state || isGenerate) {
      modules.map(async (item) => {
        let moduleNameNew = getModuleName(item, modules)
        const handle = moduleName ? updateFile : createApiFile
        promisesArray.push(await handle(`api`, moduleNameNew, JSON.stringify(item), projectPath))
      })
    }
    // 写入swagger配置
    promisesArray.push(
      await createApiFile(
        `api`,
        'system-config-detail',
        JSON.stringify({
          url: `${newUrl}/?redirect=/login#/swaggerHome?uid=${newParams.uid}&formShare=0`,
          time: getCurrentTime(),
        }),
        projectPath,
      ),
    )

    await Promise.all(promisesArray)

    if (isGenerate) {
      writeApiFolderToDisk()
    }
  } catch (err) {
    console.error('Error writing files:', err)
    throw err
  }
}

// 启动 generate:api命令 自执行
if (argumentValue === 'generate') {
  writeFilesApi()
}
