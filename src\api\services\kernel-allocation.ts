import service from '@/api'
import type { ApiResponse } from '../common'

// 分配运行内核

/** 内核信息 */
export interface KernelInfo {
  allowFlag: boolean
  autoId: number
  displayName: string
  name: string
  [property: string]: any
}

/**
 * 获取内核列表
 * @param params 参数
 * @returns 内核列表
 */
export async function getKernelList(params: {
  roleId: string
}): Promise<ApiResponse<KernelInfo[]>> {
  return service({
    method: 'get',
    url: '/api/jupyter/kernel/role',
    params,
  })
}

/**
 * 更新内核列表
 * @param 所有内核数据
 */
export async function updateKernelList(data: { roleId: string; roleKernels: KernelInfo[] }) {
  return service({
    method: 'put',
    url: '/api/jupyter/kernel/updateByRole',
    data: data.roleKernels,
    params: {
      roleId: data.roleId,
    },
  })
}
