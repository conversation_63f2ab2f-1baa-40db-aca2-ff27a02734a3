<template>
  <a-modal
    v-model:open="show"
    title="添加节点"
    @ok="handleOk"
    @cancel="handleCancel"
    :maskClosable="false"
  >
    <div class="node-info">
      <a-form
        :model="formState"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 20 }"
        class="compact-form"
      >
        <a-form-item label="父节点">
          <span>{{ parentNodeTitle }}</span>
        </a-form-item>
        <a-form-item label="节点名称" name="nodeName">
          <a-input v-model:value="formState.nodeName" placeholder="请输入节点名称" />
        </a-form-item>
      </a-form>
    </div>
  </a-modal>
</template>

<script lang="ts" setup>
import { ref, reactive, defineProps, defineEmits } from 'vue'

const props = defineProps({
  parentNodeTitle: {
    type: String,
    default: '',
  },
  parentNodeKey: {
    type: String,
    default: '',
  },
})

const emit = defineEmits(['confirm', 'cancel'])

const show = ref(true)
const formState = reactive({
  nodeName: '',
})

const handleOk = () => {
  if (!formState.nodeName.trim()) {
    return
  }

  emit('confirm', {
    parentKey: props.parentNodeKey,
    nodeName: formState.nodeName,
  })

  // 重置表单
  formState.nodeName = ''
  show.value = false
}

const handleCancel = () => {
  // 重置表单
  formState.nodeName = ''
  show.value = false
  emit('cancel')
}
</script>

<style scoped>
.node-info {
  margin-bottom: 16px;
}

.compact-form :deep(.ant-form-item) {
  margin-bottom: 12px;
}

.compact-form :deep(.ant-form-item:last-child) {
  margin-bottom: 0;
}
</style>
