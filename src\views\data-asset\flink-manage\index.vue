<template>
  <div class="page-full flink-manage-page">
    <div class="content-div">
      <cardBox :title="'实时计算处理'" :subTitle="'实时计算处理信息'">
        <template #headerRight>
          <a-button v-action:reload type="primary" @click="fetchTableData()">
            <template #icon><UndoOutlined /></template>
            刷新
          </a-button>
          <a-button v-action:task type="primary" @click="getConfiList()">Flink任务管理</a-button>
          <!-- <a-button type="primary" @click="showMore = !showMore">
            <template #icon v-if="!showMore">
              <DownOutlined />
            </template>
            <template #icon v-else><UpOutlined /></template>
            {{ showMore ? '收起' : '搜索' }}
          </a-button> -->
        </template>
        <div class="list-mid">
          <a-row :span="24">
            <div class="list-mid-left">
              <a-icon type="info-circle" />
              <div>
                已选择<span>{{ selectedRowKeys.length }}</span
                >项
              </div>
            </div>
            <div class="list-mid-right">
              <a-space :size="8">
                <a-button
                  v-action:batchUninstall
                  size="small"
                  type="link"
                  @click="uninstallHandle(null)"
                  >批量卸载</a-button
                >
              </a-space>
            </div>
          </a-row>
        </div>
        <Table
          :columns="getColumns(0)"
          :getData="transgetFlinkListAPI"
          :tableConfig="{ rowSelection: Selection, rowKey: 'tableName' }"
          ref="tableRef"
          :autoRequest="false"
          :searchFormState="params"
          :pagination="false"
        >
          <template #search>
            <a-form-item label="表名" name="表名" :rules="[{ message: '请输入表名' }]">
              <a-input v-model:value="params.tableName" placeholder="请输入表名" />
            </a-form-item>
            <a-form-item label="描述" name="描述" :rules="[{ message: '请输入描述' }]">
              <a-input v-model:value="params.registTableDesc" placeholder="请输入描述" />
            </a-form-item>
          </template>
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'column'">
              <a @click="logModal(filterColumn(record[column.dataIndex]), '列详情', true)">{{
                filterColumn(record[column.dataIndex])
              }}</a>
            </template>
            <template v-if="column.dataIndex === 'tableName'">
              <a @click="logModal(filterColumn(record[column.dataIndex]), '表名详情', true)">{{
                record[column.dataIndex]
              }}</a>
            </template>
            <template v-if="column.dataIndex === 'registTableDesc'">
              <a @click="logModal(filterColumn(record[column.dataIndex]), '描述详情', true)">{{
                record[column.dataIndex]
              }}</a>
            </template>
            <template v-if="column.dataIndex === 'action'">
              <a-button v-action:uninstall size="small" type="link" @click="uninstallHandle(record)"
                >卸载</a-button
              >
            </template>
          </template>
        </Table>
      </cardBox>
    </div>
  </div>
</template>

<script lang="ts" setup>
import {
  getFlinkListAPI,
  SparkListDTO,
  uninstallFlinkAPI,
  type GridCol,
} from '@/api/services/data-asset/config-manager'
import { message, Modal, type TableProps } from 'ant-design-vue'
import { logModal } from '@/utils/log-modal'
import { ref } from 'vue'
import { UndoOutlined } from '@ant-design/icons-vue'
import { Table } from '@fs/fs-components'
import cardBox from '@/components/card-box/card-box.vue'
import { useUserStore } from '@/stores/user'
import { storeToRefs } from 'pinia'
import { getMenuGrids } from '@/utils/menu-grids'
import { getFlinkConfiList } from '@/api/services/monitor-maintenance/flink-monitor'
const userStore = useUserStore()
const { userInfo } = storeToRefs(userStore)
const route = useRoute()
// const showMore = ref(false)
const params = ref({
  tableName: '',
  registTableDesc: '',
  code: 4,
})
const actionWidth = 135

const selectedRowKeys = ref<string[]>([])
// const cardList = ref<SparkListDTO[]>([])
// const cloneCardList = ref<SparkListDTO[]>([])
const gridCol = ref<GridCol[]>([])

const tableRef = ref<InstanceType<typeof Table> | null>(null)

const Selection: TableProps['rowSelection'] = {
  onChange: (rowKeys: (string | number)[]) => {
    selectedRowKeys.value = rowKeys as string[]
  },
}

/* 获取列表数据 */
const transgetFlinkListAPI = (params: any) =>
  getFlinkListAPI(params).then((d: any) => {
    // 接口应该返回records 前端做处理
    console.log('接口应该返回records 前端做处理', params, d)
    selectedRowKeys.value = []
    // cloneCardList.value = d.data.data.data || []
    return d.data.data.data
  })

const getConfiList = () => {
  getFlinkConfiList({}).then((res) => {
    if (res.data) {
      window.open(res.data, '_blank')
    } else {
      message.warning('打开Flink任务管理失败,请在全局配置里添加Flink地址')
    }
  })
}

/**
 * 格式化数据为"['', '']"格式
 * @param column
 */
const filterColumn = (column: any[]) => {
  if (column && column.length > 0) {
    const str =
      typeof column === 'string'
        ? column
        : column.map((item: any) => {
            return `${item.name},${item.type}`
          })
    return str
  }
}

/**
 * 卸载
 * @param item
 */
const uninstallHandle = (item: SparkListDTO | null) => {
  let params: { code: number; tables: string } = {
    code: 5,
    tables: selectedRowKeys.value.join(','),
  }
  if (item) {
    params.tables = item.tableName!
  } else if (selectedRowKeys.value.length <= 0) {
    return message.warning('请至少选择一条数据')
  }
  Modal.confirm({
    title: '提示',
    content: '确定要卸载吗？',
    onOk: () => {
      return uninstallFlinkAPI(params).then((res: any) => {
        logModal(res, '卸载详情', true)
        fetchTableData()
      })
    },
  })
}

/**
 * table列表展示数据用到的字段列表
 * @param index 表单下标
 * @param config 更多配置
 * config.orderIndex: 是否自增序号
 * config.hideactionable: 是否隐藏操作栏
 * config.fixedAction: 固定操作栏
 */
const getColumns = (index: number, config: { [x: string]: any } = {}) => {
  if (config.gridCode) {
    // 如果有配置 gridCode，优先使用 gridCode 查找表格列字段
    const gridCode = config.gridCode
    const grid = gridCol.value.find((i) => i.gridCode === gridCode)
    if (grid) {
      return grid.gridColumns
    }
  }
  let data = JSON.parse(
    JSON.stringify((gridCol.value[index] && gridCol.value[index].gridColumns) || []),
  )
  console.log('终于获取到了', data, gridCol.value, index)
  // 是否隐藏操作栏
  if (config.hideactionable) {
    if (data.length > 1) data = data.slice(0, -1)
  }
  // console.log('data', data)
  return data
}

// 获取表格点击
// const onSelectChange = (rowKeys: string[]) => {
//   selectedRowKeys.value = rowKeys
// }

// /* 表格全选事件 */
// const onSelectAll = (selected: any, selectedRows: SparkListDTO[]) => {
//   if (selected) {
//     selectedRowKeys.value = selectedRows.map((item: SparkListDTO) => {
//       return item.tableName!
//     })
//   } else {
//     selectedRowKeys.value = []
//   }
// }

onBeforeMount(() => {
  const menuId = route.meta.menuId || ''
  const { userId, tenantId } = userInfo.value
  const header = {
    opUser: userId,
    tenantId,
  }
  if (!menuId) {
    throw new Error('该路由没有menuId')
  }
  getMenuGrids(menuId, header).then((res: any) => {
    gridCol.value = res.data.map((i: any) => {
      let width = 0 + actionWidth
      const gridColumns = (i.gridColumns || [])
        .map((ii: any) => {
          let colWidth = Number(ii.width) || 180
          width += colWidth
          let obj: any = {
            title: ii.columnCaption,
            dataIndex: ii.columnName,
            show: true,
            ellipsis: true,
            width: colWidth,
            align: 'center',
            scopedSlots: { customRender: ii.columnName },
          }
          if (ii.columnType === '7') {
            // eslint-disable-next-line no-new-func
            obj.columnInfo =
              ii.columnTypeParam && ii.columnTypeParam[0] === '{'
                ? new Function('return ' + ii.columnTypeParam)()
                : {}
          }
          return obj
        })
        .concat({
          // title: '操作',
          title: '操作',
          // @ts-ignore
          show: true,
          width: actionWidth,
          align: 'center',
          fixed: 'right',
          dataIndex: 'action',
          scopedSlots: { customRender: 'action' },
        })
      return {
        gridId: i.gridId,
        gridName: i.gridName,
        gridCode: i.gridCode,
        width,
        gridColumns,
      }
    })
  })
})

const fetchTableData = async () => {
  try {
    setTimeout(() => {
      tableRef.value?.getTableData()
    }, 20)
  } catch (error) {
    console.error('获取数据表列表失败:', error)
  }
}

onMounted(() => {
  fetchTableData()
})
</script>
<style lang="less" scoped>
.flink-manage-page {
  height: 100%;
  .header-div {
    display: flex;
    align-items: center;
    height: 48px;
    padding: 0px 12px 0 20px;
    border-bottom: 1px solid #0000001a;
  }
  .content-div {
    height: calc(100% - 48px);
  }
  :deep(.ant-btn) {
    margin-left: 8px;
  }
  .list-mid {
    border: 1px solid #bae7ff;
    background: #e6f7ff;
    padding: 10px;
    margin-bottom: 24px;

    .ant-row {
      display: flex;
      justify-content: space-between;

      .list-mid-left {
        display: flex;
        justify-content: flex-start;
        align-items: center;

        i {
          color: rgba(24, 144, 255, 1);
          margin-right: 6px;
        }
      }

      .list-mid-right {
        color: rgba(24, 144, 255, 1);

        span {
          cursor: pointer;
        }
      }

      &:after {
        content: none;
      }

      &:before {
        content: none;
      }
    }
  }
}
</style>
