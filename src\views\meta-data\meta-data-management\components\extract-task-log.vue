<template>
  <a-modal
    wrapClassName="extract-task-log"
    :open="visible"
    title="任务日志"
    width="1000px"
    @cancel="onClose"
    destroyOnClose
  >
    <Table
      :columns="columns"
      :getData="getTaskLog"
      ref="tableRef"
      :searchFormState="{
        taskId: props.logs?.taskId,
      }"
      :scroll="{ x: 1600 }"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'taskstatus'">
          <a-tag color="green" v-if="record.taskstatus === 0">成功</a-tag>
          <a-tag color="red" v-if="record.taskstatus === 1">失败</a-tag>
        </template>
      </template>
    </Table>
    <template #footer>
      <a-button @click="onClose">关闭</a-button>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, watch, defineProps, defineEmits } from 'vue'
import { Table } from '@fs/fs-components'
import { getTaskLog } from '@/api/assetmanager/metadatamanage/metadatamanage'

const props = defineProps<{
  visible: boolean
  logs: Record<string, any>
}>()
const emit = defineEmits(['update:visible'])
const searchFormData = ref({})

const columns = [
  {
    title: '任务运行状态',
    dataIndex: 'taskstatus',
    key: 'taskstatus',
  },
  { title: '报错信息', dataIndex: 'errormsg', key: 'errormsg' },
  { title: '数据库类型', dataIndex: 'dbType', key: 'dbType' },
  { title: 'schame名称', dataIndex: 'dbschame', key: 'dbschame' },
  { title: '源库名称', dataIndex: 'dbname', key: 'dbname' },
  { title: '源表名称', dataIndex: 'tableName', key: 'tableName' },
  { title: '任务执行开始时间', dataIndex: 'createTime', key: 'createTime', width: 180 },
]

const onClose = () => {
  emit('update:visible', false)
}
</script>
