<template>
  <a-popover
    trigger="click"
    placement="right"
    v-model:open="visible"
    @openChange="handleVisibleChange"
  >
    <template #content>
      <div class="popover-content">
        <div class="h-40vh overflow-y-auto hide-scrollbar">
          <div class="filter-main" v-if="!selectedMethod?.columnType">
            <a-input v-model:value="filterValue" placeholder="查找..." allow-clear>
              <template #prefix>
                <SearchOutlined />
              </template>
            </a-input>
            <a-divider style="margin: 10px 0"></a-divider>
            <div class="data-source">
              <div class="data-source-title">
                <!-- <DatabaseOutlined /> -->
                <span style="margin-top: -4px; margin-left: 10px">基本功能</span>
              </div>
              <div class="data-source-list">
                <a-menu style="border: none">
                  <a-menu-item
                    v-for="item in methodList"
                    @click="selectMenu(item)"
                    :key="item.value"
                  >
                    {{ item.label }}
                  </a-menu-item>
                </a-menu>
              </div>
            </div>
          </div>
          <template v-else>
            <div class="data-source-title">
              <LeftOutlined @click="selectedMethod = null" />
              <span>{{ selectedMethod.label }}</span>
            </div>
            <div class="data-source-list">
              <div class="" v-for="item in joinTableColumns" :key="item.tableName">
                <a-collapse
                  v-model:activeKey="activeKeys"
                  collapsible="header"
                  :expandIconPosition="'end'"
                  ghost
                >
                  <a-collapse-panel :key="item.tableName" :showArrow="false">
                    <template #header>
                      <div flex="~ gap-2" class="overflow-hidden" text="20px b700">
                        <TableOutlined />
                        <Tooltip :title="item.tableName">
                          <span class="ellipsis">{{ item.tableName }}</span>
                        </Tooltip>
                        <RightOutlined
                          text="14px"
                          :rotate="!activeKeys.includes(item.tableName) ? 0 : 90"
                        />
                      </div>
                    </template>
                    <data-columns
                      :data="dataColumnData(item)"
                      @handleClick="selectColumn($event)"
                    ></data-columns>
                  </a-collapse-panel>
                </a-collapse>
              </div>
            </div>
          </template>
        </div>
      </div>
    </template>
    <div class="group-action">
      <slot>
        <div class="group-action-trigger">选择函数</div>
      </slot>
    </div>
  </a-popover>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import {
  SearchOutlined,
  DatabaseOutlined,
  LeftOutlined,
  RightOutlined,
  TableOutlined,
} from '@ant-design/icons-vue'
import type { dataColumn } from '../data-columns/type'
import { JoinTableColumnsKey, type JoinTableColumns } from '@/views/indicator-config/provide-keys'
import type { ServerFieldItem } from '@/api/services/indicator/type'
import { Tooltip } from 'ant-design-vue'

defineOptions({
  name: 'SummaryIndex',
})
const props = defineProps<{
  tableName: string
  columns: dataColumn[]
}>()

const emits = defineEmits(['handleClick'])
const joinTableColumns = inject<Ref<JoinTableColumns>>(JoinTableColumnsKey)
const activeKeys = ref<string[]>([])
const dataColumnData = (item: any) => {
  return item.fields.filter((field: ServerFieldItem) => {
    return field.columnType === selectedMethod.value.columnType
  })
}
watch(activeKeys, (newValue) => {
  console.log('activeKeys', newValue)
})
const visible = ref(false)
const filterValue = ref('')
const selectedMethod = ref<any>()

const methodList = ref([
  { value: 'sum', label: '求和', fieldName: '', columnType: 'NUMBER' },
  { value: 'avg', label: '平均值', fieldName: '', columnType: 'NUMBER' },
  { value: 'distinct_count', label: '去重计算', fieldName: '', columnType: 'any' },
  { value: 'count', label: '累计行数', fieldName: '' },
  { value: 'min', label: '最小值', fieldName: '', columnType: 'any' },
  { value: 'max', label: '最大值', fieldName: '', columnType: 'any' },
])

const currentColumns = computed(() => {
  if (selectedMethod.value.columnType === 'any') return props.columns
  return props.columns.filter((item) => {
    return item.columnType === selectedMethod.value.columnType
  })
})

const handleVisibleChange = (status: boolean) => {
  visible.value = status
}

function selectMenu(item: any) {
  selectedMethod.value = item
  if (!item.columnType) {
    handleClick()
  }
}

function selectColumn(item: dataColumn) {
  selectedMethod.value.fieldName = item.title
  handleClick()
}

function handleClick() {
  visible.value = false
  emits('handleClick', selectedMethod.value)
}
</script>

<style lang="less" scoped>
:deep(.ant-collapse-header-text) {
  max-width: 100%;
}
.popover-content {
  max-width: 500px;
  width: 300px;
}
.group {
  &-action {
    display: inline-flex;
    font-weight: bold;
    color: rgb(136, 191, 77);
    border-radius: 6px;
    border: 2px solid rgba(136, 191, 77, 0.25);
    cursor: pointer;
    pointer-events: auto;
    -webkit-box-align: stretch;
    align-items: stretch;
    transition: border 300ms linear;
    :deep(&-trigger) {
      display: flex;
      -webkit-box-align: center;
      align-items: center;
      padding: 10px;
      background-color: transparent;
      border-radius: 6px;
      transition: background 300ms linear 0s;
    }
  }
}
.data-source {
  &-title {
    display: flex;
    align-items: center;
    padding: 10px 0;
    font-size: 20px;
    font-weight: bold;
  }
}
</style>
