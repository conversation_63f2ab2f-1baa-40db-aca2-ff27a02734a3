<script setup lang="ts">
import api from '@/api/sql-edit-sys'
import type { TableProps } from 'ant-design-vue'
import { useRequest } from 'vue-hooks-plus'
import { CheckOutlined, CloseOutlined } from '@ant-design/icons-vue'
import { h } from 'vue' 

defineOptions({
  name: 'TableFields',
})
const {
  loading,
  run: getTableFieldData,
  data: tableData,
} = useRequest(api.getTableFieldData, {
  manual: true,
})
const columns = reactive<TableProps['columns']>([
  {
    title: '字段名',
    dataIndex: 'columnName',
  },
  {
    title: '字段类型',
    dataIndex: 'columnType',
  },
  {
    title: '注释',
    dataIndex: 'comment',
  },
  {
    title: '长度',
    dataIndex: 'length',
  },
  {
    title: '小数点',
    dataIndex: 'decimal',
  },
  {
    title: '是否 Null',
    dataIndex: 'isNull',
  },
  {
    title: '是否虚拟',
    dataIndex: 'virtual',
  },
  {
    title: '是否主键',
    dataIndex: 'primaryKey',
  },
  {
    title: '自动递增',
    dataIndex: 'increment',
  },
  {
    title: '操作',
    dataIndex: 'actions',
  },
])

const checkOrNot = (flag: boolean) => {
  return h(flag ? CheckOutlined : CloseOutlined, {
    style: {
      color: flag ? '#52c41a' : '#f5222d',
    }
  })
}

function onEdit(record: any) {
  console.log('编辑:', record)
}
function onDelete(record: any) {
  console.log('删除:', record)
}
</script>

<template>
  <a-table class="table-fields" :columns="columns" :data-source="tableData">
    <template #bodyCell="{ column, record }">
      <template v-if="column.dataIndex === 'actions'">
        <div flex="~ gap-2">
          <a-button type="link" class="px-0" @click="onEdit(record)">编辑</a-button>
          <a-button type="link" class="px-0" @click="onDelete(record)">删除</a-button>
        </div>
      </template>
    </template>
  </a-table>
</template>

<style scoped></style>
