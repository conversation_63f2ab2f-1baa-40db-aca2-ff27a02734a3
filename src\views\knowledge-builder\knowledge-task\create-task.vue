<template>
  <div class="create-task">
    <div class="steps-content">
      <KeepAlive>
        <Step1 ref="currentRef" v-model:data="task"></Step1>
      </KeepAlive>
    </div>
    <a-divider type="horizontal" orientation="left,right,center"> </a-divider>
    <div class="steps-action text-right">
      <a-space>
        <a-button @click="close"> 取消 </a-button>
        <a-button :loading="loading" type="primary" @click="onClickFinish"> 确认 </a-button>
      </a-space>
    </div>
  </div>
  <!-- 日志抽屉 -->
  <!-- <TaskLog ref="taskLogRef" /> -->
</template>

<script setup lang="ts">
import { message } from 'ant-design-vue'
import { DotModelType, DotType, type TaskItem } from '../type'
import Step1 from './step1.vue'
import dayjs from 'dayjs'
import type { KeepAlive } from 'vue'
import { reqPreviewSegment } from '@/api/services/knowledge'

const props = defineProps<{
  onFinish: (task: TaskItem) => void
  close: () => void
}>()

const route = useRoute()
const current = ref<number>(0)
const task = reactive<TaskItem>({
  projectId: (route.query?.projectId as string) || 1,
  createUser: '',
  file: null,
  fileList: [],
  jobName: '',
  dataType: DotType.DOTDATA,
  modelType: DotModelType.FILE,
})

const currentRef = ref()
const loading = ref(false)

const handleNext = async () => {
  // 先校验必填项
  const target = currentRef.value?.stepSubmit
  if (target) {
    const result = await target()
    if (result) {
      current.value++
    }
  }
}

const onClickFinish = async () => {
  // 先校验必填项
  const target = currentRef.value?.stepSubmit
  // console.log(
  //   '%c [ target ]-86',
  //   'font-size:13px; background:#624932; color:#a68d76;',
  //   target,
  //   currentRef.value,
  // )
  if (target) {
    const result = await target()
    if (result) {
      try {
        loading.value = true
        await props.onFinish(task)
        loading.value = false
      } catch (error) {
        loading.value = false
      }
    }
  }
}
</script>

<style scoped>
.steps-content {
  background-color: white;
  min-height: 350px;
  text-align: center;
}
</style>
