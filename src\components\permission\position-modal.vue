<template>
  <a-modal
    :open="open"
    @ok="modalHandleOk"
    :title="modalType === 'edit' ? '编辑' : '新增'"
    @cancel="cancel"
    width="800px"
    :confirm-loading="confirmLoading"
  >
    <a-form
      ref="formRef"
      :model="form"
      :rules="rules"
      :label-col="labelCol"
      :wrapper-col="wrapperCol"
    >
      <a-form-item label="职务编号" name="positionId" v-if="modalType === 'edit'">
        <a-input
          v-model:value="form.positionId"
          :disabled="disabled"
          placeholder="请输入职务编号"
        />
      </a-form-item>
      <a-form-item label="职务名称" name="positionName">
        <a-input v-model:value="form.positionName" placeholder="请输入职务名称" />
      </a-form-item>
      <a-form-item label="是否启用" name="isUse">
        <a-radio-group v-model:value="form.isUse" placeholder="请选择是否启用">
          <a-radio :value="1">是</a-radio>
          <a-radio :value="0">否</a-radio>
        </a-radio-group>
      </a-form-item>
      <a-form-item label="备注" name="remark">
        <a-textarea v-model:value="form.remark" placeholder="请输入备注" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>
<script lang="ts" setup>
import { defineEmits, defineProps, defineExpose, ref } from 'vue'
import type { Rule } from 'ant-design-vue/es/form'
import { updatePosition, addPosition } from '@/api/services/permission'
import { message } from 'ant-design-vue'
interface FormState {
  positionId: string
  positionName: string
  isUse: string
  remark: string
}

defineProps({
  open: {
    type: Boolean,
    default: false,
  },
  disabled: Boolean,
})

const emits = defineEmits(['modalHandleOk', 'modalCancel', 'confirmLoadingHandle'])
const formRef = ref<HTMLFormElement | null>(null)
const labelCol = { span: 5 }
const wrapperCol = { span: 13 }
const modalType = ref<string>('')
const confirmLoading = ref<boolean>(false)
const form = ref<FormState>({
  positionId: '',
  positionName: '',
  isUse: '',
  remark: '',
})

const rules: Record<string, Rule[]> = {
  positionName: [{ required: true, message: '请输入职务名称', trigger: 'blur' }],
  isUse: [{ required: true, message: '请选择是否启用', trigger: 'blur' }],
}

function modalHandleOk() {
  formRef.value &&
    formRef.value
      .validate()
      .then(() => {
        const { positionId, positionName, isUse, remark } = form.value
        confirmLoading.value = true
        if (modalType.value === 'edit') {
          const update = {
            positionId,
            positionName,
            isUse,
            remark,
          }
          updatePosition(update)
            .then(() => {
              emits('modalHandleOk', form.value)
              message.success('编辑成功!')
            })
            .finally(() => {
              confirmLoading.value = false
            })
        } else {
          const data = {
            positionName,
            isUse,
            remark,
            buId: 'system_bu',
          }
          addPosition(data)
            .then(() => {
              emits('modalHandleOk', form.value)
              message.success('新增成功!')
            })
            .finally(() => {
              confirmLoading.value = false
            })
        }
      })
      .catch((error: any) => {
        console.log(error)
      })
}

function cancel() {
  emits('modalCancel')
}
function show(type: string, data?: Record<string, any>) {
  formRef.value?.resetFields()
  modalType.value = type
  if (type === 'edit') {
    editForm(data)
  } else {
    form.value = {
      positionId: '',
      positionName: '',
      isUse: '',
      remark: '',
    }
  }
}

function editForm(data?: any) {
  if (data) {
    const copyData = JSON.parse(JSON.stringify(data))
    form.value = copyData
  }
}
defineExpose({ show })
</script>
