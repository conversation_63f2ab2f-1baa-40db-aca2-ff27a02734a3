import service from '@/api'
import { SODATAFLINK } from '@/api/base'
import type { Column } from '@fs/fs-components/src/components/table/type'
import axios from 'axios'
export class SparkListDTO {
  tableName?: string
  loadDate?: string
  count?: number
  column?: Array<any>
  isCheck?: boolean
  isOpen?: boolean
  registTableDesc?: string
}

export interface GridCol {
  gridCode: string
  gridName: string
  gridId: string
  width: number
  gridColumns: Partial<GridColumnsDto>[]
}

export interface GridColumnsDto extends Column {
  show: boolean
  columnInfo: {
    /* 字段在卡片展示的类型 */
    type: string
    /* 字段名 */
    columnTitle?: string
    /* 字段对应的图标 */
    icon?: string
    /* 字段文字的色值 */
    color?: string
    /* 字段值对应文字的背景色 */
    bgColor?: string
    /* 排序 */
    sort?: number
    /* 字段值过滤器 */
    filter?: { [x: string]: any }
    /* 字段值对应的标签类型过滤器 */
    filterBadge?: { [x: string]: any }
  }
}
/**
 * 获取spark列表
 * @param params
 */
export interface Parameter {
  [prop: string]: any
}
export function getSparkListAPI(params: Parameter) {
  return service({
    method: 'post',
    url: `${SODATAFLINK}/config/SparkTableManager/getDicTable`,
    data: params,
  })
}

/**
 * 页面内容加载
 * @param params
 */
export function getSparkConfiList(params: Parameter) {
  return axios({
    method: 'get',
    url: `${SODATAFLINK}/new2dataplatform/globalConfig/globalconfigPageList`,
    params,
  })
}

/**
 * spark列表卸载
 * @param params
 */
export function uninstallSparkAPI(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/config/SparkTableManager/dropRegisterTable`,
    method: 'post',
    data: params,
  })
}

/**
 * 获取flink列表
 * @param params
 */
export function getFlinkListAPI(params: Parameter) {
  return service({
    method: 'post',
    url: `${SODATAFLINK}/flink/FlinkTable/getDicTable`,
    data: params,
  })
}

/**
 * flink列表卸载
 * @param params
 */
export function uninstallFlinkAPI(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/flink/FlinkTable/dropRegisterTable`,
    method: 'post',
    data: params,
  })
}
/**
 * 页面内容加载
 * @param params
 */
export function getFlinkConfiList(params: Parameter) {
  return service({
    method: 'post',
    url: `${SODATAFLINK}/flink/FlinkTable/trunFlinkUrl`,
    data: params,
  })
}
