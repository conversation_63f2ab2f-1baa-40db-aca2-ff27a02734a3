export interface UserInfo {
  userAvatar: string
  userName: string
}

export interface UserMenu {
  menuId: string
  menuName: string
  menuUrl: string
  childrens: UserMenu[]
  [key: string]: any
}

export interface LayoutProps {
  title?: string
  logo?: string
  menu: UserMenu[] // 菜单
  menuSelectedKeys?: (string | number)[] // 当前选中的菜单项 key 数组
  menuOpenKeys?: (string | number)[] // 当前展开的 SubMenu 菜单项 key 数组
  userInfo?: UserInfo
  breadcrumb?: boolean // 是否显示面包屑
  configProvider?: any // antd ConfigProvider 配置
  isSecondary?: boolean // 二级页面
  siderWidth?: number | string // 菜单宽度
}

export interface BreadcrumbItem {
  title: string
  id: string
  path: string
}
