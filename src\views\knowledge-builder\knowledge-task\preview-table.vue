<template>
  <a-modal v-model:open="visible" title="预览数据" :width="1000" @cancel="handleCancel">
    <a-tabs v-model:activeKey="activeTab">
      <a-tab-pane key="node" tab="节点提取数据">
        <a-table
          :columns="columns"
          :data-source="tableData"
          :pagination="pagination"
          :loading="loading"
          @change="handleTableChange"
          :scroll="{ y: 480 }"
        >
          <template #bodyCell="{ column, record, index }">
            <template v-if="column.dataIndex !== 'action'">
              <div v-if="editingIndex === index && column.dataIndex === 'nodeName'">
                <a-input v-model:value="record[column.dataIndex]" />
              </div>
              <div v-else>
                {{ record[column.dataIndex] }}
              </div>
            </template>
            <template v-else>
              <div>
                <a-button v-if="editingIndex === index" type="link" @click="handleSave(record)">
                  保存
                </a-button>
                <a-button
                  v-else
                  type="link"
                  :disabled="editingIndex !== -1"
                  @click="handleEdit(record, index)"
                >
                  编辑
                </a-button>
              </div>
            </template>
          </template>
        </a-table>
      </a-tab-pane>
      <a-tab-pane key="relationship" tab="关系提取数据">
        <a-table
          :columns="relationshipColumns"
          :data-source="relationshipTableData"
          :pagination="relationshipPagination"
          :loading="relationshipLoading"
          @change="handleRelationshipTableChange"
          :scroll="{ y: 480 }"
        />
      </a-tab-pane>
    </a-tabs>
    <template #footer>
      <a-button @click="handleCancel">关闭</a-button>
      <a-button type="primary" @click="handleSubmit">提交</a-button>
    </template>
  </a-modal>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue'
import type { TablePaginationConfig } from 'ant-design-vue'
import {
  nodePreview,
  updateNodeName,
  taskCommit,
  relationshipPreview,
} from '@/api/services/knowledge'
import { message } from 'ant-design-vue'

const props = defineProps<{
  open: boolean
  curTask: any
}>()

const emit = defineEmits(['update:open', 'update:curTask'])
const previewOpen = defineModel<boolean>('open')

const visible = ref(false)
const editingIndex = ref(-1)
const tableData = ref<Record<string, any>[]>([])
const loading = ref(false)
const activeTab = ref('node')

// 关系提取数据
const relationshipTableData = ref<Record<string, any>[]>([])
const relationshipLoading = ref(false)

const pagination = ref<TablePaginationConfig>({
  current: 1,
  pageSize: 10,
  showSizeChanger: true,
  total: 0,
  size: 'small',
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条`,
})
const relationshipPagination = ref<TablePaginationConfig>({
  current: 1,
  pageSize: 10,
  showSizeChanger: true,
  total: 0,
  size: 'small',
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条`,
})

const columns = [
  {
    title: '节点名称',
    dataIndex: 'nodeName',
    key: 'nodeName',
    width: 300,
  },
  {
    title: '节点标签',
    dataIndex: 'nodeLabel',
    key: 'nodeLabel',
    width: 150,
  },
  {
    title: '提取内容',
    key: 'content',
    dataIndex: 'content',
  },
  {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
    width: 100,
    fixed: 'right',
  },
]

const relationshipColumns = [
  { title: '原节点名称', dataIndex: 'sourceNodeName', key: 'sourceNodeName', width: 200 },
  { title: '目标节点名称', dataIndex: 'targetNodeName', key: 'targetNodeName', width: 200 },
  { title: '关系', dataIndex: 'relationship', key: 'relationship', width: 120 },
  { title: '提取内容', dataIndex: 'content', key: 'content', width: 300 },
]

// 加载节点数据
const loadData = async () => {
  try {
    loading.value = true
    const res = await nodePreview({
      taskId: props.curTask.id,
      pageIndex: pagination.value.current,
      pageSize: pagination.value.pageSize,
    })
    tableData.value = res.data.records
    pagination.value.total = res.data.totalRecords
  } catch (error) {
    console.error('Failed to load data:', error)
  } finally {
    loading.value = false
  }
}

// 加载关系数据
const loadRelationshipData = async () => {
  try {
    relationshipLoading.value = true
    const res = await relationshipPreview({
      taskId: props.curTask.id,
      pageIndex: relationshipPagination.value.current,
      pageSize: relationshipPagination.value.pageSize,
    })
    relationshipTableData.value = res.data.records
    relationshipPagination.value.total = res.data.totalRecords
  } catch (error) {
    console.error('Failed to load relationship data:', error)
  } finally {
    relationshipLoading.value = false
  }
}

const handleTableChange = (pag: TablePaginationConfig) => {
  pagination.value.current = pag.current || 1
  pagination.value.pageSize = pag.pageSize || 10
  loadData()
}
const handleRelationshipTableChange = (pag: TablePaginationConfig) => {
  relationshipPagination.value.current = pag.current || 1
  relationshipPagination.value.pageSize = pag.pageSize || 10
  loadRelationshipData()
}

const handleSubmit = async () => {
  await taskCommit({
    taskId: props.curTask.id,
  })
  message.success('提交成功')
  handleCancel()
  emit('update:curTask', false)
}

// 监听open属性变化
watch(
  () => props.open,
  (val) => {
    visible.value = val
    if (val) {
      if (activeTab.value === 'node') {
        loadData()
      } else {
        loadRelationshipData()
      }
    }
  },
)

// 监听tab切换
watch(activeTab, (val) => {
  if (visible.value) {
    if (val === 'node') {
      loadData()
    } else {
      loadRelationshipData()
    }
  }
})

const handleEdit = (record: any, index: number) => {
  record.oldName = record.nodeName
  editingIndex.value = index
}

const handleSave = async (record: any) => {
  await updateNodeName({
    taskId: props.curTask.id,
    oldName: record.oldName,
    newName: record.nodeName,
  })
  editingIndex.value = -1
  message.success('修改成功')
  loadData()
}

const handleCancel = () => {
  visible.value = false
  previewOpen.value = false
  editingIndex.value = -1
  tableData.value = []
  relationshipTableData.value = []
  activeTab.value = 'node'
}
</script>

<style scoped>
.ant-input {
  width: 100%;
}
</style>
