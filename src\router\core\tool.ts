import type { RouteRecordRaw } from 'vue-router'

/**
 * 权限系统路由
 */
const routes: RouteRecordRaw[] = [
  {
    path: '/anomaly-page',
    name: 'anomaly-page',
    component: () => import('@/views/component-page/anomaly-page.vue'),
    meta: {
      icon: 'BlockOutlined',
    },
  },
  {
    path: '/advanced-form',
    name: 'advanced-form',
    component: () => import('@/views/component-page/advanced-form.vue'),
    meta: {
      icon: 'BlockOutlined',
    },
  },
  {
    path: '/table-list',
    name: 'table-list',
    component: () => import('@/views/component-page/table-list.vue'),
    meta: {
      icon: 'BlockOutlined',
    },
  },
  {
    path: '/echarts',
    name: 'echarts',
    component: () => import('@/views/component-page/echarts-page.vue'),
    meta: {
      icon: 'BlockOutlined',
    },
  },
  {
    path: '/page-layout',
    name: 'page-layout',
    component: () => import('@/views/component-page/page-layout.vue'),
    meta: {
      icon: 'BlockOutlined',
    },
  },
  {
    path: '/step-form',
    name: 'step-form',
    component: () => import('@/views/component-page/step-form.vue'),
    meta: {
      icon: 'BlockOutlined',
    },
  },
  {
    path: '/manage-api',
    name: 'manage-api',
    component: () => import('@/views/component-page/api-manage.vue'),
    meta: {
      icon: 'BlockOutlined',
    },
  },
  {
    path: '/port-manage',
    name: 'port-manage',
    component: () => import('@/views/component-page/port-manage.vue'),
    meta: {
      icon: 'BlockOutlined',
    },
  },
  {
    path: '/gpt-manage',
    name: 'gpt-manage',
    component: () => import('@/views/component-page/gpt-manage.vue'),
    meta: {
      icon: 'BlockOutlined',
    },
  },
]

export default routes
