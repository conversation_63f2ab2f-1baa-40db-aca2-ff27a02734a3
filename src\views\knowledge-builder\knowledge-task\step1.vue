<template>
  <div class="step-content">
    <a-form
      layout="horizontal"
      label-align="right"
      class="form-content w-80%"
      :label-col="{ span: 4 }"
      :wrapper-col="{ span: 20 }"
      :rules="rules"
      ref="formRef"
      :model="formState"
    >
      <!-- 任务名称输入 -->
      <a-form-item name="jobName" required label="任务名称" class="justify-center wfull">
        <a-input placeholder="请输入" v-model:value="formState.jobName" class="task-input wfull" />
      </a-form-item>

      <!-- 模型服务选择 -->
      <a-form-item
        defaultValue="MODEL_EXTRACT"
        name="modelType"
        label="提取方式"
        class="justify-center wfull"
      >
        <a-radio-group v-model:value="formState.modelType">
          <a-radio :value="DotModelType.FILE">文件上传</a-radio>
          <a-radio :value="DotModelType.DATABASE">数据库</a-radio>
        </a-radio-group>
      </a-form-item>

      <!-- 文件类型选择 -->
      <a-form-item
        defaultValue="FILE_EXTRACT"
        name="dataType"
        required
        label="数据类型："
        style=""
        v-if="formState.modelType === DotModelType.FILE"
      >
        <a-radio-group v-model:value="formState.dataType">
          <a-radio :value="DotType.DOTDATA">节点数据</a-radio>
          <a-radio :value="DotType.RELATIONSHIP">关系数据</a-radio>
        </a-radio-group>
      </a-form-item>

      <!-- 文件上传区域 -->
      <a-form-item
        name="fileList"
        required
        label="上传文件"
        v-if="formState.modelType === DotModelType.FILE"
      >
        <a-upload-dragger
          name="file"
          :multiple="false"
          :before-upload="beforeUpload"
          :show-upload-list="true"
          class="upload-area"
          @change="handleChange"
          @remove="handleRemove"
          :maxCount="1"
          accept=".csv"
        >
          <p class="upload-icon mb-2" text="34px primary">
            <InboxOutlined />
          </p>
          <p class="ant-upload-text">点击或将文件拖拽到这里上传</p>
          <p class="ant-upload-hint">支持拓展名：.csv</p>
        </a-upload-dragger>

        <div style="margin-top: 10px">
          模板下载：
          <a @click="downloadFile('node.csv')" style="margin-right: 10px; cursor: pointer"
            >node.csv</a
          >
          <a @click="downloadFile('relationship.csv')" style="cursor: pointer">relationship.csv</a>
        </div>
      </a-form-item>
      <a-form-item
        name="modelId"
        required
        label="模型库"
        v-if="formState.modelType === DotModelType.DATABASE"
      >
        <a-select style="width: 100%" placeholder="请选择模型库" v-model:value="formState.modelId">
          <!-- <a-select-option value="PARAMETER">参数</a-select-option>
                        <a-select-option value="VARIABLE">变量</a-select-option> -->
          <!-- <a-select-option value="COLLECTION">集合</a-select-option> -->
          <a-select-option v-for="(item, index) in resultList" :value="item.id" :key="index">{{
            item.modelName
          }}</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item
        name="connectionInfo"
        required
        label="数据库"
        v-if="formState.modelType === DotModelType.DATABASE"
      >
        <a-button @click="handleSelectData">选择数据</a-button>
        <a-card class="mt-4" title="数据源信息" :bordered="false" v-if="formState.connectionInfo">
          <a-descriptions :column="1">
            <a-descriptions-item label="数据源连接信息">
              {{ formState.connectionInfo?.connectionInfo?.dbDesc }}
            </a-descriptions-item>
            <a-descriptions-item label="数据库名称">
              {{ formState.connectionInfo?.dbName }}
            </a-descriptions-item>
            <a-descriptions-item label="模块名称" v-if="formState.connectionInfo?.dbSchema">
              {{ formState.connectionInfo?.dbSchema }}
            </a-descriptions-item>
            <a-descriptions-item label="表名">
              {{ formState.connectionInfo?.tabName }}
            </a-descriptions-item>
            <a-descriptions-item label="字段名">
              {{ formState.connectionInfo?.selectColumns }}
            </a-descriptions-item>
          </a-descriptions>
        </a-card>
      </a-form-item>
    </a-form>
  </div>

  <select-modal
    v-model:visible="assetModalVisible"
    :parent-data="{ name: 'test' }"
    @success="handleAssetSuccess"
  />
</template>

<script setup lang="ts">
import {
  message,
  UploadDragger,
  type UploadChangeParam,
  type UploadFile,
  type UploadProps,
} from 'ant-design-vue'
import { InboxOutlined } from '@ant-design/icons-vue'
import { DotModelType, DotType, type TaskItem } from '../type'
import type { FileType } from 'ant-design-vue/es/upload/interface'
import type { UploadRequestOption } from 'ant-design-vue/es/vc-upload/interface'
import SelectModal from './select-modal.vue'
import { reqGetModelList } from '@/views/model-service/api'

const assetModalVisible = ref(false)
const resultList = ref<any[]>([])

const handleAssetSuccess = (data: any) => {
  formState.value.connectionInfo = data
  console.log('%c [ data ]-93', 'font-size:13px; background:#389051; color:#7cd495;', data)
}
const handleSelectData = () => {
  assetModalVisible.value = true
}

onMounted(async () => {
  await reqGetModelListHandle()
})

const reqGetModelListHandle = async () => {
  const res: any = await reqGetModelList({ pageIndex: 1, pageSize: 1000 })
  resultList.value = res.data.records
  console.log('🚀 ~ reqGetModelListHandle ~ res:', res)
}

// 自定义校验
const validateUploadFn = (_: any, value: UploadFile[]) => {
  if (value.length === 0) {
    return Promise.reject('请上传文件')
  }
  if (value[0]?.response?.success === false) {
    const err = value[0]?.response?.errorMsg || ''
    return Promise.reject(`文件上传失败：${err}`)
  }
  return Promise.resolve()
}

// 自定义校验
const validateDataBase = (_: any, value: UploadFile[]) => {
  console.log('🚀 ~ validateDataBase ~ value:', value)
  if (!value) {
    return Promise.reject('请选择对应表字段')
  }
  return Promise.resolve()
}

const rules = {
  jobName: [{ required: true, message: '请输入任务名称', trigger: 'change' }],
  fileType: [{ required: true, message: '请选择数据类型', trigger: 'change' }],
  modelId: [{ required: true, message: '请选择模型库', trigger: 'change' }],
  fileList: [{ required: true, validator: validateUploadFn, trigger: 'change' }],
  connectionInfo: [{ required: true, validator: validateDataBase, trigger: 'change' }],
}
const formRef = ref()
const stepSubmit = async () => {
  const res = await formRef.value.validate().catch((error: any) => {
    console.log('error', error)
  })
  if (res) return true
}

defineExpose({
  stepSubmit,
})
const formState = defineModel<TaskItem>('data', { required: true })
const fileList = ref<UploadFile[]>([])
// 文件上传前校验
const beforeUpload: UploadProps['beforeUpload'] = (file) => {
  const validExtensions = ['csv']
  const extension = file.name.split('.').pop()?.toLowerCase()

  if (!extension || !validExtensions.includes(extension)) {
    message.error('不支持该文件类型')
    return false
  }
  return false
}

const handleChange = (info: UploadChangeParam) => {
  if (fileList.value.length > 0) return
  console.log('文件改变', info)
  let newFileList = [...info.fileList]
  // 只保留最新选择的文件（根据需求调整）
  formState.value.fileList = newFileList
  formState.value.file = info.file
  console.log(
    '%c [ info.file ]-121',
    'font-size:13px; background:#9ba289; color:#dfe6cd;',
    info.file,
  )
}
const handleRemove = (file: any) => {
  fileList.value = fileList.value.filter((f) => f.uid !== file.uid)
}

const downloadFile = (filename: string) => {
  const envMode = import.meta.env.MODE
  if (envMode === 'development') {
    window.location.href = `/public/${filename}`
  } else {
    window.location.href = `/${filename}`
  }
}
</script>

<style scoped>
.step-content {
  flex: 3 1 0%;
  display: flex;
  flex-direction: column;
  align-items: center;
}
:deep(.ant-form-item-control-input-content) {
  text-align: left;
}
:deep(.ant-form-item-explain-error) {
  text-align: left;
}
</style>
