<template>
  <div>
    <a-card title="规则配置" :bordered="false" @contextmenu="onContextmenu">
      <template v-slot:extra>
        <span style="margin-left: 16px">
          <a-button
            type="primary"
            shape="default"
            size="small"
            :loading="false"
            :disabled="false"
            @click="preview"
          >
            预览
          </a-button>
        </span>
      </template>
      <div :ops="ops" style="width: 100%; height: 100%">
        <a-row>
          <a-col :span="1" />
          <a-col :span="22">
            <a-card title="条件集" class="condition_set" size="small">
              <a-skeleton v-if="generalRule.conditionGroup.length === 0" :paragraph="{ rows: 3 }" />
              <a-spin :spinning="conditionMoveLoading">
                <draggable
                  title="条件集"
                  :group="generalRule.ruleId"
                  v-model="generalRule.conditionGroup"
                  item-key="id"
                  handle=".mover"
                >
                  <template #item="{ element: cg, index: cgi }">
                    <a-card :bordered="false" size="small" :key="cg.id">
                      <template #title>
                        <div style="margin-right: 16px; padding-left: 2px">
                          <a-input
                            class="conditionGroupNameInput"
                            style="padding: 0; border: none; background: none"
                            @blur="updateConditionGroupName(cg)"
                            :placeholder="`条件组${cgi}`"
                            v-model:value="cg.name"
                          />
                        </div>
                      </template>

                      <template #extra>
                        <DragOutlined
                          class="dynamic-delete-button mover"
                          style="font-size: 18px; margin-right: 10px"
                        />

                        <DeleteOutlined
                          class="dynamic-delete-button"
                          style="font-size: 18px"
                          @click="deleteConditionGroup(cg)"
                        />
                      </template>

                      <a-skeleton
                        v-if="cg.conditionGroupCondition.length === 0"
                        :paragraph="{ rows: 2 }"
                      />
                      <draggable v-model="cg.conditionGroupCondition" item-key="id">
                        <template #item="{ element: cgc }">
                          <a-alert
                            closable
                            style="
                              border: none;
                              padding: 6px 30px 6px 6px;
                              margin-bottom: 10px;
                              display: flex;
                              align-items: center;
                            "
                            :key="cgc.id"
                            @dblclick="editConditionHandler(cg, cgc)"
                            @close="
                              deleteCondition(cg.conditionGroupCondition, cgc.id, cgc.condition.id)
                            "
                            class="conditionItem task-item"
                          >
                            <template v-slot:description>
                              <p style="margin-bottom: 0; margin-top: 0">
                                <a-tag
                                  color="blue"
                                  style="
                                    padding: 0 2px 2px 2px;
                                    font-size: 13px;
                                    margin-bottom: 3px;
                                  "
                                >
                                  （{{ cgc.condition.name }}）
                                </a-tag>
                                <a-tag
                                  color="cyan"
                                  style="
                                    padding: 0 2px 2px 2px;
                                    font-size: 13px;
                                    margin-bottom: 3px;
                                  "
                                >
                                  {{ getTypeName(cgc.condition.config.leftValue) }}
                                </a-tag>
                                {{ getViewValue(cgc.condition.config.leftValue) }}
                                &nbsp;
                                <a-tag
                                  color="orange"
                                  style="
                                    padding: 0 2px 2px 2px;
                                    font-size: 13px;
                                    margin-bottom: 3px;
                                  "
                                >
                                  {{ getSymbolExplanation(cgc.condition.config.symbol) }}
                                </a-tag>
                                <!-- <a-tag
                                  color="cyan"
                                  style="
                                    padding: 0 2px 2px 2px;
                                    font-size: 13px;
                                    margin-bottom: 3px;
                                  "
                                >
                                  {{ getTypeName(cgc.condition.config.rightValue) }}
                                </a-tag> -->
                                {{ getViewValue(cgc.condition.config.rightValue) }}
                              </p>
                            </template>
                          </a-alert>
                        </template>
                      </draggable>

                      <br />

                      <a-button
                        type="dashed"
                        style="width: 50%; display: block; margin: 0 auto"
                        @click="addConditionHandler(cg)"
                      >
                        <PlusOutlined style="color: #777" />
                        添加条件
                      </a-button>
                    </a-card>
                  </template>
                </draggable>
              </a-spin>
              <a-button type="dashed" style="width: 100%" @click="addConditionGroup()">
                <PlusOutlined style="color: #777" />
                添加条件组
              </a-button>
            </a-card>
            <br />
            <br />

            <a-form ref="generalRuleForm" :model="generalRule">
              <a-card title="结果" size="small">
                <template v-slot:extra>
                  <span>
                    <a-popover title="温馨提示">
                      <template v-slot:content>
                        <p>普通规则结果类型确认后，及规则发布后，则不支持修改！</p>
                        <p>（具体原因可参考链接：T320523）</p>
                      </template>
                      <InfoCircleOutlined class="dynamic-delete-button" style="font-size: 18px" />
                    </a-popover>
                  </span>
                </template>
                <a-row>
                  <a-col :span="5">
                    <a-form-item
                      :name="['action', 'type']"
                      style="margin-bottom: 0"
                      :rules="{
                        required: true,
                        message: '请选择结果类型',
                        trigger: ['change', 'blur'],
                      }"
                    >
                      <a-select
                        style="width: 100%"
                        placeholder="请选择结果类型"
                        :value="valueType(generalRule.action)"
                        @change="actionValueTypeChange"
                      >
                        <!-- <a-select-option value="PARAMETER">参数</a-select-option>
                        <a-select-option value="VARIABLE">变量</a-select-option> -->
                        <!-- <a-select-option value="COLLECTION">集合</a-select-option> -->
                        <a-select-option value="BOOLEAN">布尔</a-select-option>
                        <a-select-option value="STRING">字符串</a-select-option>
                        <a-select-option value="NUMBER">数值</a-select-option>
                        <a-select-option value="DATE">日期</a-select-option>
                      </a-select>
                    </a-form-item>
                  </a-col>
                  <a-col :span="1"></a-col>
                  <a-col :span="18">
                    <a-form-item
                      :name="['action', 'value']"
                      style="margin-bottom: 0"
                      :rules="{
                        required: true,
                        message: '请输入结果值',
                        trigger: ['change', 'blur'],
                      }"
                    >
                      <a-select
                        :disabled="!generalRule.action.type"
                        v-if="generalRule.action.valueType === 'BOOLEAN'"
                        @blur="saveAction"
                        style="width: 100%"
                        v-model:value="generalRule.action.value"
                        placeholder="请选择数据"
                      >
                        <a-select-option value="true">true</a-select-option>
                        <a-select-option value="false">false</a-select-option>
                      </a-select>
                      <a-input-number
                        :disabled="!generalRule.action.type"
                        @blur="saveAction"
                        v-else-if="generalRule.action.valueType === 'NUMBER'"
                        v-model:value="generalRule.action.value"
                        style="width: 100%"
                      />
                      <a-date-picker
                        :disabled="!generalRule.action.type"
                        v-else-if="generalRule.action.valueType === 'DATE'"
                        show-time
                        @change="
                          (date: any, dateString: string) =>
                            datePickerChange(generalRule.action, date)
                        "
                        format="YYYY-MM-DD hh:mm:ss"
                        valueFormat="YYYY-MM-DD hh:mm:ss"
                        v-model:value="generalRule.action.value"
                        style="width: 100%"
                      />
                      <a-input
                        v-else
                        :disabled="!generalRule.action.type"
                        @blur="saveAction"
                        v-model:value="generalRule.action.value"
                      />
                    </a-form-item>
                  </a-col>
                </a-row>
              </a-card>

              <br />

              <a-card title="默认结果" size="small">
                <a-form-item>
                  <a-switch
                    :disabled="generalRule.action.valueType == null"
                    @change="enableDefaultActionChange"
                    :checked="generalRule.defaultAction.enableDefaultAction === 0"
                  />
                </a-form-item>
                <a-row>
                  <a-col :span="5">
                    <a-form-item
                      :name="['defaultAction', 'type']"
                      style="margin-bottom: 0"
                      :rules="{
                        required: generalRule.defaultAction.enableDefaultAction === 0,
                        message: '请选择默认结果类型',
                        trigger: ['change', 'blur'],
                      }"
                    >
                      <a-select
                        style="width: 100%"
                        :disabled="generalRule.action.valueType == null"
                        placeholder="请选择类型"
                        :value="valueType(generalRule.defaultAction)"
                        @change="defaultActionValueTypeChange"
                      >
                        <a-select-option value="BOOLEAN">布尔 </a-select-option>
                        <a-select-option value="STRING">字符串 </a-select-option>
                        <a-select-option value="NUMBER">数值 </a-select-option>
                        <a-select-option value="DATE">日期 </a-select-option>
                      </a-select>
                    </a-form-item>
                  </a-col>
                  <a-col :span="1"></a-col>
                  <a-col :span="18">
                    <a-form-item
                      :name="['defaultAction', 'value']"
                      style="margin-bottom: 0"
                      :rules="{
                        required: generalRule.defaultAction.enableDefaultAction === 0,
                        message: '请输入默认结果值',
                        trigger: ['change', 'blur'],
                      }"
                    >
                      <a-select
                        :disabled="!generalRule.defaultAction.type"
                        v-if="generalRule.defaultAction.valueType === 'BOOLEAN'"
                        @blur="saveDefaultAction"
                        style="width: 100%"
                        v-model:value="generalRule.defaultAction.value"
                        placeholder="请选择数据"
                      >
                        <a-select-option value="true">true</a-select-option>
                        <a-select-option value="false">false</a-select-option>
                      </a-select>
                      <a-input-number
                        @blur="saveDefaultAction"
                        :disabled="!generalRule.defaultAction.type"
                        v-else-if="generalRule.defaultAction.valueType === 'NUMBER'"
                        v-model:value="generalRule.defaultAction.value"
                        style="width: 100%"
                      />
                      <a-date-picker
                        :disabled="!generalRule.defaultAction.type"
                        v-else-if="generalRule.defaultAction.valueType === 'DATE'"
                        show-time
                        @change="
                          (date: any, dateString: any) =>
                            datePickerChange(generalRule.defaultAction, date)
                        "
                        format="YYYY-MM-DD hh:mm:ss"
                        valueFormat="YYYY-MM-DD hh:mm:ss"
                        v-model:value="generalRule.defaultAction.value"
                        style="width: 100%"
                      />
                      <a-input
                        v-else
                        @blur="saveDefaultAction"
                        :disabled="!generalRule.defaultAction.type"
                        v-model:value="generalRule.defaultAction.value"
                      />
                    </a-form-item>
                  </a-col>
                </a-row>
              </a-card>
            </a-form>
          </a-col>
          <a-col :span="1"></a-col>
        </a-row>
      </div>
      <br />
      <br />
      <br />
    </a-card>

    <ConditionModal ref="modalRefs" :dataType="dataType" />
    <publish ref="publishRefs"></publish>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch } from 'vue'
import { getSymbolExplanation } from '@/utils/symbol'
import moment from 'moment'
import ConditionModal from './components/condition-modal.vue'
import { getTypeName, valueType } from '@/utils/value-type'
import { createUuid } from '@/utils/tool'
import {
  DragOutlined,
  PlusOutlined,
  DeleteOutlined,
  InfoCircleOutlined,
} from '@ant-design/icons-vue'
import Draggable from 'vuedraggable'
import publish from './publish.vue'
import 'dayjs/locale/zh-cn' // 加载中文

// Props
const props = defineProps({
  id: {
    type: Number,
    required: false,
  },
  generalRuleData: {
    type: Object,
    required: false,
    default: () => {},
  },
})

// Refs
const modalRefs = ref<typeof ConditionModal>()
const publishRefs = ref<typeof publish>()
const generalRuleForm = ref<any>(null)

// Reactive state
const conditionMoveLoading = ref(false)
const menuVisible = ref(false)
const dataType = ref(0)

const ops = reactive({
  vuescroll: {},
  scrollPanel: {},
  rail: {
    keepShow: true,
  },
  bar: {
    hoverStyle: true,
    onlyShowBarOnScroll: false,
    background: '#F5F5F5',
    opacity: 0.5,
    'overflow-x': 'hidden',
  },
})

const generalRule = reactive<any>({
  id: null,
  name: null,
  code: null,
  currentVersion: null,
  description: null,
  ruleId: null,
  conditionGroup: [],
  action: {
    value: undefined,
    valueName: undefined,
    valueType: undefined,
    variableValue: undefined,
    type: undefined,
  },
  defaultAction: {
    enableDefaultAction: 1,
    value: undefined,
    valueName: undefined,
    valueType: undefined,
    variableValue: undefined,
    type: undefined,
  },
})

const actionSearchSelect = reactive({
  data: [],
  value: undefined,
})

watch(
  () => props.generalRuleData,
  (newVal) => {
    if (newVal && JSON.stringify(newVal) !== '{}') {
      Object.assign(generalRule, newVal)
    }
  },
  { deep: true, immediate: true },
)

const addConditionHandler = (cg: any) => {
  modalRefs.value?.addCondition(cg)
}

const editConditionHandler = (cg: any, cgc: any) => {
  modalRefs.value?.editCondition(cg, cgc)
}

// Methods
const defaultActionValueTypeChange = (valueType: any) => {
  generalRule.defaultAction = {
    enableDefaultAction: generalRule.defaultAction.enableDefaultAction,
    value: undefined,
    valueName: undefined,
    valueType: undefined,
    variableValue: undefined,
  }
  if (valueType === 'PARAMETER') {
    generalRule.defaultAction.type = 0
  } else if (valueType === 'VARIABLE') {
    generalRule.defaultAction.type = 1
  } else {
    generalRule.defaultAction.valueType = valueType
    generalRule.defaultAction.type = 2
  }
  actionSearchSelect.data = []
  actionSearchSelect.value = undefined
}

const enableDefaultActionChange = async () => {
  let defaultAction = generalRule.defaultAction
  if (defaultAction.enableDefaultAction === 0) {
    defaultAction.enableDefaultAction = 1
  } else {
    //   if (defaultAction.type == null) {
    //     await generalRuleForm.value.validate('action.type')
    //     // generalRuleForm.value.fields[2].validateMessage = '请选择默认结果类型'
    //     // generalRuleForm.value.fields[2].validateState = 'error'
    //     return
    //   }
    //   if (defaultAction.value == null) {
    //     // generalRuleForm.value.fields[3].validateMessage = '请输入默认结果值'
    //     // generalRuleForm.value.fields[3].validateState = 'error'
    //     return
    //   }
    defaultAction.enableDefaultAction = 0
  }
  // defaultActionSwitch({
  //   generalRuleId: generalRule.id,
  //   enableDefaultAction: defaultAction.enableDefaultAction,
  // }).then((res) => {
  //   if (res.data.data) {
  //     message.success(defaultAction.enableDefaultAction === 0 ? '默认结果已开启' : '默认结果已关闭')
  //   }
  // })
}

const onContextmenu = (e: { preventDefault: () => void }) => {
  if (e) {
    e.preventDefault()
    menuVisible.value = true
  }
}

const datePickerChange = (v: { value: string }, date: any) => {
  v.value = moment(date).format('YYYY-MM-DD HH:mm:ss')
}

const saveAction = () => {
  if (generalRule.action.type == null) {
    return
  }
  if (generalRule.action.value !== 0 && !generalRule.action.value) {
    return
  }
  if (!generalRule.action.valueType) {
    return
  }
  // saveAction({
  //   ruleId: generalRule.ruleId,
  //   configValue: generalRule.action,
  // }).then((res) => {
  //   if (res.data.data) {
  //     message.success('结果保存成功')
  //   }
  // })
}

const saveDefaultAction = () => {
  // if (generalRule.defaultAction.type == null) {
  //   return
  // }
  // if (!generalRule.defaultAction.value !== 0 && !generalRule.defaultAction.value) {
  //   return
  // }
  // if (!generalRule.defaultAction.valueType) {
  //   return
  // }
  // saveDefaultAction({
  //   generalRuleId: generalRule.id,
  //   configValue: generalRule.defaultAction,
  // }).then((res) => {
  //   if (res.data.data) {
  //     message.success('默认结果保存成功')
  //   }
  // })
}

const deleteCondition = (cgc: any, conditionGroupRefId: any, conditionId: any) => {
  // deleteCondition({
  //   conditionId: conditionId,
  //   conditionGroupRefId: conditionGroupRefId,
  // }).then((res) => {
  //   if (res.data.data) {
  cgc.forEach((value: { condition: { id: any } }, index: any) => {
    if (value.condition.id === conditionId) {
      cgc.splice(index, 1)
    }
  })
  //     message.success('删除条件成功')
  //   }
  // })
}

const getViewValue = (v: Record<string, any>) => {
  if (v.type === 2) {
    return v.value
  }
  if (v.variableValue) {
    return v.variableValue
  }
  if (v.valueName) {
    return v.valueName
  }
  return v.value
}

const actionValueTypeChange = (valueType: string) => {
  generalRule.action = {
    value: undefined,
    valueName: undefined,
    valueType: undefined,
    variableValue: undefined,
  }
  if (valueType === 'PARAMETER') {
    generalRule.action.type = 0
  } else if (valueType === 'VARIABLE') {
    generalRule.action.type = 1
  } else {
    generalRule.action.valueType = valueType
    generalRule.action.type = 2
  }
  actionSearchSelect.data = []
  actionSearchSelect.value = undefined
}

const preview = async () => {
  const val = await nextStep()
  if (val) {
    publishRefs.value?.show(val)
  }
}

const nextStep = () => {
  return new Promise<any>((resolve, reject) => {
    try {
      generalRuleForm.value
        .validate()
        .then(() => {
          resolve(generalRule)
        })
        .catch((error: any) => {
          reject(error)
        })
    } catch (error) {
      reject(error)
    }
  })
}

const addConditionGroup = () => {
  let newOrderNo = 1
  if (generalRule.conditionGroup != null) {
    let length = generalRule.conditionGroup.length
    let conditionGroupElement: Record<string, any> = generalRule.conditionGroup[length - 1]
    if (conditionGroupElement !== undefined) {
      newOrderNo = JSON.parse(JSON.stringify(conditionGroupElement.orderNo + 1))
    }
  } else {
    generalRule.conditionGroup = []
  }
  let newConditionGroup = {
    id: createUuid(),
    name: '条件组',
    ruleId: generalRule.ruleId,
    orderNo: newOrderNo,
    conditionGroupCondition: [],
  }
  // saveOrUpdate(newConditionGroup).then((res) => {
  //   if (res.data.data) {
  //     newConditionGroup.id = res.data.data
  generalRule.conditionGroup.push(newConditionGroup)
  //     message.success('添加条件组成功')
  //   }

  // })
}

const updateConditionGroupName = (cg: any) => {
  // saveOrUpdate({
  //   id: cg.id,
  //   ruleId: generalRule.ruleId,
  //   name: cg.name,
  // })
}

const deleteConditionGroup = (cg: any) => {
  const index = generalRule.conditionGroup.findIndex((value: { id: any }) => value.id === cg.id)
  if (index !== -1) generalRule.conditionGroup.splice(index, 1)
}

// Lifecycle hooks
onMounted(() => {
  generalRule.id = props.id
  // getRuleConfig()
})

defineExpose({
  nextStep,
})
</script>

<style lang="less">
.condition_set {
  .ant-alert-close-icon {
    margin-top: -5px;
  }
}
</style>

<style lang="less" scoped>
.openLeft {
  width: 30px;
  height: 80px;
  position: fixed;
  margin-left: -20px;
  z-index: 19;
}

.conditionItem {
}

.dynamic-delete-button {
  cursor: pointer;
  position: relative;
  top: 2px;
  font-size: 18px;
  color: #999;
  transition: all 0.3s;
}

.dynamic-delete-button:hover {
  color: #777;
}

.dynamic-delete-button[disabled] {
  cursor: not-allowed;
  opacity: 0.5;
}

.dynamic-delete-button:hover {
  color: #777;
}

.dynamic-delete-button[disabled] {
  cursor: not-allowed;
  opacity: 0.5;
}

// 滚动条位置
/deep/ .__bar-is-vertical {
  right: -1px !important;
}

// 隐藏横向滚动条
/deep/ .__bar-is-horizontal {
  display: none !important;
}
</style>
