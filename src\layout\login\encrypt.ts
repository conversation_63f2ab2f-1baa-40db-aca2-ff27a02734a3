import { JSEncrypt } from 'jsencrypt'

/**
 * 数据加密
 */
export function encryptData(data: string) {
  const encrypt = new JSEncrypt({})
  encrypt.setPublicKey(
    '-----BEGIN PUBLIC KEY-----\\nMIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDg3/t1twW8BvmdWXMvDyaKot2L eb5BX75mBi6cxkxBDUx5SCnARdWPUkdqpBIBMD6xgkwA3LuWFGWHQl4nou+p2no4 dC0wAJB3w9sznvaah3Lf9z3iB9+zis8mT4N57aK7zHBQ4J725MJDtLrepi8MSa7f poDEB5BZpeXSw6a4FQIDAQAB\\n-----END PUBLIC KEY-----'.replace(
      /\\n/g,
      '\n',
    ),
  )
  return encrypt.encrypt(data)
}
