<template>
  <div class="sign-in">
    <div class="section-login">
      <div class="part-left"></div>
      <div class="part-right">
        <h1 class="title">欢迎登录！</h1>
        <h1 class="title">大数据开发管理平台</h1>
        <a-form
          ref="formRef"
          :model="formState"
          :rules="rules"
          hideRequiredMark
          class="login-form"
          id="components-form-demo-normal-login"
        >
          <a-form-item name="username">
            <a-input size="large" v-model:value="formState.username" placeholder="请输入登录账号">
              <template #prefix>
                <a-icon :component="UserOutlined" />
              </template>
            </a-input>
          </a-form-item>
          <a-form-item name="password">
            <a-input-password
              size="large"
              v-model:value="formState.password"
              placeholder="请输入密码"
            >
              <template #prefix>
                <a-icon :component="LockOutlined" />
              </template>
            </a-input-password>
          </a-form-item>
          <a-form-item>
            <a-button
              block
              size="large"
              :disabled="loading"
              :loading="loading"
              class="login-form-button"
              html-type="submit"
              type="primary"
              @click="onSubmit"
              >登录</a-button
            >
          </a-form-item>
        </a-form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { message } from 'ant-design-vue'
import { useAppStore } from '@/stores/app'
import { useUserStore } from '@/stores/user'
import { getUserInfo } from '@/api/services/permission'
import { getUserMenuFn } from '@/utils/user'
import { UserOutlined, LockOutlined } from '@ant-design/icons-vue'
import { accountLogin } from '@/api/services/permission'

const appStore = useAppStore()
const userStore = useUserStore()

const formRef = ref()
const formState = reactive({
  username: '',
  password: '',
})
const loading = ref(false)

const rules = {
  username: [{ required: true, message: '请输入登录账号' }],
  password: [{ required: true, message: '请输入密码' }],
}

async function onSubmit() {
  await formRef.value.validate()
  loading.value = true
  try {
    const { data } = await accountLogin({
      loginAccount: formState.username,
      password: btoa(formState.password),
    })
    userStore.setToken(data.userToken)
    await getUserInfoFn(data.userToken)
    await getUserMenuFn()
  } catch (e: any) {
    message.error(e?.msg || '登录失败')
  } finally {
    loading.value = false
  }
}

async function getUserInfoFn(token: string) {
  try {
    appStore.appLoading = true
    const { data } = await getUserInfo({ userToken: token })
    userStore.userInfo = data
  } catch (error) {
    console.log(error)
  } finally {
    appStore.appLoading = false
  }
}
</script>

<style lang="less" scoped>
.sign-in {
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background: url('@/assets/login/login-bg.png');
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
  margin-top: -40px;
  .section-login {
    width: 850px;
    height: 470px;
    border-radius: 8px;
    background-color: #ffffff80;
    display: flex;
    overflow: hidden;
    background: url('@/assets/login/login-border-back.png') no-repeat center center / cover;
    .part-left {
      flex: 1;
    }
    .part-right {
      flex: 1;
      background: #fff;
      padding-top: 50px;
      box-sizing: border-box;
      .title {
        padding-left: 50px;
        font-size: 22px;
        font-weight: 700;
        margin-bottom: 18px;
      }
      .title:nth-of-type(2) {
        margin-bottom: 50px;
      }
    }
  }
  #components-form-demo-normal-login {
    padding: 0 50px;
    position: relative;
    :deep(.ant-input-affix-wrapper) {
      border-color: transparent;
      border-bottom: 1px solid #f2f3f5;
    }
    :deep(.ant-input-affix-wrapper:focus) {
      box-shadow: 0px 0px 0px 0px !important;
    }
    :deep(.ant-input-affix-wrapper-focused) {
      box-shadow: 0px 0px 0px 0px !important;
    }
  }
  .login-form-button {
    width: 80%;
    height: 55px;
    border-radius: 50px;
    margin-left: 20px;
    margin-top: 30px;
  }
}
</style>
