import type { ServerResponse } from '../type'
/**
 * 表格列类型
 * @enum {string}
 */
export enum ColumnType {
  TEXT = 'TEXT',
  EMAIL = 'EMAIL',
  NUMBER = 'NUMBER',
  DATE = 'DATE',
  BOOLEAN = 'BOOLEAN',
}

export interface IColumn {
  des?: string
  field: string
  zone?: string | null
  dataIndex: string
  earliestTime?: string | null
  endTime?: string | null
  title: string
  type: ColumnType
  key: string
  isShow?: boolean
  description?: string
  comment?: string
}

export interface CardData {
  /** 基础卡项ID - 关联基础卡项模板 (格式示例："674ff39578ef1f2a882660e8") */
  base_card_id: string

  /** 消费类型 - 数值型标识 (通常 1 表示正常消费类型) */
  expend_type: number

  /** 消费次数限制 - 数值型限制次数 (示例值：1) */
  expend_nume: number

  /** 最后更新时间 - 格式："YYYY-MM-DD HH:mm:ss" 或 null */
  update_time: string | null

  /** 最后更新人 - 用户ID或用户名 (示例值："q") */
  update_user: string | null

  /** 分类ID - 关联卡项分类 (格式示例："676d12f810b058b2b4129522") */
  category_id: string

  /** 卡面图片URL - 图片地址或空字符串/null */
  card_front_url: string | null

  /** 微信卡券功能 - 布尔型数值标识 (0: 未开通, 1: 已开通) */
  wechat_card: number

  /** 销售数量 - 数值型统计值 */
  sales_num: number

  /** 卡有效期类型 - 数值型 (1: 固定天数, 2: 固定日期, 3: 永久有效) */
  card_validity_type: number

  /** 用户协议签署状态 - 布尔型数值标识 (0: 未签署, 1: 已签署) */
  card_agreement: number

  /** 所属门店ID - 格式示例："S001" 或门店唯一标识 */
  store_id: string

  /** 分享文案 - 文本内容（可能用于社交分享） */
  share_text: string

  /** 赠送金额 - 数值型金额或 null */
  give_price: number | null

  /** 卡门店类型 - 数值型 (1: 指定门店, 2: 全部门店) */
  card_strore_type: number // 注意字段名疑似拼写错误 (strore -> store)

  /** 创建时间 - 格式："YYYY-MM-DD HH:mm:ss" 或 null */
  create_time: string | null

  /** 卡版本号 - 版本标识 (示例值："1") */
  card_version: string | null

  /** 前端展示类型 - 数值型界面配置标识 */
  card_show_type: number

  /** 是否展示 - 布尔型数值标识 (0: 隐藏, 1: 显示) */
  card_show: number

  /** 描述文本 - 卡项详细说明 */
  description_text: string

  /** 卡类型 - 数值型分类 (示例：1: 储值卡, 4: 折扣卡, 6: 特殊类型) */
  card_type: number

  /** 卡号掩码显示 - 布尔型数值标识 (0: 不掩码, 1: 掩码显示) */
  card_show_masking: number

  /** 卡项唯一ID - 主键 (格式示例："674ff39578ef1f2a882660e8") */
  card_id: string

  /** 有效天数 - 当card_validity_type=1时生效 */
  card_validity_day: number

  /** 有效期时间 - ISO 8601格式或特殊值 (示例："2024-12-19T06:15:34.968Z") */
  card_validity_time: string

  /** 卡状态 - 数值型 (1: 启用, 2: 停用) */
  card_status: number

  /** 卡面类型 - 数值型设计模板标识 */
  card_front_type: number

  /** 线上商城类型 - 数值型 (1: 自营商城, 2: 第三方商城) */
  online_shop_type: number

  /** 排序序号 - 数值型排序权重 */
  sort_num: number

  /** 卡项名称 - 显示名称 (示例："卡项名称1") */
  card_name: string

  /** 市场价格 - 数值型标价或 null */
  market_price: number | null

  /** 充值价格 - 数值型实际价格或 null */
  recharge_price: number | null

  /** 显示持卡人姓名 - 布尔型数值标识 (0: 不显示, 1: 显示) */
  card_show_name: number

  /** 数据同步状态 - 数值型 (0: 未同步, 1: 已同步) */
  sync_status: number

  /** 创建人 - 用户ID或用户名 */
  create_user: string | null

  /** 消费赠送规则 - 数值型赠送策略标识 */
  expend_give: number

  /** 领取ID - 特殊业务标识（需确认具体作用） */
  take_id: string
}
export type PreviewData = {
  cost: number
  dataList: CardData[]
  titleList: IColumn[]
}
export type PreviewResponse = ServerResponse<PreviewData>
export type BasicRule = {
  basicDataId: string
  basicRuleId: string
  createTime: string
  ruleScript: string
  ruleText: string
  ruleType: number
  databaseName: string
}
/**
 * 指标列表项
 */
export type IndexListItem = {
  autoId: string
  basicRuleList: BasicRule[]
  calFormula: string
  createTime: string
  createUser: string
  createUserName: string
  currentVersion: number
  dataSourceId: string
  dynamicParam: string
  dynamic?: number
  formDataId: string
  groupId: string
  indexName: string
  isDelete: number
  isRelease: number
  parentId: string
  remarks: string
  revision: number
  status: number
  tenantId: string
  updateTime: string
  updateUser: string
  updateUserName: string
}
/**
 * 指标列表响应
 */
export type IndexListResponse = ServerResponse<{
  pageIndex: number
  pageSize: number
  totalPages: number
  totalRecords: number
  records: IndexListItem[]
}>

export type WhereItem = {
  tableName: string
  fieldName: string
  operator?: string
  minValue?: string | number
  maxValue?: string | number
  value?: any
  fieldType?: ColumnType
}

export type ServerFieldItem = {
  columnType: ColumnType
  columnTypeName: string
  comment: string
  desc: string
  disable: number
  key: string
  tableId: string
  title: string
  tableName: string
}
