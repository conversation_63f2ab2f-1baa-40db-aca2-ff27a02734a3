<template>
  <Container style="height: auto" id="container">
    <draggerBanner :layout="[7, 3]" :showRight="isShowSqlEditor" :isCustomMode="isCustomMode">
      <template #left>
        <div
          class="left-wrap"
          :style="{
            borderRight: isShowSqlEditor ? 'none' : '1px solid #ccc',
            borderRadius: isShowSqlEditor ? '8px 0 0 8px' : '8px',
          }"
        >
          <a-collapse v-model:activeKey="activeKey" ghost>
            <a-collapse-panel key="1" header="指标属性配置">
              <a-form>
                <a-form-item label="指标名称" name="indexName">
                  <a-input v-model:value="paramsState.indexName" placeholder="请输入指标名称" />
                </a-form-item>
                <a-form-item label="选择分组" name="groupId">
                  <a-button type="primary" @click="handleCheckGroup">
                    {{ paramsState?.groupName || '选择分组' }}
                  </a-button>
                </a-form-item>
              </a-form>
              <form-create
                ref="fcRef"
                v-if="fcRule.length"
                v-model="formData"
                v-model:api="fApi"
                :rule="fcRule"
                :option="fcOption"
              ></form-create>
              <a-space direction="vertical" :size="16" style="width: 100%">
                <a-space>
                  <span>动态参数</span>
                  <a-switch v-model:checked="needDynamicParam" />
                </a-space>
                <DynamicParam
                  v-show="needDynamicParam"
                  v-model:value="dynamicParam"
                  class="ms-64px"
                />
              </a-space>
            </a-collapse-panel>
            <a-collapse-panel key="2" header="查询编辑器">
              <div style="margin-bottom: 16px; display: flex; align-items: center; gap: 8px">
                <span>自定义SQL：</span>
                <a-switch v-model:checked="isCustomMode" @change="handleCustomModeChange" />
              </div>

              <!-- 自定义SQL编辑器 -->
              <div v-if="isCustomMode" class="custom-sql-editor">
                <draggerBanner
                  :layout="[3, 7]"
                  :minLeftWidth="250"
                  :minRightWidth="400"
                  :showRight="true"
                  :isCustomMode="false"
                >
                  <template #left>
                    <div class="sql-editor-left">
                      <TreeMenu
                        ref="treeMenuRef"
                        @select="handleTreeSelect"
                        @tableDoubleClick="handleTableDoubleClick"
                        @fieldDoubleClick="handleFieldDoubleClick"
                        @treeLoaded="handleTreeMenuLoaded"
                      />
                    </div>
                  </template>

                  <template #right>
                    <div class="sql-editor-right">
                      <SqlAceEditor
                        ref="customSqlEditorRef"
                        v-model="customSqlContent"
                        :height="'100%'"
                        :table-fields="tableFields"
                        :selected-table-info="selectedTableInfo"
                        :available-databases="availableDatabases"
                        :available-tables="availableTables"
                        :dynamic-params="dynamicParam"
                        :need-dynamic-param="needDynamicParam"
                        @init="handleCustomSqlEditorInit"
                        @change="handleSqlContentChange"
                      />
                    </div>
                  </template>
                </draggerBanner>
              </div>

              <!-- 可视化查询构建器 -->
              <div v-else class="editor-box">
                <div class="editor-box-item" v-for="(item, index) in editDataList" :key="index">
                  <div class="editor-item" v-if="item.type === 'dataSource'">
                    <div class="editor-item-rule">
                      <div class="title">数据</div>
                      <div class="emotion-box">
                        <!-- <dataSource
                          :dataSourceId="paramsState.dataSourceId"
                          :databaseName="item.value.databaseName"
                          :selectData="item.value.fields"
                          :table-name="item.value.tableName"
                          :table-id="item.value.tableId"
                          :callback-fn="(e: any) => handleClickDataSource(e, item)"
                        /> -->
                        <a-button type="primary" @click="selectInitialData">{{
                          item.value.tableName || '选择初始数据'
                        }}</a-button>
                      </div>
                    </div>
                    <div class="editor-action">
                      <div class="play" @click="handleDataPreview(item, index)">
                        <PlayCircleOutlined />
                      </div>
                    </div>
                  </div>
                  <div class="editor-item" v-else>
                    <div class="editor-item-rule">
                      <div class="close" @click="handleClose(index)">
                        <CloseOutlined class="close-btn" />
                      </div>
                      <joinData
                        v-if="item.type === 'joins'"
                        ref="joinDataRef"
                        v-model="item.value"
                        :leftTableId="editDataList[0].value.tableId"
                        :dataSourceId="paramsState.dataSourceId"
                        :leftDatabaseName="editDataList[0].value.databaseName"
                        :leftTableName="editDataList[0].value.tableName"
                      />
                      <customColumn
                        v-else-if="item.type === 'customField'"
                        v-model="item.value"
                        :dataList="editDataList"
                        :columns="dbColumns"
                        @update:model-value="item.preview = false"
                      />
                      <customWhere
                        v-else-if="item.type === 'where'"
                        v-model="item.value"
                        :tableName="editDataList[0].value.tableName"
                        :columns="dbColumns"
                        @update:model-value="handleChange(item)"
                      ></customWhere>
                      <customGroup
                        v-else-if="item.type === 'group'"
                        v-model="item.value"
                        :tableName="editDataList[0].value.tableName"
                        :columns="dbColumns"
                        @update:model-value="handleChange(item)"
                      ></customGroup>
                      <customSort
                        v-else-if="item.type === 'order'"
                        v-model="item.value"
                        :tableName="editDataList[0].value.tableName"
                        :columns="sortsColumns"
                        @update:model-value="item.preview = false"
                      ></customSort>
                      <customLine
                        v-else-if="item.type === 'limitValue'"
                        v-model="item.value"
                        @update:model-value="item.preview = false"
                      ></customLine>
                    </div>
                    <div class="editor-action">
                      <div
                        class="play"
                        v-if="![null, undefined].includes(item.value) && !item.preview"
                        @click="handleDataPreview(item, index)"
                      >
                        <PlayCircleOutlined />
                      </div>
                    </div>
                  </div>
                  <div class="editor-prevew" v-if="item.preview">
                    <div class="editor-prevew-header">
                      <div class="editor-prevew-header-title">预览</div>
                      <CloseOutlined @click="item.preview = false" />
                    </div>
                    <div class="editor-prevew-content">
                      <tableComponent
                        :loading="item.previewLoading"
                        :dataSource="item.previewData"
                        :columns="item.previewColumns"
                      ></tableComponent>
                    </div>
                  </div>
                  <!-- <editor :indicator="item" v-else></editor> -->
                  <action-buttons
                    @add="handleAddData($event)"
                    @update="handleAddData($event, index)"
                    :dataList="editDataList"
                    :index="index"
                  ></action-buttons>
                </div>
              </div>
            </a-collapse-panel>
          </a-collapse>
          <div style="padding: 10px 10px 40px 0">
            <a-button
              type="primary"
              class="mr-5"
              :disabled="shouldDisabled"
              :loading="previewLoading"
              @click="handleAllDataPreview"
              >预览</a-button
            >
            <a-button
              v-if="!fromSearch"
              type="primary"
              class="mr-5"
              :disabled="shouldDisabled"
              @click="handleSave"
              >保存</a-button
            >
            <a-button
              v-if="!isCustomMode"
              type="primary"
              :disabled="shouldDisabled"
              :loading="viewSqlLoading"
              @click="clickSqlButton"
              >{{ isShowSqlEditor ? '关闭' : '查看' }}SQL</a-button
            >
          </div>
          <div class="previewAll" v-if="isShowPreviewTable">
            <div class="editor-prevew-header">
              <h5>预览</h5>
              <CloseOutlined @click="closePrewviewTable" />
            </div>
            <tableComponent
              :dataSource="allPreviewData"
              :columns="allPreviewColumns"
            ></tableComponent>
          </div>
        </div>
      </template>
      <template #right>
        <div class="right-wrap">
          <CloseOutlined class="close-btn" @click="clickSqlButton" />
          <SqlAceEditor
            ref="sqlPreviewEditorRef"
            v-model="content"
            :height="'100%'"
            :readonly="true"
            :enable-auto-completion="false"
            :available-databases="availableDatabases"
            :available-tables="availableTables"
            @init="handleSqlPreviewEditorInit"
          />
        </div>
      </template>
    </draggerBanner>
    <packetModal ref="packetModalRef" :handleClickGroup="handleClickGroup" />
    <SelectDataTableField ref="selectDataTableFieldRef" @confirmData="handleConfirmData" />
  </Container>
</template>
<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed, watch } from 'vue'
import { message } from 'ant-design-vue'
import { CloseOutlined, PlayCircleOutlined } from '@ant-design/icons-vue'
import { useRoute } from 'vue-router'
import {
  getDataReview,
  getGenerateSql,
  getDataAssetColumnList,
} from '@/api/indexmanage/datasourceoptions/datasourceoptions'
import { getSingleIndexFormData } from '@/api/indexmanage/indexform/indexform'
import actionButtons from './component/action-buttons.vue'
import dataSource from './component/data-source.vue'
import joinData from './component/join-data.vue'
import customColumn from './component/custom-column.vue'
import customWhere from './component/custom-where.vue'
import customGroup from './component/custom-group.vue'
import customSort from './component/custom-sort.vue'
import customLine from './component/custom-line.vue'
import packetModal from './component/packet-modal.vue'
import tableComponent from '../table-component/table.vue'
import FcDesigner from '@form-create/designer'
import {
  addNewIndexData,
  getIndexDataDtl,
  modifyIndexData,
} from '@/api/indexmanage/indexmanage/indexmanage'
import { Container } from '@fs/fs-components'
import router from '@/router/core'
import { tranlateDataToParams, translateParamsToData } from './indicator-config'
import draggerBanner from './component/dragger-banner.vue'
import SqlAceEditor from './component/sql-ace-editor.vue'
import {
  DynamicParamKey,
  JoinTableColumnsKey,
  NeedDynamicParamKey,
  type JoinTableColumns,
} from './provide-keys'
import type { ServerFieldItem } from '@/api/services/indicator/type'
import { fsOptionsDefault } from './const'
import TreeMenu from './tree-menu.vue'
import SelectDataTableField from './component/select-data-table-field.vue'
import type { ParamItem } from './component/dynamic-param.vue'
import DynamicParam, { ParamCategory } from './component/dynamic-param.vue'
import type { DatabaseInfo, TableInfo } from './types'

// 编辑器相关状态
const content = ref('') // SQL预览内容
const isCustomMode = ref(false) // 是否自定义模式
const customSqlContent = ref('') // 自定义SQL内容
const dynamicParam = ref<ParamItem[]>([])
const needDynamicParam = ref(false)
const selectDataTableFieldRef = ref<any>(null)

// 当前选中的节点信息（统一管理所有节点类型的信息）
const selectedTableInfo = ref<{
  tableId: string
  tableName: string
  databaseName: string
  datasourceId: string
  nodeType: number // 节点类型：1=目录，2=数据库，3=表，4=字段
} | null>(null)

// 处理SQL内容变化
const handleSqlContentChange = (value: string) => {
  // console.log('SQL内容变化:', value)
}

// 存储表字段信息用于智能提示
const tableFields = ref<
  Array<{
    fieldName: string
    fieldType: string
    fieldTypeName: string
    fieldComment: string
    tableName: string
    databaseName: string
  }>
>([])

// 存储所有可用数据库信息用于智能提示
const availableDatabases = ref<
  Array<{
    databaseName: string
    datasourceId: string
  }>
>([])

// 存储所有可用表信息用于智能提示
const availableTables = ref<
  Array<{
    tableName: string
    databaseName: string
    tableId: string
    comment?: string
  }>
>([])

// 更新数据库和表的智能提示信息
const updateDatabaseAndTableCompletions = () => {
  if (treeMenuRef.value) {
    // 获取所有数据库
    if (treeMenuRef.value.getAllDatabases) {
      availableDatabases.value = treeMenuRef.value.getAllDatabases()
      console.log('更新数据库智能提示:', availableDatabases.value)
    }

    // 获取所有表
    if (treeMenuRef.value.getAllTables) {
      availableTables.value = treeMenuRef.value.getAllTables()
      console.log('更新表智能提示:', availableTables.value)
    }

    // 通知编辑器组件刷新智能提示
    if (customSqlEditorRef.value && customSqlEditorRef.value.refreshCompletions) {
      customSqlEditorRef.value.refreshCompletions()
    }
  }
}

// 更新字段智能提示
const updateFieldCompletions = (fields: any[], tableName: string, databaseName: string) => {
  // 将新字段添加到智能提示列表中
  const newFields = fields.map((field) => ({
    fieldName: field.fieldName || field.title,
    fieldType: field.fieldType || field.columnType,
    fieldTypeName: field.fieldTypeName || field.columnTypeName,
    fieldComment: field.fieldComment || field.comment,
    tableName: tableName,
    databaseName: databaseName,
  }))

  // 避免重复添加相同表的字段
  tableFields.value = tableFields.value.filter(
    (existing) => !(existing.tableName === tableName && existing.databaseName === databaseName),
  )

  tableFields.value.push(...newFields)

  console.log('更新智能提示字段:', tableFields.value)

  // 通知编辑器组件刷新智能提示
  if (customSqlEditorRef.value && customSqlEditorRef.value.refreshCompletions) {
    customSqlEditorRef.value.refreshCompletions()
  }
}

const containerRef = ref<HTMLElement | null>(null)
const viewSqlLoading = ref(false)
const isShowSqlEditor = ref(false)
const fcRef = ref()
const fcRule = ref([])
const fcOption = ref({})
const formData = ref<Record<string, any>>({})
const fApi = ref(null)
const dbColumns = ref([])
const dbColumns2 = ref<ServerFieldItem[]>([])
const route = useRoute()
const autoId = ref(route.query.id || '')
const formId = ref(route.query.formId || '') // 表单id
const businessType = ref(route.query.businessType || 1)
const editDataList = ref<any[]>([])
const allPreviewData = ref<any[]>([])
const allPreviewColumns = ref<any[]>([])

const isShowPreviewTable = ref(false)

const joinDataRef: any = ref(null)
// const formState: any = ref({
//   groupName: '',
// })
const packetModalRef: any = ref(null)
// const packet = ref([])
//  新增编辑字段
const paramsState: any = ref({
  formHistoryId: formId.value,
  indexName: '',
  groupId: '',
  groupName: '',
  parentId: '',
  calFormula: '',
  dataSourceId: '',
  formData: '',
  //  ruleText：取ruleTextObj字符串
  basicRuleList: [
    // {
    //   ruleType: '1',
    //   ruleText: '',
    //   ruleScript: '',
    // },
  ],
  remarks: '',
})
const activeKey = ref(['1'])
const previewLoading = ref(false)
const fromSearch = route.query.search ? true : false
// 移除重复变量，统一使用 selectedTableInfo

function closePrewviewTable() {
  isShowPreviewTable.value = false
  allPreviewData.value = []
  allPreviewColumns.value = []
}

//  ruleText字段

const clickSqlButton = async () => {
  if (!isShowSqlEditor.value) {
    viewSqlLoading.value = true

    const res = await getGenerateSql({
      dataSourceId: paramsState.value.dataSourceId,
      queryJson: JSON.stringify(tranlateDataToParams(editDataList.value)),
    }).finally(() => {
      viewSqlLoading.value = false
    })
    isShowSqlEditor.value = !isShowSqlEditor.value
    content.value = res?.data?.generateSql

    containerRef.value?.scrollTo({ top: 0, behavior: 'smooth' })
  } else {
    isShowSqlEditor.value = false
  }
}

const sortsColumns = computed(() => {
  const groups = editDataList.value.filter((item: any) => item.type === 'group')[0]
  const sorts = editDataList.value.filter((item: any) => item.type === 'order')
  if (!groups) return dbColumns.value
  const list: any[] = groups.value.group.filter(
    (item: any) => !sorts.find((it: any) => it.key === item.key),
  )
  return list.map((it) => ({
    ...it,
    sort: 'desc',
  }))
})

const shouldDisabled = computed(() => {
  if (isCustomMode.value) {
    return false
  }
  const disabledFlag = editDataList.value.some((item) => {
    return item.type === 'dataSource' && !item.value.fields.length
  })
  return !editDataList.value.length || disabledFlag
})

function handleClose(index: number) {
  editDataList.value = editDataList.value.filter((item, i) => i !== index)
}

function handleAddData(data: any, index?: number) {
  const indicator: any = {
    type: data.key,
    value: null,
    preview: false,
  }
  if (index === undefined) {
    editDataList.value.push(indicator)
  } else {
    editDataList.value.splice(index + 1, 0, indicator)
  }
}

function handleChange(item: any) {
  item.preview = false
  item.sourceData = item.value
}

function handleAllDataPreview() {
  previewLoading.value = true
  allPreviewData.value = []
  isShowPreviewTable.value = true

  // 如果是自定义SQL模式
  if (isCustomMode.value) {
    if (!customSqlContent.value.trim()) {
      message.warning('请输入SQL语句')
      previewLoading.value = false
      isShowPreviewTable.value = false
      return
    }

    if (!selectedTableInfo.value || !selectedTableInfo.value.databaseName) {
      message.warning('请先选择数据库或数据表')
      previewLoading.value = false
      isShowPreviewTable.value = false
      return
    }

    // 确保有数据源ID
    if (!selectedTableInfo.value.datasourceId && !paramsState.value.dataSourceId) {
      message.warning('请先选择数据源')
      previewLoading.value = false
      isShowPreviewTable.value = false
      return
    }

    // 从SQL语句中提取SELECT字段
    const extractedFields = extractFieldsFromSQL(customSqlContent.value)

    // 构造自定义SQL查询参数 - 符合标准格式
    const customSqlParams = [
      {
        databaseName: selectedTableInfo.value.databaseName,
        tableId: selectedTableInfo.value.tableId,
        tableName: selectedTableInfo.value.tableName,
        fields: extractedFields, // 从SQL中提取的字段
        dbColumns: tableFields.value
          .filter(
            (field) =>
              selectedTableInfo.value &&
              field.tableName === selectedTableInfo.value.tableName &&
              field.databaseName === selectedTableInfo.value.databaseName,
          )
          .map((field) => ({
            columnType: field.fieldType,
            disable: 1,
            tableId: selectedTableInfo.value!.tableId,
            comment: field.fieldComment,
            columnTypeName: field.fieldTypeName,
            title: field.fieldName,
            key: `${selectedTableInfo.value!.tableId}_${field.fieldName}`,
            desc: field.fieldType,
          })),
      },
    ]

    console.log('自定义SQL预览参数:', {
      dataSourceId: selectedTableInfo.value.datasourceId,
      queryJson: JSON.stringify(customSqlParams),
      previewType: 2,
      customSqlParams: customSqlParams,
      previewSql: customSqlContent.value, // 自定义SQL字段
    })

    handlePreview({
      basicRuleList: customSqlParams,
      previewType: 2,
      previewSql: customSqlContent.value,
      databaseName: selectedTableInfo.value.databaseName,
      datasourceId: selectedTableInfo.value.datasourceId,
    }) // 传递previewType=2表示自定义SQL模式
      .then(({ data }) => {
        allPreviewData.value = (data as any).dataList || []
        const titleList = (data as any).titleList || []
        allPreviewColumns.value = titleList.map((item: any) => {
          return {
            ...item,
            title: `${item.title} ${item.comment || ''}`,
            dataIndex: item.dataIndex || item.title,
            key: item.key || item.title,
          }
        })
      })
      .catch((error: any) => {
        console.error('自定义SQL预览失败:', error)
        message.error(error.message || '预览失败')
      })
      .finally(() => {
        previewLoading.value = false
      })
  } else {
    // 原有的可视化查询构建器预览逻辑
    const params = tranlateDataToParams(editDataList.value)
    handlePreview({ basicRuleList: params, previewType: 1 }) // 传递previewType=1表示正常模式
      .then(({ data }) => {
        allPreviewData.value = (data as any).dataList || []
        const titleList = (data as any).titleList || []
        allPreviewColumns.value = titleList.map((item: any) => {
          return { ...item, title: `${item.title} ${item.comment}` }
        })
      })
      .finally(() => {
        previewLoading.value = false
      })
  }
}

function handleDataPreview(item: any, index: number) {
  item.preview = true
  previewLoading.value = true
  const list = editDataList.value.slice(0, index + 1)
  const params = tranlateDataToParams(list)
  handlePreview({ basicRuleList: params, previewType: 1 }) // 单个组件预览也是正常模式，传递previewType=1
    .then(({ data }) => {
      item.previewData = (data as any).dataList || []
      item.previewColumns = (data as any).titleList || []
    })
    .finally(() => {
      previewLoading.value = false
    })
}

/**
 * 预览数据
 * @param basicRuleList 查询条件
 * @param previewType 预览类型：1=正常模式，2=自定义SQL模式
 * @param previewSql 自定义SQL字段，自定义模式下使用
 * @param databaseName 数据库名称，自定义模式下使用
 */
const handlePreview = async (obj: {
  basicRuleList: any
  previewType: number
  previewSql?: string
  databaseName?: string
  datasourceId?: string | null
}) => {
  let params: any = {
    dataSourceId: obj.datasourceId || paramsState.value.dataSourceId,
    previewType: obj.previewType, // 新增previewType参数：1=正常模式，2=自定义SQL模式
  }
  if (obj.previewSql) {
    params.previewSql = obj.previewSql
  }
  if (obj.databaseName) {
    params.databaseName = obj.databaseName
  }
  if (obj.previewType === 1) {
    params.queryJson = JSON.stringify(obj.basicRuleList)
  }
  if (needDynamicParam.value) {
    params.dynamicParam = JSON.stringify(dynamicParam.value)
  }
  return await getDataReview(params)
}

const selectInitialData = () => {
  // 检查是否有现有的数据需要回显
  let echoData = null

  if (editDataList.value.length > 0 && editDataList.value[0].type === 'dataSource') {
    const dataSourceItem = editDataList.value[0].value

    // 构造回显数据
    echoData = {
      tableId: dataSourceItem.tableId || paramsState.value.tableId,
      tableName: dataSourceItem.tableName || paramsState.value.tableName,
      databaseName: dataSourceItem.databaseName || paramsState.value.databaseName,
      checkedColumns: dataSourceItem.fields?.map((field: any) => field.fieldName) || [],
      dbColumns: dataSourceItem.dbColumns || dbColumns.value,
    }

    console.log('🚀 ~ 准备回显的数据:', echoData)
  }

  selectDataTableFieldRef.value.showModal(echoData)
}

const handleConfirmData = (data: any) => {
  console.log('[ handleConfirmData ] >', data)

  // 更新全局数据库列信息
  dbColumns.value = data.dbColumns

  // 更新参数状态 - 添加缺失的databaseName
  paramsState.value.dataSourceId = data.dataSourceId
  paramsState.value.tableId = data.tableId
  paramsState.value.tableName = data.tableName
  paramsState.value.databaseName = data.databaseName // 添加数据库名称

  // 保存当前数据库下的所有表数据
  if (data.allTablesInCurrentDatabase) {
    availableTablesInCurrentDatabase.value = data.allTablesInCurrentDatabase
    console.log('保存当前数据库下的所有表数据:', availableTablesInCurrentDatabase.value)
  }

  // 保存当前激活的数据库信息
  if (data.activeDataBase) {
    currentActiveDatabase.value = {
      databaseId: data.activeDataBase.databaseId,
      databaseName: data.activeDataBase.databaseName,
      databaseType: data.activeDataBase.databaseType,
      dataSourceId: data.activeDataBase.datasourceId,
      dataSourceName: data.activeDataBase.datasourceName,
      title: data.activeDataBase.title,
    }
    console.log('保存当前激活的数据库信息:', currentActiveDatabase.value)
  }

  // 更新编辑数据列表 - 确保第一个元素是数据源
  if (editDataList.value.length > 0 && editDataList.value[0].type === 'dataSource') {
    // 更新现有的数据源项
    editDataList.value[0] = data.editDataItem
  } else {
    // 插入新的数据源项到开头
    editDataList.value.unshift(data.editDataItem)
  }

  console.log('更新后的editDataList:', editDataList.value)
  console.log('更新后的paramsState:', paramsState.value)
}
const handleClickDataSource = (it: any, data: any) => {
  console.log('[ handleClickDataSource ] >', it)
  dbColumns.value = it.dbColumns
  paramsState.value.dataSourceId = it.activeDataBase.datasourceId
  paramsState.value.tableId = it.id
  paramsState.value.tableName = it.tableName

  data.value.tableId = it.tableId
  data.value.tableName = it.tableName
  data.value.databaseName = it.dbName
  data.value.dbColumns = it.dbColumns
  data.value.fields = it.checkedColumns.map((it: string) => ({
    fieldName: it,
    alias: it,
    selected: true,
  }))
}

const handleCheckGroup = () => {
  packetModalRef.value.show()
}

const handleClickJoinData = (joinData: any, item: any) => {
  console.log('[ handleClickJoinData ] >', joinData, item)
  item.joins = joinData
}

const handleClickGroup = (item: any) => {
  paramsState.value.groupId = item.groupId
  paramsState.value.groupName = item.groupName
}

function translateFormData() {
  const dt = fcRule.value.map((item: any) => {
    return {
      ...item,
      [item.field]: formData.value[item.field],
    }
  })
  return JSON.stringify(dt)
}

const handleSave = () => {
  console.log('[ handleSave ] >', editDataList.value)
  const params = {
    ...paramsState.value,
    formData: translateFormData(),
  }
  const editRuleList = tranlateDataToParams(editDataList.value)
  const basicRuleList = editRuleList.map((it) => {
    it.databaseId = currentActiveDatabase.value?.databaseId
    const ruleText = isCustomMode.value
      ? JSON.stringify({
          databaseName: isCustomMode.value
            ? selectedTableInfo.value?.databaseName
            : it.databaseName,
          tableId: isCustomMode.value ? selectedTableInfo.value?.tableId : '',
        })
      : JSON.stringify([it])
    return {
      ruleType: isCustomMode.value ? 2 : 1,
      ruleText: ruleText,
      ruleScript: isCustomMode.value ? customSqlContent.value : '',
      databaseName: isCustomMode.value ? selectedTableInfo.value?.databaseName : '',
    }
  })
  params.basicRuleList = basicRuleList

  // 添加动态参数
  params.dynamic = needDynamicParam.value ? 1 : 0
  if (needDynamicParam.value) {
    params.dynamicParam = JSON.stringify(dynamicParam.value)
  }
  if (isCustomMode.value) {
    if (!selectedTableInfo.value?.datasourceId) {
      message.error('请先选择数据源')
      return
    }
    if (!selectedTableInfo.value?.databaseName) {
      message.error('请先选择数据库')
      return
    }
    // 接口不支持传入databaseName和tableId，所以不传
    // params.databaseName = selectedTableInfo.value.databaseName
    // params.tableId = selectedTableInfo.value.tableId
    params.dataSourceId = selectedTableInfo.value.datasourceId
  }
  let req: Promise<any>
  if (autoId.value) {
    req = modifyIndexData({ ...params, autoId: autoId.value + '' })
  } else {
    req = addNewIndexData(params)
  }
  req
    .then(() => {
      message.success('保存成功')
      router.push('/indicator-list')
    })
    .catch((err: any) => {
      message.error(err.message || '保存失败')
    })
}

async function getIndexDetail() {
  const { data } = await getIndexDataDtl({ autoId: autoId.value + '' })
  Object.assign(paramsState.value, data)
  mapDataFieldToForm(data)
  mapDataFieldToEditList(data)
  let ruleText = JSON.parse(data.basicRuleList[0]?.ruleText)
  console.log('ruleText', ruleText)
  if (Array.isArray(ruleText)) {
    ruleText = ruleText[0]
  }
  // 数据库信息
  currentActiveDatabase.value = {
    databaseId: ruleText.databaseId,
    databaseName: ruleText.databaseName,
    dataSourceId: data.dataSourceId,
  }
  if (data.basicRuleList[0]?.ruleType === 2) {
    isCustomMode.value = true
    customSqlContent.value = data.basicRuleList[0]?.ruleScript

    // 解析保存的ruleText来获取详细信息
    const ruleText = JSON.parse(data.basicRuleList[0]?.ruleText)

    // 恢复selectedTableInfo
    selectedTableInfo.value = {
      datasourceId: data.dataSourceId,
      databaseName: ruleText.databaseName || data.basicRuleList[0]?.databaseName,
      tableId: ruleText.tableId || '',
      tableName: '', // 表名需要从树形菜单中获取
      nodeType: ruleText.tableId ? 3 : 2, // 有tableId说明选中的是表，否则是数据库
    }

    // 自动展开和选中树形菜单中对应的数据库节点，如果有表ID则展开到表节点
    // 使用方案3：事件通信方式（最优雅）
    executeWhenTreeReady(() => {
      if (treeMenuRef.value && treeMenuRef.value.expandAndSelectDatabase) {
        const success = treeMenuRef.value.expandAndSelectDatabase(
          data.dataSourceId,
          selectedTableInfo.value?.databaseName,
          selectedTableInfo.value?.tableId, // 传递表ID，如果存在则展开到表节点
        )
        if (success) {
          console.log('自动展开节点成功', {
            database: selectedTableInfo.value?.databaseName,
            table: selectedTableInfo.value?.tableId,
          })
        } else {
          console.warn('自动展开节点失败，树数据可能还未完全加载')
        }
      }
    })
  }
  // 动态参数
  needDynamicParam.value = data.dynamic === 1
  if (data.dynamicParam) {
    try {
      const paramList = JSON.parse(data.dynamicParam)
      dynamicParam.value = paramList || []
    } catch (err) {
      dynamicParam.value = []
    }
  }
}

function mapDataFieldToForm(data: any) {
  //  处理fcRule
  if (data.formData) {
    fcRule.value = FcDesigner.formCreate.parseJson(data.formData)
    fcOption.value = fsOptionsDefault
  }

  const formDataDt = JSON.parse(data['formData'] || data['fieldsData'])
  formDataDt.forEach((item: any) => {
    formData.value[item.field] = item.value
  })
}

function mapDataFieldToEditList(data: any) {
  const basicRuleList = data.basicRuleList

  const editRuleList = basicRuleList.map((it: any) => {
    const parsed = JSON.parse(it.ruleText)
    if (Array.isArray(parsed)) {
      return parsed[0]
    }
    return parsed
  })
  const res = translateParamsToData(editRuleList)

  editDataList.value = res
  dbColumns.value = editDataList.value[0].value.dbColumns
}

const joinTableColumns = computed<JoinTableColumns>(() => {
  // 过滤 editDataList 中的 dataSource 和 joins 的类型
  const keys = ['dataSource', 'joins']
  const columnCollections = editDataList.value.filter((item) => keys.includes(item.type))

  const result: JoinTableColumns = []
  columnCollections.forEach((item) => {
    let fields = []
    let tableName = ''
    if (item.type === 'dataSource') {
      tableName = item.value.tableName
      fields = item.value.dbColumns.map((ele: any) => ({ ...ele, tableName }))
    } else if (item.type === 'joins') {
      tableName = item.value.joinTableName
      fields = item.value.columns.map((ele: any) => ({ ...ele, tableName }))
    }
    result.push({
      fields,
      tableName,
    })
  })

  return result
})

// 当前数据库下的所有表数据
const availableTablesInCurrentDatabase = ref<TableInfo[]>([])

// 当前激活的数据库信息
const currentActiveDatabase = ref<DatabaseInfo | null>(null)

// 通过 provide 暴露数据供子组件使用
provide('availableTablesInCurrentDatabase', availableTablesInCurrentDatabase)
provide('currentActiveDatabase', currentActiveDatabase)
provide(JoinTableColumnsKey, joinTableColumns)
provide(DynamicParamKey, dynamicParam)
provide(NeedDynamicParamKey, needDynamicParam)

function getFormData() {
  getSingleIndexFormData({
    formId: formId.value as string,
  }).then(({ data }) => {
    mapDataFieldToForm(data)
    fcRule.value = FcDesigner.formCreate.parseJson(data.fieldsData)
    fcOption.value = JSON.parse(FcDesigner.formCreate.parseJson(data.compData))
  })
}

function init() {
  // 编辑模式
  if (autoId.value) {
    getIndexDetail()
    activeKey.value = ['1', '2']
  } else {
    // 新增模式
    if (formId.value) {
      getFormData()
    }
    editDataList.value = [
      {
        type: 'dataSource',
        value: {
          databaseName: '',
          tableName: '',
          fields: [],
          dbColumns: [],
        },
        preview: false,
      },
    ]
  }
}

init()

onMounted(() => {
  containerRef.value = document.querySelector('.layout-content')
})

// 组件卸载时清理状态
onUnmounted(() => {
  resetTreeMenuLoadState()
  console.log('主组件卸载，已清理TreeMenu相关状态')
})
// 处理自定义模式切换
const handleCustomModeChange = (checked: boolean) => {
  console.log('自定义模式切换:', checked)
}

// TreeMenu组件引用
const treeMenuRef = ref()
// 自定义SQL编辑器引用
const customSqlEditorRef = ref()
// SQL预览编辑器引用
const sqlPreviewEditorRef = ref()

// 基于事件通信的TreeMenu加载检测
const pendingAutoExpandCallbacks = ref<Array<() => void>>([])
const isTreeMenuLoaded = ref(false)

// 处理TreeMenu加载完成事件
const handleTreeMenuLoaded = () => {
  console.log('主组件: 接收到TreeMenu加载完成事件')
  isTreeMenuLoaded.value = true

  // 更新数据库和表的智能提示信息
  updateDatabaseAndTableCompletions()

  // 执行所有待执行的回调
  const callbackCount = pendingAutoExpandCallbacks.value.length
  console.log(`主组件: 开始执行${callbackCount}个待执行的回调`)

  pendingAutoExpandCallbacks.value.forEach((callback, index) => {
    try {
      console.log(`主组件: 执行第${index + 1}个回调`)
      callback()
    } catch (error) {
      console.error(`执行第${index + 1}个自动展开回调时出错:`, error)
    }
  })

  // 清空待执行队列
  pendingAutoExpandCallbacks.value = []
  console.log('主组件: 所有待执行回调已完成')
}

// 在TreeMenu加载完成后执行回调
const executeWhenTreeReady = (callback: () => void, timeout: number = 10000) => {
  if (isTreeMenuLoaded.value) {
    // 如果已经加载完成，直接执行
    console.log('TreeMenu已加载，直接执行回调')
    callback()
  } else {
    // 如果还未加载完成，添加到待执行队列
    console.log('TreeMenu未加载，添加到待执行队列')
    pendingAutoExpandCallbacks.value.push(callback)

    // 设置超时保护
    setTimeout(() => {
      if (!isTreeMenuLoaded.value) {
        console.warn(`TreeMenu加载超时（${timeout}ms），强制执行回调`)
        // 从队列中移除这个回调并执行
        const index = pendingAutoExpandCallbacks.value.indexOf(callback)
        if (index > -1) {
          pendingAutoExpandCallbacks.value.splice(index, 1)
          try {
            callback()
          } catch (error) {
            console.error('超时强制执行回调时出错:', error)
          }
        }
      }
    }, timeout)
  }
}

// 重置TreeMenu加载状态（用于组件重新加载时）
const resetTreeMenuLoadState = () => {
  console.log('重置TreeMenu加载状态')
  isTreeMenuLoaded.value = false
  pendingAutoExpandCallbacks.value = []
}

// 监听TreeMenu组件的变化，如果组件被重新创建，重置状态
watch(
  () => treeMenuRef.value,
  (newVal, oldVal) => {
    if (oldVal && !newVal) {
      // 组件被卸载
      resetTreeMenuLoadState()
    }
  },
)

// 处理SQL预览编辑器初始化
const handleSqlPreviewEditorInit = (editor: any) => {
  console.log('SQL预览编辑器初始化完成:', editor)
}

// 处理自定义SQL编辑器初始化
const handleCustomSqlEditorInit = (editor: any) => {
  console.log('自定义SQL编辑器初始化完成:', editor)
}

// 处理树形菜单选择事件
const handleTreeSelect = async (node: any, selectedKeys: string[]) => {
  console.log('选中节点:', node, selectedKeys)

  // 统一更新selectedTableInfo，根据节点类型设置不同的信息
  if (treeMenuRef.value) {
    const datasourceId = treeMenuRef.value.findDatasourceId
      ? treeMenuRef.value.findDatasourceId(node)
      : ''
    const databaseName = treeMenuRef.value.findDatabaseName
      ? treeMenuRef.value.findDatabaseName(node)
      : ''
    const tableId = treeMenuRef.value.findTableId ? treeMenuRef.value.findTableId(node) : ''

    selectedTableInfo.value = {
      datasourceId: datasourceId,
      databaseName: databaseName,
      tableId: tableId,
      tableName: node.tableName || '', // 表名仅在表节点和字段节点时有值
      nodeType: node.nodeType || 0,
    }

    console.log('更新选中节点信息:', selectedTableInfo.value)

    // 更新数据库和表的智能提示（如果有新的数据加载）
    updateDatabaseAndTableCompletions()
  }
  // 如果选中的是表节点，获取字段信息
  if (node.nodeType === 3 && node.tableId) {
    try {
      const response = await getDataAssetColumnList({
        tableId: node.tableId,
      })

      if (response.code === '000000' && response.data) {
        // 通过TreeMenu组件获取数据库名称
        let databaseName = ''
        if (treeMenuRef.value && treeMenuRef.value.findDatabaseName) {
          databaseName = treeMenuRef.value.findDatabaseName(node)
        }

        // 如果TreeMenu没有提供findDatabaseName方法，尝试从node数据中获取
        if (!databaseName) {
          // 从原始树形数据中查找数据库名称
          databaseName = findDatabaseNameFromTree(node.tableId) || '未知数据库'
        }

        // 通过TreeMenu组件获取数据源ID
        let datasourceId = ''
        if (treeMenuRef.value && treeMenuRef.value.findDatasourceId) {
          datasourceId = treeMenuRef.value.findDatasourceId(node)
        }

        // 保存选中的表信息
        selectedTableInfo.value = {
          tableId: node.tableId,
          tableName: node.tableName,
          databaseName: databaseName,
          datasourceId: datasourceId,
          nodeType: node.nodeType || 3, // 表节点类型
        }

        console.log('选中表信息:', selectedTableInfo.value)

        // 将列字段数据合并到树结构中
        const columnData = response.data.map((column: any) => ({
          ...column,
          nodeType: 4, // 设置为字段节点类型
          fieldName: column.title,
          fieldType: column.columnType,
          fieldTypeName: column.columnTypeName,
          fieldComment: column.comment,
        }))

        // 通过ref调用TreeMenu的方法来更新节点数据
        if (treeMenuRef.value && treeMenuRef.value.updateNodeColumns) {
          treeMenuRef.value.updateNodeColumns(node.tableId, columnData)
        }

        // 更新智能提示字段信息
        updateFieldCompletions(response.data, node.tableName, databaseName)

        console.log('表字段信息:', response.data)
      }
    } catch (error) {
      console.error('获取表字段信息失败:', error)
    }
  }
}

// 从树形数据中查找数据库名称的辅助函数
const findDatabaseNameFromTree = (tableId: string): string => {
  // 这里需要访问TreeMenu的原始数据，但由于组件封装，我们可能无法直接访问
  // 作为临时解决方案，我们可以尝试从已有的数据源信息中获取
  if (editDataList.value.length > 0 && editDataList.value[0].value.databaseName) {
    return editDataList.value[0].value.databaseName
  }

  // 如果没有找到，返回默认值
  return 'default_database'
}

// 从SQL语句中提取SELECT字段的函数
const extractFieldsFromSQL = (
  sql: string,
): Array<{
  fieldName: string
  alias: string
  selected: boolean
}> => {
  try {
    // 移除多余的空白和换行符
    const cleanSql = sql.replace(/\s+/g, ' ').trim()

    // 使用正则表达式匹配SELECT和FROM之间的内容
    const selectMatch = cleanSql.match(/SELECT\s+(.*?)\s+FROM/i)

    if (!selectMatch || !selectMatch[1]) {
      console.warn('无法从SQL中提取SELECT字段')
      return []
    }

    const selectClause = selectMatch[1].trim()

    // 如果是SELECT *，返回空数组让后端处理
    if (selectClause === '*') {
      return []
    }

    // 分割字段，处理逗号分隔的字段，过滤空字段
    const fieldStrings = selectClause
      .split(',')
      .map((field) => field.trim())
      .filter((field) => field.length > 0) // 过滤掉空字段（处理末尾逗号的情况）

    const fields = fieldStrings.map((fieldStr) => {
      // 处理字段别名 (AS 或空格)
      const aliasMatch = fieldStr.match(/^(.+?)\s+(?:AS\s+)?(\w+)$/i)

      if (aliasMatch) {
        // 有别名的情况
        const originalField = aliasMatch[1].trim()
        const alias = aliasMatch[2].trim()

        // 提取字段名（去掉表名前缀）
        const fieldName = originalField.includes('.')
          ? originalField.split('.').pop() || originalField
          : originalField

        return {
          fieldName: fieldName,
          alias: alias,
          selected: true,
        }
      } else {
        // 没有别名的情况
        const fieldName = fieldStr.includes('.') ? fieldStr.split('.').pop() || fieldStr : fieldStr

        return {
          fieldName: fieldName,
          alias: fieldName,
          selected: true,
        }
      }
    })

    console.log('从SQL中提取的字段:', fields)
    return fields
  } catch (error) {
    console.error('解析SQL字段时出错:', error)
    return []
  }
}

// 处理表双击事件，插入表名到SQL编辑器
const handleTableDoubleClick = (tableInfo: {
  tableName: string
  databaseName: string
  tableId: string
}) => {
  console.log('双击表:', tableInfo)
  // 在光标位置插入表名
  const tableName = `${tableInfo.databaseName}.${tableInfo.tableName}`
  insertTextToSqlEditor(tableName)
}

// 处理字段双击事件，插入字段名到SQL编辑器
const handleFieldDoubleClick = (fieldInfo: {
  fieldName: string
  tableName: string
  databaseName: string
  tableId: string
  fieldType: string
  fieldComment: string
}) => {
  console.log('双击字段:', fieldInfo)

  // 构造字段名，可以选择不同的格式
  let fieldText = ''

  // 如果有表名，使用完整的字段名格式：表名.字段名
  if (fieldInfo.tableName && fieldInfo.databaseName) {
    fieldText = `${fieldInfo.databaseName}.${fieldInfo.tableName}.${fieldInfo.fieldName}`
  } else if (fieldInfo.tableName) {
    fieldText = `${fieldInfo.tableName}.${fieldInfo.fieldName}`
  } else {
    // 只使用字段名
    fieldText = fieldInfo.fieldName
  }

  insertTextToSqlEditor(fieldText)
}

// 统一的SQL编辑器文本插入函数
const insertTextToSqlEditor = (text: string) => {
  if (!isCustomMode.value) {
    console.warn('当前不在自定义SQL模式，无法插入文本')
    return
  }

  // 使用编辑器组件的插入文本方法
  if (customSqlEditorRef.value && customSqlEditorRef.value.insertText) {
    customSqlEditorRef.value.insertText(text)
  }

  console.log('已插入文本到SQL编辑器:', text)
}
</script>

<style scoped lang="less">
@import './style.less';
</style>
