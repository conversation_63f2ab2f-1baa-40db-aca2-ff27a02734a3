<template>
  <a-modal
    :open="newModelValue"
    title="mock调用"
    width="1200px"
    @cancel="newModelValue = false"
    :destroyOnClose="true"
    :footer="false"
  >
    <div class="mock-box">
      <div class="time-box">
        <div
          class="time-box-item"
          @click="selectItem(item)"
          v-for="(item, index) in mockData"
          :key="index"
          :class="{ active: requestData.time === item.time }"
        >
          {{ item.time }}
        </div>
      </div>
      <Resquest
        :data="requestData"
        @update:value="updateValue"
        @update:list="updateList"
        ref="resquestRef"
        :commonHead="commonHead"
        :rawData="data"
      ></Resquest>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { parseUrl, generateDataFromList, generateConstant, getLocalItem } from '@/utils/tool'
import Resquest from './request.vue'

interface Mock {
  modelValue: boolean
  data: any
  commonHead: any
}

const mockData = ref<any[]>([])

const props: Mock = withDefaults(defineProps<Mock>(), {
  modelValue: false,
})

const requestData = ref<Record<string, any>>({})
const resquestRef = ref()

const emit = defineEmits(['update:modelValue'])

const newModelValue = computed({
  get: () => props.modelValue,
  set: (value) => {
    emit('update:modelValue', value)
  },
})

watch(newModelValue, (val) => {
  if (val) {
    const data = props.data
    const obj = parseUrl(data.url)
    const fileItem = data.params.find((item: { type: string }) => item.type === 'File')
    const list = data.params.map((item: any) => {
      return { name: item.name, value: generateConstant(item) }
    })
    const headersList = data.headers.map((item: any) => {
      return { name: item.name, value: generateDataFromList(item.type) }
    })
    let Params: any[] = []
    let objData: any
    let type = 'JSON'
    // 含有文件用form-data
    if (fileItem) {
      type = 'Form-Data'
      objData = []
    } else {
      objData = {}
    }
    if (data.httpMethodName.toLowerCase() === 'get') {
      Params = list
    } else {
      list.map((item: { name: string | number; value: any }) => {
        // 含有文件用form-data
        if (fileItem) {
          objData.push({
            name: item.name,
            value: item.value,
          })
        } else {
          objData[item.name] = item.value
        }
      })
    }
    requestData.value = {
      time: '',
      parameter: {
        url: obj.ipWithPort + data.fullPath,
        methods: data.httpMethodName,
        Params: Params,
        Body: {
          type: type,
          value: objData,
        },
        Cookie: [],
        Headers: headersList,
      },
      response: {
        code: '',
        time: '',
        'content-length': 0,
        Preview: {},
        Headers: [],
      },
    }
    getMockData()
  }
})

const getMockData = async () => {
  const data = getLocalItem(requestData.value.parameter.url)
  mockData.value = data
}

const updateList = () => {
  getMockData()
}

const selectItem = (item: any) => {
  requestData.value = item
  resquestRef.value.updataHandle(item)
}

const updateValue = (key: string, value: any) => {
  requestData.value.parameter[key] = value
  console.log(
    '%c 🥧 requestData.value.parameter: ',
    'font-size:12px;background-color: #ED9EC7;color:#fff;',
    requestData.value.parameter,
  )
}
</script>
<style scoped lang="less">
.mock-box {
  display: flex;
  .time-box {
    width: 180px;
    padding: 5px;
    border: 1px solid #cccc;
    height: 590px;
    overflow: auto;
    &-item {
      cursor: pointer;
      padding: 5px;
    }
    .active {
      background: #d7d6d6cc;
    }
    &-item:hover {
      background: #edebebcc;
    }
  }
}
</style>
