<template>
  <Card class="multi-filter">
    <Space direction="vertical">
      <Select
        :options="stringConditionOptions"
        class="condiction-select"
        v-model:value="condition"
        @change="onConditionChange"
      />

      <Space v-show="showCheckbox" direction="vertical" class="max-h-200px overflow-y-scroll">
        <InputSearch placeholder="搜索列表" v-model:value="searchValue" :allow-clear="true" />
        <Checkbox
          v-if="selectOptions.length > 0"
          :indeterminate="indeterminate"
          v-model:checked="checkAll"
          @change="onCheckAllChange"
        >
          全选
        </Checkbox>
        <CheckboxGroup
          v-model:value="checkedList"
          @change="onCheckChange"
          class="fc"
          :options="filterOptions2"
        >
        </CheckboxGroup>
      </Space>

      <Select
        v-show="!showCheckbox"
        mode="tags"
        v-model:value="checkedList"
        class="w-full"
        @blur="onSelectChange"
        :allow-clear="true"
        @clear="onSelectClear"
      ></Select>

      <div class="text-right">
        <Button type="primary" @click="handleConfirm">确定</Button>
      </div>
    </Space>
  </Card>
</template>

<script setup lang="ts">
import { Card, Space, Select, Button, CheckboxGroup, Checkbox, InputSearch } from 'ant-design-vue'
import { ConditionEnum, stringConditionOptions } from '@/components/filter/constant'
import { computed, ref, watch } from 'vue'
import type { CheckboxValueType } from 'ant-design-vue/es/checkbox/interface'

const props = defineProps<{
  options1?: { label: string; value: string }[]
  onConfirm: (condition: string, value: string[]) => void
}>()
const condition = ref(stringConditionOptions[0].value)
const showCheckbox = computed(
  () => condition.value === ConditionEnum.equal || condition.value === ConditionEnum.notEqual,
)
const searchValue = ref('')
const checkAll = ref(false)
const indeterminate = ref(false)
const checkedList = ref<string[]>([])
// const fakeOptions = [
//   { label: '选项1', value: '1' },
//   { label: '选项2', value: '2' },
//   { label: '选项3', value: '3' },
//   { label: '选项4', value: '4' },
//   { label: '选项5', value: '5' },
// ]
const getInitOptions = () => {
  return props.options1 ? [...props.options1] : []
}
const selectOptions = ref(getInitOptions())
const filterOptions2 = computed(() => {
  const res = selectOptions.value.filter((opt) => opt.label.includes(searchValue.value))
  return res
})
watch([selectOptions, checkedList], ([arr1, arr2]) => {
  indeterminate.value = arr2.length > 0 && arr2.length < arr1.length
  checkAll.value = arr2.length === arr1.length
})
const onCheckChange = (list: CheckboxValueType[]) => {
  checkAll.value = list?.length === selectOptions.value.length
}
const onCheckAllChange = (e: any) => {
  checkedList.value = e.target.checked ? selectOptions.value.map((opt) => opt.value) : []
}

const onConditionChange = () => {
  searchValue.value = ''
}

const onSelectChange = (e: any) => {
  const value = e.target.value.trim()
  console.log('手动输入', value)
  if (!value) return
  let tar = selectOptions.value.find((opt) => opt.value === value)
  if (!tar) {
    selectOptions.value.unshift({ label: value, value: value })
    console.log('选项', selectOptions.value)
  }
}
const onSelectClear = () => {
  console.log('清空')
  checkedList.value = []
  selectOptions.value = getInitOptions()
}
const handleConfirm = () => {
  // 处理确定逻辑
  console.log('条件:', condition.value)
  console.log('选中项:', checkedList.value)
  props.onConfirm(condition.value, checkedList.value)
}
</script>

<style scoped lang="less">
.multi-filter {
  width: 380px;
  background-color: white;
  font-size: 14px;
  border-radius: 8px;
  box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.05);
  :deep(.ant-space) {
    width: 100%;
  }
  .condiction-select {
    min-width: 104px;
  }
  .text-select {
    width: 100%;
  }
  .text-right {
    text-align: right;
  }
  .fc {
    flex-direction: column;
    gap: 10px;
  }
  .option {
    font-size: 14px;
    color: #4c5773;
  }
}
</style>
