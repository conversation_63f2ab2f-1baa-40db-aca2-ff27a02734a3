import { Alias, Transformer } from '@airpower/transformer'

/** 数据库表信息 */
export class DbTableInfo extends Transformer {
  /** 表名 */
  @Alias('TABLE_NAME')
  tableName!: string

  /** 数据库长度 */
  @Alias('DATA_LENGTH')
  dataSize!: number

  /** 表类型 */
  @Alias('ENGINE')
  tableType!: string

  /** 行格式 */
  @<PERSON><PERSON>('ROW_FORMAT')
  rowFormat!: string

  /** 注释 */
  @Alias('TABLE_COMMENT')
  comment!: string
}

export class ColumnData extends Transformer {
  /** 字段名 */
  @Alias('name')
  dataIndex!: string

  /** 字段类型 */
  @Alias('type')
  dataType!: string

  /** 注释 */
  comment!: string

  /** 是否主键 */
  @Alias('isPK')
  isPrimaryKey!: boolean

  get title(): string {
    return this.comment || this.dataIndex
  }
}

/** 表-字段数据 */
export interface TableFieldData {
  columnName: string
  columnType: string
  comment: string
  decimal: string
  increment: boolean
  isNull: boolean
  length: string
  primaryKey: boolean
  virtual: boolean
}
