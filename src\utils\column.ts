interface OrderDTOItem {
  title: string
  dataIndex: string
  searchInput?: string // 可选属性
  key?: string
  customRender?: (...arg: any) => string
}
interface CustomRenderArg {
  record: {
    isDept: any
    isUse: string
  }
}

const Enabled: { [key: string]: string } = {
  '1': '是',
  '0': '否',
}

interface OrderDTO extends Array<OrderDTOItem> {}
const OrderDTO: OrderDTO = [
  {
    title: '应用标识',
    dataIndex: 'appCode',
    key: 'appCode',
  },
  {
    title: '应用名称',
    dataIndex: 'appName',
    searchInput: undefined,
    key: 'appName',
  },
  {
    title: '创建人',
    dataIndex: 'createUserName',
    key: 'createUserName',
  },
  {
    title: '是否启用',
    searchInput: undefined,
    dataIndex: 'isUse',
    key: 'isUse',
    customRender: (...arg: [CustomRenderArg]): string => {
      const { record } = arg[0]
      return Enabled[record.isUse]
    },
  },
]

//租户管理
const TebantDTO: OrderDTO = [
  {
    title: '租户代码',
    dataIndex: 'tenantCode',
    key: 'tenantCode',
  },
  {
    title: '租户名称',
    dataIndex: 'tenantName',
    searchInput: undefined,
    key: 'tenantName',
  },
  {
    title: '管理员账号',
    dataIndex: 'manageUserAccount',
    key: 'manageUserAccount',
  },
  {
    title: '是否启用',
    dataIndex: 'isUse',
    searchInput: undefined,
    key: 'isUse',
  },
  {
    title: '创建人',
    dataIndex: 'createUserName',
    key: 'createUserName',
  },
  {
    title: '创建时间',
    dataIndex: 'updateTime',
    key: 'updateTime',
  },
]

//系统参数配置表格
const ParameterDTO: OrderDTO = [
  {
    title: '参数编号',
    dataIndex: 'paramId',
    key: 'paramId',
  },
  {
    title: '参数说明',
    dataIndex: 'paramRemark',
    searchInput: undefined,
    key: 'paramRemark',
  },
  {
    title: '参数值',
    dataIndex: 'paramValue',
    key: 'paramValue',
  },
  {
    title: '更新时间',
    dataIndex: 'updateTime',
    key: 'updateTime',
  },
  {
    title: '更新人',
    dataIndex: 'updateUser',
    key: 'updateUser',
  },
]

//系统管理配置表格
const SystemManageDTO: OrderDTO = [
  {
    title: '系统名称',
    dataIndex: 'systemName',
    searchInput: undefined,
    key: 'systemName',
  },
  {
    title: '系统标识',
    dataIndex: 'systemCode',
    key: 'systemCode',
  },
  {
    title: '是否启用',
    dataIndex: 'isUse',
    searchInput: undefined,
    key: 'isUse',
    customRender: (...arg: [CustomRenderArg]): string => {
      const { record } = arg[0]
      return Enabled[record.isUse]
    },
  },
  {
    title: '创建人',
    dataIndex: 'createUserName',
    key: 'createUserName',
  },
  {
    title: '系统类别',
    dataIndex: 'systemTypeName',
    key: 'systemTypeName',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
  },
]

// 职务管理
const PossitionManageDTO: OrderDTO = [
  {
    title: '职务编号',
    dataIndex: 'positionId',
    key: 'positionId',
  },
  {
    title: '职务名称',
    dataIndex: 'positionName',
    searchInput: undefined,
    key: 'positionName',
  },
  {
    title: '是否启用',
    dataIndex: 'isUse',
    searchInput: undefined,
    key: 'isUse',
    customRender: (...arg: [CustomRenderArg]): string => {
      const { record } = arg[0]
      return Enabled[record.isUse]
    },
  },
  {
    title: '创建人',
    dataIndex: 'createUserName',
    key: 'createUserName',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
  },
]

//组织管理
const OrganizationalManagementDTO: OrderDTO = [
  {
    title: '组织名称',
    dataIndex: 'deptName',
    searchInput: undefined,
    key: 'deptName',
  },
  {
    title: '负责人',
    dataIndex: 'dutyUserName',
    key: 'dutyUserName',
  },
  {
    title: '是否启用',
    dataIndex: 'isUse',
    searchInput: undefined,
    key: 'isUse',
    customRender: (...arg: [CustomRenderArg]): string => {
      const { record } = arg[0]
      return Enabled[record.isUse]
    },
  },
  {
    title: '创建人',
    dataIndex: 'createUserName',
    key: 'createUserName',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
  },
]

//员工列表
const OrganizationalUserDTO: OrderDTO = [
  {
    title: '用户编号',
    dataIndex: 'userId',
    key: 'userId',
  },
  {
    title: '姓名',
    dataIndex: 'userName',
    searchInput: undefined,
    key: 'userName',
  },
  {
    title: '登录账号',
    dataIndex: 'loginAccount',
    searchInput: undefined,
    key: 'loginAccount',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
  },
]

//员工列表
const RoleManagementDTO: OrderDTO = [
  {
    title: '租户名称',
    dataIndex: 'tenantName',
    key: 'tenantName',
  },
  {
    title: '角色名称',
    dataIndex: 'roleName',
    searchInput: undefined,
    key: 'roleName',
  },
  {
    title: '角色类别',
    dataIndex: 'roleType',
    searchInput: undefined,
    key: 'roleType',
    customRender: (...arg: [CustomRenderArg]): string => {
      const { record } = arg[0]
      //@ts-ignore
      return record?.roleTypeName
    },
  },
  {
    title: '是否启用',
    dataIndex: 'isUser',
    key: 'isUser',
  },
  {
    title: '创建人',
    dataIndex: 'userName',
    key: 'userName',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
  },
  {
    title: '备注',
    dataIndex: 'remark',
    key: 'remark',
  },
]

//员工列表
const allocationUserDTO: OrderDTO = [
  {
    title: '登录账号',
    dataIndex: 'loginAccount',
    key: 'loginAccount',
  },
  {
    title: '姓名',
    dataIndex: 'userName',
    key: 'userName',
  },
  {
    title: '创建人',
    dataIndex: 'loginAccount',
    key: 'loginAccount',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
  },
]

//员工列表
const UserGroupDTO: OrderDTO = [
  // {
  //   title: '登录账号',
  //   dataIndex: 'loginAccount',
  //   key: 'loginAccount',
  // },
  {
    title: '姓名',
    dataIndex: 'userName',
    key: 'userName',
  },
  {
    title: '创建人',
    dataIndex: 'createusername',
    key: 'createusername',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
  },
]

//员工列表
const allocationUserModalDTO: OrderDTO = [
  {
    title: '登录账号',
    dataIndex: 'loginAccount',
    key: 'loginAccount',
  },
  {
    title: '姓名',
    dataIndex: 'userName',
    searchInput: undefined,
    key: 'userName',
  },
  {
    title: '用户状态',
    dataIndex: 'userStatus',
    key: 'userStatus',
  },
]

// 已分配系统
const assignedMenuDTO: OrderDTO = [
  {
    title: '系统标识',
    dataIndex: 'systemCode',
    key: 'systemCode',
  },
  {
    title: '系统名称',
    dataIndex: 'systemName',
    key: 'systemName',
  },
]

// 岗位管理
const PositionManageDTO: OrderDTO = [
  {
    title: '岗位编号',
    dataIndex: 'jobId',
    key: 'jobId',
  },
  {
    title: '岗位名称',
    dataIndex: 'jobName',
    searchInput: undefined,
    key: 'jobName',
  },
  {
    title: '是否启用',
    dataIndex: 'isUse',
    searchInput: undefined,
    key: 'isUse',
    customRender: (...arg: [CustomRenderArg]): string => {
      const { record } = arg[0]
      return Enabled[record.isUse]
    },
  },
  {
    title: '创建人',
    dataIndex: 'userName',
    key: 'userName',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
  },
]
//用户组管理
const UserGroupListDTO: OrderDTO = [
  {
    title: '用户组编号',
    dataIndex: 'userGroupId',
    key: 'userGroupId',
  },
  {
    title: '用户组名称',
    dataIndex: 'userGroupName',
    searchInput: undefined,
    key: 'userGroupName',
  },
  {
    title: '是否启用',
    dataIndex: 'isUse',
    searchInput: undefined,
    key: 'isUse',
    customRender: (...arg: [CustomRenderArg]): string => {
      const { record } = arg[0]
      return Enabled[record.isUse]
    },
  },
  {
    title: '创建人',
    dataIndex: 'username',
    key: 'username',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
  },
]

// 岗位管理
const organizationTypeDTO: OrderDTO = [
  {
    title: '组织编号',
    dataIndex: 'deptTypeId',
    key: 'deptTypeId',
  },
  {
    title: '组织名称',
    dataIndex: 'deptTypeName',
    searchInput: undefined,
    key: 'deptTypeName',
  },
  {
    title: '是否为部门',
    dataIndex: 'isDept',
    key: 'isDept',
    customRender: (...arg: [CustomRenderArg]): string => {
      const { record } = arg[0]
      return Enabled[record.isDept]
    },
  },
  {
    title: '等级',
    dataIndex: 'levelId',
    key: 'levelId',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
  },
  {
    title: '备注',
    dataIndex: 'remark',
    key: 'remark',
  },
]
export {
  OrderDTO,
  TebantDTO,
  ParameterDTO,
  SystemManageDTO,
  PossitionManageDTO,
  OrganizationalManagementDTO,
  OrganizationalUserDTO,
  RoleManagementDTO,
  allocationUserDTO,
  allocationUserModalDTO,
  assignedMenuDTO,
  PositionManageDTO,
  UserGroupListDTO,
  UserGroupDTO,
  organizationTypeDTO,
}
