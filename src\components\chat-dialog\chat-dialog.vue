<template>
  <a-modal
    destroyOnClose
    v-model:open="newModelValue"
    title="生成代码"
    width="1500px"
    :footer="null"
  >
    <div class="chat-container">
      <!-- 消息列表 -->
      <div class="chat-messages" ref="chatMessages">
        <div
          v-for="(item, index) in sessionData"
          :key="index"
          :class="{ 'right-box': item.type === 'sent' }"
        >
          <div style="display: inline-block" :class="['chat-bubble', item.type]">
            <div>{{ item.text }}</div>
            <argument
              v-if="item.component === 'argument'"
              :data="item.data"
              :moduleName="moduleName"
              @success:argument="successArgument"
            ></argument>
            <Tips v-else-if="item.component === 'tips'" :data="item.data"></Tips>
            <pageType
              v-else-if="item.component === 'page-type'"
              :data="item.data"
              @generation:code="generationCodHandle"
              :moduleName="moduleName"
            ></pageType>
            <createRouter
              v-else-if="item.component === 'create-router'"
              @generation:code="generationCodHandle"
            ></createRouter>
          </div>
        </div>
      </div>

      <!-- 发送消息区域 -->
      <div class="chat-input">
        <input
          v-model="newMessage"
          @keyup.enter="sendMessage"
          type="text"
          placeholder="输入消息..."
        />
        <a-button type="primary" :loading="loading" @click="sendMessage">发送</a-button>
      </div>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick, computed, watch } from 'vue'
import { getMessage, getCompletionsResponse } from '@/utils/gpt-tool'
import { completions } from '@/api/services/manage'

import { generateDataFromList } from '@/utils/tool'
import argument from './argument.vue'
import Tips from './tips.vue'
import pageType from './page-type.vue'
import createRouter from './create-router.vue'
import { generationRouter, generationCode } from '@/components/code-generation/code-generation'
import { generationGpt } from '@/api/services/manage'

interface Message {
  text: string
  type: 'sent' | 'received'
  component?: string
  data?: any
}

interface Modal {
  modelValue: boolean
}

const props: Modal = withDefaults(defineProps<Modal>(), {
  modelValue: false,
})
const moduleName = ref('')
const pageCode = ref('')
const routerData = ref<any>()
const emit = defineEmits(['update:modelValue', 'update:formData'])

const newModelValue = computed({
  get: () => props.modelValue,
  set: (value) => {
    emit('update:modelValue', value)
  },
})

watch(newModelValue, (val) => {
  if (val) {
    moduleName.value = generateDataFromList('string') as string
  }
})

const sessionData = ref<Message[]>([{ text: '您好，有什么可以帮忙的吗？', type: 'received' }])

const generationCodHandle = ({ type, content }: { type: string; content: any }) => {
  if (type === 'page-type') {
    pageCode.value = content
    sessionData.value.push({
      text: '正在创建路由...',
      component: 'create-router',
      type: 'received',
    })
    scrollToBottom()
  } else if (type === 'create-router') {
    routerData.value = content
    sessionData.value.push({
      text: '',
      component: 'tips',
      type: 'received',
      data: {
        title: '准备生成代码，期间会重新刷新页面',
        btnText: '确定',
        callback: async () => {
          // 生成当前模块api文件
          await generationGpt({ moduleName: moduleName.value })

          // // 生成路由文件和页面文件
          await generationRouter({
            routerList: [
              {
                menuId: generateDataFromList('number') as number,
                menuName: content?.menuName,
                menuUrl: content?.menuUrl,
                pageType: 'normal',
                pageContent: pageCode.value,
              },
            ],
          })
        },
      },
    })
    scrollToBottom()
  }
}

const newMessage = ref('')
const loading = ref(false)

// 发送消息函数
const sendMessage = async () => {
  if (newMessage.value.trim()) {
    loading.value = true
    sessionData.value.push({ text: newMessage.value, type: 'sent' })
    const params = {
      model: 'Qwen2-coder-32B-feisuan',
      stream: true,
      messages: [{ role: 'user', content: getMessage(newMessage.value) }],
    }
    const str = newMessage.value
    newMessage.value = '' // 清空输入框

    const response = await completions(params)
    const data = getCompletionsResponse(response)
    sessionData.value.push({
      text: str + '的接口协议如下：',
      type: 'received',
      component: 'argument',
      data,
    })

    loading.value = false
    scrollToBottom()
  }
}

const successArgument = () => {
  console.log('触发咯')
  sessionData.value.push({
    text: '',
    component: 'tips',
    type: 'received',
    data: {
      title:
        '如果您希望继续增加新的接口，请继续提出问题。我们将继续为您提供帮助。如果没有更多问题，您可以点击“下一步”以继续进行。',
      callback: () => {
        sessionData.value.push({
          text: '请选择页面类型',
          component: 'page-type',
          type: 'received',
          data: {
            moduleName: moduleName.value,
          },
        })
        scrollToBottom()
      },
    },
  })
  scrollToBottom()
}

// 滚动到最新消息
const scrollToBottom = () => {
  nextTick(() => {
    const chatMessages = document.querySelector('.chat-messages') as HTMLElement
    chatMessages.scrollTop = chatMessages.scrollHeight
  })
}

// 页面加载后滚动到最底部
onMounted(() => {
  scrollToBottom()
})
</script>

<style scoped>
.chat-container {
  margin: 0 auto;
  padding: 20px;
  border: 1px solid #ccc;
  border-radius: 8px;
  background-color: #f9f9f9;
  display: flex;
  flex-direction: column;
  height: 700px;
  max-height: 700px;
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  margin-bottom: 20px;
}

.right-box {
  display: flex;
  justify-content: flex-end;
}

.chat-bubble {
  max-width: 70%;
  padding: 10px;
  border-radius: 15px;
  margin: 5px 0;
  word-wrap: break-word;
}

.chat-bubble.sent {
  background-color: #007bff;
  color: white;
  margin-left: auto;
  text-align: right;
}

.chat-bubble.received {
  background-color: #e1e1e1;
  color: black;
}

.chat-input {
  display: flex;
  gap: 10px;
}

.chat-input input {
  flex: 1;
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
}
</style>
