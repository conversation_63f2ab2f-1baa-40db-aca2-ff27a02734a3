import service from '@/api'
import { MOCK_DATA } from './metadatamanage-mock'

/**
 * 根据taskId删除元数据提取任务表数据 - MDM004-删除元数据任务
 * @param {string} taskId - 任务ID
 * @returns {any} - 返回一个解析为响应数据的Promise
 */

export async function deleteTask(data: { taskId: string }): Promise<any> {
  return service(
    {
      method: 'POST',
      url: '/assetManager/metadataManage/deleteTask',
      data,
    },
    MOCK_DATA.deleteTask,
  )
}

/**
 * 新增元数据提取任务表数据 - MDM001-新增元数据任务
 * @param {number} datasourceId - 数据源ID
 * @param {string} databseName - 数据库名称
 * @param {string} taskName - 任务名称
 * @param {string} tableConfigs - 提取表配置，逗号分隔
 * @param {number} triggerType - 触发方式1自动2手动
 * @param {number} pushType - 推送方式1每日2每周3每月
 * @param {string} pushDate - 推送日期，1,3每周代表为每周一周三，每月代表每月1号三号
 * @param {string} pushTime - 推送时间，09:00:00
 * @param {string} description - 任务描述
 * @param {string} opUser -
 * @param {string} opUserName -
 * @returns {any} - 返回一个解析为响应数据的Promise
 */

export async function saveTask(
  data: {
    datasourceId: number
    databseName: string
    taskName: string
    tableConfigs: string
    triggerType: number
    pushType?: number
    pushDate?: string
    pushTime?: string
    description?: string
    dbSchema?: string
  },
  headers?: {
    opUser?: string
    opUserName?: string
  },
): Promise<any> {
  return service(
    {
      method: 'POST',
      url: '/assetManager/metadataManage/saveTask',
      data,
      headers,
    },
    MOCK_DATA.saveTask,
  )
}

/**
 * 分页查询元数据提取任务表数据 - MDM003-查询元数据任务列表
 * @param {number} datasourceId - 数据源ID
 * @param {string} databseName - 数据库名称
 * @param {string} taskName - 任务名称
 * @param {number} status - 任务状态1启用2暂停
 * @param {number} pageIndex - 查询当前页数
 * @param {number} pageSize - 每页显示的记录条数
 * @param {string} opUser -
 * @param {string} opUserName -
 * @returns {any} - 返回一个解析为响应数据的Promise
 */

export async function taskPageList(
  data: {
    datasourceId: number
    databseName: string
    taskName?: string
    status?: number
    pageIndex?: number
    pageSize?: number
  },
  headers?: {
    opUser?: string
    opUserName?: string
  },
): Promise<{
  code: string
  msg: string
  data: Array<{
    taskId: string
    taskName: string
    triggerType: number
    pushType: number
    pushDate: string
    pushTime: string
    status: number
    description: string
    createTime: string
    updateTime: string
  }>
}> {
  return service(
    {
      method: 'GET',
      url: '/assetManager/metadataManage/taskPageList',
      params: data,
      headers,
    },
    MOCK_DATA.taskPageList,
  )
}

/**
 * 根据taskId修改元数据提取任务表数据 - MDM002-修改元数据任务
 * @param {string} taskId - 主键
 * @param {string} taskName - 任务名称
 * @param {string} tableConfigs - 提取表配置，逗号分隔
 * @param {number} triggerType - 触发方式1自动2手动
 * @param {number} pushType - 推送方式1每日2每周3每月
 * @param {string} pushDate - 推送日期，1,3每周代表为每周一周三，每月代表每月1号三号
 * @param {string} pushTime - 推送时间，09:00:00
 * @param {string} description - 任务描述
 * @param {string} opUser -
 * @param {string} opUserName -
 * @returns {any} - 返回一个解析为响应数据的Promise
 */

export async function updateTask(
  data: {
    taskId: string
    taskName?: string
    tableConfigs?: string
    triggerType?: number
    pushType?: number
    pushDate?: string
    pushTime?: string
    description?: string
  },
  headers?: {
    opUser?: string
    opUserName?: string
  },
): Promise<any> {
  return service(
    {
      method: 'POST',
      url: '/assetManager/metadataManage/updateTask',
      data,
      headers,
    },
    MOCK_DATA.updateTask,
  )
}

/**
 * 根据taskId修改元数据提取任务表数据 - MDM005-启用/暂停元数据任务
 * @param {string} taskId - 主键
 * @param {number} status - 任务状态1启用2暂停
 * @param {string} opUser -
 * @param {string} opUserName -
 * @returns {any} - 返回一个解析为响应数据的Promise
 */

export async function enableTask(
  data: {
    taskId: string
    status: number
  },
  headers?: {
    opUser?: string
    opUserName?: string
  },
): Promise<any> {
  return service(
    {
      method: 'POST',
      url: '/assetManager/metadataManage/enableTask',
      data,
      headers,
    },
    MOCK_DATA.enableTask,
  )
}

/**
 *  - MDM006-运行元数据任务
 * @param {string} taskId -
 * @param {string} opUser -
 * @param {string} opUserName -
 * @returns {any} - 返回一个解析为响应数据的Promise
 */

export async function startTask(
  data: {
    taskId: string
  },
  headers?: {
    opUser?: string
    opUserName?: string
  },
): Promise<any> {
  return service(
    {
      method: 'POST',
      url: '/assetManager/metadataManage/startTask',
      data,
      headers,
    },
    MOCK_DATA.startTask,
  )
}

/**
 *  - MDM007-查询元数据执行日志
 * @param {string} taskId -
 * @param {number} pageIndex -
 * @param {number} pageSize -
 * @param {string} opUser -
 * @param {string} opUserName -
 * @returns {any} - 返回一个解析为响应数据的Promise
 */

export async function getTaskLog(
  data: {
    taskId: string
    pageIndex: number
    pageSize: number
  },
  headers?: {
    opUser?: string
    opUserName?: string
  },
): Promise<{
  code: string
  msg: string
  data: Array<{
    id: number
    taskstatus: number
    errormsg: string
    dbType: string
    dbschame: string
    dbname: string
    tableName: string
    dataStartTime: string
    createTime: string
    updateTime: string
  }>
}> {
  return service(
    {
      method: 'GET',
      url: '/assetManager/metadataManage/getTaskLog',
      params: data,
      headers,
    },
    MOCK_DATA.getTaskLog,
  )
}

/**
 *  - MDM008-查询数据源下数据库列表
 * @param {number} datasourceId - 数据源ID
 * @param {string} opUser -
 * @param {string} opUserName -
 * @returns {any} - 返回一个解析为响应数据的Promise
 */

export async function getDbListBySourceId(
  data: {
    datasourceId: number
  },
  headers?: {
    opUser?: string
    opUserName?: string
  },
): Promise<any> {
  return service(
    {
      method: 'GET',
      url: '/assetManager/metadataManage/getDbListBySourceId',
      params: data,
      headers,
    },
    MOCK_DATA.getDbListBySourceId,
  )
}

/**
 *  - MDM009-查询数据库下数据表列表
 * @param {number} datasourceId - 数据源ID
 * @param {string} databaseName - 数据库名称
 * @param {number} pageIndex -
 * @param {number} pageSize -
 * @param {string} opUser -
 * @param {string} opUserName -
 * @returns {any} - 返回一个解析为响应数据的Promise
 */

export async function getTableListByDb(
  data: {
    datasourceId: number
    databaseName: string
    pageIndex: number
    pageSize: number
  },
  headers?: {
    opUser?: string
    opUserName?: string
  },
): Promise<any> {
  return service(
    {
      method: 'GET',
      url: '/assetManager/metadataManage/getTableListByDb',
      params: data,
      headers,
    },
    MOCK_DATA.getTableListByDb,
  )
}

/**
 *  - MDM010-查询数据表血缘关系
 * @param {string} datasourceId - 数据源ID
 * @param {string} databaseName - 数据库名称
 * @param {string} tableName - 数据表名称
 * @param {string} opUser -
 * @param {string} opUserName -
 * @returns {any} - 返回一个解析为响应数据的Promise
 */

export async function getTableLineage(
  data: {
    datasourceId: string
    databaseName: string
    tableName: string
  },
  headers?: {
    opUser?: string
    opUserName?: string
  },
): Promise<any> {
  return service(
    {
      method: 'GET',
      url: '/assetManager/metadataManage/getTableLineage',
      params: data,
      headers,
    },
    MOCK_DATA.getTableLineage,
  )
}

/**
 * 预览数据 - DAM011-元数据管理预览数据
 * @param {string} datasourceId - 数据源ID
 * @param {string} databaseName - 数据库名称
 * @param {string} tableName - 数据表名称
 * @param {number} pageIndex - 分页页码
 * @param {number} pageSize - 分页数据量
 * @param {string} opUser - 操作人
 * @param {string} opUserName - 操作人名称
 * @returns {any} - 返回一个解析为响应数据的Promise
 */

export async function previewData(
  data: {
    datasourceId: string
    databaseName: string
    tableName: string
    pageIndex: number
    pageSize: number
    schemaName: string
  },
  headers?: {
    opUser?: string
    opUserName?: string
  },
): Promise<{
  code: string
  msg: string
  data: Array<{
    columnList: Array<{
      columnType: string
      isNullable: string
      dataType: string
      columnComment: string
      ordinalPosition: string
      columnKey: string
      tableName: string
      columnName: string
    }>
    dataList: undefined
  }>
}> {
  return service(
    {
      method: 'GET',
      url: '/assetManager/metadataManage/previewData',
      params: data,
      headers,
    },
    MOCK_DATA.previewData,
  )
}

/**
 * 预览数据 - DAM012-数据源列表
 * @param {string} keyword - 搜索描述
 * @param {number} pageIndex - 分页页码
 * @param {number} pageSize - 分页数据量
 * @param {string} opUser - 操作人
 * @param {string} opUserName - 操作人名称
 * @returns {any} - 返回一个解析为响应数据的Promise
 */

export async function getSrouceList(
  data: {
    keyword?: string
    pageIndex: number
    pageSize: number
  },
  headers?: {
    opUser?: string
    opUserName?: string
  },
): Promise<{
  code: string
  msg: string
  data: any
}> {
  return service(
    {
      method: 'GET',
      url: '/assetManager/metadataManage/getSrouceList',
      params: data,
      headers,
    },
    MOCK_DATA.getSrouceList,
  )
}

/**
 *  - MDM013-查询数据表字段列表
 * @param {number} datasourceId - 数据源ID
 * @param {string} databaseName - 数据库名称
 * @param {string} tableName - 数据表名称
 * @param {string} opUser -
 * @param {string} opUserName -
 * @returns {any} - 返回一个解析为响应数据的Promise
 */

export async function getColumnListByDb(
  data: {
    datasourceId: number
    databaseName: string
    tableName: string
  },
  headers?: {
    opUser?: string
    opUserName?: string
  },
): Promise<any> {
  return service(
    {
      method: 'GET',
      url: '/assetManager/metadataManage/getColumnListByDb',
      params: data,
      headers,
    },
    MOCK_DATA.getColumnListByDb,
  )
}

/**
 *  - MDM014-查询数据表字段变更列表
 * @param {number} datasourceId - 数据源ID
 * @param {string} databaseName - 数据库名称
 * @param {string} tableName - 数据表名称
 * @param {string} pageIndex -
 * @param {string} pageSize -
 * @returns {any} - 返回一个解析为响应数据的Promise
 */

export async function getColumnModifyList(
  data: {
    datasourceId: number
    databaseName: string
    tableName: string
    columnName: string
  },
  headers?: {
    opUser?: string
    opUserName?: string
  },
): Promise<any> {
  return service(
    {
      method: 'GET',
      url: '/assetManager/metadataManage/getColumnModifyList',
      params: data,
      headers,
    },
    MOCK_DATA.getColumnModifyList,
  )
}
