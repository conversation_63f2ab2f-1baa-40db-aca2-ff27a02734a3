<template>
  <a-modal
    v-model:open="open"
    :confirm-loading="confirmLoading"
    :title="title"
    @ok="handleOk"
    @cancel="cancelHadle"
  >
    <!-- <a-checkbox class="checkboxStyle" v-model:checked="checked">是否更新相关节点</a-checkbox> -->
    <a-upload-dragger
      v-model:fileList="fileList"
      name="file"
      :before-upload="beforeUpload"
      @drop="handleDrop"
      :max-count="1"
    >
      <p class="ant-upload-drag-icon">
        <inbox-outlined></inbox-outlined>
      </p>
      <p class="ant-upload-text">单击或拖动文件到此区域进行上传</p>
    </a-upload-dragger>
  </a-modal>
</template>
<script lang="ts" setup>
import { ref } from 'vue'
import { InboxOutlined } from '@ant-design/icons-vue'
import { message, type UploadProps } from 'ant-design-vue'

const props = defineProps({
  uploadFunc: {
    type: Function,
    required: true,
  },
  fileName: {
    type: String,
    required: true,
    default: 'file',
  },
  title: {
    type: String,
    required: false,
    default: '上传文件',
  },
})
const fileList = ref<UploadProps['fileList']>([])
const confirmLoading = ref<boolean>(false)
const files = ref()

const uploading = ref<boolean>(false)
const open = defineModel<boolean>({ required: true })

const emits = defineEmits(['success', 'cancelHandle'])

const beforeUpload: UploadProps['beforeUpload'] = (file) => {
  fileList.value = [...(fileList.value || []), file]
  files.value = file
  return false
}

function handleDrop(e: DragEvent) {
  console.log(e)
}

function cancelHadle() {
  emits('cancelHandle')
}

function handleOk() {
  if (fileList.value && fileList.value.length > 0) {
    confirmLoading.value = true
    const formData = new FormData()
    formData.append(props.fileName, files.value)
    uploading.value = true
    props
      .uploadFunc(formData)
      .then((res: any) => {
        fileList.value = []
        emits('success', res)
      })
      .finally(() => {
        confirmLoading.value = false
      })
  } else {
    message.info('请上传菜单文件')
  }
}
</script>
<style lang="less">
.checkboxStyle {
  margin-bottom: 16px;
}
</style>
