<template>
  <a-form ref="formRef" layout="vertical" :model="formState" @finish="handleSubmit">
    <a-form-item
      name="dbDesc"
      label="数据库名称"
      :rules="[{ required: true, message: '数据库描述不能为空' }]"
    >
      <a-textarea placeholder="请输入数据库名称" v-model:value="formState.dbDesc" />
    </a-form-item>
    <a-form-item name="logicId">
      <template #label> 逻辑ID<strong>【环境数据源映射】</strong> </template>
      <a-input placeholder="请输入逻辑ID" v-model:value="formState.logicId" />
    </a-form-item>
    <a-form-item name="dbUsername" label="用户名">
      <a-input placeholder="请输入用户名" v-model:value="formState.dbUsername" />
    </a-form-item>
    <a-form-item name="dbPassword" label="密码">
      <a-input type="password" placeholder="请输入密码" v-model:value="formState.dbPassword" />
    </a-form-item>
    <a-form-item
      label="数据库连接路径"
      name="dbUrl"
      :rules="[{ required: true, message: '数据库连接路径不能为空' }]"
    >
      <a-textarea placeholder="请输入数据库连接路径" v-model:value="formState.dbUrl" />
    </a-form-item>
    <a-form-item name="dbOther" label="额外配置信息">
      <a-textarea placeholder="请输入额外配置信息" v-model:value="formState.dbOther" />
    </a-form-item>
    <a-form-item name="metaUrlInfo" label="配置信息" v-if="[2, 4, 11].includes(dbType)">
      <a-input v-model:value="formState.metaUrlInfo" placeholder="请输入配置信息" :rows="4" />
    </a-form-item>
  </a-form>
</template>

<script lang="ts" setup>
import { formatTime } from '@/views/setting-page/utils'
import { addDataSource, editDataSource } from '@/views/setting-page/api/index'

import { reactive, ref } from 'vue'
import { useUserStore } from '@/stores/user'
import { useRouter, useRoute } from 'vue-router'
import { message } from 'ant-design-vue'
import { useTabor } from 'vue3-tabor'

const formRef = ref()
const userStore = useUserStore()
const router = useRouter()
const route = useRoute()

const tabor = useTabor()

const formState = reactive({
  dbDesc: '',
  dbPassword: '',
  // dbType: 0,
  dbUrl: '',
  dbUsername: '',
  dbOther: '',
  logicId: '',
  metaUrlInfo: '',
})

const props = defineProps({
  dbType: {
    type: Number,
    default: 0,
  },
  currentSelectData: {
    type: Object,
    default: () => ({}),
  },
  currentRecord: {
    type: Object,
    default: () => ({}),
  },
})

const handleSubmit = async () => {
  const values = await formRef.value?.validate()
  const params = {
    createTime: formatTime(new Date()),
    dbDesc: '',
    dbPassword: '',
    dbType: props.dbType,
    dbUrl: '',
    dbUsername: '',
    dbOther: '',
    logicId: '',
    metaUrlInfo: '',
    ...values,
    ownerId: userStore.userInfo.loginAccount,
  }
  if (route.query.id) {
    const updateParams = {
      ...params,
      ...props.currentRecord,
      ...values,
      dbType: props.dbType,
    }
    if (updateParams.metaUrlInfo === '******') {
      delete updateParams.metaUrlInfo
    }
    if (/^\*+$/.test(updateParams.dbPassword)) {
      delete updateParams.dbPassword
    }
    editDataSource(updateParams).then((res) => {
      message.success('更新成功')
      router.replace('/setting/dataSource')
      tabor.close()
    })
    return
  }

  // 创建
  addDataSource(params).then((res) => {
    message.success('提交成功')
    router.replace('/setting/dataSource')
    tabor.close()
  })
}

const initFormData = () => {
  formState.dbDesc = props.currentRecord.dbDesc
  formState.dbPassword = props.currentRecord.dbPassword
  // formState.dbType = this.currentRecord.dbType
  formState.dbUrl = props.currentRecord.dbUrl
  formState.dbUsername = props.currentRecord.dbUsername
  formState.dbOther = props.currentRecord.dbOther
  formState.logicId = props.currentRecord.logicId
  formState.metaUrlInfo = props.currentRecord.metaUrlInfo
}

watch(
  () => props.currentSelectData,
  (value) => {
    nextTick(() => {
      formState.dbUrl = value.dbUrl || ''
      formState.dbOther = value.dbOther || ''
      if (route.query.id) {
        initFormData()
      }
    })
  },
)

defineExpose({
  handleSubmit,
})
</script>

<style scoped>
.step-two {
  padding: 20px;
}
</style>
