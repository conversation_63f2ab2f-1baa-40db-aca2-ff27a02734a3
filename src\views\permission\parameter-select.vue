<template>
  <div class="parameter-select">
    <cardBox title="系统参数" subTitle="系统运行参数配置管理">
      <Table
        :columns="columns"
        :getData="viewSystemParamsList"
        ref="tableRef"
        :searchFormState="formState"
      >
        <template #search>
          <a-form-item label="" name="参数说明" :rules="[{ message: '请输入参数说明' }]">
            <a-input v-model:value="formState.paramRemark" placeholder="请输入参数说明" />
          </a-form-item>
        </template>
      </Table>
    </cardBox>
    <parameter-modal
      :open="modalOpen"
      :disabled="disabled"
      @modalCancel="modalCancel"
      @modalHandleOk="modalHandleOk"
      ref="modalRef"
    ></parameter-modal>
  </div>
</template>
<script setup lang="ts">
import { Table } from '@fs/fs-components'
import { ParameterDTO } from '@/utils/column'
import { Button } from 'ant-design-vue/es/components'
import { viewSystemParamsList } from '@/api/services/permission'
import type { Column } from '@fs/fs-components/src/components/table/type'
import { ref, onBeforeMount, h, withDirectives, resolveDirective } from 'vue'
import cardBox from '@/components/card-box/card-box.vue'

let columns = ref<Column[]>([])
const tableRef = ref<InstanceType<typeof Table> | null>(null)
const modalRef = ref<any | null>(null)
const modalOpen = ref<boolean>(false)
const disabled = ref<boolean>(true)
const formState: Record<string, any> = ref({
  paramRemark: '',
})

function modalHandleOk() {
  modalOpen.value = false
  tableRef.value?.getTableData()
}

function modalCancel() {
  modalOpen.value = false
}

onBeforeMount(() => {
  initColumns()
})

async function initColumns() {
  columns.value = ParameterDTO.map((item) => {
    return {
      ...item,
      dataIndex: item.key,
    }
  })
  columns.value.push({
    key: 'action',
    title: '操作',
    customRender(data: Record<string, any>) {
      return [
        withDirectives(
          h(
            Button,
            {
              type: 'link',
              onClick() {
                modalOpen.value = true
                disabled.value = true
                modalRef.value?.show('edit', data.record)
              },
            },
            {
              default: () => '编辑',
            },
          ),
          [[resolveDirective('action'), '', 'edit']],
        ),
      ]
    },
  })
}
</script>
<style lang="less">
.parameter-select {
  height: 100%;
  background: white;
}
</style>
