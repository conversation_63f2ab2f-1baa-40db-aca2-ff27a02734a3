<template>
  <div class="list-content">
    <a-table
      ref="table"
      :rowKey="(record: Data, index: number) => index"
      :scroll="{ x: 1800, y: 650 }"
      :indentSize="40"
      :pagination="false"
      :data-source="rddList"
      :columns="rddColumns"
    >
      <template #bodyCell="{ column, record }: { column: any; record: Data }">
        <template v-if="column.key === 'isActive'">
          <span v-if="record.isActive" class="green-text"></span>
          <span v-else class="error-text"></span>
        </template>
      </template>
    </a-table>
  </div>
</template>
<script lang="ts" setup>
import { getExecutorsInfo } from '@/api/services/monitor-maintenance/spark-monitor'
import { reactive, ref, defineExpose, onMounted } from 'vue'
import { type Action } from './spark-monitor'

type Data = {
  id: string
  hostPort: string
  isActive: boolean
  rddBlocks: number
  onHeapMemory: number
  diskUsed: number
  totalCores: number
}

const pagination = reactive({
  pageIndex: 0,
  pageSize: 100,
})
const rddList = ref<Data[]>([])
const rddColumns = [
  {
    title: 'id',
    dataIndex: 'id',
    key: 'id',
  },
  {
    title: '地址',
    dataIndex: 'hostPort',
    key: 'hostPort',
    scopedSlots: { customRender: 'hostPort' },
  },
  {
    title: '状态',
    dataIndex: 'isActive',
    key: 'isActive',
    width: 60,
    scopedSlots: { customRender: 'isActive' },
  },
  {
    title: 'RDD Blacks',
    dataIndex: 'rddBlocks',
    key: 'rddBlocks',
    width: 80,
    scopedSlots: { customRender: 'rddBlocks' },
  },
  {
    title: '内存',
    dataIndex: 'onHeapMemory',
    key: 'onHeapMemory',
    scopedSlots: { customRender: 'onHeapMemory' },
  },
  {
    title: '磁盘使用',
    dataIndex: 'diskUsed',
    key: 'diskUsed',
    scopedSlots: { customRender: 'diskUsed' },
  },
  {
    title: '总核数',
    dataIndex: 'totalCores',
    key: 'totalCores',
    scopedSlots: { customRender: 'totalCores' },
  },
  {
    title: '正在运行Task',
    dataIndex: 'activeTasks',
    key: 'activeTasks',
    scopedSlots: { customRender: 'activeTasks' },
  },
  {
    title: '失败Task',
    dataIndex: 'failedTasks',
    key: 'failedTasks',
    scopedSlots: { customRender: 'failedTasks' },
  },
  {
    title: '完成Task',
    dataIndex: 'completedTasks',
    key: 'completedTasks',
    scopedSlots: { customRender: 'completedTasks' },
  },
  {
    title: 'TotalTasks',
    dataIndex: 'totalTasks',
    key: 'totalTasks',
    scopedSlots: { customRender: 'totalTasks' },
  },
  {
    title: '堆外内存',
    dataIndex: 'offHeapMemory',
    key: 'offHeapMemory',
    scopedSlots: { customRender: 'offHeapMemory' },
  },
  {
    title: '输入大小',
    dataIndex: 'totalInputBytes',
    key: 'totalInputBytes',
    scopedSlots: { customRender: 'totalInputBytes' },
  },
  {
    title: '读Shuffle',
    dataIndex: 'totalShuffleRead',
    key: 'totalShuffleRead',
    scopedSlots: { customRender: 'totalShuffleRead' },
  },
  {
    title: '写Shuffle',
    dataIndex: 'totalShuffleWrite',
    key: 'totalShuffleWrite',
    scopedSlots: { customRender: 'totalShuffleWrite' },
  },
]

async function getList() {
  const params = {
    pageIndex: pagination.pageIndex,
    pageSize: pagination.pageSize,
  }

  const resp = await getExecutorsInfo({ ...params })

  if (resp.data.obj) {
    rddList.value = resp.data.obj
    // this.rddList = res.data.obj.list
    // this.pagination.total = res.data.obj.total
  }
}

defineExpose({ getList } satisfies Action)

// 切换分页
function handelPageChange(pageIndex: number, pageSize: number) {
  pagination.pageIndex = pageIndex
  pagination.pageSize = pageSize
  getList()
}

// 切换分页大小
function handelShowSizeChange(pageIndex: number, pageSize: number) {
  pagination.pageIndex = pageIndex
  pagination.pageSize = pageSize
  getList()
}

onMounted(getList)
</script>
<style lang="less">
.green-text {
  display: inline-block;
  background-color: #2eff00;
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.error-text {
  display: inline-block;
  background-color: red;
  width: 12px;
  height: 12px;
  border-radius: 50%;
}
</style>
