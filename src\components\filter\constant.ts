/**
 * 表单类型
 */
export enum FormType {
  date = 'date',
  input = 'input',
  select = 'select',
  range = 'range',
  checkbox = 'checkbox',
  radio = 'radio',
  whiteSpace = 'whiteSpace',
}
export enum ResidentEnum {
  today = 'today',
  yesterday = 'yesterday',
  lastMonth = 'lastMonth',
  lastWeek = 'lastWeek',
}
export enum MorePeriodEnum {
  last7Days = 'last7Days',
  last30Days = 'last30Days',
  last3Months = 'last3Months',
  last12Months = 'last12Months',
  specificDate = 'specificDate',
}
export const options: { label: string; value: string }[] = [
  { label: '今天', value: ResidentEnum.today },
  { label: '昨天', value: ResidentEnum.yesterday },
  { label: '上周', value: ResidentEnum.lastWeek },
  { label: '上个月', value: ResidentEnum.lastMonth },
]
export const moreOptions: { label: string; value: string }[] = [
  { label: '最近7天', value: MorePeriodEnum.last7Days },
  { label: '最近30天', value: MorePeriodEnum.last30Days },
  { label: '最近3个月', value: MorePeriodEnum.last3Months },
  { label: '过去12个月', value: MorePeriodEnum.last12Months },
  { label: '具体日期', value: MorePeriodEnum.specificDate },
]

export enum ConditionEnum {
  equal = '=',
  notEqual = '!=',
  include = 'contains',
  uninclude = 'not_contains',
  startWith = 'start_with',
  endWith = 'end_with',
  isNull = 'IS_NULL',
  notNull = 'NOT_NULL',
  between = 'BETWEEN',
  biggerThan = '>',
  lessThan = '<',
  biggerThanOrEqual = '>=',
  lessThanOrEqual = '<=',
  before = 'before', // 用于日期，在...之前
  after = 'after', // 用于日期，在...之后
  current = 'current', // 用于日期，在当前时间
}
export const stringConditionOptions = [
  { value: ConditionEnum.equal, label: '等于' },
  { value: ConditionEnum.notEqual, label: '不等于' },
  { value: ConditionEnum.include, label: '包含' },
  { value: ConditionEnum.uninclude, label: '不包含' },
  { value: ConditionEnum.startWith, label: '以...开始' },
  { value: ConditionEnum.endWith, label: '以...结束' },
  { value: ConditionEnum.isNull, label: '为空' },
]
export const numberConditionOptions = [
  { value: ConditionEnum.equal, label: '等于' },
  { value: ConditionEnum.notEqual, label: '不等于' },
  { value: ConditionEnum.biggerThan, label: '大于' },
  { value: ConditionEnum.lessThan, label: '小于' },
  { value: ConditionEnum.between, label: '在...之间' },
  { value: ConditionEnum.biggerThanOrEqual, label: '大于或等于' },
  { value: ConditionEnum.lessThanOrEqual, label: '小于或等于' },
  { value: ConditionEnum.isNull, label: '为空' },
]
