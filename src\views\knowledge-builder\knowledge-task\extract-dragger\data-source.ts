export const columns = [
  {
    title: '属性(中文)',
    dataIndex: 'chinese',
    key: 'chinese',
  },
  {
    title: '属性(英文)',
    dataIndex: 'english',
    key: 'english',
  },
  {
    title: '值',
    key: 'type',
    dataIndex: 'type',
  },
]

export const nameMap = {
  id: '实体主键',
  name: '名称',
  description: '描述',
  semanticType: '语义类型',
}

export const relationshipColumns = [
  {
    title: '关系名称(中文)',
    dataIndex: 'chinese',
    key: 'chinese',
  },
  {
    title: '关系名称(英文)',
    dataIndex: 'english',
    key: 'english',
  },
  {
    title: '起点/终点',
    key: 'point',
    dataIndex: 'point',
  },
]

export const relationshipData = [
  // {
  //   key: '1',
  //   chinese: '描述',
  //   english: 'description',
  //   point: 'aaa'
  // },
]
