<template>
  <div class="task-group">
    <div class="task-content">
      <!-- <draggable 
  v-model="dataArr" 
  item-key="id">
  <template #item="{element,index}">
    <div>{{element.name}}{{ index }}</div>
   </template>
</draggable> -->
      <draggable handle=".mover" item-key="id" v-model="dataArr">
        <template #item="{ element, index }">
          <slot :cg="element" :cgi="index"> </slot>
        </template>
      </draggable>
    </div>
  </div>
</template>

<script setup lang="ts">
import Draggable from 'vuedraggable'
// import {conditionGroupRearrange} from "@/services/conditionGroup";
// import {rearrange} from "@/services/conditionGroupCondition";
// import {ruleSetRuleRearrange} from '@/services/rule'

defineProps(['title', 'group', 'dataList', 'id', 'handle', 'ruleId', 'ruleSetId'])
defineEmits(['update:loading'])

const dataArr = defineModel('dataList', { type: Array, default: () => [] })
</script>

<style lang="less">
.task-group {
  width: 100%;
  //padding: 8px 8px;
  //background-color: @background-color-light;
  //border-radius: 6px;
  //border: 1px solid @shadow-color;

  .task-head {
    //margin-bottom: 8px;
    .title {
      display: inline-block;

      span {
        display: inline-block;
        border-radius: 10px;
        margin: 0 8px;
        font-size: 12px;
        padding: 2px 6px;
        // background-color: @base-bg-color;
      }
    }

    .actions {
      display: inline-block;
      float: right;
      font-size: 18px;
      font-weight: bold;

      i {
        cursor: pointer;
      }
    }
  }
}

.dragable-ghost {
  //border: 1px dashed red;
  opacity: 1;
}

.dragable-chose {
  //border: 1px dashed red;
  opacity: 0.8;
}

.dragable-drag {
  //border: 1px dashed red;
  opacity: 1;
}
</style>
