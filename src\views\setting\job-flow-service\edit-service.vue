<template>
  <a-modal v-model:open="showDialog" :title="title" width="60%" :maskClosable="false">
    <a-form layout="vertical" :model="formItem" :rules="formRules" ref="ruleForm">
      <a-row :gutter="60">
        <a-col :span="12">
          <a-form-item :label="'服务类型'" name="dbType">
            <a-select v-model:value="formItem.serviceType" :placeholder="'请选择服务类型'">
              <a-select-option :value="0">{{ 'work' }}</a-select-option>
              <a-select-option :value="1">{{ 'master' }}</a-select-option>
              <a-select-option :value="5">{{ 'minwork' }}</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="work" name="workerId" v-show="formItem.serviceType === 5">
            <a-select v-model:value="formItem.workerId" :placeholder="'请选择work'">
              <a-select-option :value="item.id" v-for="(item, index) in workList" :key="index">{{
                item.serviceIp
              }}</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="IP" name="serviceIp">
            <a-input v-model:value="formItem.serviceIp" :placeholder="'请输入服务IP'" />
          </a-form-item>
          <a-form-item :label="'端口'" name="servicePort">
            <a-input-number
              style="width: 100%"
              v-model:value="formItem.servicePort"
              :placeholder="'请输入服务IP端口'"
            />
          </a-form-item>
          <a-form-item :label="'文件端口'" name="filePort" v-show="formItem.serviceType === 5">
            <a-input-number
              style="width: 100%"
              v-model:value="formItem.filePort"
              :placeholder="'请输入服务IP端口'"
            />
          </a-form-item>
          <a-form-item :label="'用户名'" name="hostUser">
            <a-input v-model:value="formItem.hostUser" :placeholder="'请输入用户名'" />
          </a-form-item>
          <a-form-item :label="'用户密码'" name="hostPwd">
            <a-input
              v-model:value="formItem.hostPwd"
              type="password"
              :placeholder="'请输入用户密码'"
            />
          </a-form-item>
          <a-form-item :label="'线程数'" name="threadNum">
            <a-input-number
              style="width: 100%"
              v-model:value="formItem.threadNum"
              :min="0"
              :placeholder="'请输入线程数'"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item :label="'逻辑ID'" name="logicId">
            <a-input v-model:value="formItem.logicId" :placeholder="'请输入逻辑ID'" />
          </a-form-item>
          <a-form-item :label="'SSH端口'" name="sshPort">
            <a-input v-model:value="formItem.sshPort" :placeholder="'请输入SSH端口'" />
          </a-form-item>
          <a-form-item :label="'数据文件路径'" name="dataPath">
            <a-input v-model:value="formItem.dataPath" :placeholder="'请输入数据文件路径'" />
          </a-form-item>
          <a-form-item :label="'部署路径'" name="hostPath">
            <a-input v-model:value="formItem.hostPath" :placeholder="'请输入部署路径'" />
          </a-form-item>
          <a-form-item :label="'执行文件路径'" name="dataPath">
            <a-input v-model:value="formItem.execPath" :placeholder="'请输入执行文件路径'" />
          </a-form-item>
          <a-form-item :label="'日志文件路径'" name="logPath">
            <a-input v-model:value="formItem.logPath" :placeholder="'请输入日志文件路径'" />
          </a-form-item>
          <a-form-item label="组件" name="componentId" v-show="formItem.serviceType === 5">
            <a-checkbox-group @change="onChangeCheckbox" v-model:value="checkList">
              <a-row :span="24" type="flex">
                <a-col :span="6" v-for="item in checkBoxOptions" :key="item.value">
                  <a-checkbox v-model:checked="item.value" :disabled="item.value === '4'">
                    {{ item.lable }}
                  </a-checkbox>
                </a-col>
              </a-row>
            </a-checkbox-group>
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
    <template #footer>
      <a-button key="back" @click="closeModal('formCancel')">取消</a-button>
      <a-button key="submit" type="primary" :loading="submitLoading" @click="formSubmit"
        >确定</a-button
      >
    </template>
  </a-modal>
</template>

<script setup lang="ts">
interface Api {
  workList: any[]
  open: boolean
  currentItem?: any
  modalType: string
}

import {
  addJobFlowService,
  editJobFlowService,
} from '@/api/services/monitor-maintenance/flow-config'
import { useUserStore } from '@/stores/user'
import { formatTime } from '@/views/setting-page/utils'
import { message } from 'ant-design-vue'
import type { Rule } from 'ant-design-vue/es/form'
import { storeToRefs } from 'pinia'
const userStore = useUserStore()
const { userInfo } = storeToRefs(userStore)

const props: Api = defineProps({
  open: {
    type: Boolean,
    default: false,
  },
  currentItem: { type: Object, required: false, defualt: {} },
  modalType: { type: String, required: true, default: 'add' },
  workList: { type: Array, required: true, default: () => [] },
})

const checkList = ref<string[]>(['1', '2', '4', '5', '6', '7', '11'])
const checkBoxOptions = ref<any[]>([
  // 1 批量互导 2 SQL组件 4 Shell组件 5 Python组件 6 数据质量组件 7 血缘关系组件  11 元数据组件
  { value: '1', lable: '批量互导' },
  { value: '2', lable: 'SQL组件' },
  { value: '4', lable: 'Shell组件' },
  { value: '5', lable: 'Python组件' },
  { value: '6', lable: '数据质量组件' },
  { value: '7', lable: '血缘关系组件' },
  { value: '11', lable: '元数据组件' },
])

const title = computed(() => {
  return props.modalType === 'add' ? '新增服务配置' : '修改服务配置'
})

const showDialog = ref(false)
const initFormItem = () => {
  return {
    serviceType: 0,
    serviceIp: '',
    logicId: '',
    sshPort: 22,
    servicePort: 8080,
    filePort: 12346,
    hostUser: '',
    hostPwd: '',
    // threadNum: '',
    execPath: '',
    hostPath: '',
    dataPath: '',
    logPath: '',
    componentId: '',
    workerId: null,
  }
}
const formItem = ref<{ [x: string]: any }>(initFormItem())
const submitLoading = ref(false)
const ruleForm = ref()
const formRules: Record<string, Rule[]> = {
  serviceIp: [
    {
      required: true,
      message: '请输入服务IP',
      trigger: 'blur',
    },
  ],
  servicePort: [
    {
      required: true,
      message: '请输入服务IP端口',
      trigger: 'blur',
    },
  ],
  hostUser: [
    {
      required: true,
      message: '请输入用户名',
      trigger: 'blur',
    },
  ],
  hostPwd: [
    {
      required: true,
      message: '请输入用户密码',
      trigger: 'blur',
    },
  ],
  threadNum: [
    {
      required: true,
      message: '请输入线程数',
      trigger: 'blur',
    },
  ],
  execPath: [
    {
      required: true,
      message: '请输入数据文件路径',
      trigger: 'blur',
    },
  ],
  hostPath: [
    {
      required: true,
      message: '请输入部署路径',
      trigger: 'blur',
    },
  ],
  dataPath: [
    {
      required: true,
      message: '请输入执行文件路径',
      trigger: 'blur',
    },
  ],
  logPath: [
    {
      required: true,
      message: '请输入日志文件路径',
      trigger: 'blur',
    },
  ],
  filePort: [{ required: false, message: '请输入文件端口', trigger: 'blur' }],
  workerId: [{ required: false, message: '请选择work', trigger: 'blur' }],
}
const emit = defineEmits(['success'])

watch(
  () => props.open,
  () => {
    showDialog.value = props.open
    if (props.open) {
      formItem.value =
        props.modalType === 'edit'
          ? JSON.parse(JSON.stringify(props.currentItem))
          : JSON.parse(JSON.stringify(initFormItem()))
      checkList.value =
        formItem.value.serviceType === 5 ? formItem.value.componentId.split(',') : []

      const flag = formItem.value.serviceType === 5 || false
      formRules.workerId &&
        formRules.workerId.forEach((el: any) => {
          el.required = flag
        })
      formRules.filePort &&
        formRules.filePort.forEach((el: any) => {
          el.required = flag
        })
    }
    console.log(props.currentItem, 'watch')
  },
  { immediate: true, deep: true },
)

const formSubmit = async () => {
  try {
    await ruleForm.value?.validate()
    submitLoading.value = true
    formItem.value.componentId = formItem.value.serviceType === 5 ? checkList.value.join() : ''
    if (formItem.value.serviceType !== 3) delete formItem.value.workerId
    const params = JSON.parse(JSON.stringify(formItem.value))
    params.ownerId = userStore.userInfo.loginAccount

    if (params.id && props.modalType === 'edit') {
      params.updateTime = formatTime(new Date())
      if (/^\*+$/.test(params.hostPwd)) delete params.hostPwd
      console.log(
        '%c [ params.hostPwd ]-190',
        'font-size:13px; background:#708e08; color:#b4d24c;',
        **********,
      )
      editJobFlowService(params)
        .then(() => {
          message.success('编辑成功')
          // closeModal('修改成功')
        })
        .finally(() => {
          submitLoading.value = false
        })
    } else {
      params.createTime = formatTime(new Date())
      addJobFlowService(params)
        .then(() => {
          message.success('新增成功')
        })
        .finally(() => {
          submitLoading.value = false
        })
    }

    closeModal('formSubmit')
  } catch (error) {
    console.error('提交失败:', error)
  } finally {
    submitLoading.value = false
  }
}

const onChangeCheckbox = (checkedList: []) => {
  formItem.value.componentId = checkedList.join()
}

const closeModal = (btnType: string) => {
  // console.log(msg, 'closeModal')
  showDialog.value = false
  emit('success', btnType)
  // message.success(msg)
  // message.success('操作成功')
}
</script>
