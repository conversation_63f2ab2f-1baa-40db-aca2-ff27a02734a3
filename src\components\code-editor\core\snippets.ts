import { functions, keyword, operators } from './keywords'
import { editor, Position } from 'monaco-editor'
// @ts-ignore
import { language as sqlLanguage } from 'monaco-editor/esm/vs/basic-languages/sql/sql.js'
// @ts-ignore
import { language as shellLanguage } from 'monaco-editor/esm/vs/basic-languages/shell/shell.js'
// @ts-ignore
import { language as pythonLanguage } from 'monaco-editor/esm/vs/basic-languages/python/python.js'

export class dbsDTO {
  tables?: { tableColumns?: any[]; tblName?: string; tblComment?: string }[]
  dbName?: string
}

export class tableDTO {
  tableColumns?: any[]
  tblName?: string
  tblComment?: string
}

export default class Snippets {
  private monaco: any
  private SORT_TEXT: {
    Database: string
    Table: string
    Column: string
    Keyword: string
    Operator: string
    Function: string
    Variable: string
  }
  private customKeywords: any[]
  private keywords: any[] // 关键字
  private functions: any[] // 函数
  private operators: any[] // 操作符
  private dbSchema: Array<dbsDTO>
  private onInputTableColumn: any
  private onInputTableAila: any
  private isShowDb: boolean

  constructor(
    monaco: any,
    customKeywords: any[] = [],
    onInputTableColumn: any,
    onInputTableAila: any,
    dbSchema = [{ dbName: '', tables: [{ tblName: '', tblComment: '', tableColumns: [] }] }],
    isShowDb = false,
  ) {
    this.SORT_TEXT = {
      Database: '0',
      Table: '1',
      Column: '2',
      Keyword: '3',
      Operator: '4',
      Function: '5',
      Variable: '6',
    }
    this.customKeywords = customKeywords
    this.keywords = [...new Set([...keyword, ...customKeywords])]
    this.operators = [...new Set(operators)]
    this.functions = [...new Set(functions)]
    this.dbSchema = dbSchema
    this.monaco = monaco
    this.getKeywordSuggest = this.getKeywordSuggest.bind(this)
    this.getOperatorsSuggest = this.getOperatorsSuggest.bind(this)
    this.getFunctionsSuggest = this.getFunctionsSuggest.bind(this)
    this.getTableSuggest = this.getTableSuggest.bind(this)
    this.getTableColumnSuggest = this.getTableColumnSuggest.bind(this)
    // 字段联想callback
    this.onInputTableColumn = onInputTableColumn
    // <别名>.<字段>联想callback
    this.onInputTableAila = onInputTableAila
    this.isShowDb = isShowDb
  }

  /**
   * 动态设置数据库表&&数据库字段
   * @param {*} dbSchema 数据库schema
   * @example [{ dbName: '', tables: [{ tblName: '', tableColumns: [] }] }]
   */
  setDbSchema(dbSchema: Array<dbsDTO>) {
    this.dbSchema = dbSchema
  }

  /**
   * monaco提示方法
   * @param {*} model
   * @param {*} position
   */
  async provideCompletionItems(
    model: editor.ITextModel,
    position: Position,
    optionLanguage: string,
  ) {
    if (optionLanguage === 'sql') {
      const { lineNumber, column } = position
      // 光标前文本
      const textBeforePointer = model.getValueInRange({
        startLineNumber: lineNumber,
        startColumn: 0,
        endLineNumber: lineNumber,
        endColumn: column,
      })
      const textBeforePointerMulti = model.getValueInRange({
        startLineNumber: 1,
        startColumn: 0,
        endLineNumber: lineNumber,
        endColumn: column,
      })
      const textAfterPointerMulti = model.getValueInRange({
        startLineNumber: lineNumber,
        startColumn: column,
        endLineNumber: model.getLineCount(),
        endColumn: model.getLineMaxColumn(model.getLineCount()),
      })
      // const nextTokens = textAfterPointer.trim().split(/\s+/)
      // const nextToken = nextTokens[0].toLowerCase()
      const tokens = textBeforePointer.trim().split(/\s+/)
      const lastToken = tokens[tokens.length - 1].toLowerCase()
      const keywordLenovo: any = [
        ...this.getKeywordSuggest(),
        ...this.getOperatorsSuggest(),
        ...this.getFunctionsSuggest(),
      ]
      // 数据库名联想
      if (lastToken === 'database') {
        return {
          suggestions: this.getDataBaseSuggest(),
        }
        // <库名>.<表名> || <别名>.<字段>
      } else if (lastToken.endsWith('.')) {
        // 去掉点后的字符串
        const tokenNoDot = lastToken.slice(0, lastToken.length - 1)
        if (this.dbSchema.find((db: dbsDTO) => db.dbName === tokenNoDot.replace(/^.*,/g, ''))) {
          // <库名>.<表名>联想
          return {
            suggestions: [...this.getTableSuggestByDbName(tokenNoDot.replace(/^.*,/g, ''))],
          }
        } else if (
          this.getTableNameAndTableAlia(
            textBeforePointerMulti.split(';')[textBeforePointerMulti.split(';').length - 1] +
              textAfterPointerMulti.split(';')[0],
          )
        ) {
          const tableInfoList = this.getTableNameAndTableAlia(
            textBeforePointerMulti.split(';')[textBeforePointerMulti.split(';').length - 1] +
              textAfterPointerMulti.split(';')[0],
          )
          const currentTable = tableInfoList.find(
            (item: any) => item.tableAlia === tokenNoDot.replace(/^.*,/g, ''),
          )
          // <别名>.<字段>联想
          if (currentTable && currentTable.tableName) {
            return {
              suggestions: await this.getTableColumnSuggestByTableAlia(currentTable.tableName),
            }
          } else {
            return {
              suggestions: [],
            }
          }
        } else {
          return {
            suggestions: [],
          }
        }
      } else if (
        lastToken === 'from' ||
        lastToken === 'join' ||
        /(from|join)\s+.*?\s?,\s*$/.test(textBeforePointer.replace(/.*?\(/gm, '').toLowerCase())
      ) {
        // 库名联想
        const databases = this.getTableSuggest()
        return {
          suggestions: databases,
        }
        // 字段联想
      } else if (
        [
          'select',
          'where',
          'order by',
          'group by',
          'by',
          'and',
          'or',
          'having',
          'distinct',
          'on',
        ].includes(lastToken.replace(/.*?\(/g, '')) ||
        (lastToken.endsWith('.') && !this.dbSchema.find((db) => `${db.dbName}.` === lastToken)) ||
        /(select|where|order by|group by|by|and|or|having|distinct|on)\s+.*?\s?,\s*$/.test(
          textBeforePointer.toLowerCase(),
        )
      ) {
        let tableName = ''
        const sql = model.getValue()
        const res = sql.toLowerCase().match(/from\s+([\w.]+)\s/)
        if (res) {
          tableName = res[1]
        }
        return {
          suggestions: await this.getTableColumnSuggest(tableName),
        }
        // 自定义字段联想
      } else if (this.customKeywords.toString().includes(lastToken)) {
        return {
          suggestions: this.getCustomSuggest(lastToken.startsWith('$')),
        }
        // 默认联想
      } else {
        return {
          suggestions: this.isShowDb
            ? [...this.getDataBaseSuggest(), ...this.getTableSuggest(), ...keywordLenovo]
            : [...this.getTableSuggest(), ...keywordLenovo],
        }
      }
    } else {
      const suggestions: Array<any> = []
      // 关键字插入
      let language = null
      switch (optionLanguage) {
        // case 'sql':
        //   language = sqlLanguage
        //   break
        case 'shell':
          language = shellLanguage
          break
        case 'python':
          language = pythonLanguage
          break
      }
      language.keywords.forEach((item: string) => {
        suggestions.push({
          label: item,
          kind: this.monaco.languages.CompletionItemKind.Keyword, // 分类，进入源码看分类
          insertText: item,
        })
      })
      return {
        suggestions: suggestions,
      }
    }
  }

  /**
   * 获取自定义联想建议
   */
  getCustomSuggest(startsWith$: any) {
    return this.customKeywords.map((keyword: any) => ({
      label: keyword,
      kind: this.monaco.languages.CompletionItemKind.Keyword,
      detail: '',
      sortText: this.SORT_TEXT.Keyword,
      // Fix插入两个$符号
      insertText: startsWith$ ? keyword.slice(1) : keyword,
    }))
  }

  /**
   * 获取所有字段
   */
  getAllTableColumn() {
    const tableColumns: any = []
    this.dbSchema.forEach((db: dbsDTO) => {
      db.tables!.forEach((table: { tableColumns?: any[]; tblName?: string }) => {
        table.tableColumns!.forEach((field: any) => {
          tableColumns.push({
            label: field.columnName ? field.columnName : '',
            kind: this.monaco.languages.CompletionItemKind.Module,
            detail: `<字段>`,
            sortText: this.SORT_TEXT.Column,
            insertText: field.columnName ? field.columnName : '',
          })
        })
      })
    })
    return tableColumns
  }

  /**
   * 获取数据库库名联想建议
   */
  getDataBaseSuggest() {
    return this.dbSchema.map((db: dbsDTO) => {
      return {
        label: db.dbName ? db.dbName : '',
        kind: this.monaco.languages.CompletionItemKind.Class,
        detail: `<数据库>`,
        sortText: this.SORT_TEXT.Database,
        insertText: db.dbName ? db.dbName : '',
      }
    })
  }

  /**
   * 获取关键字联想建议
   * @param {*} keyword
   */
  getKeywordSuggest() {
    return this.keywords.map((keyword: any) => ({
      label: keyword,
      kind: this.monaco.languages.CompletionItemKind.Keyword,
      detail: '<关键字>',
      sortText: this.SORT_TEXT.Keyword,
      insertText: keyword,
    }))
  }

  /**
   * 获取操作符联想建议
   * @param {*} operator
   */
  getOperatorsSuggest() {
    return this.operators.map((operator: any) => ({
      label: operator,
      kind: this.monaco.languages.CompletionItemKind.Operator,
      detail: '<操作符>',
      sortText: this.SORT_TEXT.Operator,
      insertText: operator,
    }))
  }

  /**
   * 获取函数联想建议
   * @param {*} Function
   */
  getFunctionsSuggest() {
    return this.functions.map((Function: any) => ({
      label: Function,
      kind: this.monaco.languages.CompletionItemKind.Function,
      detail: '<函数>',
      sortText: this.SORT_TEXT.Function,
      insertText: Function,
    }))
  }

  /**
   * 获取数据库表名建议
   */
  getTableSuggest() {
    const tables: any = []
    this.dbSchema.forEach((db: dbsDTO) => {
      db.tables!.forEach(
        (table: { tableColumns?: any[]; tblName?: string; tblComment?: string }) => {
          tables.push({
            label: table.tblName ? table.tblName : '',
            kind: this.monaco.languages.CompletionItemKind.Struct,
            detail: `<表> ${db.dbName} ${table.tblComment ? table.tblComment : ''}`,
            sortText: this.SORT_TEXT.Table,
            insertText: table.tblName ? table.tblName : '',
            documentation: table.tblComment ? table.tblComment : '',
          })
        },
      )
    })
    return tables
  }

  /*
   * <库名>.<表名>联想
   * */
  getTableSuggestByDbName(dbName: string) {
    const currentDb = this.dbSchema.find((db: dbsDTO) => db.dbName === dbName)
    const tables: any = []
    if (currentDb) {
      currentDb.tables!.forEach((table: tableDTO) => {
        tables.push({
          label: table.tblName ? table.tblName : '',
          kind: this.monaco.languages.CompletionItemKind.Struct,
          detail: `<表> ${currentDb.dbName} ${table.tblComment ? table.tblComment : ''}`,
          sortText: this.SORT_TEXT.Table,
          insertText: table.tblName ? table.tblName : '',
          documentation: table.tblComment ? table.tblComment : '',
        })
      })
    }
    return tables
  }

  /**
   * 获取所有表字段
   * @param {*} table
   * @param {*} column
   */
  async getTableColumnSuggest(tableName?: string) {
    const defaultFields: any[] = []
    this.dbSchema.forEach((db: dbsDTO) => {
      db.tables!.filter((t) => tableName === '' || t.tblName === tableName).forEach(
        (table: tableDTO) => {
          table.tableColumns &&
            table.tableColumns.forEach((field: any) => {
              defaultFields.push({
                label: field.columnName ? field.columnName : '',
                kind: this.monaco.languages.CompletionItemKind.Field,
                // detail: `<字段> ${field.commentName ? field.commentName : ''} <${field.columnType}>`,
                detail: `<字段> ${field.commentName ? field.commentName : ''}`,
                sortText: this.SORT_TEXT.Column,
                insertText: field.columnName ? field.columnName : '',
                documentation: {
                  value: `
                ### 数据库: ${field.dbName}
                ### 表: ${field.tblName}
                ### 注释: ${field.commentName ? field.commentName : ''}
              `,
                },
              })
            })
        },
      )
    })
    const asyncFields: any = []
    if (typeof this.onInputTableColumn === 'function') {
      const fileds = await this.onInputTableColumn()
      fileds.forEach((field: any) => {
        asyncFields.push({
          label: field.columnName ? field.columnName : '',
          kind: this.monaco.languages.CompletionItemKind.Field,
          detail: `<字段> ${field.commentName ? field.commentName : ''} <${field.columnType}>`,
          sortText: this.SORT_TEXT.Column,
          insertText: field.columnName ? field.columnName : '',
          documentation: {
            value: `
              ### 数据库: ${field.dbName}
              ### 表: ${field.tblName}
              ### 注释: ${field.commentName ? field.commentName : ''}
            `,
          },
        })
      })
    }
    return [...defaultFields, ...asyncFields]
  }

  /**
   * 根据别名获取所有表字段
   * @param {*} table
   * @param {*} column
   */
  async getTableColumnSuggestByTableAlia(tableName: string) {
    const defaultFields: any[] = []
    this.dbSchema.forEach((db: dbsDTO) => {
      db.tables!.forEach((table: tableDTO) => {
        table.tableColumns &&
          table.tableColumns.forEach((field: any) => {
            defaultFields.push({
              label: field.columnName ? field.columnName : '',
              kind: this.monaco.languages.CompletionItemKind.Field,
              detail: `<字段> ${field.commentName ? field.commentName : ''} <${field.columnType}>`,
              sortText: this.SORT_TEXT.Column,
              insertText: field.columnName ? field.columnName : '',
              documentation: {
                value: `
                ### 数据库: ${field.dbName}
                ### 表: ${field.tblName}
                ### 注释: ${field.commentName ? field.commentName : ''}
              `,
              },
            })
          })
      })
    })
    const asyncFields: any[] = []
    if (typeof this.onInputTableAila === 'function') {
      const fileds: any[] = await this.onInputTableAila(tableName)
      fileds.forEach((field: any) => {
        asyncFields.push({
          label: field.columnName ? field.columnName : '',
          kind: this.monaco.languages.CompletionItemKind.Field,
          detail: `<字段> ${field.commentName ? field.commentName : ''} <${field.columnType}>`,
          sortText: this.SORT_TEXT.Column,
          insertText: field.columnName ? field.columnName : '',
          documentation: {
            value: `
              ### 数据库: ${field.dbName}
              ### 表: ${field.tblName}
              ### 注释: ${field.commentName ? field.commentName : ''}
            `,
          },
        })
      })
    }
    return [...defaultFields, ...asyncFields]
  }

  /**
   * 获取sql中所有的表名和别名
   * @param {*} sqlText SQL字符串
   */
  getTableNameAndTableAlia(sqlText: any) {
    const regTableAliaFrom =
      /(^|(\s+))from\s+([^\s]+(\s+|(\s+as\s+))[^\s]+(\s+|,)\s*)+(\s+(where|left|right|full|join|inner|union))?/gi
    const regTableAliaJoin = /(^|(\s+))join\s+([^\s]+)\s+(as\s+)?([^\s]+)\s+on/gi
    const regTableAliaFromList = sqlText.match(regTableAliaFrom)
      ? sqlText.match(regTableAliaFrom)
      : []
    const regTableAliaJoinList = sqlText.match(regTableAliaJoin)
      ? sqlText.match(regTableAliaJoin)
      : []
    const strList = [
      ...regTableAliaFromList.map((item: any) =>
        item
          .replace(/(^|(\s+))from\s+/gi, '')
          .replace(/\s+(where|left|right|full|join|inner|union)((\s+.*?$)|$)/gi, '')
          .replace(/\s+as\s+/gi, ' ')
          .trim(),
      ),
      ...regTableAliaJoinList.map((item: any) =>
        item
          .replace(/(^|(\s+))join\s+/gi, '')
          .replace(/\s+on((\s+.*?$)|$)/, '')
          .replace(/\s+as\s+/gi, ' ')
          .trim(),
      ),
    ]
    const tableList: any = []
    strList.map((tableAndAlia) => {
      tableAndAlia.split(',').forEach((item: any) => {
        const tableName = item.trim().split(/\s+/)[0]
        const tableAlia = item.trim().split(/\s+/)[1]
        tableList.push({
          tableName,
          tableAlia,
        })
      })
    })
    return tableList
  }
}
