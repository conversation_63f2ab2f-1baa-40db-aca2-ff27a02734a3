import service from '@/api'
import { ASSETMANAGER } from '@/api/base'
import type { VNode } from 'vue'

export interface AssetResponse<T = any> {
  data: T
  code: string
  msg: string
  result: string
}

export interface ITagCategory {
  categoryId?: number
  categoryName: string
  description: string
}

export interface ITag {
  labelId?: number
  labelName: string
  description: string
  categoryId: number
}

export type ITagForm = Pick<ITag, 'labelName' | 'description'>

export interface ITagCategoryMenu {
  key: string
  icon: () => VNode
  label: string
  desc: string
}

export interface ITagParams {
  categoryId: number
  labelId?: number
  labelName?: string
}

// 获取分类列表
export function getTagCategoryList(): Promise<AssetResponse<ITagCategory[]>> {
  return service({
    method: 'GET',
    url: `${ASSETMANAGER}/tagsModule/getDataAssetsCategory`,
  })
}

// 新增分类
export function addTagCategoryItem(data: ITagCategory): Promise<AssetResponse<string>> {
  return service({
    method: 'post',
    url: `${ASSETMANAGER}/tagsModule/addDataAssetsCategory`,
    data: data,
  })
}

// 修改分类
export function editTagCategoryItem(data: ITagCategory): Promise<AssetResponse<string>> {
  return service({
    method: 'post',
    url: `${ASSETMANAGER}/tagsModule/updateDataAssetsCategory`,
    data: data,
  })
}

// 删除分类
export function deleteTagCategoryItem(categoryId: number): Promise<AssetResponse<string>> {
  return service({
    method: 'post',
    url: `${ASSETMANAGER}/tagsModule/delDataAssetsCategory`,
    data: { categoryId },
  })
}

// 获取标签列表
export function getTagList(params: ITagParams): Promise<AssetResponse<ITag[]>> {
  return service({
    method: 'GET',
    url: `${ASSETMANAGER}/dataLabelModule/getDataAssetsLabel`,
    params,
  })
}

// 新增标签
export function addTagItem(data: ITag): Promise<AssetResponse<string>> {
  return service({
    method: 'post',
    url: `${ASSETMANAGER}/dataLabelModule/addDataAssetsLabel`,
    data: data,
  })
}

// 修改标签
export function editTagItem(data: ITag): Promise<AssetResponse<string>> {
  return service({
    method: 'post',
    url: `${ASSETMANAGER}/dataLabelModule/updateDataAssetsLabel`,
    data: data,
  })
}

// 删除标签
export function deleteTagItem(labelId: number): Promise<AssetResponse<string>> {
  return service({
    method: 'post',
    url: `${ASSETMANAGER}/dataLabelModule/delDataAssetsLabel`,
    data: { labelId },
  })
}
