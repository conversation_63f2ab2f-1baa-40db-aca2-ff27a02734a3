export const COLUMS = [
  {
    title: '指标配置模板编号',
    dataIndex: 'formId',
    key: 'formId',
    width: 100,
  },
  {
    title: '指标配置模板名称',
    dataIndex: 'formName',
    key: 'formName',
    width: 120,
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 60,
    // customRender: (text: any) => (text === 1 ? '已提交' : '编辑中'),
  },
  {
    title: '备注',
    dataIndex: 'remarks',
    key: 'remarks',
    width: 100,
  },
  {
    title: '创建人',
    dataIndex: 'createUser',
    key: 'createUser',
    width: 100,
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: 100,
  },
  {
    title: '更新人',
    dataIndex: 'updateUser',
    key: 'updateUser',
    width: 100,
  },
  {
    title: '更新人名称',
    dataIndex: 'updateUsername',
    key: 'updateUsername',
    width: 100,
  },
  {
    title: '更新时间',
    dataIndex: 'updateTime',
    key: 'updateTime',
    width: 100,
  },
  {
    title: '是否已发布',
    dataIndex: 'isRelease',
    key: 'isRelease',
    width: 100,
    // customRender: (text: any) => (text === 1 ? '是' : '否'),
  },
  {
    title: '当前版本号',
    dataIndex: 'currentVersion',
    key: 'currentVersion',
    width: 100,
  },
  {
    title: '操作',
    key: 'action',
    width: 300,
    fixed: 'right',
    dataIndex: 'action',
  },
]
