<template>
  <a-modal v-model:open="visable" title="">
    <div class="Publish">
      <a-card title="规则预览发布" :bordered="false">
        <div :ops="ops" style="width: 100%; height: 100%">
          <div>
            <br />
            <a-timeline>
              <a-timeline-item v-for="(cg, cgi) in generalRule.conditionGroup" :key="cg.id">
                <span style="color: #606266; font-size: 14px" v-if="0 === cgi">如果</span>
                <span style="color: #606266; font-size: 14px" v-else>或者</span>
                <div
                  v-for="cgc in cg.conditionGroupCondition"
                  style="margin-left: 20px"
                  :key="cgc.id"
                >
                  <a-alert
                    style="border: none; padding: 6px 6px 6px 6px; margin-bottom: 10px"
                    class="conditionItem"
                  >
                    <template v-slot:description>
                      <p style="margin-bottom: 0; margin-top: 0">
                        <a-tag
                          color="blue"
                          style="padding: 0 2px 2px 2px; font-size: 13px; margin-bottom: 3px"
                        >
                          （{{ cgc.condition.name }}）
                        </a-tag>
                        <a-tag
                          color="cyan"
                          style="padding: 0 2px 2px 2px; font-size: 13px; margin-bottom: 3px"
                        >
                          {{ getTypeName(cgc.condition.config.leftValue) }}
                        </a-tag>
                        {{ getViewValue(cgc.condition.config.leftValue) }}
                        &nbsp;
                        <a-tag
                          color="orange"
                          style="padding: 0 2px 2px 2px; font-size: 13px; margin-bottom: 3px"
                        >
                          {{ getSymbolExplanation(cgc.condition.config.symbol) }}
                        </a-tag>
                        <!-- <a-tag
                          color="cyan"
                          style="padding: 0 2px 2px 2px; font-size: 13px; margin-bottom: 3px"
                        >
                          {{ getTypeName(cgc.condition.config.rightValue) }}
                        </a-tag> -->
                        {{ cgc.condition.config.rightValue.value }}
                      </p>
                    </template>
                  </a-alert>
                </div>
              </a-timeline-item>
            </a-timeline>
            <span style="color: #606266; font-size: 14px">返回</span>
            <div style="margin-left: 20px; margin-top: 3px">
              <a-alert
                :closable="false"
                type="success"
                style="border: none; padding: 6px 6px 6px 6px; margin-bottom: 10px"
              >
                <template v-slot:description>
                  <p style="margin-bottom: 0; margin-top: 0">
                    <a-tag
                      color="cyan"
                      style="padding: 0 2px 2px 2px; font-size: 13px; margin-bottom: 3px"
                    >
                      {{ getTypeName(generalRule.action) }}
                    </a-tag>
                    {{ getActionView(generalRule.action) }}
                  </p>
                </template>
              </a-alert>
            </div>
            <span v-if="generalRule.conditionGroup.length !== 0">
              <span style="color: #606266; font-size: 14px">否则返回</span>
              <br />
              <div style="margin-left: 20px; margin-top: 3px">
                <a-alert
                  :closable="false"
                  type="warning"
                  style="border: none; padding: 6px 6px 6px 6px; margin-bottom: 10px"
                >
                  <template v-slot:description>
                    <p style="margin-bottom: 0; margin-top: 0">
                      <a-tag
                        color="cyan"
                        v-if="getDefaultActionView(generalRule.defaultAction) !== 'null'"
                        style="padding: 0 2px 2px 2px; font-size: 13px; margin-bottom: 3px"
                      >
                        {{ getTypeName(generalRule.defaultAction) }}
                      </a-tag>
                      {{ getDefaultActionView(generalRule.defaultAction) }}
                    </p>
                  </template>
                </a-alert>
              </div>
            </span>
          </div>
        </div>
      </a-card>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
// import { runTest, viewGeneralRule } from '@/services/generalRule'
import { getTypeName } from '@/utils/value-type'
import { getSymbolExplanation } from '@/utils/symbol'

const visable = ref(false)
const ops = {
  vuescroll: {},
  scrollPanel: {},
  rail: {
    keepShow: true,
  },
  bar: {
    hoverStyle: true,
    onlyShowBarOnScroll: false,
    background: '#F5F5F5',
    opacity: 0.5,
    'overflow-x': 'hidden',
  },
}

const generalRule = ref<any>({
  id: 226,
  name: null,
  code: 'ccc2222',
  description: null,
  workspaceCode: 'test',
  ruleId: null,
  conditionGroup: [],
  action: {
    value: undefined,
    valueName: null,
    valueType: 'NUMBER',
    type: null,
    loading: false,
    searchSelect: {
      data: [],
      value: undefined,
    },
  },
  defaultAction: {
    enableDefaultAction: 1,
    value: undefined,
    valueName: null,
    valueType: 'NUMBER',
    type: null,
    loading: false,
    searchSelect: {
      data: [],
      value: undefined,
    },
  },
})

const getActionView = (action: any) => {
  console.log('🚀 ~ getActionView ~ action:', action)
  if (action.variableValue != null) {
    return action.variableValue
  }
  if (action.valueName === '') {
    return '空'
  }
  return action.value
}

const getDefaultActionView = (defaultAction: any) => {
  if (defaultAction.enableDefaultAction === 0) {
    return getActionView(defaultAction)
  } else {
    return 'null'
  }
}

const getViewValue = (v: any) => {
  if (v.type === 2) {
    return v.value
  }
  if (v.variableValue !== null) {
    return v.variableValue
  }
  if (v.valueName !== null) {
    return v.valueName
  }
  return v.value
}

const getRuleConfig = () => {
  // viewGeneralRule({
  //   id: generalRule.value.id,
  //   status: 1,
  // }).then((res: any) => {
  //   let da = res.data.data
  //   generalRule.value = da
  //   let param = {}
  //   if (da.parameters != null && da.parameters.length !== 0) {
  //     da.parameters.forEach((e: any) => {
  //       param[e.code] = '略'
  //     })
  //   }
  //   request.value.requestJson = JSON.stringify(
  //     {
  //       code: da.code,
  //       workspaceCode: da.workspaceCode,
  //       accessKeyId: '略',
  //       accessKeySecret: '略',
  //       param: param,
  //     },
  //     null,
  //     6,
  //   )
  //   request.value.param = da.parameters
  // })
}

const show = (data: any) => {
  let da = data
  generalRule.value = da
  let param: any = {}
  if (da.parameters != null && da.parameters.length !== 0) {
    da.parameters.forEach((e: any) => {
      param[e.code] = '略'
    })
  }
  visable.value = true
}

defineExpose({
  show,
})

onMounted(() => {
  getRuleConfig()
})
</script>

<style lang="less">
.runTest {
  .ant-popover-inner-content {
    padding: 0 0;
  }
}

// 滚动条位置
/deep/ .__bar-is-vertical {
  right: -1px !important;
}

// 隐藏横向滚动条
/deep/ .__bar-is-horizontal {
  display: none !important;
}
</style>
