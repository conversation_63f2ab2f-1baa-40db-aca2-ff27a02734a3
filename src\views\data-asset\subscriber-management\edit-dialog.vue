<template>
  <!-- 订阅者弹框 -->
  <a-modal
    v-model:open="showDialog"
    :title="`${dialogType === 'edit' ? '编辑' : '新增'}订阅者`"
    @ok="okHandle"
    @cancel="canceHandle"
    width="600px"
  >
    <a-form
      :model="formData"
      name="basic"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 16 }"
      autocomplete="off"
      ref="formRef"
    >
      <a-form-item
        label="订阅者名称"
        name="serverName"
        :rules="[{ required: true, message: '请输入订阅者名称' }]"
      >
        <a-input v-model:value="formData.serverName" placeholder="请输入订阅者名称" />
      </a-form-item>
      <a-form-item
        label="订阅类型"
        name="apiType"
        :rules="[{ required: true, message: '请选择订阅类型' }]"
      >
        <a-select v-model:value="formData.apiType" placeholder="请选择订阅类型">
          <a-select-option value="HTTP">HTTP</a-select-option>
          <a-select-option value="EMAIL">EMAIL</a-select-option>
        </a-select>
      </a-form-item>

      <!-- HTTP 类型表单项 -->
      <template v-if="formData.apiType === 'HTTP'">
        <a-form-item
          label="服务地址"
          name="apiHost"
          :rules="[{ required: true, message: '请输入服务地址' }]"
        >
          <a-input v-model:value="formData.apiHost" placeholder="请输入服务地址" />
        </a-form-item>
        <a-form-item
          label="服务路径"
          name="apiPath"
          :rules="[{ required: true, message: '请输入服务路径' }]"
        >
          <a-input v-model:value="formData.apiPath" placeholder="请输入服务路径" />
        </a-form-item>
        <a-form-item label="API头参参数配置" name="apiHeaders">
          <a-input v-model:value="formData.apiHeaders" placeholder="请输入API头参参数配置" />
        </a-form-item>
      </template>

      <!-- EMAIL 类型表单项 -->
      <template v-else-if="formData.apiType === 'EMAIL'">
        <a-form-item
          label="收件人邮箱"
          name="recEmail"
          :rules="[{ required: true, message: '请输入收件人邮箱' }]"
        >
          <a-input v-model:value="formData.recEmail" placeholder="请输入收件人邮箱" />
        </a-form-item>
        <a-form-item
          label="服务器host"
          name="host"
          :rules="[{ required: true, message: '请输入服务器host' }]"
        >
          <a-input v-model:value="formData.host" placeholder="请输入服务器host" />
        </a-form-item>
        <a-form-item
          label="发送地址"
          name="fromAddress"
          :rules="[{ required: true, message: '请输入发送地址' }]"
        >
          <a-input v-model:value="formData.fromAddress" placeholder="请输入发送地址" />
        </a-form-item>
        <a-form-item
          label="发送邮箱"
          name="userName"
          :rules="[{ required: true, message: '请输入发送邮箱' }]"
        >
          <a-input v-model:value="formData.userName" placeholder="请输入发送邮箱" />
        </a-form-item>
        <a-form-item
          label="邮箱密码"
          name="password"
          :rules="[{ required: true, message: '请输入邮箱密码' }]"
        >
          <a-input v-model:value="formData.password" placeholder="请输入邮箱密码" />
        </a-form-item>
      </template>
    </a-form>
  </a-modal>
</template>

<script lang="ts" setup>
import {
  type AssetResponse,
  type ISubscriberForm,
  type ISubscriber,
} from '@/api/services/data-asset/subscriber-management'
import { message } from 'ant-design-vue'
import {
  addDataSubscripServer,
  updateDataSubscripServer,
} from '@/api/assetmanager/datasubscriptionadmin/datasubscriptionadmin'

const props = defineProps({
  currentItem: {
    type: Object,
    required: false,
    defualt: null,
  },
  visibleDialog: {
    type: Boolean,
    default: false,
  },
  dialogType: {
    type: String,
    default: 'add', // add edit
  },
})
const emit = defineEmits(['okHandle', 'canceHandle'])

const initForm = () => {
  return {
    password: '',
    apiHeaders: '',
    recEmail: '',
    apiHost: '',
    apiPath: '',
    host: '',
    serverName: '',
    fromAddress: '',
    userName: '',
    apiType: '',
  }
}
const formRef = ref()
const showDialog = ref(false)
const formData = ref<ISubscriberForm>(initForm())
console.log('🚀 ~ formData:', formData.value)

watch(
  () => props.visibleDialog,
  () => {
    showDialog.value = props.visibleDialog
    if (props.currentItem && props.visibleDialog) {
      formData.value = JSON.parse(JSON.stringify(props.currentItem)) as ISubscriberForm
    }
  },
  { immediate: true },
)

// 分类保存
const okHandle = async () => {
  formRef.value.validate().then(async () => {
    const params: any = {
      ...formData.value,
    }
    let res: AssetResponse<string>
    if (props.dialogType === 'add') {
      res = await addDataSubscripServer(params)
    } else {
      params.serverId = props.currentItem?.serverId ?? -1
      res = await updateDataSubscripServer(params)
    }
    if (res.code === '000000') {
      message.success('操作成功')
      canceHandle()
      emit('okHandle')
    } else {
      message.error(res.msg)
    }
  })
}

// 取消操作
const canceHandle = () => {
  showDialog.value = false
  formData.value = initForm()
  emit('canceHandle')
}
</script>
