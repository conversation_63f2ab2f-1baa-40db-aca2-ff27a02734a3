import { getApiJson } from '../http-servers/module/index.js'
import { testApiTemplate } from '../page-template/test-template.js'
import { writeTextFile } from '../base-business/virtual-file-system.js'
import process from 'node:process'
// import { testApiData } from '../tools/file-api.js'

const args = process.argv.slice(2)
const argumentValue = args[0]

let apiData = []

/**
 * 生成测试用例的函数
 * 该函数首先从 getApiJson 函数获取 API 数据，然后为每个 API 数据项创建一个测试用例，并将这些测试用例写入文件
 * @returns {Promise} 当所有测试用例生成并写入文件后解析的 Promise
 */
export async function generateTestCases(data) {
  try {
    // 从 getApiJson 函数获取 API 数据 如果存在data表示网页部署传递数据
    apiData = await getApiJson(data)

    // const testcase = await testApiData()
    // 创建一个 Promise 数组，用于存储所有测试用例的 Promise
    const promisesArray = []

    // 遍历 API 数据，为每个数据项创建一个测试用例，并将其添加到 promisesArray 中
    apiData.map((item) => {
      item.modules.map((it) => {
        promisesArray.push(testApiTemplate(it))
      })
    })

    // 等待所有测试用例生成完成
    await Promise.all(promisesArray)

    // 调用 writeTest 函数，将生成的测试用例写入文件
    await writeTest()
  } catch (error) {
    console.log('写入测试用例失败', error)
  }
}

/**
 * 将 API 数据写入文件的函数
 * 该函数遍历 apiData 数组，将每个数据项写入文件
 * @returns {Promise} 当所有数据项写入文件后解析的 Promise
 */
async function writeTest() {
  try {
    apiData.map((item) => {
      item.modules.map((it) => {
        writeTextFile(it, item)
      })
    })
  } catch (error) {
    console.log('写入测试用例模板失败', error)
  }
}

// 启动 generate:api命令 自执行
if (argumentValue === 'test') {
  generateTestCases()
}
