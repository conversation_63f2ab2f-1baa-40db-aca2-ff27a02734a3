<template>
  <div class="page-wrap">
    <a-tabs v-model:activeKey="activeKey" default-active-key="1" style="height: 100%">
      <a-tab-pane key="1" tab="job详情">
        <JobDetail v-if="activeKey === '1'" />
      </a-tab-pane>
      <a-tab-pane key="2" tab="集群详情">
        <ClusterDetail v-if="activeKey === '2'" />
      </a-tab-pane>
      <a-tab-pane key="3" tab="jobManager监控指标">
        <JobManagerMetrics v-if="activeKey === '3'" />
      </a-tab-pane>
      <a-tab-pane key="4" tab="jobManager配置">
        <JobManagerConfig v-if="activeKey === '4'" />
      </a-tab-pane>
      <a-tab-pane key="5" tab="taskManager详情">
        <TaskManagerDetail />
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import JobDetail from './job-detail.vue'
import ClusterDetail from './cluster-detail.vue'
import JobManagerMetrics from './job-manager-metrics.vue'
import JobManagerConfig from './job-manager-config.vue'
import TaskManagerDetail from './task-manager-detail.vue'

const activeKey = ref('1')
</script>

<style lang="less" scoped>
.page-wrap {
  height: 100%;
  width: 100%;
  border-radius: 12px;
  background-color: #fff;
  box-shadow: 0px 20px 27px rgba(0, 0, 0, 0.05);
}
</style>
