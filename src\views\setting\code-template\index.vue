<template>
  <div class="codeTemplate">
    <cardBox title="Java模板管理" subTitle="Java代码模板配置及管理">
      <template #headerRight>
        <a-button v-action:add type="primary" @click="handleAddModal">新增</a-button>
      </template>

      <Table
        :columns="getColumns"
        :getData="getCodeTemplatList"
        ref="tableRef"
        :searchFormState="formMsg"
      >
        <template #search>
          <a-form-item>
            <a-input
              v-model:value="formMsg.name"
              allowClear
              @pressEnter="handleQuery"
              placeholder="请输入模板名称"
            />
          </a-form-item>
          <a-form-item>
            <a-input
              v-model:value="formMsg.desc"
              allowClear
              @pressEnter="handleQuery"
              placeholder="请输入模板描述"
            />
          </a-form-item>
        </template>
        <template #bodyCell="{ record, column, text }">
          <a v-if="column.key === 'content'" class="table-cell" @click="handleCheckDetail(text)">{{
            text
          }}</a>
          <template v-if="column.key === 'operation'">
            <a-space>
              <a-button size="small" type="link" v-action:edit @click="handleEditModel(record)"
                >修改</a-button
              >
              <a-button size="small" type="link" v-action:delete @click="handelDelete(record.id)"
                >删除</a-button
              >
            </a-space>
          </template>
        </template>
      </Table>
      <EditCodeTemplate ref="editCodeTemplate" @success="loadData" />
    </cardBox>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick, computed } from 'vue'
import { useUserStore } from '@/stores/user'
import { message, Modal } from 'ant-design-vue'
import EditCodeTemplate from './edit-code-template.vue'
import { getCodeTemplatList, deleteCodeTemplat } from '@/api/setting/code-template'
import cardBox from '@/components/card-box/card-box.vue'
import { Table } from '@fs/fs-components'
import { logModal } from '@/utils/log-modal'

// 类型定义
interface CodeTemplateDTO {
  id: string
  name: string
  desc: string
  content: string
  ownerId: string
  createTime?: string
  updateTime?: string
}

const scrollY = ref(0)
const pageMain = ref<HTMLElement>()
const editCodeTemplate = ref<InstanceType<typeof EditCodeTemplate>>()

const formMsg = reactive({
  name: '',
  desc: '',
})

const pagination = reactive({
  total: 0,
  size: 'small',
  pageIndex: 1,
  pageSize: 24,
  showSizeChanger: true,
  pageSizeOptions: ['12', '24', '36', '48'],
  showQuickJumper: true,
})

// Store
const userStore = useUserStore()
const ownerId = computed(() => userStore.userInfo.loginAccount || '')
const tableRef = ref()

const handleCheckDetail = (text: any) => {
  logModal(text, '查看详情', true, 60, null, { type: 'java' })
}

const loadData = async () => {
  tableRef.value?.getTableData()
}

const handleQuery = () => {}

// 打开弹出层
const handleAddModal = () => {
  editCodeTemplate.value?.showModal('add')
}

// 处理编辑规则
const handleEditModel = (record: CodeTemplateDTO) => {
  editCodeTemplate.value?.showModal('edit', record)
}

// 删除
const handelDelete = (id: string) => {
  Modal.confirm({
    title: '确认删除',
    content: '确定要删除这个模板吗？',
    okText: '确定',
    cancelText: '取消',
    onOk: async () => {
      try {
        const res = await deleteCodeTemplat({ id, ownerId: ownerId.value })
        if (res.code === '000000') {
          message.success('删除成功')
          loadData()
        }
      } catch (error) {
        console.error('删除失败:', error)
      }
    },
  })
}

// 删除最后一页最后一条数据页码问题
const hackDeleteLastItem = () => {
  const lastPage = Math.ceil(pagination.total / pagination.pageSize)
  if (pagination.pageIndex === lastPage && pagination.total % pagination.pageSize === 1) {
    pagination.pageIndex--
  }
}

// 获取表格列配置
const getColumns = [
  {
    title: '模板名称',
    key: 'name',
    dataIndex: 'name',
  },
  {
    title: '模板描述',
    key: 'desc',
    dataIndex: 'desc',
  },
  {
    title: '模板内容',
    key: 'content',
    dataIndex: 'content',
  },
  {
    title: '创建时间',
    key: 'createTime',
    dataIndex: 'createTime',
  },
  {
    title: '更新时间',
    key: 'updateTime',
    dataIndex: 'updateTime',
  },
  {
    title: '创建者',
    key: 'ownerId',
    dataIndex: 'ownerId',
  },
  {
    title: '操作',
    key: 'operation',
    dataIndex: 'operation',
    width: 120,
    fixed: 'right',
    align: 'center',
  },
]

// 生命周期
onMounted(() => {
  // 设置表格高度
  nextTick(() => {
    if (pageMain.value) {
      const pageY = document.body.offsetHeight
      scrollY.value = pageY - (pageMain.value.offsetHeight || 0) - 325
    }
  })

  // 监听窗口大小变化
  window.addEventListener('resize', () => {
    if (pageMain.value) {
      scrollY.value = 0
      setTimeout(() => {
        const pageY = document.body.offsetHeight
        scrollY.value = pageY - (pageMain.value?.offsetHeight || 0) - 325
      }, 200)
    }
  })
})
</script>

<style lang="less" scoped>
.codeTemplate {
  height: calc(100vh - 40px);
  padding: 24px;
}

.nav-page-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background: #fff;
  border-bottom: 1px solid #f0f0f0;
}

.title-left p {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  color: #262626;
}

.title-left .desc {
  margin-top: 4px;
  font-size: 12px;
  color: #8c8c8c;
}

.page-main {
  flex: 1;
  padding: 24px;
  background: #f5f5f5;
}

.query-container {
  background: #fff;
  padding: 24px;
  margin-bottom: 16px;
  border-radius: 6px;
}

.list-content {
  background: #fff;
  border-radius: 6px;
}

.table-card {
  border-radius: 6px;
}

.table-cell {
  color: #1890ff;
  cursor: pointer;
}

.table-cell:hover {
  text-decoration: underline;
}

.card-pagination {
  padding: 16px 24px;
  text-align: right;
}
</style>
