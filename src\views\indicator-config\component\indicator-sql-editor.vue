<template>
  <SqlEditor
    v-model:value="modelValue"
    :readonly="readonly"
    :height="height"
    :enableAutoCompletion="enableAutoCompletion"
    :otherCompleters="otherCompleters"
    style="height: 100%; width: 100%"
    @init="onInit"
    @update:value="onUpdate"
  />
</template>

<script setup lang="ts">
import { ref, computed, nextTick, onMounted, watch } from 'vue'
import SqlEditor from '@/components/sql-editor/sql-editor.vue'
import ace from 'ace-builds/src-noconflict/ace'
import { ParamCategory } from './dynamic-param.vue'
import type { Completer } from '@/components/sql-editor/types'
import type { Ace } from 'ace-builds'

// Props 定义
interface Props {
  readonly?: boolean
  height?: string | number
  enableAutoCompletion?: boolean
  tableFields?: Array<{
    fieldName: string
    fieldType: string
    fieldTypeName: string
    fieldComment: string
    tableName: string
    databaseName: string
  }>
  selectedTableInfo?: {
    tableId: string
    tableName: string
    databaseName: string
    datasourceId: string
    nodeType: number
  } | null
  // 所有可用的数据库列表
  availableDatabases?: Array<{
    databaseName: string
    datasourceId: string
  }>
  // 所有可用的表列表
  availableTables?: Array<{
    tableName: string
    databaseName: string
    tableId: string
    comment?: string
  }>
  dynamicParams?: Array<{
    name: string
    category: number
    default: string
    required: boolean
  }>
  needDynamicParam?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  readonly: false,
  height: '400px',
  enableAutoCompletion: true,
  tableFields: () => [],
  selectedTableInfo: null,
  availableDatabases: () => [],
  availableTables: () => [],
  dynamicParams: () => [],
  needDynamicParam: false,
})

// Events 定义
const emit = defineEmits<{
  init: [editor: any]
  change: [value: string]
}>()

// 双向绑定
const modelValue = defineModel<string>({ default: '' })

const editorInstance = ref<Ace.Editor | null>(null)

// 处理编辑器初始化
const onInit = (editor: any) => {
  // console.log('编辑器初始化完成')
  editorInstance.value = editor
}

// 处理值更新
const onUpdate = (value: string) => {
  emit('change', value)
}

const otherCompleters = computed(() => {
  // console.log('%c 更新补全器', 'background: blue')
  const completers: Completer[] = []
  // 字段补全器
  const fieldCompleter: Completer = {
    getCompletions: function (editor, session, pos, prefix, callback) {
      const completions = props.tableFields.map((field) => ({
        caption: `${field.fieldName} (${field.fieldTypeName})`,
        value: field.fieldName,
        meta: field.fieldComment || '字段',
        type: 'field',
        score: 1000,
      }))
      callback(null, completions)
    },
  }

  // 数据库名补全器
  const databaseCompleter: Completer = {
    getCompletions: function (editor, session, pos, prefix, callback) {
      const completions = props.availableDatabases.map((db) => ({
        caption: `${db.databaseName}`,
        value: db.databaseName,
        meta: '数据库',
        type: 'database',
        score: 800,
      }))
      callback(null, completions)
    },
  }

  // 表名补全器
  const tableCompleter: Completer = {
    getCompletions: function (editor, session, pos, prefix, callback) {
      const completions = props.availableTables.map((table) => ({
        caption: `${table.tableName}${table.comment ? ' (' + table.comment + ')' : ''}`,
        value: table.tableName,
        meta: `表 - ${table.databaseName}`,
        type: 'table',
        score: 900,
      }))

      // 如果有选中的数据库，添加完整的数据库.表名格式
      if (props.selectedTableInfo?.databaseName) {
        const dbTableCompletions = props.availableTables
          .filter((table) => table.databaseName === props.selectedTableInfo?.databaseName)
          .map((table) => ({
            caption: `${table.databaseName}.${table.tableName}${table.comment ? ' (' + table.comment + ')' : ''}`,
            value: `${table.databaseName}.${table.tableName}`,
            meta: `完整表名`,
            type: 'full_table',
            score: 950,
          }))
        completions.push(...dbTableCompletions)
      }

      callback(null, completions)
    },
  }
  // 动态参数补全器
  const dynamicParamCompleter: Completer = {
    triggerCharacters: ['#'],
    getCompletions: function (editor, session, pos, prefix, callback) {
      // 只有当启用动态参数且用户输入 # 时才提供补全
      if (!props.needDynamicParam || !props.dynamicParams?.length) {
        callback(null, [])
        return
      }

      // 获取光标前的文本来检查是否输入了 #
      const currentLine = session.getLine(pos.row)
      const textBeforeCursor = currentLine.substring(0, pos.column)

      // 检查是否以 # 开头的参数输入
      const hashMatch = textBeforeCursor.match(/#(\w*)$/)
      if (!hashMatch) {
        callback(null, [])
        return
      }

      const completions = props.dynamicParams.map((param) => ({
        caption: param.name,
        value: `#{${param.name}}`, // 使用 #{参数名} 的格式
        meta: `动态参数 (${getCategoryName(param.category)})${param.required ? ' *必填' : ''}`,
        type: 'dynamic_param',
        score: 1200, // 高优先级
        completer: {
          getCompletions: () => {
            return []
          },
          insertMatch: function (editor: any, data: any) {
            // 自定义插入逻辑：替换 # 开始的部分
            const pos = editor.getCursorPosition()
            const currentLine = editor.session.getLine(pos.row)
            const textBeforeCursor = currentLine.substring(0, pos.column)
            const hashIndex = textBeforeCursor.lastIndexOf('#')

            if (hashIndex !== -1) {
              const range = {
                start: { row: pos.row, column: hashIndex },
                end: { row: pos.row, column: pos.column },
              }
              editor.session.remove(range)
            }

            // 插入完整的动态参数
            editor.insert(data.value)
          },
        },
      }))

      callback(null, completions)
    },
  }

  completers.push(fieldCompleter, databaseCompleter, tableCompleter, dynamicParamCompleter)
  return completers
})
// 获取参数类型名称的辅助函数
const getCategoryName = (category: number): string => {
  const categoryMap: Record<number, string> = {
    [ParamCategory.STRING]: '字符串',
    [ParamCategory.NUMBER]: '数字',
    [ParamCategory.ARRAY]: '数组',
  }
  return categoryMap[category] || '未知类型'
}
</script>

<style scoped lang="less">
.sql-ace-editor-container {
  border-radius: 6px;
  overflow: hidden;
  border: 1px solid #d9d9d9;
  background: #fff;
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;

  .editor-wrapper {
    flex: 1;
    position: relative;
    overflow: hidden;
  }

  // 编辑器样式覆盖
  :deep(.ace_editor) {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', 'Courier New', monospace !important;
    font-size: 14px !important;
    line-height: 1.5 !important;
  }

  :deep(.ace_gutter) {
    background: #f8f9fa !important;
    border-right: 1px solid #e9ecef !important;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', 'Courier New', monospace !important;
  }

  :deep(.ace_cursor) {
    color: #000000 !important;
    border-left: 2px solid #000000 !important;
  }

  :deep(.ace_content) {
    cursor: text !important;
  }

  :deep(.ace_text-layer) {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', 'Courier New', monospace !important;
    font-size: 14px !important;
  }

  // GitHub主题语法高亮
  :deep(.ace-github) {
    .ace_keyword {
      color: #d73a49 !important;
      font-weight: bold !important;
    }

    .ace_string {
      color: #032f62 !important;
    }

    .ace_constant.ace_numeric {
      color: #005cc5 !important;
    }

    .ace_comment {
      color: #6a737d !important;
      font-style: italic !important;
    }

    .ace_support.ace_function {
      color: #6f42c1 !important;
    }

    .ace_variable {
      color: #e36209 !important;
    }

    .ace_operator {
      color: #d73a49 !important;
    }

    .ace_selection {
      background: #c8e6f5 !important;
    }

    .ace_selected-word {
      background: #ffeaa7 !important;
      border: 1px solid #fdcb6e !important;
    }

    .ace_marker-layer .ace_active-line {
      background: #f6f8fa !important;
    }
  }

  // 智能提示样式
  :deep(.ace_autocomplete) {
    border: 1px solid #d9d9d9 !important;
    border-radius: 4px !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
    background: #fff !important;
    width: 400px !important;
    min-width: 300px !important;
    max-width: 600px !important;
  }

  :deep(.ace_autocomplete .ace_line) {
    padding: 8px 12px !important;
    line-height: 1.4 !important;
    border-bottom: 1px solid #f0f0f0 !important;
  }

  :deep(.ace_autocomplete .ace_line.ace_selected) {
    background: #e6f7ff !important;
  }

  :deep(.ace_autocomplete .ace_completion-meta) {
    color: #666 !important;
    font-size: 12px !important;
  }
}

.sql-editor-toolbar {
  height: 38px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 12px;
  background: #fafafa;
  border-bottom: 1px solid #e9ecef;
  flex-shrink: 0;

  .toolbar-left {
    display: flex;
    align-items: center;

    .theme-label {
      font-size: 12px;
      color: #666;
      font-weight: 500;
    }
  }

  .toolbar-right {
    display: flex;
    align-items: center;
  }

  .refresh-btn {
    font-size: 12px;
    color: #666;

    &:hover {
      color: #1890ff;
      background: #f0f8ff;
    }
  }
}
// 动态参数智能提示特殊样式
:deep(.ace_autocomplete .ace_line) {
  // 动态参数项的特殊样式
  &[data-type='dynamic_param'] {
    background: linear-gradient(90deg, #f0f9ff 0%, #e0f2fe 100%) !important;
    border-left: 3px solid #0ea5e9 !important;

    &.ace_selected {
      background: linear-gradient(90deg, #dbeafe 0%, #bfdbfe 100%) !important;
    }

    .ace_completion-meta {
      color: #0ea5e9 !important;
      font-weight: 500 !important;
    }
  }
}
// 动态参数提示图标
:deep(.ace_autocomplete .ace_line[data-type='dynamic_param']) {
  &:before {
    content: '🔧';
    margin-right: 8px;
    font-size: 14px;
  }
}
</style>
