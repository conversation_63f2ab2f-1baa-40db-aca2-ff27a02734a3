<template>
  <div class="add-service">
    <a-steps progress-dot size="small" :current="current" style="margin-bottom: 25px">
      <a-step title="选择数据库类型" />
      <a-step title="连接详情" />
    </a-steps>
    <a-input
      v-show="current === 0"
      class="input-search"
      v-model="searchValue"
      placeholder="搜索数据库"
    />
    <div class="db-list" v-show="current === 0">
      <div
        @click="dbType = item.value"
        :class="['db-item', dbType === item.value ? 'active' : '']"
        v-for="item in filterList"
        :key="item.value"
      >
        <img :src="getDbIcon(item.value)" alt="" />
        <div class="text">{{ item.label }}</div>
      </div>
    </div>
    <div class="input-form" v-show="current === 1">
      <StepTwo
        ref="stepTwoRef"
        :dbType="dbType"
        :currentSelectData="currentSelectData"
        :currentRecord="currentRecord"
      ></StepTwo>
    </div>
    <div class="btns">
      <a-button type="link" @click="handleCancel">
        {{ current === 0 ? '关闭' : '上一步' }}
      </a-button>
      <a-button v-if="current === 0" type="primary" @click="handleNextStep"> 下一步 </a-button>
      <a-button v-if="current === 1" type="primary" @click="handleSubmit"> 提交 </a-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { message } from 'ant-design-vue'
import { getDbTypeList } from '../utils'
import StepTwo from './step-two.vue'
import { getDataSourceTemplate, getDataSourceList } from '@/views/setting-page/api/index'

import { onMounted, ref } from 'vue'
import { useUserStore } from '@/stores/user'
import { useRouter } from 'vue-router'
import { useTabor } from 'vue3-tabor'
import { getDbIcon } from '@/utils/utils'

const router = useRouter()
const route = useRoute()
const tabor = useTabor()
const userStore = useUserStore()

const current = ref(0)
const searchValue = ref('')
const list = ref(getDbTypeList())
const dbType = ref(0)
const templateList = ref<any>([])
const currentSelectData = ref({})
const currentRecord = ref({})
const stepTwoRef = ref()

const filterList = computed(() => {
  const filterArr = list.value.filter((item) => {
    if (!searchValue.value) {
      return true
    } else {
      return item.label.toLocaleLowerCase().includes(searchValue.value.toLocaleLowerCase())
    }
  })
  return filterArr
})

watch(dbType, (value) => {
  const template = templateList.value.find((i: any) => i.dbType === String(value))
  currentSelectData.value = template
})

const handleCancel = () => {
  if (current.value === 0) {
    router.go(-1)
    tabor.close()
  } else {
    current.value = 0
  }
}

const handleNextStep = () => {
  if (!dbType.value) {
    message.warning({
      content: '请选择数据库类型',
    })
    return
  }
  current.value = 1
}

const handleSubmit = () => {
  console.log('---------提交')
  // @ts-ignore
  stepTwoRef.value.handleSubmit()
}

const getDataSourceTemplateFunc = async () => {
  const res = await getDataSourceTemplate({})
  templateList.value = res.data || []
}

const getMainList = async () => {
  let params = {
    dbType: '',
    dbDesc: '',
    dbUrl: null,
    pageIndex: 1,
    pageSize: 9999,
    ownerId: userStore.userInfo.loginAccount,
  }
  const res = await getDataSourceList(params)
  console.log(res)
  const id = route.query.id
  const list = res.data.records
  const findTarget = list.find((item: any) => item.id === Number(id))

  currentRecord.value = findTarget || {}
  dbType.value = findTarget.dbType
}

onMounted(() => {
  getDataSourceTemplateFunc()
  if (route.query.id) {
    getMainList()
  }
})

// export default {
//   name: 'AddService',
//   components: {
//     StepTwo,
//   },
//   data() {
//     return {
//       description: 'This is a description.',
//       current: 0,
//       searchValue: '',
//       list: getDbTypeList(),
//       dbType: 0,
//       templateList: [],
//       currentSelectData: {},
//       currentRecord: {},
//     }
//   },
//   computed: {
//     filterList() {
//       const filterArr = this.list.filter((item) => {
//         if (!this.searchValue) {
//           return true
//         } else {
//           return item.label.toLocaleLowerCase().includes(this.searchValue.toLocaleLowerCase())
//         }
//       })
//       return filterArr
//     },
//   },
//   watch: {
//     dbType(value) {
//       const template = this.templateList.find((i) => i.dbType === String(value))
//       this.currentSelectData = template
//     },
//   },
//   methods: {
//     getDbImg: dbImg,
//     handleCancel() {
//       if (this.current === 0) {
//         this.$router.go(-1)
//         this.$tabs.close()
//       } else {
//         this.current = 0
//       }
//     },
//     handleNextStep() {
//       if (!this.dbType) {
//         message.warning({
//           content: '请选择数据库类型',
//         })
//         return
//       }
//       this.current = 1
//     },
//     handleSubmit() {
//       console.log('---------提交')
//       this.$refs.stepTwoRef.handleSubmit()
//     },
//     getDataSourceTemplateFunc() {
//       getDataSourceTemplate({})
//         .then((res) => {
//           this.templateList = res.data || []
//         })
//         .catch((e) => console.log(e))
//     },
//     // 获取列表信息
//     getMainList() {
//       let params = {
//         dbType: '',
//         dbDesc: '',
//         dbUrl: null,
//         pageIndex: 1,
//         pageSize: 9999,
//         ownerId: this.$store.getters.ownerId,
//       }
//       getDataSourceList(params).then((res) => {
//         const id = this.$route.query.id
//         const list = res.data.records
//         const findTarget = list.find((item) => item.id === Number(id))

//         this.currentRecord = findTarget || {}
//         this.dbType = findTarget.dbType
//         console.log('==============findTarget', id, list, 111, findTarget)
//       })
//     },
//   },
//   mounted() {
//     this.getDataSourceTemplateFunc()
//     if (this.$route.query.id) {
//       this.getMainList()
//     }
//   },
// }
</script>

<style lang="less" scoped>
.add-service {
  max-width: 768px;
  margin: 0 auto;
  padding-top: 25px;
  height: 100%;
  overflow: auto;
}

.input-search {
  margin: 10px 0;
}

.db-list {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 16px;
  .db-item {
    cursor: pointer;
    width: 114px;
    height: 93px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    border: 1px solid #0000001a;
    border-radius: 4px;
    font-size: 14px;
    color: #222;
    img {
      width: 36px;
      height: 36px;
      margin-bottom: 8px;
    }
    &.active {
      border-color: #2e87e6;
      color: #2e87e6;
    }
  }
}
.btns {
  display: flex;
  justify-content: flex-end;
}
</style>
