<script setup lang="ts">
import {
  getResourceServiceList,
  getAthrorityRSList,
  setAthrorityRSList,
  type ResourceServiceItem,
} from '@/api/services/resource-service'
import { message, type TableProps } from 'ant-design-vue'
import type { Key } from 'ant-design-vue/es/table/interface'

defineOptions({
  name: 'ResourceServiceModal',
})

const open = ref(false)
const modalWidth = 1000
const tableWidth = modalWidth - 50
const serviceTypeEnum = {
  0: 'worker',
  1: 'master',
  2: '执行脚本节点',
  3: 'flink',
  4: 'spark',
  5: 'minwork',
  6: '模型服务',
} as const
type ServiceType = keyof typeof serviceTypeEnum

const confirmLoading = ref(false)
const pagination = ref({
  pageSize: 10,
  current: 1,
  total: 0,
  pageSizeOptions: ['10', '50', '100'],
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条`,
})
const columns = ref([
  {
    title: '资源类型',
    dataIndex: 'serviceType',
    customRender: ({ text }: { text: ServiceType }) => {
      return serviceTypeEnum[text]
    },
  },
  {
    title: 'IP地址',
    dataIndex: 'serviceIp',
  },
  {
    title: '服务端口',
    dataIndex: 'servicePort',
  },
  {
    title: '用户名',
    dataIndex: 'hostUser',
  },
])
/** 选中行 */
const selectedRowKeys = ref<Key[]>([])
/** 行选择配置 */
const rowSelection = computed<TableProps['rowSelection']>(() => ({
  selectedRowKeys: selectedRowKeys.value,
  onChange: (keys: Key[]) => {
    // 处理分页的情况
    const curPageKeys = tableData.value.map((item) => item.id)
    // console.log('%c curPageKeys', 'color: blue', curPageKeys)
    // console.log('%c selectedKeys', 'color: green', keys)
    const selectedKeys = [...keys]
    let unSelectedKeys: Key[] = []
    if (keys.length !== curPageKeys.length) {
      unSelectedKeys = curPageKeys.filter((key) => !keys.includes(key as number))
    }
    // console.log('%c unSelectedKeys', 'color: red', unSelectedKeys)
    if (keys.length) {
      selectedRowKeys.value = Array.from(new Set([...selectedRowKeys.value, ...selectedKeys]))
      if (unSelectedKeys.length) {
        selectedRowKeys.value = selectedRowKeys.value.filter(
          (key) => !unSelectedKeys.includes(key as number),
        )
      }
    } else {
      selectedRowKeys.value = selectedRowKeys.value.filter(
        (key) => !unSelectedKeys.includes(key as number),
      )
    }
  },
}))
/** 表格数据 */
const tableData = ref<ResourceServiceItem[]>([])
const roleId = ref<string>()
const loading = ref(false)
const getTableData = async () => {
  if (!roleId.value) return
  try {
    loading.value = true
    const res = await getResourceServiceList({
      roleId: roleId.value,
      pageIndex: pagination.value.current,
      pageSize: pagination.value.pageSize,
    })
    tableData.value = res.data.records
    pagination.value.total = res.data.totalRecords
  } catch (error) {
    message.error((error as any).msg || '获取资源服务失败')
  } finally {
    loading.value = false
  }
}
const getSelectedKeys = async () => {
  if (!roleId.value) return
  const res = await getAthrorityRSList(roleId.value)
  selectedRowKeys.value = res.data.map((key) => Number(key)) as Key[]
}

/** 确定 */
const handleOk = async () => {
  // console.log(`selectedRowKeys: ${selectedRowKeys.value}`)
  if (!roleId.value) return
  try {
    confirmLoading.value = true
    const res = await setAthrorityRSList({
      roleId: roleId.value,
      resourceIds: selectedRowKeys.value.map((key) => key.toString()),
      resourceType: 3,
    })
    if (res.code === '000000') {
      open.value = false
      message.success('分配成功')
    } else {
      message.error(res.msg || '分配失败')
    }
  } catch (error) {
    message.error((error as any).msg || '分配失败')
  } finally {
    confirmLoading.value = false
  }
}

/** 取消 */
const handleCancel = () => {
  open.value = false
}

const showModal = (record?: any, id?: string) => {
  // console.log(`分配资源服务弹窗: ${record}`)
  open.value = true
  roleId.value = id
  getTableData()
  getSelectedKeys()
}

const handleTableChange = (obj: any) => {
  pagination.value.current = obj.current
  pagination.value.pageSize = obj.pageSize
  getTableData()
}

defineExpose({
  showModal,
})
</script>

<template>
  <a-modal
    v-model:open="open"
    title="资源服务"
    @ok="handleOk"
    @cancel="handleCancel"
    :width="modalWidth"
    :confirm-loading="confirmLoading"
  >
    <a-table
      :columns="columns"
      :data-source="tableData"
      :row-selection="rowSelection"
      :scroll="{ x: tableWidth }"
      row-key="id"
      :pagination="pagination"
      :loading="loading"
      @change="handleTableChange"
    />
  </a-modal>
</template>

<style lang="less" scoped>
:deep(.ant-table-thead > tr > th) {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
