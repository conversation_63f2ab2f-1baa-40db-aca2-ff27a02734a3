<template>
  <div class="list-content">
    <!-- <a-card class="table-card" :bordered="false"> -->
    <a-table
      ref="table"
      :rowKey="(record: Data, index: number) => index"
      :indentSize="40"
      :pagination="false"
      :data-source="rddList"
      :scroll="{ x: 1800, y: 650 }"
      :columns="rddColumns"
    >
    </a-table>
    <!-- 分页卡片-->
    <div class="card-pagination">
      <a-pagination
        size="small"
        @change="handelPageChange"
        v-model="pagination.pageIndex"
        :total="pagination.total"
        @showSizeChange="handelShowSizeChange"
        :defaultPageSize="pagination.pageSize"
        :pageSizeOptions="pagination.pageSizeOptions"
      />
    </div>
    <!-- </a-card> -->
  </div>
</template>
<script lang="ts" setup>
import { defineExpose, ref, onMounted } from 'vue'
import { getSrorageInfo } from '@/api/services/monitor-maintenance/spark-monitor'
import { type Action } from './spark-monitor'
type Data = {
  id: string
  name: string
  storageLevel: string
  numCachedPartitions: number
  percent: number
}
const pagination = reactive({
  pageIndex: 1,
  pageSize: 10,
  total: 0,
  pageSizeOptions: ['10', '20', '30', '40'],
})
const rddList = ref<Data[]>([])
const rddColumns = [
  {
    title: 'id',
    dataIndex: 'id',
    key: 'id',
  },
  {
    title: 'RDD名称',
    dataIndex: 'name',
    key: 'name',
    scopedSlots: { customRender: 'name' },
  },
  {
    title: '缓存级别',
    dataIndex: 'storageLevel',
    key: 'storageLevel',
    scopedSlots: { customRender: 'storageLevel' },
  },
  {
    title: '缓存分区数',
    dataIndex: 'numCachedPartitions',
    key: 'numCachedPartitions',
    scopedSlots: { customRender: 'numCachedPartitions' },
  },
  // {
  //   title: this.$t('rddtable_22'),
  //   dataIndex: 'percent',
  //   key: 'percent',
  //   scopedSlots: { customRender: 'percent' }
  // },
  {
    title: '已占内存',
    dataIndex: 'memoryUsed',
    key: 'memoryUsed',
    scopedSlots: { customRender: 'memoryUsed' },
  },
  {
    title: '已占磁盘',
    dataIndex: 'diskUsed',
    key: 'diskUsed',
    scopedSlots: { customRender: 'diskUsed' },
  },
]

async function getList() {
  const params = {
    pageIndex: pagination.pageIndex,
    pageSize: pagination.pageSize,
  }

  const resp = await getSrorageInfo({ ...params })

  if (resp.data.obj) {
    rddList.value = resp.data.obj.list
    pagination.total = resp.data.obj.total
  }
}
defineExpose({ getList } satisfies Action)

// 切换分页
function handelPageChange(pageIndex: number, pageSize: number) {
  pagination.pageIndex = pageIndex
  pagination.pageSize = pageSize
  getList()
}

// 切换分页大小
function handelShowSizeChange(pageIndex: number, pageSize: number) {
  pagination.pageIndex = pageIndex
  pagination.pageSize = pageSize
  getList()
}

onMounted(getList)
</script>
