<template>
  <div class="flow-node">
    <!-- <a-checkbox :disabled="true" :checked="nodeData.enabled" @change="changeEnabled" /> -->
    <MinusCircleOutlined
      class="minus-btn"
      :class="{ 'disabled-minus-btn': isStartOrEnd }"
      style="font-size: 18px"
      @click="minusNode"
    />
    <div v-if="isStartOrEnd" class="name">{{ nodeData.label }}</div>
    <div v-else class="selector">
      <span class="selector-btn" @click="selectUser">
        <span v-if="!nodeData.value">请选择审批人</span>
        <span v-else>{{ nodeData.label }}</span>
      </span>
      <!-- <div class="minus-btn" @click="minusNode">
        <a-icon type="minus-circle"  />
        <span>减少该审批</span>
      </div> -->
    </div>
    <div v-if="!isEnd" class="line">
      <div class="add-btn" @click="addNode">
        <PlusCircleOutlined style="font-size: 18px" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, defineEmits, defineProps } from 'vue'
import type { FlowNodeData } from '../type'
import { PlusCircleOutlined, MinusCircleOutlined } from '@ant-design/icons-vue'

// Props
const props = defineProps<{
  nodeData: FlowNodeData
}>()

// Emits
const emit = defineEmits<{
  addNode: [nodeData: FlowNodeData]
  changeEnabled: [nodeData: FlowNodeData, checked: boolean]
  minusNode: [nodeData: FlowNodeData]
  selectUser: [nodeData: FlowNodeData]
}>()

// Computed
const name = computed(() => {
  return props.nodeData.isStart ? '开始' : props.nodeData.isEnd ? '结束' : ''
})

const isStartOrEnd = computed(() => {
  return props.nodeData.isStart || props.nodeData.isEnd || false
})

const isEnd = computed(() => {
  return props.nodeData.isEnd || false
})

// Methods
const addNode = () => {
  emit('addNode', props.nodeData)
}

const changeEnabled = (checked: boolean) => {
  emit('changeEnabled', props.nodeData, checked)
}

const minusNode = () => {
  if (isStartOrEnd.value) {
    return
  }
  emit('minusNode', props.nodeData)
}

const selectUser = () => {
  emit('selectUser', props.nodeData)
}
</script>

<style lang="less" scoped>
.flow-node {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  width: 180px;
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: 180px;
  & .name {
    font-size: 14px;
    color: #333;
  }
  & .minus-btn:hover {
    color: #1890ff;
  }
  & .disabled-minus-btn {
    color: #ccc;
    cursor: not-allowed;
    &:hover {
      color: #ccc;
    }
  }
  & .selector {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    padding: 0 16px;
    overflow: hidden;
    gap: 4px;
    & .selector-btn {
      width: 100%;
      height: 100%;
      text-decoration: underline;
      cursor: pointer;
      text-align: center;
      color: #1890ff;
    }
    & .minus-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
      gap: 4px;
      cursor: pointer;
      &:hover {
        color: #1890ff;
      }
    }
  }

  & .line {
    position: absolute;
    top: 8px;
    left: 106px;
    width: 148px;
    border: 1px solid #ccc;
    & .add-btn {
      position: absolute;
      top: -9px;
      left: 66px;
      width: 18px;
      height: 18px;
      background-color: #fff;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 10;
      &:hover {
        color: #1890ff;
      }
    }
  }
}
</style>
