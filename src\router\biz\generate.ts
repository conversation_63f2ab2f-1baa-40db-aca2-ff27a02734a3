import type { RouteRecordRaw } from 'vue-router'

const isSecondary = import.meta.env.VITE_APP_NEED_AUTH === 'false'

/**
 * 生成路由
 */
const routes: RouteRecordRaw[] = [
  {
    path: '/indicators-template',
    name: 'indicators-template',
    component: () => import('@/views/indicators-template/index.vue'),
    meta: {
      isSecondary,
    },
  },
  {
    path: '/indicator-list',
    name: 'indicator-list',
    component: () => import('@/views/indicator-list/index.vue'),
    meta: {
      isSecondary,
    },
  },
  {
    path: '/indicator-config',
    name: 'indicator-config',
    component: () => import('@/views/indicator-config/index.vue'),
    meta: {
      isSecondary,
    },
  },
  {
    path: '/indicators-template/create',
    name: 'indicators-template/create',
    component: () => import('@/views/indicators-template/create/index.vue'),
    meta: {
      breadcrumb: false,
      isSecondary,
    },
  },
  {
    path: '/packet-management',
    name: 'packet-management',
    component: () => import('@/views/packet-management/index.vue'),
    meta: {
      isSecondary,
    },
  },
  {
    path: '/indicator-list/create',
    name: 'indicator-list/create',
    component: () => import('@/views/indicator-config/index.vue'),
    meta: {
      title: '新增指标',
      isSecondary,
    },
  },
  {
    path: '/indicator-list/edit',
    name: 'indicator-list/edit',
    component: () => import('@/views/indicator-config/index.vue'),
    meta: {
      title: '编辑指标',
      isSecondary,
    },
  },
  {
    path: '/rule-engine',
    name: 'rule-engine',
    component: () => import('@/views/rule-engine/index.vue'),
    meta: {
      breadcrumb: false,
      title: '数据建模管理',
      isSecondary,
    },
  },
  {
    path: '/machine-learning-file',
    name: 'machine-learning-file',
    component: () => import('@/views/machine-learning-file/index.vue'),
    meta: {
      breadcrumb: false,
      title: '机器学习文件管理',
      isSecondary,
    },
  },
  {
    path: '/rule-engine/rule-manage',
    name: 'rule-engine/rule-manage',
    component: () => import('@/views/rule-engine/rule-manage/index.vue'),
    meta: {
      breadcrumb: false,
      isSecondary,
    },
  },
  {
    path: '/rule-engine/function-manage',
    name: 'rule-engine/function-manage',
    component: () => import('@/views/rule-engine/function-manage/index.vue'),
    meta: {
      breadcrumb: false,
      isSecondary,
    },
  },
  {
    path: '/setConfig',
    name: 'setConfig',
    component: () => import('@/views/setting/index.vue'),
    meta: {
      breadcrumb: false,
      isSecondary,
      title: '设置',
    },
  },
  {
    path: '/setting/permission',
    name: 'permission',
    component: () => import('@/views/setting/permission.vue'),
    meta: {
      breadcrumb: false,
      isSecondary,
      title: '团队和用户管理',
    },
  },
  {
    path: '/setting/management',
    name: 'management',
    component: () => import('@/views/setting/management.vue'),
    meta: {
      breadcrumb: false,
      isSecondary,
      title: '管理配置',
    },
  },
  {
    path: '/setting/access-control',
    name: 'access-control',
    component: () => import('@/views/setting/access-control.vue'),
    meta: {
      breadcrumb: false,
      isSecondary,
      title: '访问控制',
    },
  },
  {
    path: '/setting/system-settings',
    name: 'system-settings',
    component: () => import('@/views/setting/system-settings.vue'),
    meta: {
      breadcrumb: false,
      isSecondary,
      title: '系统设置',
    },
  },
  {
    path: '/setting/auditLog',
    name: 'auditLog',
    component: () => import('@/views/setting/audit-log.vue'),
    meta: {
      isSecondary,
      title: '审计日志',
    },
  },
  {
    path: '/iframePage',
    name: 'iframePage',
    meta: {
      title: 'tabs',
      hideInMenu: false,
      keepAlive: true,
      reuse: true,
      permission: true,
    },
    component: () => import('@/views/iframe-page/index.vue'),
  },
  {
    path: '/search-result',
    name: 'searchResult',
    component: () => import('@/views/search/global-search-result.vue'),
    meta: {
      title: '搜索结果',
    },
  },
  {
    path: '/apiconfig/api/apiList/apiList',
    name: 'apiList',
    component: () => import('@/views/api-manage/index.vue'),
    meta: {
      title: 'API列表',
    },
  },
  {
    path: '/approval-workflow',
    name: 'approval-workflow',
    component: () => import('@/views/approval-workflow/index.vue'),
    meta: {
      title: '流程审批配置',
    },
  },
  {
    path: '/setting/template-manager',
    name: 'setting/template-manager',
    component: () => import('@/views/setting/template-manager/index.vue'),
    meta: {
      title: '模版管理',
    },
  },
]
export default routes
