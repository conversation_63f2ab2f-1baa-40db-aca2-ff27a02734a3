import { expect, test } from 'vitest'
import {
  addIndexTemplate,
  getIndexFormDataListByPage,
  delIndexTemplate,
  saveIndexTemplate,
  getValidIndexFormDataList,
  getSingleIndexFormData,
  deployIndexFormData,
  getHistoryListPage,
  getIndexFormByType,
} from './indexform'

test('addIndexTemplate', async () => {
  const { data } = await addIndexTemplate({
    formCode: '*9C!#',
    formName: 'OA^tI',
    remarks: 'RI6',
    fieldsData: 'U0U',
    compData: '!Mx#K',
    businessType: 184466265211147,
  })

  expect(data).toBeTruthy()
})

test('getIndexFormDataListByPage', async () => {
  const { data } = await getIndexFormDataListByPage({
    formName: 'sp9$R^7',
    isDeleted: 8281294797464127,
    status: 367246735283185,
    startTime: 'ml&D',
    endTime: '8Yk@76',
    isRelease: 2564658442449525,
    businessType: 3343384935376119,
    pageIndex: 5239534233200752,
    pageSize: 5855334742749036,
  })

  expect(data).toHaveProperty('code')
  expect(data).toHaveProperty('msg')
  expect(data.data).toHaveProperty('records')
})

test('delIndexTemplate', async () => {
  const { data } = await delIndexTemplate({ formId: 'KLMTZ' })

  expect(data).toHaveProperty('code')
  expect(data).toHaveProperty('msg')
  expect(data.data[0]).toHaveProperty('userId'), expect(data.data[0]).toHaveProperty('userName')
})

test('saveIndexTemplate', async () => {
  const { data } = await saveIndexTemplate({
    formId: '8JSf%',
    formCode: 'rCf5$d',
    formName: 'r2lFL',
    remarks: '6Q^uU4',
    fieldsData: 'Cj6CZ',
    compData: 'PAPu',
    revision: 2646286667443363,
  })

  expect(data).toHaveProperty('code')
  expect(data).toHaveProperty('msg')
  expect(data.data[0]).toHaveProperty('userId'), expect(data.data[0]).toHaveProperty('userName')
})

test('getValidIndexFormDataList', async () => {
  const { data } = await getValidIndexFormDataList({ formName: 'e[vv' })

  expect(data).toHaveProperty('code')
  expect(data).toHaveProperty('msg')
  expect(data.data[0]).toHaveProperty('formId'),
    expect(data.data[0]).toHaveProperty('formCode'),
    expect(data.data[0]).toHaveProperty('formName'),
    expect(data.data[0]).toHaveProperty('historyId'),
    expect(data.data[0]).toHaveProperty('fieldsData'),
    expect(data.data[0]).toHaveProperty('compData'),
    expect(data.data[0]).toHaveProperty('isDeleted'),
    expect(data.data[0]).toHaveProperty('status'),
    expect(data.data[0]).toHaveProperty('revision'),
    expect(data.data[0]).toHaveProperty('remarks'),
    expect(data.data[0]).toHaveProperty('tenantId'),
    expect(data.data[0]).toHaveProperty('createUser'),
    expect(data.data[0]).toHaveProperty('createUsername'),
    expect(data.data[0]).toHaveProperty('createTime'),
    expect(data.data[0]).toHaveProperty('updateUser'),
    expect(data.data[0]).toHaveProperty('updateUsername'),
    expect(data.data[0]).toHaveProperty('updateTime'),
    expect(data.data[0]).toHaveProperty('isRelease'),
    expect(data.data[0]).toHaveProperty('currentVersion')
})

test('getSingleIndexFormData', async () => {
  const { data } = await getSingleIndexFormData({ formId: 'Xsi7HwK' })

  expect(data).toHaveProperty('code')
  expect(data).toHaveProperty('msg')
  expect(data.data).toHaveProperty('formId'),
    expect(data.data).toHaveProperty('formCode'),
    expect(data.data).toHaveProperty('formName'),
    expect(data.data).toHaveProperty('fieldsData'),
    expect(data.data).toHaveProperty('compData'),
    expect(data.data).toHaveProperty('isDeleted'),
    expect(data.data).toHaveProperty('status'),
    expect(data.data).toHaveProperty('revision'),
    expect(data.data).toHaveProperty('remarks'),
    expect(data.data).toHaveProperty('tenantId'),
    expect(data.data).toHaveProperty('createUser'),
    expect(data.data).toHaveProperty('createUsername'),
    expect(data.data).toHaveProperty('createTime'),
    expect(data.data).toHaveProperty('updateUser'),
    expect(data.data).toHaveProperty('updateUsername'),
    expect(data.data).toHaveProperty('updateTime'),
    expect(data.data).toHaveProperty('isRelease'),
    expect(data.data).toHaveProperty('currentVersion'),
    expect(data.data).toHaveProperty('fieldList')
})

test('deployIndexFormData', async () => {
  const { data } = await deployIndexFormData({ formId: 'EBNGd' })

  expect(data).toHaveProperty('code')
  expect(data).toHaveProperty('msg')
  expect(data).toHaveProperty('data')
})

test('getHistoryListPage', async () => {
  const { data } = await getHistoryListPage({
    formId: '6Un',
    startTime: 'jnBh$',
    endTime: 'plIKg&D',
    pageIndex: 1426899404888415,
    pageSize: 7142480710374775,
  })

  expect(data).toHaveProperty('code')
  expect(data).toHaveProperty('msg')
  expect(data.data).toHaveProperty('records')
})

test('getIndexFormByType', async () => {
  const { data } = await getIndexFormByType({ businessType: 3334311681103025 })

  expect(data).toHaveProperty('code')
  expect(data).toHaveProperty('msg')
  expect(data.data).toHaveProperty('formId'),
    expect(data.data).toHaveProperty('formCode'),
    expect(data.data).toHaveProperty('formName'),
    expect(data.data).toHaveProperty('fieldsData'),
    expect(data.data).toHaveProperty('compData'),
    expect(data.data).toHaveProperty('isDeleted'),
    expect(data.data).toHaveProperty('status'),
    expect(data.data).toHaveProperty('revision'),
    expect(data.data).toHaveProperty('remarks'),
    expect(data.data).toHaveProperty('tenantId'),
    expect(data.data).toHaveProperty('createUser'),
    expect(data.data).toHaveProperty('createUsername'),
    expect(data.data).toHaveProperty('createTime'),
    expect(data.data).toHaveProperty('updateUser'),
    expect(data.data).toHaveProperty('updateUsername'),
    expect(data.data).toHaveProperty('updateTime'),
    expect(data.data).toHaveProperty('isRelease'),
    expect(data.data).toHaveProperty('currentVersion'),
    expect(data.data).toHaveProperty('fieldList')
})
