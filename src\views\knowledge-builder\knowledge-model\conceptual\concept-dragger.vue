<template>
  <a-drawer
    v-model:open="open"
    class="custom-class"
    root-class-name="root-class-name"
    :bodyStyle="{ padding: 0 }"
    :headerStyle="{ borderBottom: 'none' }"
    :root-style="{ color: 'blue' }"
    :width="650"
    placement="right"
    :getContainer="false"
    :mask-closable="true"
    :maskStyle="{ background: 'transparent' }"
    @after-open-change="afterOpenChange"
    destroyOnClose
    @close="mode = Mode.DES"
  >
    <template #title>
      <header class="title">{{ currentEchartsData?.nameZh }}</header>
    </template>
    <div class="opt">
      <a-radio-group v-model:value="mode">
        <a-radio-button :value="Mode.DES">描述</a-radio-button>
        <a-radio-button :value="Mode.PROP">属性</a-radio-button>
        <a-radio-button :value="Mode.RULE" disabled>规则</a-radio-button>
      </a-radio-group>
    </div>
    <div class="container-wrap">
      <a-descriptions :column="1" style="padding: 24px" v-show="mode === Mode.DES">
        <a-descriptions-item label="概念名称">-</a-descriptions-item>
        <a-descriptions-item label="概念描述">-</a-descriptions-item>
        <a-descriptions-item label="父概念"
          >{{ currentEchartsData.nameZh }}({{ currentEchartsData.name }})</a-descriptions-item
        >
        <a-descriptions-item label="元概念"
          >{{ currentEchartsData.nameZh }}({{ currentEchartsData.name }})</a-descriptions-item
        >
        <a-descriptions-item label="别名"> - </a-descriptions-item>
      </a-descriptions>
      <a-table
        v-show="mode === Mode.PROP"
        :columns="columns"
        :dataSource="[]"
        :pagination="false"
      ></a-table>
    </div>
  </a-drawer>
</template>

<script lang="ts" setup>
import { ref, defineProps } from 'vue'
import { columns } from './source-data'

enum Mode {
  DES = 'DES',
  PROP = 'PROP',
  RULE = 'RULE',
}
const props = defineProps<{
  currentEchartsData: any
}>()
const mode = ref<Mode>(Mode.DES)
const open = ref<boolean>(false)

const afterOpenChange = (bool: boolean) => {
  console.log('open', bool)
}

const showDrawer = () => {
  open.value = true
}

defineExpose({
  showDrawer,
})
</script>
<style scoped lang="less">
.root-class-name {
  /deep/ .ant-drawer-header {
    border-bottom: none;
  }
  .radio-wrap {
    padding: 0 24px 16px;
  }
}
.container-wrap {
  padding: 16px;
}
.opt {
  padding: 0 24px 24px;
  border-bottom: 1px solid #eee;
}
</style>
