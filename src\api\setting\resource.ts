import service from '@/api'
import { SODATAFLINK } from '@/api/base'

// 资源DTO
export class ResourceDTO {
  /* ID */
  id!: string

  /* IP地址 */
  serviceIp?: string

  /* SSH端口 */
  sshPort?: number

  /* 逻辑ID */
  logicId?: string

  /* 端口 */
  servicePort?: number

  /* 用户名 */
  hostUser?: string

  /* 密码 */
  hostPwd?: string

  /* 执行路径 */
  execPath?: string

  /* 日志路径 */
  logPath?: string

  /* 创建时间 */
  createTime?: string

  /* 更新时间 */
  updateTime?: string
}

interface Parameter {
  [prop: string]: any
}

interface ServiceResponseList<T> {
  code: string
  data: {
    totalRecords: number
    pageIndex: number
    records: T[]
    totalPages: number
    pageSize: number
  }
  error?: { [key: string]: any }
  msg?: string
}

// 查询资源列表
export function getResourceList(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/new2dataplatform/resourceManager/scheserviceinfoPageList`,
    params,
  })
}

// 新增资源
export function addResource(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/new2dataplatform/resourceManager/scheserviceinfoInsert`,
    method: 'post',
    data: params,
  })
}

// 修改资源
export function editResource(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/new2dataplatform/resourceManager/scheserviceinfoUpdate/${params.id}`,
    method: 'put',
    data: params,
  })
}

// 删除资源
export function deleteResource(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/new2dataplatform/resourceManager/scheserviceinfoDelete/${params.id}`,
    method: 'delete',
    data: params,
  })
}

// 测试资源
export function testResource(params: Parameter) {
  return service({
    url: `${SODATAFLINK}/flink/resourceManager/resourceMangerCheckFun`,
    method: 'post',
    data: params,
  })
}
