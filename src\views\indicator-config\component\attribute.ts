export const attributeList: any[] = [
  {
    name: 'case',
    tips: '用一个案例列表测试一个表达式，并返回第一个匹配案例的对应值，如果不满足其他条件，还可以选择默认值。',
    example: 'case([重量] > 200, "大型", [重量] > 150, "中型", "小")',
    children: [
      {
        name: '条件',
        tips: '应该评估为真或假的东西。',
      },
      {
        name: '产量',
        tips: '如果前面的条件为真，将返回的值。',
      },
      {
        name: '…',
        tips: '你可以添加更多的条件进行测试。',
      },
    ],
  },
  {
    name: 'concat',
    tips: '将两个或更多的文字串组合在一起。',
    example: 'concat([姓氏], ", ", [名字])',
    children: [
      {
        name: '值1',
        tips: '要开始的栏目或文本。',
      },
      {
        name: '值2',
        tips: '这将被添加到value1的末尾。',
      },
      {
        name: '…',
        tips: '这将被添加到值2的末尾，以此类推。',
      },
    ],
  },
  {
    name: 'sum',
    tips: '计算指定列的总和。',
    example: 'sum([销售额])',
    children: [
      {
        name: '列名',
        tips: '需要计算总和的列。',
      },
    ],
  },
  {
    name: 'avg',
    tips: '计算指定列的平均值。',
    example: 'avg([评分])',
    children: [
      {
        name: '列名',
        tips: '需要计算平均值的列。',
      },
    ],
  },
  {
    name: 'count',
    tips: '计算指定列的行数。',
    example: 'count([订单号])',
    children: [
      {
        name: '列名',
        tips: '需要计算行数的列。',
      },
    ],
  },
  {
    name: 'min',
    tips: '返回指定列的最小值。',
    example: 'min([价格])',
    children: [
      {
        name: '列名',
        tips: '需要查找最小值的列。',
      },
    ],
  },
  {
    name: 'max',
    tips: '返回指定列的最大值。',
    example: 'max([价格])',
    children: [
      {
        name: '列名',
        tips: '需要查找最大值的列。',
      },
    ],
  },
  {
    name: 'coalesce',
    tips: '返回参数列表中的第一个非空值。',
    example: 'coalesce([备注], "无备注")',
    children: [
      {
        name: '值1',
        tips: '第一个可能为空的列或值。',
      },
      {
        name: '值2',
        tips: '第二个可能为空的列或值。',
      },
      {
        name: '…',
        tips: '可以添加更多的值。',
      },
    ],
  },
  {
    name: 'nullif',
    tips: '比较两个值，如果相等则返回 NULL。',
    example: 'nullif([列1], [列2])',
    children: [
      {
        name: '值1',
        tips: '第一个比较值。',
      },
      {
        name: '值2',
        tips: '第二个比较值。',
      },
    ],
  },
  {
    name: 'if',
    tips: '条件判断函数，如果条件为真则返回第一个值，否则返回第二个值。',
    example: 'if([成绩] >= 60, "及格", "不及格")',
    children: [
      {
        name: '条件',
        tips: '需要评估的条件。',
      },
      {
        name: '值1',
        tips: '条件为真时返回的值。',
      },
      {
        name: '值2',
        tips: '条件为假时返回的值。',
      },
    ],
  },
  {
    name: 'ifnull',
    tips: '如果第一个参数为 NULL，则返回第二个参数。',
    example: 'ifnull([备注], "无备注")',
    children: [
      {
        name: '值1',
        tips: '可能为空的列或值。',
      },
      {
        name: '值2',
        tips: '如果值1为 NULL 时返回的值。',
      },
    ],
  },
  {
    name: 'cast',
    tips: '将值转换为指定的数据类型。',
    example: 'cast([价格] AS DECIMAL(10, 2))',
    children: [
      {
        name: '值',
        tips: '需要转换的值。',
      },
      {
        name: '数据类型',
        tips: '目标数据类型（如 INT、VARCHAR、DATE 等）。',
      },
    ],
  },
  {
    name: 'convert',
    tips: '将值转换为指定的数据类型。',
    example: 'convert([价格], DECIMAL(10, 2))',
    children: [
      {
        name: '值',
        tips: '需要转换的值。',
      },
      {
        name: '数据类型',
        tips: '目标数据类型（如 INT、VARCHAR、DATE 等）。',
      },
    ],
  },
  {
    name: 'date_add',
    tips: '在日期上添加指定的时间间隔。',
    example: 'date_add([订单日期], INTERVAL 7 DAY)',
    children: [
      {
        name: '日期',
        tips: '需要操作的日期。',
      },
      {
        name: '间隔',
        tips: '时间间隔（如 DAY、MONTH、YEAR 等）。',
      },
    ],
  },
  {
    name: 'date_sub',
    tips: '从日期中减去指定的时间间隔。',
    example: 'date_sub([订单日期], INTERVAL 7 DAY)',
    children: [
      {
        name: '日期',
        tips: '需要操作的日期。',
      },
      {
        name: '间隔',
        tips: '时间间隔（如 DAY、MONTH、YEAR 等）。',
      },
    ],
  },
  {
    name: 'datediff',
    tips: '计算两个日期之间的差值。',
    example: 'datediff([结束日期], [开始日期])',
    children: [
      {
        name: '日期1',
        tips: '第一个日期。',
      },
      {
        name: '日期2',
        tips: '第二个日期。',
      },
    ],
  },
  {
    name: 'date_format',
    tips: '格式化日期为指定的格式。',
    example: 'date_format([订单日期], "%Y-%m-%d")',
    children: [
      {
        name: '日期',
        tips: '需要格式化的日期。',
      },
      {
        name: '格式',
        tips: '目标格式（如 "%Y-%m-%d"）。',
      },
    ],
  },
  {
    name: 'now',
    tips: '返回当前日期和时间。',
    example: 'now()',
    children: [],
  },
  {
    name: 'year',
    tips: '从日期中提取年份。',
    example: 'year([订单日期])',
    children: [
      {
        name: '日期',
        tips: '需要提取年份的日期。',
      },
    ],
  },
  {
    name: 'month',
    tips: '从日期中提取月份。',
    example: 'month([订单日期])',
    children: [
      {
        name: '日期',
        tips: '需要提取月份的日期。',
      },
    ],
  },
  {
    name: 'day',
    tips: '从日期中提取日。',
    example: 'day([订单日期])',
    children: [
      {
        name: '日期',
        tips: '需要提取日的日期。',
      },
    ],
  },
  {
    name: 'hour',
    tips: '从时间中提取小时。',
    example: 'hour([订单时间])',
    children: [
      {
        name: '时间',
        tips: '需要提取小时的时间。',
      },
    ],
  },
  {
    name: 'minute',
    tips: '从时间中提取分钟。',
    example: 'minute([订单时间])',
    children: [
      {
        name: '时间',
        tips: '需要提取分钟的时间。',
      },
    ],
  },
  {
    name: 'second',
    tips: '从时间中提取秒。',
    example: 'second([订单时间])',
    children: [
      {
        name: '时间',
        tips: '需要提取秒的时间。',
      },
    ],
  },
  {
    name: 'trim',
    tips: '去除字符串两端的空格。',
    example: 'trim([备注])',
    children: [
      {
        name: '字符串',
        tips: '需要去除空格的字符串。',
      },
    ],
  },
  {
    name: 'ltrim',
    tips: '去除字符串左侧的空格。',
    example: 'ltrim([备注])',
    children: [
      {
        name: '字符串',
        tips: '需要去除左侧空格的字符串。',
      },
    ],
  },
  {
    name: 'rtrim',
    tips: '去除字符串右侧的空格。',
    example: 'rtrim([备注])',
    children: [
      {
        name: '字符串',
        tips: '需要去除右侧空格的字符串。',
      },
    ],
  },
  {
    name: 'upper',
    tips: '将字符串转换为大写。',
    example: 'upper([姓名])',
    children: [
      {
        name: '字符串',
        tips: '需要转换为大写的字符串。',
      },
    ],
  },
  {
    name: 'lower',
    tips: '将字符串转换为小写。',
    example: 'lower([姓名])',
    children: [
      {
        name: '字符串',
        tips: '需要转换为小写的字符串。',
      },
    ],
  },
  {
    name: 'substring',
    tips: '从字符串中提取子字符串。',
    example: 'substring([备注], 1, 10)',
    children: [
      {
        name: '字符串',
        tips: '需要提取子字符串的字符串。',
      },
      {
        name: '起始位置',
        tips: '子字符串的起始位置。',
      },
      {
        name: '长度',
        tips: '子字符串的长度。',
      },
    ],
  },
  {
    name: 'replace',
    tips: '替换字符串中的子串。',
    example: 'replace([备注], "旧值", "新值")',
    children: [
      {
        name: '字符串',
        tips: '需要替换的字符串。',
      },
      {
        name: '旧值',
        tips: '需要被替换的子串。',
      },
      {
        name: '新值',
        tips: '替换后的子串。',
      },
    ],
  },
  {
    name: 'length',
    tips: '返回字符串的长度。',
    example: 'length([备注])',
    children: [
      {
        name: '字符串',
        tips: '需要计算长度的字符串。',
      },
    ],
  },
  {
    name: 'char_length',
    tips: '返回字符串的字符数。',
    example: 'char_length([备注])',
    children: [
      {
        name: '字符串',
        tips: '需要计算字符数的字符串。',
      },
    ],
  },
  {
    name: 'round',
    tips: '对数值进行四舍五入。',
    example: 'round([价格], 2)',
    children: [
      {
        name: '数值',
        tips: '需要四舍五入的数值。',
      },
      {
        name: '小数位数',
        tips: '保留的小数位数。',
      },
    ],
  },
  {
    name: 'truncate',
    tips: '截断数值到指定的小数位数。',
    example: 'truncate([价格], 2)',
    children: [
      {
        name: '数值',
        tips: '需要截断的数值。',
      },
      {
        name: '小数位数',
        tips: '保留的小数位数。',
      },
    ],
  },
  {
    name: 'abs',
    tips: '返回数值的绝对值。',
    example: 'abs([差值])',
    children: [
      {
        name: '数值',
        tips: '需要计算绝对值的数值。',
      },
    ],
  },
  {
    name: 'sqrt',
    tips: '返回数值的平方根。',
    example: 'sqrt([面积])',
    children: [
      {
        name: '数值',
        tips: '需要计算平方根的数值。',
      },
    ],
  },
  {
    name: 'power',
    tips: '返回数值的幂值。',
    example: 'power([基数], [指数])',
    children: [
      {
        name: '基数',
        tips: '需要计算幂值的基数。',
      },
      {
        name: '指数',
        tips: '幂值的指数。',
      },
    ],
  },
  {
    name: 'mod',
    tips: '返回两个数值相除的余数。',
    example: 'mod([被除数], [除数])',
    children: [
      {
        name: '被除数',
        tips: '需要计算的被除数。',
      },
      {
        name: '除数',
        tips: '需要计算的除数。',
      },
    ],
  },
  {
    name: 'rand',
    tips: '返回一个随机数。',
    example: 'rand()',
    children: [],
  },
  {
    name: 'group_concat',
    tips: '将分组结果连接为字符串。',
    example: 'group_concat([姓名], ",")',
    children: [
      {
        name: '列名',
        tips: '需要连接的列。',
      },
      {
        name: '分隔符',
        tips: '连接时的分隔符。',
      },
    ],
  },
  {
    name: 'json_object',
    tips: '创建 JSON 对象。',
    example: 'json_object("name", [姓名], "age", [年龄])',
    children: [
      {
        name: '键值对',
        tips: 'JSON 对象的键值对。',
      },
    ],
  },
  {
    name: 'json_array',
    tips: '创建 JSON 数组。',
    example: 'json_array([值1], [值2])',
    children: [
      {
        name: '值',
        tips: '需要添加到 JSON 数组的值。',
      },
    ],
  },
  {
    name: 'json_extract',
    tips: '从 JSON 数据中提取值。',
    example: 'json_extract([json列], "$.name")',
    children: [
      {
        name: 'JSON 数据',
        tips: '需要提取值的 JSON 数据。',
      },
      {
        name: '路径',
        tips: '提取值的路径（如 "$.name"）。',
      },
    ],
  },
  {
    name: 'json_contains',
    tips: '检查 JSON 是否包含某个值。',
    example: 'json_contains([json列], "value", "$.name")',
    children: [
      {
        name: 'JSON 数据',
        tips: '需要检查的 JSON 数据。',
      },
      {
        name: '值',
        tips: '需要检查的值。',
      },
      {
        name: '路径',
        tips: '检查值的路径（如 "$.name"）。',
      },
    ],
  },
  {
    name: 'json_merge',
    tips: '合并多个 JSON 数据。',
    example: 'json_merge([json列1], [json列2])',
    children: [
      {
        name: 'JSON 数据1',
        tips: '第一个 JSON 数据。',
      },
      {
        name: 'JSON 数据2',
        tips: '第二个 JSON 数据。',
      },
    ],
  },
  {
    name: 'isnull',
    tips: '检查值是否为 NULL。',
    example: 'isnull([备注])',
    children: [
      {
        name: '值',
        tips: '需要检查的值。',
      },
    ],
  },
  {
    name: 'greatest',
    tips: '返回一组值中的最大值。',
    example: 'greatest([值1], [值2], [值3])',
    children: [
      {
        name: '值',
        tips: '需要比较的值。',
      },
    ],
  },
  {
    name: 'least',
    tips: '返回一组值中的最小值。',
    example: 'least([值1], [值2], [值3])',
    children: [
      {
        name: '值',
        tips: '需要比较的值。',
      },
    ],
  },
  {
    name: 'str_to_date',
    tips: '将字符串转换为日期。',
    example: 'str_to_date([日期字符串], "%Y-%m-%d")',
    children: [
      {
        name: '字符串',
        tips: '需要转换的字符串。',
      },
      {
        name: '格式',
        tips: '字符串的日期格式（如 "%Y-%m-%d"）。',
      },
    ],
  },
  {
    name: 'date',
    tips: '提取日期部分。',
    example: 'date([日期时间])',
    children: [
      {
        name: '日期时间',
        tips: '需要提取日期的日期时间值。',
      },
    ],
  },
  {
    name: 'time',
    tips: '提取时间部分。',
    example: 'time([日期时间])',
    children: [
      {
        name: '日期时间',
        tips: '需要提取时间的日期时间值。',
      },
    ],
  },
  {
    name: 'timestamp',
    tips: '返回时间戳。',
    example: 'timestamp([日期时间])',
    children: [
      {
        name: '日期时间',
        tips: '需要转换为时间戳的日期时间值。',
      },
    ],
  },
  {
    name: 'uuid',
    tips: '生成 UUID。',
    example: 'uuid()',
    children: [],
  },
]
