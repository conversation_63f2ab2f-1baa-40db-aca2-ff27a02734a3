<template>
  <div class="action-buttons">
    <template v-for="btn in visibleBtns" :key="btn.key">
      <button
        :class="btn.key + '-btn action-btn ' + (onlyIcon ? 'sm' : '')"
        @click="handleBtnClick(btn)"
      >
        <div class="action-btn-container" v-if="!onlyIcon">
          <component :is="btn.icon"></component>
          <div>{{ btn.title }}</div>
        </div>
        <a-tooltip v-else :title="btn.title">
          <template #default>
            <div class="action-btn-icon">
              <component :is="btn.icon"></component>
            </div>
          </template>
        </a-tooltip>
      </button>
    </template>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch, markRaw } from 'vue'
import filterIcon from './buttons-icon/filter-icon.vue'
import groupIcon from './buttons-icon/group-icon.vue'
import JoinsIcon from './buttons-icon/joins-icon.vue'
import orderIcon from './buttons-icon/order-icon.vue'
import limitIcon from './buttons-icon/limit-icon.vue'
import fieldIcon from './buttons-icon/field-icon.vue'

const props = defineProps<{
  dataList: any[]
  index: number
}>()

const emits = defineEmits(['add', 'update'])

const onlyIcon = ref(false)
const operatorBtns = ref([
  {
    title: '筛选器',
    sort: 3,
    key: 'where',
    icon: markRaw(filterIcon),
  },
  {
    title: '汇总',
    sort: 4,
    key: 'group',
    icon: markRaw(groupIcon),
  },
  {
    title: '联合（Join）数据',
    sort: 1,
    key: 'joins',
    icon: markRaw(JoinsIcon),
  },
  {
    title: '自定义列',
    sort: 2,
    key: 'customField',
    icon: markRaw(fieldIcon),
  },
  {
    title: '排序',
    sort: 5,
    key: 'order',
    icon: markRaw(orderIcon),
  },
  {
    title: '行数限制',
    sort: 6,
    key: 'limitValue',
    icon: markRaw(limitIcon),
  },
])
const visibleBtns = ref(operatorBtns.value)

const nextData = computed<any>(() => {
  const index = props.index + 1
  if (index >= props.dataList.length) return null
  return props.dataList[index]
})

const data = computed<any>(() => props.dataList[props.index])

const lastData = computed<any>(() => {
  const index = props.dataList.length - 1
  return props.dataList[index]
})

const groupData = computed<any>(() => {
  return props.dataList.find((it) => it.type === 'group')
})

const lastGroupData = computed<any>(() => {
  const reverseData = [...props.dataList].reverse()
  return reverseData.find((it) => it.type === 'group')
})

const allGroupData = computed<any>(() => {
  return props.dataList.filter((it) => it.type === 'group')
})

const currentBtn = computed<any>(() => {
  return operatorBtns.value.find((btn) => btn.key === data.value.type)
})

const nextBtn = computed<any>(() => {
  return operatorBtns.value.find((btn) => btn.key === nextData.value?.type)
})

watch(
  () => nextData.value,
  () => {
    if (nextData.value) {
      onlyIcon.value = true
      if (nextBtn.value) {
        console.log('[ currentBtn.value ] >', currentBtn.value)
        if (currentBtn.value && nextBtn.value.sort - currentBtn.value.sort >= 1) {
          visibleBtns.value = operatorBtns.value.filter((it) => {
            if (currentBtn.value.key === 'joins') {
              return it.sort >= currentBtn.value.sort && it.sort < nextBtn.value.sort
            }
            return it.sort > currentBtn.value.sort && it.sort < nextBtn.value.sort
          })
        } else {
          visibleBtns.value = operatorBtns.value.filter((it) => it.sort < nextBtn.value.sort)
        }
      } else {
        visibleBtns.value = []
      }
    }
  },
  {
    immediate: true,
    deep: true,
  },
)

watch(
  () => [lastData.value, groupData.value],
  () => {
    if (lastData.value === data.value) {
      if (['dataSource', 'joins'].includes(lastData.value.type)) {
        onlyIcon.value = false
        visibleBtns.value = operatorBtns.value
      } else {
        if (groupData.value?.value?.group?.length) {
          operatorBtns.value.forEach((it) => {
            if (it.key === 'order') it.sort = -1
            if (it.key === 'limitValue') it.sort = 0
          })
          if (lastData.value.type === 'group') {
            onlyIcon.value = false
            visibleBtns.value =
              allGroupData.value.length > 1 && !lastGroupData.value.value?.group.length
                ? []
                : operatorBtns.value
          } else {
            onlyIcon.value = currentBtn.value.sort >= 4 ? true : false
            visibleBtns.value = operatorBtns.value.filter((it) => it.sort > currentBtn.value.sort)
          }
        } else {
          onlyIcon.value = currentBtn.value.sort >= 4 ? true : false
          visibleBtns.value = operatorBtns.value.filter((it) => it.sort > currentBtn.value.sort)
        }
      }
    }
  },
  {
    immediate: true,
    deep: true,
  },
)

function handleBtnClick(btn: any) {
  emits(onlyIcon.value ? 'update' : 'add', btn)
}
</script>

<style lang="less" scoped>
.action-buttons {
  margin-top: 0.5rem;
}
.action-btn {
  cursor: pointer;
  border: none;
  padding: 12px 16px;
  font-weight: bold;
  border-radius: 6px;
  margin-right: 16px;
  margin-top: 16px;
  &.sm {
    margin-top: 0;
    margin-right: 8px;
    padding: 8px;
  }
  &-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    svg {
      width: 16px;
      height: 16px;
    }
  }
  &-container {
    min-width: 60px;
  }
}
</style>
