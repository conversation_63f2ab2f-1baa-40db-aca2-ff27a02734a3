import service from '@/api'
import { MOCK_DATA } from './indexform-mock'

/**
 *  - 新增指标模板
 * @param {string} formCode - 指标配置模板编号
 * @param {string} formName - 指标配置模板名称
 * @param {string} remarks -  备注
 * @param {string} fieldsData - 字段数据
 * @param {string} compData - 组件数据
 * @param {number} businessType - 业务类型1=301
 * @param {string} opUser -
 * @param {string} tenantId -
 * @param {string} opUserName -
 * @returns {any} - 返回一个解析为响应数据的Promise
 */

export async function addIndexTemplate(
  data: {
    formCode: string
    formName: string
    remarks: string
    fieldsData?: string
    compData?: string
    businessType: number
  },
  headers?: {
    opUser?: string
    tenantId?: string
    opUserName?: string
  },
): Promise<any> {
  return service(
    {
      method: 'POST',
      url: '/indexManage/indexForm/addIndexTemplate',
      data,
      headers,
    },
    MOCK_DATA.addIndexTemplate,
  )
}

/**
 * 新增接口 - 分页获取指标配置模板列表
 * @param {string} formName - 指标配置模板名称
 * @param {number} isDeleted - 是否启用:1是0否
 * @param {number} status - 状态:0编辑中1已提交
 * @param {string} startTime - 开始时间
 * @param {string} endTime - 结束时间
 * @param {number} isRelease - 是否已发布:1是0否
 * @param {number} businessType - 业务类型
 * @param {number} pageIndex - 页码
 * @param {number} pageSize - 页大小
 * @param {string} opUser - 操作人
 * @param {string} opUserName - 操作人
 * @param {string} tenantId - 租户编号
 * @returns {any} - 返回一个解析为响应数据的Promise
 */

export async function getIndexFormDataListByPage(
  data: {
    formName?: string
    isDeleted?: number
    status?: number
    startTime?: string
    endTime?: string
    isRelease?: number
    businessType?: number
    pageIndex: number
    pageSize: number
  },
  headers?: {
    opUser?: string
    opUserName?: string
    tenantId?: string
  },
): Promise<{
  code: string
  msg: string
  data: {
    records: Array<{
      formId: string
      formCode: string
      formName: string
      isDeleted: number
      status: number
      revision: number
      remarks: string
      tenantId: string
      createUser: string
      createUsername: string
      createTime: string
      updateUser: string
      updateUsername: string
      updateTime: string
      isRelease: number
      currentVersion: number
    }>
  }
}> {
  return service(
    {
      method: 'GET',
      url: '/indexManage/indexForm/getIndexFormDataListByPage',
      params: data,
      headers,
    },
    MOCK_DATA.getIndexFormDataListByPage,
  )
}

/**
 * 新增接口 - 删除指标模板
 * @param {string} formId - 主键id
 * @param {string} opUser - 操作人
 * @param {string} opUserName - 操作人
 * @param {string} tenantId - 租户编号
 * @returns {any} - 返回一个解析为响应数据的Promise
 */

export async function delIndexTemplate(
  data: {
    formId?: string
  },
  headers?: {
    opUser?: string
    opUserName?: string
    tenantId?: string
  },
): Promise<{
  code: string
  msg: string
  data: Array<{ userId: string; userName: string }>
}> {
  return service(
    {
      method: 'POST',
      url: '/indexManage/indexForm/delIndexTemplate',
      data,
      headers,
    },
    MOCK_DATA.delIndexTemplate,
  )
}

/**
 * 新增接口 - 保存指标模板
 * @param {string} formId - 主键id
 * @param {string} formCode - 指标配置模板编号
 * @param {string} formName - 指标配置模板名称
 * @param {string} remarks - 备注
 * @param {string} fieldsData - 字段数据
 * @param {string} compData - 组件数据
 * @param {number} revision - 乐观锁
 * @param {string} opUser - 操作人
 * @param {string} opUserName - 操作人
 * @param {string} tenantId - 租户编号
 * @returns {any} - 返回一个解析为响应数据的Promise
 */

export async function saveIndexTemplate(
  data: {
    formId: string
    formCode?: string
    formName?: string
    remarks?: string
    fieldsData?: string
    compData?: string
    revision?: number
  },
  headers?: {
    opUser?: string
    opUserName?: string
    tenantId?: string
  },
): Promise<{
  code: string
  msg: string
  data: Array<{ userId: string; userName: string }>
}> {
  return service(
    {
      method: 'POST',
      url: '/indexManage/indexForm/saveIndexTemplate',
      data,
      headers,
    },
    MOCK_DATA.saveIndexTemplate,
  )
}

/**
 * 新增接口 - 获取有效指标配置模板列表
 * @param {string} formName - 指标配置模板名称
 * @param {string} opUser - 操作人
 * @param {string} opUserName - 操作人
 * @param {string} tenantId - 租户编号
 * @returns {any} - 返回一个解析为响应数据的Promise
 */

export async function getValidIndexFormDataList(
  data: {
    formName?: string
  },
  headers?: {
    opUser?: string
    opUserName?: string
    tenantId?: string
  },
): Promise<{
  code: string
  msg: string
  data: Array<{
    formId: string
    formCode: string
    formName: string
    historyId: string
    fieldsData: string
    compData: string
    isDeleted: number
    status: number
    revision: number
    remarks: string
    tenantId: string
    createUser: string
    createUsername: string
    createTime: string
    updateUser: string
    updateUsername: string
    updateTime: string
    isRelease: number
    currentVersion: number
  }>
}> {
  return service(
    {
      method: 'GET',
      url: '/indexManage/indexForm/getValidIndexFormDataList',
      params: data,
      headers,
    },
    MOCK_DATA.getValidIndexFormDataList,
  )
}

/**
 * 新增接口 - 获取单个指标配置模板数据
 * @param {string} formId - 指标配置模板主键
 * @param {string} opUser - 操作人
 * @param {string} opUserName - 操作人名称
 * @param {string} tenantId - 租户编号
 * @returns {any} - 返回一个解析为响应数据的Promise
 */

export async function getSingleIndexFormData(
  data: {
    formId: string
  },
  headers?: {
    opUser?: string
    opUserName?: string
    tenantId?: string
  },
): Promise<{
  code: string
  msg: string
  data: {
    formId: string
    formCode: string
    formName: string
    fieldsData: string
    compData: string
    isDeleted: number
    status: number
    revision: number
    remarks: string
    tenantId: string
    createUser: string
    createUsername: string
    createTime: string
    updateUser: string
    updateUsername: string
    updateTime: string
    isRelease: number
    currentVersion: number
    fieldList: Array<{ fieldId: string; fieldName: string; fieldType: string }>
  }
}> {
  return service(
    {
      method: 'GET',
      url: '/indexManage/indexForm/getSingleIndexFormData',
      params: data,
      headers,
    },
    MOCK_DATA.getSingleIndexFormData,
  )
}

/**
 * 新增接口 - 指标配置模板发布
 * @param {string} formId - 指标配置模板主键
 * @param {string} opUser - 操作人
 * @param {string} opUserName - 操作人名称
 * @param {string} tenantId - 租户编号
 * @returns {any} - 返回一个解析为响应数据的Promise
 */

export async function deployIndexFormData(
  data: {
    formId: string
  },
  headers?: {
    opUser?: string
    opUserName?: string
    tenantId?: string
  },
): Promise<{
  code: string
  msg: string
  data: undefined
}> {
  return service(
    {
      method: 'POST',
      url: '/indexManage/indexForm/deployIndexFormData',
      data,
      headers,
    },
    MOCK_DATA.deployIndexFormData,
  )
}

/**
 * 新增接口 - 获取指标配置模板历史版本数据列表
 * @param {string} formId - 指标配置模板id
 * @param {string} startTime - 开始时间
 * @param {string} endTime - 结束时间
 * @param {number} pageIndex - 页码
 * @param {number} pageSize - 页大小
 * @param {string} opUser - 操作人
 * @param {string} opUserName - 操作人名称
 * @param {string} tenantId - 租户编号
 * @returns {any} - 返回一个解析为响应数据的Promise
 */

export async function getHistoryListPage(
  data: {
    formId: string
    startTime?: string
    endTime?: string
    pageIndex: number
    pageSize: number
  },
  headers?: {
    opUser?: string
    opUserName?: string
    tenantId?: string
  },
): Promise<{
  code: string
  msg: string
  data: {
    records: Array<{
      historyId: string
      formId: string
      formCode: string
      formName: string
      isDeleted: number
      revision: number
      remarks: string
      version: number
      tenantId: string
      createUser: string
      createUsername: string
      createTime: string
      updateUser: string
      updateUsername: string
      updateTime: string
    }>
  }
}> {
  return service(
    {
      method: 'GET',
      url: '/indexManage/indexForm/getHistoryListPage',
      params: data,
      headers,
    },
    MOCK_DATA.getHistoryListPage,
  )
}

/**
 * 新增接口 - 根据类型获取单个指标配置模板数据
 * @param {number} businessType - 业务类型1:301
 * @param {string} opUser - 操作人
 * @param {string} opUserName - 操作人名称
 * @param {string} tenantId - 租户编号
 * @returns {any} - 返回一个解析为响应数据的Promise
 */

export async function getIndexFormByType(
  data: {
    businessType?: number
  },
  headers?: {
    opUser?: string
    opUserName?: string
    tenantId?: string
  },
): Promise<{
  code: string
  msg: string
  data: {
    formId: string
    formCode: string
    formName: string
    fieldsData: string
    compData: string
    isDeleted: number
    status: number
    revision: number
    remarks: string
    tenantId: string
    createUser: string
    createUsername: string
    createTime: string
    updateUser: string
    updateUsername: string
    updateTime: string
    isRelease: number
    currentVersion: number
    fieldList: Array<{ fieldId: string; fieldName: string; fieldType: string }>
  }
}> {
  return service(
    {
      method: 'GET',
      url: '/indexManage/indexForm/getIndexFormByType',
      params: data,
      headers,
    },
    MOCK_DATA.getIndexFormByType,
  )
}
