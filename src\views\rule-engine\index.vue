<template>
  <main class="rule-engine-container">
    <header flex="~ justify-between">
      <Title
        title="数据建模管理"
        desc="数据建模管理，在项目中管理数据模型的函数、可视化创建规则，以及规则的应用"
      />
      <Button type="primary" @click="handleCreateProject">创建项目</Button>
    </header>
    <Spin :spinning="loading">
      <div flex="~ gap-6 wrap" v-if="projectList.length">
        <ProjectCard
          v-for="item in projectList"
          :key="item.autoId"
          :data="item"
          @edit="handleEdit"
          @delete="handleDelete"
        />
      </div>
      <Empty v-else description="暂无数据" style="margin-top: 100px" />
    </Spin>

    <!-- 使用项目编辑模态框组件 -->
    <ProjectEditModal
      v-model:open="editModalVisible"
      :project="currentProject"
      @updateSuccess="handleEditSuccess"
      @createSuccess="handleCreateSuccess"
      @cancel="handleCancel"
    />
  </main>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import ProjectCard from './components/project-card.vue'
import ProjectEditModal from './components/project-edit-modal.vue'
import { Spin, Button, Empty } from 'ant-design-vue'
import { getProjectList } from './service'
import type { ProjectItem } from './data'

const loading = ref(false)
const projectList = ref<ProjectItem[]>([])
const editModalVisible = ref(false)
const currentProject = ref<ProjectItem>()

// 获取项目列表
const fetchProjects = async () => {
  loading.value = true
  try {
    const res = await getProjectList()
    projectList.value = res.data || []
  } finally {
    loading.value = false
  }
}

// 处理编辑按钮点击
const handleEdit = (project: ProjectItem) => {
  currentProject.value = project
  editModalVisible.value = true
}

// 处理编辑成功
const handleEditSuccess = () => {
  fetchProjects()
}

// 处理创建成功
const handleCreateSuccess = () => {
  fetchProjects()
}

// 处理删除
const handleDelete = () => {
  fetchProjects()
}

// 处理取消
const handleCancel = () => {
  editModalVisible.value = false
  currentProject.value = void 0
}

// 处理创建项目
const handleCreateProject = () => {
  editModalVisible.value = true
  currentProject.value = void 0
}
onMounted(() => {
  fetchProjects()
})
</script>
<style scoped lang="less">
.mg-rt20 {
  margin-right: 20px;
}
.rule-engine-container {
  background: white;
  height: 100%;
}
</style>
