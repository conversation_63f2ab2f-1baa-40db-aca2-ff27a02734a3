<template>
  <a-modal
    v-model:open="open"
    title="用户详情"
    width="800px"
    :mask-closable="false"
    :footer="false"
  >
    <a-spin :spinning="spinning">
      <div class="section-main">
        <div class="item">
          <div class="t">用户编号：</div>
          <div class="con">{{ userDetail.userId }}</div>
        </div>
        <div class="item">
          <div class="t">登录账号：</div>
          <div class="con">{{ userDetail.loginAccount }}</div>
        </div>
        <div class="item">
          <div class="t">用户名称：</div>
          <div class="con">{{ userDetail.userName }}</div>
        </div>
        <div class="item">
          <div class="t">用户类型：</div>
          <div class="con">{{ userDetail.userTypeName }}</div>
        </div>
        <div class="item">
          <div class="t">手机号：</div>
          <div class="con">{{ userDetail.mobile }}</div>
        </div>
        <div class="item">
          <div class="t">账号到期时间：</div>
          <div class="con">{{ userDetail.accountEndDate }}</div>
        </div>
        <div class="item">
          <div class="t">用户状态：</div>
          <div class="con">{{ userDetail.userStatusName }}</div>
        </div>
        <div class="item">
          <div class="t">工号：</div>
          <div class="con">{{ userDetail.employeeCode }}</div>
        </div>
        <div class="item">
          <div class="t">员工状态：</div>
          <div class="con">{{ userDetail.employeeStatusName }}</div>
        </div>
        <div class="item">
          <div class="t">入职日期：</div>
          <div class="con">{{ userDetail.entryDate }}</div>
        </div>
        <div class="item">
          <div class="t">离职日期：</div>
          <div class="con">{{ userDetail.leaveDate }}</div>
        </div>
        <div class="item">
          <div class="t">部门名称：</div>
          <div class="con">{{ userDetail.deptName }}</div>
        </div>
        <div class="item">
          <div class="t">岗位名称：</div>
          <div class="con">{{ userDetail.jobName }}</div>
        </div>
        <div class="item">
          <div class="t">职务名称：</div>
          <div class="con">{{ userDetail.positionName }}</div>
        </div>
        <div class="item">
          <div class="t">备注：</div>
          <div class="con">{{ userDetail.remark }}</div>
        </div>
        <div class="item">
          <div class="t">已分配角色列表：</div>
          <div class="con">
            <ul class="ul">
              <li class="li" v-for="(item, index) in userDetail.roleList" :key="item.roleId">
                {{ index + 1 }} {{ item.roleName }}
              </li>
            </ul>
          </div>
        </div>
        <div class="item">
          <div class="t">可查看用户组列表：</div>
          <div class="con">
            <ul class="ul">
              <li
                class="li"
                v-for="(item, index) in userDetail.userGroupList"
                :key="item.userGroupId"
              >
                {{ index + 1 }} {{ item.userGroupName }}
              </li>
            </ul>
          </div>
        </div>
        <div class="item">
          <div class="t">可查看部门列表：</div>
          <div class="con">
            <ul class="ul">
              <li class="li" v-for="(item, index) in userDetail.viewDeptList" :key="item.deptId">
                {{ index + 1 }} {{ item.deptName }}
              </li>
            </ul>
          </div>
        </div>
      </div>
    </a-spin>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, onBeforeMount } from 'vue'
import { viewUserDetail } from '@/api/services/permission'

const open = defineModel<boolean>({ default: false, required: true })
const props = withDefaults(defineProps<{ userId: string }>(), {
  userId: '',
})

const spinning = ref(false)
const userDetail = ref<any>({})

onBeforeMount(() => {
  viewUserDetailFn()
})

/**
 * 获取用户详情数据
 */
async function viewUserDetailFn() {
  try {
    spinning.value = true
    const { data } = await viewUserDetail(props.userId)
    if (Array.isArray(data)) {
      userDetail.value = data[0]
    }
  } catch (error) {
    console.log(error)
  } finally {
    spinning.value = false
  }
}
</script>

<style scoped lang="less">
.section-main {
  margin-top: 20px;
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 30px;
  .item {
    .t {
      font-weight: bold;
    }
  }
}
</style>
