<template>
  <div class="menu-list">
    <div
      class="menu-item"
      style="justify-content: space-between"
      v-for="(item, key) in list"
      :key="key"
      @click="handleClick(item)"
    >
      <!-- <div> -->
      <div class="menu-item-icon" v-if="showIcon">
        <slot name="icon">
          <component :is="item.icon" />
        </slot>
      </div>
      <div class="menu-item-text" v-if="showIcon">{{ item.label }}</div>
      <!-- </div> -->
      <!-- <div v-else> -->
      <div class="menu-item-label" v-if="!showIcon">{{ item.label }}</div>
      <div class="menu-item-value" v-if="!showIcon && !hideValue">{{ item.value }}</div>
      <!-- </div> -->
    </div>
  </div>
</template>

<script setup lang="ts">
const props = defineProps({
  list: {
    type: Array<any>,
    default: () => [],
  },
  showIcon: {
    type: Boolean,
    default: false,
    required: false,
  },
  clickCallBack: {
    type: Function,
    default: () => {},
    required: false,
  },
  hideValue: {
    type: Boolean,
    default: false,
    required: false,
  },
})

const handleClick = (item: any) => {
  props.clickCallBack && props.clickCallBack(item)
}
</script>

<style scoped lang="less">
.menu-list {
  // padding: 10px 5px;
  margin-top: 10px;
  width: 100%;
  .menu-item {
    display: flex;
    line-height: 32px;
    cursor: pointer;
    border-radius: 8px;
    color: #4c5773;
    font-weight: 600;
    padding: 0 10px;
    box-sizing: border-box;
    width: 100%;
    .menu-item-icon {
      color: #509ee3;
      margin-right: 15px;
    }
    .menu-item-label {
      color: #4c5773;
      font-weight: 600;
    }
    .menu-item-value {
      color: #b0b5c1;
    }
  }
  .menu-item:hover {
    background-color: #509ee3;
    color: #fff;
    .menu-item-icon,
    .menu-item-label,
    .menu-item-value {
      color: #fff;
    }
  }
}
</style>
