<!-- eslint-disable vue/no-mutating-props -->
<template>
  <div v-if="!current.comp"></div>
  <component
    v-else
    :is="current.comp"
    :key="current.key"
    v-model:value="data"
    :filterData="filterData"
    v-bind="componentProps"
  />
</template>

<script setup lang="ts">
import { computed } from 'vue'
import DateSelector from './date-selector.vue'
import MyRadio from './my-radio.vue'
import MyInput from './my-input.vue'
import MySelect from './my-select.vue'
import MyCheckbox from './my-checkbox.vue'
import NumberRange from './number-range.vue'
import { FormType } from './constant'
import type { FilterItem } from './type'

const props = defineProps<{
  formType: FormType
  filterData: FilterItem
  componentProps?: Record<string | symbol, any>
}>()
const data = defineModel<any>('value', { required: true })
const componentMap = {
  [FormType.date]: {
    comp: DateSelector,
    key: FormType.date,
  },
  [FormType.radio]: {
    comp: MyRadio,
    key: FormType.radio,
  },
  [FormType.input]: {
    comp: MyInput,
    key: FormType.input,
  },
  [FormType.select]: {
    comp: MySelect,
    key: FormType.select,
  },
  [FormType.checkbox]: {
    comp: MyCheckbox,
    key: FormType.checkbox,
  },
  [FormType.range]: {
    comp: NumberRange,
    key: FormType.range,
  },
  [FormType.whiteSpace]: {
    comp: null,
    key: FormType.whiteSpace,
  },
}
const current = computed(() => componentMap[props.formType])
</script>

<style scoped lang="less"></style>
