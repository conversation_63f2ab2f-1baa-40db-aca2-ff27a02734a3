<template>
  <a-spin :spinning="loading">
    <div style="display: flex; flex-direction: column; height: 100%">
      <a-tabs v-model:activeKey="activeKey">
        <a-tab-pane key="1">
          <template #tab>
            <div style="display: flex; align-items: center; flex-direction: row">
              <img style="width: 20px; height: 20px" src="@/assets/vllm.png" class="img-layout" />
              <span> vllm </span>
            </div>
          </template>
          <vllm :data="vllmList" @updateVllmSuccess="editVllm" @deleteVllmOk="deleteVllm" />
        </a-tab-pane>
        <a-tab-pane key="2">
          <template #tab>
            <div style="display: flex; align-items: center; flex-direction: row">
              <img style="width: 20px; height: 20px" src="@/assets/maas.png" class="img-layout" />
              <span> maas </span>
            </div>
          </template>
          <Maas :data="maasList" @updateMaasSuccess="editMaas" @deleteMaasOk="deleteMaas" />
        </a-tab-pane>
        <a-tab-pane key="3">
          <template #tab>
            <div style="display: flex; align-items: center; flex-direction: row">
              <img style="width: 20px; height: 20px" src="@/assets/ollama.png" class="img-layout" />
              <span style="margin-left: 5px"> Ollama </span>
            </div>
          </template>

          <Ollama
            :data="OllamaList"
            @updateOllamaSuccess="editOllama"
            @deleteOllamaOk="deleteOllama"
          />
        </a-tab-pane>
      </a-tabs>
      <span style="margin-top: 20px"> 模型列表 </span>

      <div style="display: flex; flex-direction: row; margin-top: 20px">
        <div class="ant-card">
          <div class="ant-card-body">
            <img src="@/assets/vllm.png" class="img-layout" />
            <div>vllm</div>
            <button type="button" class="btn-layout">
              <span role="img" aria-label="plus" class="anticon anticon-plus"
                ><svg
                  viewBox="64 64 896 896"
                  focusable="false"
                  data-icon="plus"
                  width="1em"
                  height="1em"
                  fill="currentColor"
                  aria-hidden="true"
                >
                  <path d="M482 152h60q8 0 8 8v704q0 8-8 8h-60q-8 0-8-8V160q0-8 8-8z"></path>
                  <path
                    d="M192 474h672q8 0 8 8v60q0 8-8 8H160q-8 0-8-8v-60q0-8 8-8z"
                  ></path></svg></span
              ><span class="text-layout" @click="toVllmAdd">添加模型</span>
            </button>
          </div>
        </div>

        <div class="ant-card" style="margin-left: 20px">
          <div class="ant-card-body">
            <img src="@/assets/maas.png" class="img-layout" />
            <div>maas</div>
            <button type="button" class="btn-layout">
              <span role="img" aria-label="plus" class="anticon anticon-plus"
                ><svg
                  viewBox="64 64 896 896"
                  focusable="false"
                  data-icon="plus"
                  width="1em"
                  height="1em"
                  fill="currentColor"
                  aria-hidden="true"
                >
                  <path d="M482 152h60q8 0 8 8v704q0 8-8 8h-60q-8 0-8-8V160q0-8 8-8z"></path>
                  <path
                    d="M192 474h672q8 0 8 8v60q0 8-8 8H160q-8 0-8-8v-60q0-8 8-8z"
                  ></path></svg></span
              ><span class="text-layout" @click="toMaasAdd">添加模型</span>
            </button>
          </div>
        </div>

        <div class="ant-card" style="margin-left: 20px">
          <div class="ant-card-body">
            <img src="@/assets/ollama.png" class="img-layout" />
            <div>Ollama</div>
            <button type="button" class="btn-layout">
              <span role="img" aria-label="plus" class="anticon anticon-plus"
                ><svg
                  viewBox="64 64 896 896"
                  focusable="false"
                  data-icon="plus"
                  width="1em"
                  height="1em"
                  fill="currentColor"
                  aria-hidden="true"
                >
                  <path d="M482 152h60q8 0 8 8v704q0 8-8 8h-60q-8 0-8-8V160q0-8 8-8z"></path>
                  <path
                    d="M192 474h672q8 0 8 8v60q0 8-8 8H160q-8 0-8-8v-60q0-8 8-8z"
                  ></path></svg></span
              ><span class="text-layout" @click="toOllamaAdd">添加模型</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </a-spin>

  <createVllm ref="createVllmRef" @createVllmOk="addVllm" />
  <createMaas ref="createMaasRef" @createMaasOk="addMaas" />
  <createOllama ref="createOllamaRef" @createOllamaOk="addOllama" />
</template>

<script lang="ts" setup>
// import { AppleOutlined, AndroidOutlined } from '@ant-design/icons-vue'
import { ref, onMounted, watch } from 'vue'
import { message } from 'ant-design-vue'
import { updateKnowledgeConfig } from '@/api/services/home'
import type { KnowledgeBaseConfig } from '@/api/type'
import vllm from '@/views/knowledge-builder/knowledge-model-config/model-configuration/vllm.vue'
import Maas from '@/views/knowledge-builder/knowledge-model-config/model-configuration/maas.vue'
import Ollama from '@/views/knowledge-builder/knowledge-model-config/model-configuration/Ollama.vue'
import createVllm from '@/views/knowledge-builder/knowledge-model-config/model-configuration/create-vllm.vue'
import createMaas from '@/views/knowledge-builder/knowledge-model-config/model-configuration/create-maas.vue'
import createOllama from '@/views/knowledge-builder/knowledge-model-config/model-configuration/create-ollama.vue'
import { useRoute } from 'vue-router'
const route = useRoute()
const props = defineProps<{ data: KnowledgeBaseConfig }>()
const emits = defineEmits(['updateSuccess'])
const activeKey = ref('1')
const loading = ref(false)

watch(
  () => props.data,
  (newVal, oldVal) => {
    console.log('%c 🍛 newVal: ', 'font-size:12px;background-color: #465975;color:#fff;', newVal)
    console.log('%c 🍖 oldVal: ', 'font-size:12px;background-color: #465975;color:#fff;', oldVal)

    updateDate()
  },
)
onMounted(() => {
  console.log(
    '%c 🥚 props.data: ',
    'font-size:12px;background-color: #4b4b4b;color:#fff;',
    props.data,
  )
  updateDate()
})
const createVllmRef = ref<InstanceType<typeof createVllm>>()
const createMaasRef = ref<InstanceType<typeof createMaas>>()
const createOllamaRef = ref<InstanceType<typeof createOllama>>()
const vllmList = ref([])
const maasList = ref([])
const OllamaList = ref([])
function updateDate() {
  vllmList.value = []
  maasList.value = []
  OllamaList.value = []
  const res = JSON.parse(props.data.config) as KnowledgeBaseConfig

  res.llm_select.forEach((item) => {
    if (item.type === 'vllm') {
      vllmList.value.push(item)
    } else if (item.type === 'maas') {
      maasList.value.push(item)
    } else if (item.type === 'Ollama') {
      OllamaList.value.push(item)
    }
  })
}
const formState = ref<KnowledgeBaseConfig>({
  graph_store: {
    database: '',
    password: '',
    uri: '',
    user: '',
  },
  vectorizer: {
    type: '',
    model: '',
    base_url: '',
    api_key: '',
  },
  prompt: {
    biz_scene: '',
    language: '',
  },
  llm_select: [],
  llm: {},
})
//添加Vllm模型
function addVllm(details: any) {
  formState.value = JSON.parse(props.data.config) as KnowledgeBaseConfig
  formState.value.llm_select.push({
    type: 'vllm',
    creator: 'openspg',
    create_Time: getCurrentTime(),
    model: details.model,
    base_url: details.base_url,
    desc: details.desc,
  })
  updateGloablConfigFn()
}

//修改Vllm模型
function editVllm(record: any) {
  formState.value = JSON.parse(props.data.config) as KnowledgeBaseConfig
  const index = formState.value.llm_select.findIndex((item) => item.llm_id === record.llm_id)
  formState.value.llm_select[index] = record
  updateGloablConfigFn()
  console.log('%c 🍕 record: ', 'font-size:12px;background-color: #465975;color:#fff;', record)
}
//删除Vllm模型
function deleteVllm(record: any) {
  console.log('%c 🍨 record: ', 'font-size:12px;background-color: #E41A6A;color:#fff;', record)
  formState.value = JSON.parse(props.data.config) as KnowledgeBaseConfig
  const index = formState.value.llm_select.findIndex((item) => item.llm_id === record.llm_id)
  formState.value.llm_select.splice(index, 1)
  updateGloablConfigFn()
}
//添加Maas模型
function addMaas(details: any) {
  formState.value = JSON.parse(props.data.config) as KnowledgeBaseConfig
  formState.value.llm_select.push({
    type: 'maas',
    creator: 'openspg',
    create_Time: getCurrentTime(),
    model: details.model,
    base_url: details.base_url,
    desc: details.desc,
  })
  updateGloablConfigFn()
}
//修改Maas模型
function editMaas(record: any) {
  formState.value = JSON.parse(props.data.config) as KnowledgeBaseConfig
  const index = formState.value.llm_select.findIndex((item) => item.llm_id === record.llm_id)
  formState.value.llm_select[index] = record
  updateGloablConfigFn()
}
//删除Maas模型
function deleteMaas(record: any) {
  formState.value = JSON.parse(props.data.config) as KnowledgeBaseConfig
  const index = formState.value.llm_select.findIndex((item) => item.llm_id === record.llm_id)
  formState.value.llm_select.splice(index, 1)
  updateGloablConfigFn()
}
//添加Ollama模型
function addOllama(details: any) {
  formState.value = JSON.parse(props.data.config) as KnowledgeBaseConfig
  formState.value.llm_select.push({
    type: 'Ollama',
    creator: 'openspg',
    create_Time: getCurrentTime(),
    model: details.model,
    base_url: details.base_url,
    desc: details.desc,
  })
  updateGloablConfigFn()
}
//修改Ollama模型
function editOllama(record: any) {
  formState.value = JSON.parse(props.data.config) as KnowledgeBaseConfig
  const index = formState.value.llm_select.findIndex((item) => item.llm_id === record.llm_id)
  formState.value.llm_select[index] = record
  updateGloablConfigFn()
}
//删除Ollama模型
function deleteOllama(record: any) {
  console.log('%c 🥖 record: ', 'font-size:12px;background-color: #EA7E5C;color:#fff;', record)
  console.log('========删除Ollama模型==============')
  formState.value = JSON.parse(props.data.config) as KnowledgeBaseConfig
  console.log(
    '%c 🍭 props.data: ',
    'font-size:12px;background-color: #FFDD4D;color:#fff;',
    props.data,
  )
  const index = formState.value.llm_select.findIndex((item) => item.llm_id === record.llm_id)
  formState.value.llm_select.splice(index, 1)
  updateGloablConfigFn()
}
async function updateGloablConfigFn() {
  console.log(route.params.projectId)
  console.log(props.data)
  loading.value = true
  const data = {
    id: route.params.projectId,
    name: props.data.name,
    namespace: props.data.namespace,
    description: props.data.description,
    config: JSON.stringify(formState.value),
  }
  await updateKnowledgeConfig({ id: route.params.projectId }, data)
  loading.value = false
  message.success('保存成功')
  emits('updateSuccess')
  // loading.value = true
  // const res = await updateKnowledgeConfig(
  //   { id: route.params.projectId, ctoken: 'bigfish_ctoken_19h6kga8eb' },
  //   {
  //     id: route.params.projectId,
  //     configName: 'Global Configuration',
  //     configId: 'KAG_CONFIG',
  //     version: '1',
  //     config: JSON.stringify(formState.value),
  //   },
  // )
  // if (res === 1) {
  //   loading.value = false
  //   message.success('保存成功')
  //   emits('modalAddOk')
  // } else {
  //   loading.value = false
  //   message.error('保存失败')
  //   emits('modalAddOk')
  // }
}

const getCurrentTime = () => {
  const now = new Date()
  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0') // 月份从0开始，需+1
  const day = String(now.getDate()).padStart(2, '0')
  const hours = String(now.getHours()).padStart(2, '0')
  const minutes = String(now.getMinutes()).padStart(2, '0')
  const seconds = String(now.getSeconds()).padStart(2, '0')

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}

function toVllmAdd() {
  createVllmRef.value?.show()
}
function toMaasAdd() {
  createMaasRef.value?.show()
}
function toOllamaAdd() {
  createOllamaRef.value?.show()
}
</script>

<style scoped>
.ant-card {
  width: 190px;
  height: 170px;
  border-radius: 8px;
}
.ant-card-body {
  width: 190px;
  height: 170px;
  border-radius: 8px;
  background: #fff;
  border: 1px solid #f0f0f0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding-top: 16px;
}
.btn-layout {
  background-color: #ffffff;
  border: 1px solid #dcdcdc;
  padding: 4px 8px;
  border-radius: 4px;
}
.img-layout {
  width: 56px;
  height: 56px;
  border-radius: 50%;
  border: none;
  margin-left: 0px;
  margin-right: 0px;
}
.text-layout {
  font-size: 12px;
  margin-left: 4px;
}
</style>
