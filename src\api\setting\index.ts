import service from '@/api'
import { SODATAFLINK } from '@/api/base'

type ArrayResponse<T> = {
  code: string
  data: { code: number; message: string; obj: Array<T> }
  msg: string
}

type DataResponse<T> = {
  code: string
  data: any
  msg: string
}

export function logoperateinfoPageList(data: { json: string }): Promise<DataResponse<string>> {
  return service({
    method: 'GET',
    url: `${SODATAFLINK}/new2dataplatform/checklog/logoperateinfoPageList`,
    params: data,
  })
}
