<template>
  <div class="sql-ace-editor-container">
    <!-- 编辑器工具栏 -->
    <div class="sql-editor-toolbar">
      <div class="toolbar-left">
        <span class="theme-label">SQL 编辑器</span>
      </div>
      <div class="toolbar-right">
        <!-- <a-tooltip title="手动触发智能提示 (Ctrl+Space)">
          <a-button
            type="text"
            size="small"
            @click="triggerAutoComplete"
            class="refresh-btn"
          >
            💡 智能提示
          </a-button>
        </a-tooltip> -->
        <!-- <a-tooltip title="如果光标位置不正确，点击此按钮修复">
          <a-button
            type="text"
            size="small"
            @click="forceRefreshEditor"
            class="refresh-btn"
          >
            🔄 修复光标
          </a-button>
        </a-tooltip> -->
      </div>
    </div>

    <!-- Ace 编辑器 -->
    <div class="editor-wrapper" :style="editorWrapperStyle">
      <VAceEditor
        ref="aceEditorRef"
        v-model:value="localValue"
        lang="sql"
        theme="github"
        :options="aceOptions"
        :readonly="readonly"
        style="height: 100%; width: 100%"
        @init="onInit"
        @update:value="onUpdate"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick, onMounted, watch } from 'vue'
import { VAceEditor } from 'vue3-ace-editor'

// Ace 相关导入
import 'ace-builds/src-noconflict/mode-sql'
import 'ace-builds/src-noconflict/theme-github'
import 'ace-builds/src-noconflict/ext-language_tools'
import ace from 'ace-builds/src-noconflict/ace'
import { ParamCategory } from './dynamic-param.vue'

// Props 定义
interface Props {
  readonly?: boolean
  height?: string | number
  enableAutoCompletion?: boolean
  tableFields?: Array<{
    fieldName: string
    fieldType: string
    fieldTypeName: string
    fieldComment: string
    tableName: string
    databaseName: string
  }>
  selectedTableInfo?: {
    tableId: string
    tableName: string
    databaseName: string
    datasourceId: string
    nodeType: number
  } | null
  // 所有可用的数据库列表
  availableDatabases?: Array<{
    databaseName: string
    datasourceId: string
  }>
  // 所有可用的表列表
  availableTables?: Array<{
    tableName: string
    databaseName: string
    tableId: string
    comment?: string
  }>
  dynamicParams?: Array<{
    name: string
    category: number
    default: string
    required: boolean
  }>
  needDynamicParam?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  readonly: false,
  height: '400px',
  enableAutoCompletion: true,
  tableFields: () => [],
  selectedTableInfo: null,
  availableDatabases: () => [],
  availableTables: () => [],
  dynamicParams: () => [],
  needDynamicParam: false,
})

// Events 定义
const emit = defineEmits<{
  init: [editor: any]
  change: [value: string]
}>()

// 双向绑定
const modelValue = defineModel<string>({ default: '' })

// 本地值用于处理双向绑定
const localValue = ref('')

// 编辑器引用
const aceEditorRef = ref()
const editorInstance = ref<any>(null)

// 编辑器包装器样式
const editorWrapperStyle = computed(() => {
  const height = typeof props.height === 'number' ? `${props.height}px` : props.height
  return {
    height: `calc(${height} - 38px)`,
    width: '100%',
  }
})

// Ace 编辑器选项
const aceOptions = computed(() => ({
  fontSize: 14,
  fontFamily: 'Monaco, Menlo, "Ubuntu Mono", Consolas, "Courier New", monospace',
  showPrintMargin: false,
  highlightActiveLine: true,
  showGutter: true,
  tabSize: 2,
  wrap: false,
  useSoftTabs: true,
  enableBasicAutocompletion: props.enableAutoCompletion,
  enableLiveAutocompletion: props.enableAutoCompletion,
  enableSnippets: true,
  showLineNumbers: true,
  displayIndentGuides: true,
  showFoldWidgets: true,
  highlightSelectedWord: true,
  behavioursEnabled: true,
  wrapBehavioursEnabled: true,
}))

// 处理编辑器初始化
const onInit = (editor: any) => {
  console.log('编辑器初始化完成')
  editorInstance.value = editor

  if (editor) {
    // 基本配置
    editor.setReadOnly(props.readonly)

    // 设置字体
    editor.setOptions({
      fontFamily: 'Monaco, Menlo, "Ubuntu Mono", Consolas, "Courier New", monospace',
      fontSize: 14,
    })

    // 确保编辑器获得焦点（如果不是只读）
    if (!props.readonly) {
      nextTick(() => {
        editor.focus()
        editor.renderer.scrollCursorIntoView()
      })
    }

    // 设置智能提示 - 延迟执行确保 ace 完全初始化
    if (props.enableAutoCompletion) {
      // 延迟设置智能提示，确保 ace 完全加载
      setTimeout(() => {
        setupAutoCompletion(editor)
      }, 200)
    }

    // 发出初始化事件
    emit('init', editor)
  }
}

// 处理值更新
const onUpdate = (value: string) => {
  emit('change', value)
}

// 设置智能提示
const setupAutoCompletion = (editor: any) => {
  try {
    const langTools = ace.require('ace/ext/language_tools')

    if (!langTools) {
      console.warn('Language tools not available')
      return
    }

    // SQL关键字
    const sqlKeywords = [
      'SELECT',
      'FROM',
      'WHERE',
      'INSERT',
      'UPDATE',
      'DELETE',
      'CREATE',
      'DROP',
      'ALTER',
      'INDEX',
      'TABLE',
      'DATABASE',
      'AND',
      'OR',
      'NOT',
      'NULL',
      'ORDER BY',
      'GROUP BY',
      'HAVING',
      'LIMIT',
      'DISTINCT',
      'COUNT',
      'SUM',
      'AVG',
      'MAX',
      'MIN',
      'JOIN',
      'LEFT JOIN',
      'RIGHT JOIN',
      'INNER JOIN',
    ]

    // 关键字补全器
    const keywordCompleter = {
      getCompletions: function (
        editor: any,
        session: any,
        pos: any,
        prefix: string,
        callback: any,
      ) {
        const completions = sqlKeywords.map((keyword) => ({
          caption: keyword,
          value: keyword,
          meta: 'SQL关键字',
          type: 'keyword',
          score: 500,
        }))
        callback(null, completions)
      },
    }

    // 字段补全器
    const fieldCompleter = {
      getCompletions: function (
        editor: any,
        session: any,
        pos: any,
        prefix: string,
        callback: any,
      ) {
        const completions = props.tableFields.map((field) => ({
          caption: `${field.fieldName} (${field.fieldTypeName})`,
          value: field.fieldName,
          meta: field.fieldComment || '字段',
          type: 'field',
          score: 1000,
        }))
        callback(null, completions)
      },
    }

    // 数据库名补全器
    const databaseCompleter = {
      getCompletions: function (
        editor: any,
        session: any,
        pos: any,
        prefix: string,
        callback: any,
      ) {
        const completions = props.availableDatabases.map((db) => ({
          caption: `${db.databaseName}`,
          value: db.databaseName,
          meta: '数据库',
          type: 'database',
          score: 800,
        }))
        callback(null, completions)
      },
    }

    // 表名补全器
    const tableCompleter = {
      getCompletions: function (
        editor: any,
        session: any,
        pos: any,
        prefix: string,
        callback: any,
      ) {
        const completions = props.availableTables.map((table) => ({
          caption: `${table.tableName}${table.comment ? ' (' + table.comment + ')' : ''}`,
          value: table.tableName,
          meta: `表 - ${table.databaseName}`,
          type: 'table',
          score: 900,
        }))

        // 如果有选中的数据库，添加完整的数据库.表名格式
        if (props.selectedTableInfo?.databaseName) {
          const dbTableCompletions = props.availableTables
            .filter((table) => table.databaseName === props.selectedTableInfo?.databaseName)
            .map((table) => ({
              caption: `${table.databaseName}.${table.tableName}${table.comment ? ' (' + table.comment + ')' : ''}`,
              value: `${table.databaseName}.${table.tableName}`,
              meta: `完整表名`,
              type: 'full_table',
              score: 950,
            }))
          completions.push(...dbTableCompletions)
        }

        callback(null, completions)
      },
    }

    // 动态参数补全器
    const dynamicParamCompleter = {
      triggerCharacters: ['#'],
      getCompletions: function (
        editor: any,
        session: any,
        pos: any,
        prefix: string,
        callback: any,
      ) {
        // 只有当启用动态参数且用户输入 # 时才提供补全
        if (!props.needDynamicParam || !props.dynamicParams?.length) {
          callback(null, [])
          return
        }

        // 获取光标前的文本来检查是否输入了 #
        const currentLine = session.getLine(pos.row)
        const textBeforeCursor = currentLine.substring(0, pos.column)

        // 检查是否以 # 开头的参数输入
        const hashMatch = textBeforeCursor.match(/#(\w*)$/)
        if (!hashMatch) {
          callback(null, [])
          return
        }

        const completions = props.dynamicParams.map((param) => ({
          caption: param.name,
          value: `#{${param.name}}`, // 使用 #{参数名} 的格式
          meta: `动态参数 (${getCategoryName(param.category)})${param.required ? ' *必填' : ''}`,
          type: 'dynamic_param',
          score: 1200, // 高优先级
          completer: {
            insertMatch: function (editor: any, data: any) {
              // 自定义插入逻辑：替换 # 开始的部分
              const pos = editor.getCursorPosition()
              const currentLine = editor.session.getLine(pos.row)
              const textBeforeCursor = currentLine.substring(0, pos.column)
              const hashIndex = textBeforeCursor.lastIndexOf('#')

              if (hashIndex !== -1) {
                const range = {
                  start: { row: pos.row, column: hashIndex },
                  end: { row: pos.row, column: pos.column },
                }
                editor.session.remove(range)
              }

              // 插入完整的动态参数
              editor.insert(data.value)
            },
          },
        }))

        callback(null, completions)
      },
    }

    // 获取参数类型名称的辅助函数
    const getCategoryName = (category: number): string => {
      const categoryMap: Record<number, string> = {
        [ParamCategory.STRING]: '字符串',
        [ParamCategory.NUMBER]: '数字',
        [ParamCategory.ARRAY]: '数组',
      }
      return categoryMap[category] || '未知类型'
    }

    // 更新补全器列表 - 添加动态参数补全器
    langTools.setCompleters([
      dynamicParamCompleter,
      fieldCompleter,
      databaseCompleter,
      tableCompleter,
      keywordCompleter,
    ])

    // 确保编辑器启用了智能提示
    editor.setOptions({
      enableBasicAutocompletion: true,
      enableLiveAutocompletion: true,
      enableSnippets: true,
    })
    console.log('%c智能提示设置完成:', 'background: #47dd6f; color: #fff;', {
      关键字数量: sqlKeywords.length,
      字段数量: props.tableFields.length,
      数据库数量: props.availableDatabases.length,
      表数量: props.availableTables.length,
      动态参数数量: props.dynamicParams?.length || 0,
      启用动态参数: props.needDynamicParam,
      当前选中: props.selectedTableInfo,
    })
  } catch (error) {
    console.error('设置智能提示失败:', error)
  }
}

// 刷新智能提示
const refreshCompletions = () => {
  if (editorInstance.value && props.enableAutoCompletion) {
    setupAutoCompletion(editorInstance.value)
  }
}

// 手动触发智能提示
const triggerAutoComplete = () => {
  if (editorInstance.value) {
    try {
      editorInstance.value.focus()
      editorInstance.value.execCommand('startAutocomplete')
      console.log('手动触发智能提示')
    } catch (error) {
      console.error('触发智能提示失败:', error)
    }
  }
}

// 强制刷新编辑器
const forceRefreshEditor = () => {
  if (editorInstance.value) {
    try {
      const cursorPosition = editorInstance.value.getCursorPosition()

      // 重新调整大小和刷新
      editorInstance.value.resize(true)
      editorInstance.value.renderer.updateFull(true)

      // 恢复光标位置
      editorInstance.value.moveCursorToPosition(cursorPosition)
      editorInstance.value.focus()

      console.log('编辑器已强制刷新')
    } catch (error) {
      console.error('刷新编辑器失败:', error)
    }
  }
}

// 插入文本
const insertText = (text: string) => {
  if (!editorInstance.value) {
    console.warn('编辑器未初始化，无法插入文本')
    return
  }

  try {
    const currentValue = localValue.value || ''

    if (!currentValue.trim()) {
      localValue.value = text
    } else {
      const needSpace = !currentValue.endsWith(' ') && !currentValue.endsWith('\n')
      localValue.value = currentValue + (needSpace ? ' ' : '') + text
    }

    // 移动光标到末尾
    nextTick(() => {
      if (editorInstance.value) {
        const session = editorInstance.value.getSession()
        const lastRow = session.getLength() - 1
        const lastColumn = session.getLine(lastRow).length
        editorInstance.value.moveCursorTo(lastRow, lastColumn)
        editorInstance.value.focus()
      }
    })

    console.log('已插入文本:', text)
  } catch (error) {
    console.error('插入文本失败:', error)
  }
}

// 获取编辑器实例
const getEditor = () => {
  return editorInstance.value
}
// 监听动态参数变化，重新设置补全器
watch(
  () => [props.dynamicParams, props.needDynamicParam],
  () => {
    refreshCompletions()
  },
  { deep: true },
)
// 监听 modelValue 变化
watch(
  () => modelValue.value,
  (newVal) => {
    if (newVal !== localValue.value) {
      localValue.value = newVal
    }
  },
  { immediate: true },
)

// 监听 localValue 变化
watch(
  () => localValue.value,
  (newVal) => {
    if (newVal !== modelValue.value) {
      modelValue.value = newVal
    }
  },
)

// 监听表字段变化
watch(
  () => props.tableFields,
  () => {
    refreshCompletions()
  },
  { deep: true },
)

// 暴露方法给父组件
defineExpose({
  insertText,
  forceRefreshEditor,
  getEditor,
  refreshCompletions,
})
</script>

<style scoped lang="less">
.sql-ace-editor-container {
  border-radius: 6px;
  overflow: hidden;
  border: 1px solid #d9d9d9;
  background: #fff;
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;

  .editor-wrapper {
    flex: 1;
    position: relative;
    overflow: hidden;
  }

  // 编辑器样式覆盖
  :deep(.ace_editor) {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', 'Courier New', monospace !important;
    font-size: 14px !important;
    line-height: 1.5 !important;
  }

  :deep(.ace_gutter) {
    background: #f8f9fa !important;
    border-right: 1px solid #e9ecef !important;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', 'Courier New', monospace !important;
  }

  :deep(.ace_cursor) {
    color: #000000 !important;
    border-left: 2px solid #000000 !important;
  }

  :deep(.ace_content) {
    cursor: text !important;
  }

  :deep(.ace_text-layer) {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', 'Courier New', monospace !important;
    font-size: 14px !important;
  }

  // GitHub主题语法高亮
  :deep(.ace-github) {
    .ace_keyword {
      color: #d73a49 !important;
      font-weight: bold !important;
    }

    .ace_string {
      color: #032f62 !important;
    }

    .ace_constant.ace_numeric {
      color: #005cc5 !important;
    }

    .ace_comment {
      color: #6a737d !important;
      font-style: italic !important;
    }

    .ace_support.ace_function {
      color: #6f42c1 !important;
    }

    .ace_variable {
      color: #e36209 !important;
    }

    .ace_operator {
      color: #d73a49 !important;
    }

    .ace_selection {
      background: #c8e6f5 !important;
    }

    .ace_selected-word {
      background: #ffeaa7 !important;
      border: 1px solid #fdcb6e !important;
    }

    .ace_marker-layer .ace_active-line {
      background: #f6f8fa !important;
    }
  }

  // 智能提示样式
  :deep(.ace_autocomplete) {
    border: 1px solid #d9d9d9 !important;
    border-radius: 4px !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
    background: #fff !important;
    width: 400px !important;
    min-width: 300px !important;
    max-width: 600px !important;
  }

  :deep(.ace_autocomplete .ace_line) {
    padding: 8px 12px !important;
    line-height: 1.4 !important;
    border-bottom: 1px solid #f0f0f0 !important;
  }

  :deep(.ace_autocomplete .ace_line.ace_selected) {
    background: #e6f7ff !important;
  }

  :deep(.ace_autocomplete .ace_completion-meta) {
    color: #666 !important;
    font-size: 12px !important;
  }
}

.sql-editor-toolbar {
  height: 38px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 12px;
  background: #fafafa;
  border-bottom: 1px solid #e9ecef;
  flex-shrink: 0;

  .toolbar-left {
    display: flex;
    align-items: center;

    .theme-label {
      font-size: 12px;
      color: #666;
      font-weight: 500;
    }
  }

  .toolbar-right {
    display: flex;
    align-items: center;
  }

  .refresh-btn {
    font-size: 12px;
    color: #666;

    &:hover {
      color: #1890ff;
      background: #f0f8ff;
    }
  }
}
// 动态参数智能提示特殊样式
:deep(.ace_autocomplete .ace_line) {
  // 动态参数项的特殊样式
  &[data-type='dynamic_param'] {
    background: linear-gradient(90deg, #f0f9ff 0%, #e0f2fe 100%) !important;
    border-left: 3px solid #0ea5e9 !important;

    &.ace_selected {
      background: linear-gradient(90deg, #dbeafe 0%, #bfdbfe 100%) !important;
    }

    .ace_completion-meta {
      color: #0ea5e9 !important;
      font-weight: 500 !important;
    }
  }
}
// 动态参数提示图标
:deep(.ace_autocomplete .ace_line[data-type='dynamic_param']) {
  &:before {
    content: '🔧';
    margin-right: 8px;
    font-size: 14px;
  }
}
</style>
