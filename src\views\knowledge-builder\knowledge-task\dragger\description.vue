<template>
  <div class="description">
    <a-collapse v-model:activeKey="activeKey">
      <a-collapse-panel key="1" header="知识描述">
        <a-descriptions :column="1">
          <a-descriptions-item label="名称" style="color: #000a1aad"
            >{{ currentEchartsData?.nameZh }}({{ currentEchartsData?.name }})</a-descriptions-item
          >
          <a-descriptions-item label="描述">-</a-descriptions-item>
        </a-descriptions>
      </a-collapse-panel>
    </a-collapse>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue'

const activeKey = ref(['1'])
defineProps<{
  currentEchartsData: any
}>()

watch(activeKey, (val) => {
  console.log(val)
})
</script>
<style scoped lang="less">
.description {
  padding-left: 24px;
}
</style>
