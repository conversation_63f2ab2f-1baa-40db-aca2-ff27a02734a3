<template>
  <a-modal
    v-model:open="modalVisible"
    :title="title"
    width="60%"
    :maskClosable="false"
    destroyOnClose
  >
    <a-form layout="vertical" :model="formItem" :rules="formRules" ref="ruleForm">
      <a-form-item label="模板名" name="name">
        <a-input v-model:value="formItem.name" placeholder="请输入模板名" />
      </a-form-item>
      <a-form-item label="模板描述" name="desc">
        <a-textarea v-model:value="formItem.desc" placeholder="请输入模板描述" :rows="4" />
      </a-form-item>
      <a-form-item label="模板内容" name="content">
        <div class="codeTemplateEditor" id="codeTemplateEditor" />
      </a-form-item>
    </a-form>
    <template #footer>
      <a-upload
        action=""
        name="file"
        accept=".scala"
        :showUploadList="false"
        @change="handleFileChange"
      >
        <a-button icon="file" style="margin-right: 8px">导入代码模板</a-button>
      </a-upload>
      <a-button key="back" @click="modalVisible = false">取消</a-button>
      <a-button key="submit" type="primary" :loading="submitLoading" @click="formSubmit"
        >提交</a-button
      >
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick, onUnmounted } from 'vue'
import { useUserStore } from '@/stores/user'
import { message } from 'ant-design-vue'
import { addCodeTemplat, editCodeTemplat } from '@/api/setting/code-template'
import * as monaco from 'monaco-editor'
import { toRaw } from 'vue'

// Scala 关键字定义
const scalaLanguageObj = {
  keywords: [
    'val',
    'var',
    'def',
    'class',
    'object',
    'trait',
    'extends',
    'with',
    'import',
    'package',
    'if',
    'else',
    'for',
    'while',
    'do',
    'try',
    'catch',
    'finally',
    'throw',
    'return',
    'new',
    'this',
    'super',
    'null',
    'true',
    'false',
    'case',
    'match',
    '=>',
    'type',
    'sealed',
    'abstract',
    'final',
    'private',
    'protected',
    'override',
    'implicit',
    'lazy',
    'yield',
    'forSome',
    'with',
    'do',
    'while',
    'until',
  ],
}

// 定义props和emits
interface Props {
  visible?: boolean
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'success'): void
}

withDefaults(defineProps<Props>(), {
  visible: false,
})

const emit = defineEmits<Emits>()

// 响应式数据
const modalVisible = ref(false)
const codeTemplateEditor = ref<any>(null)
const userStore = useUserStore()
const ownerId = computed(() => userStore.userInfo.loginAccount || '')

const modalType = ref('add')
const submitLoading = ref(false)
const ruleForm = ref<any>(null)

const formItem = ref({
  name: '',
  desc: '',
  content: '',
})

const formRules = {
  name: [{ required: true, message: '请输入模板名称', trigger: 'blur' }],
  content: [{ required: true, message: '请输入模板内容', trigger: 'blur' }],
}

// 计算属性
const title = computed(() => {
  return modalType.value === 'add' ? '新增模板' : modalType.value === 'edit' ? '修改模板' : ''
})

// 监听器
watch(modalVisible, (val: boolean) => {
  if (!val) {
    if (codeTemplateEditor.value) {
      toRaw(codeTemplateEditor.value).dispose()
      codeTemplateEditor.value = null
    }
    formItem.value = {
      name: '',
      desc: '',
      content: '',
    }
  }
})

// 方法
const showModal = (type: string, record?: { [x: string]: any }) => {
  modalType.value = type
  if (type === 'edit' && record) {
    Object.assign(formItem.value, JSON.parse(JSON.stringify(record)))
  }
  modalVisible.value = true
  console.log('🚀 ~ showModal ~ modalVisible.value:', modalVisible.value)
  nextTick(() => {
    initEditor(formItem.value.content)
  })
}

const initEditor = async (editorCode: string = '') => {
  try {
    // 如果编辑器已存在，先销毁
    if (codeTemplateEditor.value) {
      toRaw(codeTemplateEditor.value).dispose()
    }

    // 等待 DOM 元素准备好
    await nextTick()
    const editorElement = document.getElementById('codeTemplateEditor')
    if (!editorElement) {
      console.error('Editor element not found')
      return
    }

    // 创建 Monaco 编辑器
    codeTemplateEditor.value = monaco.editor.create(editorElement, {
      contextmenu: true,
      value: editorCode, // 编辑器初始显示文字
      language: 'scala', // 语言
      theme: 'vs-dark', // 主题
      automaticLayout: true, // 自动布局
      scrollBeyondLastLine: false, // 滚动超越最后一行
      fontSize: 14,
      tabSize: 2,
      minimap: {
        enabled: true,
      },
      wordWrap: 'on',
    })

    // 注册 Scala 语言补全提供者
    monaco.languages.registerCompletionItemProvider('scala', {
      provideCompletionItems() {
        const suggestions = scalaLanguageObj.keywords.map((item: string) => ({
          label: item,
          kind: monaco.languages.CompletionItemKind.Keyword,
          insertText: item,
        }))
        return {
          suggestions: suggestions,
        }
      },
    })

    // 监听编辑器内容变化
    codeTemplateEditor.value.onDidChangeModelContent(() => {
      formItem.value.content = toRaw(codeTemplateEditor.value).getValue()
    })
  } catch (error) {
    console.error('Failed to initialize editor:', error)
  }
}

const formSubmit = () => {
  ruleForm.value
    ?.validate()
    .then(() => {
      submitLoading.value = true
      const params = JSON.parse(JSON.stringify(formItem.value))
      params.ownerId = ownerId.value

      if (params.id && modalType.value === 'edit') {
        editCodeTemplat(params)
          .then(() => {
            closeModal('修改成功')
          })
          .finally(() => {
            submitLoading.value = false
          })
      } else {
        addCodeTemplat(params)
          .then(() => {
            closeModal('新增成功')
          })
          .finally(() => {
            submitLoading.value = false
          })
      }
    })
    .catch(() => {
      // 验证失败，不做任何操作
    })
}

const closeModal = (msg: string) => {
  modalVisible.value = false
  emit('success')
  message.success(msg)
}

const handleFileChange = (info: any) => {
  const file = info.file
  const reader = new FileReader()
  reader.readAsText(file.originFileObj, 'UTF-8')
  reader.onload = (evt: any) => {
    formItem.value.content = evt.target.result
    // 更新编辑器内容
    if (codeTemplateEditor.value) {
      toRaw(codeTemplateEditor.value).setValue(evt.target.result)
    }
  }
}

// 组件卸载时清理编辑器
onUnmounted(() => {
  if (codeTemplateEditor.value) {
    toRaw(codeTemplateEditor.value).dispose()
  }
})

// 暴露方法给父组件
defineExpose({
  showModal,
})
</script>

<style lang="less" scoped>
.codeTemplateEditor {
  width: 100%;
  height: 362px;
  padding: 16px 0;
  background-color: #fff;
  flex: 1;
  border: 1px solid rgba(0, 0, 0, 0.2);
}
</style>
