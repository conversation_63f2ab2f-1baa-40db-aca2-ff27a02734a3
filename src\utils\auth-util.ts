/**
 * 认证相关工具函数
 */

// 获取第三方登录地址的函数
export function getExternalLoginUrl(redirectPath2?: string): string {
  const redirectPath1 = '/#/auth-callback'
  const externalLoginUrl = window.ENV.EXTERNAL_LOGIN_URL
  // 如果 redirectPath2 为 iframePage，则会发生异常
  // const callbackUrl = `${window.location.origin}${redirectPath1}?redirect=${redirectPath2}`
  const callbackUrl = `${window.location.origin}${redirectPath1}`

  return `${externalLoginUrl}?redirect=${encodeURIComponent(callbackUrl)}`
}
