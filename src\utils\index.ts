export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number,
  immediate: boolean = false,
): (...args: Parameters<T>) => void {
  let timeout: ReturnType<typeof setTimeout> | null

  return function (this: any, ...args: Parameters<T>): void {
    const context = this

    const later = function () {
      timeout = null
      if (!immediate) func.apply(context, args)
    }

    const callNow = immediate && !timeout

    if (timeout) clearTimeout(timeout)
    timeout = setTimeout(later, wait)

    if (callNow) func.apply(context, args)
  }
}

export function isDevMock() {
  return import.meta.env.MODE === 'development' && import.meta.env.VITE_MOCK_VARIABLE
}

export function isDev() {
  return import.meta.env.MODE === 'development'
}

export async function sleep(d: number): Promise<void> {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve()
    }, d)
  })
}
