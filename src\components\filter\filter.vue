<script setup lang="ts">
import { ref, defineProps } from 'vue'
import FilterInputComponent from './filter-input-component.vue'
import type { FilterItem } from './type'
import { Modal } from 'ant-design-vue'
import { ConditionEnum, FormType } from './constant'
import Condition from './condition.vue'

const props = defineProps<{ filters?: FilterItem[]; onConfirm: (result: FilterItem[]) => void }>()

const open = defineModel<boolean>('open', { default: false })

const fakeData = [
  {
    field: '日期',
    originType: FormType.date,
    type: FormType.date,
    value: null,
    componentProps: {
      onConditionChange: (condition?: ConditionEnum, filter?: FilterItem) => {
        if (filter) filter.condition = condition
      },
    },
  },
  {
    field: '下拉列表有选项',
    originType: FormType.select,
    type: FormType.select,
    condition: ConditionEnum.equal,
    value: [],
    componentProps: {
      options: [
        {
          value: 'Active',
          label: 'Active',
        },
        {
          value: 'Inactive',
          label: 'Inactive',
        },
        {
          value: 'Pending',
          label: 'Pending',
        },
        {
          value: 'Suspended',
          label: 'Suspended',
        },
        {
          value: 'Deleted',
          label: 'Deleted',
        },
      ],
    },
  },
  {
    field: '下拉列表无选项',
    originType: FormType.select,
    type: FormType.select,
    condition: ConditionEnum.equal,
    value: [],
    componentProps: {},
  },
  {
    field: '输入',
    originType: FormType.input,
    type: FormType.input,
    condition: ConditionEnum.equal,
    value: '',
    componentProps: {},
  },
  {
    field: '范围',
    originType: FormType.range,
    type: FormType.range,
    condition: ConditionEnum.between,
    value: [],
    componentProps: {
      isNumber: true,
    },
  },
  {
    field: '多选框',
    originType: FormType.checkbox,
    type: FormType.checkbox,
    condition: ConditionEnum.equal,
    value: [],
    componentProps: {
      options: [
        {
          value: 'AAffilate',
          label: 'AAffilate',
        },
        {
          value: 'BFFilate',
          label: 'BFFilate',
        },
        {
          value: 'CFFilate',
          label: 'CFFilate',
        },
        {
          value: 'DFFilate',
          label: 'DFFilate',
        },
      ],
    },
  },
  {
    field: '单选',
    originType: FormType.radio,
    type: FormType.radio,
    value: '1',
    componentProps: {
      options: [
        {
          value: '1',
          label: '是',
        },
        {
          value: '2',
          label: '否',
        },
      ],
    },
  },
]
const dataSource = ref<FilterItem[]>([])
watch(
  () => props.filters,
  (filters) => {
    console.log('过滤器', filters)
    requestIdleCallback(() => {
      dataSource.value = filters ?? []
    })
  },
)
const handleOk = () => {
  if (import.meta.env.MODE === 'development') {
    // 打印 field 和 value
    dataSource.value.forEach((item) => {
      console.log(`${item.field}: ${item.value ?? '无'}, 条件${item.condition}`)
    })
  }
  open.value = false
  // 只返回有填数据的字段
  const result = dataSource.value.filter((item) => {
    if (Array.isArray(item.value)) return item.value.length > 0
    else return !!item.value
  })
  props.onConfirm(result)
}
const onConditionChange = (item: FilterItem, condition: ConditionEnum) => {
  item.condition = condition
  switch (item.originType) {
    case FormType.checkbox:
      if (condition === ConditionEnum.equal || condition === ConditionEnum.notEqual) {
        // 将 value 转为 checkbox 的 value 数据格式， 即 Array
        item.type = FormType.checkbox
        // console.log('checkbox 等于/不等于', item.value)
        if (item.value.length) {
          if (!item.componentProps.options) {
            item.componentProps.options = []
          }
          for (let v of item.value) {
            let tar = item.componentProps.options.find(
              (o: { label: string; value: any }) => o.value === v,
            )
            if (!tar) {
              item.componentProps.options.push({
                label: v,
                value: v,
              })
            }
          }
        }
      } else if (condition === ConditionEnum.isNull) {
        item.type = FormType.whiteSpace
      } else {
        item.type = FormType.select
      }
      break
    case FormType.range:
      if (condition === ConditionEnum.between) {
        item.type = FormType.range
        if (item.value.length > 2) {
          item.value = item.value.slice(0, 2)
        }
      } else if (condition === ConditionEnum.isNull) {
        item.type = FormType.whiteSpace
      } else {
        item.type = FormType.select
      }
      break

    default:
      if (condition === ConditionEnum.isNull) {
        item.type = FormType.whiteSpace
      } else {
        item.type = item.originType
      }
      break
  }
}
</script>
<template>
  <Modal
    v-model:open="open"
    width="60%"
    class="filter-modal"
    :body-style="{ overflowY: 'auto', maxHeight: '65vh', overflowX: 'hidden' }"
    @ok="handleOk"
  >
    <template #title>
      <h2 style="color: #4c5773; margin-top: 10px">筛选条件</h2>
      <a-divider type="horizontal"> </a-divider>
    </template>
    <div v-for="(it, key) in dataSource" :key="key">
      <a-row :gutter="10" type="flex" :wrap="true">
        <a-col :span="6">
          <a-flex align="center">
            <a-tooltip trigger="hover" placement="top" color="white">
              <template #title>
                <span text="black">{{ it.comment }}</span>
              </template>
              <a-tag color="blue">{{ it.columnType }}</a-tag>
            </a-tooltip>
            <!-- <span class="ellipsis">{{ it.field }}</span> -->
            <a-tooltip trigger="hover" placement="top" color="white">
              <template #title>
                <span text="black">{{ it.field }}</span>
              </template>
              <span class="ellipsis">{{ it.field }}</span>
            </a-tooltip>
            <Condition
              v-if="it.type !== FormType.date && it.type !== FormType.radio"
              :isNumber="it.componentProps.isNumber"
              :value="it.condition"
              :callback="(condition) => onConditionChange(it, condition)"
            ></Condition>
          </a-flex>
        </a-col>
        <a-col :span="18">
          <div class="emotion-ofnbir">
            <FilterInputComponent
              :formType="it.type ?? it.originType"
              v-model:value="it.value"
              :filterData="it"
              :componentProps="it.componentProps"
            />
          </div>
        </a-col>
      </a-row>
      <a-divider type="horizontal"></a-divider>
    </div>
  </Modal>
</template>
<style lang="less" scoped>
@tab-color: rgb(76, 87, 115);
@bg-color: #eef5fc;
@ac-color: rgb(80, 158, 227);
@mb-color-border: #eeecec;

.filter-modal {
  .emotion-ofnbir {
    border-bottom: 1px solid var(@mb-color-border);
    background-color: #fafbfc;
    margin-bottom: 20px;
  }
}

:deep(.ant-tag) {
  transform: none !important;
  transition: none !important;
}

:deep(.ant-tabs-tab) {
  width: 280px;
  color: @tab-color;
  font-weight: 400;
  &:hover {
    background-color: @bg-color;
    color: @tab-color;
  }
}

:deep(.ant-tabs-tab-active) {
  .ant-tabs-tab-btn {
    color: @ac-color !important;
  }
}
// }
</style>
