<template>
  <div class="container">
    <CardBox :title="'订阅者管理'" sub-title="用于新增、编辑，查询订阅者管理">
      <template v-slot:headerRight
        ><a-button type="primary" class="add-tag" @click="addSubscriberClick"
          >+ 添加订阅者</a-button
        ></template
      >
      <div class="table-wrap">
        <Table
          :getData="getPageDataSubscripServer"
          :columns="columns"
          :scroll="{ y: 550 }"
          :searchFormState="form"
          ref="tableRef"
        >
          <template #search>
            <a-form-item label="" name="服务名称" :rules="[{ message: '请输入服务名称' }]">
              <a-input v-model:value="form.serverName" placeholder="请输入岗位名称" />
            </a-form-item>
            <a-form-item label="" name="订阅类型" :rules="[{ message: '请输入订阅类型' }]">
              <a-select v-model:value="form.apiType" allow-clear placeholder="请选择订阅类型">
                <a-select-option value="HTTP">HTTP</a-select-option>
                <a-select-option value="EMAIL">EMAIL</a-select-option>
              </a-select>
            </a-form-item>
          </template>
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'operate'">
              <a-button type="link" @click="editSubscriberClick(record)">编辑</a-button>
              <a-popconfirm
                title="是否确认删除"
                ok-text="是"
                cancel-text="否"
                @confirm="delSubscriberClick(record)"
              >
                <a>删除</a>
              </a-popconfirm>
            </template>
          </template>
        </Table>
      </div>
    </CardBox>

    <EditDialog
      :visibleDialog="showSubscriberDialog"
      :dialogType="editType"
      :currentItem="editItem"
      @okHandle="saveSubscriber"
      @canceHandle="canceSubscriber"
    ></EditDialog>
  </div>
</template>

<script lang="ts" setup>
import { type ISubscriber } from '@/api/services/data-asset/subscriber-management'
import {
  getPageDataSubscripServer,
  delDataSubscripServer,
} from '@/api/assetmanager/datasubscriptionadmin/datasubscriptionadmin'
import { Table } from '@fs/fs-components'
import { message } from 'ant-design-vue'
import CardBox from '@/components/card-box/card-box.vue'
import EditDialog from './edit-dialog.vue'

const initSubscriberForm = () => {
  return {
    password: '',
    apiHeaders: '',
    recEmail: '',
    apiHost: '',
    apiPath: '',
    host: '',
    serverName: '',
    fromAddress: '',
    userName: '',
    apiType: null,
  }
}

const showSubscriberDialog = ref(false)
const tableRef = ref()
const editType = ref('add')
const editItem = ref<ISubscriber>(initSubscriberForm())
const form = ref<Record<string, any>>({
  serverName: null,
  apiType: null,
})

const columns = ref([
  {
    title: '服务名称',
    dataIndex: 'serverName',
    key: 'serverName',
    width: 100,
  },
  {
    title: '服务地址',
    dataIndex: 'apiHost',
    key: 'apiHost',
    width: 100,
  },
  {
    title: '服务路径',
    dataIndex: 'apiPath',
    key: 'apiPath',
    width: 100,
  },
  {
    title: 'API头参参数配置',
    dataIndex: 'apiHeaders',
    key: 'apiHeaders',
    width: 100,
  },
  {
    title: 'API类型',
    dataIndex: 'apiType',
    key: 'apiType',
    width: 100,
  },
  {
    title: '收件人邮箱',
    dataIndex: 'recEmail',
    key: 'recEmail',
    width: 100,
  },
  {
    title: '服务host',
    dataIndex: 'host',
    key: 'host',
    width: 100,
  },
  {
    title: '发送地址',
    dataIndex: 'fromAddress',
    key: 'fromAddress',
    width: 100,
  },
  {
    title: '发送邮箱',
    dataIndex: 'userName',
    key: 'userName',
    width: 100,
  },
  {
    title: '操作',
    dataIndex: 'operate',
    key: 'operate',
    width: 100,
  },
])

// 删除item
const delSubscriberClick = async (item: ISubscriber) => {
  const res = await delDataSubscripServer({ serverId: item.serverId as string })
  if (res.code === '000000') {
    message.success('删除成功')
    initList()
  } else {
    message.error(res.msg)
  }
}

// 获取订阅者列表
const initList = async () => {
  tableRef.value?.getTableData()
}

const editSubscriberClick = (item: ISubscriber) => {
  editItem.value = item
  editType.value = 'edit'
  showSubscriberDialog.value = true
}

// 添加订阅者
const addSubscriberClick = () => {
  editItem.value = initSubscriberForm()
  editType.value = 'add'
  showSubscriberDialog.value = true
}
// 保存订阅者
const saveSubscriber = () => {
  canceSubscriber()
  initList()
}
// 取消操作
const canceSubscriber = () => {
  showSubscriberDialog.value = false
  editItem.value = initSubscriberForm()
}
</script>
<style scoped lang="less">
.container {
  width: 100%;
  height: 100%;
  background: white;
}
</style>
