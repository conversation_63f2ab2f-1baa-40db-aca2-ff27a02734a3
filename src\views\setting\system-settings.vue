<template>
  <div class="set-config">
    <div class="nav-page-title">
      <div class="title-left">
        <p>系统设置</p>
        <div class="desc">配置平台基础参数与功能选项</div>
      </div>
    </div>
    <div class="set-config__card-list">
      <CardOption
        v-for="item in cardList"
        :key="item.title"
        :title="item.title"
        :desc="item.desc"
        :icon-color="item.iconColor"
        :icon="item.icon"
        @click="handleCardClick(item)"
        v-action:[item.actionName]
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import CardOption from './card-option.vue'
import params from '@/assets/icons/params.png'

interface CardItem {
  title: string
  desc: string
  iconColor?: string
  icon?: string
  key: string
  path: string
  actionName?: string
}

const router = useRouter()

const cardList = ref<CardItem[]>([
  {
    title: '系统参数设置',
    desc: '配置运行规则与全局变量',
    iconColor: '#5b8ff9',
    icon: params,
    key: 'ParameterSelect',
    path: '/ParameterSelect',
    actionName: 'ParameterSelect',
  },
])

function handleCardClick(item: CardItem) {
  router.push(item.path)
}
</script>

<style lang="less" scoped>
@import '@/assets/main.less';
@import './page.less';

.set-config {
  width: 100%;
  height: ~'calc(100vh - @{layout-header-height})';
  overflow-y: auto;
  // background: #fafbfc;
  box-sizing: border-box;
  &__title {
    font-size: 18px;
    font-weight: 600;
    color: #222;
    margin-bottom: 8px;
  }
  &__card-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 430px));
    gap: 16px 24px;
    width: 100%;
    margin: 24px auto;
  }
}
</style>
