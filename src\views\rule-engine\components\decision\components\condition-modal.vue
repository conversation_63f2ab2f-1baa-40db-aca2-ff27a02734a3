<template>
  <a-modal
    :title="condition.from.id === undefined ? '创建条件' : '编辑条件'"
    :mask="false"
    :width="700"
    v-model:open="condition.visible"
    :ok-text="condition.from.id === undefined ? '确认添加' : '确认更新'"
    @ok="addConditionOk()"
    @cancel="addConditionCancel"
  >
    <a-form
      ref="conditionForm"
      :model="condition.from"
      :label-col="{ span: 3 }"
      :wrapper-col="{ span: 19 }"
    >
      <a-form-item
        label="参数说明"
        name="name"
        style="margin-bottom: 10px"
        :rules="{
          required: true,
          message: '请输入参数说明',
          trigger: ['change', 'blur'],
        }"
      >
        <a-input v-model:value="condition.from.name"> </a-input>
      </a-form-item>
      <a-form-item label="配置" name="config" required style="margin-bottom: 10px">
        <br />
        <a-row>
          <a-col :span="3"> 参数名称 </a-col>
          <a-col :span="6">
            <a-form-item
              :name="['config', 'leftValue', 'type']"
              style="margin-bottom: 10px"
              :rules="{
                required: true,
                message: '请选择参数类型',
                trigger: ['change', 'blur'],
              }"
            >
              <a-select
                :value="valueType(condition.from.config.leftValue)"
                placeholder="请选择"
                @change="leftValueTypeChange"
              >
                <!-- <a-select-option value="PARAMETER">参数</a-select-option>
                <a-select-option value="VARIABLE">变量</a-select-option>
                <a-select-option v-if="viewSelectGR" value="GENERAL_RULE">普通规则</a-select-option> -->
                <a-select-option value="BOOLEAN">布尔</a-select-option>
                <!-- <a-select-option value="COLLECTION">集合</a-select-option> -->
                <a-select-option value="STRING">字符串</a-select-option>
                <a-select-option value="NUMBER">数值</a-select-option>
                <a-select-option value="DATE">日期</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="1" />
          <a-col :span="14">
            <a-form-item
              :name="['config', 'leftValue']"
              style="margin-bottom: 10px"
              :rules="{
                required: true,
                message: '请输入左值',
                trigger: ['change', 'blur'],
              }"
            >
              <!-- <a-select
                :disabled="condition.from.config.leftValue.type == null"
                v-if="condition.from.config.leftValue.valueType === 'BOOLEAN'"
                v-model:value="condition.from.config.leftValue.value"
                placeholder="请选择数据"
              >
                <a-select-option value="true">true</a-select-option>
                <a-select-option value="false">false</a-select-option>
              </a-select>
              <a-input-number
                :disabled="condition.from.config.leftValue.type == null"
                v-else-if="condition.from.config.leftValue.valueType === 'NUMBER'"
                v-model:value="condition.from.config.leftValue.value"
                style="width: 100%"
              />
              <a-date-picker
                  format="YYYY-MM-DD hh:mm:ss"
                valueFormat="YYYY-MM-DD hh:mm:ss"
                :disabled="condition.from.config.leftValue.type == null"
                v-else-if="condition.from.config.leftValue.valueType === 'DATE'"
                show-time
                v-model:value="condition.from.config.leftValue.value"
                @change="
                  (date: any, dateString: string) =>
                    datePickerChange(condition.from.config.leftValue, date, dateString)
                "
                style="width: 100%"
              /> -->
              <a-input
                :disabled="condition.from.config.leftValue.type == null"
                v-model:value="condition.from.config.leftValue.value"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row>
          <a-col :span="3"> 运算符 </a-col>
          <a-col :span="6">
            <a-form-item
              style="margin-bottom: 10px"
              :name="['config', 'symbol']"
              :rules="{
                required: true,
                message: '请选择运算符',
                trigger: ['change', 'blur'],
              }"
            >
              <a-select
                placeholder="请选择"
                :disabled="
                  condition.from.config.leftValue.value == null &&
                  condition.from.config.leftValue.type !== 2 &&
                  condition.from.config.rightValue.value == null
                "
                v-model:value="condition.from.config.symbol"
              >
                <a-select-option v-for="op in condition.operators" :value="op.name" :key="op.name">
                  {{ op.explanation }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="15" />
        </a-row>

        <a-row>
          <a-col :span="3"> 匹配值 </a-col>
          <!-- <a-col :span="6">
            <a-form-item
              name="config.rightValue.type"
              style="margin-bottom: 10px"
              :rules="{
                required: true,
                message: '请选择右值类型',
                trigger: ['change', 'blur'],
              }"
            >
              <a-select
                :disabled="
                  condition.from.config.leftValue.value == null &&
                  condition.from.config.leftValue.type !== 2 &&
                  condition.from.config.rightValue.value == null
                "
                :value="valueType(condition.from.config.rightValue)"
                placeholder="请选择"
                @change="rightValueTypeChange"
              >
                <a-select-option
                  v-if="condition.from.config.leftValue.valueType != null"
                  value="PARAMETER"
                  >参数
                </a-select-option>
                <a-select-option
                  v-if="condition.from.config.leftValue.valueType != null"
                  value="VARIABLE"
                  >变量
                </a-select-option>
                <a-select-option
                  v-if="condition.from.config.leftValue.valueType != null && viewSelectGR"
                  value="GENERAL_RULE"
                >
                  普通规则
                </a-select-option>
                <a-select-option v-if="isRightTypeSelectView('BOOLEAN')" value="BOOLEAN"
                  >布尔
                </a-select-option>
                <a-select-option v-if="isRightTypeSelectView('COLLECTION')" value="COLLECTION"
                  >集合
                </a-select-option>
                <a-select-option v-if="isRightTypeSelectView('STRING')" value="STRING"
                  >字符串
                </a-select-option>
                <a-select-option v-if="isRightTypeSelectView('NUMBER')" value="NUMBER"
                  >数值
                </a-select-option>
                <a-select-option v-if="isRightTypeSelectView('DATE')" value="DATE"
                  >日期
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col> -->
          <!-- <a-col :span="1" /> -->
          <a-col :span="14">
            <a-form-item
              :name="['config', 'rightValue', 'value']"
              style="margin-bottom: 10px"
              :rules="{
                required: true,
                message: '请输入匹配值',
                trigger: ['change', 'blur'],
              }"
            >
              <a-select
                :disabled="condition.from.config.leftValue.type == null"
                v-if="condition.from.config.leftValue.valueType === 'BOOLEAN'"
                v-model:value="condition.from.config.rightValue.value"
                placeholder="请选择数据 "
              >
                <a-select-option value="true">true</a-select-option>
                <a-select-option value="false">false</a-select-option>
              </a-select>
              <a-input-number
                v-else-if="condition.from.config.leftValue.valueType === 'NUMBER'"
                :disabled="condition.from.config.leftValue.type == null"
                v-model:value="condition.from.config.rightValue.value"
                style="width: 100%"
              />
              <a-date-picker
                v-else-if="condition.from.config.leftValue.valueType === 'DATE'"
                :disabled="condition.from.config.leftValue.type == null"
                show-time
                format="YYYY-MM-DD hh:mm:ss"
                valueFormat="YYYY-MM-DD hh:mm:ss"
                v-model:value="condition.from.config.rightValue.value"
                @change="
                  (date: any, dateString: string) =>
                    datePickerChange(condition.from.config.rightValue, date)
                "
                style="width: 100%"
              />
              <a-input
                :disabled="condition.from.config.leftValue.type == null"
                v-else
                v-model:value="condition.from.config.rightValue.value"
              />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form-item>

      <!-- <a-form-item label="说明" name="description" style="margin-bottom: 8px">
        <a-textarea v-model:value="condition.from.description" :rows="3" />
      </a-form-item> -->
    </a-form>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { setDefaultValue } from '@/utils/json'
import moment from 'moment'
import { valueType } from '@/utils/value-type'
import { getSymbolByValueType } from '@/utils/symbol'
import { createUuid } from '@/utils/tool'

defineProps({
  dataType: {
    type: [String, Number],
    required: true,
  },
  viewSelectGR: {
    type: Boolean,
    default: false,
  },
})

const conditionForm = ref<any>(null)

const conditionLeftSearchSelect = reactive<any>({
  data: [],
  value: undefined,
})

const condition = reactive<any>({
  visible: false,
  currentConditionGroup: null,
  confirmLoading: false,
  operators: [],
  from: {
    id: null,
    name: null,
    description: null,
    config: {
      leftValue: {
        type: undefined,
        valueType: undefined,
        value: undefined,
        valueName: undefined,
        variableValue: undefined,
      },
      symbol: undefined,
      rightValue: {
        type: undefined,
        valueType: undefined,
        value: undefined,
        valueName: undefined,
        variableValue: undefined,
      },
    },
  },
})

const datePickerChange = (v: { value: string }, date: moment.MomentInput) => {
  v.value = moment(date).format('YYYY-MM-DD HH:mm:ss')
}

const addConditionOk = () => {
  condition.confirmLoading = true
  conditionForm.value
    .validate()
    .then(() => {
      // 更新条件
      if (condition.from.id !== undefined) {
        condition.currentConditionGroup.conditionGroupCondition.forEach(
          (f: { condition: { id: any } }) => {
            if (f.condition.id === condition.from.id) {
              f.condition = condition.from
            }
          },
        )
        //     ElMessage.success('条件更新成功')
        //     condition.confirmLoading = false
        condition.visible = false
        //   }
        // })
        return
      }
      // 获取最后一条的orderNo
      let orderNo = 0
      let conditionGroupCondition = condition.currentConditionGroup.conditionGroupCondition

      if (conditionGroupCondition.length > 0) {
        orderNo = conditionGroupCondition[conditionGroupCondition.length - 1].orderNo + 1
      }

      // 插入一条记录
      // saveConditionAndBindGroup({
      //   // 传入条件组信息，条件信息 绑定关系
      //   conditionGroupId: condition.currentConditionGroup.id,
      //   orderNo: orderNo,
      //   addConditionRequest: condition.from,
      //   dataId: props.dataId,
      //   dataType: props.dataType,
      // }).then((res) => {
      //   if (res.data.data) {
      //     // 当前条件组内插入一条数据
      condition.from.id = createUuid()
      condition.currentConditionGroup.conditionGroupCondition.push({
        id: createUuid(),
        orderNo: orderNo,
        condition: JSON.parse(JSON.stringify(condition.from)),
      })
      condition.confirmLoading = false
      condition.visible = false

      //   }
      //   // 重置表单
      //   conditionForm.value.resetFields()
      // })
    })
    .catch(() => {
      condition.confirmLoading = false
      return false
    })
}

const addConditionCancel = () => {
  if (conditionForm.value) {
    conditionForm.value.resetFields()
  }
  condition.visible = false
}

const leftValueTypeChange = (valueType: string) => {
  condition.from.config.leftValue = {
    value: undefined,
    valueName: undefined,
    variableValue: undefined,
    valueType: undefined,
  }
  // 如果是变量或者元素
  if (valueType === 'PARAMETER') {
    condition.from.config.leftValue.type = 0
  } else if (valueType === 'VARIABLE') {
    condition.from.config.leftValue.type = 1
  } else if (valueType === 'GENERAL_RULE') {
    condition.from.config.leftValue.type = 10
  } else {
    condition.from.config.leftValue.type = 2
    condition.from.config.leftValue.valueType = valueType
    // 固定值场景清空右值，如果变量或者参数，等搜索到选中时再去判断清空
    // 左面发生改变，右边也改变  如果值类型相同，则不需要更改
    if (valueType !== condition.from.config.rightValue.valueType) {
      condition.from.config.rightValue = setDefaultValue(condition.from.config.rightValue)
      // 删除运算符
      condition.from.config.symbol = undefined
      condition.operators = []
    }
    // 根据左值更改运算符
    condition.operators = getSymbolByValueType(valueType)
  }
  // 清空远程搜索缓存
  conditionLeftSearchSelect.data = []
  conditionLeftSearchSelect.value = undefined

  // 手动触发表单验证，清除必填提示
  // nextTick(() => {
  //   conditionForm.value?.clearValidate()
  // })
}

const editCondition = (
  cg: any,
  cgc: { condition: { config: { leftValue: { valueType: any } } } },
) => {
  // 当前条件组
  condition.currentConditionGroup = cg
  // bug修复
  condition.from = JSON.parse(JSON.stringify(cgc.condition))
  // 加载运算符
  condition.operators = getSymbolByValueType(cgc.condition.config.leftValue.valueType)
  condition.visible = true
}

const addCondition = (cg: any) => {
  condition.from = {
    id: undefined,
    name: null,
    description: null,
    config: {
      leftValue: {
        type: undefined,
        valueType: undefined,
        value: undefined,
        valueName: undefined,
        variableValue: undefined,
      },
      symbol: undefined,
      rightValue: {
        type: undefined,
        valueType: undefined,
        value: undefined,
        valueName: undefined,
        variableValue: undefined,
      },
    },
  }
  condition.currentConditionGroup = cg
  condition.visible = true
}

// Expose methods to parent component
defineExpose({
  editCondition,
  addCondition,
})
</script>

<style scoped></style>
