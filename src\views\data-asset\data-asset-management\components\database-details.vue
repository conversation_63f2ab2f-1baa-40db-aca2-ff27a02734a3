<template>
  <div class="database-details">
    <div class="title">资产详情</div>
    <div class="form-container">
      <div class="form-item">
        <span class="label">数据库名称：</span>
        <span class="value">{{ databaseInfo.databaseName }}</span>
      </div>
      <div class="form-item">
        <span class="label">数据库类型：</span>
        <span class="value">{{ databaseInfo.databaseType }}</span>
      </div>
      <div class="form-item">
        <span class="label">数据库引擎类型：</span>
        <span class="value">{{ databaseInfo.engine }}</span>
      </div>
      <div class="form-item">
        <span class="label">表个数：</span>
        <span class="value">{{ databaseInfo.tableCount }}</span>
      </div>
      <div class="form-item">
        <span class="label">已发布：</span>
        <span class="value">{{ databaseInfo.releaseCount || 0 }}</span>
      </div>
      <div class="form-item">
        <span class="label">未发布：</span>
        <span class="value">{{ databaseInfo.tableCount - (databaseInfo.releaseCount || 0) }}</span>
      </div>
      <div class="form-item">
        <span class="label">字段个数：</span>
        <span class="value">{{ databaseInfo.columnCount }}</span>
      </div>
      <div class="form-item">
        <span class="label">描述：</span>
        <span class="value">
          {{ databaseInfo.description }}<EditOutlined @click.prevent="openEditCommentModal(record)"
        /></span>
      </div>
    </div>
  </div>

  <!-- Edit Comment Modal -->
  <a-modal
    v-model:visible="commentModalVisible"
    title="编辑注释"
    @ok="handleCommentSubmit"
    :confirmLoading="submitLoading"
    @cancel="commentModalVisible = false"
  >
    <a-form :model="commentForm" :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }">
      <a-form-item label="数据库名" name="databaseName">
        {{ databaseInfo.databaseName }}
      </a-form-item>
      <a-form-item label="自定义注释" name="description">
        <a-input v-model:value="commentForm.description" placeholder="请输入自定义注释" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script lang="ts" setup>
import { watch, ref } from 'vue'
import {
  getDatabaseDetail,
  updateDatabaseDesc,
} from '@/api/assetmanager/dataassertmanage/dataassertmanage'
import { EditOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'

const props = defineProps<{
  databaseId?: string
  databaseData?: any
}>()

const emit = defineEmits(['refresh', 'update:tableCount'])

const databaseInfo = ref({
  databaseName: '',
  databaseType: '',
  engine: '',
  description: '',
  tableCount: 0,
  columnCount: 0,
  releaseCount: 0,
})

// 编辑注释相关变量
const commentModalVisible = ref(false)
const submitLoading = ref(false)
const commentForm = ref({
  customComment: '',
})

// 提交编辑注释
const handleCommentSubmit = async () => {
  submitLoading.value = true
  try {
    // TODO: 这里应该添加保存注释的API调用
    await updateDatabaseDesc({
      databaseId: databaseInfo.value.databaseId,
      description: commentForm.value.description,
    })
    message.success('更新注释成功')
    commentModalVisible.value = false
    fetchTableData() // 刷新表格数据
  } catch (error) {
    console.error('更新注释失败:', error)
  } finally {
    submitLoading.value = false
  }
}

// 打开编辑注释弹窗
const openEditCommentModal = (record: any) => {
  commentForm.value.description = databaseInfo.value?.description || ''
  commentModalVisible.value = true
}

const fetchTableData = async () => {
  if (!props?.databaseId) return
  try {
    const response = await getDatabaseDetail({ databaseId: props.databaseId }, {})
    if (response?.data) {
      databaseInfo.value = response.data
      emit('update:tableCount', {
        ...props.databaseData,
        tableCount: response.data?.tableCount,
        description: databaseInfo.value?.description,
      })
    }
  } catch (error) {
    console.error('获取数据失败:', error)
  }
}

// 暴露给父组件调用的刷新方法
defineExpose({
  refresh: fetchTableData,
})

watch(
  () => props.databaseData,
  (newVal) => {
    if (newVal) {
      fetchTableData()
    }
  },
  { immediate: true, deep: true },
)
</script>

<style lang="less" scoped>
.database-details {
  .title {
    margin-right: auto;
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 24px;
    padding-bottom: 16px;
    display: flex;
    justify-content: space-between;
    padding-right: 12px;
  }

  .form-container {
    padding: 16px 0px 16px 16px;
    background: #fff;
    border-radius: 4px;

    .form-item {
      display: flex;
      margin-bottom: 16px;
      line-height: 32px;

      &:last-child {
        margin-bottom: 0;
      }

      .label {
        width: 120px;
        color: #666;
      }

      .value {
        flex: 1;
        color: #333;
      }
    }
  }
}
:deep(.fs-table .search-btn) {
  display: none;
}
</style>
