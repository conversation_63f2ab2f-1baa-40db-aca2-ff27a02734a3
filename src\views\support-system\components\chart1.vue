<script lang="ts" setup>
import * as echarts from 'echarts'

const chartRef = ref(null)

const option = {
  title: {
    text: '各医院综合有效性得分对比图',
    left: 'center',
    bottom: 0,
    textStyle: {
      fontSize: 14,
      fontWeight: 'normal',
    },
  },
  tooltip: {
    trigger: 'axis',
    formatter: '{b}<br/>得分: {c}',
  },
  xAxis: {
    type: 'category',
    boundaryGap: false,
    data: [
      '医院1',
      '医院2',
      '医院3',
      '医院4',
      '医院5',
      '医院6',
      '医院7',
      '医院8',
      '医院9',
      '医院10',
    ],
    axisLine: {
      lineStyle: {
        color: '#ccc',
      },
    },
    axisLabel: {
      color: '#666',
    },
  },
  yAxis: {
    type: 'value',
    min: 0.6,
    max: 1.0,
    splitLine: {
      lineStyle: {
        type: 'dashed',
        color: '#ddd',
      },
    },
    axisLine: {
      show: false,
    },
    axisLabel: {
      color: '#666',
    },
  },
  series: [
    {
      name: '综合有效性得分',
      type: 'line',
      data: [0.7, 0.9, 0.85, 0.88, 0.92, 0.87, 0.93, 0.89, 0.91, 0.95],
      lineStyle: {
        color: '#5470C6',
      },
      symbol: 'circle',
      symbolSize: 6,
      itemStyle: {
        color: '#5470C6',
      },
      areaStyle: {
        color: 'rgba(84, 112, 198, 0.1)',
      },
    },
    {
      name: '标准线',
      type: 'line',
      data: Array(10).fill(0.9), // 标准线固定值
      lineStyle: {
        type: 'dashed',
        color: '#F56C6C',
      },
      symbol: 'none',
    },
  ],
}

onMounted(() => {
  const chart = echarts.init(chartRef.value, null, { renderer: 'canvas', height: 320 })
  chart.setOption(option)
})
</script>

<template>
  <div class="download-chart" ref="chartRef"></div>
</template>

<style lang="scss" scoped></style>
