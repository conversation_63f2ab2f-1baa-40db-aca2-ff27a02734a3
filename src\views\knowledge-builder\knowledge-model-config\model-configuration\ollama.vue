<template>
  <div style="padding-right: 10px">
    <a-table :columns="columns" :data-source="props.data">
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <a @click="handleEdit(record)">编辑</a>
          <a-popconfirm
            title="是否确认删除"
            ok-text="是"
            cancel-text="否"
            @confirm="handleDelete(record)"
          >
            <a style="margin-left: 10px">删除</a>
          </a-popconfirm>
        </template>
      </template>
    </a-table>
  </div>
  <createOllama ref="createOllamaRef" @updateVllmOk="handleUpdateOllamaSuccess" />
</template>
<script lang="ts" setup>
import { ref } from 'vue'
import type { KnowledgeBaseConfig } from '@/api/type'
import createOllama from '@/views/knowledge-builder/knowledge-model-config/model-configuration/create-ollama.vue'

const props = defineProps<{ data: KnowledgeBaseConfig }>()
const createOllamaRef = ref<InstanceType<typeof createOllama>>()
const emits = defineEmits(['deleteOllamaOk', 'updateOllamaSuccess'])

const handleUpdateOllamaSuccess = (record: any) => {
  emits('updateOllamaSuccess', record)
}
const handleDelete = (record: any) => {
  emits('deleteOllamaOk', record)
}
const handleEdit = (record: any) => {
  createOllamaRef.value?.show(record)
}

const columns = [
  {
    title: '模型名称',
    key: 'model',
    dataIndex: 'model',
    width: 200,
    align: 'center',
  },
  {
    title: '备注',
    key: 'desc',
    dataIndex: 'desc',
    width: 200,
    align: 'center',
  },
  {
    title: '创建人',
    key: 'creator',
    dataIndex: 'creator',
    width: 200,
    align: 'center',
  },
  {
    title: '修改时间',
    key: 'createTime',
    dataIndex: 'createTime',
    width: 200,
    align: 'center',
  },
  {
    title: '操作',
    key: 'action',
    dataIndex: 'action',
    width: 200,
    align: 'center',
  },
]
</script>
<style lang="less" scoped>
:deep(.filter-box) {
  display: none;
}
</style>
