<template>
  <a-modal
    v-model:open="addOpen"
    @ok="modalHandleOk"
    :title="modalType === 'edit' ? '编辑' : '新增' + '用户组'"
    @cancel="cancel"
    width="800px"
    :confirm-loading="confirmLoading"
  >
    <a-form
      ref="formRef"
      :model="form"
      :rules="rules"
      :label-col="labelCol"
      :wrapper-col="wrapperCol"
    >
      <a-form-item label="用户组编号" name="userGroupId" v-if="modalType === 'edit'">
        <a-input v-model:value="form.userGroupId" :disabled="true" />
      </a-form-item>
      <a-form-item label="用户组组名" name="userGroupName">
        <a-input v-model:value="form.userGroupName" />
      </a-form-item>
      <a-form-item label="是否启用" name="isUse">
        <a-radio-group v-model:value="form.isUse">
          <a-radio :value="1">是</a-radio>
          <a-radio :value="0">否</a-radio>
        </a-radio-group>
      </a-form-item>
      <a-form-item label="备注" name="remark">
        <a-textarea v-model:value="form.remark" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>
<script lang="ts" setup>
import { defineEmits, defineProps, defineExpose, ref } from 'vue'
import { message } from 'ant-design-vue'
import type { Rule } from 'ant-design-vue/es/form'
import { addUserGroup, updateUserGroup } from '@/api/services/permission'
interface FormState {
  userGroupId: string
  userGroupName: string
  isUse: string
  remark: string
}
const emits = defineEmits(['modalHandleOk', 'modalCancel', 'confirmLoadingHandle'])
const formRef = ref<HTMLFormElement | null>(null)
const labelCol = { span: 5 }
const wrapperCol = { span: 13 }
const modalType = ref<string>('')
const confirmLoading = ref<boolean>(false)
const addOpen = ref<boolean>(false)
const form = ref<FormState>({
  userGroupId: '',
  userGroupName: '',
  isUse: '',
  remark: '',
})

defineProps({
  disabled: Boolean,
})

const rules: Record<string, Rule[]> = {
  userGroupName: [{ required: true, message: '请输入用户组组名', trigger: 'blur' }],
  isUse: [{ required: true, message: '请选择是否启用', trigger: 'blur' }],
}

function modalHandleOk() {
  formRef.value &&
    formRef.value
      .validate()
      .then(() => {
        confirmLoading.value = true
        if (modalType.value === 'edit') {
          updateUserGroup({ ...form.value })
            .then(() => {
              emits('modalHandleOk', form.value)
              message.success('更新成功!')
              addOpen.value = false
            })
            .finally(() => {
              confirmLoading.value = false
            })
        } else {
          const { isUse, userGroupName, remark } = form.value
          const params = {
            isUse,
            userGroupName,
            remark,
          }
          addUserGroup(params)
            .then(() => {
              emits('modalHandleOk', form.value)
              message.success('新增成功!')
              addOpen.value = false
            })
            .finally(() => {
              confirmLoading.value = false
            })
        }
      })
      .catch((error: any) => {
        console.log(error)
      })
}

function cancel() {
  emits('modalCancel')
  formRef.value?.resetFields()
}

function show(type: string, data?: FormState) {
  formRef.value?.resetFields()
  modalType.value = type
  addOpen.value = true
  if (type === 'edit') {
    editForm(data)
  }
}

function editForm(data?: FormState) {
  if (data) {
    const copyData = JSON.parse(JSON.stringify(data))
    const { userGroupId, userGroupName, isUse, remark } = copyData
    const formValue = { userGroupId, userGroupName, isUse, remark }
    form.value = formValue
  }
}

defineExpose({ show })
</script>
