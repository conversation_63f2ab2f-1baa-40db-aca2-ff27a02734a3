.subscription-settings {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    background: white;
}
.head {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 48px;
}
.head-title {
    margin-bottom: 0.5em;
    color: rgba(0, 0, 0, 0.85);
    font-weight: 600;
    font-size: 16px;
    line-height: 1.5;
}
.subscription-settings :global(.ant-modal-content) {
    padding: 0!important;
    border-radius: 5px;
}
.subscription-settings :global(.ant-modal-header) {
    margin: 0;
    color: rgba(0, 0, 0, 0.85);
    font-weight: 500;
    font-size: 16px;
    line-height: 22px;
    word-wrap: break-word;
    border-bottom: 1px solid #dde3ea;
    padding: 16px 24px;
}
.subscription-settings :global(.ant-modal-body) {
    padding: 24px;
    font-size: 14px;
    line-height: 1.5715;
    word-wrap: break-word;
}
.subscription-settings :global(.ant-modal-footer ) {
    padding: 16px 23px;
    border-top: 1px solid #dde3ea;
}
.subscription-settings :global(.ant-picker)
.subscription-settings :global(.ant-select) {
    width: 100%;
}

.subscription-settings :global(.pushrecord-modal .search-btn) {
    display: none;
}
.index-modal-content,
.pushrecord-modal-content {
    min-height: 300px; /* 最小高度保证 */
}