<template>
  <div class="order-page">
    <cardBox title="职务管理" subTitle="职务管理，对职务的新增、修改、删除">
      <template #headerRight>
        <a-button type="primary" @click="addTable" v-action:add>新增</a-button>
      </template>
      <Table
        :columns="columns"
        :getData="getPositionList"
        ref="tableRef"
        :searchFormState="formState"
      >
        <template #search>
          <a-form-item label="" name="职务名称" :rules="[{ message: '请输入职务名称' }]">
            <a-input
              v-model:value="formState.positionName"
              allow-clear
              placeholder="请输入职务名称"
            />
          </a-form-item>
          <a-form-item label="">
            <a-select v-model:value="formState.isUse" placeholder="请选择是否启用" allow-clear>
              <a-select-option value="1">是</a-select-option>
              <a-select-option value="0">否</a-select-option>
            </a-select>
          </a-form-item>
        </template>
      </Table>
    </cardBox>
    <position-modal
      :open="modalOpen"
      :disabled="disabled"
      @modalCancel="modalCancel"
      @modalHandleOk="modalHandleOk"
      ref="modalRef"
    ></position-modal>
  </div>
</template>
<script setup lang="ts">
import { ref, onBeforeMount, h, createVNode, resolveDirective, withDirectives } from 'vue'
import { Table } from '@fs/fs-components'
import type { Column } from '@fs/fs-components/src/components/table/type'
import { Button, message } from 'ant-design-vue/es/components'
import Modal from 'ant-design-vue/es/modal/Modal'
import { ExclamationCircleOutlined } from '@ant-design/icons-vue'
import { deletePosition, getPositionList } from '@/api/services/permission'
import { PossitionManageDTO } from '@/utils/column'
import cardBox from '@/components/card-box/card-box.vue'

defineOptions({
  name: 'positionManageList',
})

let columns = ref<Column[]>([])
const tableRef = ref<InstanceType<typeof Table> | null>(null)
const modalRef = ref<any | null>(null)
const modalOpen = ref<boolean>(false)
const disabled = ref<boolean>(false)
const formState: Record<string, any> = ref({
  positionName: '',
  isUse: null,
})

function modalHandleOk() {
  tableRef.value?.getTableData()
  modalCancel()
}

function modalCancel() {
  modalOpen.value = false
}

onBeforeMount(() => {
  initColumns()
})

function addTable() {
  modalOpen.value = true
  disabled.value = false
  modalRef.value?.show('add')
}

async function initColumns() {
  columns.value = PossitionManageDTO.map((item) => {
    return {
      ...item,
      dataIndex: item.key,
    }
  })
  columns.value.push({
    key: 'action',
    title: '操作',
    customRender(data: Record<string, any>) {
      return [
        withDirectives(
          h(
            Button,
            {
              type: 'link',
              onClick() {
                modalOpen.value = true
                disabled.value = true
                modalRef.value?.show('edit', data.record)
              },
            },
            {
              default: () => '编辑',
            },
          ),
          [[resolveDirective('action'), '', 'update']],
        ),
        withDirectives(
          h(
            Button,
            {
              type: 'link',
              onClick() {
                Modal.confirm({
                  title: '提示',
                  icon: createVNode(ExclamationCircleOutlined),
                  content: createVNode('div', { style: 'color:red;' }, '确认执行删除操作？'),
                  async onOk() {
                    try {
                      return await new Promise((resolve, reject) => {
                        deletePosition({ positionId: data?.record?.positionId })
                          .then((res: any) => {
                            if (res.code === '000000') {
                              message.success('删除成功')
                              tableRef.value?.resetForm()
                              resolve(res)
                            }
                          })
                          .catch(() => {
                            reject()
                          })
                      })
                    } catch {
                      return console.log('Oops errors!')
                    }
                  },
                  class: 'test',
                })
              },
            },
            {
              default: () => '删除',
            },
          ),
          [[resolveDirective('action'), '', 'delete']],
        ),
      ]
    },
  })
}
</script>
<style lang="less">
.order-page {
  height: 100%;
  background: white;
}
</style>
