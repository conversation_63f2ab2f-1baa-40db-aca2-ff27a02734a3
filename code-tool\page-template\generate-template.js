import prettier from 'prettier'
import parseBabel from 'prettier/parser-babel'
import parseHtml from 'prettier/parser-html'
import { getJsonPath, checkFileExistence } from '../base-business/virtual-file-system.js'
import path from 'path'

/**
 * 生成模板Echarts代码
 * @param {Object} data - 数据对象
 * @param {Function} [data.getOptions=()=>{}] - 获取Echarts配置的函数
 * @param {Array} [data.items=[]] - 图表项数组
 * @param {string} [data.title='图表'] - 图表标题
 * @returns {string} - 生成的模板Echarts代码
 */
function generateTemplateHandle(data) {
  const content = `
<script setup>
</script>
<template>
  <Container>
    <template #headContent>headContent</template>
    <template #default>${data.menuName}</template>
    <template #footer>footer</template>
  </Container>
</template>

<script lang="ts" setup>
import { Container } from '@fs/fs-components'
</script>

  `
  return prettier.format(content, { parser: 'vue', plugins: [parseHtml, parseBabel] })
}

/**
 * 生成基础模板代码
 * @returns {string} - 生成的模板代码
 */
function generateBaseTemplate() {
  const content = `<template>
  <div class="content">
    <a-button @click="openMadol('table')" class="mg-rt20"> 快速创建表格管理页面</a-button>
    <a-button @click="openMadol('form')" class="mg-rt20"> 快速创建表单管理页面</a-button>
    <a-button @click="openMadol('echarts')" class="mg-rt20"> 快速创建图表管理页面</a-button>
    <a-button @click="openMadol('grid')"> 快速创建布局页面</a-button>
  </div>
</template>

<script lang="ts" setup>
import { useAppStore } from '@/stores/app'

const appStore = useAppStore()

function openMadol(type: string) {
  appStore.setGenerationType({ type, state: true })
}
</script>
 <style scoped lang="less">
    .mg-rt20{
    margin-right: 20px
    }
    </style>
  `
  return prettier.format(content, { parser: 'vue', plugins: [parseHtml, parseBabel] })
}

/**
 * 生成模板的异步函数
 * @param {Object} data - 包含页面类型等信息的数据对象
 * @returns {Promise<string | undefined>} 生成的模板代码，如果文件已存在可能返回 undefined
 */
export async function generateTemplate(data) {
  // 获取页面类型
  const pageType = data?.pageType
  // 如果不覆盖已有文件
  if (!data?.isCover) {
    // 获取源视图目录路径
    const { srcViewsDirPath } = getJsonPath()
    // 拼接视图路径
    const viewPath = path.join(srcViewsDirPath, data?.menuUrl)
    // 拼接视图页面路径
    const viewPagePath = path.join(viewPath, `index.vue`)
    // 检查文件是否存在
    const val = await checkFileExistence(viewPagePath)
    // 如果文件存在则返回，不进行后续操作
    if (val) return
  }
  // 根据页面类型生成相应模板
  switch (pageType) {
    case 'secondary-page':
      // 生成二级页面模板
      return generateTemplateHandle(data)
    default:
      // 生成基础模板
      return generateBaseTemplate()
  }
}
