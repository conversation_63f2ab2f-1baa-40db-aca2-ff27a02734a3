<template>
  <div class="filter-main">
    <a-input v-model:value="filterValue" placeholder="查找..." allow-clear>
      <template #prefix>
        <SearchOutlined />
      </template>
    </a-input>
    <a-divider style="margin: 10px 0"></a-divider>
    <div class="h-40vh overflow-y-auto hide-scrollbar">
      <div class="data-source" v-for="item in joinTableColumns" :key="item.tableName">
        <a-collapse
          v-model:activeKey="activeKeys"
          collapsible="header"
          :expandIconPosition="'end'"
          ghost
        >
          <a-collapse-panel :key="item.tableName" :showArrow="false">
            <template #header>
              <div flex="~ gap-2" class="overflow-hidden" text="20px b700">
                <TableOutlined />
                <Tooltip :title="item.tableName">
                  <span class="ellipsis">{{ item.tableName }}</span>
                </Tooltip>
                <RightOutlined
                  text="14px"
                  :rotate="!activeKeys.includes(item.tableName) ? 0 : 90"
                />
              </div>
            </template>
            <data-columns
              :data="dataColumnData(item)"
              @handleClick="(res) => handleClick(res, item.tableName)"
            ></data-columns>
          </a-collapse-panel>
        </a-collapse>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import { SearchOutlined, TableOutlined, RightOutlined } from '@ant-design/icons-vue'
import dataColumns from '../data-columns/index.vue'
import type { dataColumn } from '../data-columns/type'
import { JoinTableColumnsKey, type JoinTableColumns } from '@/views/indicator-config/provide-keys'
import type { ServerFieldItem } from '@/api/services/indicator/type'
import { Tooltip } from 'ant-design-vue'
const emits = defineEmits(['handleClick'])
const props = withDefaults(
  defineProps<{
    showArrow?: boolean
    tableName: string
    columns: dataColumn[]
  }>(),
  {
    showArrow: true,
    tableName: '',
    columns: () => [],
  },
)

const joinTableColumns = inject<Ref<JoinTableColumns>>(JoinTableColumnsKey)
const dataColumnData = (item: any) => {
  return item.fields.filter(
    (field: ServerFieldItem) =>
      !filterValue.value || field.title.toLowerCase().includes(filterValue.value.toLowerCase()),
  )
}
const filterValue = ref('')
const activeKeys = ref<string[]>([])
const handleClick = (item: ServerFieldItem, tableName: string) => {
  console.log('filter main click', item, tableName)
  emits('handleClick', item, tableName)
}
watch(filterValue, (newValue) => {
  if (!newValue) {
    activeKeys.value = []
    return
  }
  const matchedTables =
    joinTableColumns?.value?.filter((table) =>
      table.fields.some((field) => field.title.toLowerCase().includes(newValue.toLowerCase())),
    ) ?? []
  activeKeys.value = matchedTables.map((table) => table.tableName)
})

const filterColumns = computed(() => {
  return filterValue.value
    ? props.columns.filter((column) => column.title.includes(filterValue.value))
    : props.columns
})
</script>

<style lang="less" scoped>
:deep(.ant-collapse-header-text) {
  max-width: 100%;
}
.data-source {
  &-title {
    display: flex;
    align-items: center;
    font-size: 20px;
    font-weight: bold;
  }
}

.hide-scrollbar {
  &::-webkit-scrollbar {
    display: none;
  }
  -ms-overflow-style: none;
  scrollbar-width: none;
}
</style>
