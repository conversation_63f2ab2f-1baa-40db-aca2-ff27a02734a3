<template>
  <div class="input-between">
    <a-input-number
      v-model:value="modelValue[0]"
      :min="0"
      :step="1"
      placeholder="最小值"
      class="input-between-item"
    />
    <div class="input-between-separator">和</div>
    <a-input-number
      v-model:value="modelValue[1]"
      :min="0"
      :step="1"
      placeholder="最大值"
      class="input-between-item"
    />
  </div>
</template>

<script setup lang="ts">
const modelValue = defineModel({
  default: () => [undefined, undefined],
})

if (!modelValue.value) {
  modelValue.value = [undefined, undefined]
}
</script>

<style lang="less" scoped>
.input-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
  &-item {
    width: 48%;
  }
  &-separator {
    margin: 0 10px;
  }
}
</style>
