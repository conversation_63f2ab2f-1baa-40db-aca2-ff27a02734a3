import dayjs from 'dayjs'
// import localizedFormat from 'dayjs/plugin/localizedFormat';
// import updateLocale from 'dayjs/plugin/updateLocale';
import 'dayjs/locale/zh-cn'
import numeral from 'numeral'
import Field from './field.vue'
import { ColumnType } from '@/api/services/indicator/type'

// dayjs.extend(localizedFormat);
// dayjs.extend(updateLocale);

// // 更新本地化配置
// dayjs.updateLocale('zh-cn', {
//   weekdays: ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'],
//   weekdaysShort: ['周日', '周一', '周二', '周三', '周四', '周五', '周六'],
//   months: ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月'],
//   monthsShort: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
// });

export async function sleep(d: number): Promise<void> {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve()
    }, d)
  })
}

interface IDate {
  des: 'share_text'
  field: 'share_text'
  zone: null
  dataIndex: 'share_text'
  earliestTime: null
  endTime: null
  title: 'share_text'
  type: 'TEXT' | 'NUMBER' | 'DATE' | 'SELECT' | 'BOOLEAN'
  key: 'share_text'
}

export function getFilterlist(data: IDate[]) {
  const TYPE = {
    TEXT: 'input',
    NUMBER: 'limit',
    DATE: 'date',
    SELECT: 'select',
    BOOLEAN: 'boolean',
  }
  return data.map((item) => {
    return {
      field: item.field.toLocaleUpperCase(),
      type: TYPE[item.type] || 'text',
      value: '',
      time: [],
      selectOptions: [],
    }
  })
}

export const dateFormats = [
  {
    label: dayjs('2018-01-31').format('MMMM D, YYYY'), // 一月 31, 2018
    value: 'MMMM D, YYYY',
  },
  {
    label: dayjs('2018-01-31').format('D MMMM, YYYY'), // 31 一月, 2018
    value: 'D MMMM, YYYY',
  },
  {
    label: dayjs('2018-01-31').format('dddd, MMMM D, YYYY'), // 星期三, 一月 31, 2018
    value: 'dddd, MMMM D, YYYY',
  },
  {
    label: dayjs('2018-01-31').format('M/D/YYYY'), // 1/31/2018
    value: 'M/D/YYYY',
  },
  {
    label: dayjs('2018-01-31').format('D/M/YYYY'), // 31/1/2018
    value: 'D/M/YYYY',
  },
  {
    label: dayjs('2018-01-31').format('YYYY/M/D'), // 2018/1/31
    value: 'YYYY/M/D',
  },
]

// export const dateFormats2 = [
//   {
//     label: dayjs('2031-08-16T14:50:00').locale('zh-cn').format('dddd, MMMM D, YYYY, h:mm A'),
//     value: 'dddd, MMMM D, YYYY, h:mm A',
//   },
//   {
//     label: dayjs('2031-08-16T14:50:00').locale('zh-cn').format('ddd, M月 D, YYYY, h:mm A'),
//     value: 'ddd, M月 D, YYYY, h:mm A',
//   },
// ];

export const numberStyleList = [
  {
    label: '正常',
    value: 0, // 正常格式，不需要转换
  },
  {
    label: '百分比',
    value: 1, // 百分比格式，需要乘以100
  },
  {
    label: '科学的',
    value: 2, // 科学计数法格式
  },
  {
    label: '货币',
    value: 3, // 货币格式
  },
]

export const decimalStyleList = [
  {
    label: '100,000',
    value: '0,0', // 使用逗号作为千位分隔符，点作为小数分隔符
  },
  {
    label: '100 000,00',
    value: '0 0,00', // 使用空格作为千位分隔符，逗号作为小数分隔符
  },
  {
    label: '100.000,00',
    value: '0.000,00', // 使用点作为千位分隔符，逗号作为小数分隔符
  },
  {
    label: '100000.00',
    value: '0,0.00', // 无千位分隔符，点作为小数分隔符
  },
  {
    label: "100'000.00",
    value: "0'0.00", // 使用单引号作为千位分隔符，点作为小数分隔符
  },
]

interface NumberConfig {
  numberStyle: number // 样式
  decimalStyle: string // 分隔样式
  decimalNum: number // 小数位数
  multiple: number // 乘以一个数字
  startStr: string // 添加一个前缀
  endStr: string // 添加一个后缀
}

export function calculateNumber(val: string | number, config: NumberConfig) {
  let num = Number(val)

  // 1.乘以一个数字
  if (config.multiple || config.multiple === 0) {
    num *= config.multiple
  }

  // 2.小数位数
  if (config.decimalNum || config.decimalNum === 0) {
    num = parseFloat(num.toFixed(config.decimalNum))
  }

  // 3.格式化数字
  let formattedValue = numeral(num).format(config.decimalStyle)
  // console.log(
  //   '数字==>',
  //   num,
  //   config.decimalStyle,
  //   '转化后==>',
  //   numeral(num).format(config.decimalStyle),
  // )

  // 4.添加前缀和后缀
  if (config.startStr) {
    formattedValue = `${config.startStr}${formattedValue}`
  }
  if (config.endStr) {
    formattedValue = `${formattedValue}${config.endStr}`
  }

  // 5.处理样式
  switch (config.numberStyle) {
    case 1: // 百分比
      formattedValue = numeral(num / 100).format('0.00%')
      break
    case 2: // 科学计数法
      formattedValue = num.toExponential()
      break
    case 3: // 货币
      formattedValue = numeral(num).format('$0,0.00')
      break
    default: // 正常
      break
  }

  return formattedValue
}

export function calculateDate(val: string, config: any) {
  let a = '',
    b = '',
    c = ''
  if (config.selectedDateFormate) {
    // console.log('aaa==>', val, '转化后==>', dayjs(val).format(config.selectedDateFormate))
    a = val === null ? '' : dayjs(val).format(config.selectedDateFormate)
  }
  if (config.displayTime) {
    // console.log('bbb==>', val, '转化后==>', dayjs(val).format(config.displayTime))
    b = val === null ? '' : dayjs(val).format(config.displayTime)
  }
  if (config.timeFormate) {
    // console.log('ccc==>', val, '转化后==>', dayjs(val).format(config.timeFormate))
    c = val === null ? '' : dayjs(val).format(config.timeFormate)
  }
  // return temp
  return a + b + c
}

export function displayFn(type: ColumnType, value: string | number, config: any) {
  if (type === ColumnType.TEXT) {
    return value
  }
  if (type === ColumnType.NUMBER) {
    return calculateNumber(value, config)
  }
  if (type === ColumnType.DATE) {
    return calculateDate(value as string, config)
  }
  return value
}
