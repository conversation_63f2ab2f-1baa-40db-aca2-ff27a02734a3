import { sleep } from '@/utils'
export async function getData({ dbType = '1' }) {
  await sleep(200)

  console.log('dbType', dbType)

  switch (dbType) {
    case '1': //
      return warpData(getDataSrouceData1)
    case '2':
      return warpData(getDataSrouceData2)
    case '3':
      return warpData(getDataSrouceData3)
    case '4':
      return warpData(getDataSrouceData4)
    case '5':
      return warpData(getDataSrouceData5)
    case '6':
      return warpData(getDataSrouceData6)
    case '7':
      return warpData(getDataSrouceData7)
    case '8':
      return warpData(getDataSrouceData8)
    case '9':
      return warpData(getDataSrouceData9)
    case '10':
      return warpData(getDataSrouceData10)
    case '11':
      return warpData(getDataSrouceData11)
    case '12':
      return warpData(getDataSrouceData12)
    default:
  }
}

async function warpData(unkownData: any) {
  return Promise.resolve({
    code: '000000',
    data: unkownData,
  })
}

const getDataSrouceData1 = [
  { title: '药品库存明细表', key: 'PHARMACY_STOCK_DETAIL' },
  { title: '财务收费流水表', key: 'FIN_PAYMENT_RECORD' },
  { title: '教育训练能力分析', key: 'Education_Training_Capacity' },
  { title: '院感监测原始表', key: 'INFECTION_RAW_DATA' },
  { title: '手术排班计划表', key: 'SURGERY_SCHEDULE' },
  { title: '医护人员档案表', key: 'HR_DOCTOR_ARCHIVE' },
  { title: '设备运行日志表', key: 'EQUIPMENT_LOG' },
  { title: '检验项目信息表', key: 'LIS_TEST_ITEM' },
  { title: '医生信息表', key: 'DOCTOR_INFO' },
  { title: '医生证件信息表', key: 'DOCTOR_IDCARD' },
  { title: '医嘱信息表', key: 'DOCTOR_ADVICE' },
  { title: '药品信息表', key: 'PHARMACY_DRUG' },
  { title: '预约信息表', key: 'RESERVE_INFO' },
  { title: '患者满意度信息表', key: 'PATIENT_SATISFACTION' },
  { title: '收费明细表', key: 'FIN_CHARGE_DETAIL' },
  { title: '银行卡信息表', key: 'BANK_CARD_INFO' },
  { title: '短信发送记录表', key: 'SMS_SEND_RECORD' },
  { title: 'HIS患者信息主表', key: 'HIS_PATIENT_INFO' },
  { title: 'EMR电子病历基础表', key: 'EMR_BASIC_RECORD' },
  { title: 'LIS检验结果表', key: 'LIS_TEST_RESULT' },
  { title: 'PACS影像报告表', key: 'PACS_IMAGE_REPORT' },
]

const getDataSrouceData2 = [
  { title: '培训课程编号', key: 'TRAINING_COURSE_ID' },
  { title: '培训课程名称', key: 'TRAINING_COURSE_NAME' },
  { title: '课程类型', key: 'COURSE_TYPE' },
  { title: '授课教师', key: 'INSTRUCTOR_NAME' },
  { title: '课程时长', key: 'COURSE_DURATION' },
  { title: '培训对象', key: 'TRAINING_TARGET' },
  { title: '课程大纲', key: 'COURSE_OUTLINE' },
  { title: '培训时间', key: 'TRAINING_DATE' },
  { title: '培训地点', key: 'TRAINING_LOCATION' },
  { title: '课程考试成绩', key: 'COURSE_EXAM_SCORE' },
  { title: '学员考核情况', key: 'STUDENT_ASSESSMENT' },
  { title: '培训合格证书', key: 'TRAINING_CERTIFICATE' },
  { title: '继续教育学分', key: 'CONTINUING_EDUCATION_CREDITS' },
  { title: '医院资质认证编号', key: 'HOSPITAL_CERTIFICATION_ID' },
  { title: '医院资质名称', key: 'HOSPITAL_CERTIFICATION_NAME' },
  { title: '认证颁发机构', key: 'CERTIFICATION_AUTHORITY' },
  { title: '认证日期', key: 'CERTIFICATION_DATE' },
  { title: '认证有效期', key: 'CERTIFICATION_EXPIRY_DATE' },
  { title: '医院培训记录', key: 'HOSPITAL_TRAINING_RECORDS' },
  { title: '医务人员培训档案', key: 'MEDICAL_STAFF_TRAINING_ARCHIVES' },
]

const getDataSrouceData3 = [
  { title: '培训项目编号', key: 'TRAINING_PROGRAM_ID' },
  { title: '培训项目名称', key: 'TRAINING_PROGRAM_NAME' },
  { title: '合作医院名称', key: 'PARTNER_HOSPITAL_NAME' },
  { title: '培训课程编号', key: 'TRAINING_COURSE_ID' },
  { title: '培训课程名称', key: 'TRAINING_COURSE_NAME' },
  { title: '讲师姓名', key: 'INSTRUCTOR_NAME' },
  { title: '培训开始日期', key: 'TRAINING_START_DATE' },
  { title: '培训结束日期', key: 'TRAINING_END_DATE' },
  { title: '培训地点', key: 'TRAINING_LOCATION' },
  { title: '培训参与人员', key: 'TRAINING_PARTICIPANTS' },
  { title: '考核成绩', key: 'ASSESSMENT_SCORE' },
  { title: '培训合格状态', key: 'TRAINING_PASS_STATUS' },
  { title: '证书编号', key: 'CERTIFICATE_ID' },
  { title: '培训反馈', key: 'TRAINING_FEEDBACK' },
  { title: '后续培训计划', key: 'FOLLOW_UP_TRAINING_PLAN' },
]

const getDataSrouceData4 = [
  { title: '消防设备编号', key: 'FIRE_EQUIPMENT_ID' },
  { title: '消防设备名称', key: 'FIRE_EQUIPMENT_NAME' },
  { title: '设备类别', key: 'EQUIPMENT_CATEGORY' },
  { title: '设备型号', key: 'EQUIPMENT_MODEL' },
  { title: '设备存放位置', key: 'EQUIPMENT_LOCATION' },
  { title: '设备状态', key: 'EQUIPMENT_STATUS' },
  { title: '维护周期', key: 'MAINTENANCE_CYCLE' },
  { title: '最后检查日期', key: 'LAST_INSPECTION_DATE' },
  { title: '责任人', key: 'RESPONSIBLE_PERSON' },
  { title: '物业安全巡检记录', key: 'SECURITY_INSPECTION_RECORD' },
  { title: '安保人员信息', key: 'SECURITY_PERSONNEL_INFO' },
  { title: '医院监控设备编号', key: 'CCTV_DEVICE_ID' },
  { title: '监控设备位置', key: 'CCTV_DEVICE_LOCATION' },
  { title: '监控设备状态', key: 'CCTV_DEVICE_STATUS' },
  { title: '紧急疏散通道', key: 'EMERGENCY_EXIT_PATH' },
  { title: '医院安全事件记录', key: 'HOSPITAL_SECURITY_INCIDENTS' },
  { title: '医院消防演习计划', key: 'FIRE_DRILL_PLAN' },
  { title: '医院消防报警记录', key: 'FIRE_ALARM_RECORDS' },
  { title: '医院防火措施', key: 'FIRE_PREVENTION_MEASURES' },
  { title: '医院安全培训记录', key: 'SAFETY_TRAINING_RECORDS' },
]

const getDataSrouceData5 = [
  { title: '安保培训编号', key: 'SECURITY_TRAINING_ID' },
  { title: '培训课程名称', key: 'TRAINING_COURSE_NAME' },
  { title: '培训课程类型', key: 'TRAINING_COURSE_TYPE' },
  { title: '授课讲师', key: 'INSTRUCTOR_NAME' },
  { title: '培训日期', key: 'TRAINING_DATE' },
  { title: '培训时长', key: 'TRAINING_DURATION' },
  { title: '培训内容摘要', key: 'TRAINING_CONTENT_SUMMARY' },
  { title: '培训考核成绩', key: 'TRAINING_ASSESSMENT_SCORE' },
  { title: '培训通过状态', key: 'TRAINING_PASS_STATUS' },
  { title: '安保人员编号', key: 'SECURITY_PERSONNEL_ID' },
  { title: '安保人员姓名', key: 'SECURITY_PERSONNEL_NAME' },
  { title: '安保证书编号', key: 'SECURITY_CERTIFICATE_ID' },
  { title: '安保证书名称', key: 'SECURITY_CERTIFICATE_NAME' },
  { title: '证书颁发机构', key: 'CERTIFICATE_ISSUING_AUTHORITY' },
  { title: '证书有效期', key: 'CERTIFICATE_EXPIRATION_DATE' },
]

const getDataSrouceData6 = [
  { title: 'equipment_inventory', key: 'equipment_inventory' },
  { title: 'maintenance_logs', key: 'maintenance_logs' },
  { title: 'scheduled_maintenance', key: 'scheduled_maintenance' },
  { title: 'repair_requests', key: 'repair_requests' },
  { title: 'spare_parts_inventory', key: 'spare_parts_inventory' },
  { title: 'equipment_suppliers', key: 'equipment_suppliers' },
  { title: 'inspection_reports', key: 'inspection_reports' },
  { title: 'warranty_information', key: 'warranty_information' },
  { title: 'calibration_records', key: 'calibration_records' },
  { title: 'asset_depreciation', key: 'asset_depreciation' },
]

const getDataSrouceData7 = [
  { title: '服装标准编号', key: 'UNIFORM_STANDARD_ID' },
  { title: '服装类别', key: 'UNIFORM_CATEGORY' },
  { title: '适用岗位', key: 'APPLICABLE_POSITION' },
  { title: '材质要求', key: 'MATERIAL_REQUIREMENTS' },
  { title: '颜色标准', key: 'COLOR_STANDARD' },
  { title: '清洗消毒要求', key: 'CLEANING_DISINFECTION_REQUIREMENTS' },
  { title: '更换周期', key: 'REPLACEMENT_CYCLE' },
  { title: '配件要求', key: 'ACCESSORY_REQUIREMENTS' },
  { title: '防护等级', key: 'PROTECTION_LEVEL' },
  { title: '供应商信息', key: 'SUPPLIER_INFORMATION' },
  { title: '采购批次', key: 'PURCHASE_BATCH' },
  { title: '适用季节', key: 'APPLICABLE_SEASON' },
  { title: '穿着规范', key: 'WEARING_REGULATIONS' },
  { title: '标识与徽章要求', key: 'IDENTIFICATION_BADGE_REQUIREMENTS' },
  { title: '特殊岗位要求', key: 'SPECIAL_POSITION_REQUIREMENTS' },
  { title: '服装标准编号', key: 'UNIFORM_STANDARD_ID' },
  { title: '服装类别', key: 'UNIFORM_CATEGORY' },
  { title: '适用岗位', key: 'APPLICABLE_POSITION' },
  { title: '材质要求', key: 'MATERIAL_REQUIREMENTS' },
  { title: '颜色标准', key: 'COLOR_STANDARD' },
  { title: '清洗消毒要求', key: 'CLEANING_DISINFECTION_REQUIREMENTS' },
  { title: '更换周期', key: 'REPLACEMENT_CYCLE' },
  { title: '配件要求', key: 'ACCESSORY_REQUIREMENTS' },
  { title: '防护等级', key: 'PROTECTION_LEVEL' },
  { title: '供应商信息', key: 'SUPPLIER_INFORMATION' },
  { title: '采购批次', key: 'PURCHASE_BATCH' },
  { title: '适用季节', key: 'APPLICABLE_SEASON' },
  { title: '穿着规范', key: 'WEARING_REGULATIONS' },
  { title: '标识与徽章要求', key: 'IDENTIFICATION_BADGE_REQUIREMENTS' },
  { title: '特殊岗位要求', key: 'SPECIAL_POSITION_REQUIREMENTS' },
]

const getDataSrouceData8 = [
  { title: '卫生等级编号', key: 'HYGIENE_LEVEL_ID' },
  { title: '卫生等级名称', key: 'HYGIENE_LEVEL_NAME' },
  { title: '评定标准', key: 'EVALUATION_CRITERIA' },
  { title: '评定机构', key: 'EVALUATION_AUTHORITY' },
  { title: '评定日期', key: 'EVALUATION_DATE' },
  { title: '下次评定日期', key: 'NEXT_EVALUATION_DATE' },
  { title: '医院科室编号', key: 'HOSPITAL_DEPARTMENT_ID' },
  { title: '医院科室名称', key: 'HOSPITAL_DEPARTMENT_NAME' },
  { title: '清洁消毒记录', key: 'CLEANING_DISINFECTION_RECORD' },
  { title: '感染控制措施', key: 'INFECTION_CONTROL_MEASURES' },
  { title: '废弃物处理标准', key: 'WASTE_DISPOSAL_STANDARDS' },
  { title: '手卫生合规率', key: 'HAND_HYGIENE_COMPLIANCE_RATE' },
  { title: '医护人员健康检查记录', key: 'HEALTHCARE_WORKER_HEALTH_CHECKS' },
  { title: '病房空气质量检测', key: 'WARD_AIR_QUALITY_TEST' },
  { title: '卫生突发事件记录', key: 'HYGIENE_EMERGENCY_INCIDENTS' },
]

const getDataSrouceData9 = [
  { title: '设备编号', key: 'EQUIPMENT_ID' },
  { title: '设备名称', key: 'EQUIPMENT_NAME' },
  { title: '设备类别', key: 'EQUIPMENT_CATEGORY' },
  { title: '设备品牌', key: 'EQUIPMENT_BRAND' },
  { title: '设备型号', key: 'EQUIPMENT_MODEL' },
  { title: '采购日期', key: 'PURCHASE_DATE' },
  { title: '设备状态', key: 'EQUIPMENT_STATUS' },
  { title: '维护周期', key: 'MAINTENANCE_CYCLE' },
  { title: '设备存放位置', key: 'EQUIPMENT_LOCATION' },
  { title: '设备负责人', key: 'EQUIPMENT_MANAGER' },
]

const getDataSrouceData10 = [
  { title: '设备编号', key: 'EQUIPMENT_ID' },
  { title: '设备名称', key: 'EQUIPMENT_NAME' },
  { title: '设备类别', key: 'EQUIPMENT_CATEGORY' },
  { title: '设备品牌', key: 'EQUIPMENT_BRAND' },
  { title: '设备型号', key: 'EQUIPMENT_MODEL' },
  { title: '采购日期', key: 'PURCHASE_DATE' },
  { title: '设备状态', key: 'EQUIPMENT_STATUS' },
  { title: '维护周期', key: 'MAINTENANCE_CYCLE' },
  { title: '设备存放位置', key: 'EQUIPMENT_LOCATION' },
  { title: '设备负责人', key: 'EQUIPMENT_MANAGER' },
]

const getDataSrouceData12 = [
  { title: '设备类别', key: 'EQUIPMENT_CATEGORY' },
  { title: '设备品牌', key: 'EQUIPMENT_BRAND' },
  { title: '设备型号', key: 'EQUIPMENT_MODEL' },
  { title: '采购日期', key: 'PURCHASE_DATE' },
  { title: '设备状态', key: 'EQUIPMENT_STATUS' },
  { title: '维护周期', key: 'MAINTENANCE_CYCLE' },
  { title: '设备存放位置', key: 'EQUIPMENT_LOCATION' },
  { title: '设备负责人', key: 'EQUIPMENT_MANAGER' },
  { title: '医院科室名称', key: 'HOSPITAL_DEPARTMENT_NAME' },
  { title: '清洁消毒记录', key: 'CLEANING_DISINFECTION_RECORD' },
  { title: '感染控制措施', key: 'INFECTION_CONTROL_MEASURES' },
  { title: '废弃物处理标准', key: 'WASTE_DISPOSAL_STANDARDS' },
]

const getDataSrouceData11 = [
  { title: '教育训练能力分析', key: 'Education_Training_Capacity' },
  { title: '检验项目信息表', key: 'LIS_TEST_ITEM' },
  { title: '医生信息表', key: 'DOCTOR_INFO' },
  { title: '医生证件信息表', key: 'DOCTOR_IDCARD' },
  { title: '医嘱信息表', key: 'DOCTOR_ADVICE' },
  { title: 'HIS患者信息主表', key: 'HIS_PATIENT_INFO' },
  { title: 'EMR电子病历基础表', key: 'EMR_BASIC_RECORD' },
  { title: 'LIS检验结果表', key: 'LIS_TEST_RESULT' },
  { title: 'PACS影像报告表', key: 'PACS_IMAGE_REPORT' },
  { title: '药品库存明细表', key: 'PHARMACY_STOCK_DETAIL' },
  { title: '财务收费流水表', key: 'FIN_PAYMENT_RECORD' },
  { title: '院感监测原始表', key: 'INFECTION_RAW_DATA' },
  { title: '手术排班计划表', key: 'SURGERY_SCHEDULE' },
  { title: '医护人员档案表', key: 'HR_DOCTOR_ARCHIVE' },
  { title: '设备运行日志表', key: 'EQUIPMENT_LOG' },
  { title: '药品信息表', key: 'PHARMACY_DRUG' },
  { title: '预约信息表', key: 'RESERVE_INFO' },
  { title: '患者满意度信息表', key: 'PATIENT_SATISFACTION' },
  { title: '收费明细表', key: 'FIN_CHARGE_DETAIL' },
  { title: '银行卡信息表', key: 'BANK_CARD_INFO' },
  { title: '短信发送记录表', key: 'SMS_SEND_RECORD' },
]
