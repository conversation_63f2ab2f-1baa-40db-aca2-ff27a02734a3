<template>
  <a-dropdown :trigger="['click']">
    <Card class="w-300px" @click="handleDirectory(data)">
      <template #title>
        <div class="" flex="~ justify-between">
          <span>{{ data.name }}</span>
          <Space>
            <a-tooltip placement="bottom">
              <template #title>
                <span>编辑</span>
              </template>
              <Button type="text" @click.stop="handleEdit">
                <template #icon>
                  <EditOutlined />
                </template>
              </Button>
            </a-tooltip>
            <a-tooltip placement="bottom">
              <template #title>
                <span>删除</span>
              </template>
              <Popconfirm title="确定删除吗？" @confirm="handleDelete">
                <Button type="text" @click.stop>
                  <template #icon>
                    <DeleteOutlined />
                  </template>
                </Button>
              </Popconfirm>
            </a-tooltip>
            <!-- <a-tooltip placement="bottom">
            <template #title>
              <span>点击进入目录</span>
            </template>
            <Button type="text" @click="handleDirectory(data)">
              <template #icon>
                <FolderOutlined />
              </template>
            </Button>
          </a-tooltip> -->
          </Space>
        </div>
      </template>
      <div flex="~ col justify-center gap-2">
        <div v-for="item in content" :key="item.value" flex="~ items-center">
          <span>{{ item.label }}：</span>
          <span>{{ item.value }}</span>
        </div>
      </div>
      <!-- <template #actions>
      <Button type="text" @click="handleFunction"> 函数 </Button>
      <Button type="text" @click="handleRule"> 规则 </Button>
    </template> -->
    </Card>
    <template #overlay>
      <a-menu>
        <a-menu-item v-for="(item, index) in JumpUrl" :key="item" @click="jumpFunc(item, data)">
          <a href="javascript:;">环境 {{ index + 1 }}</a>
        </a-menu-item>
      </a-menu>
    </template>
  </a-dropdown>
</template>

<script setup lang="ts">
import { Card, Button, Popconfirm, message, Space } from 'ant-design-vue'
import { EditOutlined, DeleteOutlined, FolderOutlined } from '@ant-design/icons-vue'
import type { ProjectItem } from '../data'
import { deleteProject } from '../service'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'

const router = useRouter()

const props = defineProps<{
  data: ProjectItem
}>()
const { data } = toRefs(props)

const emit = defineEmits<{
  (e: 'edit', project: ProjectItem): void
  (e: 'delete', project: ProjectItem): void
}>()

const JumpUrl = ref<string[]>([])
const content = computed(() => {
  return [
    { label: '名称', value: data.value.name },
    { label: '描述', value: data.value.description },
  ]
})

const handleEdit = () => {
  emit('edit', props.data)
}

const handleDelete = async () => {
  try {
    await deleteProject(props.data.autoId!)
    emit('delete', props.data)
    message.success('删除成功')
  } catch (error) {
    console.error(error)
    message.error('删除失败')
  }
}

const jumpFunc = (item: string, data: ProjectItem) => {
  const userStore = useUserStore()
  const token = userStore.token
  window.location.href = `${item}/${data.name}?token=${token}`
}

const handleDirectory = () => {
  const urlParams = new URLSearchParams(window.location.search)
  const url = urlParams.get('url')
  if (url) {
    JumpUrl.value = url.split(',')
  }
}

const handleFunction = () => {
  router.push({
    path: '/rule-engine/function-manage',
    query: { projectId: props.data.autoId },
  })
}

const handleRule = () => {
  router.push({
    path: '/rule-engine/rule-manage',
    query: { projectId: props.data.autoId },
  })
}
</script>

<style scoped></style>
