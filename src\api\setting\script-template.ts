import service from '@/api'
import { SODATAFLINK } from '@/api/base'

export interface TemplateDTO {
  id: string
  templateName: string
  templateDesc: string
  templateType: string
  templateContent: string
  createTime?: string
  updateTime?: string
  [key: string]: any
}

export interface TemplateListParams {
  templateName?: string | null
  templateDesc?: string | null
  templateType?: string | null
  pageIndex: number
  pageSize: number
  ownerId: string
}

export interface TemplateListResponse {
  records: TemplateDTO[]
  totalRecords: number
}

export interface DeleteTemplateParams {
  id: string
  ownerId: string
}

export interface DeleteTemplateResponse {
  result: string
  data: {
    message: string
  }
}

/**
 * 获取模板列表
 */
export function getTemplatList(
  params: TemplateListParams,
): Promise<{ data: TemplateListResponse }> {
  return service({
    method: 'get',
    url: `${SODATAFLINK}/new2dataplatform/templateManager/scripttemplatePageList`,
    params,
  })
}

/**
 * 删除模板
 */
export function deleteTemplat(params: DeleteTemplateParams): Promise<DeleteTemplateResponse> {
  return service({
    url: `${SODATAFLINK}/new2dataplatform/templateManager/scripttemplateDelete/${params.id}`,
    method: 'delete',
    data: params,
  })
}

/**
 * 新增模板
 */
export function addTemplat(data: any): Promise<any> {
  return service({
    url: `${SODATAFLINK}/new2dataplatform/templateManager/scripttemplateInsert`,
    method: 'post',
    data: data,
  })
}

/**
 * 编辑模板
 */
export function editTemplat(data: TemplateDTO): Promise<any> {
  return service({
    url: `${SODATAFLINK}/new2dataplatform/templateManager/scripttemplateUpdate/${data.id}`,
    method: 'put',
    data: data,
  })
}
